name: PR Storybook Preview

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - master
      - main

permissions:
  contents: write
  pull-requests: write

concurrency:
  group: 'pr-preview-${{ github.event.pull_request.number }}'
  cancel-in-progress: true

jobs:
  deploy-storybook-preview:
    runs-on: ubuntu-latest
    environment: storybook-preview

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: NPM Install and build
        if: github.event.action != 'closed'
        run: |
          npm ci
          npm run build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          HUSKY: 0

      - name: Build Storybook for PR
        if: github.event.action != 'closed'
        run: |
          npm run storybook:build -- --output-dir ./storybook-static
        env:
          STORYBOOK_BASE_PATH: /test-before-migrate/pr-preview/pr-${{ github.event.pull_request.number }}
          HUSKY: 0

      - name: Deploy PR Preview (separate from main)
        uses: rossjrw/pr-preview-action@v1
        env:
          CI: true
        with:
          source-dir: ./storybook-static