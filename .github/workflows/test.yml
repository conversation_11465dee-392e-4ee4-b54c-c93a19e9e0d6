name: Run Tests

on:
  pull_request:
    branches:
      - main
      - master

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: NPM Install and build
        run: |
          npm ci

      - name: Lint
        run: |
          npm run eslint

      # Tests currently using https://github.com/stenciljs/core/issues/6157 workaround
      # This may be slower than expected
      - name: Test
        run: |
          npm run test
