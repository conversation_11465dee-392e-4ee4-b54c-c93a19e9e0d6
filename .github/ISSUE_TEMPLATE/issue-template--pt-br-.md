---
name: Issue Template (PT-BR)
about: Sugerir uma ideia ou melhoria para este projeto
title: 'feat: '
labels: ''
assignees: ''

---

**Essa funcionalidade está relacionada a algum problema? Descreva.**  
Descreva de forma clara e concisa qual é o problema. Ex.: Fico frustrado quando [...]

---

**Descreva a solução que você gostaria**  
Explique de forma clara e concisa o que você gostaria que acontecesse.

---

**Descreva alternativas que você considerou**  
Liste qualquer solução ou funcionalidade alternativa que você tenha considerado.

---

**Componente do Design System**  
Qual componente do design system está relacionado a essa solicitação?

---

**Link(s) do Figma**  
Forneça o(s) link(s) do arquivo Figma relevante(s) para essa solicitação.

---

**Isso é uma alteração em um componente existente ou criação de um novo?**  
- [ ] Atualização de componente existente  
- [ ] Criação de novo componente

---

**Contexto adicional**  
Adicione qualquer outro contexto, capturas de tela, casos extremos ou considerações de UX aqui.

---

### 🛠️ Notas de implementação (para desenvolvedores e agentes de IA) - NÃO REMOVER

- Utilize os links do Figma com as **ferramentas Figma MCP** para inspecionar e extrair os detalhes do componente antes de iniciar o desenvolvimento.
- Todo componente implementado ou atualizado **deve conter histórias no Storybook** demonstrando **todos os estados possíveis** do componente.
- Sempre que possível, **consolide as variações em uma única página de story**, utilizando controles ou variantes para representar diferentes estados.
