---
name: Issue Template (EN)
about: Suggest an idea for this project
title: 'feat: '
labels: ''
assignees: ''

---

**Is your feature request related to a problem? Please describe.**  
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

---

**Describe the solution you'd like**  
A clear and concise description of what you want to happen.

---

**Describe alternatives you've considered**  
A clear and concise description of any alternative solutions or features you've considered.

---

**Design System Component**  
Which component from the design system does this issue relate to?

---

**Figma Link(s)**  
Please provide link(s) to the Figma file(s) relevant to this request.

---

**Is this about an existing component or a new one?**  
- [ ] Update to an existing component  
- [ ] New component creation

---

**Additional context**  
Add any other context, screenshots, edge cases, or UX considerations here.

----

### 🛠️ Implementation Notes (for developers and AI Agents) - DO NOT REMOVE

- Use the provided Figma links with the **Figma MCP tools** to inspect and extract component details before starting implementation.
- All implemented or updated components **must include Storybook stories** that demonstrate **every possible state** of the component.
- Try to **consolidate stories into a single story page** when feasible, using controls or variants to show multiple states.
