{
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "E2E Test Current File",
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/@stencil/core/bin/stencil",
            "args": [
                "test",
                "--e2e",
                "--devtools",
                "--",
                "--maxWorkers=0",
                "${fileBasename}"
            ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "autoAttachChildProcesses": false
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Spec Test Current File",
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/.bin/stencil",
            "args": [
                "test",
                "--spec",
                "--devtools",
                "--",
                "--maxWorkers=0",
                "${fileBasename}"
            ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "autoAttachChildProcesses": false,
        }
    ]
}