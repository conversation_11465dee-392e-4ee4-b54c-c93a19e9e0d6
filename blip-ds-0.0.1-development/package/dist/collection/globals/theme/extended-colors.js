import * as extendedColors from "blip-tokens/build/json/extended-color.json";
export const color_extended_blue = extendedColors['color-extended-blue'];
export const color_extended_ocean = extendedColors['color-extended-ocean'];
export const color_extended_green = extendedColors['color-extended-green'];
export const color_extended_yellow = extendedColors['color-extended-yellow'];
export const color_extended_orange = extendedColors['color-extended-orange'];
export const color_extended_red = extendedColors['color-extended-red'];
export const color_extended_pink = extendedColors['color-extended-pink'];
export const color_extended_gray = extendedColors['color-extended-gray'];
//# sourceMappingURL=extended-colors.js.map
