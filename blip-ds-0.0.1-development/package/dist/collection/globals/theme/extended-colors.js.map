{"version": 3, "file": "extended-colors.js", "sourceRoot": "", "sources": ["../../../../src/globals/theme/extended-colors.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,cAAc,MAAM,4CAA4C,CAAC;AAE7E,MAAM,CAAC,MAAM,mBAAmB,GAAG,cAAc,CAAC,qBAAqB,CAAC,CAAC;AACzE,MAAM,CAAC,MAAM,oBAAoB,GAAG,cAAc,CAAC,sBAAsB,CAAC,CAAC;AAC3E,MAAM,CAAC,MAAM,oBAAoB,GAAG,cAAc,CAAC,sBAAsB,CAAC,CAAC;AAC3E,MAAM,CAAC,MAAM,qBAAqB,GAAG,cAAc,CAAC,uBAAuB,CAAC,CAAC;AAC7E,MAAM,CAAC,MAAM,qBAAqB,GAAG,cAAc,CAAC,uBAAuB,CAAC,CAAC;AAC7E,MAAM,CAAC,MAAM,kBAAkB,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC;AACvE,MAAM,CAAC,MAAM,mBAAmB,GAAG,cAAc,CAAC,qBAAqB,CAAC,CAAC;AACzE,MAAM,CAAC,MAAM,mBAAmB,GAAG,cAAc,CAAC,qBAAqB,CAAC,CAAC", "sourcesContent": ["import * as extendedColors from 'blip-tokens/build/json/extended-color.json';\n\nexport const color_extended_blue = extendedColors['color-extended-blue'];\nexport const color_extended_ocean = extendedColors['color-extended-ocean'];\nexport const color_extended_green = extendedColors['color-extended-green'];\nexport const color_extended_yellow = extendedColors['color-extended-yellow'];\nexport const color_extended_orange = extendedColors['color-extended-orange'];\nexport const color_extended_red = extendedColors['color-extended-red'];\nexport const color_extended_pink = extendedColors['color-extended-pink'];\nexport const color_extended_gray = extendedColors['color-extended-gray'];\n"]}