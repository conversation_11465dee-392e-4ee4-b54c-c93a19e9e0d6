{"version": 3, "file": "radio-group.js", "sourceRoot": "", "sources": ["../../../../src/components/radio-button/radio-group.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAoC,MAAM,eAAe,CAAC;AAMlH,MAAM,OAAO,UAAU;IAJvB;QAKU,sBAAiB,GAA2C,IAAI,CAAC;QA6BjE,kBAAa,GAAG,CAAC,KAAa,EAAE,KAAkB,EAAQ,EAAE;YAClE,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,CAAC;QACH,CAAC,CAAC;KAoBH;IAvCC,YAAY,CAAC,KAAa;QACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAA0C,CAAC;QACjH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,KAAkB,EAAE,EAAE,CAC7E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAC3D,CAAC;QACJ,CAAC;IACH,CAAC;IAQO,gBAAgB,CAAC,KAAa;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACjC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;YAC1B,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;gBACpD,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Element, Prop, Watch, Event, EventEmitter, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-radio-group',\n  scoped: true,\n})\nexport class RadioGroup implements ComponentInterface {\n  private radioGroupElement?: HTMLCollectionOf<HTMLBdsRadioElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n  /**\n   * Emitted when the value has changed due to a click event.\n   */\n  @Event() bdsRadioGroupChange: EventEmitter;\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n\n    this.bdsRadioGroupChange.emit({ value });\n  }\n\n  componentWillRender() {\n    this.radioGroupElement = this.element.getElementsByTagName('bds-radio') as HTMLCollectionOf<HTMLBdsRadioElement>;\n    for (let i = 0; i < this.radioGroupElement.length; i++) {\n      this.radioGroupElement[i].addEventListener('bdsChange', (event: CustomEvent) =>\n        this.chagedOptions(this.radioGroupElement[i].value, event),\n      );\n    }\n  }\n\n  private chagedOptions = (value: string, event: CustomEvent): void => {\n    if (event.detail.checked == true) {\n      this.value = value;\n    }\n  };\n\n  private setSelectedRadio(value: string) {\n    const radios = this.radioGroupElement;\n    for (let i = 0; i < radios.length; i++) {\n      const getValue = radios[i].value;\n      radios[i].checked = false;\n      if (radios[i].checked == false && value == getValue) {\n        radios[i].checked = true;\n      }\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}