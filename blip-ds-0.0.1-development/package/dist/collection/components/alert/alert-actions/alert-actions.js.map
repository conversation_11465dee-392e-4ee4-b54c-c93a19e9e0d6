{"version": 3, "file": "alert-actions.js", "sourceRoot": "", "sources": ["../../../../../src/components/alert/alert-actions/alert-actions.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,CAAC,EAAE,MAAM,eAAe,CAAC;AAOjE,MAAM,OAAO,YAAY;IACvB,MAAM;QACJ,OAAO,CACL,4DAAK,KAAK,EAAC,gBAAgB;YACzB,8DAAQ,CACJ,CACP,CAAC;IACJ,CAAC;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-alert-actions',\n  styleUrl: 'alert-actions.scss',\n  shadow: true,\n})\nexport class AlertActions implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"alert__actions\">\n        <slot />\n      </div>\n    );\n  }\n}\n"]}