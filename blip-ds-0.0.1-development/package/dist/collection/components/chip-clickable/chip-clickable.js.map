{"version": 3, "file": "chip-clickable.js", "sourceRoot": "", "sources": ["../../../../src/components/chip-clickable/chip-clickable.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAU9F,MAAM,OAAO,aAAa;IAL1B;QAOW,YAAO,GAAG,IAAI,CAAC;QASxB;;WAEG;QACK,UAAK,GAAwB,SAAS,CAAC;QAC/C;;WAEG;QACK,SAAI,GAAU,UAAU,CAAC;QACjC;;WAEG;QACK,cAAS,GAAa,KAAK,CAAC;QACpC;;WAEG;QACK,UAAK,GAAa,KAAK,CAAC;QAChC;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QACnC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAEjC;;;WAGG;QACK,kBAAa,GAAY,IAAI,CAAC;KAmGvC;IA5FS,cAAc,CAAC,KAAK;QAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAK;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,KAAK;QAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAEO,cAAc,CAAC,KAAK;QAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,aAAa,CAAC;QACvB,CAAC;;YAAM,OAAO,OAAO,CAAC;IACxB,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAC;QAClB,CAAC;;YAAM,OAAO,SAAS,CAAC;IAC1B,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DACE,KAAK,EAAE;oBACL,cAAc,EAAE,IAAI;oBACpB,CAAC,mBAAmB,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACzD,CAAC,mBAAmB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;oBACtC,sBAAsB,EAAE,CAAC,IAAI,CAAC,OAAO;oBACrC,uBAAuB,EAAE,IAAI,CAAC,SAAS;oBACvC,0BAA0B,EAAE,IAAI,CAAC,QAAQ;iBAC1C,EACD,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eACzB,IAAI,CAAC,QAAQ;gBAEvB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CACnC,4DAAK,KAAK,EAAC,YAAY,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,GAAO,CACvF;gBACA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,4DAAK,KAAK,EAAC,aAAa,GAAO;gBACnE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAC5B,4DAAK,KAAK,EAAC,sBAAsB;oBAC/B,iEAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAa,CAChE,CACP;gBACA,IAAI,CAAC,MAAM,IAAI,CACd,4DAAK,KAAK,EAAC,wBAAwB;oBACjC,mEAAY,IAAI,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,GAAe,CAC7E,CACP;gBACD,4DACE,KAAK,EACH,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC;wBACtC,CAAC,CAAC,qCAAqC;wBACvC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;4BACzC,CAAC,CAAC,sCAAsC;4BACxC,CAAC,CAAC,sCAAsC;oBAG9C,4EAAkB,MAAM,EAAC,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM;wBAC/E,8DAAa,CACJ,CACP;gBACL,IAAI,CAAC,KAAK,IAAI,CACb,4DAAK,KAAK,EAAC,uBAAuB,eAAY,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;oBACvG,CAAC,IAAI,CAAC,QAAQ,IAAI,CACjB,4DAAK,KAAK,EAAC,aAAa,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,GAAO,CACxF;oBACD,iEAAU,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,GAAY,CAC3D,CACP,CACG,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop, Event, EventEmitter, Element, State } from '@stencil/core';\n\nexport type ColorChipClickable = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline';\nexport type Size = 'standard' | 'tall';\n\n@Component({\n  tag: 'bds-chip-clickable',\n  styleUrl: 'chip-clickable.scss',\n  shadow: true,\n})\nexport class ChipClickable {\n  @Element() private element: HTMLElement;\n  @State() visible = true;\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for add avatar left container. Uses the bds-avatar component.\n   */\n  @Prop() avatar?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipClickable = 'default';\n  /**\n   * used for change the size chip. Uses one of them.\n   */\n  @Prop() size?: Size = 'standard';\n  /**\n   * it makes the chip clickable.\n   */\n  @Prop() clickable?: boolean = false;\n  /**\n   * used for delete the chip.\n   */\n  @Prop() close?: boolean = false;\n  /**\n   * the chip gone stay disabled while this prop be true.\n   */\n  @Prop() disabled?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   *  Triggered after a mouse click on close icon, return id element. Only fired when close is true.\n   */\n  @Event() chipClickableClose: EventEmitter;\n  @Event() chipClickableClick: EventEmitter;\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleClick(event) {\n    if (!this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleCloseChip(event) {\n    event.preventDefault();\n    this.chipClickableClose.emit({ id: this.element.id });\n  }\n\n  private handleCloseKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClose.emit({ id: this.element.id });\n    }\n  }\n\n  private getSizeAvatarChip() {\n    if (this.size === 'tall') {\n      return 'extra-small';\n    } else return 'micro';\n  }\n\n  private getSizeIconChip() {\n    if (this.size === 'tall') {\n      return 'medium';\n    } else return 'x-small';\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_clickable: true,\n            [`chip_clickable--${this.color}`]: true && !this.disabled,\n            [`chip_clickable--${this.size}`]: true,\n            'chip_clickable--hide': !this.visible,\n            'chip_clickable--click': this.clickable,\n            'chip_clickable--disabled': this.disabled,\n          }}\n          onClick={this.handleClick.bind(this)}\n          data-test={this.dataTest}\n        >\n          {this.clickable && !this.disabled && (\n            <div class=\"chip_focus\" onKeyDown={this.handleClickKey.bind(this)} tabindex=\"0\"></div>\n          )}\n          {this.clickable && !this.disabled && <div class=\"chip_darker\"></div>}\n          {this.icon && !this.avatar && (\n            <div class=\"chip_clickable--icon\">\n              <bds-icon size={this.getSizeIconChip()} name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.avatar && (\n            <div class=\"chip_clickable--avatar\">\n              <bds-avatar size={this.getSizeAvatarChip()} thumbnail={this.avatar}></bds-avatar>\n            </div>\n          )}\n          <div\n            class={\n              this.close && (this.icon || this.avatar)\n                ? `chip_clickable--container-text--min`\n                : !this.close && !this.icon && !this.avatar\n                  ? `chip_clickable--container-text--full`\n                  : `chip_clickable--container-text--half`\n            }\n          >\n            <bds-typo no-wrap=\"true\" class=\"chip_clickable--text\" variant=\"fs-12\" bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n          {this.close && (\n            <div class=\"chip_clickable--close\" data-test={this.dtButtonClose} onClick={this.handleCloseChip.bind(this)}>\n              {!this.disabled && (\n                <div class=\"close_focus\" onKeyDown={this.handleCloseKey.bind(this)} tabindex=\"0\"></div>\n              )}\n              <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n            </div>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"]}