/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  height: max-content;
  border-radius: 4px;
  box-sizing: border-box;
  max-width: 100%;
}
:host .chip_clickable {
  display: flex;
  min-width: 32px;
  width: fit-content;
  height: 24px;
  border-radius: 12px;
  padding: 2px 6px;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}
:host .chip_clickable--container-text--full {
  width: 100%;
}
:host .chip_clickable--container-text--min {
  width: calc(100% - 36px);
}
:host .chip_clickable--container-text--half {
  width: calc(100% - 16px);
}
:host .chip_clickable--hide {
  display: none;
  padding: 0;
  border: none;
}
:host .chip_clickable .chip_focus:focus {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 2px;
  border-radius: 4px;
  outline: var(--color-focus, rgb(194, 38, 251)) solid 2px;
}
:host .chip_clickable--click {
  cursor: pointer;
}
:host .chip_clickable--click .chip_darker {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  z-index: 1;
  backdrop-filter: brightness(1);
  box-sizing: border-box;
}
:host .chip_clickable--click:hover .chip_darker {
  opacity: 1;
  backdrop-filter: brightness(0.9);
}
:host .chip_clickable--click:active .chip_darker {
  opacity: 1;
  backdrop-filter: brightness(0.8);
}
:host .chip_clickable--disabled {
  cursor: default;
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
:host .chip_clickable--disabled .chip_clickable--icon {
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_clickable--disabled .chip_clickable--text {
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_clickable--disabled .chip_clickable--close {
  cursor: default;
}
:host .chip_clickable--text {
  display: flex;
  align-items: center;
  height: 16px;
  margin: 0;
  padding: 0 2px;
  z-index: 2;
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
  line-height: 1;
}
:host .chip_clickable--icon {
  display: flex;
  align-items: center;
  height: 16px;
  padding-right: 2px;
  z-index: 2;
}
:host .chip_clickable--close {
  display: flex;
  align-items: center;
  height: 16px;
  padding-left: 2px;
  mix-blend-mode: hard-light;
  opacity: 0.5;
  z-index: 2;
  position: relative;
  cursor: pointer;
}
:host .chip_clickable--close .close_focus:focus {
  position: absolute;
  width: 100%;
  height: 100%;
  left: -2px;
  border-radius: 4px;
  outline: var(--color-focus, rgb(194, 38, 251)) solid 2px;
}
:host .chip_clickable--tall {
  height: 32px;
  border-radius: 16px;
  padding: 4px 8px;
}
:host .chip_clickable--tall .chip_clickable--text {
  height: 20px;
  line-height: 1.1;
}
:host .chip_clickable--tall .chip_clickable--icon {
  height: 20px;
  padding-right: 4px;
}
:host .chip_clickable--tall .chip_clickable--close {
  height: 20px;
  padding-left: 4px;
}
:host .chip_clickable--default {
  background-color: var(--color-system, rgb(178, 223, 253));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_clickable--info {
  background-color: var(--color-info, rgb(128, 227, 235));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_clickable--success {
  background-color: var(--color-success, rgb(132, 235, 188));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_clickable--warning {
  background-color: var(--color-warning, rgb(253, 233, 155));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_clickable--danger {
  background-color: var(--color-error, rgb(250, 190, 190));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_clickable--outline {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_clickable:focus-visible {
  outline: none;
}