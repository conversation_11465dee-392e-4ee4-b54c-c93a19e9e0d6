{"version": 3, "file": "image.js", "sourceRoot": "", "sources": ["../../../../src/components/image/image.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AASjF,MAAM,OAAO,KAAK;IALlB;QAMU,oBAAe,GAAY,KAAK,CAAC;QAuBzC;;WAEG;QACK,cAAS,GAAoB,OAAO,CAAC;QAO7C;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAEjC;;WAEG;QACM,gBAAW,GAAG,KAAK,CAAC;QAE7B;;WAEG;QACM,cAAS,GAAG,KAAK,CAAC;KAoE5B;IA7DC,gBAAgB;;QACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,IAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7E,CAAC;IAGD,KAAK,CAAC,SAAS;QACb,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACnC,MAAM,SAAS,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;oBAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACxB,CAAC;YACH,CAAC;YAAC,WAAM,CAAC;gBACP,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,gEAAgE;YAChE,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;QACD,OAAO,CACL,EAAC,IAAI,qDAAC,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAC1C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAClB,WACE,GAAG,EAAE,IAAI,CAAC,UAAU,EACpB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,KAAK,EAAE;gBACL,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,KAAK,EAAE,MAAM;gBACb,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,cAAc,IAAI,CAAC,UAAU,GAAG;aACzC,eACU,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,KAAK,GAChB,CACH,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CACzB,oBAAc,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,GAAgB,CACxE,CAAC,CAAC,CAAC,CACF,wBACE,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,cAAc,EACnB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EACzD,GAAG,EAAE,IAAI,CAAC,GAAG,eACF,IAAI,CAAC,QAAQ,GACN,CACrB,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Element, Component, Prop, Method, State, h, Host } from '@stencil/core';\n\nexport type ObjectFitValue = 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';\n\n@Component({\n  tag: 'bds-image',\n  styleUrl: 'image.scss',\n  shadow: true,\n})\nexport class Image {\n  private imageHasLoading: boolean = false;\n\n  @Element() element: HTMLElement;\n  /**\n   * URL of the main image.\n   */\n  @Prop({ reflect: true, mutable: true }) src?: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Width of the image.\n   */\n  @Prop() width?: string;\n\n  /**\n   * Height of the image.\n   */\n  @Prop() height?: string;\n\n  /**\n   * Specifies the object-fit style for the image. Can be: 'fill', 'contain', 'cover', 'none', 'scale-down'.\n   */\n  @Prop() objectFit?: ObjectFitValue = 'cover';\n\n  /**\n   * Brightness of the image.\n   */\n  @Prop() brightness?: number;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Indicates whether the main image has been successfully loaded.\n   */\n  @State() imageLoaded = false;\n\n  /**\n   * Indicates whether there was an error during image loading.\n   */\n  @State() loadError = false;\n\n  /**\n   * The current source URL of the image to be rendered.\n   */\n  @State() currentSrc: string;\n\n  componentDidLoad() {\n    this.element.style.width = this.width ? this.width : 'auto';\n    this.element.style.height = this.height?.length > 0 ? this.height : 'auto';\n  }\n\n  @Method()\n  async loadImage(): Promise<void> {\n    if (this.src) {\n      this.imageHasLoading = true;\n      try {\n        const response = await fetch(this.src);\n        if (response.ok) {\n          const blob = await response.blob();\n          const objectURL = URL.createObjectURL(blob);\n          this.currentSrc = objectURL;\n          this.imageLoaded = true;\n          this.imageHasLoading = false;\n        } else {\n          this.loadError = true;\n        }\n      } catch {\n        this.imageHasLoading = false;\n        this.loadError = true;\n      }\n    }\n  }\n\n  render(): JSX.Element {\n    if (!this.imageLoaded && !this.loadError) {\n      // Se a imagem ainda não foi carregada, chame o método loadImage\n      this.loadImage();\n    }\n    return (\n      <Host class={{ empty_img: !this.imageLoaded }}>\n        {this.imageLoaded ? (\n          <img\n            src={this.currentSrc}\n            alt={this.alt}\n            style={{\n              objectFit: this.objectFit,\n              width: '100%',\n              height: '100%',\n              filter: `brightness(${this.brightness})`,\n            }}\n            data-test={this.dataTest}\n            draggable={false}\n          />\n        ) : this.imageHasLoading ? (\n          <bds-skeleton shape=\"square\" width=\"100%\" height=\"100%\"></bds-skeleton>\n        ) : (\n          <bds-illustration\n            class=\"img-feedback\"\n            type=\"empty-states\"\n            name={this.loadError ? 'broken-image' : 'image-not-found'}\n            alt={this.alt}\n            data-test={this.dataTest}\n          ></bds-illustration>\n        )}\n      </Host>\n    );\n  }\n}\n"]}