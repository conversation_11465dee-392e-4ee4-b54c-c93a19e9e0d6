/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
}

.spinner_container {
  display: inline-flex;
  position: relative;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
}

.spinner_background {
  border-radius: 50%;
  border: 2px solid;
}
.spinner_background_extra-small {
  border-width: 2px;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
}
.spinner_background_small {
  border-width: 4px;
  width: 32px;
  height: 32px;
  box-sizing: border-box;
}
.spinner_background_standard {
  border-width: 8px;
  width: 64px;
  height: 64px;
  box-sizing: border-box;
}
.spinner_background_main {
  border-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.16;
}
.spinner_background_light {
  border-color: var(--color-content-bright, rgb(255, 255, 255));
  opacity: 0.16;
}
.spinner_background_content {
  border-color: var(--color-surface-0, rgb(255, 255, 255));
  opacity: 0.16;
}
.spinner_background_positive {
  border-color: var(--color-positive, #10603b);
  opacity: 0.16;
}
.spinner_background_negative {
  border-color: var(--color-negative, #e60f0f);
  opacity: 0.16;
}

.spinner_loading {
  animation: rotate 0.5s linear infinite;
  position: absolute;
}
.spinner_loading_extra-small {
  width: 16px;
  height: 16px;
}
.spinner_loading_small {
  width: 32px;
  height: 32px;
}
.spinner_loading_standard {
  width: 64px;
  height: 64px;
}
.spinner_loading_main {
  color: var(--color-primary, rgb(30, 107, 241));
}
.spinner_loading_light {
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.spinner_loading_content {
  color: var(--color-surface-0, rgb(255, 255, 255));
}
.spinner_loading_positive {
  color: var(--color-positive, #10603b);
}
.spinner_loading_negative {
  color: var(--color-negative, #e60f0f);
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}