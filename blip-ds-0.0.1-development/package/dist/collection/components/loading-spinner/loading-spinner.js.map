{"version": 3, "file": "loading-spinner.js", "sourceRoot": "", "sources": ["../../../../src/components/loading-spinner/loading-spinner.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAChE,OAAO,cAAc,MAAM,uCAAuC,CAAC;AACnE,OAAO,SAAS,MAAM,iCAAiC,CAAC;AACxD,OAAO,YAAY,MAAM,oCAAoC,CAAC;AAa9D,MAAM,OAAO,iBAAiB;IAL9B;QAOE;;WAEG;QACK,YAAO,GAA0B,SAAS,CAAC;QACnD;;;WAGG;QACK,SAAI,GAAiB,UAAU,CAAC;QACxC;;;WAGG;QACK,UAAK,GAAoB,MAAM,CAAC;QAExC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAMjC,qDAAqD;QACrD,cAAS,GAAG,CAAC,UAAkB,EAAE,EAAE;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1C,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB,CAAC;YAErC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC,SAAS,CAAC;QACvB,CAAC,CAAC;QAEF,kBAAa,GAAG,GAAG,EAAE;YACnB,MAAM,SAAS,GACb,IAAI,CAAC,IAAI,IAAI,aAAa;gBACxB,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO;oBACpB,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,YAAY,CAAC;YAEhD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC;KA+BH;IAxDC,iBAAiB;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAyBD,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DACE,KAAK,EAAE;oBACL,iBAAiB,EAAE,IAAI;oBACvB,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;iBAC1C,eACU,IAAI,CAAC,QAAQ;gBAExB,4DACE,KAAK,EAAE;wBACL,kBAAkB,EAAE,IAAI;wBACxB,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;wBACzC,CAAC,sBAAsB,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;qBAC3C,GACI;gBACP,4DACE,KAAK,EAAE;wBACL,eAAe,EAAE,IAAI;wBACrB,CAAC,mBAAmB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;wBACtC,CAAC,mBAAmB,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;qBACxC,EACD,SAAS,EAAE,IAAI,CAAC,UAAU,GACrB,CACH,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Prop, State } from '@stencil/core';\nimport loadExtraSmall from '../../assets/svg/load-extra-small.svg';\nimport loadSmall from '../../assets/svg/load-small.svg';\nimport loadStandard from '../../assets/svg/load-standard.svg';\n\nexport type LoadingSpinnerVariant = 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'delete';\nexport type loadingSize = 'extra-small' | 'small' | 'standard';\nexport type colorsVariants = 'main' | 'light' | 'content' | 'positive' | 'negative';\n\nexport type LoadingSpinnerColorMap = { [key in LoadingSpinnerVariant]: string };\n\n@Component({\n  tag: 'bds-loading-spinner',\n  styleUrl: 'loading-spinner.scss',\n  shadow: true,\n})\nexport class BdsLoadingSpinner {\n  @State() private svgContent?: string;\n  /**\n   * \tSets the color of the spinner, can be 'primary', 'secondary' or 'ghost'\n   */\n  @Prop() variant: LoadingSpinnerVariant = 'primary';\n  /**\n   * Size, Entered as one of the size. Can be one of:\n   * 'small', 'standard', 'large'.\n   */\n  @Prop() size?: loadingSize = 'standard';\n  /**\n   * Color, Entered as one of the color. Can be one of:\n   * 'default', 'white'.\n   */\n  @Prop() color?: colorsVariants = 'main';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setSvgContent();\n  }\n\n  /**Function to transform the svg in a div element. */\n  formatSvg = (svgContent: string) => {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    return div.innerHTML;\n  };\n\n  setSvgContent = () => {\n    const innerHTML =\n      this.size == 'extra-small'\n        ? loadExtraSmall\n        : this.size == 'small'\n          ? loadSmall\n          : this.size == 'standard' && loadStandard;\n\n    const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));\n    this.svgContent = this.formatSvg(svg);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            spinner_container: true,\n            [`spinner_background_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          <div\n            class={{\n              spinner_background: true,\n              [`spinner_background_${this.size}`]: true,\n              [`spinner_background_${this.color}`]: true,\n            }}\n          ></div>\n          <div\n            class={{\n              spinner_loading: true,\n              [`spinner_loading_${this.size}`]: true,\n              [`spinner_loading_${this.color}`]: true,\n            }}\n            innerHTML={this.svgContent}\n          ></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}