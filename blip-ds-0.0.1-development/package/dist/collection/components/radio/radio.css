/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
}

.radio {
  display: flex;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  flex-wrap: nowrap;
  /** State Checked */
  /** State Disabled */
}
.radio [type=radio] {
  display: none;
}
.radio [type=radio]:focus {
  outline: 0;
}
.radio__circle {
  transition: all 0.3s;
  transition-property: all;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  transition-delay: 0s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  border: 2px solid var(--color-content-default, rgb(40, 40, 40));
  padding: 4px;
  border-radius: 100%;
  box-sizing: border-box;
  background: transparent;
  position: relative;
}
.radio__circle__pointer {
  transition: all 0.3s;
  transition-property: all;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  transition-delay: 0s;
  border-radius: 100%;
  background: transparent;
  height: 10px;
  width: 10px;
}
.radio__circle .hover {
  width: 0;
  height: 0;
  opacity: 0;
}
.radio__circle .focus:focus-visible {
  display: flex;
  position: absolute;
  border: 2px solid var(--color-focus, rgb(194, 38, 251));
  border-radius: 4px;
  padding: 4px;
  width: 100%;
  height: 100%;
  outline: none;
}
.radio:hover {
  border-color: var(--color-content-disable, rgb(89, 89, 89));
}
.radio:hover .hover {
  display: flex;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  position: absolute;
  width: 36px;
  height: 36px;
  opacity: 1;
  border-radius: 24px;
  transition: width 0.2s, height 0.2s;
}
.radio__text {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  padding-left: 8px;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.radio__input[type=radio]:checked ~ .radio__circle {
  background: transparent;
  border-color: var(--color-content-default, rgb(40, 40, 40));
}
.radio__input[type=radio]:checked ~ .radio__circle .radio__circle__pointer {
  background-color: var(--color-primary, rgb(30, 107, 241));
}
.radio__input[type=radio]:checked ~ .radio__circle:hover {
  border-color: var(--color-content-default, rgb(40, 40, 40));
}
.radio__input[type=radio]:checked ~ .radio__circle:hover .radio__circle__pointer {
  background-color: var(--color-primary, rgb(30, 107, 241));
}
.radio__input[type=radio]:disabled ~ .radio__circle {
  border-color: var(--color-content-disable, rgb(89, 89, 89));
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
.radio__input[type=radio]:disabled ~ .radio__circle .radio__circle__pointer {
  background-color: transparent;
}
.radio__input[type=radio]:disabled:hover ~ .radio__circle {
  border-color: var(--color-content-disable, rgb(89, 89, 89));
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
.radio__input[type=radio]:disabled:hover ~ .radio__circle .radio__circle__pointer {
  background-color: transparent;
}
.radio__input[type=radio]:disabled:checked ~ .radio__circle {
  border-color: var(--color-content-disable, rgb(89, 89, 89));
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
.radio__input[type=radio]:disabled:checked ~ .radio__circle .radio__circle__pointer {
  background-color: var(--color-content-default, rgb(40, 40, 40));
}
.radio__input[type=radio]:disabled:checked:hover ~ .radio__circle {
  border-color: var(--color-content-disable, rgb(89, 89, 89));
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
.radio__input[type=radio]:disabled:checked:hover ~ .radio__circle .radio__circle__pointer {
  background-color: var(--color-content-default, rgb(40, 40, 40));
}
.radio__input[type=radio]:disabled ~ .radio__text {
  color: var(--color-content-disable, rgb(89, 89, 89));
  cursor: not-allowed;
}
.radio__input[type=radio]:disabled ~ .radio__circle {
  cursor: not-allowed;
}