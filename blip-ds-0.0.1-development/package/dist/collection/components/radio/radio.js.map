{"version": 3, "file": "radio.js", "sourceRoot": "", "sources": ["../../../../src/components/radio/radio.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAEpG,IAAI,cAAc,GAAG,CAAC,CAAC;AAMvB,MAAM,OAAO,KAAK;IALlB;QA8BE;;WAEG;QACqC,YAAO,GAAa,KAAK,CAAC;QAElE;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QAEnC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QA2BzB,YAAO,GAAG,CAAC,KAAY,EAAQ,EAAE;YACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YACpD,KAAK,CAAC,eAAe,EAAE,CAAC;QAC1B,CAAC,CAAC;QAEM,mBAAc,GAAG,CAAC,KAAuB,EAAQ,EAAE;YACzD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC;KA+CH;IArEW,cAAc,CAAC,SAAkB;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,CAAC;IAGD,eAAe;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAGD,QAAQ;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC;IAYD,iBAAiB;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,aAAa,cAAc,EAAE,EAAE,CAAC;IAC/D,CAAC;IAEO,cAAc,CAAC,KAAK;QAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,8DAAO,KAAK,EAAC,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACxC,8DACE,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,OAAO,EAChB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,eACJ,IAAI,CAAC,QAAQ,GACxB;gBACF,4DAAK,KAAK,EAAC,eAAe;oBACvB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAK,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAQ,CAAC,CAAC,CAAC,EAAE;oBACvG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAK,KAAK,EAAC,OAAO,GAAO,CAAC,CAAC,CAAC,EAAE;oBAChD,4DAAK,KAAK,EAAC,wBAAwB,GAAO,CACtC;gBAEL,IAAI,CAAC,KAAK,IAAI,CACb,iEAAU,KAAK,EAAC,aAAa,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,EAAC,MAAM,IAC9F,IAAI,CAAC,KAAK,CACF,CACZ;gBAED,8DAAQ,CACF,CACH,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Prop, Event, EventEmitter, Watch, State, Method, Host } from '@stencil/core';\n\nlet radioButtonIds = 0;\n@Component({\n  tag: 'bds-radio',\n  styleUrl: 'radio.scss',\n  shadow: true,\n})\nexport class Radio {\n  private nativeInput?: HTMLInputElement;\n\n  @State() radioId?: string;\n\n  /**\n   * Refer. Field to add refer in radio buttom.\n   */\n  @Prop() refer?: string;\n\n  /**\n   * label in radio, with he the input size increases.\n   */\n  @Prop() label?: string;\n\n  /**\n   * The value of the input.\n   */\n  @Prop() value!: string;\n\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name?: string;\n\n  /**\n   * If `true`, the checkbox is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n\n  /**\n   * If `true`, the user cannot interact with the checkbox.\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsClickChange!: EventEmitter;\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChange.emit({ checked: isChecked });\n  }\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  private onClick = (event: Event): void => {\n    this.checked = true;\n    this.bdsClickChange.emit({ checked: this.checked });\n    event.stopPropagation();\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  connectedCallback(): void {\n    this.radioId = this.refer || `bds-radio-${radioButtonIds++}`;\n  }\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      this.onClick(event);\n      event.preventDefault();\n      this.bdsClickChange.emit({ checked: this.checked });\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <label class=\"radio\" htmlFor={this.radioId}>\n          <input\n            class=\"radio__input\"\n            type=\"radio\"\n            ref={this.refNativeInput}\n            id={this.radioId}\n            onClick={this.onClick}\n            disabled={this.disabled}\n            checked={this.checked}\n            value={this.value}\n            name={this.name}\n            data-test={this.dataTest}\n          />\n          <div class=\"radio__circle\">\n            {!this.disabled ? <div class=\"focus\" tabindex=\"0\" onKeyDown={this.handleClickKey.bind(this)}></div> : ''}\n            {!this.disabled ? <div class=\"hover\"></div> : ''}\n            <div class=\"radio__circle__pointer\"></div>\n          </div>\n\n          {this.label && (\n            <bds-typo class=\"radio__text\" variant=\"fs-14\" bold={this.checked ? 'bold' : 'regular'} tag=\"span\">\n              {this.label}\n            </bds-typo>\n          )}\n\n          <slot />\n        </label>\n      </Host>\n    );\n  }\n}\n"]}