/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  max-width: 100%;
}
:host .chip_tag {
  display: flex;
  align-items: center;
  min-width: 32px;
  width: fit-content;
  height: 24px;
  border-radius: 12px;
  padding: 0px 4px;
  box-sizing: border-box;
}
:host .chip_tag--container-text--full {
  width: 100%;
}
:host .chip_tag--container-text--half {
  width: calc(100% - 16px);
}
:host .chip_tag--icon {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
}
:host .chip_tag--text {
  display: flex;
  align-items: center;
  margin: 0 8px;
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
}
:host .chip_tag--default {
  background-color: var(--color-system, rgb(178, 223, 253));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_tag--info {
  background-color: var(--color-info, rgb(128, 227, 235));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_tag--success {
  background-color: var(--color-success, rgb(132, 235, 188));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_tag--warning {
  background-color: var(--color-warning, rgb(253, 233, 155));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_tag--danger {
  background-color: var(--color-error, rgb(250, 190, 190));
  color: var(--color-content-din, rgb(0, 0, 0));
}
:host .chip_tag--outline {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_tag--disabled {
  background-color: var(--color-surface-3, rgb(227, 227, 227));
  color: var(--color-content-default, rgb(40, 40, 40));
}