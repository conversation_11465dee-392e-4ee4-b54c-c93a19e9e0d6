{"version": 3, "file": "chip-tag.js", "sourceRoot": "", "sources": ["../../../../src/components/chip-tag/chip-tag.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AASzD,MAAM,OAAO,OAAO;IALpB;QAUE;;WAEG;QACK,UAAK,GAAkB,SAAS,CAAC;QACzC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;KA0BlC;IAxBC,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DACE,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAI;oBACd,CAAC,aAAa,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;iBAClC,eACU,IAAI,CAAC,QAAQ;gBAEvB,IAAI,CAAC,IAAI,IAAI,CACZ,4DAAK,KAAK,EAAC,gBAAgB;oBACzB,iEAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAa,CACjD,CACP;gBACD,4DAAK,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,gCAAgC;oBACzF,4EAAkB,MAAM,EAAC,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM;wBACzE,8DAAa,CACJ,CACP,CACF,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop } from '@stencil/core';\n\nexport type ColorChipTag = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline' | 'disabled';\n\n@Component({\n  tag: 'bds-chip-tag',\n  styleUrl: 'chip-tag.scss',\n  shadow: true,\n})\nexport class ChipTag {\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipTag = 'default';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_tag: true,\n            [`chip_tag--${this.color}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.icon && (\n            <div class=\"chip_tag--icon\">\n              <bds-icon size=\"x-small\" name={this.icon}></bds-icon>\n            </div>\n          )}\n          <div class={this.icon ? `chip_tag--container-text--half` : `chip_tag--container-text--full`}>\n            <bds-typo no-wrap=\"true\" class=\"chip_tag--text\" variant=\"fs-12\" bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}