{"version": 3, "file": "warning.js", "sourceRoot": "", "sources": ["../../../../src/components/warning/warning.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,IAAI,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAOvE,MAAM,OAAO,OAAO;IAClB,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,KAAK,EAAC,eAAe;gBACxB,iEAAU,KAAK,EAAC,eAAe,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,GAAY;gBACrF,iEAAU,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAC,KAAK,EAAC,kBAAkB;oBAC3D,8DAAQ,CACC,CACP,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-warning',\n  styleUrl: 'warning.scss',\n  shadow: true,\n})\nexport class Warning implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <div class=\"warning__body\">\n          <bds-icon class=\"warning__icon\" theme=\"solid\" size=\"small\" name=\"warning\"></bds-icon>\n          <bds-typo variant=\"fs-14\" tag=\"span\" class=\"warning__message\">\n            <slot />\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}