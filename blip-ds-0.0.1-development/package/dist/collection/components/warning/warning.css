/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  align-items: center;
}

.warning__body {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  background-color: #ededed;
  border-radius: 8px;
  padding: 16px;
}

.warning__icon {
  color: #f6a721;
}

.warning__message {
  color: #505f79;
  margin-left: 8px;
}