/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
}
:host .actions_select {
  width: 74px;
}

:host(.full_width) {
  width: 100%;
}

@media screen and (max-width: 905px) {
  .items_per_page {
    display: none;
  }
  .actions {
    width: 100%;
    justify-content: center;
  }
}
@media screen and (max-width: 600px) {
  .actions--text {
    display: none;
  }
}