{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["../../../../src/components/pagination/pagination.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AACrG,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAQlD,MAAM,OAAO,UAAU;IALvB;QASE;;;WAGG;QACM,UAAK,GAAW,IAAI,CAAC,WAAW,CAAC;QAU1C;;WAEG;QACM,sBAAiB,GAAG,EAAE,CAAC;QAKhC,2DAA2D;QAClD,aAAQ,GAAiB,IAAI,CAAC;QAYvC;;;WAGG;QACK,oBAAe,GAAmC,MAAM,CAAC;QAEjE,iEAAiE;QACzD,gBAAW,GAAa,KAAK,CAAC;QAQtC,6DAA6D;QACrD,aAAQ,GAAY,OAAO,CAAC;QAEpC;;;WAGG;QACK,oBAAe,GAAY,IAAI,CAAC;QAExC;;;WAGG;QACK,iBAAY,GAAY,IAAI,CAAC;QAErC;;;WAGG;QACK,mBAAc,GAAY,IAAI,CAAC;QAEvC;;;WAGG;QACK,iBAAY,GAAY,IAAI,CAAC;QAErC;;;WAGG;QACK,gBAAW,GAAY,IAAI,CAAC;QA0EpC,aAAQ,GAAG,CAAC,KAAY,EAAE,EAAE;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,gBAAW,GAAG,CAAC,KAAY,EAAE,EAAE;YAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;gBACX,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,cAAS,GAAG,CAAC,KAAY,EAAE,EAAE;YAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;gBACX,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,aAAQ,GAAG,CAAC,KAAY,EAAE,EAAE;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBACxB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,gBAAW,GAAG,GAAG,EAAE;YACjB,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;QACrC,CAAC,CAAC;QAEF,WAAM,GAAG,GAAG,EAAE;YACZ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC1B,CAAC,CAAC;KAsGH;IAvMC,iBAAiB;QACf,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAID,YAAY;QACV,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAGD,YAAY;QACV,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,gBAAgB;QACd,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;YAChD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IA8CD,cAAc,CAAC,KAAK;QAClB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAGD,YAAY,CAAC,KAAK;QAChB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrD,CAAC;IAED,eAAe;QACb,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5E,CAAC;IAED,IAAI,eAAe;QACjB,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC;YACf,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC;YACf;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED,MAAM;;QACJ,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QACjC,OAAO,CACL,EAAC,IAAI,qDAAC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE;YAC3C,oFAA0B,eAAe;gBACtC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,IAAI,CACtC,iEAAU,GAAG,EAAC,GAAG,iBAAa,QAAQ,EAAC,KAAK,EAAC,gBAAgB;oBAC3D,iEAAU,OAAO,EAAC,OAAO;wBAAE,eAAe,CAAC,YAAY;4BAAa;oBACpE,mEAAY,KAAK,EAAC,gBAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,SAAS,sBAAoB,IAAI,CAAC,eAAe,IAC7F,MAAA,IAAI,CAAC,SAAS,0CAAE,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAClC,yBAAmB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAC3E,EAAE,CACe,CACrB,CAAC,CACS;oBACb,iEAAU,OAAO,EAAC,OAAO,aAAS,MAAM;wBACrC,IAAI,CAAC,SAAS;;wBAAG,IAAI,CAAC,OAAO;;wBAAG,eAAe,CAAC,EAAE;;wBAAG,IAAI,CAAC,WAAW,CAC7D,CACF,CACZ;gBAED,iEAAU,GAAG,EAAC,GAAG,iBAAa,QAAQ,EAAC,KAAK,EAAC,SAAS;oBACpD,wEACE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EACtC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,aAAa,EAClB,QAAQ,EAAE,IAAI,CAAC,eAAe,GACb;oBACnB,wEACE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACxC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,YAAY,EACjB,QAAQ,EAAE,IAAI,CAAC,YAAY,GACV;oBAEnB,mEAAY,KAAK,EAAC,gBAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,sBAAoB,IAAI,CAAC,eAAe,IACzF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CACzC,yBAAmB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,IAC7E,EAAE,CACe,CACrB,CAAC,CACS;oBACZ,IAAI,CAAC,WAAW,IAAI,CACnB,iEAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,aAAS,MAAM;wBAC3D,eAAe,CAAC,EAAE;;wBAAG,IAAI,CAAC,KAAK;;wBAAG,eAAe,CAAC,KAAK,CAC/C,CACZ;oBACD,wEACE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EACrC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,aAAa,EAClB,QAAQ,EAAE,IAAI,CAAC,YAAY,GACV;oBACnB,wEACE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EACrC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,YAAY,EACjB,QAAQ,EAAE,IAAI,CAAC,WAAW,GACT,CACV,CACF,CACN,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Element, Prop, State, Event, EventEmitter, Watch } from '@stencil/core';\nimport { getScrollParent } from '../../utils/position-element';\nimport { pt_BR, en_US, es_MX } from './languages';\n\nexport type PaginationOptionsPositionType = 'auto' | 'top' | 'bottom';\n@Component({\n  tag: 'bds-pagination',\n  styleUrl: 'pagination.scss',\n  shadow: true,\n})\nexport class Pagination {\n  // Elemento HTML nativo onde o componente será renderizado\n  @Element() private el!: HTMLElement;\n\n  /**\n   * Estado que armazena o valor selecionado no seletor de página.\n   * Inicialmente, é configurado com a página inicial (startedPage).\n   */\n  @State() value: number = this.startedPage;\n\n  // Estado que armazena o valor selecionado no seletor de itens por página\n  @State() itemValue: number;\n\n  /**\n   * Estado que controla se o seletor de opções de página está aberto ou fechado.\n   */\n  @State() openSelect: boolean;\n\n  /**\n   * Estado que armazena o número de páginas, gerado com base no total de itens e itens por página.\n   */\n  @State() paginationNumbers = [];\n\n  // Estado que armazena o número de itens por página selecionado\n  @State() itemsPerPage: number;\n\n  // Estado que guarda o elemento pai com rolagem (se houver)\n  @State() intoView?: HTMLElement = null;\n\n  /**\n   * Propriedade para receber o número total de páginas, baseado no total de itens e itens por página.\n   */\n  @Prop({ mutable: true, reflect: true }) pages?: number;\n\n  /**\n   * Propriedade que define a página inicial ao renderizar o componente.\n   */\n  @Prop() startedPage?: number;\n\n  /**\n   * Define a posição do menu de opções. Pode ser 'bottom' ou 'top'.\n   * Padrão é 'auto', que ajusta automaticamente a posição.\n   */\n  @Prop() optionsPosition?: PaginationOptionsPositionType = 'auto';\n\n  // Propriedade que controla se o contador de páginas será exibido\n  @Prop() pageCounter?: boolean = false;\n\n  // Propriedade para receber as opções de itens por página (por exemplo, [10, 20, 30])\n  @Prop({ mutable: true, reflect: true }) itemsPage?: any;\n\n  // Propriedade que define o número total de itens que serão paginados\n  @Prop() numberItems?: number;\n\n  // Propriedade para definir o idioma do componente (opcional)\n  @Prop() language?: string = 'pt_BR';\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão inicial.\n   * dtButtonInitial é o data-test para o botão inicial.\n   */\n  @Prop() dtButtonInitial?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de página anterior.\n   * dtButtonPrev é o data-test para o botão anterior.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar o seletor de número de páginas.\n   * dtSelectNumber é o data-test para o seletor de número de páginas.\n   */\n  @Prop() dtSelectNumber?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de próxima página.\n   * dtButtonNext é o data-test para o botão próximo.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão final.\n   * dtButtonEnd é o data-test para o botão final.\n   */\n  @Prop() dtButtonEnd?: string = null;\n\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsPaginationChange: EventEmitter;\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsItemsPerPageChange: EventEmitter;\n\n  // Variável que armazena o número do primeiro item sendo exibido na página atual\n  startItem: number;\n\n  // Variável que armazena o número do último item sendo exibido na página atual\n  endItem: number;\n\n  componentWillLoad() {\n    this.countPage();\n    this.intoView = getScrollParent(this.el);\n    this.processItemsPage();\n    if (this.pageCounter) {\n      this.itemValue = this.itemsPage[0];\n    }\n    this.itemSelected(this.itemValue);\n    this.countItem();\n  }\n\n  @Watch('pages')\n  @Watch('startedPage')\n  pagesChanged(): void {\n    this.countPage();\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsPaginationChange.emit(this.value);\n  }\n\n  processItemsPage() {\n    if (typeof this.itemsPage === 'string') {\n      try {\n        this.itemsPage = JSON.parse(this.itemsPage.replace(/'/g, '\"'));\n      } catch (error) {\n        this.itemsPage = [];\n      }\n    }\n  }\n\n  countItem() {\n    if (this.pageCounter) {\n      const pages = this.numberItems / this.itemValue;\n      this.pages = Math.ceil(pages);\n    }\n  }\n\n  countPage() {\n    if (this.paginationNumbers.length !== 0) {\n      this.paginationNumbers = [];\n    }\n    if (this.paginationNumbers.length === 0) {\n      for (let i = 1; i <= this.pages; i++) {\n        this.paginationNumbers.push(i);\n      }\n      if (this.startedPage && this.startedPage < this.pages) {\n        this.value = this.startedPage;\n      } else {\n        this.value = this.paginationNumbers[0];\n      }\n    }\n  }\n\n  nextPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.value + 1;\n      this.updateItemRange();\n    }\n  };\n\n  previewPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.value - 1;\n      this.updateItemRange();\n    }\n  };\n\n  firstPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.paginationNumbers[0];\n      this.updateItemRange();\n    }\n  };\n\n  lastPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.pages;\n      this.updateItemRange();\n    }\n  };\n\n  openOptions = () => {\n    this.openSelect = !this.openSelect;\n  };\n\n  onBlur = () => {\n    this.openSelect = false;\n  };\n\n  optionSelected(index) {\n    this.value = index;\n    this.openOptions();\n    this.updateItemRange();\n  }\n\n  @Watch('itemValue')\n  itemSelected(index) {\n    this.itemValue = index;\n    this.itemsPerPage = index;\n    this.openOptions();\n    this.countItem();\n    this.updateItemRange();\n    this.bdsItemsPerPageChange.emit(this.itemsPerPage);\n  }\n\n  updateItemRange() {\n    this.startItem = (this.value - 1) * this.itemsPerPage + 1;\n    this.endItem = Math.min(this.value * this.itemsPerPage, this.numberItems);\n  }\n\n  get currentLanguage() {\n    switch (this.language) {\n      case 'en_US':\n        return en_US;\n      case 'es_MX':\n        return es_MX;\n      default:\n        return pt_BR;\n    }\n  }\n\n  render() {\n    const { currentLanguage } = this;\n    return (\n      <Host class={{ full_width: this.pageCounter }}>\n        <bds-grid justify-content=\"space-between\">\n          {this.itemsPerPage && this.itemsPage && (\n            <bds-grid gap=\"1\" align-items=\"center\" class=\"items_per_page\">\n              <bds-typo variant=\"fs-14\">{currentLanguage.itemsPerPage}:</bds-typo>\n              <bds-select class=\"actions_select\" value={this.itemValue} options-position={this.optionsPosition}>\n                {this.itemsPage?.map((el, index) => (\n                  <bds-select-option key={index} value={el} onClick={() => this.itemSelected(el)}>\n                    {el}\n                  </bds-select-option>\n                ))}\n              </bds-select>\n              <bds-typo variant=\"fs-14\" no-wrap=\"true\">\n                {this.startItem}-{this.endItem} {currentLanguage.of} {this.numberItems}\n              </bds-typo>\n            </bds-grid>\n          )}\n\n          <bds-grid gap=\"1\" align-items=\"center\" class=\"actions\">\n            <bds-button-icon\n              onBdsClick={(ev) => this.firstPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-first\"\n              dataTest={this.dtButtonInitial}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.previewPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-left\"\n              dataTest={this.dtButtonPrev}\n            ></bds-button-icon>\n\n            <bds-select class=\"actions_select\" value={this.value} options-position={this.optionsPosition}>\n              {this.paginationNumbers.map((el, index) => (\n                <bds-select-option key={index} value={el} onClick={() => this.optionSelected(el)}>\n                  {el}\n                </bds-select-option>\n              ))}\n            </bds-select>\n            {this.pageCounter && (\n              <bds-typo class=\"actions--text\" variant=\"fs-14\" no-wrap=\"true\">\n                {currentLanguage.of} {this.pages} {currentLanguage.pages}\n              </bds-typo>\n            )}\n            <bds-button-icon\n              onBdsClick={(ev) => this.nextPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-right\"\n              dataTest={this.dtButtonNext}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.lastPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-last\"\n              dataTest={this.dtButtonEnd}\n            ></bds-button-icon>\n          </bds-grid>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"]}