{"version": 3, "file": "toast-container.js", "sourceRoot": "", "sources": ["../../../../src/components/toast-container/toast-container.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAOnD,MAAM,OAAO,iBAAiB;IAC5B,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-toast-container',\n  styleUrl: 'toast-container.scss',\n  scoped: true,\n})\nexport class BdsToastContainer {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}