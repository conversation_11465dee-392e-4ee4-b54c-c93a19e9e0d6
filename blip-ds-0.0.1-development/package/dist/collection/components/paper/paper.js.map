{"version": 3, "file": "paper.js", "sourceRoot": "", "sources": ["../../../../src/components/paper/paper.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAQpF,MAAM,OAAO,KAAK;IALlB;QAMW,cAAS,GAAG,IAAI,CAAC;QAE1B;;;WAGG;QACqC,cAAS,GAAoB,QAAQ,CAAC;QAE9E;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAEjC;;WAEG;QACK,WAAM,GAAa,KAAK,CAAC;QACjC;;WAEG;QACK,WAAM,GAAY,IAAI,CAAC;QAE/B;;WAEG;QACK,UAAK,GAAY,IAAI,CAAC;QAE9B;;WAEG;QACK,YAAO,GAAqB,WAAW,CAAC;QAEhD;;WAEG;QACK,gBAAW,GAAiB,IAAI,CAAC;KAuB1C;IArBC,iBAAiB;QACf,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;IAC5E,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,KAAK,EAAE;gBACL,CAAC,qBAAqB,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS;gBACvD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI;gBAC5B,CAAC,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI;aACrC,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE;YAE3D,4DAAK,KAAK,EAAC,gBAAgB,eAAY,IAAI,CAAC,QAAQ;gBAClD,8DAAa,CACT,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, Host, h, Prop, State } from '@stencil/core';\nimport { PaperElevation, PaperBackground, BorderColor } from './paper-interface';\n\n@Component({\n  tag: 'bds-paper',\n  styleUrl: 'paper.scss',\n  shadow: true,\n})\nexport class Paper implements ComponentInterface {\n  @State() hasBorder = true;\n  @State() constElevation: string;\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'static', 'primary', 'secondary';\n   */\n  @Prop({ mutable: true, reflect: true }) elevation?: PaperElevation = 'static';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Prop for set the border of the component.\n   */\n  @Prop() border?: boolean = false;\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string = null;\n\n  /**\n   * Prop for set the width of the component.\n   */\n  @Prop() width?: string = null;\n\n  /**\n   * Prop for set the background color.\n   */\n  @Prop() bgColor?: PaperBackground = 'surface-1';\n\n  /**\n   * Prop for set the border color.\n   */\n  @Prop() borderColor?: BorderColor = null;\n\n  componentWillLoad() {\n    this.border === true ? (this.hasBorder = false) : (this.hasBorder = true);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`paper__elevation--${this.elevation}`]: this.hasBorder,\n          border: this.border,\n          [`bg-${this.bgColor}`]: true,\n          [`border-${this.borderColor}`]: true,\n        }}\n        style={{ height: `${this.height}`, width: `${this.width}` }}\n      >\n        <div class=\"paper__display\" data-test={this.dataTest}>\n          <slot></slot>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}