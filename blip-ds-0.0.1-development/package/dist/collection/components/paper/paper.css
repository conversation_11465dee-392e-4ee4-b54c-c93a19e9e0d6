/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.bds-hover:hover {
  mix-blend-mode: multiply;
}

.focus::before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
}
.focus:focus-visible {
  outline: none;
}
.focus:focus-visible::before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}

.disabled {
  pointer-events: none;
}

.bg-surface-1 {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
}

.bg-surface-2 {
  background-color: var(--color-surface-2, rgb(237, 237, 237));
}

.bg-surface-3 {
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}

.bg-surface-4 {
  background-color: var(--color-surface-4, rgb(20, 20, 20));
}

:host {
  display: block;
  border-radius: 16px;
}

:host(.border) {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  margin: -1px;
}

:host(.bg-surface-0) {
  background-color: var(--color-surface-0, rgb(255, 255, 255));
}

:host(.bg-surface-1) {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
}

:host(.bg-surface-2) {
  background-color: var(--color-surface-2, rgb(237, 237, 237));
}

:host(.bg-surface-3) {
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}

:host(.bg-surface-4) {
  background-color: var(--color-surface-4, rgb(20, 20, 20));
}

:host(.border-1) {
  border-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}

:host(.border-2) {
  border-color: var(--color-border-2, rgba(0, 0, 0, 0.16));
}

:host(.border-3) {
  border-color: var(--color-border-3, rgba(0, 0, 0, 0.06));
}

:host(.border-primary) {
  border-color: var(--color-primary, rgb(30, 107, 241));
}

:host(.border-secondary) {
  border-color: var(--color-secondary, rgb(41, 41, 41));
}

:host(.border-positive) {
  border-color: var(--color-positive, #10603b);
}

:host(.border-negative) {
  border-color: var(--color-negative, #e60f0f);
}

:host(.border-warning) {
  border-color: var(--color-warning, rgb(253, 233, 155));
}

:host(.border-error) {
  border-color: var(--color-error, rgb(250, 190, 190));
}

:host(.border-success) {
  border-color: var(--color-success, rgb(132, 235, 188));
}

:host(.border-delete) {
  border-color: var(--color-delete, rgb(230, 15, 15));
}

:host(.paper__elevation--none) {
  box-shadow: none;
}

:host(.paper__elevation--static) {
  box-shadow: 0px 2px 8px -2px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
}

:host(.paper__elevation--primary) {
  box-shadow: 0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
}

:host(.paper__elevation--secondary) {
  box-shadow: 0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
}

.paper__display {
  display: contents;
}