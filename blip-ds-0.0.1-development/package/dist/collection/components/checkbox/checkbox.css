/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.checkbox {
  display: inline;
}
.checkbox input[type=checkbox] {
  display: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
  margin: 0;
}
.checkbox input[type=checkbox]:focus {
  outline: 0;
}
.checkbox__icon {
  position: relative;
}
.checkbox__icon::before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
}
.checkbox__icon:focus-visible {
  outline: none;
}
.checkbox__icon:focus-visible::before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}
.checkbox__icon:hover {
  border-color: var(--color-brand, rgb(0, 150, 250));
}
.checkbox--selected .checkbox__icon {
  background-color: var(--color-surface-primary, rgb(30, 107, 241));
  border-color: var(--color-surface-primary, rgb(30, 107, 241));
}
.checkbox--selected .checkbox__icon__svg {
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.checkbox--selected .checkbox__icon:hover {
  background-color: var(--color-brand, rgb(0, 150, 250));
}
.checkbox--selected-disabled .checkbox__label {
  cursor: not-allowed;
}
.checkbox--selected-disabled .checkbox__icon {
  color: var(--color-content-default, rgb(40, 40, 40));
  border-color: var(--color-content-default, rgb(40, 40, 40));
  background-color: var(--color-surface-3, rgb(227, 227, 227));
  opacity: 50%;
}
.checkbox--selected-disabled .checkbox__text {
  opacity: 50%;
}
.checkbox--deselected .checkbox__icon__svg {
  display: none;
}
.checkbox--deselected-disabled .checkbox__label {
  cursor: not-allowed;
}
.checkbox--deselected-disabled .checkbox__icon {
  opacity: 50%;
  background-color: var(--color-surface-1, rgb(246, 246, 246));
  border: 1px solid var(--color-brand, rgb(0, 150, 250));
}
.checkbox--deselected-disabled .checkbox__icon__svg {
  display: none;
}
.checkbox__label {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  max-width: fit-content;
}
.checkbox__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 18px;
  width: 18px;
  min-width: 18px;
  border-radius: 4px;
  color: var(--color-surface-1, rgb(246, 246, 246));
  border: 1px solid var(--color-content-default, rgb(40, 40, 40));
  box-sizing: border-box;
  border-radius: 4px;
  transition: all 0.3s;
  transition-property: all;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  transition-delay: 0s;
}
.checkbox__text {
  margin-left: 8px;
  color: var(--color-content-default, rgb(40, 40, 40));
}