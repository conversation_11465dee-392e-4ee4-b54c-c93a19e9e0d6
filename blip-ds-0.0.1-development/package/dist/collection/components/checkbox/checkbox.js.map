{"version": 3, "file": "checkbox.js", "sourceRoot": "", "sources": ["../../../../src/components/checkbox/checkbox.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAgB,MAAM,eAAe,CAAC;AAEvF,IAAI,WAAW,GAAG,CAAC,CAAC;AAMpB,MAAM,OAAO,QAAQ;IALrB;QAmBE;;WAEG;QACqC,YAAO,GAAG,KAAK,CAAC;QAExD;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAkCzB,YAAO,GAAG,CAAC,EAAS,EAAQ,EAAE;YACpC,EAAE,CAAC,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC,CAAC;QAEM,mBAAc,GAAG,CAAC,KAAuB,EAAQ,EAAE;YACzD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC;QAEM,kBAAa,GAAG,GAAW,EAAE;YACnC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,OAAO,oBAAoB,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpC,OAAO,sBAAsB,CAAC;YAChC,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,OAAO,6BAA6B,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,OAAO,+BAA+B,CAAC;YACzC,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;KA4CH;IA1GC,iBAAiB;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,gBAAgB,WAAW,EAAE,EAAE,CAAC;IAClE,CAAC;IAaD,eAAe;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAGD,QAAQ;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC;IAGD,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAkCO,aAAa,CAAC,KAAK;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAExC,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI;gBACd,CAAC,UAAU,CAAC,EAAE,IAAI;aACnB;YAED,8DACE,IAAI,EAAC,UAAU,EACf,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,UAAU,EACnB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EACjC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,GACjB;YACT,8DAAO,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,UAAU;gBACrD,4DAAK,KAAK,EAAC,gBAAgB,EAAC,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC/E,iEAAU,KAAK,EAAC,qBAAqB,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,KAAK,EAAC,SAAS,GAAY,CACxF;gBACL,IAAI,CAAC,KAAK,IAAI,CACb,iEAAU,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACxD,IAAI,CAAC,KAAK,CACF,CACZ,CACK,CACJ,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Prop, State, Method, Event, EventEmitter } from '@stencil/core';\n\nlet checkBoxIds = 0;\n@Component({\n  tag: 'bds-checkbox',\n  styleUrl: 'checkbox.scss',\n  shadow: true,\n})\nexport class Checkbox {\n  private nativeInput?: HTMLInputElement;\n\n  @State() checkBoxId?: string;\n\n  @Prop() refer!: string;\n\n  @Prop() label!: string;\n\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name!: string;\n\n  /**\n   * If `true`, the checkbox is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked = false;\n\n  /**\n   * If `true`, the user cannot interact with the checkbox.\n   */\n  @Prop() disabled = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    this.checkBoxId = this.refer || `bds-checkbox-${checkBoxIds++}`;\n  }\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<KeyboardEvent>;\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  @Method()\n  async toggle() {\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  }\n\n  private onClick = (ev: Event): void => {\n    ev.stopPropagation();\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  private getStyleState = (): string => {\n    if (this.checked && !this.disabled) {\n      return 'checkbox--selected';\n    }\n\n    if (!this.checked && !this.disabled) {\n      return 'checkbox--deselected';\n    }\n\n    if (this.checked && this.disabled) {\n      return 'checkbox--selected-disabled';\n    }\n\n    if (!this.checked && this.disabled) {\n      return 'checkbox--deselected-disabled';\n    }\n\n    return '';\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.checked = !this.checked;\n      this.bdsChange.emit({\n        checked: this.checked,\n      });\n    }\n  }\n\n  render(): HTMLElement {\n    const styleState = this.getStyleState();\n\n    return (\n      <div\n        class={{\n          checkbox: true,\n          [styleState]: true,\n        }}\n      >\n        <input\n          type=\"checkbox\"\n          ref={this.refNativeInput}\n          id={this.checkBoxId}\n          name={this.name}\n          onClick={(ev) => this.onClick(ev)}\n          checked={this.checked}\n          disabled={this.disabled}\n          data-test={this.dataTest}\n        ></input>\n        <label class=\"checkbox__label\" htmlFor={this.checkBoxId}>\n          <div class=\"checkbox__icon\" tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)}>\n            <bds-icon class=\"checkbox__icon__svg\" size=\"x-small\" name=\"true\" color=\"inherit\"></bds-icon>\n          </div>\n          {this.label && (\n            <bds-typo class=\"checkbox__text\" variant=\"fs-14\" tag=\"span\">\n              {this.label}\n            </bds-typo>\n          )}\n        </label>\n      </div>\n    );\n  }\n}\n"]}