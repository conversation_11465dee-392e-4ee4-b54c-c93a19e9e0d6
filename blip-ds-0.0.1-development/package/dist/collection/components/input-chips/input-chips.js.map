{"version": 3, "file": "input-chips.js", "sourceRoot": "", "sources": ["../../../../src/components/input-chips/input-chips.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAgB,KAAK,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACpG,OAAO,EAAE,eAAe,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAQhF,MAAM,OAAO,UAAU;IALvB;QAQW,cAAS,GAAY,IAAI,CAAC;QAEnC;;WAEG;QACM,qBAAgB,GAAa,KAAK,CAAC;QAC5C;;WAEG;QACM,kBAAa,GAAa,IAAI,CAAC;QACxC;;WAEG;QACM,cAAS,GAAI,KAAK,CAAC;QAE5B;;WAEG;QACM,qBAAgB,GAAI,EAAE,CAAC;QAEvB,kBAAa,GAAa,EAAE,CAAC;QAEtC;;;;WAIG;QACsB,UAAK,GAAsB,EAAE,CAAC;QAEvD;;WAEG;QACK,iBAAY,GAAG,KAAK,CAAC;QAE7B;;;WAGG;QACK,SAAI,GAAoB,MAAM,CAAC;QAEvC;;WAEG;QACK,UAAK,GAAI,EAAE,CAAC;QAWpB;;WAEG;QACsB,SAAI,GAAY,EAAE,CAAC;QAE5C;;WAEG;QACK,eAAU,GAAI,KAAK,CAAC;QAE5B;;WAEG;QACsB,iBAAY,GAAI,EAAE,CAAC;QAE5C;;WAEG;QACqC,WAAM,GAAI,KAAK,CAAC;QACxD;;WAEG;QACqC,YAAO,GAAa,KAAK,CAAC;QAClE;;WAEG;QACqC,UAAK,GAAmB,EAAE,CAAC;QAEnE;;WAEG;QACK,eAAU,GAAa,KAAK,CAAC;QAErC;;WAEG;QACK,kBAAa,GAAG,KAAK,CAAC;QAE9B;;WAEG;QACsB,aAAQ,GAAa,KAAK,CAAC;QAEpD;;WAEG;QACK,kBAAa,GAAY,EAAE,CAAC;QACpC;;WAEG;QACsB,mBAAc,GAAY,EAAE,CAAC;QACtD;;WAEG;QACK,cAAS,GAAY,EAAE,CAAC;QAEhC;;WAEG;QACK,gBAAW,GAAY,EAAE,CAAC;QAElC;;;WAGG;QACK,kBAAa,GAAI,KAAK,CAAC;QAS/B;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QACjC;;;WAGG;QACK,kBAAa,GAAY,IAAI,CAAC;QA0H9B,mBAAc,GAAG,GAAS,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEM,YAAO,GAAG,GAAS,EAAE;YAC3B,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC;QAeM,YAAO,GAAG,CAAC,EAAc,EAAQ,EAAE;YACzC,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC,CAAC;YACnD,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACjC,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,CAAC;QAiBM,oBAAe,GAAG,CAAC,KAAoB,EAAQ,EAAE;YACvD,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACzB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;oBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBAC7E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBAClF,MAAM;gBACR,KAAK,WAAW,CAAC;gBACjB,KAAK,QAAQ;oBACX,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;wBACjF,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;wBAC7E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBACpF,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC,CAAC;KA6QH;IAtaC;;OAEG;IAEO,YAAY;QACpB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9C,CAAC;gBAAC,WAAM,CAAC;oBACP,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;YAClC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAGS,oBAAoB;QAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,GAAG;QACP,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAGD,KAAK,CAAC,GAAG,CAAC,KAAa;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAGD,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAGD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAcO,YAAY;QAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAUO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAsBO,gBAAgB;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE/B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,6BAA6B,CAAC,KAAa;QACjD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAEhE,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAqC;QAC9D,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK,CAAC;QAEV,iEAAiE;QAEjE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE/B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IACpF,CAAC;IAEO,gBAAgB,CAAC,KAAK,GAAG,EAAE;QACjC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEO,OAAO,CAAC,IAAY;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC5F,IAAI,MAAM;gBAAE,OAAO;QACrB,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClF,CAAC;IAEO,UAAU,CAAC,KAAkC;QACnD,MAAM,EACJ,MAAM,EAAE,EAAE,EAAE,EAAE,GACf,GAAG,KAAK,CAAC;QAEV,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1F,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IACpF,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC5C,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC5B,kFAAkF;YAClF,MAAM,KAAK,GAAG,EAAE,CAAC;YACjB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;gBACzB,OAAO,CACL,0BACE,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACvD,aAAa,EAAE,IAAI,CAAC,aAAa,IAEhC,IAAI,CACc,CACtB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CACL,mBAAa,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,kBAAe,IAAI;oBAC5D,0BACE,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACvD,aAAa,EAAE,IAAI,CAAC,aAAa,IAEhC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CACV,CACT,CACf,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU;QAChB,OAAO,CACL,IAAI,CAAC,IAAI,IAAI,CACX,WACE,KAAK,EAAE;gBACL,WAAW,EAAE,IAAI;gBACjB,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;aACnC;YAED,gBAAU,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,GAAY,CACzF,CACP,CACF,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,CACL,IAAI,CAAC,KAAK,IAAI,CACZ,aACE,KAAK,EAAE;gBACL,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE;YAED,gBAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,IAClC,IAAI,CAAC,KAAK,CACF,CACL,CACT,CACF,CAAC;IACJ,CAAC;IAEO,aAAa;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;QACzE,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAExG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEvE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAClC,CAAC,CAAC,uCAAuC;YACzC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACZ,CAAC,CAAC,wCAAwC;gBAC1C,CAAC,CAAC,gBAAgB,CAAC;QAEzB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CACL,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB;gBACvC,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,gBAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E;gBACN,gBAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,IACnD,OAAO,CACC,CACP,CACP,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnD,+DAA+D;QAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC;QAElD,OAAO,CACL,EAAC,IAAI,sEAAgB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;YAChD,4DACE,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI;oBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBAC9D,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;oBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;oBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;oBACtC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;oBAC5B,gBAAgB,EAAE,SAAS;iBAC5B,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB;gBAErB,IAAI,CAAC,UAAU,EAAE;gBAClB,4DAAK,KAAK,EAAC,kBAAkB;oBAC1B,IAAI,CAAC,WAAW,EAAE;oBACnB,4DACE,KAAK,EAAC,2BAA2B,EACjC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE;wBAGrC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;wBACnD,IAAI,CAAC,aAAa,IAAI,CACrB,8DACE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,KAAK,EAAC,wBAAwB,EAC9B,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,EACjC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EACjC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,GACxB,CACH,CACG,CACF;gBACL,IAAI,CAAC,aAAa,IAAI,CACrB,yEAAkB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,SAAS,GAAI,CACrG;gBACA,IAAI,CAAC,OAAO,IAAI,iEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG;gBACjG,6DAAM,IAAI,EAAC,aAAa,GAAQ,CAC5B;YACL,IAAI,CAAC,aAAa,EAAE,CAChB,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop, Method, Event, EventEmitter, Watch, State } from '@stencil/core';\nimport { emailValidation, whitespaceValidation } from '../../utils/validations';\nimport { InputChipsTypes } from './input-chips-interface';\n\n@Component({\n  tag: 'bds-input-chips',\n  styleUrl: 'input-chips.scss',\n  shadow: true,\n})\nexport class InputChips {\n  private nativeInput?: HTMLInputElement;\n\n  @State() InputSize?: number = null;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Used to enable or disable input\n   */\n  @State() inputAvalible?: boolean = true;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string[] | string = [];\n\n  /**\n   * When true, the press enter will be simulated on blur event.\n   */\n  @Prop() blurCreation = false;\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   *  Set maximum length value for the chip content\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   *  Set maximum length value for chips\n   */\n  @Prop() maxChipsLength?: number;\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n\n  /**\n   * Disabled input\n   */\n  @Prop({ reflect: true }) disabled?: boolean = false;\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Passing true to display a counter of available size, it is necessary to\n   * pass another maxlength property.\n   */\n  @Prop() counterLength? = false;\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsFocus!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsBlur!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsInput!: EventEmitter;\n\n  /**\n   * Emitted when a maximum value defined by the \"max-chips-length\" prop is entered\n   */\n  @Event() bdsExtendedQuantityInput!: EventEmitter;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.minMaxValidation();\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async get(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentDidLoad() {\n    this.minMaxValidation();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n  }\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsInputChipsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit(this.internalChips);\n    if (this.internalChips.length > 0) {\n      this.bdsSubmit.emit({ value: this.internalChips });\n    }\n    this.handleDelimiters();\n    this.isPressed = false;\n    if (this.blurCreation) {\n      this.setChip(this.value);\n      this.value = '';\n    }\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInputChipsInput.emit(ev);\n  };\n\n  private minMaxValidation() {\n    if (!this.maxChipsLength == undefined) {\n      this.inputAvalible = true;\n    } else if (this.internalChips.length >= this.maxChipsLength) {\n      this.inputAvalible = false;\n      this.bdsExtendedQuantityInput.emit({ value: !this.inputAvalible });\n    } else {\n      this.inputAvalible = true;\n    }\n  }\n\n  private getLastChip(): string {\n    return this.internalChips[this.internalChips.length - 1];\n  }\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.handleDelimiters();\n        this.setChip(this.value);\n        this.value = '';\n        this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n        this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        }\n        break;\n    }\n  };\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    const {\n      detail: { value },\n    } = event;\n\n    // console.log('TRACE [input-chips] handleChange 1:', { value });\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      // Reduce the limit to prevent chips from being too wide and causing scroll issues\n      const limit = 20;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n            dtButtonClose={this.dtButtonClose}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n              dtButtonClose={this.dtButtonClose}\n            >\n              {`${chip.slice(0, limit)}...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n    // Set default maxHeight if not provided to prevent UI breaking\n    const defaultMaxHeight = this.maxHeight || '80px';\n    \n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div \n              class=\"input__container__wrapper\"\n              style={{ maxHeight: defaultMaxHeight }}\n            >\n              {/* Chips and input are now siblings in the same flex container */}\n              {this.internalChips.length > 0 && this.renderChips()}\n              {this.inputAvalible && (\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class=\"input__container__text\"\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                />\n              )}\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text length={this.internalChips.length} max={this.maxChipsLength} active={isPressed} />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"checkb\" theme=\"outline\" size=\"xxx-small\" />}\n          <slot name=\"input-right\"></slot>\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"]}