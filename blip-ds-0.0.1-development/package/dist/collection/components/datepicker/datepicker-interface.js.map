{"version": 3, "file": "datepicker-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/datepicker/datepicker-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nexport interface DaysList {\n  date: number;\n  month: number;\n  year: number;\n  day: number;\n}\n/* eslint-disable @typescript-eslint/no-explicit-any */\nexport interface MonthsSlide {\n  year: number;\n  month: number;\n  days: DaysList[];\n}\n/* eslint-disable @typescript-eslint/no-explicit-any */\nexport interface Options {\n  value: number;\n  label: string;\n}\n"]}