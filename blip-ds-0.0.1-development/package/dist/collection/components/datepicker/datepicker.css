/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
}
:host input,
:host textarea {
  box-shadow: inherit;
}
:host input::placeholder,
:host textarea::placeholder {
  color: var(--color-content-ghost, rgb(140, 140, 140));
  opacity: 1;
}
:host input::-webkit-input-placeholder,
:host textarea::-webkit-input-placeholder {
  color: var(--color-content-ghost, rgb(140, 140, 140));
  opacity: 1;
}

.input {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 4px 8px 12px;
  flex: 1;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  gap: 8px;
}
.input .bds-icon {
  position: relative;
  z-index: 1;
}
.input--state-primary {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-primary .input__icon {
  position: relative;
}
.input--state-primary .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  z-index: 0;
  border-radius: 8px;
}
.input--state-primary:hover {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-primary.input--pressed {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
  box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
}
.input--state-primary.input--pressed .input__icon .bds-icon {
  color: var(--color-primary, rgb(30, 107, 241));
}
.input--state-primary .input__container__label {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-primary .input__container__label--pressed bds-typo {
  color: var(--color-primary, rgb(30, 107, 241));
}
.input--state-primary .input__container__text {
  caret-color: var(--color-primary, rgb(30, 107, 241));
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-danger {
  border: 1px solid var(--color-delete, rgb(230, 15, 15));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-danger .input__icon {
  position: relative;
}
.input--state-danger .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-negative, rgb(138, 0, 0));
  z-index: 0;
  opacity: 50%;
  border-radius: 8px;
}
.input--state-danger:hover {
  border: 1px solid var(--color-negative, #e60f0f);
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-danger.input--pressed {
  border: 1px solid var(--color-negative, #e60f0f);
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-error, rgb(250, 190, 190));
  box-shadow: 0 0 0 2px var(--color-error, rgb(250, 190, 190));
}
.input--state-danger.input--pressed .input__icon .bds-icon {
  color: var(--color-negative, #e60f0f);
}
.input--state-danger .input__container__label {
  color: var(--color-delete, rgb(230, 15, 15));
}
.input--state-danger .input__container__label--pressed bds-typo {
  color: var(--color-negative, #e60f0f);
}
.input--state-danger .input__container__text {
  caret-color: var(--color-negative, #e60f0f);
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-success {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-success .input__icon {
  position: relative;
}
.input--state-success .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-positive, rgb(1, 114, 62));
  z-index: 0;
  border-radius: 8px;
}
.input--state-success:hover {
  border: 1px solid var(--color-positive, #10603b);
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-success.input--pressed {
  border: 1px solid var(--color-positive, #10603b);
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-success, rgb(132, 235, 188));
  box-shadow: 0 0 0 2px var(--color-success, rgb(132, 235, 188));
}
.input--state-success.input--pressed .input__icon .bds-icon {
  color: var(--color-positive, #10603b);
}
.input--state-success .input__container__label {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-success .input__container__label--pressed bds-typo {
  color: var(--color-positive, #10603b);
}
.input--state-success .input__container__text {
  caret-color: var(--color-positive, #10603b);
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
}
.input--state-disabled .input__icon {
  position: relative;
}
.input--state-disabled .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  z-index: 0;
  opacity: 50%;
  border-radius: 8px;
}
.input .icon-success {
  color: var(--color-positive, #10603b);
  margin-left: 4px;
}
.input--label {
  padding: 7px 4px 8px 12px;
}
.input__icon {
  cursor: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 2px;
}
.input__container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}
.input__container__wrapper {
  display: flex;
  flex-wrap: wrap;
}
.input__container__wrapper__chips {
  display: inline;
  max-height: 100px;
  overflow: auto;
}
.input__container__wrapper__chips::-webkit-scrollbar {
  width: 16px;
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
  border-radius: 10px;
}
.input__container__wrapper__chips::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 4px solid transparent;
  border-radius: 10px;
  background-clip: content-box;
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.input__container__label {
  display: flex;
  align-items: center;
}
.input__container__text {
  display: inline-block;
  margin: 0;
  border: 0;
  padding: 0;
  width: auto;
  vertical-align: middle;
  white-space: normal;
  line-height: inherit;
  background: none;
  /* Browsers have different default form fonts */
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  /* Make webkit render the search input like a normal text field */
  /* Turn off the recent search for webkit. It adds about 15px padding on the left */
  /* Fix IE7 display bug */
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
  font-size: 0.875rem;
  line-height: 22px;
  resize: none;
  cursor: inherit;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text::-webkit-file-upload-button {
  padding: 0;
  border: 0;
  background: none;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text[type=checkbox], .input__container__text[type=radio] {
  width: 13px;
  height: 13px;
}
.input__container__text[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
}
::-webkit-search-decoration {
  display: none;
}

.input__container__text[type=reset], .input__container__text[type=button], .input__container__text[type=submit] {
  overflow: visible;
}
.input__container__text::-webkit-scrollbar {
  width: 16px;
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
  border-radius: 10px;
}
.input__container__text::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 4px solid transparent;
  border-radius: 10px;
  background-clip: content-box;
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.input__container__text[type=date]::-webkit-calendar-picker-indicator {
  opacity: 0;
  pointer-events: none;
}
.input__message {
  display: flex;
  align-items: baseline;
  height: 20px;
  margin: 3.7px 2.5px;
  gap: 4px;
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--color-content-disable, rgb(89, 89, 89));
  word-break: break-word;
  height: auto;
  min-height: 20px;
}
.input__message bds-typo {
  margin-top: 0px;
  align-self: self-start;
}
.input__message__icon {
  display: flex;
  margin-top: 0px;
}
.input__message--danger .input__message__icon .bds-icon {
  color: var(--color-negative, #e60f0f);
}
.input__message--danger .input__message__text {
  color: var(--color-negative, #e60f0f);
}
.input__message--success .input__message__icon .bds-icon {
  color: var(--color-positive, #10603b);
}
.input__message--success .input__message__text {
  color: var(--color-content-default, rgb(40, 40, 40));
}

.input__container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}
.input__container__label {
  display: flex;
  align-items: center;
}

.input__container__wrapper {
  display: flex;
  flex-wrap: nowrap;
}

.input__container__text {
  display: inline-block;
  margin: 0;
  border: 0;
  padding: 0;
  width: auto;
  vertical-align: middle;
  white-space: normal;
  line-height: inherit;
  background: none;
  /* Browsers have different default form fonts */
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  /* Make webkit render the search input like a normal text field */
  /* Turn off the recent search for webkit. It adds about 15px padding on the left */
  /* Fix IE7 display bug */
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
  font-size: 0.875rem;
  line-height: 22px;
  width: 100%;
  resize: none;
  cursor: inherit;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text::-webkit-file-upload-button {
  padding: 0;
  border: 0;
  background: none;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text[type=checkbox], .input__container__text[type=radio] {
  width: 13px;
  height: 13px;
}
.input__container__text[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
}
::-webkit-search-decoration {
  display: none;
}

.input__container__text[type=reset], .input__container__text[type=button], .input__container__text[type=submit] {
  overflow: visible;
}
.input__container__text__chips {
  width: auto;
  min-width: 216px;
  max-width: 216px;
}

:host {
  position: relative;
  max-width: 608px;
}

.datepicker__inputs {
  position: relative;
  width: 100%;
  display: grid;
}
.datepicker__inputs__open {
  z-index: 90000;
}
.datepicker__inputs__single {
  grid-template-columns: 1fr;
}
.datepicker__inputs__period {
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}
.datepicker__inputs bds-input {
  height: fit-content;
  width: 100%;
}
.datepicker__inputs bds-input::part(input-container) {
  position: relative;
}
.datepicker__inputs__icon {
  cursor: pointer;
  color: var(--color-content-ghost, rgb(140, 140, 140));
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding-right: 16px;
}
.datepicker__inputs__icon bds-icon:first-child {
  margin-right: 8px;
}
.datepicker__inputs__icon:hover bds-icon:first-child {
  color: var(--color-primary, rgb(30, 107, 241));
}
.datepicker__menu {
  position: absolute;
  pointer-events: none;
  background-color: var(--color-surface-0, rgb(255, 255, 255));
  box-shadow: 0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  border-radius: 8px;
  padding: 16px;
  opacity: 0;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  transition: opacity 0.5s;
}
.datepicker__menu__open {
  z-index: 100000;
  pointer-events: auto;
  opacity: 1;
}
.datepicker__menu__single__top-center {
  bottom: calc(100% + 8px);
  left: calc(50% - 146px);
}
.datepicker__menu__single__top-left {
  bottom: calc(100% + 8px);
  left: 0;
}
.datepicker__menu__single__top-right {
  bottom: calc(100% + 8px);
  right: 0;
}
.datepicker__menu__single__bottom-center {
  top: calc(100% + 8px);
  left: calc(50% - 146px);
}
.datepicker__menu__single__bottom-right {
  top: calc(100% + 8px);
  right: 0;
}
.datepicker__menu__single__bottom-left {
  top: calc(100% + 8px);
  left: 0;
}
.datepicker__menu__single__right-center {
  right: calc(100% + 8px);
}
.datepicker__menu__single__right-top {
  right: calc(100% + 8px);
  top: 0;
}
.datepicker__menu__single__right-bottom {
  right: calc(100% + 8px);
  bottom: 0;
}
.datepicker__menu__single__left-center {
  left: calc(100% + 8px);
}
.datepicker__menu__single__left-top {
  left: calc(100% + 8px);
  top: 0;
}
.datepicker__menu__single__left-bottom {
  left: calc(100% + 8px);
  bottom: 0;
}
.datepicker__menu__period__top-center {
  bottom: calc(100% + 8px);
  left: calc(50% - 240px);
}
.datepicker__menu__period__top-left {
  bottom: calc(100% + 8px);
  left: 0;
}
.datepicker__menu__period__top-right {
  bottom: calc(100% + 8px);
  right: 0;
}
.datepicker__menu__period__bottom-center {
  top: calc(100% + 8px);
  left: calc(50% - 240px);
}
.datepicker__menu__period__bottom-right {
  top: calc(100% + 8px);
  right: 0;
}
.datepicker__menu__period__bottom-left {
  top: calc(100% + 8px);
  left: 0;
}
.datepicker__menu__period__right-center {
  right: calc(100% + 8px);
}
.datepicker__menu__period__right-top {
  right: calc(100% + 8px);
  top: 0;
}
.datepicker__menu__period__right-bottom {
  right: calc(100% + 8px);
  bottom: 0;
}
.datepicker__menu__period__left-center {
  left: calc(100% + 8px);
}
.datepicker__menu__period__left-top {
  left: calc(100% + 8px);
  top: 0;
}
.datepicker__menu__period__left-bottom {
  left: calc(100% + 8px);
  bottom: 0;
}
.datepicker__menu__message {
  padding: 8px;
  border-radius: 8px;
  background-color: var(--color-warning, rgb(253, 233, 155));
  color: var(--color-content-din, rgb(0, 0, 0));
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.datepicker__menu__message bds-icon {
  margin-right: 4px;
}
.datepicker__menu__footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 8px;
  margin-top: 8px;
  border-top: 1px solid var(--color-border-2, rgba(0, 0, 0, 0.16));
}
.datepicker__menu__footer bds-button {
  margin-left: 8px;
}
.datepicker__calendar {
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.datepicker__calendar__selectDate {
  width: 100%;
  display: grid;
  grid-template-columns: 32px 104px auto 32px;
  grid-gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  justify-items: center;
}
.datepicker__calendar__selectDate__select {
  position: relative;
  width: 100%;
}
.datepicker__calendar__selectDate__select__input {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8px 4px 8px 12px;
  flex: 1;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  background: var(--color-surface-1, rgb(246, 246, 246));
  color: var(--color-content-default, rgb(40, 40, 40));
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.datepicker__calendar__selectDate__select__input:hover {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
}
.datepicker__calendar__selectDate__select__input.input--pressed {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
  box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
}
.datepicker__calendar__selectDate__select__input.input--pressed .input__icon .bds-icon {
  color: var(--color-primary, rgb(30, 107, 241));
}
.datepicker__calendar__selectDate__select__input .input__container__label {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.datepicker__calendar__selectDate__select__input .input__container__label--pressed bds-typo {
  color: var(--color-primary, rgb(30, 107, 241));
}
.datepicker__calendar__selectDate__select__input .input__container__text {
  caret-color: var(--color-primary, rgb(30, 107, 241));
  color: var(--color-content-default, rgb(40, 40, 40));
}
.datepicker__calendar__selectDate__select__input__disable {
  cursor: not-allowed;
  background: var(--color-surface-2, rgb(237, 237, 237));
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.datepicker__calendar__selectDate__select__input__disable:hover {
  border: 1px solid var(--color-content-disable, rgb(89, 89, 89));
  box-sizing: border-box;
  border-radius: 8px;
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.datepicker__calendar__selectDate__select__input__disable.input--pressed {
  border: 1px solid var(--color-content-disable, rgb(89, 89, 89));
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-surface-3, rgb(227, 227, 227));
  box-shadow: 0 0 0 2px var(--color-surface-3, rgb(227, 227, 227));
}
.datepicker__calendar__selectDate__select__input__disable.input--pressed .input__icon .bds-icon {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.datepicker__calendar__selectDate__select__input__disable .input__container__label {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.datepicker__calendar__selectDate__select__input__disable .input__container__label--pressed bds-typo {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.datepicker__calendar__selectDate__select__input__disable .input__container__text {
  caret-color: var(--color-content-disable, rgb(89, 89, 89));
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.datepicker__calendar__selectDate__select__input .icon-arrow {
  color: var(--color-content-ghost, rgb(140, 140, 140));
  display: flex;
}
.datepicker__calendar__selectDate__select__options {
  background: var(--color-surface-0, rgb(255, 255, 255));
  width: 100%;
  max-height: 250px;
  position: absolute;
  top: 99%;
  left: 0;
  border-radius: 8px;
  box-shadow: 0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  overflow-y: auto;
  z-index: 2;
  margin-top: 4px;
  transition: transform 0.25s, opacity 0.75s, visibility 0.75s;
  transform-origin: top left;
  transform: scaleY(0);
  opacity: 0;
}
.datepicker__calendar__selectDate__select__options::-webkit-scrollbar {
  width: 16px;
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
  border-radius: 10px;
}
.datepicker__calendar__selectDate__select__options::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 4px solid transparent;
  border-radius: 10px;
  background-clip: content-box;
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.datepicker__calendar__selectDate__select__options--open {
  visibility: visible;
  transform: scale(1);
  opacity: 1;
}
.datepicker__calendar__selectDate__icon {
  cursor: pointer;
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.datepicker__calendar__selectDate .arrow-left__disable {
  opacity: 0;
  pointer-events: none;
}
.datepicker__calendar__selectDate .arrow-right__disable {
  opacity: 0;
  pointer-events: none;
}
.datepicker__calendar__week {
  width: fit-content;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 8px;
}
.datepicker__calendar__week__day {
  width: 32px;
  height: 32px;
  text-align: center;
  color: var(--color-content-ghost, rgb(140, 140, 140));
  display: flex;
  justify-content: center;
  align-items: center;
}
.datepicker__calendar__car {
  height: 192px;
  width: 224px;
  overflow: hidden;
  position: relative;
}
.datepicker__calendar__car__slide {
  display: flex;
  position: absolute;
  left: -100%;
}
.datepicker__calendar__car__slide__box {
  width: fit-content;
  height: fit-content;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}
.datepicker__calendar__car__slide__box__day {
  width: 32px;
  height: 32px;
  position: relative;
}
.datepicker__calendar__car__slide__box__day__period:before {
  content: "";
  position: absolute;
  inset: 4px 0px;
  background-color: var(--color-primary, rgb(30, 107, 241));
  opacity: 0.25;
}
.datepicker__calendar__car__slide__box__day__start:before {
  inset: 4px 0;
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
}
.datepicker__calendar__car__slide__box__day__end:before {
  inset: 4px 0;
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
}
.datepicker__calendar__car__slide__box__day__typo {
  position: relative;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  color: var(--color-content-default, rgb(40, 40, 40));
  border: 1px solid transparent;
  cursor: pointer;
}
.datepicker__calendar__car__slide__box__day__typo:hover {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
  color: var(--color-primary, rgb(30, 107, 241));
  border-color: var(--color-primary, rgb(30, 107, 241));
}
.datepicker__calendar__car__slide__box__day__current {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
  color: var(--color-primary, rgb(30, 107, 241));
  border-color: var(--color-primary, rgb(30, 107, 241));
}
.datepicker__calendar__car__slide__box__day__selected {
  background-color: var(--color-primary, rgb(30, 107, 241));
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.datepicker__calendar__car__slide__box__day__selected:hover {
  background-color: var(--color-primary, rgb(30, 107, 241));
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.datepicker__calendar__car__slide__box__day__disable {
  pointer-events: none;
  background-color: transparent;
  color: var(--color-content-ghost, rgb(140, 140, 140));
}
.datepicker__calendar__car .animate__prev {
  animation-name: animationPrev;
  animation-duration: 0.33s;
  animation-timing-function: ease-in-out;
}
.datepicker__calendar__car .animate__next {
  animation-name: animationNext;
  animation-duration: 0.33s;
  animation-timing-function: ease-in-out;
}
.period .datepicker__calendar__selectDate {
  grid-template-columns: 32px 120px 80px auto 32px;
}
.period .datepicker__calendar__selectDate__futureMonth {
  padding: 0 8px;
  text-align: center;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.period .datepicker__calendar__week {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.period .datepicker__calendar__week__present, .period .datepicker__calendar__week__future {
  width: fit-content;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}
.period .datepicker__calendar__car {
  width: 464px;
}
.period .datepicker__calendar__car__slide {
  left: calc(-50% - 24px);
}
.period .datepicker__calendar__car__slide__box {
  margin-left: 16px;
}
.period .datepicker__calendar__car .animate__prev {
  animation-name: animationPeriodPrev;
}
.period .datepicker__calendar__car .animate__next {
  animation-name: animationPeriodNext;
}

.outzone {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 80000;
}

@keyframes animationPrev {
  0% {
    left: -100%;
  }
  100% {
    left: 0;
  }
}
@keyframes animationNext {
  0% {
    left: -100%;
  }
  100% {
    left: -200%;
  }
}
@keyframes animationPeriodPrev {
  0% {
    left: calc(-50% - 24px);
  }
  100% {
    left: -16px;
  }
}
@keyframes animationPeriodNext {
  0% {
    left: calc(-50% - 24px);
  }
  100% {
    left: calc(-100% - 24px);
  }
}