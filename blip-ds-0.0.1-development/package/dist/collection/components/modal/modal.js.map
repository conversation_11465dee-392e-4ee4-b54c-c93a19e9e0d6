{"version": 3, "file": "modal.js", "sourceRoot": "", "sources": ["../../../../src/components/modal/modal.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAgB,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAQ3G,MAAM,OAAO,QAAQ;IALrB;QAME;;WAEG;QAKI,SAAI,GAAa,KAAK,CAAC;QAE9B;;WAEG;QAKI,gBAAW,GAAa,IAAI,CAAC;QAEpC;;WAEG;QAKI,SAAI,GAAW,OAAO,CAAC;QAE9B;;WAEG;QACK,iBAAY,GAAa,IAAI,CAAC;QAEtC;;WAEG;QACK,eAAU,GAAa,IAAI,CAAC;QAEpC;;;WAGG;QACK,cAAS,GAAY,IAAI,CAAC;QAElC;;;WAGG;QACK,kBAAa,GAAY,IAAI,CAAC;QA0B9B,aAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;YAC3B,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC;gBACvE,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;QAEM,qBAAgB,GAAG,GAAS,EAAE;YACpC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC;QAEM,mBAAc,GAAG,GAAG,EAAE;YAC5B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;KAiCH;IAlEC;;OAEG;IAEH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACzB,CAAC;IAGS,aAAa;QACrB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAkBD,MAAM;QACJ,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,aAAa,EAAE,IAAI;gBACnB,qBAAqB,EAAE,IAAI,CAAC,IAAI;gBAChC,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;aACtC;YAED,4DAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,eAAa,IAAI,CAAC,SAAS,GAAQ;YACtG,4DAAK,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,UAAU,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;gBACvD,IAAI,CAAC,WAAW,IAAI,CACnB,iEACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAC9B,QAAQ,EAAE,IAAI,CAAC,aAAa,GAC5B,CACH;gBACA,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,8DAAa;gBACrC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CACxB,4DAAK,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;oBACtD,8DAAa,CACT,CACP,CACG,CACF,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, h, Method, Event, EventEmitter, Prop, Watch } from '@stencil/core';\n\nexport type sizes = 'fixed' | 'dynamic';\n@Component({\n  tag: 'bds-modal',\n  styleUrl: 'modal.scss',\n  shadow: true,\n})\nexport class BdsModal implements ComponentInterface {\n  /**\n   * Used to open/close the modal\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public closeButton?: boolean = true;\n\n  /**\n   * Used to change the modal heights.\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public size?: sizes = 'fixed';\n\n  /**\n   * If true, the modal will close clicking outside the component.\n   */\n  @Prop() outzoneClose?: boolean = true;\n\n  /**\n   * If true, the modal will close keydown Enter.\n   */\n  @Prop() enterClose?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtOutzone is the data-test to button close.\n   */\n  @Prop() dtOutzone?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n\n  /**\n   * Emitted when modal status has changed.\n   */\n  @Event() bdsModalChanged!: EventEmitter;\n\n  /**\n   * Can be used outside to open/close the modal\n   */\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Watch('open')\n  protected isOpenChanged(): void {\n    if (this.open) {\n      document.addEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'opened' });\n    } else {\n      document.removeEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'closed' });\n    }\n  }\n\n  private listener = (event) => {\n    if (this.enterClose && (event.key == 'Enter' || event.key == 'Escape')) {\n      this.toggle();\n    }\n  };\n\n  private handleMouseClick = (): void => {\n    this.open = false;\n  };\n\n  private onClickOutzone = () => {\n    if (this.outzoneClose) {\n      this.open = false;\n    }\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          modal__dialog: true,\n          'modal__dialog--open': this.open,\n          [`modal__dialog--${this.size}`]: true,\n        }}\n      >\n        <div class={{ outzone: true }} onClick={() => this.onClickOutzone()} data-test={this.dtOutzone}></div>\n        <div class={{ modal: true, [`modal--${this.size}`]: true }}>\n          {this.closeButton && (\n            <bds-icon\n              size=\"medium\"\n              class=\"close-button\"\n              name=\"close\"\n              tabindex=\"0\"\n              onClick={this.handleMouseClick}\n              dataTest={this.dtButtonClose}\n            />\n          )}\n          {this.size == 'fixed' && <slot></slot>}\n          {this.size !== 'fixed' && (\n            <div class={{ slot: true, [`slot--${this.size}`]: true }}>\n              <slot></slot>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"]}