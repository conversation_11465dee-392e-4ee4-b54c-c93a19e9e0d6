{"version": 3, "file": "modal-close-button.js", "sourceRoot": "", "sources": ["../../../../../src/components/modal/modal-close-button/modal-close-button.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,IAAI,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAOvE,MAAM,OAAO,mBAAmB;IALhC;QAME;;WAEG;QAKI,WAAM,GAAa,IAAI,CAAC;KAchC;IAZC,MAAM;QACJ,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,2BAA2B,EAAE,IAAI;gBACjC,mCAAmC,EAAE,IAAI,CAAC,MAAM;aACjD;YAED,iEAAU,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,GAAY,CAC5C,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-close-button',\n  styleUrl: 'modal-close-button.scss',\n  shadow: true,\n})\nexport class BdsModalCloseButton implements ComponentInterface {\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public active?: boolean = true;\n\n  render() {\n    return (\n      <div\n        class={{\n          'modal__close__button-icon': true,\n          'modal__close__button-icon--active': this.active,\n        }}\n      >\n        <bds-icon size=\"medium\" name=\"close\"></bds-icon>\n      </div>\n    );\n  }\n}\n"]}