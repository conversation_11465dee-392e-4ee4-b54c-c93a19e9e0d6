/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.modal__close__button-icon {
  opacity: 0;
  visibility: hidden;
  color: var(--color-content-default, rgb(40, 40, 40));
  display: flex;
  justify-content: flex-end;
  padding-bottom: 16px;
}
.modal__close__button-icon--active {
  opacity: 1;
  visibility: visible;
}