/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.modal__dialog {
  opacity: 0;
  visibility: hidden;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 80000;
  display: none;
}
.modal__dialog .outzone {
  position: absolute;
  inset: 0;
  background-color: var(--color-content-din, rgb(0, 0, 0));
  opacity: 0.7;
}
.modal__dialog--dynamic {
  overflow-y: auto;
  padding-top: 40px;
  padding-bottom: 40px;
  height: -webkit-fill-available;
}
.modal__dialog .modal {
  position: relative;
  margin: auto;
  width: 592px;
  height: 368px;
  border-radius: 8px;
  background: var(--color-surface-1, rgb(246, 246, 246));
  box-shadow: 0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  padding: 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.modal__dialog .modal--dynamic {
  height: auto;
  width: auto;
  max-width: 1000px;
}
.modal__dialog .modal .close-button {
  position: relative;
  color: var(--color-content-default, rgb(40, 40, 40));
  align-self: flex-end;
  margin-bottom: 16px;
  cursor: pointer;
}
.modal__dialog .modal .close-button::before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
}
.modal__dialog .modal .close-button:focus-visible {
  outline: none;
}
.modal__dialog .modal .close-button:focus-visible::before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}
.modal__dialog .modal .slot--dynamic {
  flex: 1 1 auto;
}
.modal__dialog--open {
  opacity: 1;
  visibility: visible;
  display: flex;
}