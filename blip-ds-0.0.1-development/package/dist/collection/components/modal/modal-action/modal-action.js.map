{"version": 3, "file": "modal-action.js", "sourceRoot": "", "sources": ["../../../../../src/components/modal/modal-action/modal-action.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,CAAC,EAAE,MAAM,eAAe,CAAC;AAOjE,MAAM,OAAO,cAAc;IACzB,MAAM;QACJ,OAAO,CACL,4DAAK,KAAK,EAAC,eAAe;YACxB,8DAAQ,CACJ,CACP,CAAC;IACJ,CAAC;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-action',\n  styleUrl: 'modal-action.scss',\n  shadow: true,\n})\nexport class BdsModalAction implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"modal__action\">\n        <slot />\n      </div>\n    );\n  }\n}\n"]}