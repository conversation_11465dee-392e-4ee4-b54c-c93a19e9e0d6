{"version": 3, "file": "grid-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/grid/grid-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export type direction = 'row' | 'row-reverse' | 'column' | 'column-reverse';\nexport type justifyContent =\n  | 'flex-start'\n  | 'center'\n  | 'flex-end'\n  | 'space-between'\n  | 'space-around'\n  | 'space-evenly'\n  | 'stretch';\nexport type alignItems = 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';\nexport type flexWrap = 'wrap' | 'wrap-reverse';\nexport type breakpoint = 'auto' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12';\nexport type gap = 'none' | 'half' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12';\nexport type padding =\n  | 'none'\n  | 'half'\n  | '1'\n  | '2'\n  | '3'\n  | '4'\n  | '5'\n  | '6'\n  | '7'\n  | '8'\n  | '9'\n  | '10'\n  | '11'\n  | '12'\n  | 'l-none'\n  | 'l-half'\n  | 'l-1'\n  | 'l-2'\n  | 'l-3'\n  | 'l-4'\n  | 'l-5'\n  | 'l-6'\n  | 'l-7'\n  | 'l-8'\n  | 'l-9'\n  | 'l-10'\n  | 'l-11'\n  | 'l-12'\n  | 'b-none'\n  | 'b-half'\n  | 'b-1'\n  | 'b-2'\n  | 'b-3'\n  | 'b-4'\n  | 'b-5'\n  | 'b-6'\n  | 'b-7'\n  | 'b-8'\n  | 'b-9'\n  | 'b-10'\n  | 'b-11'\n  | 'b-12'\n  | 'r-none'\n  | 'r-half'\n  | 'r-1'\n  | 'r-2'\n  | 'r-3'\n  | 'r-4'\n  | 'r-5'\n  | 'r-6'\n  | 'r-7'\n  | 'r-8'\n  | 'r-9'\n  | 'r-10'\n  | 'r-11'\n  | 'r-12'\n  | 't-none'\n  | 't-half'\n  | 't-1'\n  | 't-2'\n  | 't-3'\n  | 't-4'\n  | 't-5'\n  | 't-6'\n  | 't-7'\n  | 't-8'\n  | 't-9'\n  | 't-10'\n  | 't-11'\n  | 't-12'\n  | 'y-none'\n  | 'y-half'\n  | 'y-1'\n  | 'y-2'\n  | 'y-3'\n  | 'y-4'\n  | 'y-5'\n  | 'y-6'\n  | 'y-7'\n  | 'y-8'\n  | 'y-9'\n  | 'y-10'\n  | 'y-11'\n  | 'y-12'\n  | 'x-none'\n  | 'x-half'\n  | 'x-1'\n  | 'x-2'\n  | 'x-3'\n  | 'x-4'\n  | 'x-5'\n  | 'x-6'\n  | 'x-7'\n  | 'x-8'\n  | 'x-9'\n  | 'x-10'\n  | 'x-11'\n  | 'x-12';\nexport type margin =\n  | 'none'\n  | 'half'\n  | '1'\n  | '2'\n  | '3'\n  | '4'\n  | '5'\n  | '6'\n  | '7'\n  | '8'\n  | '9'\n  | '10'\n  | '11'\n  | '12'\n  | 'l-none'\n  | 'l-half'\n  | 'l-1'\n  | 'l-2'\n  | 'l-3'\n  | 'l-4'\n  | 'l-5'\n  | 'l-6'\n  | 'l-7'\n  | 'l-8'\n  | 'l-9'\n  | 'l-10'\n  | 'l-11'\n  | 'l-12'\n  | 'b-none'\n  | 'b-half'\n  | 'b-1'\n  | 'b-2'\n  | 'b-3'\n  | 'b-4'\n  | 'b-5'\n  | 'b-6'\n  | 'b-7'\n  | 'b-8'\n  | 'b-9'\n  | 'b-10'\n  | 'b-11'\n  | '12'\n  | 'r-none'\n  | 'r-half'\n  | 'r-1'\n  | 'r-2'\n  | 'r-3'\n  | 'r-4'\n  | 'r-5'\n  | 'r-6'\n  | 'r-7'\n  | 'r-8'\n  | 'r-9'\n  | 'r-10'\n  | 'r-11'\n  | 'r-12'\n  | 't-none'\n  | 't-half'\n  | 't-1'\n  | 't-2'\n  | 't-3'\n  | 't-4'\n  | 't-5'\n  | 't-6'\n  | 't-7'\n  | 't-8'\n  | 't-9'\n  | 't-10'\n  | 't-11'\n  | 't-12'\n  | 'y-none'\n  | 'y-half'\n  | 'y-1'\n  | 'y-2'\n  | 'y-3'\n  | 'y-4'\n  | 'y-5'\n  | 'y-6'\n  | 'y-7'\n  | 'y-8'\n  | 'y-9'\n  | 'y-10'\n  | 'y-11'\n  | 'y-12'\n  | 'x-none'\n  | 'x-half'\n  | 'x-1'\n  | 'x-2'\n  | 'x-3'\n  | 'x-4'\n  | 'x-5'\n  | 'x-6'\n  | 'x-7'\n  | 'x-8'\n  | 'x-9'\n  | 'x-10'\n  | 'x-11'\n  | 'x-12';\n"]}