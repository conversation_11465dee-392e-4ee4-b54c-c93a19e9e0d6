/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host(.color-brand) {
  background-color: var(--color-brand, rgb(0, 150, 250));
}

:host(.color-primary) {
  background-color: var(--color-primary, rgb(30, 107, 241));
}

:host(.color-secondary) {
  background-color: var(--color-secondary, rgb(41, 41, 41));
}

:host(.color-surface-0) {
  background-color: var(--color-surface-0, rgb(255, 255, 255));
}

:host(.color-surface-1) {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
}

:host(.color-surface-2) {
  background-color: var(--color-surface-2, rgb(237, 237, 237));
}

:host(.color-surface-3) {
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}

:host(.color-surface-4) {
  background-color: var(--color-surface-4, rgb(20, 20, 20));
}

:host(.color-surface-positive) {
  background-color: var(--color-surface-positive, rgb(1, 114, 62));
}

:host(.color-surface-negative) {
  background-color: var(--color-surface-negative, rgb(138, 0, 0));
}

:host(.color-surface-primary) {
  background-color: var(--color-surface-primary, rgb(30, 107, 241));
}

:host(.color-content-default) {
  background-color: var(--color-content-default, rgb(40, 40, 40));
}

:host(.color-content-disable) {
  background-color: var(--color-content-disable, rgb(89, 89, 89));
}

:host(.color-content-ghost) {
  background-color: var(--color-content-ghost, rgb(140, 140, 140));
}

:host(.color-content-bright) {
  background-color: var(--color-content-bright, rgb(255, 255, 255));
}

:host(.color-content-din) {
  background-color: var(--color-content-din, rgb(0, 0, 0));
}

:host(.color-border-1) {
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}

:host(.color-border-2) {
  background-color: var(--color-border-2, rgba(0, 0, 0, 0.16));
}

:host(.color-border-3) {
  background-color: var(--color-border-3, rgba(0, 0, 0, 0.06));
}

:host(.color-info) {
  background-color: var(--color-info, rgb(128, 227, 235));
}

:host(.color-system) {
  background-color: var(--color-system, rgb(178, 223, 253));
}

:host(.color-focus) {
  background-color: var(--color-focus, rgb(194, 38, 251));
}

:host(.color-success) {
  background-color: var(--color-success, rgb(132, 235, 188));
}

:host(.color-warning) {
  background-color: var(--color-warning, rgb(253, 233, 155));
}

:host(.color-error) {
  background-color: var(--color-error, rgb(250, 190, 190));
}

:host(.color-delete) {
  background-color: var(--color-delete, rgb(230, 15, 15));
}

:host(.color-shadow-0) {
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
}

:host(.color-shadow-1) {
  background-color: var(--color-shadow-1, rgba(0, 0, 0, 0.16));
}

:host(.color-hover) {
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
}

:host(.color-pressed) {
  background-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
}

:host(.color-positive) {
  background-color: var(--color-positive, #10603b);
}

:host(.color-negative) {
  background-color: var(--color-negative, #e60f0f);
}

:host {
  display: flex;
  box-sizing: border-box;
}

:host(.container) {
  width: 100%;
}

:host(.xxsoffset--auto) {
  margin-left: auto !important;
}

:host(.xxsoffset--1) {
  margin-left: 8.33% !important;
}

:host(.xxsoffset--2) {
  margin-left: 16.66% !important;
}

:host(.xxsoffset--3) {
  margin-left: 24.99% !important;
}

:host(.xxsoffset--4) {
  margin-left: 33.32% !important;
}

:host(.xxsoffset--5) {
  margin-left: 41.65% !important;
}

:host(.xxsoffset--6) {
  margin-left: 50% !important;
}

:host(.xxsoffset--7) {
  margin-left: 58.33% !important;
}

:host(.xxsoffset--8) {
  margin-left: 66.66% !important;
}

:host(.xxsoffset--9) {
  margin-left: 74.99% !important;
}

:host(.xxsoffset--10) {
  margin-left: 83.32% !important;
}

:host(.xxsoffset--11) {
  margin-left: 91.65% !important;
}

:host(.xxsoffset--12) {
  margin-left: 100% !important;
}

@media (max-width: 599px) {
  :host(.container) {
    width: 100%;
  }
  :host(.xxsoffset--auto) {
    margin-left: auto !important;
  }
  :host(.xxsoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.xxsoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.xxsoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.xxsoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.xxsoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.xxsoffset--6) {
    margin-left: 50% !important;
  }
  :host(.xxsoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.xxsoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.xxsoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.xxsoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.xxsoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.xxsoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 600px) {
  :host(.container) {
    width: 100%;
  }
  :host(.xxsoffset--auto) {
    margin-left: auto !important;
  }
  :host(.xsoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.xsoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.xsoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.xsoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.xsoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.xsoffset--6) {
    margin-left: 50% !important;
  }
  :host(.xsoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.xsoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.xsoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.xsoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.xsoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.xsoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 905px) {
  :host(.container) {
    max-width: 848px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  :host(.smoffset--auto) {
    margin-left: auto !important;
  }
  :host(.smoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.smoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.smoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.smoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.smoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.smoffset--6) {
    margin-left: 50% !important;
  }
  :host(.smoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.smoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.smoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.smoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.smoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.smoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 993px) {
  :host(.container) {
    max-width: 944px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  :host(.mdoffset--auto) {
    margin-left: auto !important;
  }
  :host(.mdoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.mdoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.mdoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.mdoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.mdoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.mdoffset--6) {
    margin-left: 50% !important;
  }
  :host(.mdoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.mdoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.mdoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.mdoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.mdoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.mdoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 1601px) {
  :host(.container) {
    max-width: 1328px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  :host(.lgoffset--auto) {
    margin-left: auto !important;
  }
  :host(.lgoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.lgoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.lgoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.lgoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.lgoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.lgoffset--6) {
    margin-left: 50% !important;
  }
  :host(.lgoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.lgoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.lgoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.lgoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.lgoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.lgoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 1921px) {
  :host(.container) {
    max-width: 1424px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  :host(.xgoffset--auto) {
    margin-left: auto !important;
  }
  :host(.xgoffset--1) {
    margin-left: 8.33% !important;
  }
  :host(.xgoffset--2) {
    margin-left: 16.66% !important;
  }
  :host(.xgoffset--3) {
    margin-left: 24.99% !important;
  }
  :host(.xgoffset--4) {
    margin-left: 33.32% !important;
  }
  :host(.xgoffset--5) {
    margin-left: 41.65% !important;
  }
  :host(.xgoffset--6) {
    margin-left: 50% !important;
  }
  :host(.xgoffset--7) {
    margin-left: 58.33% !important;
  }
  :host(.xgoffset--8) {
    margin-left: 66.66% !important;
  }
  :host(.xgoffset--9) {
    margin-left: 74.99% !important;
  }
  :host(.xgoffset--10) {
    margin-left: 83.32% !important;
  }
  :host(.xgoffset--11) {
    margin-left: 91.65% !important;
  }
  :host(.xgoffset--12) {
    margin-left: 100% !important;
  }
}
@media (min-width: 600px) {
  :host(.container-fluid) {
    max-width: 100%;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (min-width: 905px) {
  :host(.container-fluid) {
    max-width: 848px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (min-width: 993px) {
  :host(.container-fluid) {
    max-width: 944px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (min-width: 1280px) {
  :host(.container-fluid) {
    max-width: 1232px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (min-width: 1440px) {
  :host(.container-fluid) {
    max-width: 1328px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
@media (min-width: 1920px) {
  :host(.container-fluid) {
    max-width: 1424px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}
:host(.flex_wrap--wrap) {
  flex-wrap: wrap;
}

:host(.flex_wrap--wrap-reverse) {
  flex-wrap: wrap-reverse;
}

:host(.direction--row) {
  flex-direction: row;
}

:host(.direction--column) {
  flex-direction: column;
}

:host(.direction--row-reverse) {
  flex-direction: row-reverse;
}

:host(.direction--column-reverse) {
  flex-direction: column-reverse;
}

:host(.justify_content--center) {
  justify-content: center;
}

:host(.justify_content--flex-start) {
  justify-content: flex-start;
}

:host(.justify_content--flex-end) {
  justify-content: flex-end;
}

:host(.justify_content--space-between) {
  justify-content: space-between;
}

:host(.justify_content--space-around) {
  justify-content: space-around;
}

:host(.justify_content--space-evenly) {
  justify-content: space-evenly;
}

:host(.justify_content--stretch) {
  justify-content: stretch;
}

:host(.align_items--flex-start) {
  align-items: flex-start;
}

:host(.align_items--flex-end) {
  align-items: flex-end;
}

:host(.align_items--center) {
  align-items: center;
}

:host(.align_items--stretch) {
  align-items: stretch;
}

:host(.align_items--baseline) {
  align-items: baseline;
}

:host(.gap--none) {
  gap: 0;
}

:host(.gap--half) {
  gap: 4px;
}

:host(.gap--1) {
  gap: 8px;
}

:host(.gap--2) {
  gap: 16px;
}

:host(.gap--3) {
  gap: 24px;
}

:host(.gap--4) {
  gap: 32px;
}

:host(.gap--5) {
  gap: 40px;
}

:host(.gap--6) {
  gap: 48px;
}

:host(.gap--7) {
  gap: 56px;
}

:host(.gap--8) {
  gap: 64px;
}

:host(.gap--9) {
  gap: 72px;
}

:host(.gap--10) {
  gap: 80px;
}

:host(.gap--11) {
  gap: 88px;
}

:host(.gap--12) {
  gap: 96px;
}

:host(.xxs--auto) {
  flex: 1 0 auto;
  width: auto;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--1) {
  flex: 0 0 auto;
  width: 8.33%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--2) {
  flex: 0 0 auto;
  width: 16.66%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--3) {
  flex: 0 0 auto;
  width: 24.99%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--4) {
  flex: 0 0 auto;
  width: 33.32%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--5) {
  flex: 0 0 auto;
  width: 41.65%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--6) {
  flex: 0 0 auto;
  width: 50%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--7) {
  flex: 0 0 auto;
  width: 58.33%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--8) {
  flex: 0 0 auto;
  width: 66.66%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--9) {
  flex: 0 0 auto;
  width: 74.99%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--10) {
  flex: 0 0 auto;
  width: 83.32%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--11) {
  flex: 0 0 auto;
  width: 91.65%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.xxs--12) {
  flex: 0 0 auto;
  width: 100%;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

@media (min-width: 600px) {
  :host(.xs--auto) {
    flex: 1 0 auto;
    width: auto;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--1) {
    flex: 0 0 auto;
    width: 8.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--2) {
    flex: 0 0 auto;
    width: 16.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--3) {
    flex: 0 0 auto;
    width: 24.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--4) {
    flex: 0 0 auto;
    width: 33.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--5) {
    flex: 0 0 auto;
    width: 41.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--6) {
    flex: 0 0 auto;
    width: 50%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--7) {
    flex: 0 0 auto;
    width: 58.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--8) {
    flex: 0 0 auto;
    width: 66.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--9) {
    flex: 0 0 auto;
    width: 74.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--10) {
    flex: 0 0 auto;
    width: 83.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--11) {
    flex: 0 0 auto;
    width: 91.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xs--12) {
    flex: 0 0 auto;
    width: 100%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
@media (min-width: 905px) {
  :host(.sm--auto) {
    flex: 1 0 auto;
    width: auto;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--1) {
    flex: 0 0 auto;
    width: 8.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--2) {
    flex: 0 0 auto;
    width: 16.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--3) {
    flex: 0 0 auto;
    width: 24.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--4) {
    flex: 0 0 auto;
    width: 33.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--5) {
    flex: 0 0 auto;
    width: 41.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--6) {
    flex: 0 0 auto;
    width: 50%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--7) {
    flex: 0 0 auto;
    width: 58.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--8) {
    flex: 0 0 auto;
    width: 66.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--9) {
    flex: 0 0 auto;
    width: 74.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--10) {
    flex: 0 0 auto;
    width: 83.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--11) {
    flex: 0 0 auto;
    width: 91.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.sm--12) {
    flex: 0 0 auto;
    width: 100%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
@media (min-width: 993px) {
  :host(.md--auto) {
    flex: 1 0 auto;
    width: auto;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--1) {
    flex: 0 0 auto;
    width: 8.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--2) {
    flex: 0 0 auto;
    width: 16.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--3) {
    flex: 0 0 auto;
    width: 24.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--4) {
    flex: 0 0 auto;
    width: 33.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--5) {
    flex: 0 0 auto;
    width: 41.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--6) {
    flex: 0 0 auto;
    width: 50%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--7) {
    flex: 0 0 auto;
    width: 58.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--8) {
    flex: 0 0 auto;
    width: 66.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--9) {
    flex: 0 0 auto;
    width: 74.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--10) {
    flex: 0 0 auto;
    width: 83.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--11) {
    flex: 0 0 auto;
    width: 91.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.md--12) {
    flex: 0 0 auto;
    width: 100%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
@media (min-width: 1601px) {
  :host(.lg--auto) {
    flex: 1 0 auto;
    width: auto;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--1) {
    flex: 0 0 auto;
    width: 8.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--2) {
    flex: 0 0 auto;
    width: 16.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--3) {
    flex: 0 0 auto;
    width: 24.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--4) {
    flex: 0 0 auto;
    width: 33.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--5) {
    flex: 0 0 auto;
    width: 41.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--6) {
    flex: 0 0 auto;
    width: 50%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--7) {
    flex: 0 0 auto;
    width: 58.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--8) {
    flex: 0 0 auto;
    width: 66.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--9) {
    flex: 0 0 auto;
    width: 74.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--10) {
    flex: 0 0 auto;
    width: 83.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--11) {
    flex: 0 0 auto;
    width: 91.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.lg--12) {
    flex: 0 0 auto;
    width: 100%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
@media (min-width: 1921px) {
  :host(.xg--auto) {
    flex: 1 0 auto;
    width: auto;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--1) {
    flex: 0 0 auto;
    width: 8.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--2) {
    flex: 0 0 auto;
    width: 16.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--3) {
    flex: 0 0 auto;
    width: 24.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--4) {
    flex: 0 0 auto;
    width: 33.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--5) {
    flex: 0 0 auto;
    width: 41.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--6) {
    flex: 0 0 auto;
    width: 50%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--7) {
    flex: 0 0 auto;
    width: 58.33%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--8) {
    flex: 0 0 auto;
    width: 66.66%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--9) {
    flex: 0 0 auto;
    width: 74.99%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--10) {
    flex: 0 0 auto;
    width: 83.32%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--11) {
    flex: 0 0 auto;
    width: 91.65%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  :host(.xg--12) {
    flex: 0 0 auto;
    width: 100%;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
@media (min-width: 1280px) {
  :host(.lg--auto .container-fluid) {
    flex: 1 0 auto;
    width: auto;
  }
  :host(.lg--1.container-fluid) {
    flex: 0 0 auto;
    width: 8.33%;
  }
  :host(.lg--2.container-fluid) {
    flex: 0 0 auto;
    width: 16.66%;
  }
  :host(.lg--3.container-fluid) {
    flex: 0 0 auto;
    width: 24.99%;
  }
  :host(.lg--4.container-fluid) {
    flex: 0 0 auto;
    width: 33.32%;
  }
  :host(.lg--5.container-fluid) {
    flex: 0 0 auto;
    width: 41.65%;
  }
  :host(.lg--6.container-fluid) {
    flex: 0 0 auto;
    width: 50%;
  }
  :host(.lg--7.container-fluid) {
    flex: 0 0 auto;
    width: 58.33%;
  }
  :host(.lg--8.container-fluid) {
    flex: 0 0 auto;
    width: 66.66%;
  }
  :host(.lg--9.container-fluid) {
    flex: 0 0 auto;
    width: 74.99%;
  }
  :host(.lg--10.container-fluid) {
    flex: 0 0 auto;
    width: 83.32%;
  }
  :host(.lg--11.container-fluid) {
    flex: 0 0 auto;
    width: 91.65%;
  }
  :host(.lg--12.container-fluid) {
    flex: 0 0 auto;
    width: 100%;
  }
}
@media (min-width: 1440px) {
  :host(.xg--auto .container-fluid) {
    flex: 1 0 auto;
    width: auto;
  }
  :host(.xg--1.container-fluid) {
    flex: 0 0 auto;
    width: 8.33%;
  }
  :host(.xg--2.container-fluid) {
    flex: 0 0 auto;
    width: 16.66%;
  }
  :host(.xg--3.container-fluid) {
    flex: 0 0 auto;
    width: 24.99%;
  }
  :host(.xg--4.container-fluid) {
    flex: 0 0 auto;
    width: 33.32%;
  }
  :host(.xg--5.container-fluid) {
    flex: 0 0 auto;
    width: 41.65%;
  }
  :host(.xg--6.container-fluid) {
    flex: 0 0 auto;
    width: 50%;
  }
  :host(.xg--7.container-fluid) {
    flex: 0 0 auto;
    width: 58.33%;
  }
  :host(.xg--8.container-fluid) {
    flex: 0 0 auto;
    width: 66.66%;
  }
  :host(.xg--9.container-fluid) {
    flex: 0 0 auto;
    width: 74.99%;
  }
  :host(.xg--10.container-fluid) {
    flex: 0 0 auto;
    width: 83.32%;
  }
  :host(.xg--11.container-fluid) {
    flex: 0 0 auto;
    width: 91.65%;
  }
  :host(.xg--12.container-fluid) {
    flex: 0 0 auto;
    width: 100%;
  }
}
@media (min-width: 1920px) {
  :host(.xxg--auto .container-fluid) {
    flex: 1 0 auto;
    width: auto;
  }
  :host(.xxg--1.container-fluid) {
    flex: 0 0 auto;
    width: 8.33%;
  }
  :host(.xxg--2.container-fluid) {
    flex: 0 0 auto;
    width: 16.66%;
  }
  :host(.xxg--3.container-fluid) {
    flex: 0 0 auto;
    width: 24.99%;
  }
  :host(.xxg--4.container-fluid) {
    flex: 0 0 auto;
    width: 33.32%;
  }
  :host(.xxg--5.container-fluid) {
    flex: 0 0 auto;
    width: 41.65%;
  }
  :host(.xxg--6.container-fluid) {
    flex: 0 0 auto;
    width: 50%;
  }
  :host(.xxg--7.container-fluid) {
    flex: 0 0 auto;
    width: 58.33%;
  }
  :host(.xxg--8.container-fluid) {
    flex: 0 0 auto;
    width: 66.66%;
  }
  :host(.xxg--9.container-fluid) {
    flex: 0 0 auto;
    width: 74.99%;
  }
  :host(.xxg--10.container-fluid) {
    flex: 0 0 auto;
    width: 83.32%;
  }
  :host(.xxg--11.container-fluid) {
    flex: 0 0 auto;
    width: 91.65%;
  }
  :host(.xxg--12.container-fluid) {
    flex: 0 0 auto;
    width: 100%;
  }
}
:host(.padding--none) {
  padding: 0 !important;
}

:host(.padding--l-none) {
  padding-left: 0 !important;
}

:host(.padding--r-none) {
  padding-right: 0 !important;
}

:host(.padding--t-none) {
  padding-top: 0 !important;
}

:host(.padding--b-none) {
  padding-bottom: 0 !important;
}

:host(.padding--x-none) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

:host(.padding--y-none) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

:host(.padding--half) {
  padding: 4px !important;
}

:host(.padding--l-half) {
  padding-left: 4px !important;
}

:host(.padding--r-half) {
  padding-right: 4px !important;
}

:host(.padding--t-half) {
  padding-top: 4px !important;
}

:host(.padding--b-half) {
  padding-bottom: 4px !important;
}

:host(.padding--x-half) {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

:host(.padding--y-half) {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

:host(.padding--1) {
  padding: 8px !important;
}

:host(.padding--l-1) {
  padding-left: 8px !important;
}

:host(.padding--r-1) {
  padding-right: 8px !important;
}

:host(.padding--t-1) {
  padding-top: 8px !important;
}

:host(.padding--b-1) {
  padding-bottom: 8px !important;
}

:host(.padding--x-1) {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

:host(.padding--y-1) {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

:host(.padding--2) {
  padding: 16px !important;
}

:host(.padding--l-2) {
  padding-left: 16px !important;
}

:host(.padding--r-2) {
  padding-right: 16px !important;
}

:host(.padding--t-2) {
  padding-top: 16px !important;
}

:host(.padding--b-2) {
  padding-bottom: 16px !important;
}

:host(.padding--x-2) {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

:host(.padding--y-2) {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

:host(.padding--3) {
  padding: 24px !important;
}

:host(.padding--l-3) {
  padding-left: 24px !important;
}

:host(.padding--r-3) {
  padding-right: 24px !important;
}

:host(.padding--t-3) {
  padding-top: 24px !important;
}

:host(.padding--b-3) {
  padding-bottom: 24px !important;
}

:host(.padding--x-3) {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

:host(.padding--y-3) {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}

:host(.padding--4) {
  padding: 32px !important;
}

:host(.padding--l-4) {
  padding-left: 32px !important;
}

:host(.padding--r-4) {
  padding-right: 32px !important;
}

:host(.padding--t-4) {
  padding-top: 32px !important;
}

:host(.padding--b-4) {
  padding-bottom: 32px !important;
}

:host(.padding--x-4) {
  padding-left: 32px !important;
  padding-right: 32px !important;
}

:host(.padding--y-4) {
  padding-top: 32px !important;
  padding-bottom: 32px !important;
}

:host(.padding--5) {
  padding: 40px !important;
}

:host(.padding--l-5) {
  padding-left: 40px !important;
}

:host(.padding--r-5) {
  padding-right: 40px !important;
}

:host(.padding--t-5) {
  padding-top: 40px !important;
}

:host(.padding--b-5) {
  padding-bottom: 40px !important;
}

:host(.padding--x-5) {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

:host(.padding--y-5) {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

:host(.padding--6) {
  padding: 48px !important;
}

:host(.padding--l-6) {
  padding-left: 48px !important;
}

:host(.padding--r-6) {
  padding-right: 48px !important;
}

:host(.padding--t-6) {
  padding-top: 48px !important;
}

:host(.padding--b-6) {
  padding-bottom: 48px !important;
}

:host(.padding--x-6) {
  padding-left: 48px !important;
  padding-right: 48px !important;
}

:host(.padding--y-6) {
  padding-top: 48px !important;
  padding-bottom: 48px !important;
}

:host(.padding--7) {
  padding: 56px !important;
}

:host(.padding--l-7) {
  padding-left: 56px !important;
}

:host(.padding--r-7) {
  padding-right: 56px !important;
}

:host(.padding--t-7) {
  padding-top: 56px !important;
}

:host(.padding--b-7) {
  padding-bottom: 56px !important;
}

:host(.padding--x-7) {
  padding-left: 56px !important;
  padding-right: 56px !important;
}

:host(.padding--y-7) {
  padding-top: 56px !important;
  padding-bottom: 56px !important;
}

:host(.padding--8) {
  padding: 64px !important;
}

:host(.padding--l-8) {
  padding-left: 64px !important;
}

:host(.padding--r-8) {
  padding-right: 64px !important;
}

:host(.padding--t-8) {
  padding-top: 64px !important;
}

:host(.padding--b-8) {
  padding-bottom: 64px !important;
}

:host(.padding--x-8) {
  padding-left: 64px !important;
  padding-right: 64px !important;
}

:host(.padding--y-8) {
  padding-top: 64px !important;
  padding-bottom: 64px !important;
}

:host(.padding--9) {
  padding: 72px !important;
}

:host(.padding--l-9) {
  padding-left: 72px !important;
}

:host(.padding--r-9) {
  padding-right: 72px !important;
}

:host(.padding--t-9) {
  padding-top: 72px !important;
}

:host(.padding--b-9) {
  padding-bottom: 72px !important;
}

:host(.padding--x-9) {
  padding-left: 72px !important;
  padding-right: 72px !important;
}

:host(.padding--y-9) {
  padding-top: 72px !important;
  padding-bottom: 72px !important;
}

:host(.padding--10) {
  padding: 80px !important;
}

:host(.padding--l-10) {
  padding-left: 80px !important;
}

:host(.padding--r-10) {
  padding-right: 80px !important;
}

:host(.padding--t-10) {
  padding-top: 80px !important;
}

:host(.padding--b-10) {
  padding-bottom: 80px !important;
}

:host(.padding--x-10) {
  padding-left: 80px !important;
  padding-right: 80px !important;
}

:host(.padding--y-10) {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

:host(.padding--11) {
  padding: 88px !important;
}

:host(.padding--l-11) {
  padding-left: 88px !important;
}

:host(.padding--r-11) {
  padding-right: 88px !important;
}

:host(.padding--t-11) {
  padding-top: 88px !important;
}

:host(.padding--b-11) {
  padding-bottom: 88px !important;
}

:host(.padding--x-11) {
  padding-left: 88px !important;
  padding-right: 88px !important;
}

:host(.padding--y-11) {
  padding-top: 88px !important;
  padding-bottom: 88px !important;
}

:host(.padding--12) {
  padding: 96px !important;
}

:host(.padding--l-12) {
  padding-left: 96px !important;
}

:host(.padding--r-12) {
  padding-right: 96px !important;
}

:host(.padding--t-12) {
  padding-top: 96px !important;
}

:host(.padding--b-12) {
  padding-bottom: 96px !important;
}

:host(.padding--x-12) {
  padding-left: 96px !important;
  padding-right: 96px !important;
}

:host(.padding--y-12) {
  padding-top: 96px !important;
  padding-bottom: 96px !important;
}

:host(.margin--auto) {
  margin: auto !important;
}

:host(.margin--l-auto) {
  margin-left: auto !important;
}

:host(.margin--r-auto) {
  margin-right: auto !important;
}

:host(.margin--t-auto) {
  margin-top: auto !important;
}

:host(.margin--b-auto) {
  margin-bottom: auto !important;
}

:host(.margin--x-auto) {
  margin-left: auto !important;
  margin-right: auto !important;
}

:host(.margin--y-auto) {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

:host(.margin--none) {
  margin: 0 !important;
}

:host(.margin--l-none) {
  margin-left: 0 !important;
}

:host(.margin--r-none) {
  margin-right: 0 !important;
}

:host(.margin--t-none) {
  margin-top: 0 !important;
}

:host(.margin--b-none) {
  margin-bottom: 0 !important;
}

:host(.margin--x-none) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:host(.margin--y-none) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

:host(.margin--half) {
  margin: 4px !important;
}

:host(.margin--l-half) {
  margin-left: 4px !important;
}

:host(.margin--r-half) {
  margin-right: 4px !important;
}

:host(.margin--t-half) {
  margin-top: 4px !important;
}

:host(.margin--b-half) {
  margin-bottom: 4px !important;
}

:host(.margin--x-half) {
  margin-left: 4px !important;
  margin-right: 4px !important;
}

:host(.margin--y-half) {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

:host(.margin--1) {
  margin: 8px !important;
}

:host(.margin--l-1) {
  margin-left: 8px !important;
}

:host(.margin--r-1) {
  margin-right: 8px !important;
}

:host(.margin--t-1) {
  margin-top: 8px !important;
}

:host(.margin--b-1) {
  margin-bottom: 8px !important;
}

:host(.margin--x-1) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}

:host(.margin--y-1) {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

:host(.margin--2) {
  margin: 16px !important;
}

:host(.margin--l-2) {
  margin-left: 16px !important;
}

:host(.margin--r-2) {
  margin-right: 16px !important;
}

:host(.margin--t-2) {
  margin-top: 16px !important;
}

:host(.margin--b-2) {
  margin-bottom: 16px !important;
}

:host(.margin--x-2) {
  margin-left: 16px !important;
  margin-right: 16px !important;
}

:host(.margin--y-2) {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}

:host(.margin--3) {
  margin: 24px !important;
}

:host(.margin--l-3) {
  margin-left: 24px !important;
}

:host(.margin--r-3) {
  margin-right: 24px !important;
}

:host(.margin--t-3) {
  margin-top: 24px !important;
}

:host(.margin--b-3) {
  margin-bottom: 24px !important;
}

:host(.margin--x-3) {
  margin-left: 24px !important;
  margin-right: 24px !important;
}

:host(.margin--y-3) {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}

:host(.margin--4) {
  margin: 32px !important;
}

:host(.margin--l-4) {
  margin-left: 32px !important;
}

:host(.margin--r-4) {
  margin-right: 32px !important;
}

:host(.margin--t-4) {
  margin-top: 32px !important;
}

:host(.margin--b-4) {
  margin-bottom: 32px !important;
}

:host(.margin--x-4) {
  margin-left: 32px !important;
  margin-right: 32px !important;
}

:host(.margin--y-4) {
  margin-top: 32px !important;
  margin-bottom: 32px !important;
}

:host(.margin--5) {
  margin: 40px !important;
}

:host(.margin--l-5) {
  margin-left: 40px !important;
}

:host(.margin--r-5) {
  margin-right: 40px !important;
}

:host(.margin--t-5) {
  margin-top: 40px !important;
}

:host(.margin--b-5) {
  margin-bottom: 40px !important;
}

:host(.margin--x-5) {
  margin-left: 40px !important;
  margin-right: 40px !important;
}

:host(.margin--y-5) {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

:host(.margin--6) {
  margin: 48px !important;
}

:host(.margin--l-6) {
  margin-left: 48px !important;
}

:host(.margin--r-6) {
  margin-right: 48px !important;
}

:host(.margin--t-6) {
  margin-top: 48px !important;
}

:host(.margin--b-6) {
  margin-bottom: 48px !important;
}

:host(.margin--x-6) {
  margin-left: 48px !important;
  margin-right: 48px !important;
}

:host(.margin--y-6) {
  margin-top: 48px !important;
  margin-bottom: 48px !important;
}

:host(.margin--7) {
  margin: 56px !important;
}

:host(.margin--l-7) {
  margin-left: 56px !important;
}

:host(.margin--r-7) {
  margin-right: 56px !important;
}

:host(.margin--t-7) {
  margin-top: 56px !important;
}

:host(.margin--b-7) {
  margin-bottom: 56px !important;
}

:host(.margin--x-7) {
  margin-left: 56px !important;
  margin-right: 56px !important;
}

:host(.margin--y-7) {
  margin-top: 56px !important;
  margin-bottom: 56px !important;
}

:host(.margin--8) {
  margin: 64px !important;
}

:host(.margin--l-8) {
  margin-left: 64px !important;
}

:host(.margin--r-8) {
  margin-right: 64px !important;
}

:host(.margin--t-8) {
  margin-top: 64px !important;
}

:host(.margin--b-8) {
  margin-bottom: 64px !important;
}

:host(.margin--x-8) {
  margin-left: 64px !important;
  margin-right: 64px !important;
}

:host(.margin--y-8) {
  margin-top: 64px !important;
  margin-bottom: 64px !important;
}

:host(.margin--9) {
  margin: 72px !important;
}

:host(.margin--l-9) {
  margin-left: 72px !important;
}

:host(.margin--r-9) {
  margin-right: 72px !important;
}

:host(.margin--t-9) {
  margin-top: 72px !important;
}

:host(.margin--b-9) {
  margin-bottom: 72px !important;
}

:host(.margin--x-9) {
  margin-left: 72px !important;
  margin-right: 72px !important;
}

:host(.margin--y-9) {
  margin-top: 72px !important;
  margin-bottom: 72px !important;
}

:host(.margin--10) {
  margin: 80px !important;
}

:host(.margin--l-10) {
  margin-left: 80px !important;
}

:host(.margin--r-10) {
  margin-right: 80px !important;
}

:host(.margin--t-10) {
  margin-top: 80px !important;
}

:host(.margin--b-10) {
  margin-bottom: 80px !important;
}

:host(.margin--x-10) {
  margin-left: 80px !important;
  margin-right: 80px !important;
}

:host(.margin--y-10) {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

:host(.margin--11) {
  margin: 88px !important;
}

:host(.margin--l-11) {
  margin-left: 88px !important;
}

:host(.margin--r-11) {
  margin-right: 88px !important;
}

:host(.margin--t-11) {
  margin-top: 88px !important;
}

:host(.margin--b-11) {
  margin-bottom: 88px !important;
}

:host(.margin--x-11) {
  margin-left: 88px !important;
  margin-right: 88px !important;
}

:host(.margin--y-11) {
  margin-top: 88px !important;
  margin-bottom: 88px !important;
}

:host(.margin--12) {
  margin: 96px !important;
}

:host(.margin--l-12) {
  margin-left: 96px !important;
}

:host(.margin--r-12) {
  margin-right: 96px !important;
}

:host(.margin--t-12) {
  margin-top: 96px !important;
}

:host(.margin--b-12) {
  margin-bottom: 96px !important;
}

:host(.margin--x-12) {
  margin-left: 96px !important;
  margin-right: 96px !important;
}

:host(.margin--y-12) {
  margin-top: 96px !important;
  margin-bottom: 96px !important;
}