{"version": 3, "file": "color-grid-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/grid/color-grid-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export type Color =\n  | '$color-brand'\n  | '$color-primary'\n  | '$color-secondary'\n  | '$color-surface-0'\n  | '$color-surface-1'\n  | '$color-surface-2'\n  | '$color-surface-3'\n  | '$color-surface-4'\n  | '$color-surface-positive'\n  | '$color-surface-negative'\n  | '$color-surface-primary'\n  | '$color-content-default'\n  | '$color-content-disable'\n  | '$color-content-ghost'\n  | '$color-content-bright'\n  | '$color-content-din'\n  | '$color-border-1'\n  | '$color-border-2'\n  | '$color-border-3'\n  | '$color-info'\n  | '$color-system'\n  | '$color-focus'\n  | '$color-success'\n  | '$color-warning'\n  | '$color-error'\n  | '$color-delete'\n  | '$color-shadow-0'\n  | '$color-shadow-1'\n  | '$color-hover'\n  | '$color-pressed'\n  | '$color-positive'\n  | '$color-negative';\n"]}