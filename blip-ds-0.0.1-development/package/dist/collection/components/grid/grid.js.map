{"version": 3, "file": "grid.js", "sourceRoot": "", "sources": ["../../../../src/components/grid/grid.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAQzD,MAAM,OAAO,IAAI;IAwBf,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI;gBACV,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI;gBACtC,CAAC,oBAAoB,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,IAAI;gBACjD,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACvD,CAAC,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBAClE,CAAC,cAAc,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACrC,CAAC,gBAAgB,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI;gBACzC,CAAC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI;gBAC1B,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACxB,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACxB,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACxB,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACxB,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI;gBACxB,CAAC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI;gBAC1B,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI;gBACtC,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACpC,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACpC,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACpC,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACpC,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;gBACpC,CAAC,YAAY,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI;gBAClC,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI;gBAChC,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI;aAC3B,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAG;YAE/B,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Prop } from '@stencil/core';\nimport { direction, justifyContent, flexWrap, alignItems, breakpoint, gap, padding, margin } from './grid-interface';\nimport { Color } from './color-grid-interface';\n@Component({\n  tag: 'bds-grid',\n  styleUrl: 'grid.scss',\n  shadow: true,\n})\nexport class Grid {\n  @Prop() height?: string;\n  @Prop() direction?: direction;\n  @Prop() justifyContent?: justifyContent;\n  @Prop() flexWrap?: flexWrap;\n  @Prop() alignItems?: alignItems;\n  @Prop() container?: boolean;\n  @Prop() containerFluid?: boolean;\n  @Prop() xxs?: breakpoint;\n  @Prop() xs?: breakpoint;\n  @Prop() sm?: breakpoint;\n  @Prop() md?: breakpoint;\n  @Prop() lg?: breakpoint;\n  @Prop() xg?: breakpoint;\n  @Prop() xxsOffset?: breakpoint;\n  @Prop() xsOffset?: breakpoint;\n  @Prop() smOffset?: breakpoint;\n  @Prop() mdOffset?: breakpoint;\n  @Prop() lgOffset?: breakpoint;\n  @Prop() xgOffset?: breakpoint;\n  @Prop() gap?: gap;\n  @Prop() padding?: padding;\n  @Prop() margin?: margin;\n  @Prop() bgColor?: Color;\n  render() {\n    return (\n      <Host\n        class={{\n          host: true,\n          [`direction--${this.direction}`]: true,\n          [`justify_content--${this.justifyContent}`]: true,\n          [`${this.container === true ? 'container' : ''}`]: true,\n          [`${this.containerFluid === true ? 'container-fluid' : ''}`]: true,\n          [`flex_wrap--${this.flexWrap}`]: true,\n          [`align_items--${this.alignItems}`]: true,\n          [`xxs--${this.xxs}`]: true,\n          [`xs--${this.xs}`]: true,\n          [`sm--${this.sm}`]: true,\n          [`md--${this.md}`]: true,\n          [`lg--${this.lg}`]: true,\n          [`xg--${this.xg}`]: true,\n          [`gap--${this.gap}`]: true,\n          [`xxsoffset--${this.xxsOffset}`]: true,\n          [`xsoffset--${this.xsOffset}`]: true,\n          [`smoffset--${this.smOffset}`]: true,\n          [`mdoffset--${this.mdOffset}`]: true,\n          [`lgoffset--${this.lgOffset}`]: true,\n          [`xgoffset--${this.xgOffset}`]: true,\n          [`padding--${this.padding}`]: true,\n          [`margin--${this.margin}`]: true,\n          [this.bgColor || '']: true,\n        }}\n        style={{ height: this.height  }}\n      >\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}