{"version": 3, "file": "carousel-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/carousel/carousel-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface Itens {\n  id: number;\n  label: string;\n  offsetHeight?: number;\n  offsetLeft?: number;\n  isWhole?: boolean;\n}\n\nexport type arrows = 'outside' | 'inside' | 'none';\n\nexport type bullets = 'outside' | 'inside' | 'none';\n\nexport type bulletsPositions = 'left' | 'center' | 'right';\n\nexport type gap = 'none' | 'half' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12';\n"]}