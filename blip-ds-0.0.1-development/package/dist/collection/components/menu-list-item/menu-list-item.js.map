{"version": 3, "file": "menu-list-item.js", "sourceRoot": "", "sources": ["../../../../src/components/menu-list-item/menu-list-item.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAOzD,MAAM,OAAO,YAAY;IAQvB,MAAM;QACJ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,cAAc,CAAC;QAE3C,OAAO,CACL,EAAC,IAAI,qDAAC,IAAI,EAAC,QAAQ;YACjB,4DAAK,KAAK,EAAC,gBAAgB;gBACzB,iEAAU,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAa;gBACpD,iEAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO;oBACpD,8DAAQ,CACC,CACP,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-menu-list-item',\n  styleUrl: 'menu-list-item.scss',\n  shadow: true,\n})\nexport class MenuListItem {\n  @Prop() color: string;\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon!: string;\n\n  render(): HTMLElement {\n    const color = this.color || 'currentColor';\n\n    return (\n      <Host role=\"button\">\n        <div class=\"menu-list-item\">\n          <bds-icon color={color} name={this.icon}></bds-icon>\n          <bds-typo class=\"menu-list-item__text\" variant=\"fs-10\">\n            <slot />\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}