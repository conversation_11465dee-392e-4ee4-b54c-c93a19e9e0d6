/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  flex: 1;
}

.menu-list-item {
  background-color: #f6f6f6;
  color: currentColor;
  cursor: pointer;
  width: 76px;
  height: 56px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.menu-list-item:hover, .menu-list-item:hover > .menu-list-item__text {
  background-color: #f3f6fa;
  color: #3f7de8;
}
.menu-list-item:active, .menu-list-item:active > .menu-list-item__text {
  background-color: #d1e3fa;
  color: #3f7de8;
}
.menu-list-item:focus, .menu-list-item:focus > .menu-list-item__text {
  background-color: #f3f6fa;
  color: #3f7de8;
}
.menu-list-item__text {
  color: #e3e3e3;
}