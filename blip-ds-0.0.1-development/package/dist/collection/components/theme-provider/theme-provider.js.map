{"version": 3, "file": "theme-provider.js", "sourceRoot": "", "sources": ["../../../../src/components/theme-provider/theme-provider.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AASzD,MAAM,OAAO,aAAa;IAL1B;QAME;;;WAGG;QACK,UAAK,GAAY,OAAO,CAAC;KASlC;IAPC,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE;YAC1D,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop } from '@stencil/core';\n\nexport type Themes = 'light' | 'dark' | 'high-contrast';\n\n@Component({\n  tag: 'bds-theme-provider',\n  styleUrl: 'theme-provider.scss',\n  shadow: true,\n})\nexport class ThemeProvider {\n  /**\n   * Set what theme will be aplyed inside the component.\n   * 'light', 'dark';\n   */\n  @Prop() theme?: Themes = 'light';\n\n  render() {\n    return (\n      <Host class={{ theme: true, [`theme--${this.theme}`]: true }}>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}