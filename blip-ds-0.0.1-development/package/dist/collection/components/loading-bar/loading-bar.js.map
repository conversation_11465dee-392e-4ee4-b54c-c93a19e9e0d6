{"version": 3, "file": "loading-bar.js", "sourceRoot": "", "sources": ["../../../../src/components/loading-bar/loading-bar.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AASzD,MAAM,OAAO,aAAa;IAL1B;QAME;;WAEG;QACK,YAAO,GAAY,CAAC,CAAC;QAC7B;;WAEG;QACK,SAAI,GAAoB,SAAS,CAAC;QAC1C;;WAEG;QACK,SAAI,GAAY,EAAE,CAAC;QAE3B;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;KAgBlC;IAdC,MAAM;QACJ,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC7F,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,eAAa,IAAI,CAAC,QAAQ;gBACtF,4DAAK,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;oBAC9B,4DAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM;wBAC1C,4DAAK,KAAK,EAAC,QAAQ,GAAO,CACtB,CACF,CACF,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, Prop, h } from '@stencil/core';\n\nexport type loadingBarSize = 'small' | 'default';\n\n@Component({\n  tag: 'bds-loading-bar',\n  styleUrl: 'loading-bar.scss',\n  shadow: true,\n})\nexport class BdsloadingBar {\n  /**\n   * Percent, property to enter the loading bar status percentage value.\n   */\n  @Prop() percent?: number = 0;\n  /**\n   * Size, property to define size of component.\n   */\n  @Prop() size?: loadingBarSize = 'default';\n  /**\n   * Text, property to enable the bar info text.\n   */\n  @Prop() text?: string = '';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    const styles = { width: `${this.percent ? (this.percent > 100 ? 100 : this.percent) : 0}%` };\n    return (\n      <Host>\n        <div class={{ loading_bar: true, [`size_${this.size}`]: true }} data-test={this.dataTest}>\n          <div class={{ bar_behind: true }}>\n            <div class={{ loading: true }} style={styles}>\n              <div class=\"loader\"></div>\n            </div>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}