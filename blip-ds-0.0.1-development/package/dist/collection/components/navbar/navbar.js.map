{"version": 3, "file": "navbar.js", "sourceRoot": "", "sources": ["../../../../src/components/navbar/navbar.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAUlE,MAAM,OAAO,MAAM;IALnB;QAQE;;WAEG;QACK,gBAAW,GAAiB,UAAU,CAAC;QAE/C;;WAEG;QACK,oBAAe,GAAsB,WAAW,CAAC;QAEzD;;WAEG;QACK,mBAAc,GAAoB,eAAe,CAAC;QAE1D;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;KAmBlC;IAjBC,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE;YAC5C,4DACE,KAAK,EAAE;oBACL,MAAM,EAAE,IAAI;oBACZ,CAAC,4BAA4B,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,IAAI;oBACzD,CAAC,wBAAwB,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI;oBAClD,CAAC,6BAA6B,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI;iBAC5D,eACU,IAAI,CAAC,QAAQ;gBAExB,8DAAQ,CACJ,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Prop, Element } from '@stencil/core';\n\nexport type orientation = 'horizontal' | 'vertical';\nexport type navbarBackground = 'surface-1' | 'surface-2' | 'surface-3';\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n@Component({\n  tag: 'bds-navbar',\n  styleUrl: 'navbar.scss',\n  shadow: true,\n})\nexport class Navbar {\n  @Element() hostElement: HTMLElement;\n\n  /**\n   * Navbar orientation. Used to orientation the navbar. Either on the left or on the right.\n   */\n  @Prop() orientation?: orientation = 'vertical';\n\n  /**\n   * Width, number to define navbar width.\n   */\n  @Prop() backgroundColor?: navbarBackground = 'surface-1';\n\n  /**\n   * Justify Content. Used to align itens in navbar.\n   */\n  @Prop() justifyContent?: justifyContent = 'space-between';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host class={{ [`${this.orientation}`]: true }}>\n        <div\n          class={{\n            navbar: true,\n            [`navbar__justify-content__${this.justifyContent}`]: true,\n            [`navbar__orientation__${this.orientation}`]: true,\n            [`navbar__background-color__${this.backgroundColor}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          <slot />\n        </div>\n      </Host>\n    );\n  }\n}\n"]}