/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
}

:host(.horizontal) {
  width: 100%;
  height: fit-content;
}

:host(.vertical) {
  width: fit-content;
  height: 100%;
}

.navbar {
  width: fit-content;
  display: flex;
  gap: 8px;
  box-sizing: border-box;
}
.navbar ::slotted(*) {
  display: flex;
  gap: 8px;
  align-items: center;
}
.navbar__justify-content__flex-start {
  justify-content: flex-start;
}
.navbar__justify-content__center {
  justify-content: center;
}
.navbar__justify-content__flex-end {
  justify-content: flex-end;
}
.navbar__justify-content__space-between {
  justify-content: space-between;
}
.navbar__justify-content__space-around {
  justify-content: space-around;
}
.navbar__justify-content__space-evenly {
  justify-content: space-evenly;
}
.navbar__orientation__horizontal {
  flex-direction: row;
  width: 100%;
  padding: 8px 16px;
}
.navbar__orientation__horizontal ::slotted(*) {
  flex-direction: row;
}
.navbar__orientation__vertical {
  flex-direction: column;
  height: 100%;
  padding: 16px 8px;
}
.navbar__orientation__vertical ::slotted(*) {
  flex-direction: column;
}
.navbar__background-color__surface-1 {
  background-color: var(--color-surface-1, rgb(246, 246, 246));
}
.navbar__background-color__surface-2 {
  background-color: var(--color-surface-2, rgb(237, 237, 237));
}
.navbar__background-color__surface-3 {
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
.navbar__background-color__surface-4 {
  background-color: var(--color-surface-4, rgb(20, 20, 20));
}