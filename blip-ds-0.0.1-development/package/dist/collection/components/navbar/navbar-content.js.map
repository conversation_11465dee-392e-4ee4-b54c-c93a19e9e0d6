{"version": 3, "file": "navbar-content.js", "sourceRoot": "", "sources": ["../../../../src/components/navbar/navbar-content.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAO5D,MAAM,OAAO,aAAa;IAGxB,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDAAC,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;YAClC,8DAAQ,CACH,CACR,CAAC;IACJ,CAAC;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Element } from '@stencil/core';\n\n@Component({\n  tag: 'bds-navbar-content',\n  styleUrl: 'navbar.scss',\n  shadow: true,\n})\nexport class NavbarContent {\n  @Element() hostElement: HTMLElement;\n\n  render() {\n    return (\n      <Host class={{ NavbarContent: true }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"]}