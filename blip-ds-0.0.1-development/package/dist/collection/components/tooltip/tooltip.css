/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.tooltip__wrapper {
  display: inline-block;
  position: relative;
}
.tooltip__tip {
  position: absolute;
  left: 50%;
  border-radius: 8px;
  padding: 8px;
  background: var(--color-content-default, rgb(40, 40, 40));
  z-index: 90000;
  white-space: normal;
  width: max-content;
  min-width: 32px;
  max-width: 320px;
  box-shadow: 0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  cursor: default;
}
.tooltip__tip--visible {
  visibility: visible;
}
.tooltip__tip::before {
  content: "";
  left: 50%;
  border: solid transparent;
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  margin-left: -6px;
  border-width: 6px;
}
.tooltip__tip--top-center, .tooltip__tip--top-left, .tooltip__tip--top-right {
  bottom: calc(100% + 10px);
}
.tooltip__tip--top-center::before, .tooltip__tip--top-left::before, .tooltip__tip--top-right::before {
  top: 100%;
  border-top-color: var(--color-content-default, rgb(40, 40, 40));
}
.tooltip__tip--top-left {
  left: 0;
  transform: translateX(-15%);
}
.tooltip__tip--top-left::before {
  left: calc(15% + 6px);
}
.tooltip__tip--top-right {
  left: initial;
  right: 0;
  transform: translateX(15%);
}
.tooltip__tip--top-right::before {
  left: calc(85% - 6px);
}
.tooltip__tip--bottom-center, .tooltip__tip--top-center {
  transform: translateX(-50%);
}
.tooltip__tip--left-center, .tooltip__tip--right-center {
  transform: translateX(0) translateY(-50%);
}
.tooltip__tip--right-center, .tooltip__tip--right-top, .tooltip__tip--right-bottom {
  left: calc(100% + 10px);
  top: 50%;
}
.tooltip__tip--right-center::before, .tooltip__tip--right-top::before, .tooltip__tip--right-bottom::before {
  left: -5px;
  top: 50%;
  transform: translateX(0) translateY(-50%);
  border-right-color: var(--color-content-default, rgb(40, 40, 40));
}
.tooltip__tip--right-top {
  top: 0;
}
.tooltip__tip--right-top::before {
  top: 40%;
}
.tooltip__tip--right-bottom {
  top: initial;
  bottom: 0;
}
.tooltip__tip--right-bottom::before {
  top: 60%;
}
.tooltip__tip--bottom-center, .tooltip__tip--bottom-right, .tooltip__tip--bottom-left {
  top: calc(100% + 10px);
}
.tooltip__tip--bottom-center::before, .tooltip__tip--bottom-right::before, .tooltip__tip--bottom-left::before {
  bottom: 100%;
  border-bottom-color: var(--color-content-default, rgb(40, 40, 40));
}
.tooltip__tip--bottom-right {
  left: initial;
  right: 0;
  transform: translateX(15%);
}
.tooltip__tip--bottom-right::before {
  left: calc(85% - 6px);
}
.tooltip__tip--bottom-left {
  left: 0;
  transform: translateX(-15%);
}
.tooltip__tip--bottom-left::before {
  left: calc(15% + 6px);
}
.tooltip__tip--left-center, .tooltip__tip--left-top, .tooltip__tip--left-bottom {
  left: auto;
  right: calc(100% + 10px);
  top: 50%;
}
.tooltip__tip--left-center::before, .tooltip__tip--left-top::before, .tooltip__tip--left-bottom::before {
  left: auto;
  right: -11px;
  top: 50%;
  transform: translateX(0) translateY(-50%);
  border-left-color: var(--color-content-default, rgb(40, 40, 40));
}
.tooltip__tip--left-top {
  top: 0;
}
.tooltip__tip--left-top::before {
  top: 40%;
}
.tooltip__tip--left-bottom {
  top: initial;
  bottom: 0;
}
.tooltip__tip--left-bottom::before {
  top: 60%;
}
.tooltip__tip__text pre {
  margin: 0;
  display: flex;
  font-family: inherit;
  white-space: break-spaces;
}
.tooltip__tip__text .text {
  color: var(--color-surface-1, rgb(246, 246, 246));
}