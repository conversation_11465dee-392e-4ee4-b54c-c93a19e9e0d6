{"version": 3, "file": "tooltip.js", "sourceRoot": "", "sources": ["../../../../src/components/tooltip/tooltip.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAqBzE,MAAM,OAAO,OAAO;IALpB;QAME;;WAEG;QACM,gBAAW,GAAG,KAAK,CAAC;QAI7B;;WAEG;QACsB,gBAAW,GAAG,SAAS,CAAC;QAEjD;;WAEG;QACsB,aAAQ,GAAI,KAAK,CAAC;QAE3C;;WAEG;QACK,aAAQ,GAAuB,aAAa,CAAC;QAErD;;WAEG;QACK,aAAQ,GAAY,OAAO,CAAC;QAEpC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;KAyElC;IAvEC;;OAEG;IAEH,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAEO,aAAa,CAAC,KAAc;QAClC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;IACtC,CAAC;IAGD,kBAAkB;QAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvF,CAAC;IAGD,eAAe;QACb,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,MAAM;QACJ,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE,IAAI,CAAC,cAAc;SAC9B,CAAC;QACF,OAAO,CACL,4DAAK,KAAK,EAAC,kBAAkB;YAC3B,4DACE,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAC5C,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAClC,IAAI,CAAC,QAAQ;gBAExB,8DAAQ,CACJ;YACN,4DACE,KAAK,EAAE;oBACL,YAAY,EAAE,IAAI;oBAClB,CAAC,iBAAiB,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI;oBACxC,uBAAuB,EAAE,IAAI,CAAC,WAAW;iBAC1C,EACD,KAAK,EAAE,YAAY;gBAEnB,4DAAK,KAAK,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE;oBACtC;wBACE,iEAAU,KAAK,EAAC,MAAM,aAAS,OAAO,EAAC,OAAO,EAAC,OAAO,IACnD,IAAI,CAAC,UAAU,CACP,CACP,CACF,CACF,CACF,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Method, Prop, State, Watch } from '@stencil/core';\n\nexport type TooltipPostionType =\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom';\n\n@Component({\n  tag: 'bds-tooltip',\n  styleUrl: 'tooltip.scss',\n  shadow: true,\n})\nexport class Tooltip {\n  /**\n   * Used to set tooltip visibility\n   */\n  @State() isMouseOver = false;\n  @State() textVerify: string;\n  @State() maxWidtTooltip: string;\n\n  /**\n   * Used to set tooltip text\n   */\n  @Prop({ mutable: true }) tooltipText = 'Tooltip';\n\n  /**\n   * Used to disable tooltip when the button are avalible\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Used to set tooltip position\n   */\n  @Prop() position: TooltipPostionType = 'left-center';\n\n  /**\n   * Used to set tooltip max width\n   */\n  @Prop() maxWidth?: string = '320px';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async visible() {\n    this.isMouseOver = true;\n  }\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async invisible() {\n    this.isMouseOver = false;\n  }\n\n  private setVisibility(value: boolean) {\n    if (this.disabled) {\n      this.isMouseOver = false;\n      return;\n    }\n    this.isMouseOver = value;\n  }\n\n  componentWillLoad() {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  @Watch('tooltipText')\n  tooltipTextChanged(): void {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n  }\n\n  @Watch('maxWidth')\n  maxWidthChanged(): void {\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  render() {\n    const styleTooltip = {\n      maxWidth: this.maxWidtTooltip,\n    };\n    return (\n      <div class=\"tooltip__wrapper\">\n        <div\n          onMouseEnter={() => this.setVisibility(true)}\n          onMouseLeave={() => this.setVisibility(false)}\n          data-test={this.dataTest}\n        >\n          <slot />\n        </div>\n        <div\n          class={{\n            tooltip__tip: true,\n            [`tooltip__tip--${this.position}`]: true,\n            'tooltip__tip--visible': this.isMouseOver,\n          }}\n          style={styleTooltip}\n        >\n          <div class={{ tooltip__tip__text: true }}>\n            <pre>\n              <bds-typo class=\"text\" no-wrap=\"false\" variant=\"fs-12\">\n                {this.textVerify}\n              </bds-typo>\n            </pre>\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n"]}