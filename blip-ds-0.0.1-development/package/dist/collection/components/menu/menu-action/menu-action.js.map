{"version": 3, "file": "menu-action.js", "sourceRoot": "", "sources": ["../../../../../src/components/menu/menu-action/menu-action.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAU1E,MAAM,OAAO,aAAa;IAL1B;QAUW,mBAAc,GAAa,KAAK,CAAC;QACjC,gBAAW,GAAa,KAAK,CAAC;QAC9B,oBAAe,GAA0B,OAAO,CAAC;QACjD,iBAAY,GAAuB,OAAO,CAAC;QAC3C,iBAAY,GAAa,KAAK,CAAC;QAC/B,WAAM,GAAY,CAAC,CAAC;QACpB,UAAK,GAAG,IAAI,CAAC;QACtB;;WAEG;QACK,eAAU,GAAY,EAAE,CAAC;QACjC;;WAEG;QACK,YAAO,GAAa,KAAK,CAAC;QAClC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QACjC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QACjC;;WAEG;QACK,gBAAW,GAAY,IAAI,CAAC;QACpC;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QACnC;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QAE3B,mBAAc,GAAG,GAAS,EAAE;YAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,CAAC,CAAC;QA+CM,uBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE;YACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3C,CAAC,CAAC;KAqFH;IApIC,iBAAiB;QACf,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAmC,CAAC;YACpE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACzD,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGS,qBAAqB,CAAC,MAAe;QAC7C,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;QAChH,CAAC;IACH,CAAC;IAGS,kBAAkB,CAAC,MAAe;QAC1C,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC7B,CAAC;IACH,CAAC;IAGS,mBAAmB,CAAC,KAAwB;QACpD,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,MAAM;gBACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,MAAM;QACV,CAAC;IACH,CAAC;IAMD,MAAM;QACJ,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC;QAEnD,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;SACzB,CAAC;QAEF,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,CAAC,YAAY,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI;aAC3C,EACD,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,YAAY;YAExB,+DACE,KAAK,EAAE;oBACL,kBAAkB,EAAE,IAAI;oBACxB,CAAC,kCAAkC,CAAC,EAAE,OAAO;oBAC7C,CAAC,mCAAmC,CAAC,EAAE,QAAQ;oBAC/C,CAAC,uCAAuC,CAAC,EAAE,YAAY;oBACvD,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAAC,QAAQ;oBAC/C,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAAC,QAAQ;iBAChD;gBAEA,IAAI,CAAC,QAAQ,IAAI,iEAAU,KAAK,EAAC,WAAW,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,OAAO,GAAY;gBAC3G,4DAAK,KAAK,EAAC,cAAc;oBACtB,IAAI,CAAC,UAAU,IAAI,CAClB,iEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACpD,IAAI,CAAC,UAAU,CACP,CACZ;oBACA,IAAI,CAAC,QAAQ,IAAI,CAChB,iEAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACvD,IAAI,CAAC,QAAQ,CACL,CACZ;oBACA,IAAI,CAAC,WAAW,IAAI,CACnB,iEAAU,KAAK,EAAC,kBAAkB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IAC1D,IAAI,CAAC,WAAW,CACR,CACZ,CACG;gBACL,IAAI,CAAC,OAAO,IAAI,CACf,iEACE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EACtB,IAAI,EAAE,SAAS,IAAI,CAAC,eAAe,EAAE,EACrC,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,GACF,CACb,CACM;YACR,IAAI,CAAC,OAAO,IAAI,CACf,4DACE,KAAK,EAAE;oBACL,mBAAmB,EAAE,IAAI;oBACzB,yBAAyB,EAAE,IAAI,CAAC,YAAY;iBAC7C,EACD,KAAK,EAAE,aAAa;gBAEpB,8DAAQ,CACJ,CACP,CACG,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Element, State, Prop, Watch } from '@stencil/core';\n\nexport type closeSubMenuState = 'close' | 'pending' | 'open';\nexport type positionSubMenuState = 'right' | 'left';\n\n@Component({\n  tag: 'bds-menu-action',\n  styleUrl: 'menu-action.scss',\n  shadow: true,\n})\nexport class BdsMenuAction {\n  private menuElement?: HTMLBdsMenuElement;\n\n  @Element() private element: HTMLElement;\n\n  @State() openParentMenu?: boolean = false;\n  @State() openSubMenu?: boolean = false;\n  @State() positionSubMenu?: positionSubMenuState = 'right';\n  @State() stateSubMenu?: closeSubMenuState = 'close';\n  @State() delaySubMenu?: boolean = false;\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n  /**\n   * ButtonText. Used to enter the display text for the item.\n   */\n  @Prop() buttonText?: string = '';\n  /**\n   * SubMenu. Used to declare that the button will have a submenu.\n   */\n  @Prop() subMenu?: boolean = false;\n  /**\n   * Iconleft. Used to insert the string icon and make the icon available to the left of the item.\n   */\n  @Prop() iconLeft?: string = null;\n  /**\n   * Subtitle. Used to insert a subtitle in the display item.\n   */\n  @Prop() subtitle?: string = null;\n  /**\n   * Description. Used to insert a subtitle in the display item.\n   */\n  @Prop() description?: string = null;\n  /**\n   * Lipstick. Used to declare that the item will be a negative/error action.\n   */\n  @Prop() lipstick?: boolean = false;\n  /**\n   * Disabled. Used to declare that the item will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  componentWillLoad() {\n    if (this.subMenu) {\n      this.menuElement = this.element.parentElement as HTMLBdsMenuElement;\n      this.menuElement.addEventListener('bdsOpenMenu', (event) => {\n        this.onChangeOpenParent(event);\n      });\n    }\n  }\n\n  @Watch('openParentMenu')\n  protected openParentMenuChanged(active: boolean): void {\n    if (active) {\n      const divMenu = this.menuElement.shadowRoot.querySelectorAll('div')[0];\n      this.positionSubMenu = divMenu.offsetLeft + divMenu.offsetWidth + 196 >= window.innerWidth ? 'left' : 'right';\n    }\n  }\n\n  @Watch('openSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: closeSubMenuState): void {\n    switch (state) {\n      case 'open':\n        this.delaySubMenu = true;\n        break;\n      case 'pending':\n        this.delaySubMenu = true;\n        break;\n      case 'close':\n        this.delaySubMenu = false;\n        break;\n    }\n  }\n\n  private onChangeOpenParent = (event) => {\n    this.openParentMenu = event.detail.value;\n  };\n\n  render() {\n    const actLeft = this.iconLeft && !this.subMenu;\n    const actRight = this.subMenu && !this.iconLeft;\n    const actLeftright = this.iconLeft && this.subMenu;\n\n    const openSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 1;\n        this.openSubMenu = true;\n      }\n    };\n\n    const closeSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 0;\n        this.openSubMenu = false;\n      }\n    };\n\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n\n    return (\n      <div\n        class={{\n          menuaction: true,\n          [`position-${this.positionSubMenu}`]: true,\n        }}\n        onMouseOver={openSubmenu}\n        onMouseOut={closeSubmenu}\n      >\n        <button\n          class={{\n            menuaction__button: true,\n            [`menuaction__button__activeicleft`]: actLeft,\n            [`menuaction__button__activeicright`]: actRight,\n            [`menuaction__button__activeicleftright`]: actLeftright,\n            [`menuaction__button__lipstick`]: this.lipstick,\n            [`menuaction__button__disabled`]: this.disabled,\n          }}\n        >\n          {this.iconLeft && <bds-icon class=\"icon-item\" name={this.iconLeft} theme=\"outline\" size=\"small\"></bds-icon>}\n          <div class=\"content-item\">\n            {this.buttonText && (\n              <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\">\n                {this.buttonText}\n              </bds-typo>\n            )}\n            {this.subtitle && (\n              <bds-typo class=\"subtitle-item\" variant=\"fs-10\" tag=\"span\">\n                {this.subtitle}\n              </bds-typo>\n            )}\n            {this.description && (\n              <bds-typo class=\"description-item\" variant=\"fs-10\" tag=\"span\">\n                {this.description}\n              </bds-typo>\n            )}\n          </div>\n          {this.subMenu && (\n            <bds-icon\n              class={{ arrow: true }}\n              name={`arrow-${this.positionSubMenu}`}\n              theme=\"outline\"\n              size=\"small\"\n            ></bds-icon>\n          )}\n        </button>\n        {this.subMenu && (\n          <div\n            class={{\n              menuaction__submenu: true,\n              menuaction__submenu__open: this.delaySubMenu,\n            }}\n            style={zIndexSubmenu}\n          >\n            <slot />\n          </div>\n        )}\n      </div>\n    );\n  }\n}\n"]}