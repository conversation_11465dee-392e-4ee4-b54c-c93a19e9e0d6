/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  height: fit-content;
  border-radius: 4px;
  box-sizing: border-box;
  max-width: 100%;
}
:host .chip {
  display: flex;
  min-width: 32px;
  width: max-content;
  height: 32px;
  border-radius: 16px;
  padding: 2px 4px;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
  position: relative;
  z-index: 0;
}
:host .chip .chip_focus:focus {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 2px;
  border-radius: 4px;
  outline: var(--color-focus, rgb(194, 38, 251)) solid 2px;
}
:host .chip .chip_darker {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  z-index: 1;
  backdrop-filter: brightness(1);
  box-sizing: border-box;
}
:host .chip--icon {
  display: flex;
  align-items: center;
  padding-left: 4px;
  height: 20px;
  z-index: 2;
}
:host .chip--text {
  display: flex;
  align-items: center;
  height: 20px;
  z-index: 2;
  margin: 0 8px;
  flex-wrap: nowrap;
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
}
:host .chip--tall {
  height: 40px;
  border-radius: 24px;
}
:host .chip--default {
  background-color: var(--color-system, rgb(178, 223, 253));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip--info {
  background-color: var(--color-info, rgb(128, 227, 235));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip--success {
  background-color: var(--color-success, rgb(132, 235, 188));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip--warning {
  background-color: var(--color-warning, rgb(253, 233, 155));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip--danger {
  background-color: var(--color-error, rgb(250, 190, 190));
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip--outline {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  color: var(--color-content-default, rgb(40, 40, 40));
  padding: 2px 3px;
}
:host .chip--outline .chip_darker {
  height: calc(100% + 2px);
}
:host .chip:hover {
  cursor: pointer;
}
:host .chip:hover .chip_darker {
  backdrop-filter: brightness(0.9);
}
:host .chip:active {
  cursor: pointer;
}
:host .chip:active .chip_darker {
  backdrop-filter: brightness(0.8);
}
:host .chip:focus-visible {
  outline: none;
}
:host .chip_selected {
  display: flex;
  min-width: 32px;
  width: max-content;
  height: 32px;
  border-radius: 16px;
  padding: 2px;
  align-items: center;
  box-sizing: border-box;
  background-color: var(--color-surface-1, rgb(246, 246, 246));
  border: 2px solid var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_selected--container-text--full {
  width: 100%;
}
:host .chip_selected--container-text--half {
  width: calc(100% - 20px);
}
:host .chip_selected--icon {
  display: flex;
  align-items: center;
  height: 20px;
  padding-left: 4px;
  color: var(--color-content-default, rgb(40, 40, 40));
}
:host .chip_selected--text {
  display: flex;
  align-items: center;
  height: 20px;
  margin: 0 8px;
  flex-wrap: nowrap;
  color: var(--color-content-default, rgb(40, 40, 40));
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
}
:host .chip_selected--tall {
  height: 40px;
  border-radius: 24px;
}
:host .chip_selected:hover {
  opacity: 38%;
  cursor: pointer;
}
:host .chip_selected:active {
  opacity: 38%;
}
:host .chip_disabled {
  display: flex;
  min-width: 32px;
  width: max-content;
  height: 32px;
  border-radius: 16px;
  padding: 2px 4px;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
  position: relative;
  z-index: 0;
  background-color: var(--color-surface-3, rgb(227, 227, 227));
}
:host .chip_disabled--icon {
  display: flex;
  align-items: center;
  padding-left: 4px;
  width: 16px;
  height: 20px;
  color: var(--color-content-default, rgb(40, 40, 40));
  z-index: 2;
}
:host .chip_disabled--text {
  display: flex;
  align-items: center;
  height: 20px;
  z-index: 2;
  margin: 0 8px;
  flex-wrap: nowrap;
  color: var(--color-content-default, rgb(40, 40, 40));
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
}
:host .chip_disabled--tall {
  height: 40px;
  border-radius: 24px;
}
:host .chip_disabled:hover {
  cursor: default;
}