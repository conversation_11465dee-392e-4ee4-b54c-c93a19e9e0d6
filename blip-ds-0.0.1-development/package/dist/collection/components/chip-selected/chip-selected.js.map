{"version": 3, "file": "chip-selected.js", "sourceRoot": "", "sources": ["../../../../src/components/chip-selected/chip-selected.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAU9F,MAAM,OAAO,YAAY;IALzB;QAOW,eAAU,GAAG,KAAK,CAAC;QAK5B;;WAEG;QACK,UAAK,GAAuB,SAAS,CAAC;QAC9C;;WAEG;QACK,SAAI,GAAU,UAAU,CAAC;QACjC;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QACnC;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QACnC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;KAyFlC;IArFS,aAAa,CAAC,KAAK;QACzB,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAK;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7F,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,UAAU;YACpB,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;YAChE,CAAC,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;IACtE,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC;YACrD,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAC;QAClB,CAAC;;YAAM,OAAO,SAAS,CAAC;IAC1B,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DACE,KAAK,gCACH,IAAI,EAAE,IAAI,IACP,IAAI,CAAC,YAAY,EAAE,GACnB,IAAI,CAAC,eAAe,EAAE,GAE3B,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAC1B,IAAI,CAAC,QAAQ;gBAEvB,CAAC,IAAI,CAAC,QAAQ,IAAI,4DAAK,KAAK,EAAC,YAAY,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,GAAO;gBACvG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,4DAAK,KAAK,EAAC,aAAa,GAAO;gBACrE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAChC,4DAAK,KAAK,EAAC,YAAY;oBACrB,iEAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAa,CAChE,CACP;gBACA,IAAI,CAAC,UAAU,IAAI,CAClB,4DAAK,KAAK,EAAC,qBAAqB;oBAC9B,iEAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAC,WAAW,GAAY,CAChE,CACP;gBACD,4DAAK,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC,qCAAqC;oBACzG,iEAAU,KAAK,kBAAI,YAAY,EAAE,IAAI,IAAK,IAAI,CAAC,YAAY,EAAE,GAAI,OAAO,EAAC,OAAO,mBAAS,IAAI,EAAC,MAAM;wBAClG,8DAAa,CACJ,CACP,CACF,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop, Event, EventEmitter, State, Element } from '@stencil/core';\n\nexport type ColorChipSelected = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline';\nexport type Size = 'standard' | 'tall';\n\n@Component({\n  tag: 'bds-chip-selected',\n  styleUrl: 'chip-selected.scss',\n  shadow: true,\n})\nexport class ChipSelected {\n  @Element() el?: HTMLElement;\n  @State() isSelected = false;\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipSelected = 'default';\n  /**\n   * used for change the chip size. Use one of them;\n   */\n  @Prop() size?: Size = 'standard';\n  /**\n   * used for set the initial setup for true;\n   */\n  @Prop() selected?: boolean = false;\n  /**\n   * When 'true', no events will be dispatched\n   */\n  @Prop() disabled?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Event() chipClick: EventEmitter;\n\n  private handleKeyDown(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      if (this.isSelected) {\n        this.isSelected = false;\n      } else {\n        this.isSelected = true;\n      }\n      this.chipClick.emit({ selected: this.isSelected });\n    }\n  }\n\n  private handleClick(event) {\n    if (!this.disabled) {\n      event.preventDefault();\n      if (this.isSelected) {\n        this.isSelected = false;\n      } else {\n        this.isSelected = true;\n      }\n      this.chipClick.emit({ selected: this.isSelected });\n    }\n  }\n\n  componentWillLoad() {\n    this.el.focus();\n    this.isSelected = this.selected;\n  }\n\n  private getDisabledChip() {\n    return this.disabled ? { chip_disabled: true, [`chip_disabled--${this.size}`]: true } : {};\n  }\n\n  private getStyleChip() {\n    return this.isSelected\n      ? { chip_selected: true, [`chip_selected--${this.size}`]: true }\n      : { [`chip--${this.color}`]: true, [`chip--${this.size}`]: true };\n  }\n\n  private getStyleText() {\n    if (this.isSelected) {\n      const chipSelected = { 'chip_selected--text': true };\n      return chipSelected;\n    }\n  }\n\n  private getSizeIconChip() {\n    if (this.size === 'tall') {\n      return 'medium';\n    } else return 'x-small';\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip: true,\n            ...this.getStyleChip(),\n            ...this.getDisabledChip(),\n          }}\n          onClick={(ev) => this.handleClick(ev)}\n          data-test={this.dataTest}\n        >\n          {!this.disabled && <div class=\"chip_focus\" onKeyDown={this.handleKeyDown.bind(this)} tabindex=\"0\"></div>}\n          {!this.isSelected && !this.disabled && <div class=\"chip_darker\"></div>}\n          {this.icon && !this.isSelected && (\n            <div class=\"chip--icon\">\n              <bds-icon size={this.getSizeIconChip()} name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.isSelected && (\n            <div class=\"chip_selected--icon\">\n              <bds-icon size={this.getSizeIconChip()} name=\"checkball\"></bds-icon>\n            </div>\n          )}\n          <div class={this.isSelected ? `chip_selected--container-text--half` : `chip_selected--container-text--full`}>\n            <bds-typo class={{ 'chip--text': true, ...this.getStyleText() }} variant=\"fs-12\" no-wrap bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}