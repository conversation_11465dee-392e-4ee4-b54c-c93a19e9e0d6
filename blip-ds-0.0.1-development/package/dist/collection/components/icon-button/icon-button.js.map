{"version": 3, "file": "icon-button.js", "sourceRoot": "", "sources": ["../../../../src/components/icon-button/icon-button.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,MAAM,eAAe,CAAC;AAcxE,MAAM,OAAO,UAAU;IALvB;QAME;;WAEG;QACK,aAAQ,GAAa,KAAK,CAAC;QAEnC;;;WAGG;QACK,SAAI,GAAoB,UAAU,CAAC;QAE3C;;;WAGG;QACK,YAAO,GAAuB,SAAS,CAAC;QAEhD;;;WAGG;QACsB,cAAS,GAAoB,SAAS,CAAC;QAEhE;;WAEG;QACsB,SAAI,GAAY,IAAI,CAAC;QAE9C;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAOzB,YAAO,GAAgB;YAC7B,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,SAAS;YACnB,KAAK,EAAE,QAAQ;SAChB,CAAC;QAEM,oBAAe,GAAyB;YAC9C,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,QAAQ,EAAE,wBAAwB;YAClC,MAAM,EAAE,sBAAsB;YAC9B,KAAK,EAAE,qBAAqB;YAC5B,kBAAkB,EAAE,+BAA+B;SACpD,CAAC;QAEM,gBAAW,GAAG,CAAC,EAAE,EAAE,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;KAyBH;IAvBC,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAE5B,MAAM,IAAI,GAAa,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAW,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzD,OAAO,CACL,cACE,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,KAAK,EAAE;gBACL,CAAC,cAAc,CAAC,EAAE,IAAI;gBACtB,CAAC,KAAK,CAAC,EAAE,IAAI;gBACb,CAAC,GAAG,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,QAAQ;gBACrC,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;aAC5B,eACU,IAAI,CAAC,QAAQ,EACxB,QAAQ,EAAC,GAAG;YAEZ,gBAAU,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAC,SAAS,GAAY,CAClF,CACV,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Prop, Event, EventEmitter } from '@stencil/core';\nimport { IconSize } from '../icon/icon-interface';\n\nexport type IconButtonSize = 'tall' | 'standard' | 'short';\nexport type IconButtonVariant = 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'secondary--white' | 'delete';\nexport type IconSizeMap = { [key in string]: IconSize };\nexport type IconButtonVariantMap = { [key in IconButtonVariant]: string };\nexport type ButtonIconTheme = 'outline' | 'solid';\n\n@Component({\n  tag: 'bds-button-icon',\n  styleUrl: 'icon-button.scss',\n  shadow: true,\n})\nexport class IconButton {\n  /**\n   * \tIf true, the base button will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: IconButtonSize = 'standard';\n\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'primary', 'secondary', 'ghost', 'dashed';\n   */\n  @Prop() variant?: IconButtonVariant = 'primary';\n\n  /**\n   * The theme of the icon. Can be one of:\n   * 'outline', 'solid';\n   */\n  @Prop({ reflect: true }) iconTheme: ButtonIconTheme = 'outline';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Event buttom onClick.\n   */\n  @Event() bdsClick: EventEmitter;\n\n  private mapSize: IconSizeMap = {\n    tall: 'xxx-large',\n    standard: 'x-large',\n    short: 'medium',\n  };\n\n  private mapVariantStyle: IconButtonVariantMap = {\n    primary: 'icon__button--primary',\n    secondary: 'icon__button--secondary',\n    tertiary: 'icon__button--tertiary',\n    delete: 'icon__button--delete',\n    ghost: 'icon__button--ghost',\n    'secondary--white': 'icon__button--secondary-white',\n  };\n\n  private handleClick = (ev) => {\n    if (!this.disabled) {\n      this.bdsClick.emit(ev);\n    }\n  };\n\n  render(): HTMLElement {\n    if (!this.icon) return null;\n\n    const size: IconSize = this.mapSize[this.size];\n    const state: string = this.mapVariantStyle[this.variant];\n\n    return (\n      <button\n        onClick={(ev) => this.handleClick(ev)}\n        disabled={this.disabled}\n        class={{\n          ['icon__button']: true,\n          [state]: true,\n          [`${state}--disabled`]: this.disabled,\n          [`size-${this.size}`]: true,\n        }}\n        data-test={this.dataTest}\n        tabindex=\"0\"\n      >\n        <bds-icon name={this.icon} size={size} theme={this.iconTheme} color=\"inherit\"></bds-icon>\n      </button>\n    );\n  }\n}\n"]}