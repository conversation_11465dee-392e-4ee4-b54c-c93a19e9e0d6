/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.icon__button {
  border: none;
  margin: 0;
  padding: 0;
  width: auto;
  overflow: visible;
  outline: none;
  background: transparent;
  /* inherit font & color from ancestor */
  color: inherit;
  font: inherit;
  /* Normalize `line-height`. Cannot be changed from `normal` in Firefox 4+. */
  line-height: normal;
  /* Corrects font smoothing for webkit */
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;
  /* Corrects inability to style clickable `input` types in iOS */
  -webkit-appearance: none;
  /* Remove excess padding and border in Firefox 4+ */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  transition: all 0.5s;
  border-radius: 8px;
  padding: 8px;
}
.icon__button::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.icon__button::before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
}
.icon__button:focus-visible {
  outline: none;
}
.icon__button:focus-visible::before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}
.icon__button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 0;
  border-radius: 8px;
}
.icon__button:hover::after {
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
}
.icon__button:active::after {
  background-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
}
.icon__button .icon__button {
  position: relative;
  z-index: 1;
}
.icon__button * {
  pointer-events: none;
}
.icon__button--primary {
  background: var(--color-surface-primary, rgb(30, 107, 241));
}
.icon__button--primary .bds-icon {
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.icon__button--primary--disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
  color: #e8f2ff;
  border: #637798;
}
.icon__button--primary--disabled:hover, .icon__button--primary--disabled:active {
  color: #e8f2ff;
  border: #637798;
}
.icon__button--secondary {
  color: var(--color-content-default, rgb(40, 40, 40));
  background: transparent;
}
.icon__button--secondary .bds-icon {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.icon__button--secondary--disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
  color: transparent;
  border: #637798;
}
.icon__button--secondary--disabled:hover, .icon__button--secondary--disabled:active {
  color: transparent;
  border: #637798;
}
.icon__button--tertiary {
  background: transparent;
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.icon__button--tertiary .bds-icon {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.icon__button--tertiary--disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
  color: #637798;
  border: 1px solid #637798;
}
.icon__button--tertiary--disabled:hover, .icon__button--tertiary--disabled:active {
  color: #637798;
  border: 1px solid #637798;
}
.icon__button--ghost {
  background: transparent;
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.icon__button--ghost .bds-icon {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.icon__button--ghost--disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
  color: #637798;
  border: 1px solid #637798;
}
.icon__button--ghost--disabled:hover, .icon__button--ghost--disabled:active {
  color: #637798;
  border: 1px solid #637798;
}
.icon__button--secondary-white {
  background: transparent;
  color: #f6f6f6;
}
.icon__button--secondary-white:hover, .icon__button--secondary-white:focus {
  background: rgba(246, 246, 246, 0.3);
  color: #f6f6f6;
}
.icon__button--secondary-white:active {
  background: rgba(246, 246, 246, 0.4);
  color: #f6f6f6;
}
.icon__button--secondary-white--disabled {
  cursor: not-allowed;
  color: #637798;
  background: #e8f2ff;
}
.icon__button--delete {
  background: var(--color-delete, rgb(230, 15, 15));
}
.icon__button--delete .bds-icon {
  color: var(--color-content-bright, rgb(255, 255, 255));
}
.icon__button--delete--disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
  color: #ba5a5a;
}
.icon__button--delete--disabled:hover, .icon__button--delete--disabled:active {
  color: #ba5a5a;
}
.icon__button.size-tall {
  width: 56px;
  height: 56px;
}
.icon__button.size-standard {
  width: 48px;
  height: 48px;
}
.icon__button.size-short {
  width: 40px;
  height: 40px;
}