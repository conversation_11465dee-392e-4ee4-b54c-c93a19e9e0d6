{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/components/upload/languages/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAIlC,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,IAAe,EAAE,MAAc,EAAU,EAAE;IACvE,IAAI,SAAS,CAAC;IACd,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,OAAO;YACV,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,MAAM;QACR,KAAK,OAAO;YACV,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,MAAM;QACR,KAAK,OAAO;YACV,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,MAAM;QACR;YACE,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n"]}