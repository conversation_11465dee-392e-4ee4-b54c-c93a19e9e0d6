{"version": 3, "file": "bds-upload.js", "sourceRoot": "", "sources": ["../../../../src/components/upload/bds-upload.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AACvG,OAAO,EAAE,aAAa,EAAa,MAAM,aAAa,CAAC;AACvD,OAAO,UAAU,MAAM,8BAA8B,CAAC;AAOtD,MAAM,OAAO,SAAS;IALtB;QASW,UAAK,GAAW,EAAE,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,UAAK,GAAG,KAAK,CAAC;QAEd,SAAI,GAAa,EAAE,CAAC;QACpB,oBAAe,GAAa,EAAE,CAAC;QAC/B,gBAAW,GAAG,KAAK,CAAC;QAC7B;;WAEG;QACK,aAAQ,GAAe,OAAO,CAAC;QAsBvC;;WAEG;QACK,eAAU,GAAsB,EAAE,CAAC;QAE3C;;;WAGG;QACK,iBAAY,GAAY,IAAI,CAAC;QAErC;;;WAGG;QACK,mBAAc,GAAY,IAAI,CAAC;QAEvC;;;WAGG;QACK,mBAAc,GAAY,IAAI,CAAC;QAkEvC,oBAAe,GAAG,CAAC,IAAU,EAAE,KAAa,EAAE,EAAE;YAC9C,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,OAAO;YACT,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,OAAO;YACT,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACH,eAAU,GAAG,CAAC,KAAK,EAAE,EAAE;YACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC;YAC9B,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC;QAEF;;WAEG;QACH,gBAAW,GAAG,CAAC,KAAK,EAAE,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC;QAwEM,oBAAe,GAAG,CAAC,EAAoB,EAAQ,EAAE;YACvD,IAAI,CAAC,YAAY,GAAG,EAAsB,CAAC;QAC7C,CAAC,CAAC;KAmHH;IArRW,iBAAiB;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACxC,IAAI,CAAC;oBACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrD,CAAC;gBAAC,WAAM,CAAC;oBACP,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;gBAC5B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAGS,YAAY;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAGS,kBAAkB,CAAC,KAAK;QAChC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YACzD,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;QACd,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACnE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAClF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QACH,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAC9C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACxF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QACH,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAC1C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACxF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAoCD;;OAEG;IACH,eAAe,CAAC,CAAC;QACf,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;IACtB,CAAC;IACD;;OAEG;IACH,SAAS,CAAC,OAAO;QACf,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;IACvB,CAAC;IACD;;OAEG;IACI,aAAa,CAAC,KAAK;QACxB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IACD;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IACD;;OAEG;IAEH,KAAK,CAAC,UAAU,CAAC,KAAK;QACpB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IAMO,aAAa,CAAC,KAAK;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,4DAAK,KAAK,EAAC,QAAQ;YACjB,4DAAK,KAAK,EAAC,eAAe;gBACxB,iEAAU,KAAK,EAAC,oBAAoB,EAAC,IAAI,EAAC,WAAW,EAAC,IAAI,EAAC,QAAQ,GAAY;gBAC/E,4DAAK,KAAK,EAAC,oBAAoB;oBAC7B,iEAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,gBAAa,IAAI,CAAC,SAAS,IAC7D,IAAI,CAAC,SAAS,CACN;oBACX,iEAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,gBAAa,IAAI,CAAC,QAAQ,IAC/D,IAAI,CAAC,QAAQ,CACL,CACP,CACF;YACL,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACZ,kBAAY,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,gBAAa,IAAI,CAAC,KAAK,IAChE,IAAI,CAAC,KAAK,CACA,CACd,CAAC,CAAC,CAAC,CACF,EAAE,CACH;YACA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAChB;gBACE,WAAK,KAAK,EAAC,cAAc,IACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,KAAK,EAAE,EAAE,CAAC,CACrC,WAAK,KAAK,EAAC,iBAAiB,EAAC,GAAG,EAAE,KAAK,EAAE,EAAE,EAAC,WAAW;oBACrD,WAAK,KAAK,EAAC,SAAS,EAAC,EAAE,EAAC,SAAS;wBAC/B,gBAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,GAAY;wBAClD,SAAG,KAAK,EAAC,cAAc,EAAC,EAAE,EAAC,cAAc,gBAAa,KAAK,CAAC,IAAI,IAC7D,KAAK,CAAC,IAAI,CACT;wBACJ,uBACE,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,gBACzB,UAAU,KAAK,CAAC,IAAI,EAAE,eACvB,GAAG,IAAI,CAAC,cAAc,IAAI,KAAK,EAAE,GAC3B,CACf,CACF,CACP,CAAC,CACE;gBACL,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CACf,gBACE,OAAO,EAAC,OAAO,EACf,MAAM,QACN,KAAK,EAAC,gBAAgB,gBACV,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAEnD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CACvF,CACZ,CAAC,CAAC,CAAC,CACF,EAAE,CACH,CACG,CACP,CAAC,CAAC,CAAC,CACF,EAAE,CACH;YACD,4DAAK,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;gBAChC,8DACE,KAAK,EAAE,EAAE,qBAAqB,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC,KAAK,EAAE,EACzE,EAAE,EAAC,YAAY,EACf,OAAO,EAAC,MAAM,eACH,IAAI,CAAC,cAAc,EAC9B,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;oBAExC,4DAAK,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,eAAe,IAChF,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACZ,gBACE,KAAK,EAAC,MAAM,EACZ,OAAO,EAAC,OAAO,EACf,IAAI,EAAC,SAAS,gBACF,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAEnD,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAChC,CACZ,CAAC,CAAC,CAAC,CACF,gBACE,KAAK,EAAC,MAAM,EACZ,OAAO,EAAC,OAAO,EACf,IAAI,EAAC,SAAS,gBACF,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,IAEtD,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CACnC,CACZ,CACG;oBACN,4DAAK,KAAK,EAAE,EAAE,wBAAwB,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,UAAU,GAAI,CACjG;gBACR,8DACE,GAAG,EAAE,IAAI,CAAC,eAAe,EACzB,IAAI,EAAC,MAAM,EACX,IAAI,EAAC,SAAS,EACd,EAAE,EAAC,MAAM,EACT,KAAK,EAAC,eAAe,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EACvF,QAAQ,EAAE,CAAC,MAAW,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,eACvD,IAAI,CAAC,YAAY,GAC5B,CACE,CACF,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Element, State, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\nimport { termTranslate, languages } from './languages';\nimport background from '../../assets/svg/pattern.svg';\n\n@Component({\n  tag: 'bds-upload',\n  styleUrl: 'bds-upload.scss',\n  shadow: true,\n})\nexport class BdsUpload {\n  private inputElement?: HTMLInputElement;\n\n  @Element() private dropArea: HTMLElement;\n  @State() files: File[] = [];\n  @State() haveFiles = false;\n  @State() hover = false;\n  @State() background: string;\n  @State() size: number[] = [];\n  @State() internalAccepts: string[] = [];\n  @State() formatError = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * Used for add a text on title.\n   */\n  @Prop() titleName: string;\n  /**\n   * Used for add a text on subtitle.\n   */\n  @Prop() subtitle: string;\n  /**\n   * Used for add a error message. In case a verify.\n   */\n  @Prop({ reflect: true, mutable: true }) error: string;\n  /**\n   * Used to allow upload multiple files.\n   */\n  @Prop() multiple: boolean;\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() accept: string;\n\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() dataAccept: string[] | string = [];\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtInputFiles is the data-test to button clear.\n   */\n  @Prop() dtInputFiles?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtLabelAddFile is the data-test to button clear.\n   */\n  @Prop() dtLabelAddFile?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonDelete is the data-test to button clear.\n   */\n  @Prop() dtButtonDelete?: string = null;\n  /**\n   * Event emited when delete a item from the list.\n   */\n  @Event() bdsUploadDelete: EventEmitter;\n  /**\n   * Event emited when change the value of Upload.\n   */\n  @Event() bdsUploadChange: EventEmitter;\n\n  @Watch('dataAccept')\n  protected dataAcceptChanged(): void {\n    if (this.dataAccept) {\n      if (typeof this.dataAccept === 'string') {\n        try {\n          this.internalAccepts = JSON.parse(this.dataAccept);\n        } catch {\n          this.internalAccepts = [];\n        }\n      } else {\n        this.internalAccepts = this.dataAccept;\n      }\n    } else {\n      this.internalAccepts = [];\n    }\n  }\n\n  @Watch('files')\n  protected filesChanged(): void {\n    if (this.files.length > 0) {\n      for (let i = 0; i < this.files.length; i++) {\n        if (this.internalAccepts.length > 0) {\n          this.validationFiles(this.files[i], i);\n        }\n      }\n    }\n  }\n\n  @Watch('formatError')\n  protected formatErrorChanged(value): void {\n    if (value) {\n      this.error = termTranslate(this.language, 'formatError');\n      setTimeout(() => (this.error = null), 5000);\n    }\n  }\n\n  componentWillLoad() {\n    this.dataAcceptChanged();\n  }\n\n  componentDidLoad() {\n    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragenter', 'dragover'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(false), false);\n    });\n    this.dropArea.shadowRoot.addEventListener('drop', this.handleDrop, false);\n  }\n\n  validationFiles = (File: File, index: number) => {\n    const filetype = `.${File.name.split('.').pop()}`;\n    const validate = this.internalAccepts.includes(filetype);\n    if (validate) {\n      this.formatError = false;\n      return;\n    } else {\n      this.formatError = true;\n      this.deleteFile(index);\n      return;\n    }\n  };\n\n  /**\n   * Recive the file data using drag and drop.\n   */\n  handleDrop = (Event) => {\n    this.haveFiles = true;\n    const dt = Event.dataTransfer;\n    const files = dt.files;\n    this.handleFiles(files);\n  };\n\n  /**\n   * Verify if allow the state recive one or more items.\n   */\n  handleFiles = (files) => {\n    if (!this.multiple) {\n      this.files = [files[0]];\n    } else {\n      this.files = [...this.files, ...files];\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  };\n  /**\n   * Prevent the screen to reload.\n   */\n  preventDefaults(e) {\n    e.preventDefault();\n    e.stopPropagation();\n  }\n  /**\n   * Definy if are hover to aply styles in drop area.\n   */\n  hoverFile(boolean) {\n    this.hover = boolean;\n  }\n  /**\n   * Recive the file data using click.\n   */\n  public onUploadClick(files) {\n    if (files.length > 0) {\n      if (!this.multiple) {\n        this.files = [files[0]];\n      } else {\n        this.files = [...this.files, ...files];\n      }\n      this.haveFiles = true;\n      this.getSize();\n    } else {\n      return false;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n  /**\n   * Return the size information from the file.\n   */\n  getSize() {\n    this.files.map((size: any) => {\n      const listSize = size.size;\n      this.size.push(listSize);\n    });\n  }\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteFile(index) {\n    const fileToDelete = this.files.filter((item, i) => i == index && item);\n    this.bdsUploadDelete.emit({ value: fileToDelete });\n    this.files.splice(index, 1);\n    this.files = [...this.files];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteAllFiles() {\n    this.bdsUploadDelete.emit({ value: this.files });\n    this.files = [];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  private refInputElement = (el: HTMLInputElement): void => {\n    this.inputElement = el as HTMLInputElement;\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.inputElement.click();\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"upload\">\n        <div class=\"upload-header\">\n          <bds-icon class=\"upload-header_icon\" size=\"xxx-large\" name=\"upload\"></bds-icon>\n          <div class=\"upload-header_text\">\n            <bds-typo variant=\"fs-16\" bold=\"bold\" aria-label={this.titleName}>\n              {this.titleName}\n            </bds-typo>\n            <bds-typo variant=\"fs-14\" bold=\"regular\" aria-label={this.subtitle}>\n              {this.subtitle}\n            </bds-typo>\n          </div>\n        </div>\n        {this.error ? (\n          <bds-banner context=\"inside\" variant=\"error\" aria-label={this.error}>\n            {this.error}\n          </bds-banner>\n        ) : (\n          ''\n        )}\n        {this.haveFiles ? (\n          <div>\n            <div class=\"list-preview\">\n              {this.files.map((names: any, index) => (\n                <div class=\"upload__preview\" key={index} id=\"drop-area\">\n                  <div class=\"preview\" id=\"preview\">\n                    <bds-icon size=\"x-small\" name=\"attach\"></bds-icon>\n                    <p class=\"preview-text\" id=\"preview-text\" aria-label={names.name}>\n                      {names.name}\n                    </p>\n                    <bds-button-icon\n                      class=\"preview-icon\"\n                      size=\"short\"\n                      icon=\"trash\"\n                      variant=\"secondary\"\n                      onClick={() => this.deleteFile(index)}\n                      aria-label={`delete ${names.name}`}\n                      data-test={`${this.dtButtonDelete}-${index}`}\n                    ></bds-button-icon>\n                  </div>\n                </div>\n              ))}\n            </div>\n            {this.multiple ? (\n              <bds-typo\n                variant=\"fs-14\"\n                italic\n                class=\"preview-length\"\n                aria-label={termTranslate(this.language, 'uploaded')}\n              >\n                {this.files.length > 1 ? `${this.files.length} ${termTranslate(this.language, 'uploaded')}` : ''}\n              </bds-typo>\n            ) : (\n              ''\n            )}\n          </div>\n        ) : (\n          ''\n        )}\n        <div class={{ upload__edit: true }}>\n          <label\n            class={{ 'upload__edit--label': true, 'upload__edit--hover': this.hover }}\n            id=\"file-label\"\n            htmlFor=\"file\"\n            data-test={this.dtLabelAddFile}\n            tabindex=\"0\"\n            onKeyDown={this.handleKeyDown.bind(this)}\n          >\n            <div class={{ 'text-box': true, 'text-box--hover': this.hover }} id=\"file-text_box\">\n              {this.hover ? (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropHere')}\n                >\n                  {termTranslate(this.language, 'dropHere')}\n                </bds-typo>\n              ) : (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropOrClick')}\n                >\n                  {termTranslate(this.language, 'dropOrClick')}\n                </bds-typo>\n              )}\n            </div>\n            <img class={{ 'upload__img--invisible': true, 'upload__img--visible': this.hover }} src={background} />\n          </label>\n          <input\n            ref={this.refInputElement}\n            type=\"file\"\n            name=\"files[]\"\n            id=\"file\"\n            class=\"upload__input\"\n            multiple={this.multiple}\n            accept={this.internalAccepts.length > 0 ? this.internalAccepts.toString() : this.accept}\n            onChange={($event: any) => this.onUploadClick($event.target.files)}\n            data-test={this.dtInputFiles}\n          />\n        </div>\n      </div>\n    );\n  }\n}\n"]}