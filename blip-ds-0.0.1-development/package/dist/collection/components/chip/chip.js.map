{"version": 3, "file": "chip.js", "sourceRoot": "", "sources": ["../../../../src/components/chip/chip.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,OAAO,EAAE,MAAM,eAAe,CAAC;AAUvF,MAAM,OAAO,IAAI;IALjB;QAaE;;;WAGG;QACK,SAAI,GAAc,UAAU,CAAC;QAErC;;;WAGG;QACK,YAAO,GAAiB,SAAS,CAAC;QAE1C;;WAEG;QACsB,WAAM,GAAI,KAAK,CAAC;QAEzC;;WAEG;QACK,WAAM,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACK,cAAS,GAAG,KAAK,CAAC;QAE1B;;WAEG;QACK,cAAS,GAAG,KAAK,CAAC;QAE1B;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;KAqE1B;IA9DC,iBAAiB,CAAC,KAAK;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO;QAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEO,aAAa;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACvD,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;IACxF,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,EAAE,CAAC;YAClC,OAAO,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACtC,CAAC;QAED,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,KAAK,8CACH,IAAI,EAAE,IAAI,IACP,IAAI,CAAC,aAAa,EAAE,GACpB,IAAI,CAAC,aAAa,EAAE,GACpB,IAAI,CAAC,YAAY,EAAE;YAGvB,IAAI,CAAC,IAAI,IAAI,CACZ,4DAAK,KAAK,EAAC,YAAY;gBACrB,iEAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAa,CACjD,CACP;YACD,8DAAQ;YACP,IAAI,CAAC,SAAS,IAAI,CACjB,4DAAK,KAAK,EAAC,cAAc,EAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;gBAClE,iEAAU,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,GAAY,CAC3D,CACP,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop, Event, EventEmitter, Element } from '@stencil/core';\n\nexport type ChipSize = 'standard' | 'tall';\nexport type ChipVariant = 'primary' | 'default' | 'watermelon';\n\n@Component({\n  tag: 'bds-chip',\n  styleUrl: 'chip.scss',\n  shadow: true,\n})\nexport class Chip {\n  @Element() private element: HTMLElement;\n\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n\n  /**\n   * Chip size. Entered as one of the size design tokens. Can be one of:\n   * \"standard\" and \"tall\"\n   */\n  @Prop() size?: ChipSize = 'standard';\n\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'primary', 'default';\n   */\n  @Prop() variant?: ChipVariant = 'default';\n\n  /**\n   * Add state danger on chip, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state filter on chip whith specific color.\n   */\n  @Prop() filter = false;\n\n  /**\n   * When 'true' and the component is using the primary variant, a hover is added\n   */\n  @Prop() clickable = false;\n\n  /**\n   * When 'true', the component recive remove button and dispach event onBdsDelete\n   */\n  @Prop() deletable = false;\n\n  /**\n   * When 'true', no events will be dispatched\n   */\n  @Prop() disabled = false;\n\n  /**\n   *  Triggered after a mouse click on delete icon, return id element. Only fired when deletable is true.\n   */\n  @Event() bdsDelete: EventEmitter;\n\n  handleClickDelete(event) {\n    if (!this.deletable || this.disabled) return;\n    event.preventDefault();\n    this.bdsDelete.emit({ id: this.element.id });\n  }\n\n  private getClickClass() {\n    return this.clickable ? { 'chip--click': true } : {};\n  }\n\n  private getSizeClass() {\n    return this.size === 'standard' ? { 'chip--standard': true } : { 'chip--tall': true };\n  }\n\n  private getStateClass() {\n    if (this.disabled) {\n      return { 'chip--default': true };\n    }\n\n    if (this.danger) {\n      return { 'chip--danger': true };\n    }\n\n    if (this.filter) {\n      return { 'chip--filter': true };\n    }\n\n    if (this.variant === 'primary') {\n      return { 'chip--primary': true };\n    }\n\n    if (this.variant === 'watermelon') {\n      return { 'chip--watermelon': true };\n    }\n\n    return { 'chip--default': true };\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          chip: true,\n          ...this.getClickClass(),\n          ...this.getStateClass(),\n          ...this.getSizeClass(),\n        }}\n      >\n        {this.icon && (\n          <div class=\"chip__icon\">\n            <bds-icon size=\"x-small\" name={this.icon}></bds-icon>\n          </div>\n        )}\n        <slot />\n        {this.deletable && (\n          <div class=\"chip__delete\" onClick={this.handleClickDelete.bind(this)}>\n            <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"]}