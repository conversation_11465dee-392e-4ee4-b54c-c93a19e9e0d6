{"version": 3, "file": "input-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/input/input-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { CounterTextRule } from '../counter-text/counter-text-interface';\nexport type InputType = 'text' | 'password' | 'email' | 'number' | 'phonenumber' | 'date';\n\nexport type InputAutocapitalize = 'off' | 'none' | 'words' | 'on' | 'sentences' | 'characters';\n\nexport type InputAutoComplete = 'on' | 'off' | 'current-password' | 'new-password' | 'username';\n\nexport type InputCounterLengthRules = {\n  warning: CounterTextRule;\n  delete: CounterTextRule;\n};\n"]}