{"version": 3, "file": "input.js", "sourceRoot": "", "sources": ["../../../../src/components/input/input.tsx"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAgB,MAAM,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAEpG,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAO5E,MAAM,OAAO,KAAK;IALlB;QAQW,cAAS,GAAI,KAAK,CAAC;QACnB,eAAU,GAAI,KAAK,CAAC;QACpB,qBAAgB,GAAI,EAAE,CAAC;QACvB,qBAAgB,GAAI,KAAK,CAAC;QACnC;;WAEG;QACK,cAAS,GAAI,EAAE,CAAC;QAExB;;WAEG;QACsB,SAAI,GAAe,MAAM,CAAC;QAEnD;;WAEG;QACK,UAAK,GAAI,EAAE,CAAC;QAEpB;;WAEG;QACK,gBAAW,GAAY,EAAE,CAAC;QAElC;;WAEG;QACK,mBAAc,GAAyB,KAAK,CAAC;QAErD;;WAEG;QACK,iBAAY,GAAuB,KAAK,CAAC;QAsBjD;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAYzB;;WAEG;QACK,kBAAa,GAAY,EAAE,CAAC;QAEpC;;WAEG;QACsB,iBAAY,GAAY,EAAE,CAAC;QAEpD;;WAEG;QACsB,mBAAc,GAAY,EAAE,CAAC;QAEtD;;WAEG;QACsB,SAAI,GAAY,EAAE,CAAC;QAE5C;;WAEG;QACqC,aAAQ,GAAa,KAAK,CAAC;QAEnE;;WAEG;QACqC,WAAM,GAAa,KAAK,CAAC;QAEjE;;WAEG;QACqC,YAAO,GAAa,KAAK,CAAC;QAElE;;WAEG;QACsB,UAAK,GAAmB,EAAE,CAAC;QAEpD;;WAEG;QACK,kBAAa,GAAI,KAAK,CAAC;QAE/B;;WAEG;QACK,sBAAiB,GAA6B,IAAI,CAAC;QAE3D;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACK,eAAU,GAAG,KAAK,CAAC;QAE3B;;WAEG;QACK,SAAI,GAAY,CAAC,CAAC;QAE1B;;WAEG;QACK,SAAI,GAAY,CAAC,CAAC;QAqC1B;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAEzB,WAAM,GAAa,KAAK,CAAC;QA8GjC;;WAEG;QACK,oBAAe,GAAG,CAAC,KAAoB,EAAQ,EAAE;YACvD,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBAElD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,KAAK,CAAC,cAAc,EAAE,CAAC;oBACzB,CAAC;oBACD,MAAM;gBACR,KAAK,WAAW,CAAC;gBACjB,KAAK,QAAQ;oBACX,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC5D,MAAM;YACV,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACK,YAAO,GAAG,CAAC,EAAc,EAAQ,EAAE;YACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC,CAAC;YACnD,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACjC,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF;;WAEG;QACK,WAAM,GAAG,GAAS,EAAE;YAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC,CAAC;QAEF;;WAEG;QACK,YAAO,GAAG,GAAS,EAAE;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC,CAAC;QAEF;;WAEG;QACK,mBAAc,GAAG,GAAS,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEF;;WAEG;QACK,mBAAc,GAAG,CAAC,EAAU,EAAE,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;gBAC3C,EAAE,CAAC,cAAc,EAAE,CAAC;gBACpB,EAAE,CAAC,eAAe,EAAE,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAEhB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC;KA8PH;IAhZC;;OAEG;IAEH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,eAAe;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;IACzC,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAc;QAChC,MAAM,EAAE,GAAG,IAAI,EACb,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,GAAG,GAAG,IAAI,EACV,KAAK,GAAG,KAAK,CAAC;QACpB,IAAG,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAC1B,OAAO,CACL,KAAK;YACL,KAAK;iBACF,QAAQ,EAAE;iBACV,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;iBACnB,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;iBACnB,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC;iBACpB,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC;iBACpB,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;iBACrB,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IAEO,YAAY,CAAC,QAAuB;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC;QACpF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;IAC9C,CAAC;IA6ED;;OAEG;IACK,UAAU;QAChB,OAAO,CACL,IAAI,CAAC,IAAI,IAAI,CACX,WACE,KAAK,EAAE;gBACL,WAAW,EAAE,IAAI;gBACjB,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;aACnC;YAED,gBACE,KAAK,EAAC,oBAAoB,EAC1B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EACrC,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,GACL,CACR,CACP,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,OAAO,CACL,IAAI,CAAC,KAAK,IAAI,CACZ,aACE,KAAK,EAAE;gBACL,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE;YAED,gBAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,IAClC,IAAI,CAAC,KAAK,CACF,CACL,CACT,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;QACzE,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAExG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEvE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAClC,CAAC,CAAC,uCAAuC;YACzC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACZ,CAAC,CAAC,wCAAwC;gBAC1C,CAAC,CAAC,gBAAgB,CAAC;QAEzB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CACL,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB;gBACvC,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,gBAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E;gBACN,gBAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,IACnD,OAAO,CACC,CACP,CACP,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9D,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QAChD,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO;QACT,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO;QACT,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC7D,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC;QAEvD,OAAO,CACL,EAAC,IAAI,sEAAgB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;YAChD,4DACE,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI;oBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBAC9D,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;oBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;oBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;oBACtC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;oBAC5B,gBAAgB,EAAE,SAAS;iBAC5B,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB;gBAErB,IAAI,CAAC,UAAU,EAAE;gBAClB,6DAAM,IAAI,EAAC,YAAY,GAAQ;gBAC/B,4DAAK,KAAK,EAAC,kBAAkB;oBAC1B,IAAI,CAAC,WAAW,EAAE;oBACnB,4DAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,gCAAgC,EAAE,IAAI,CAAC,KAAK,EAAE;wBAClG,6DAAM,IAAI,EAAC,mBAAmB,GAAQ;wBACtC,EAAC,OAAO,qDACN,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,KAAK,EAAE,EAClF,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAC,OAAO,eACD,IAAI,CAAC,QAAQ,GACf,CACP,CACF;gBACL,IAAI,CAAC,aAAa,IAAI,CACrB,uFACE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EACzB,GAAG,EAAE,IAAI,CAAC,SAAS,EACnB,MAAM,EAAE,SAAS,IACb,IAAI,CAAC,iBAAiB,EAC1B,CACH;gBACA,IAAI,CAAC,OAAO,IAAI,iEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,OAAO,GAAG;gBAC5F,6DAAM,IAAI,EAAC,aAAa,GAAG,CACvB;YACL,IAAI,CAAC,aAAa,EAAE,CAChB,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["/* eslint-disable no-console */\nimport { Component, h, Prop, State, Watch, Event, EventEmitter, Method, Host } from '@stencil/core';\nimport { InputType, InputAutocapitalize, InputAutoComplete, InputCounterLengthRules } from './input-interface';\nimport { emailValidation, numberValidation } from '../../utils/validations';\n\n@Component({\n  tag: 'bds-input',\n  styleUrl: 'input.scss',\n  shadow: true,\n})\nexport class Input {\n  private nativeInput?: HTMLInputElement;\n\n  @State() isPressed? = false;\n  @State() isPassword? = false;\n  @State() validationMesage? = '';\n  @State() validationDanger? = false;\n  /**\n   * Nome do input, usado para identificação no formulário.\n   */\n  @Prop() inputName? = '';\n\n  /**\n   * Define o tipo do input (por exemplo, `text`, `password`, etc).\n   */\n  @Prop({ reflect: true }) type?: InputType = 'text';\n\n  /**\n   * <PERSON><PERSON><PERSON><PERSON> que será exibido acima do input.\n   */\n  @Prop() label? = '';\n\n  /**\n   * Texto que será exibido como sugestão ou dica no input.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Define a capitalização automática do texto (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Define o comportamento de autocompletar do navegador (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * Define o valor máximo permitido para o input.\n   */\n  @Prop() max?: string;\n\n  /**\n   * Define o número máximo de caracteres permitidos no input.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * Define o valor mínimo permitido para o input.\n   */\n  @Prop() min?: string;\n\n  /**\n   * Define o número mínimo de caracteres permitidos no input.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * Torna o input somente leitura.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Define se o input é obrigatório.\n   */\n  @Prop() required: boolean;\n\n  /**\n   * Define um padrão regex que o valor do input deve seguir.\n   */\n  @Prop() pattern?: string;\n\n  /**\n   * Mensagem de ajuda exibida abaixo do input.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Mensagem de erro exibida quando o valor do input é inválido.\n   */\n  @Prop({ mutable: true }) errorMessage?: string = '';\n\n  /**\n   * Mensagem exibida quando o valor do input é válido.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n\n  /**\n   * Nome do ícone a ser exibido dentro do input.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Define se o input está desabilitado.\n   */\n  @Prop({ reflect: true, mutable: true }) disabled?: boolean = false;\n\n  /**\n   * Define se o input está em estado de erro.\n   */\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\n\n  /**\n   * Define se o input está em estado de sucesso.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n\n  /**\n   * O valor atual do input.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Define se será exibido um contador de comprimento de caracteres.\n   */\n  @Prop() counterLength? = false;\n\n  /**\n   * Define a regra do contador de comprimento de caracteres (min, max, etc).\n   */\n  @Prop() counterLengthRule?: InputCounterLengthRules = null;\n\n  /**\n   * Define se o input será submetido ao pressionar Enter.\n   */\n  @Prop() isSubmit = false;\n\n  /**\n   * Define se o input é uma área de texto (textarea).\n   */\n  @Prop() isTextarea = false;\n\n  /**\n   * Define a quantidade de linhas da área de texto (se for `textarea`).\n   */\n  @Prop() rows?: number = 1;\n\n  /**\n   * Define a quantidade de colunas da área de texto (se for `textarea`).\n   */\n  @Prop() cols?: number = 0;\n\n  /**\n   * Mensagem de erro exibida quando o input não é preenchido e é obrigatório.\n   */\n  @Prop() requiredErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao comprimento mínimo.\n   */\n  @Prop() minlengthErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor mínimo permitido.\n   */\n  @Prop() minErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor máximo permitido.\n   */\n  @Prop() maxErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um email válido.\n   */\n  @Prop() emailErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um número válido.\n   */\n  @Prop() numberErrorMessage: string;\n\n  /**\n   * Define se o input será exibido como chips (um tipo de entrada com múltiplos valores).\n   */\n  @Prop() chips: boolean;\n\n  /**\n   * Data test é a prop para testar especificamente a ação do componente.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Prop() encode?: boolean = false;\n\n  /**\n   * Evento disparado quando o valor do input muda.\n   */\n  @Event({ bubbles: true, composed: true }) bdsChange!: EventEmitter;\n\n  /**\n   * Evento disparado quando o input recebe um input (digitação).\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Evento disparado quando o input perde o foco.\n   */\n  @Event() bdsOnBlur: EventEmitter;\n\n  /**\n   * Evento disparado quando o input ganha o foco.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  /**\n   * Evento disparado quando o formulário é submetido.\n   */\n  @Event() bdsSubmit: EventEmitter;\n\n  /**\n   * Evento disparado para validação de padrão regex.\n   */\n  @Event() bdsPatternValidation: EventEmitter;\n\n  /**\n   * Evento disparado quando a tecla \"Backspace\" é pressionada.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  /**\n   * Define o foco no campo de entrada.\n   */\n  @Method()\n  async setFocus(): Promise<void> {\n    this.onClickWrapper();\n  }\n\n  /**\n   * Remove o foco do campo de entrada.\n   */\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.onBlur();\n  }\n\n  /**\n   * Retorna o elemento de input do componente.\n   */\n  @Method()\n  async getInputElement(): Promise<HTMLInputElement> {\n    return this.nativeInput;\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.nativeInput.validity.valid;\n  }\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.value = '';\n  }\n\n  /**\n   * Codifica os caracteres especiais para exibição segura (evita injeção de código HTML).\n   */\n  private encodeValue(value?: string): string {\n    const lt = /</g,\n      gt = />/g,\n      ap = /'/g,\n      ic = /\"/g,\n      amp = /&/g,\n      slash = /\\//g;\nif(!this.encode) return value;\n    return (\n      value &&\n      value\n        .toString()\n        .replace(lt, '&lt;')\n        .replace(gt, '&gt;')\n        .replace(ap, '&#39;')\n        .replace(ic, '&#34;')\n        .replace(amp, '&amp;')\n        .replace(slash, '&#47;')\n    );\n  }\n\n  /**\n   * Avisa sobre a mudança do valor do campo de entrada.\n   */\n  @Watch('value')\n  protected valueChanged(newValue: string | null): void {\n    const changeValue = this.encode ? this.encodeValue(newValue || '') : newValue || '';\n    this.bdsChange.emit({ value: changeValue });\n  }\n\n  /**\n   * Tratamento de eventos de pressionamento de tecla (Enter, Backspace, etc).\n   */\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsSubmit.emit({ event, value: this.value });\n\n        if (this.isSubmit) {\n          this.clearTextInput();\n          event.preventDefault();\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  /**\n   * Função chamada ao digitar no campo de entrada.\n   */\n  private onInput = (ev: InputEvent): void => {\n    this.onBdsInputValidations();\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n  };\n\n  /**\n   * Função chamada ao perder o foco do campo de entrada.\n   */\n  private onBlur = (): void => {\n    this.onBlurValidations();\n    this.isPressed = false;\n    this.bdsOnBlur.emit();\n  };\n\n  /**\n   * Função chamada ao ganhar o foco do campo de entrada.\n   */\n  private onFocus = (): void => {\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  /**\n   * Função chamada ao clicar no campo de entrada.\n   */\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  private clearTextInput = (ev?: Event) => {\n    if (!this.readonly && !this.disabled && ev) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n\n    this.value = '';\n\n    if (this.nativeInput) {\n      this.nativeInput.value = '';\n    }\n  };\n\n  /**\n   * Função que renderiza o ícone dentro do campo de entrada.\n   */\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon\n            class=\"input__icon--color\"\n            size={this.label ? 'medium' : 'small'}\n            name={this.icon}\n            color=\"inherit\"\n          ></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza a label do campo de entrada.\n   */\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza as mensagens de erro ou sucesso abaixo do campo de entrada.\n   */\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Valida o campo de entrada ao perder o foco.\n   */\n  private onBlurValidations() {\n    this.required && this.requiredValidation();\n    this.pattern && this.patternValidation();\n    (this.minlength || this.maxlength) && this.lengthValidation();\n    (this.min || this.max) && this.minMaxValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Realiza as validações do campo enquanto o usuário digita.\n   */\n  private onBdsInputValidations() {\n    this.type === 'email' && this.emailValidation();\n    this.type === 'phonenumber' && this.numberValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Valida o padrão regex do campo.\n   */\n  private patternValidation() {\n    const regex = new RegExp(this.pattern);\n    this.bdsPatternValidation.emit(regex.test(this.nativeInput.value));\n  }\n\n  /**\n   * Valida se o campo é obrigatório.\n   */\n  private requiredValidation() {\n    if (this.nativeInput.validity.valueMissing) {\n      this.validationMesage = this.requiredErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida o comprimento do texto no campo de entrada.\n   */\n  private lengthValidation() {\n    if (this.nativeInput.validity.tooShort) {\n      this.validationMesage = this.minlengthErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.tooLong) {\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida os valores mínimos e máximos do campo de entrada.\n   */\n  private minMaxValidation() {\n    if (this.nativeInput.validity.rangeUnderflow) {\n      this.validationMesage = this.minErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.rangeOverflow) {\n      this.validationMesage = this.maxErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um email válido.\n   */\n  private emailValidation() {\n    if (emailValidation(this.nativeInput.value)) {\n      this.validationMesage = this.emailErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um número válido.\n   */\n  private numberValidation() {\n    if (numberValidation(this.nativeInput.value)) {\n      this.validationMesage = this.numberErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  /**\n   * Atualiza o valor do campo de entrada após as mudanças.\n   */\n  componentDidUpdate() {\n    if (this.nativeInput && this.value != this.nativeInput.value) {\n      this.nativeInput.value = this.value;\n    }\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const Element = this.isTextarea ? 'textarea' : 'input';\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <slot name=\"input-left\"></slot>\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: !this.chips, input__container__wrapper__chips: this.chips }}>\n              <slot name=\"inside-input-left\"></slot>\n              <Element\n                class={{ input__container__text: true, input__container__text__chips: this.chips }}\n                ref={(input) => (this.nativeInput = input)}\n                rows={this.rows}\n                cols={this.cols}\n                autocapitalize={this.autoCapitalize}\n                autocomplete={this.autoComplete}\n                disabled={this.disabled}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                name={this.inputName}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.onInput}\n                placeholder={this.placeholder}\n                readOnly={this.readonly}\n                type={this.type}\n                value={this.encodeValue(this.value)}\n                pattern={this.pattern}\n                required={this.required}\n                part=\"input\"\n                data-test={this.dataTest}\n              ></Element>\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text\n              length={this.value.length}\n              max={this.maxlength}\n              active={isPressed}\n              {...this.counterLengthRule}\n            />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"small\" />}\n          <slot name=\"input-right\" />\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"]}