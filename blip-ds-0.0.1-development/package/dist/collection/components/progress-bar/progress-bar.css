/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
}

.progress_bar {
  box-sizing: border-box;
  position: relative;
  border-radius: 32px;
  border: 1px solid var(--color-content-disable, rgb(89, 89, 89));
  margin-bottom: 4px;
}
.progress_bar.size_small {
  height: 8px;
}
.progress_bar.size_small .bar_behind .progress {
  border-radius: 1px;
}
.progress_bar.size_default {
  height: 16px;
}
.progress_bar.size_default .bar_behind .progress {
  border-radius: 2px;
}
.progress_bar .bar_behind {
  position: absolute;
  inset: 0.5px 1px 1px 0.5px;
  border-radius: 16px;
  overflow: hidden;
}
.progress_bar .bar_behind .progress {
  position: absolute;
  height: 100%;
  transition: all 0.3s;
  transition-property: all;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  transition-delay: 0s;
  overflow: hidden;
}
.progress_bar .bar_behind .progress.color_default {
  background-color: var(--color-extended-blue, rgb(25, 104, 240));
}
.progress_bar .bar_behind .progress.color_positive {
  background-color: var(--color-extended-green, rgb(53, 222, 144));
}
.progress_bar .bar_behind .progress.color_information {
  background-color: var(--color-extended-yellow, rgb(251, 207, 35));
}
.progress_bar .bar_behind .progress.color_warning {
  background-color: var(--color-extended-red, rgb(230, 15, 15));
}
.progress_bar .bar_behind .progress .loading {
  position: absolute;
  left: -16px;
  width: calc(100% + 16px);
  height: 100%;
  background: rgb(255, 255, 255);
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 75%, rgba(0, 0, 0, 0.26) 75%);
  background-size: 4px;
  transform: skewX(-15deg);
  animation-name: load;
  animation-timing-function: linear;
  animation-duration: 0.5s;
  animation-iteration-count: infinite;
}

.typo_progress {
  color: var(--color-content-default, rgb(40, 40, 40));
}

@keyframes load {
  from {
    left: -16px;
  }
  to {
    left: 0;
  }
}