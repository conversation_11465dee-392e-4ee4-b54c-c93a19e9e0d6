/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
  margin: -4px;
  padding: 4px;
  width: 100%;
  position: relative;
  overflow: hidden;
  -webkit-transition: height 0.5s;
  -moz-transition: height 0.5s;
  transition: height 0.5s;
}

.nav_main {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px;
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  border: 1px solid transparent;
  overflow: hidden;
}
.nav_main--loading {
  cursor: wait;
}
.nav_main--disable {
  opacity: 0.5;
  cursor: not-allowed;
}
.nav_main:before {
  content: "";
  position: absolute;
  inset: 0;
}
.nav_main:hover:before {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.08;
}
.nav_main:active:before {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.16;
}
.nav_main:hover, .nav_main_active {
  border-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
}
.nav_main_active:before {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  border-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  opacity: 0.08;
}
.nav_main--disable:before, .nav_main--disable:hover {
  border-color: transparent;
  background-color: transparent;
}
.nav_main .icon-item {
  position: relative;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.nav_main .icon-item-active {
  color: var(--color-primary, rgb(30, 107, 241));
}
.nav_main_text {
  position: relative;
  display: flex;
  gap: 2px;
  flex-direction: column;
}
.nav_main_text .title-item {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.nav_main_text .title-item--loading {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.nav_main_text .subtitle-item {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.nav_main_text .subtitle-item--loading {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.nav_main_content {
  width: 100%;
  flex-shrink: 99999;
}
.nav_main_arrow {
  -webkit-transition: all ease 0.3s;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  transform: rotate(0deg);
}
.nav_main_arrow--disable {
  color: var(--color-content-disable, rgb(89, 89, 89));
}
.nav_main_arrow_active {
  transform: rotate(180deg);
}

.accordion {
  display: grid;
  grid-template-rows: 0fr;
  -webkit-transition: all ease 0.5s;
  -moz-transition: all ease 0.5s;
  transition: all ease 0.5s;
}
.accordion_open {
  grid-template-rows: 1fr;
  padding: 8px 0;
}
.accordion .container {
  overflow: hidden;
  position: relative;
  padding-left: 23px;
}
.accordion .container:before {
  content: "";
  position: absolute;
  width: 2px;
  inset: 0;
  left: 23px;
  top: 8px;
  bottom: 8px;
  border-radius: 8px;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  opacity: 0.8;
}
.accordion .container--disable:before {
  background-color: transparent;
}

.nav_tree_item {
  position: relative;
  display: flex;
  gap: 8px;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  padding-left: 22px;
}
.nav_tree_item--loading {
  cursor: wait;
}
.nav_tree_item--disable {
  opacity: 0.5;
  cursor: not-allowed;
}
.nav_tree_item--disable:before, .nav_tree_item--disable:hover {
  border-color: transparent;
  background-color: transparent;
}
.nav_tree_item .icon-item {
  position: relative;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.nav_tree_item .icon-item-active {
  color: var(--color-primary, rgb(30, 107, 241));
}
.nav_tree_item_content {
  position: relative;
  display: flex;
  flex-direction: column;
}
.nav_tree_item_slot {
  width: 100%;
  flex-shrink: 99999;
}
.nav_tree_item:before {
  content: "";
  position: absolute;
  width: 2px;
  inset: 0;
  top: 8px;
  bottom: 8px;
  border-radius: 8px;
  background-color: transparent;
  -webkit-transition: background-color ease 0.8s;
  -moz-transition: background-color ease 0.8s;
  transition: background-color ease 0.8s;
}
.nav_tree_item:hover:before {
  background-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
  -webkit-transition: background-color ease 0.3s;
  -moz-transition: background-color ease 0.3s;
  transition: background-color ease 0.3s;
}
.nav_tree_item_active:before {
  background-color: var(--color-primary, rgb(30, 107, 241));
  -webkit-transition: background-color ease 0.3s;
  -moz-transition: background-color ease 0.3s;
  transition: background-color ease 0.3s;
}
.nav_tree_item_active:hover:before {
  background-color: var(--color-primary, rgb(30, 107, 241));
  -webkit-transition: background-color ease 0.3s;
  -moz-transition: background-color ease 0.3s;
  transition: background-color ease 0.3s;
}
.nav_tree_item .icon-arrow {
  position: relative;
  -webkit-transition: all ease 0.3s;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  transform: rotate(0deg);
}
.nav_tree_item .icon-arrow-active {
  transform: rotate(180deg);
}
.nav_tree_item_button {
  padding: 8px;
  margin-left: 14px;
  border-radius: 8px;
  border: 1px solid transparent;
}
.nav_tree_item_button:before {
  left: -15px;
}
.nav_tree_item_button:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background-color: transparent;
}
.nav_tree_item_button:hover, .nav_tree_item_button_active {
  border-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
}
.nav_tree_item_button:hover:after, .nav_tree_item_button_active:after {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.08;
}
.nav_tree_item_button:active {
  border-color: var(--color-pressed, rgba(0, 0, 0, 0.16));
}
.nav_tree_item_button:active:after {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.16;
}

.focus {
  position: relative;
}
.focus:before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
}
.focus:focus-visible {
  outline: none;
}
.focus:focus-visible:before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}