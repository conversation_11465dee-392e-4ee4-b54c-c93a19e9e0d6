{"version": 3, "file": "nav-tree.js", "sourceRoot": "", "sources": ["../../../../src/components/nav-tree/nav-tree.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAgB,KAAK,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAS7G,MAAM,OAAO,OAAO;IALpB;QAMU,eAAU,GAAgC,IAAI,CAAC;QAI9C,uBAAkB,GAAa,KAAK,CAAC;QACrC,iBAAY,GAAI,IAAI,CAAC;QACrB,kBAAa,GAAY,IAAI,CAAC;QACvC;;WAEG;QACK,aAAQ,GAAe,QAAQ,CAAC;QACxC;;WAEG;QACqC,WAAM,GAAa,KAAK,CAAC;QACjE;;WAEG;QACK,SAAI,GAAY,IAAI,CAAC;QAK7B;;WAEG;QACK,kBAAa,GAAY,IAAI,CAAC;QACtC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QACjC;;WAEG;QACK,YAAO,GAAa,KAAK,CAAC;QAElC;;WAEG;QACK,YAAO,GAAa,KAAK,CAAC;QA4C1B,YAAO,GAAG,GAAS,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;KAiGH;IA1IC,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,MAAM;QACvB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;IAC9B,CAAC;IAGD,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAES,aAAa,CAAC,KAAK;;QAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBACzC,MAAA,IAAI,CAAC,UAAU,0CAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,oBAAoB;gBACzD,IAAI,CAAC,OAAO,CAAC,aAA4C,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9F,CAAC;IAQO,aAAa,CAAC,KAAK;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAC,OAAO;gBACvE,4DACE,KAAK,EAAE;wBACL,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,OAAO;qBACpC;oBAED,4DACE,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,KAAK,EAAE;4BACL,QAAQ,EAAE,IAAI;4BACd,eAAe,EAAE,IAAI,CAAC,MAAM;4BAC5B,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,OAAO;4BACnC,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,OAAO;yBACpC,eACU,IAAI,CAAC,QAAQ,gBACZ,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;wBAExE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CACd,2BAAqB,IAAI,EAAC,aAAa,GAAuB,CAC/D,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACd,gBACE,KAAK,EAAE;gCACL,CAAC,WAAW,CAAC,EAAE,IAAI;gCACnB,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,MAAM;6BAClC,EACD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EACf,KAAK,EAAC,SAAS,GACL,CACb,CAAC,CAAC,CAAC,CACF,EAAE,CACH;wBACD,4DAAK,KAAK,EAAC,eAAe;4BACvB,IAAI,CAAC,IAAI,IAAI,CACZ,iEACE,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EACtE,OAAO,EAAC,OAAO,EACf,GAAG,EAAC,MAAM,iBACE,OAAO,EACnB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,IAEvC,IAAI,CAAC,IAAI,CACD,CACZ;4BACA,IAAI,CAAC,aAAa,IAAI,CACrB,iEACE,KAAK,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAC5E,OAAO,EAAC,OAAO,iBACH,OAAO,EACnB,GAAG,EAAC,MAAM,EACV,MAAM,EAAE,KAAK,IAEZ,IAAI,CAAC,aAAa,CACV,CACZ,CACG;wBACN,4DAAK,KAAK,EAAC,kBAAkB;4BAC3B,6DAAM,IAAI,EAAC,gBAAgB,GAAQ,CAC/B;wBACL,IAAI,CAAC,YAAY,IAAI,CACpB,iEACE,IAAI,EAAC,YAAY,EACjB,KAAK,EAAE;gCACL,CAAC,gBAAgB,CAAC,EAAE,IAAI;gCACxB,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,MAAM;gCACtC,CAAC,yBAAyB,CAAC,EAAE,IAAI,CAAC,OAAO;6BAC1C,GACS,CACb,CACG,CACF,CACF;YACN,4DACE,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY;iBACjD;gBAED,4DAAK,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;oBACvE,8DAAa,CACT,CACF,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, Element, State, Prop, Method, Event, EventEmitter, Watch, h } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTree {\n  private itemsGroup?: HTMLBdsNavTreeGroupElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  @State() numberElement?: number = null;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * A prop for make the nav open.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * When de open or close of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    if (!this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    if (value) {\n      if (this.itemsGroup.collapse == 'single') {\n        this.itemsGroup?.closeAll(this.numberElement);\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.itemsGroup =\n      this.element.parentElement.tagName == 'BDS-NAV-TREE-GROUP' &&\n      (this.element.parentElement as HTMLBdsNavTreeGroupElement);\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item') === null ? false : true;\n  }\n\n  private handler = (): void => {\n    if (!this.loading && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter' && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              [`nav_main--disable`]: this.disable,\n            }}\n          >\n            <div\n              onClick={this.handler}\n              class={{\n                nav_main: true,\n                nav_main_active: this.isOpen,\n                [`nav_main--loading`]: this.loading,\n                [`nav_main--disable`]: this.disable,\n              }}\n              data-test={this.dataTest}\n              aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n            >\n              {this.loading ? (\n                <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n              ) : this.icon ? (\n                <bds-icon\n                  class={{\n                    [`icon-item`]: true,\n                    [`icon-item-active`]: this.isOpen,\n                  }}\n                  size=\"medium\"\n                  name={this.icon}\n                  color=\"inherit\"\n                  theme=\"outline\"\n                ></bds-icon>\n              ) : (\n                ''\n              )}\n              <div class=\"nav_main_text\">\n                {this.text && (\n                  <bds-typo\n                    class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                    variant=\"fs-14\"\n                    tag=\"span\"\n                    line-height=\"small\"\n                    bold={this.isOpen ? 'bold' : 'semi-bold'}\n                  >\n                    {this.text}\n                  </bds-typo>\n                )}\n                {this.secondaryText && (\n                  <bds-typo\n                    class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                    variant=\"fs-12\"\n                    line-height=\"small\"\n                    tag=\"span\"\n                    margin={false}\n                  >\n                    {this.secondaryText}\n                  </bds-typo>\n                )}\n              </div>\n              <div class=\"nav_main_content\">\n                <slot name=\"header-content\"></slot>\n              </div>\n              {this.navTreeChild && (\n                <bds-icon\n                  name=\"arrow-down\"\n                  class={{\n                    [`nav_main_arrow`]: true,\n                    [`nav_main_arrow_active`]: this.isOpen,\n                    [`nav_main_arrow--loading`]: this.loading,\n                  }}\n                ></bds-icon>\n              )}\n            </div>\n          </div>\n        </div>\n        <div\n          class={{\n            accordion: true,\n            accordion_open: this.isOpen && this.navTreeChild,\n          }}\n        >\n          <div class={{ ['container']: true, [`container--disable`]: this.disable }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}