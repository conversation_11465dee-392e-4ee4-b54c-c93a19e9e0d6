{"version": 3, "file": "nav-tree-item.js", "sourceRoot": "", "sources": ["../../../../src/components/nav-tree/nav-tree-item.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AAStG,MAAM,OAAO,WAAW;IALxB;QAMU,kBAAa,GAAuD,IAAI,CAAC;QACzE,iBAAY,GAA+B,IAAI,CAAC;QAChD,iBAAY,GAA2C,IAAI,CAAC;QAGpE;;WAEG;QACK,aAAQ,GAAe,QAAQ,CAAC;QACxC;;WAEG;QACK,SAAI,GAAY,IAAI,CAAC;QAK7B;;WAEG;QACK,kBAAa,GAAY,IAAI,CAAC;QACtC;;WAEG;QACqC,WAAM,GAAa,KAAK,CAAC;QACjE;;WAEG;QACK,YAAO,GAAa,KAAK,CAAC;QAElC;;WAEG;QACK,YAAO,GAAa,KAAK,CAAC;QAClC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAgCzB,YAAO,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACvF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBAClD,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO;4BAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;oBAChF,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;KA+FH;IAjIC,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAC7B,CAAC;IAGS,aAAa,CAAC,KAAK;QAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,2DAA2D;IAC7D,CAAC;IAED,iBAAiB;;QACf,IAAI,CAAC,aAAa;YAChB,CAAC,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,aAAa,0CAAE,OAAO,KAAI,cAAc,IAAK,IAAI,CAAC,OAAO,CAAC,aAAuC,CAAC;gBAChH,CAAC,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,aAAa,0CAAE,OAAO,KAAI,mBAAmB,IAAK,IAAI,CAAC,OAAO,CAAC,aAA2C,CAAC;gBACzH,IAAI,CAAC;QACP,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;IACtE,CAAC;IACD,mBAAmB;QACjB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACrD,mBAAmB,CACqB,CAAC;QAC7C,CAAC;IACH,CAAC;IAaO,aAAa,CAAC,KAAK;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAC,OAAO;gBACvE,4DACE,KAAK,EAAE;wBACL,aAAa,EAAE,IAAI;wBACnB,oBAAoB,EAAE,IAAI,CAAC,MAAM;wBACjC,oBAAoB,EAAE,CAAC,IAAI,CAAC,YAAY;wBACxC,2BAA2B,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM;wBAC9D,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,OAAO;wBACxC,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,OAAO;qBACzC,EACD,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,eAClB,IAAI,CAAC,QAAQ,gBACZ,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;oBAExE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CACd,2BAAqB,IAAI,EAAC,aAAa,GAAuB,CAC/D,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACd,gBACE,KAAK,EAAE;4BACL,CAAC,WAAW,CAAC,EAAE,IAAI;4BACnB,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,MAAM;yBAClC,EACD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EACf,KAAK,EAAC,SAAS,GACL,CACb,CAAC,CAAC,CAAC,CACF,EAAE,CACH;oBACD,4DAAK,KAAK,EAAC,uBAAuB;wBAC/B,IAAI,CAAC,IAAI,IAAI,CACZ,iEACE,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EACtE,OAAO,EAAC,OAAO,EACf,GAAG,EAAC,MAAM,iBACE,OAAO,EACnB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,IAEvC,IAAI,CAAC,IAAI,CACD,CACZ;wBACA,IAAI,CAAC,aAAa,IAAI,CACrB,iEACE,KAAK,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAC5E,OAAO,EAAC,OAAO,iBACH,OAAO,EACnB,GAAG,EAAC,MAAM,EACV,MAAM,EAAE,KAAK,IAEZ,IAAI,CAAC,aAAa,CACV,CACZ,CACG;oBACN,4DAAK,KAAK,EAAC,oBAAoB;wBAC7B,6DAAM,IAAI,EAAC,gBAAgB,GAAQ,CAC/B;oBACL,IAAI,CAAC,YAAY,IAAI,CACpB,iEACE,KAAK,EAAE;4BACL,CAAC,gBAAgB,CAAC,EAAE,IAAI;4BACxB,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,MAAM;4BACtC,CAAC,yBAAyB,CAAC,EAAE,IAAI,CAAC,OAAO;yBAC1C,EACD,IAAI,EAAC,YAAY,GACP,CACb,CACG,CACF;YACL,IAAI,CAAC,YAAY,IAAI,CACpB,4DACE,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,IAAI,CAAC,MAAM;iBAC5B;gBAED,4DAAK,KAAK,EAAC,WAAW;oBACpB,8DAAa,CACT,CACF,CACP,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Element, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree-item',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTreeItem {\n  private navTreeParent?: HTMLBdsNavTreeElement | HTMLBdsNavTreeItemElement = null;\n  private navTreeChild?: HTMLBdsNavTreeItemElement = null;\n  private itensElement?: NodeListOf<HTMLBdsNavTreeItemElement> = null;\n\n  @Element() private element: HTMLElement;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * When de activation of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    // if (this.navTreeChild) this.navTreeChild.isOpen = value;\n  }\n\n  componentWillLoad() {\n    this.navTreeParent =\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE' && (this.element.parentElement as HTMLBdsNavTreeElement)) ||\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE-ITEM' && (this.element.parentElement as HTMLBdsNavTreeItemElement)) ||\n      null;\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item');\n  }\n  componentWillRender() {\n    if (this.navTreeParent) {\n      this.itensElement = this.navTreeParent.querySelectorAll(\n        'bds-nav-tree-item',\n      ) as NodeListOf<HTMLBdsNavTreeItemElement>;\n    }\n  }\n\n  private handler = () => {\n    if (!this.loading && !this.disable) {\n      if (this.navTreeParent && this.navTreeParent.collapse == 'single' && this.itensElement) {\n        for (let i = 0; i < this.itensElement.length; i++) {\n          if (this.itensElement[i] != this.element) this.itensElement[i].isOpen = false;\n        }\n      }\n      this.toggle();\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.handler();\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              nav_tree_item: true,\n              nav_tree_item_active: this.isOpen,\n              nav_tree_item_button: !this.navTreeChild,\n              nav_tree_item_button_active: !this.navTreeChild && this.isOpen,\n              [`nav_tree_item--loading`]: this.loading,\n              [`nav_tree_item--disable`]: this.disable,\n            }}\n            onClick={() => this.handler()}\n            data-test={this.dataTest}\n            aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n          >\n            {this.loading ? (\n              <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n            ) : this.icon ? (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.isOpen,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme=\"outline\"\n              ></bds-icon>\n            ) : (\n              ''\n            )}\n            <div class=\"nav_tree_item_content\">\n              {this.text && (\n                <bds-typo\n                  class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                  variant=\"fs-14\"\n                  tag=\"span\"\n                  line-height=\"small\"\n                  bold={this.isOpen ? 'bold' : 'semi-bold'}\n                >\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo\n                  class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                  variant=\"fs-12\"\n                  line-height=\"small\"\n                  tag=\"span\"\n                  margin={false}\n                >\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n            <div class=\"nav_tree_item_slot\">\n              <slot name=\"header-content\"></slot>\n            </div>\n            {this.navTreeChild && (\n              <bds-icon\n                class={{\n                  [`nav_main_arrow`]: true,\n                  [`nav_main_arrow_active`]: this.isOpen,\n                  [`nav_main_arrow--loading`]: this.loading,\n                }}\n                name=\"arrow-down\"\n              ></bds-icon>\n            )}\n          </div>\n        </div>\n        {this.navTreeChild && (\n          <div\n            class={{\n              accordion: true,\n              accordion_open: this.isOpen,\n            }}\n          >\n            <div class=\"container\">\n              <slot></slot>\n            </div>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"]}