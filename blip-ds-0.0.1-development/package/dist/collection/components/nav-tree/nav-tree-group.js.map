{"version": 3, "file": "nav-tree-group.js", "sourceRoot": "", "sources": ["../../../../src/components/nav-tree/nav-tree-group.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAStG,MAAM,OAAO,YAAY;IALzB;QAMU,iBAAY,GAA6C,IAAI,CAAC;QAG7D,uBAAkB,GAAa,KAAK,CAAC;QACrC,iBAAY,GAAI,IAAI,CAAC;QAC9B;;WAEG;QACK,aAAQ,GAAe,QAAQ,CAAC;KA2CzC;IAtCC,mBAAmB;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAA4C,CAAC;QACjH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,QAAQ,CAAC,SAAU;QACvB,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAChC,IAAI,SAAS,IAAI,CAAC;oBAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,OAAO,CAAC,SAAU;QACtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAChC,IAAI,SAAS,IAAI,CAAC;oBAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, Element, State, Prop, h, EventEmitter, Event, Method } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree-group',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTreeGroup {\n  private itemsElement?: HTMLCollectionOf<HTMLBdsNavTreeElement> = null;\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  /**\n   * Collapse. Used to set mode of iteraction of componente when navigate with menu. You can choose a option single or multiple.\n   */\n  @Prop() collapse?: collapses = 'single';\n\n  @Event() bdsNavTreeGroupCloseAll?: EventEmitter;\n  @Event() bdsNavTreeGroupOpenAll?: EventEmitter;\n\n  componentWillRender() {\n    this.itemsElement = this.element.getElementsByTagName('bds-nav-tree') as HTMLCollectionOf<HTMLBdsNavTreeElement>;\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      this.itemsElement[i].reciveNumber(i);\n    }\n  }\n\n  @Method()\n  async closeAll(actNumber?) {\n    this.bdsNavTreeGroupCloseAll.emit();\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.itemsElement[i].close();\n      } else {\n        this.itemsElement[i].close();\n      }\n    }\n  }\n\n  @Method()\n  async openAll(actNumber?) {\n    this.bdsNavTreeGroupOpenAll.emit();\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.itemsElement[i].open();\n      } else {\n        this.itemsElement[i].open();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}