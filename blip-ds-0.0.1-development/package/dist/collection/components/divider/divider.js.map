{"version": 3, "file": "divider.js", "sourceRoot": "", "sources": ["../../../../src/components/divider/divider.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAOzD,MAAM,OAAO,UAAU;IALvB;QAOE;;WAEG;QACK,cAAS,GAAkC,OAAO,CAAC;QAE3D;;WAEG;QACK,gBAAW,GAA8B,YAAY,CAAC;QAE9D;;WAEG;QACK,UAAK,GAA4C,WAAW,CAAC;KAWtE;IATC,MAAM;QACJ,MAAM,gBAAgB,GAAG,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC;QAEtF,OAAO,CACP,EAAC,IAAI,qDAAC,KAAK,EAAE,gBAAgB;YAC3B,2DAAI,KAAK,EAAE,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,KAAK,EAAE,GAAI,CACrE,CACN,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Prop, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-divider',\n  styleUrl: 'divider.scss',\n  shadow: true,\n})\nexport class BdsDivider {\n\n  /**\n   * O tipo de estilo da linha: sólida, pontil<PERSON>a, tracejada\n   */\n  @Prop() styleType: 'solid' | 'dotted' | 'dashed' = 'solid';\n\n  /**\n   * Define se o divider deve ser exibido horizontalmente ou verticalmente\n   */\n  @Prop() orientation: 'horizontal' | 'vertical' = 'horizontal';\n\n  /**\n   * Cor da linha, aceitando qualquer valor válido em CSS (hex, rgb, nome da cor)\n   */\n  @Prop() color: 'divider-1' | 'divider-2' | 'divider-3' = 'divider-1';\n\n  render() {\n    const orientationClass = `${this.orientation} ${this.styleType} color-${this.color} `;\n\n    return (\n    <Host class={orientationClass}>\n      <hr class={`${this.orientation} ${this.styleType} color-${this.color}`} />\n    </Host>\n    );\n  }\n}\n"]}