{"version": 3, "file": "illustration.js", "sourceRoot": "", "sources": ["../../../../src/components/illustration/illustration.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEhE,OAAO,WAAW,MAAM,uBAAuB,CAAC;AAQhD,MAAM,OAAO,eAAe;IAN5B;QASE;;WAEG;QACK,SAAI,GAAqB,SAAS,CAAC;QAW3C;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAMjC,+DAA+D;QAC/D,2BAAsB,GAAG,GAAG,EAAE;YAC5B,MAAM,aAAa,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAC/E,MAAM,MAAM,GAAG,4CAA4C,aAAa,6BAA6B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC;YACnI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC9B,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;YACzE,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC;KAuBH;IApCC,iBAAiB;QACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAaD,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,IAAI,EAAC,KAAK,EACV,KAAK,EAAE;gBACL,kBAAkB,EAAE,IAAI;aACzB,IAEA,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC1B,WACE,SAAS,EAAE,KAAK,EAChB,GAAG,EAAE,6BAA6B,IAAI,CAAC,mBAAmB,EAAE,EAC5D,GAAG,EAAE,IAAI,CAAC,GAAG,eACF,IAAI,CAAC,QAAQ,GACxB,CACH,CAAC,CAAC,CAAC,CACF,WAAK,KAAK,EAAC,SAAS,eAAY,IAAI,CAAC,QAAQ,GAAQ,CACtD,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Prop, State } from '@stencil/core';\nimport { IllustrationType } from './illustration-interface';\nimport packageJson from '../../../package.json';\n\n@Component({\n  tag: 'bds-illustration',\n  assetsDirs: ['svg'],\n  styleUrl: 'illustration.scss',\n  shadow: true,\n})\nexport class BdsIllustration {\n  @State() private IllustrationContent?: string;\n\n  /**\n   * Specifies the type to use. Can be: 'default'.\n   */\n  @Prop() type: IllustrationType = 'default';\n  /**\n   * Specifies the name of illustration. Verify the names on illustration tokens.\n   */\n  @Prop() name: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setIllustrationContent();\n  }\n\n  /**Function to map the svg and call the \"formatSvg\" function */\n  setIllustrationContent = () => {\n    const tokensVersion = packageJson.dependencies['blip-tokens'].replace('^', '');\n    const apiUrl = `https://cdn.jsdelivr.net/npm/blip-tokens@${tokensVersion}/build/json/illustrations/${this.type}/${this.name}.json`;\n    fetch(apiUrl).then((response) =>\n      response.json().then((data) => {\n        this.IllustrationContent = data[`asset-${this.type}-${this.name}-svg`];\n      }),\n    );\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-illustration': true,\n        }}\n      >\n        {this.IllustrationContent ? (\n          <img\n            draggable={false}\n            src={`data:image/svg+xml;base64,${this.IllustrationContent}`}\n            alt={this.alt}\n            data-test={this.dataTest}\n          />\n        ) : (\n          <div class=\"default\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"]}