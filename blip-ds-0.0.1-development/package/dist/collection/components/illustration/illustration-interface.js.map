{"version": 3, "file": "illustration-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/illustration/illustration-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export type IllustrationType =\n  | 'default'\n  | 'screens'\n  | 'blip-solid'\n  | 'blip-outline'\n  | 'logo-integration'\n  | 'empty-states'\n  | 'brand'\n  | 'segmented'\n  | 'smartphone'\n  | 'spots';\n"]}