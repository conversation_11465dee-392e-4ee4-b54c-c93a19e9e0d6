{"version": 3, "file": "accordion.js", "sourceRoot": "", "sources": ["../../../../src/components/accordion/accordion.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAgB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAOvG,MAAM,OAAO,SAAS;IALtB;QAMU,aAAQ,GAAkC,IAAI,CAAC;QAC/C,eAAU,GAAmC,IAAI,CAAC;QAClD,cAAS,GAAiC,IAAI,CAAC;QAI9C,WAAM,GAAa,KAAK,CAAC;QACzB,kBAAa,GAAY,IAAI,CAAC;QAC9B,cAAS,GAAa,KAAK,CAAC;QAMZ,cAAS,GAAa,KAAK,CAAC;QAC5B,YAAO,GAAa,IAAI,CAAC;KAmFnD;IAhFC,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAGD,KAAK,CAAC,IAAI;;QACR,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,EAAE,CAAC;QACxB,MAAA,IAAI,CAAC,SAAS,0CAAE,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,KAAK;;QACT,MAAA,IAAI,CAAC,UAAU,0CAAE,KAAK,EAAE,CAAC;QACzB,MAAA,IAAI,CAAC,SAAS,0CAAE,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAGD,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,MAAM;QACvB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;IAC9B,CAAC;IAGD,aAAa,CAAC,KAAK;;QACjB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBACnE,MAAA,IAAI,CAAC,QAAQ,0CAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9C,CAAC;YACD,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,EAAE,CAAC;YACxB,MAAA,IAAI,CAAC,SAAS,0CAAE,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,MAAA,IAAI,CAAC,UAAU,0CAAE,KAAK,EAAE,CAAC;YACzB,MAAA,IAAI,CAAC,SAAS,0CAAE,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IAGD,cAAc,CAAC,QAAiB;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC,CAAC;QACtG,IAAI,aAAa,EAAE,CAAC;YACjB,aAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,QAAQ;YACX,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,qBAAqB;gBAC1D,IAAI,CAAC,OAAO,CAAC,aAA8C,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAkC,CAAC;QACtG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC,CAAC;QAEjG,6CAA6C;QAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC,CAAC;QACtG,IAAI,aAAa,EAAE,CAAC;YACjB,aAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,4DAAK,KAAK,EAAC,iBAAiB;YAC1B,8DAAa,CACT,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Element, Event, EventEmitter, h, Method, Prop, State, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class Accordion {\n  private accGroup?: HTMLBdsAccordionGroupElement = null;\n  private accheaders?: HTMLBdsAccordionHeaderElement = null;\n  private accBodies?: HTMLBdsAccordionBodyElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n  @State() numberElement?: number = null;\n  @State() condition?: boolean = false;\n\n  @Event() bdsToggle?: EventEmitter;\n  @Event() bdsAccordionOpen?: EventEmitter;\n  @Event() bdsAccordionClose?: EventEmitter;\n\n  @Prop({ reflect: true }) startOpen?: boolean = false;\n  @Prop({ reflect: true }) divisor?: boolean = true;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n    this.bdsToggle.emit({ value: this.isOpen });\n  }\n\n  @Method()\n  async open() {\n    this.accheaders?.open();\n    this.accBodies?.open();\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.accheaders?.close();\n    this.accBodies?.close();\n    this.isOpen = false;\n  }\n\n  @Method()\n  async notStart() {\n    this.startOpen = false;\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(value): void {\n    if (value) {\n      if (this.accGroup.collapse == 'single' && this.condition === false) {\n        this.accGroup?.closeAll(this.numberElement);\n      }\n      this.accheaders?.open();\n      this.accBodies?.open();\n      this.bdsAccordionOpen.emit();\n    } else {\n      this.accheaders?.close();\n      this.accBodies?.close();\n      this.bdsAccordionClose.emit();\n    }\n    this.condition = false;\n  }\n\n  @Watch('divisor')\n  divisorChanged(newValue: boolean): void {\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(newValue);\n    }\n  }\n\n  componentWillLoad() {\n    this.accGroup =\n      this.element.parentElement.tagName == 'BDS-ACCORDION-GROUP' &&\n      (this.element.parentElement as HTMLBdsAccordionGroupElement);\n    this.accheaders = this.element.querySelector('bds-accordion-header') as HTMLBdsAccordionHeaderElement;\n    this.accBodies = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n\n    // Passar a prop divisor para o AccordionBody\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(this.divisor);\n    }\n\n    if (this.startOpen === true) {\n      this.condition = true;\n      this.isOpen = true;\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"accordion_group\">\n        <slot></slot>\n      </div>\n    );\n  }\n}\n"]}