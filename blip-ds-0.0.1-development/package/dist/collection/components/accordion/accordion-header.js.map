{"version": 3, "file": "accordion-header.js", "sourceRoot": "", "sources": ["../../../../src/components/accordion/accordion-header.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAO3E,MAAM,OAAO,eAAe;IAL5B;QAMU,qBAAgB,GAA6B,IAAI,CAAC;QAIjD,WAAM,GAAa,KAAK,CAAC;QAEzB,oBAAe,GAAa,KAAK,CAAC;QAElC,kBAAa,GAAY,IAAI,CAAC;QAEvC;;WAEG;QACK,mBAAc,GAAY,IAAI,CAAC;QAEvC;;WAEG;QACK,SAAI,GAAY,IAAI,CAAC;QAE7B;;WAEG;QACK,eAAU,GAAY,IAAI,CAAC;QAEnC;;WAEG;QACK,gBAAW,GAAY,IAAI,CAAC;QAEpC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAqBzB,iBAAY,GAAG,GAAS,EAAE;;YAChC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAA,IAAI,CAAC,gBAAgB,0CAAE,KAAK,EAAE,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,MAAA,IAAI,CAAC,gBAAgB,0CAAE,IAAI,EAAE,CAAC;YAChC,CAAC;QACH,CAAC,CAAC;KAyCH;IAjEC,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAC7B,CAAC;IAGD,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAwC,CAAC;IAChF,CAAC;IAUD,aAAa,CAAC,KAAK;;QACjB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAA,IAAI,CAAC,gBAAgB,0CAAE,KAAK,EAAE,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,MAAA,IAAI,CAAC,gBAAgB,0CAAE,IAAI,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,4DAAK,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,eAAa,IAAI,CAAC,QAAQ;YACzF,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CACrC,kBAAY,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAC,aAAa,GAAc,CACjG,CAAC,CAAC,CAAC,CACF,IAAI,CAAC,IAAI,IAAI,gBAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,GAAY,CACnF;YACA,IAAI,CAAC,cAAc,IAAI,CACtB,iEAAU,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,iBAAa,QAAQ,IACvD,IAAI,CAAC,cAAc,CACX,CACZ;YACD,8DAAa;YACb,iEACE,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,IAAI,CAAC,MAAM;oBAC9B,kBAAkB,EAAE,IAAI,CAAC,eAAe;iBACzC,EACD,IAAI,EAAC,SAAS,EACd,IAAI,EAAC,YAAY,EACjB,KAAK,EAAC,SAAS,EACf,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAC9B,CACR,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, State, h, Prop, Element, Method } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-header',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionHeader {\n  private accordionElement?: HTMLBdsAccordionElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n\n  @State() btToggleIsfocus?: boolean = false;\n\n  @State() numberElement?: number = null;\n\n  /**\n   * Accordion Title. Used to add title in header accordion.\n   */\n  @Prop() accordionTitle?: string = null;\n\n  /**\n   * Icon. Used to add icon in header accordion.\n   */\n  @Prop() icon?: string = null;\n\n  /**\n   * Avatar Name. Used to add avatar in header accordion.\n   */\n  @Prop() avatarName?: string = null;\n\n  /**\n   * Avatar Thumb. Used to add avatar in header accordion.\n   */\n  @Prop() avatarThumb?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  componentWillRender() {\n    this.accordionElement = this.element.parentElement as HTMLBdsAccordionElement;\n  }\n\n  private toggleHeader = (): void => {\n    if (this.isOpen) {\n      this.accordionElement?.close();\n    } else {\n      this.accordionElement?.open();\n    }\n  };\n\n  handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      if (this.isOpen) {\n        this.accordionElement?.close();\n      } else {\n        this.accordionElement?.open();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <div onClick={this.toggleHeader} class={{ accordion_header: true }} data-test={this.dataTest}>\n        {this.avatarName || this.avatarThumb ? (\n          <bds-avatar name={this.avatarName} thumbnail={this.avatarThumb} size=\"extra-small\"></bds-avatar>\n        ) : (\n          this.icon && <bds-icon size=\"x-large\" name={this.icon} color=\"inherit\"></bds-icon>\n        )}\n        {this.accordionTitle && (\n          <bds-typo bold=\"bold\" variant=\"fs-16\" line-height=\"double\">\n            {this.accordionTitle}\n          </bds-typo>\n        )}\n        <slot></slot>\n        <bds-icon\n          class={{\n            accButton: true,\n            accButton__isopen: this.isOpen,\n            accButton__isfocus: this.btToggleIsfocus,\n          }}\n          size=\"x-large\"\n          name=\"arrow-down\"\n          color=\"inherit\"\n          tabindex=\"0\"\n          onKeyDown={this.handleKeyDown.bind(this)}\n        ></bds-icon>\n      </div>\n    );\n  }\n}\n"]}