{"version": 3, "file": "accordion-group.js", "sourceRoot": "", "sources": ["../../../../src/components/accordion/accordion-group.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAgB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAShG,MAAM,OAAO,cAAc;IAL3B;QAMU,sBAAiB,GAA+C,IAAI,CAAC;QAGrE,aAAQ,GAAe,QAAQ,CAAC;QAChC,YAAO,GAAa,IAAI,CAAC;KAuDlC;IAjDC,KAAK,CAAC,QAAQ,CAAC,SAAU;QACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAChC,IAAI,SAAS,IAAI,CAAC;oBAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,OAAO,CAAC,SAAU;QACtB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAChC,IAAI,SAAS,IAAI,CAAC;oBAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAGD,cAAc,CAAC,QAAiB;QAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAE,8BAA8B;YAC/E,CAAC;QACH,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACxD,eAAe,CAC6B,CAAC;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACnD,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,CACL,4DAAK,KAAK,EAAC,iBAAiB;YAC1B,8DAAa,CACT,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Element, Prop, EventEmitter, Event, Method, Watch } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-accordion-group',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionGroup {\n  private accordionsElement?: HTMLCollectionOf<HTMLBdsAccordionElement> = null;\n\n  @Element() private element: HTMLElement;\n  @Prop() collapse?: collapses = 'single';\n  @Prop() divisor?: boolean = true;\n\n  @Event() bdsAccordionCloseAll?: EventEmitter;\n  @Event() bdsAccordionOpenAll?: EventEmitter;\n\n  @Method()\n  async closeAll(actNumber?) {\n    this.bdsAccordionCloseAll.emit();\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.accordionsElement[i].close();\n      } else {\n        this.accordionsElement[i].close();\n      }\n    }\n  }\n\n  @Method()\n  async openAll(actNumber?) {\n    this.bdsAccordionOpenAll.emit();\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.accordionsElement[i].open();\n      } else {\n        this.accordionsElement[i].open();\n      }\n    }\n  }\n\n  @Watch('divisor')\n  divisorChanged(newValue: boolean): void {\n    if (this.accordionsElement) {\n      for (let i = 0; i < this.accordionsElement.length; i++) {\n        this.accordionsElement[i].divisor = newValue;  // Atualiza divisor nos filhos\n      }\n    }\n  }\n\n  componentWillRender() {\n    this.accordionsElement = this.element.getElementsByTagName(\n      'bds-accordion',\n    ) as HTMLCollectionOf<HTMLBdsAccordionElement>;\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      this.accordionsElement[i].reciveNumber(i);\n      this.accordionsElement[i].divisor = this.divisor;\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"accordion_group\">\n        <slot></slot>\n      </div>\n    );\n  }\n}\n"]}