{"version": 3, "file": "accordion-body.js", "sourceRoot": "", "sources": ["../../../../src/components/accordion/accordion-body.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAOzE,MAAM,OAAO,aAAa;IAL1B;QAMU,cAAS,GAAiB,IAAI,CAAC;QAE9B,WAAM,GAAa,KAAK,CAAC;QACzB,uBAAkB,GAAa,KAAK,CAAC;QAErC,kBAAa,GAAY,IAAI,CAAC;QAC9B,eAAU,GAAa,IAAI,CAAC;QAErC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAkCzB,iBAAY,GAAG,CAAC,EAAe,EAAQ,EAAE;YAC/C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC,CAAC;KAmBH;IApDC,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAC7B,CAAC;IAGD,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAGD,KAAK,CAAC,OAAO,CAAC,KAAK;QACjB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1B,CAAC;IAGD,aAAa;;QACX,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,YAAY,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACjC,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAClC,CAAC;IACH,CAAC;IAMD,MAAM;QACJ,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,cAAc,EAAE,IAAI;gBACpB,sBAAsB,EAAE,IAAI,CAAC,UAAU;gBACvC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB;aAC/C,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,IAAI,EAAE,eACnC,IAAI,CAAC,QAAQ;YAExB,4DAAK,KAAK,EAAC,WAAW,EAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvD,8DAAa,CACT,CACF,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, State, h, Method, Prop, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-body',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionBody {\n  private container?: HTMLElement = null;\n\n  @State() isOpen?: boolean = false;\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() heightContainer?: number;\n  @State() numberElement?: number = null;\n  @State() hasDivisor?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  @Method()\n  async divisor(valor) {\n    this.hasDivisor = valor;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(): void {\n    this.heightContainer = this.isOpen ? (this.container?.offsetHeight || 0) : 0;\n    if (this.isOpen) {\n      setTimeout(() => {\n        this.isOpenAftAnimation = true;\n      }, 500);\n    } else {\n      this.isOpenAftAnimation = false;\n    }\n  }\n\n  private refContainer = (el: HTMLElement): void => {\n    this.container = el;\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          accordion_body: true,\n          accordion_body_divisor: this.hasDivisor,\n          accordion_body_isOpen: this.isOpenAftAnimation,\n        }}\n        style={{ height: `${this.heightContainer}px` }}\n        data-test={this.dataTest}\n      >\n        <div class=\"container\" ref={(el) => this.refContainer(el)}>\n          <slot></slot>\n        </div>\n      </div>\n    );\n  }\n}\n"]}