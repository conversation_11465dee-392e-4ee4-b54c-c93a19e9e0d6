/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
  width: 100%;
}

:host(.list_item_content) {
  display: flex;
  width: fit-content;
}

.list_item {
  display: flex;
  gap: 16px;
  align-items: center;
}
.list_item_tall {
  padding: 16px;
}
.list_item_standard {
  padding: 8px 16px;
}
.list_item_short {
  padding: 8px;
}
.list_item .input_list {
  position: relative;
}
.list_item .avatar-item {
  position: relative;
  display: block;
}
.list_item .icon-item {
  position: relative;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.list_item .icon-item-active {
  color: var(--color-primary, rgb(30, 107, 241));
}
.list_item .grow-up {
  position: relative;
  flex-grow: 2;
}
.list_item .content-slot {
  display: flex;
  align-items: center;
  gap: 8px;
}
.list_item .content-item {
  position: relative;
  display: flex;
  gap: 2px;
  flex-direction: column;
}
.list_item .content-item .title-item {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.list_item .content-item .subtitle-item {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.list_item .content-area {
  position: relative;
  flex-grow: 2;
}
.list_item .content-area .internal-chips,
.list_item .content-area ::slotted(*) {
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.list_item .action-area {
  position: relative;
}
.list_item .action-area .internal-actions-buttons,
.list_item .action-area ::slotted(*) {
  display: flex;
  flex-direction: row;
  color: var(--color-content-default, rgb(40, 40, 40));
}
.list_item .icon-arrow {
  -webkit-transition: all ease 0.3s;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  transform: rotate(0deg);
}
.list_item .icon-arrow-active {
  transform: rotate(180deg);
}

.border_radius {
  border-radius: 8px;
}
.border_radius:before, .border_radius:after, .border_radius .active {
  border-radius: 8px;
}

.active {
  position: absolute;
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.08;
  inset: 0;
}

.clickable {
  position: relative;
  cursor: pointer;
  gap: 8px;
}
.clickable:before {
  content: "";
  position: absolute;
  inset: 0;
}
.clickable:hover:before {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.08;
}
.clickable:active:before {
  background-color: var(--color-content-default, rgb(40, 40, 40));
  opacity: 0.16;
}