{"version": 3, "file": "list.js", "sourceRoot": "", "sources": ["../../../../src/components/list/list.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AAUrG,MAAM,OAAO,IAAI;IALjB;QAMU,oBAAe,GAAmF,IAAI,CAAC;QAK/G;;WAEG;QACK,aAAQ,GAAc,IAAI,CAAC;QAmF3B,kBAAa,GAAG,CAAC,KAAkB,EAAQ,EAAE;YACnD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;YACzB,IAAI,MAAM,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC/B,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;oBAC3B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;gBACtB,CAAC;YACH,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAsDM,0BAAqB,GAAG,CAAC,KAAkB,EAAQ,EAAE;YAC3D,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC;KAoCH;IA9JC,iBAAiB;QACf,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,kBAAkB;QAChB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAGD,WAAW;QACT,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAGD,YAAY,CAAC,KAAa;QACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAGD,mBAAmB;QACjB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACnF,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACtD,eAAe,CAC4B,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACjD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,KAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5G,CAAC;IACH,CAAC;IAEO,UAAU;QAChB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAiBO,gBAAgB,CAAC,QAAQ;;QAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,CAAC;QAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACtC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,SAAS,GAAG;oBAChB,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;oBACtB,IAAI,EAAE,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,IAAI;oBACrB,aAAa,EAAE,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,aAAa;oBACvC,UAAU,EAAE,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,UAAU;oBACjC,eAAe,EAAE,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,eAAe;oBAC3C,QAAQ,EAAE,MAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,QAAQ;iBAC9B,CAAC;gBACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;QACvC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,KAAK;aACjB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aAC9B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACd,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;YAChB,aAAa,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa;YAClC,UAAU,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU;YAC5B,eAAe,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe;YACtC,QAAQ,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ;SACzB,CAAC,CAAC,CAAC;QACN,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEO,iBAAiB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;QAC7E,MAAM,MAAM,GAAG,KAAK;aACjB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aAC9B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACd,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI;YAChB,aAAa,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa;YAClC,UAAU,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU;YAC5B,eAAe,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe;YACtC,QAAQ,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ;SACzB,CAAC,CAAC,CAAC;QACN,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAOD,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DACE,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI;iBACX,IAEA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CACnB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CACnC,qBACE,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,eACJ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,oBACxC,IAAI,CAAC,aAAa,iBACrB,IAAI,CAAC,UAAU,sBACV,IAAI,CAAC,eAAe,EACtC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EAC5C,sBAAsB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,EAC9D,QAAQ,EAAE,IAAI,CAAC,QAAQ,GACR,CAClB,CAAC,CACH,CAAC,CAAC,CAAC,CACF,eAAa,CACd,CACG,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { Data } from './list-interface';\n\nexport type TypeList = 'checkbox' | 'radio' | 'switch' | 'default';\n\n@Component({\n  tag: 'bds-list',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class List {\n  private itemListElement?: HTMLCollectionOf<HTMLBdsListItemElement> | NodeListOf<HTMLBdsListItemElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalData: Data[];\n  /**\n   * Typelist. Used to .\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n\n  /**\n   * The Data of the list\n   * Should be passed this way:\n   * data='[{\"value\": \"01\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"true\",\"icon\": \"settings-builder\"}, {\"value\": \"02\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"false\",\"icon\": \"settings-builder\",}]'\n   * Data can also be passed as child by using bds-list-item component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true, reflect: true }) data?: string | Data[];\n\n  /**\n   * Emitted when the value checkboxes has changed because of a click event.\n   */\n  @Event() bdsListCheckboxChange!: EventEmitter;\n  /**\n   * Emitted when the value radios has changed because of a click event.\n   */\n  @Event() bdsListRadioChange!: EventEmitter;\n  /**\n   * Emitted when the value switches has changed because of a click event.\n   */\n  @Event() bdsListSwitchChange!: EventEmitter;\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionsButtons!: EventEmitter;\n\n  componentWillLoad() {\n    this.data && this.dataChanged();\n  }\n\n  componentWillRender() {\n    this.data && this.updateData();\n    if (!this.data) {\n      this.setitemListElement();\n    }\n  }\n  componentDidRender() {\n    if (this.data) {\n      this.internalDataChanged();\n    }\n  }\n\n  @Watch('data')\n  dataChanged() {\n    this.updateData();\n  }\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n  }\n\n  @Watch('internalData')\n  internalDataChanged() {\n    this.itemListElement = this.element.shadowRoot.querySelectorAll('bds-list-item');\n  }\n\n  private setitemListElement() {\n    this.itemListElement = this.element.getElementsByTagName(\n      'bds-list-item',\n    ) as HTMLCollectionOf<HTMLBdsListItemElement>;\n\n    for (let i = 0; i < this.itemListElement.length; i++) {\n      this.itemListElement[i].typeList = this.typeList;\n      this.itemListElement[i].addEventListener('bdsChecked', (event: CustomEvent) => this.chagedOptions(event));\n    }\n  }\n\n  private updateData() {\n    if (this.data) {\n      if (typeof this.data === 'string') {\n        this.internalData = JSON.parse(this.data);\n      } else {\n        this.internalData = this.data;\n      }\n    }\n  }\n\n  private chagedOptions = (event: CustomEvent): void => {\n    const { detail } = event;\n    if (detail.typeList == 'radio') {\n      if (detail.checked == true) {\n        this.value = detail;\n      }\n    }\n    if (detail.typeList == 'checkbox') {\n      this.setSelectedCheckbox();\n    }\n    if (detail.typeList == 'switch') {\n      this.setSelectedSwitch();\n    }\n  };\n\n  private setSelectedRadio(itemList) {\n    const itens = Array.from(this.itemListElement);\n    const radios = itens.filter((item) => item.typeList == 'radio');\n    for (let i = 0; i < radios.length; i++) {\n      if (radios[i].value != itemList.value) {\n        radios[i].checked = false;\n      } else {\n        const construct = {\n          value: radios[i].value,\n          text: radios[i]?.text,\n          secondaryText: radios[i]?.secondaryText,\n          avatarName: radios[i]?.avatarName,\n          avatarThumbnail: radios[i]?.avatarThumbnail,\n          typeList: radios[i]?.typeList,\n        };\n        this.bdsListRadioChange.emit(construct);\n      }\n    }\n  }\n\n  private setSelectedCheckbox() {\n    const checkboxs = this.itemListElement;\n    const itens = Array.from(checkboxs).filter((item) => item.typeList == 'checkbox');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListCheckboxChange.emit(result);\n  }\n\n  private setSelectedSwitch() {\n    const Switch = this.itemListElement;\n    const itens = Array.from(Switch).filter((item) => item.typeList == 'switch');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListSwitchChange.emit(result);\n  }\n\n  private onClickActionsButtons = (event: CustomEvent): void => {\n    const { detail } = event;\n    this.bdsClickActionsButtons.emit(detail);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            list: true,\n          }}\n        >\n          {this.internalData ? (\n            this.internalData.map((item, idx) => (\n              <bds-list-item\n                key={idx}\n                value={item.value}\n                text={item.text}\n                type-list={this.typeList ? this.typeList : item.typeList}\n                secondary-text={item.secondaryText}\n                avatar-name={item.avatarName}\n                avatar-thumbnail={item.avatarThumbnail}\n                checked={item.checked}\n                icon={item.icon}\n                chips={item.chips}\n                actionsButtons={item.actionsButtons}\n                onBdsChecked={(ev) => this.chagedOptions(ev)}\n                onBdsClickActionButtom={(ev) => this.onClickActionsButtons(ev)}\n                dataTest={item.dataTest}\n              ></bds-list-item>\n            ))\n          ) : (\n            <slot></slot>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"]}