{"version": 3, "file": "list-item.js", "sourceRoot": "", "sources": ["../../../../src/components/list/list-item.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AAQrG,MAAM,OAAO,QAAQ;IALrB;QAWW,kBAAa,GAAa,EAAE,CAAC;QAE7B,2BAAsB,GAAa,EAAE,CAAC;QAEP,YAAO,GAAa,KAAK,CAAC;QAClE;;WAEG;QACK,aAAQ,GAAc,IAAI,CAAC;QACnC;;WAEG;QACK,eAAU,GAAY,IAAI,CAAC;QACnC;;WAEG;QACK,oBAAe,GAAY,IAAI,CAAC;QACxC;;WAEG;QACK,SAAI,GAAY,IAAI,CAAC;QAC7B;;WAEG;QACK,UAAK,GAAW,IAAI,CAAC;QAC7B;;WAEG;QACK,SAAI,GAAY,IAAI,CAAC;QAC7B;;WAEG;QACK,kBAAa,GAAY,IAAI,CAAC;QAEtC;;;;WAIG;QACsB,UAAK,GAAsB,EAAE,CAAC;QAEvD;;;;WAIG;QACsB,mBAAc,GAAsB,EAAE,CAAC;QAEhE;;WAEG;QACK,cAAS,GAAa,KAAK,CAAC;QAEpC;;WAEG;QACK,WAAM,GAAa,KAAK,CAAC;QACjC;;WAEG;QACK,iBAAY,GAAa,KAAK,CAAC;QAEvC;;;WAGG;QACK,SAAI,GAAc,UAAU,CAAC;QACrC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAuDzB,YAAO,GAAG,GAAS,EAAE;YAC3B,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpF,CAAC,CAAC;QAEM,uBAAkB,GAAG,CAAC,IAAI,EAAE,KAAK,EAAQ,EAAE;YACjD,MAAM,aAAa,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI;gBACV,aAAa,EAAE,aAAa;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC;KAsIH;IA7LC,iBAAiB;QACf,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QAClF,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAGS,cAAc,CAAC,SAAkB;QACzC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,SAAS;SACnB,CAAC,CAAC;IACL,CAAC;IAGS,YAAY;QACpB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;YAClC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAGS,qBAAqB;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC;YACpD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAeO,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC5C,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,EAAE,CAAC;YACjB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;gBACzB,OAAO,CACL,0BAAoB,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAC,SAAS,IACjD,IAAI,CACc,CACtB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CACL,mBAAa,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,kBAAe,IAAI;oBAC5D,0BAAoB,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAC,SAAS,IACjD,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CACX,CACT,CACf,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC;YACxC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC5B,OAAO,CACL,uBACE,GAAG,EAAE,EAAE,EACP,OAAO,EAAC,WAAW,EACnB,IAAI,EAAE,MAAM,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC,GACnC,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;QACjH,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC;QAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC;QAC1D,OAAO,CACL,EAAC,IAAI;YACH,4DACE,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAC,GAAG,EACZ,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,QAAQ;oBACnB,aAAa,EAAE,IAAI,CAAC,YAAY;oBAChC,CAAC,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;iBACjC,eACU,IAAI,CAAC,QAAQ;gBAEvB,IAAI,CAAC,MAAM,IAAI,4DAAK,KAAK,EAAC,QAAQ,GAAO;gBACzC,YAAY,IAAI,CACf,4DAAK,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;oBAC7B,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,kEAAW,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,GAAc;oBAC7F,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,CAC9B,qEAAc,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,IAAI,EAAC,KAAK,EAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,GAAiB,CACnG,CACG,CACP;gBACA,SAAS,CAAC,CAAC,CAAC,CACX,kBACE,KAAK,EAAC,aAAa,EACnB,IAAI,EAAE,IAAI,CAAC,UAAU,EACrB,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,aAAa,GACN,CACf,CAAC,CAAC,CAAC,CACF,IAAI,CAAC,IAAI,IAAI,CACX,gBACE,KAAK,EAAE;wBACL,CAAC,WAAW,CAAC,EAAE,IAAI;wBACnB,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,MAAM;qBAClC,EACD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,GAC9B,CACb,CACF;gBACD,4DAAK,KAAK,EAAE,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE;oBACpC,8DAAa,CACT;gBACL,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CACpC,4DACE,KAAK,EAAE;wBACL,CAAC,cAAc,CAAC,EAAE,IAAI;wBACtB,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;qBAClG;oBAEA,IAAI,CAAC,IAAI,IAAI,CACZ,iEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,IAC3F,IAAI,CAAC,IAAI,CACD,CACZ;oBACA,IAAI,CAAC,aAAa,IAAI,CACrB,iEAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,iBAAa,OAAO,EAAC,GAAG,EAAC,MAAM,IAC3E,IAAI,CAAC,aAAa,CACV,CACZ,CACG,CACP;gBACD,4DAAK,KAAK,EAAE,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE;oBACtD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,4DAAK,KAAK,EAAC,gBAAgB,IAAE,IAAI,CAAC,WAAW,EAAE,CAAO;oBACxF,6DAAM,IAAI,EAAC,cAAc,GAAQ,CAC7B;gBACL,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CACjD,4DAAK,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE;oBAClC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,IAAI,CACzC,4DAAK,KAAK,EAAC,0BAA0B,IAAE,IAAI,CAAC,oBAAoB,EAAE,CAAO,CAC1E;oBACD,6DAAM,IAAI,EAAC,aAAa,GAAQ,CAC5B,CACP;gBACA,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAI,mEAAY,KAAK,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAe,CAC3F,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { TypeList } from './list';\nexport type ItemSize = 'tall' | 'standard' | 'short';\n@Component({\n  tag: 'bds-list-item',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class ListItem {\n  private hasActionAreaSlot: boolean;\n  private hasContentAreaSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() internalChips: string[] = [];\n\n  @State() internalActionsButtons: string[] = [];\n\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n  /**\n   * Typelis. Used toselect type of item list.\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Value. Used to insert a value in list item.\n   */\n  @Prop() value: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text?: string = null;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * The actions buttons on the component\n   * Should be passed this way:\n   * actions-buttons='[\"copy\", \"settings-general\", \"more-options-horizontal\"]'\n   */\n  @Prop({ mutable: true }) actionsButtons: string | string[] = [];\n\n  /**\n   * Clickable. Used to define if the item is clickable or not.\n   */\n  @Prop() clickable?: boolean = false;\n\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop() active?: boolean = false;\n  /**\n   * Enable rounded border on item\n   */\n  @Prop() borderRadius?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: ItemSize = 'standard';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsChecked!: EventEmitter;\n\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionButtom!: EventEmitter;\n\n  componentWillLoad() {\n    this.hasActionAreaSlot = !!this.hostElement.querySelector('[slot=\"action-area\"]');\n    this.hasContentAreaSlot = !!this.hostElement.querySelector('[slot=\"content-area\"]');\n    this.chipsChanged();\n    this.actionsButtonsChanged();\n  }\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChecked.emit({\n      value: this.value,\n      text: this.text,\n      secondaryText: this.secondaryText,\n      typeList: this.typeList,\n      checked: isChecked,\n    });\n  }\n\n  @Watch('chips')\n  protected chipsChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        this.internalChips = JSON.parse(this.chips);\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('actionsButtons')\n  protected actionsButtonsChanged(): void {\n    if (this.actionsButtons) {\n      if (typeof this.actionsButtons === 'string') {\n        this.internalActionsButtons = JSON.parse(this.actionsButtons);\n      } else {\n        this.internalActionsButtons = this.actionsButtons;\n      }\n    } else {\n      this.internalActionsButtons = [];\n    }\n  }\n\n  private handler = (): void => {\n    this.typeList == 'radio' ? (this.checked = true) : (this.checked = !this.checked);\n  };\n\n  private clickActionButtons = (data, event): void => {\n    const elementButton = event.composedPath()[0];\n    this.bdsClickActionButtom.emit({\n      value: this.value,\n      icon: data,\n      elementButton: elementButton,\n    });\n  };\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable id={id} key={id} color=\"default\">\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable id={id} key={id} color=\"default\">\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderActionsButtons() {\n    if (!this.internalActionsButtons.length) {\n      return [];\n    }\n\n    return this.internalActionsButtons.map((button, index) => {\n      const id = index.toString();\n      return (\n        <bds-button-icon\n          key={id}\n          variant=\"secondary\"\n          icon={button}\n          size=\"short\"\n          onClick={(ev) => this.clickActionButtons(button, ev)}\n        ></bds-button-icon>\n      );\n    });\n  }\n\n  render() {\n    const hasInput =\n      this.clickable == true || this.typeList == 'checkbox' || this.typeList == 'radio' || this.typeList == 'switch';\n    const hasLeftInput = this.typeList == 'checkbox' || this.typeList == 'radio';\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <Host>\n        <div\n          onClick={this.handler}\n          tabindex=\"0\"\n          class={{\n            list_item: true,\n            clickable: hasInput,\n            border_radius: this.borderRadius,\n            [`list_item_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.active && <div class=\"active\"></div>}\n          {hasLeftInput && (\n            <div class={{ input_list: true }}>\n              {this.typeList == 'radio' && <bds-radio value={this.value} checked={this.checked}></bds-radio>}\n              {this.typeList == 'checkbox' && (\n                <bds-checkbox refer=\"\" label=\"\" name=\"cb1\" disabled={false} checked={this.checked}></bds-checkbox>\n              )}\n            </div>\n          )}\n          {hasAvatar ? (\n            <bds-avatar\n              class=\"avatar-item\"\n              name={this.avatarName}\n              thumbnail={this.avatarThumbnail}\n              size=\"extra-small\"\n            ></bds-avatar>\n          ) : (\n            this.icon && (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.active,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme={this.active ? 'solid' : 'outline'}\n              ></bds-icon>\n            )\n          )}\n          <div class={{ [`content-slot`]: true }}>\n            <slot></slot>\n          </div>\n          {(this.text || this.secondaryText) && (\n            <div\n              class={{\n                [`content-item`]: true,\n                [`grow-up`]: !this.hasActionAreaSlot && !this.hasContentAreaSlot && this.internalChips.length < 0,\n              }}\n            >\n              {this.text && (\n                <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\" bold={this.active ? 'bold' : 'regular'}>\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo class=\"subtitle-item\" variant=\"fs-12\" line-height=\"small\" tag=\"span\">\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n          )}\n          <div class={{ [`content-area`]: true, [`grow-up`]: true }}>\n            {this.internalChips.length > 0 && <div class=\"internal-chips\">{this.renderChips()}</div>}\n            <slot name=\"content-area\"></slot>\n          </div>\n          {(!this.typeList || this.typeList == 'default') && (\n            <div class={{ [`action-area`]: true }}>\n              {this.internalActionsButtons.length > 0 && (\n                <div class=\"internal-actions-buttons\">{this.renderActionsButtons()}</div>\n              )}\n              <slot name=\"action-area\"></slot>\n            </div>\n          )}\n          {this.typeList == 'switch' && <bds-switch refer=\"\" name=\"\" checked={this.checked}></bds-switch>}\n        </div>\n      </Host>\n    );\n  }\n}\n"]}