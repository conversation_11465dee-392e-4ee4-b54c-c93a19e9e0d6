{"version": 3, "file": "list-item-content.js", "sourceRoot": "", "sources": ["../../../../src/components/list/list-item-content.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAQlE,MAAM,OAAO,eAAe;IAL5B;QAQU,cAAS,GAAe,QAAQ,CAAC;QACjC,mBAAc,GAAoB,YAAY,CAAC;QAC/C,aAAQ,GAAc,MAAM,CAAC;QAC7B,eAAU,GAAgB,YAAY,CAAC;KAsBhD;IAnBC,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,KAAK,EAAE;gBACL,iBAAiB,EAAE,IAAI;aACxB;YAED,iEACE,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,GAAG,EAAE,IAAI,CAAC,GAAG;gBAEb,8DAAQ,CACC,CACN,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Host, Element, Prop } from '@stencil/core';\nimport { direction, justifyContent, flexWrap, alignItems, gap } from '../grid/grid-interface';\n\n@Component({\n  tag: 'bds-list-item-content',\n  styleUrl: 'list.scss',\n  scoped: true,\n})\nexport class ListItemContent {\n  @Element() hostElement: HTMLElement;\n\n  @Prop() direction?: direction = 'column';\n  @Prop() justifyContent?: justifyContent = 'flex-start';\n  @Prop() flexWrap?: flexWrap = 'wrap';\n  @Prop() alignItems?: alignItems = 'flex-start';\n  @Prop() gap?: gap;\n\n  render() {\n    return (\n      <Host\n        class={{\n          list_item_content: true,\n        }}\n      >\n        <bds-grid\n          direction={this.direction}\n          flexWrap={this.flexWrap}\n          justifyContent={this.justifyContent}\n          alignItems={this.alignItems}\n          gap={this.gap}\n        >\n          <slot />\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"]}