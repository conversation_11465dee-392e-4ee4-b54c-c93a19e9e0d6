import { Host, h } from "@stencil/core";
export class List {
    constructor() {
        this.itemListElement = null;
        /**
         * Typelist. Used to .
         */
        this.typeList = null;
        this.chagedOptions = (event) => {
            const { detail } = event;
            if (detail.typeList == 'radio') {
                if (detail.checked == true) {
                    this.value = detail;
                }
            }
            if (detail.typeList == 'checkbox') {
                this.setSelectedCheckbox();
            }
            if (detail.typeList == 'switch') {
                this.setSelectedSwitch();
            }
        };
        this.onClickActionsButtons = (event) => {
            const { detail } = event;
            this.bdsClickActionsButtons.emit(detail);
        };
    }
    componentWillLoad() {
        this.data && this.dataChanged();
    }
    componentWillRender() {
        this.data && this.updateData();
        if (!this.data) {
            this.setitemListElement();
        }
    }
    componentDidRender() {
        if (this.data) {
            this.internalDataChanged();
        }
    }
    dataChanged() {
        this.updateData();
    }
    valueChanged(value) {
        this.setSelectedRadio(value);
    }
    internalDataChanged() {
        this.itemListElement = this.element.shadowRoot.querySelectorAll('bds-list-item');
    }
    setitemListElement() {
        this.itemListElement = this.element.getElementsByTagName('bds-list-item');
        for (let i = 0; i < this.itemListElement.length; i++) {
            this.itemListElement[i].typeList = this.typeList;
            this.itemListElement[i].addEventListener('bdsChecked', (event) => this.chagedOptions(event));
        }
    }
    updateData() {
        if (this.data) {
            if (typeof this.data === 'string') {
                this.internalData = JSON.parse(this.data);
            }
            else {
                this.internalData = this.data;
            }
        }
    }
    setSelectedRadio(itemList) {
        var _a, _b, _c, _d, _e;
        const itens = Array.from(this.itemListElement);
        const radios = itens.filter((item) => item.typeList == 'radio');
        for (let i = 0; i < radios.length; i++) {
            if (radios[i].value != itemList.value) {
                radios[i].checked = false;
            }
            else {
                const construct = {
                    value: radios[i].value,
                    text: (_a = radios[i]) === null || _a === void 0 ? void 0 : _a.text,
                    secondaryText: (_b = radios[i]) === null || _b === void 0 ? void 0 : _b.secondaryText,
                    avatarName: (_c = radios[i]) === null || _c === void 0 ? void 0 : _c.avatarName,
                    avatarThumbnail: (_d = radios[i]) === null || _d === void 0 ? void 0 : _d.avatarThumbnail,
                    typeList: (_e = radios[i]) === null || _e === void 0 ? void 0 : _e.typeList,
                };
                this.bdsListRadioChange.emit(construct);
            }
        }
    }
    setSelectedCheckbox() {
        const checkboxs = this.itemListElement;
        const itens = Array.from(checkboxs).filter((item) => item.typeList == 'checkbox');
        const result = itens
            .filter((item) => item.checked)
            .map((term) => ({
            value: term.value,
            text: term === null || term === void 0 ? void 0 : term.text,
            secondaryText: term === null || term === void 0 ? void 0 : term.secondaryText,
            avatarName: term === null || term === void 0 ? void 0 : term.avatarName,
            avatarThumbnail: term === null || term === void 0 ? void 0 : term.avatarThumbnail,
            typeList: term === null || term === void 0 ? void 0 : term.typeList,
        }));
        this.bdsListCheckboxChange.emit(result);
    }
    setSelectedSwitch() {
        const Switch = this.itemListElement;
        const itens = Array.from(Switch).filter((item) => item.typeList == 'switch');
        const result = itens
            .filter((item) => item.checked)
            .map((term) => ({
            value: term.value,
            text: term === null || term === void 0 ? void 0 : term.text,
            secondaryText: term === null || term === void 0 ? void 0 : term.secondaryText,
            avatarName: term === null || term === void 0 ? void 0 : term.avatarName,
            avatarThumbnail: term === null || term === void 0 ? void 0 : term.avatarThumbnail,
            typeList: term === null || term === void 0 ? void 0 : term.typeList,
        }));
        this.bdsListSwitchChange.emit(result);
    }
    render() {
        return (h(Host, { key: '74e1b4e602cd6e1f0ab6351394a494cbb74e5a6e' }, h("div", { key: '576759d97c75e15ef10b3d34bdf7044cc00b70b0', class: {
                list: true,
            } }, this.internalData ? (this.internalData.map((item, idx) => (h("bds-list-item", { key: idx, value: item.value, text: item.text, "type-list": this.typeList ? this.typeList : item.typeList, "secondary-text": item.secondaryText, "avatar-name": item.avatarName, "avatar-thumbnail": item.avatarThumbnail, checked: item.checked, icon: item.icon, chips: item.chips, actionsButtons: item.actionsButtons, onBdsChecked: (ev) => this.chagedOptions(ev), onBdsClickActionButtom: (ev) => this.onClickActionsButtons(ev), dataTest: item.dataTest })))) : (h("slot", null)))));
    }
    static get is() { return "bds-list"; }
    static get encapsulation() { return "shadow"; }
    static get originalStyleUrls() {
        return {
            "$": ["list.scss"]
        };
    }
    static get styleUrls() {
        return {
            "$": ["list.css"]
        };
    }
    static get properties() {
        return {
            "typeList": {
                "type": "string",
                "attribute": "type-list",
                "mutable": false,
                "complexType": {
                    "original": "TypeList",
                    "resolved": "\"checkbox\" | \"default\" | \"radio\" | \"switch\"",
                    "references": {
                        "TypeList": {
                            "location": "local",
                            "path": "/lucas/stilingue/git/projects/blip-ds/src/components/list/list.tsx",
                            "id": "src/components/list/list.tsx::TypeList"
                        }
                    }
                },
                "required": false,
                "optional": true,
                "docs": {
                    "tags": [],
                    "text": "Typelist. Used to ."
                },
                "getter": false,
                "setter": false,
                "reflect": false,
                "defaultValue": "null"
            },
            "value": {
                "type": "string",
                "attribute": "value",
                "mutable": true,
                "complexType": {
                    "original": "string",
                    "resolved": "string",
                    "references": {}
                },
                "required": false,
                "optional": true,
                "docs": {
                    "tags": [],
                    "text": "The value of the selected radio"
                },
                "getter": false,
                "setter": false,
                "reflect": true
            },
            "data": {
                "type": "string",
                "attribute": "data",
                "mutable": true,
                "complexType": {
                    "original": "string | Data[]",
                    "resolved": "Data[] | string",
                    "references": {
                        "Data": {
                            "location": "import",
                            "path": "./list-interface",
                            "id": "src/components/list/list-interface.ts::Data"
                        }
                    }
                },
                "required": false,
                "optional": true,
                "docs": {
                    "tags": [],
                    "text": "The Data of the list\nShould be passed this way:\ndata='[{\"value\": \"01\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"true\",\"icon\": \"settings-builder\"}, {\"value\": \"02\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"false\",\"icon\": \"settings-builder\",}]'\nData can also be passed as child by using bds-list-item component, but passing as a child you may have some compatibility problems with Angular."
                },
                "getter": false,
                "setter": false,
                "reflect": true
            }
        };
    }
    static get states() {
        return {
            "internalData": {}
        };
    }
    static get events() {
        return [{
                "method": "bdsListCheckboxChange",
                "name": "bdsListCheckboxChange",
                "bubbles": true,
                "cancelable": true,
                "composed": true,
                "docs": {
                    "tags": [],
                    "text": "Emitted when the value checkboxes has changed because of a click event."
                },
                "complexType": {
                    "original": "any",
                    "resolved": "any",
                    "references": {}
                }
            }, {
                "method": "bdsListRadioChange",
                "name": "bdsListRadioChange",
                "bubbles": true,
                "cancelable": true,
                "composed": true,
                "docs": {
                    "tags": [],
                    "text": "Emitted when the value radios has changed because of a click event."
                },
                "complexType": {
                    "original": "any",
                    "resolved": "any",
                    "references": {}
                }
            }, {
                "method": "bdsListSwitchChange",
                "name": "bdsListSwitchChange",
                "bubbles": true,
                "cancelable": true,
                "composed": true,
                "docs": {
                    "tags": [],
                    "text": "Emitted when the value switches has changed because of a click event."
                },
                "complexType": {
                    "original": "any",
                    "resolved": "any",
                    "references": {}
                }
            }, {
                "method": "bdsClickActionsButtons",
                "name": "bdsClickActionsButtons",
                "bubbles": true,
                "cancelable": true,
                "composed": true,
                "docs": {
                    "tags": [],
                    "text": "Emitted when click in someone actions buttom insert in data."
                },
                "complexType": {
                    "original": "any",
                    "resolved": "any",
                    "references": {}
                }
            }];
    }
    static get elementRef() { return "element"; }
    static get watchers() {
        return [{
                "propName": "data",
                "methodName": "dataChanged"
            }, {
                "propName": "value",
                "methodName": "valueChanged"
            }, {
                "propName": "internalData",
                "methodName": "internalDataChanged"
            }];
    }
}
//# sourceMappingURL=list.js.map
