{"version": 3, "file": "list-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/list/list-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TypeList } from './list';\n\nexport interface Data {\n  value: string;\n  text?: string;\n  secondaryText?: string;\n  typeList?: TypeList;\n  avatarName?: string;\n  avatarThumbnail?: string;\n  checked?: boolean;\n  icon?: string;\n  chips?: string[];\n  actionsButtons?: string[];\n  dataTest?: string;\n}\n"]}