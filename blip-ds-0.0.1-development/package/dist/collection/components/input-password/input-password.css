/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
}
:host input {
  box-shadow: inherit;
}
:host input::placeholder {
  color: var(--color-content-ghost, rgb(140, 140, 140));
  opacity: 1;
}
:host input::-webkit-input-placeholder {
  color: var(--color-content-ghost, rgb(140, 140, 140));
  opacity: 1;
}

.input {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 4px 9px 12px;
  flex: 1;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
}
.input .bds-icon {
  position: relative;
  z-index: 1;
}
.input--state-primary {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-primary .input__icon {
  position: relative;
}
.input--state-primary .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  z-index: 0;
  opacity: 50%;
  border-radius: 8px;
}
.input--state-primary:hover {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-primary.input--pressed {
  border: 1px solid var(--color-primary, rgb(30, 107, 241));
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
  box-shadow: 0 0 0 2px var(--color-info, rgb(128, 227, 235));
}
.input--state-primary.input--pressed .input__icon .bds-icon {
  color: var(--color-primary, rgb(30, 107, 241));
}
.input--state-primary .input__container__label {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-primary .input__container__label--pressed bds-typo {
  color: var(--color-primary, rgb(30, 107, 241));
}
.input--state-primary .input__container__text {
  caret-color: var(--color-primary, rgb(30, 107, 241));
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-danger {
  border: 1px solid var(--color-delete, rgb(230, 15, 15));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-danger .input__icon {
  position: relative;
}
.input--state-danger .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-negative, rgb(138, 0, 0));
  z-index: 0;
  opacity: 50%;
  border-radius: 8px;
}
.input--state-danger:hover {
  border: 1px solid var(--color-negative, #e60f0f);
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-danger.input--pressed {
  border: 1px solid var(--color-negative, #e60f0f);
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-error, rgb(250, 190, 190));
  box-shadow: 0 0 0 2px var(--color-error, rgb(250, 190, 190));
}
.input--state-danger.input--pressed .input__icon .bds-icon {
  color: var(--color-negative, #e60f0f);
}
.input--state-danger .input__container__label {
  color: var(--color-delete, rgb(230, 15, 15));
}
.input--state-danger .input__container__label--pressed bds-typo {
  color: var(--color-negative, #e60f0f);
}
.input--state-danger .input__container__text {
  caret-color: var(--color-negative, #e60f0f);
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-success {
  border: 1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-success .input__icon {
  position: relative;
}
.input--state-success .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-positive, rgb(1, 114, 62));
  z-index: 0;
  border-radius: 8px;
}
.input--state-success:hover {
  border: 1px solid var(--color-positive, #10603b);
  box-sizing: border-box;
  border-radius: 8px;
}
.input--state-success.input--pressed {
  border: 1px solid var(--color-positive, #10603b);
  box-sizing: border-box;
  border-radius: 8px;
  -webkit-box-shadow: 0 0 0 2px var(--color-success, rgb(132, 235, 188));
  box-shadow: 0 0 0 2px var(--color-success, rgb(132, 235, 188));
}
.input--state-success.input--pressed .input__icon .bds-icon {
  color: var(--color-positive, #10603b);
}
.input--state-success .input__container__label {
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-success .input__container__label--pressed bds-typo {
  color: var(--color-positive, #10603b);
}
.input--state-success .input__container__text {
  caret-color: var(--color-positive, #10603b);
  color: var(--color-content-default, rgb(40, 40, 40));
}
.input--state-disabled {
  opacity: 50%;
  pointer-events: none;
  cursor: not-allowed;
}
.input--state-disabled .input__icon {
  position: relative;
}
.input--state-disabled .input__icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-hover, rgba(0, 0, 0, 0.08));
  z-index: 0;
  opacity: 50%;
  border-radius: 8px;
}
.input .icon-success {
  color: var(--color-positive, #10603b);
  margin-left: 4px;
}
.input--label {
  padding: 7px 4px 8px 12px;
}
.input__icon {
  cursor: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 8px;
  padding: 2.5px;
}
.input__icon--large {
  padding: 4px;
}
.input__container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}
.input__container__wrapper {
  display: flex;
  flex-wrap: wrap;
}
.input__container__wrapper__chips {
  display: inline;
  max-height: 100px;
  overflow: auto;
}
.input__container__wrapper__chips::-webkit-scrollbar {
  width: 16px;
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
  border-radius: 10px;
}
.input__container__wrapper__chips::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 4px solid transparent;
  border-radius: 10px;
  background-clip: content-box;
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.input__container__label {
  display: flex;
  align-items: center;
}
.input__container__text {
  display: inline-block;
  margin: 0;
  border: 0;
  padding: 0;
  width: auto;
  vertical-align: middle;
  white-space: normal;
  line-height: inherit;
  background: none;
  /* Browsers have different default form fonts */
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  /* Make webkit render the search input like a normal text field */
  /* Turn off the recent search for webkit. It adds about 15px padding on the left */
  /* Fix IE7 display bug */
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
  font-size: 0.875rem;
  line-height: 150%;
  resize: none;
  cursor: inherit;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text::-webkit-file-upload-button {
  padding: 0;
  border: 0;
  background: none;
}
.input__container__text:focus {
  outline: 0;
}
.input__container__text[type=checkbox], .input__container__text[type=radio] {
  width: 13px;
  height: 13px;
}
.input__container__text[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
}
::-webkit-search-decoration {
  display: none;
}

.input__container__text[type=reset], .input__container__text[type=button], .input__container__text[type=submit] {
  overflow: visible;
}
.input__container__text::-webkit-scrollbar {
  width: 16px;
  background-color: var(--color-shadow-0, rgba(0, 0, 0, 0.04));
  border-radius: 10px;
}
.input__container__text::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 4px solid transparent;
  border-radius: 10px;
  background-clip: content-box;
  background-color: var(--color-border-1, rgba(0, 0, 0, 0.2));
}
.input__message {
  display: flex;
  align-items: baseline;
  height: 20px;
  margin: 3.7px 2.5px;
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--color-content-disable, rgb(89, 89, 89));
  word-break: break-word;
  height: auto;
  min-height: 20px;
}
.input__message bds-typo {
  margin-top: 0px;
  align-self: self-start;
}
.input__message__icon {
  display: flex;
  padding-right: 4px;
  margin-top: 0px;
  padding-top: 2px;
}
.input__message--danger .bds-icon {
  color: var(--color-negative, #e60f0f);
}
.input__message--danger .input__message__text {
  color: var(--color-negative, #e60f0f);
}
.input__message--success .input__message__icon .bds-icon {
  color: var(--color-positive, #10603b);
}
.input__message--success .input__message__text {
  color: var(--color-content-default, rgb(40, 40, 40));
}

.input__password--icon {
  position: relative;
  color: var(--color-content-disable, rgb(89, 89, 89));
  display: flex;
}
.input__password--icon::before {
  content: "";
  position: absolute;
  inset: -4px;
  border: 2px solid transparent;
  border-radius: 4px;
  pointer-events: none;
}
.input__password--icon:focus-visible {
  outline: none;
}
.input__password--icon:focus-visible::before {
  border-color: var(--color-focus, rgb(194, 38, 251));
}