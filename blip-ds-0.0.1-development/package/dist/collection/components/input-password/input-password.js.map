{"version": 3, "file": "input-password.js", "sourceRoot": "", "sources": ["../../../../src/components/input-password/input-password.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAgB,KAAK,EAAE,MAAM,eAAe,CAAC;AAQ5F,MAAM,OAAO,aAAa;IAL1B;QAOE;;WAEG;QACM,qBAAgB,GAAa,KAAK,CAAC;QAC5C;;WAEG;QACM,cAAS,GAAI,KAAK,CAAC;QAE5B;;WAEG;QACM,qBAAgB,GAAI,EAAE,CAAC;QAExB,aAAQ,GAAI,KAAK,CAAC;QAE1B;;WAEG;QACqC,UAAK,GAAmB,EAAE,CAAC;QAEnE;;WAEG;QACK,UAAK,GAAY,EAAE,CAAC;QAE5B;;WAEG;QACK,cAAS,GAAY,EAAE,CAAC;QAsBhC;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACK,kBAAa,GAAY,EAAE,CAAC;QAEpC;;WAEG;QACK,iBAAY,GAAY,EAAE,CAAC;QACnC;;WAEG;QACsB,mBAAc,GAAY,EAAE,CAAC;QACtD;;WAEG;QACsB,WAAM,GAAa,KAAK,CAAC;QAClD;;WAEG;QACqC,YAAO,GAAa,KAAK,CAAC;QAClE;;WAEG;QACsB,SAAI,GAAY,EAAE,CAAC;QAE5C;;WAEG;QACK,aAAQ,GAAI,KAAK,CAAC;QAE1B;;WAEG;QACK,mBAAc,GAAyB,KAAK,CAAC;QAErD;;WAEG;QACK,iBAAY,GAAuB,KAAK,CAAC;QAEjD;;WAEG;QACK,gBAAW,GAAY,EAAE,CAAC;QAElC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAgCzB,mBAAc,GAAG,CAAC,EAAoB,EAAQ,EAAE;YACtD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACxB,CAAC,CAAC;QAEM,sBAAiB,GAAG,GAAS,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QAaM,mBAAc,GAAG,GAAS,EAAE;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAOM,YAAO,GAAG,CAAC,EAAc,EAAQ,EAAE;YACzC,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC,CAAC;YACnD,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACjC,CAAC;YACD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC;QAEM,WAAM,GAAG,GAAS,EAAE;YAC1B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC;QAEM,YAAO,GAAG,GAAS,EAAE;YAC3B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC;QAEM,aAAQ,GAAG,GAAS,EAAE;YAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;QACrC,CAAC,CAAC;QAEM,oBAAe,GAAG,CAAC,KAAoB,EAAQ,EAAE;YACvD,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBAE/D,MAAM;gBACR,KAAK,WAAW,CAAC;gBACjB,KAAK,QAAQ;oBACX,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC5D,MAAM;YACV,CAAC;QACH,CAAC,CAAC;KA8HH;IAtLS,aAAa,CAAC,KAAK;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,kBAAkB,CAAC;QAC9C,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAUS,QAAQ;QAChB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACvG,CAAC;IAqCO,UAAU;QAChB,OAAO,CACL,IAAI,CAAC,IAAI,IAAI,CACX,WACE,KAAK,EAAE;gBACL,WAAW,EAAE,IAAI;gBACjB,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;aACnC;YAED,gBAAU,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,GAAY,CACzF,CACP,CACF,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,CACL,IAAI,CAAC,KAAK,IAAI,CACZ,aACE,KAAK,EAAE;gBACL,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE;YAED,gBAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,IAClC,IAAI,CAAC,KAAK,CACF,CACL,CACT,CACF,CAAC;IACJ,CAAC;IAEO,aAAa;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;QACzE,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;QAExG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEvE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAClC,CAAC,CAAC,uCAAuC;YACzC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACZ,CAAC,CAAC,wCAAwC;gBAC1C,CAAC,CAAC,gBAAgB,CAAC;QAEzB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CACL,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB;gBACvC,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,gBAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E;gBACN,gBAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,IACnD,OAAO,CACC,CACP,CACP,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC;QAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,OAAO,CACL,EAAC,IAAI,sEAAgB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;YAChD,4DACE,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI;oBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBAC9D,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;oBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;oBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;oBACtC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;oBAC5B,gBAAgB,EAAE,SAAS;iBAC5B,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB;gBAErB,IAAI,CAAC,UAAU,EAAE;gBAClB,4DAAK,KAAK,EAAC,kBAAkB;oBAC1B,IAAI,CAAC,WAAW,EAAE;oBACnB,4DAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE;wBAC7C,8DACE,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,YAAY,EAAE,YAAY,EAC1B,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,GACjB,CACL,CACF;gBACN,4DACE,KAAK,EAAC,uBAAuB,EAC7B,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,QAAQ,EAAC,GAAG;oBAEZ,iEAAU,IAAI,EAAC,OAAO,EAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAC,SAAS,GAAY,CAClE;gBACL,IAAI,CAAC,OAAO,IAAI,iEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F;YACL,IAAI,CAAC,aAAa,EAAE,CAChB,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, State, Prop, h, Host, Event, EventEmitter, Watch } from '@stencil/core';\nimport { InputAutocapitalize, InputAutoComplete } from '../input/input-interface';\n\n@Component({\n  tag: 'bds-input-password',\n  styleUrl: 'input-password.scss',\n  shadow: true,\n})\nexport class InputPassword {\n  private nativeInput?: HTMLInputElement;\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @Prop() openEyes? = false;\n\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label?: string = '';\n\n  /**\n   * Input Name\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * The maximum value, which must not be less than its minimum (min attribute) value.\n   */\n  @Prop() max?: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the maximum number of characters that the user can enter.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * The minimum value, which must not be greater than its maximum (max attribute) value.\n   */\n  @Prop() min?: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the minimum number of characters that the user can enter.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger?: boolean = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop() disabled? = false;\n\n  /**\n   * Capitalizes every word's second character.\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Hint for form autofill feature\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsInputPasswordChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInputPasswordInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsInputPasswordBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsInputPasswordFocus: EventEmitter;\n\n  /**\n   * Event input enter.\n   */\n  @Event() bdsInputPasswordSubmit: EventEmitter;\n\n  /**\n   * Event input key down backspace.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  private refNativeInput = (el: HTMLInputElement): void => {\n    this.nativeInput = el;\n  };\n\n  private toggleEyePassword = (): void => {\n    if (!this.disabled) {\n      this.openEyes = !this.openEyes;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.toggleEyePassword();\n    }\n  }\n\n  private getAutoComplete(): string {\n    if (!this.openEyes) return 'current-password';\n    return this.autoComplete;\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  @Watch('value')\n  protected onChange(): void {\n    this.bdsInputPasswordChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInputPasswordInput.emit(ev);\n  };\n\n  private onBlur = (): void => {\n    this.bdsInputPasswordBlur.emit();\n    this.isPressed = false;\n  };\n\n  private onFocus = (): void => {\n    this.bdsInputPasswordFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onSubmit = (): void => {\n    this.bdsInputPasswordSubmit.emit();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsInputPasswordSubmit.emit({ event, value: this.value });\n\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const iconPassword = this.openEyes ? 'eye-open' : 'eye-closed';\n    const type = this.openEyes ? 'text' : 'password';\n    const autocomplete = this.getAutoComplete();\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              <input\n                ref={this.refNativeInput}\n                class={{ input__container__text: true }}\n                type={type}\n                name={this.inputName}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                readOnly={this.readonly}\n                autocomplete={autocomplete}\n                autocapitalize={this.autoCapitalize}\n                placeholder={this.placeholder}\n                onInput={this.onInput}\n                onFocus={this.onFocus}\n                onBlur={this.onBlur}\n                onSubmit={this.onSubmit}\n                value={this.value}\n                disabled={this.disabled}\n                data-test={this.dataTest}\n              ></input>\n            </div>\n          </div>\n          <div\n            class=\"input__password--icon\"\n            onClick={this.toggleEyePassword}\n            onKeyDown={this.handleKeyDown.bind(this)}\n            tabindex=\"0\"\n          >\n            <bds-icon size=\"small\" name={iconPassword} color=\"inherit\"></bds-icon>\n          </div>\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"]}