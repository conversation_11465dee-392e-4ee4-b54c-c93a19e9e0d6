{"version": 3, "file": "toast.js", "sourceRoot": "", "sources": ["../../../../src/components/toast/toast.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAgB,MAAM,eAAe,CAAC;AAc7G,MAAM,OAAO,QAAQ;IALrB;QAOE;;WAEG;QACsB,SAAI,GAAY,IAAI,CAAC;QAC9C;;;WAGG;QACK,eAAU,GAAe,QAAQ,CAAC;QAC1C;;;WAGG;QACK,YAAO,GAAgB,QAAQ,CAAC;QAaxC;;;WAGG;QACK,aAAQ,GAAG,CAAC,CAAC;QACrB;;;;;WAKG;QACK,iBAAY,GAAqB,OAAO,CAAC;QACjD;;WAEG;QACK,SAAI,GAAG,KAAK,CAAC;QACrB;;WAEG;QACK,SAAI,GAAG,KAAK,CAAC;QACrB;;;WAGG;QACK,aAAQ,GAAiB,aAAa,CAAC;QAE/C;;;WAGG;QACK,mBAAc,GAAY,IAAI,CAAC;QACvC;;;WAGG;QACK,kBAAa,GAAY,IAAI,CAAC;QAKtC;;WAEG;QACK,wBAAmB,GAAG,GAAG,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;iBAC3C,CAAC;gBACJ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;QACH,CAAC,CAAC;QAiFM,gBAAW,GAAmB;YACpC,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,YAAY,EAAE,cAAc;SAC7B,CAAC;KAyDH;IAhJS,gBAAgB,CAAC,KAAK;QAC5B,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;iBAC3C,CAAC;gBACJ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,MAAM,CAAC,EACX,UAAU,EACV,YAAY,EACZ,UAAU,EACV,IAAI,EACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,QAAQ,GACQ;QAChB,IAAI,cAAc,GAAG,QAAQ,CAAC,aAAa,CACzC,uBAAuB,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,EAAE,CAClF,CAAC;QAEF,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QACzF,CAAC;aAAM,CAAC;YACN,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAC/D,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;YACvF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAC1C,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC;QAC5C,IAAI,CAAC,EAAE,CAAC,YAAY,GAAG,YAAY,IAAI,OAAO,CAAC;QAC/C,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,IAAI,QAAQ,CAAC;QACtC,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC;QAE5E,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACzB,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;gBACnB,CAAC,EAAE,GAAG,CAAC,CAAC;YACV,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;YACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACjE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;QACnB,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAYD,MAAM;QACJ,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,KAAK,EAAE,IAAI;gBACX,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI;gBAChC,CAAC,kBAAkB,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI;gBAC3C,CAAC,cAAc,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI;gBAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;YAEA,IAAI,CAAC,OAAO,KAAK,cAAc,IAAI,CAClC,iEAAU,KAAK,EAAC,eAAe,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,EAAC,IAAI,EAAC,OAAO,GAAG,CAC/E;YACA,IAAI,CAAC,IAAI,IAAI,iEAAU,KAAK,EAAC,aAAa,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAI;YAC7F,4DAAK,KAAK,EAAC,gBAAgB;gBACxB,IAAI,CAAC,UAAU,IAAI,CAClB,iEAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,IAClC,IAAI,CAAC,UAAU,CACP,CACZ;gBACA,IAAI,CAAC,SAAS,IAAI,iEAAU,OAAO,EAAC,OAAO,EAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAa,CAC/E;YACN,4DACE,KAAK,EAAE;oBACL,aAAa,EAAE,IAAI;oBACnB,CAAC,kBAAkB,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI;iBAC5C,IAEA,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,CAC9B,kBACE,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3C,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,EACzC,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,UAAU,EACf,QAAQ,EAAE,IAAI,CAAC,cAAc,IAE5B,IAAI,CAAC,UAAU,CACL,CACd,CAAC,CAAC,CAAC,CACF,uBACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,EACzC,IAAI,EAAC,OAAO,EACZ,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3C,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,OAAO,EACZ,QAAQ,EAAE,IAAI,CAAC,aAAa,GAC5B,CACH,CACG,CACF,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, ComponentInterface, h, Prop, Method, Element, Event, EventEmitter } from '@stencil/core';\nimport {\n  ActionType,\n  VariantType,\n  ButtonActionType,\n  CreateToastType,\n  IconVariantMap,\n  PositionType,\n} from './toast-interface';\n@Component({\n  tag: 'bds-toast',\n  styleUrl: 'toast.scss',\n  shadow: true,\n})\nexport class BdsToast implements ComponentInterface {\n  @Element() el: HTMLBdsToastElement;\n  /**\n   * used for add the icon. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n  /**\n   * ActionType. Defines if the button should have a button or an icon. Can be one of:\n   * 'icon', 'button';\n   */\n  @Prop() actionType: ActionType = 'button';\n  /**\n   * Variant. Defines the color of the toast. Can be one of:\n   * 'system', 'error', 'success', 'warning', 'undo', 'redo';\n   */\n  @Prop() variant: VariantType = 'system';\n  /**\n   * The title of the component:\n   */\n  @Prop() toastTitle: string;\n  /**\n   * The text content of the component:\n   */\n  @Prop() toastText: string;\n  /**\n   * If the action type is button, this will be the text of the button:\n   */\n  @Prop() buttonText: string;\n  /**\n   * Time to close the toast in seconds\n   * 0 = never close automatically (default value)\n   */\n  @Prop() duration = 0;\n  /**\n   * Define an action to the button toast. Can be one of:\n   * 'close', 'custom';\n   * if the action type is set to close, the button will close automatically.\n   * if the action type is set to custom, a function need to be passed when the toastButtonClick is emitted.\n   */\n  @Prop() buttonAction: ButtonActionType = 'close';\n  /**\n   * Controls the open event of the component:\n   */\n  @Prop() show = false;\n  /**\n   * Controls the hide event of the component:\n   */\n  @Prop() hide = false;\n  /**\n   * The toast position on the screen. Can be one of:\n   * 'top-right', 'top-left', 'bottom-right', 'bottom-left' (default value);\n   */\n  @Prop() position: PositionType = 'bottom-left';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonAction is the data-test to button action.\n   */\n  @Prop() dtButtonAction?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Event used to execute some action when the action button on the toast is clicked\n   */\n  @Event() toastButtonClick!: EventEmitter;\n  /**\n   * Sends an event to be used when creating an action when clicking the toast button\n   */\n  private _buttonClickHandler = () => {\n    if (this.buttonAction === 'close') this.close();\n    else {\n      this.toastButtonClick.emit(this.el);\n      this.close();\n    }\n  };\n\n  private _keyPressHandler(event) {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault();\n      if (this.buttonAction === 'close') this.close();\n      else {\n        this.toastButtonClick.emit(this.el);\n        this.close();\n      }\n    }\n  }\n\n  /**\n   * Can be used outside to open the toast\n   */\n  @Method()\n  async create({\n    actionType,\n    buttonAction,\n    buttonText,\n    icon,\n    toastText,\n    toastTitle,\n    variant,\n    duration,\n  }: CreateToastType) {\n    let toastContainer = document.querySelector(\n      `bds-toast-container.${variant === 'notification' ? 'top-right' : 'bottom-left'}`,\n    );\n\n    if (toastContainer) {\n      toastContainer.appendChild(this.el);\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n    } else {\n      toastContainer = document.createElement('bds-toast-container');\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n      document.body.appendChild(toastContainer);\n      toastContainer.appendChild(this.el);\n    }\n    this.el.actionType = actionType || 'button';\n    this.el.buttonAction = buttonAction || 'close';\n    this.el.buttonText = buttonText;\n    this.el.toastText = toastText;\n    this.el.toastTitle = toastTitle;\n    this.el.variant = variant || 'system';\n    this.el.duration = duration * 1000 || 0;\n    this.el.position = variant === 'notification' ? 'top-right' : 'bottom-left';\n\n    this.el.icon = icon ?? this.mapIconName[this.variant];\n\n    this.el.show = true;\n\n    if (this.el.duration > 0) {\n      setTimeout(() => {\n        this.el.hide = true;\n        setTimeout(() => {\n          this.el.remove();\n        }, 400);\n      }, this.el.duration);\n    }\n  }\n\n  /**\n   * Can be used outside the component to close the toast\n   */\n  @Method()\n  async close() {\n    if (this.el.shadowRoot) {\n      this.el.shadowRoot.querySelector('div').classList.remove('show');\n      this.el.shadowRoot.querySelector('div').classList.add('hide');\n    } else {\n      this.el.querySelector('div').classList.remove('show');\n      this.el.querySelector('div').classList.add('hide');\n    }\n\n    setTimeout(() => {\n      this.el.remove();\n    }, 400);\n  }\n\n  private mapIconName: IconVariantMap = {\n    system: 'bell',\n    error: 'error',\n    success: 'like',\n    warning: 'attention',\n    undo: 'undo',\n    redo: 'redo',\n    notification: 'notification',\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          toast: true,\n          [`toast--${this.variant}`]: true,\n          [`toast--action--${this.actionType}`]: true,\n          [`show show--${this.position}`]: this.show,\n          hide: this.hide,\n        }}\n      >\n        {this.variant === 'notification' && (\n          <bds-icon class=\"toast__ballon\" theme=\"solid\" name=\"blip-chat\" size=\"brand\" />\n        )}\n        {this.icon && <bds-icon class=\"toast__icon\" theme=\"outline\" size=\"medium\" name={this.icon} />}\n        <div class=\"toast__content\">\n          {this.toastTitle && (\n            <bds-typo variant=\"fs-14\" bold=\"bold\">\n              {this.toastTitle}\n            </bds-typo>\n          )}\n          {this.toastText && <bds-typo variant=\"fs-14\" innerHTML={this.toastText}></bds-typo>}\n        </div>\n        <div\n          class={{\n            toast__action: true,\n            [`toast__action__${this.actionType}`]: true,\n          }}\n        >\n          {this.actionType === 'button' ? (\n            <bds-button\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              onClick={() => this._buttonClickHandler()}\n              variant=\"secondary\"\n              size=\"standard\"\n              dataTest={this.dtButtonAction}\n            >\n              {this.buttonText}\n            </bds-button>\n          ) : (\n            <bds-button-icon\n              onClick={() => this._buttonClickHandler()}\n              size=\"short\"\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              variant=\"secondary\"\n              icon=\"close\"\n              dataTest={this.dtButtonClose}\n            />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"]}