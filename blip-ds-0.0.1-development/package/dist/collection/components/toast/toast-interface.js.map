{"version": 3, "file": "toast-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/toast/toast-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export type ActionType = 'button' | 'icon';\n\nexport type VariantType = 'system' | 'error' | 'success' | 'warning' | 'undo' | 'redo' | 'notification';\n\nexport type ButtonActionType = 'close' | 'custom';\n\nexport type IconVariantMap = { [key in VariantType]: string };\n\nexport type PositionType = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\n\nexport type CreateToastType = {\n  buttonAction?: ButtonActionType;\n  buttonText?: string;\n  toastText: string;\n  toastTitle: string;\n  icon?: string;\n  actionType?: ActionType;\n  variant?: VariantType;\n  duration?: number;\n  position?: PositionType;\n};\n"]}