{"version": 3, "file": "counter-text-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/counter-text/counter-text-interface.ts"], "names": [], "mappings": "AAAA,MAAM,CAAN,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,uCAAmB,CAAA;IACnB,uCAAmB,CAAA;IACnB,qCAAiB,CAAA;AACnB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B", "sourcesContent": ["export enum CounterTextState {\n  Default = 'default',\n  Warning = 'warning',\n  Delete = 'delete',\n}\n\nexport type CounterTextRule = {\n  max: number;\n  min: number;\n};\n"]}