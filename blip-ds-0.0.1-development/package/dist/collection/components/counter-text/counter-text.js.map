{"version": 3, "file": "counter-text.js", "sourceRoot": "", "sources": ["../../../../src/components/counter-text/counter-text.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACnD,OAAO,EAAmB,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAM7E,MAAM,OAAO,WAAW;IAJxB;QAO2B,WAAM,GAAI,KAAK,CAAC;QAEhB,YAAO,GAAqB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAChD,WAAM,GAAqB,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;KAoCxE;IAlCC,QAAQ;QACN,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACzE,OAAO,gBAAgB,CAAC,OAAO,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YACpC,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,OAAO,gBAAgB,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;IAChC,CAAC;IAED,MAAM;QACJ,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,OAAO,CACL,4DACE,KAAK,EAAE;gBACL,cAAc,EAAE,IAAI;gBACpB,sBAAsB,EAAE,IAAI,CAAC,MAAM;gBACnC,CAAC,iBAAiB,KAAK,EAAE,CAAC,EAAE,IAAI;aACjC;YAED,iEAAU,OAAO,EAAC,OAAO,IAAE,YAAY,CAAY,CAC/C,CACP,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, h, Prop } from '@stencil/core';\nimport { CounterTextRule, CounterTextState } from './counter-text-interface';\n\n@Component({\n  tag: 'bds-counter-text',\n  styleUrl: 'counter-text.scss',\n})\nexport class CounterText {\n  @Prop({ mutable: true }) length!: number;\n  @Prop() max?: number;\n  @Prop({ mutable: true }) active? = false;\n\n  @Prop({ mutable: true }) warning?: CounterTextRule = { max: 20, min: 2 };\n  @Prop({ mutable: true }) delete?: CounterTextRule = { max: 1, min: 0 };\n\n  getState(): string {\n    const actualLength = this.getActualLength();\n\n    if (actualLength >= this.warning.min && actualLength <= this.warning.max) {\n      return CounterTextState.Warning;\n    }\n\n    if (actualLength <= this.delete.max) {\n      return CounterTextState.Delete;\n    }\n\n    return CounterTextState.Default;\n  }\n\n  getActualLength(): number {\n    return this.max - this.length;\n  }\n\n  render(): HTMLElement {\n    const state = this.getState();\n    const actualLength = this.getActualLength();\n\n    return (\n      <div\n        class={{\n          'counter-text': true,\n          'counter-text--active': this.active,\n          [`counter-text--${state}`]: true,\n        }}\n      >\n        <bds-typo variant=\"fs-10\">{actualLength}</bds-typo>\n      </div>\n    );\n  }\n}\n"]}