/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  width: 1em;
  height: 1em;
  color: var(--color-content-default, rgb(40, 40, 40));
  fill: currentColor;
  box-sizing: content-box !important;
}

.icon-inner {
  max-width: 100%;
  height: 100%;
}

.emoji-inner {
  max-width: 100%;
  height: 100%;
  display: flex;
}

:host(.bds-icon) {
  display: inline-block;
}
:host(.bds-icon) svg {
  fill: currentColor;
  width: 100%;
  min-width: 100%;
}

:host(.bds-icon__size--brand) {
  width: 64px;
  min-width: 64px;
  height: 64px;
  max-height: 64px;
  min-height: 64px;
}

:host(.bds-icon__size--xxx-large) {
  width: 40px;
  min-width: 40px;
  height: 40px;
  max-height: 40px;
  min-height: 40px;
}

:host(.bds-icon__size--xx-large) {
  width: 36px;
  min-width: 36px;
  height: 36px;
  max-height: 36px;
  min-height: 36px;
}

:host(.bds-icon__size--x-large) {
  width: 32px;
  min-width: 32px;
  height: 32px;
  max-height: 32px;
  min-height: 32px;
}

:host(.bds-icon__size--large) {
  width: 28px;
  min-width: 28px;
  height: 28px;
  max-height: 28px;
  min-height: 28px;
}

:host(.bds-icon__size--medium) {
  width: 24px;
  min-width: 24px;
  height: 24px;
  max-height: 24px;
  min-height: 24px;
}

:host(.bds-icon__size--small) {
  width: 20px;
  min-width: 20px;
  height: 20px;
  max-height: 20px;
  min-height: 20px;
}

:host(.bds-icon__size--x-small) {
  width: 16px;
  min-width: 16px;
  height: 16px;
  max-height: 16px;
  min-height: 16px;
}

:host(.bds-icon__size--xx-small) {
  width: 12px;
  min-width: 12px;
  height: 12px;
  max-height: 12px;
  min-height: 12px;
}