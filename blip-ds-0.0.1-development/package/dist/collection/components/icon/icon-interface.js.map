{"version": 3, "file": "icon-interface.js", "sourceRoot": "", "sources": ["../../../../src/components/icon/icon-interface.ts"], "names": [], "mappings": "", "sourcesContent": ["export type IconSize =\n  | 'xxx-small'\n  | 'xx-small'\n  | 'x-small'\n  | 'small'\n  | 'medium'\n  | 'large'\n  | 'x-large'\n  | 'xx-large'\n  | 'xxx-large'\n  | 'brand';\n\nexport type IconTheme = 'outline' | 'solid';\n\nexport type IconType = 'icon' | 'emoji' | 'logo';\n"]}