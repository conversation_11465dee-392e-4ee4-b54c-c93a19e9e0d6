{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/components/icon/utils.ts"], "names": [], "mappings": "AAEA,MAAM,sBAAsB,GAAG,CAAC,GAAY,EAAE,KAAa,EAAQ,EAAE;IACnE,MAAM,KAAK,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,UAAyB,EAAE,KAAoB,EAAE,KAAK,GAAG,KAAK,EAAU,EAAE;IAClG,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC;QAE3B,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB,CAAC;QAErC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACjC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAE5C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,sBAAsB,CAAC,MAAM,EAAE,KAAK,IAAI,cAAc,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,GAAG,CAAC,SAAS,CAAC;IACvB,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,KAAgB,EAAE,EAAE;IAC5D,OAAO,cAAc,IAAI,IAAI,KAAK,EAAE,CAAC;AACvC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,EAAE;IAC3C,OAAO,eAAe,IAAI,EAAE,CAAC;AAC/B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,OAAO,cAAc,IAAI,EAAE,CAAC;AAC9B,CAAC,CAAC", "sourcesContent": ["import { IconTheme } from './icon-interface';\n\nconst clearPathsAndFillColor = (svg: Element, color: string): void => {\n  const paths = svg.getElementsByTagName('path');\n\n  for (let i = 0; i < paths.length; i++) {\n    paths[i].setAttribute('fill', color);\n  }\n\n  svg.setAttribute('fill', color);\n};\n\nexport const formatSvg = (svgContent: string | null, color: string | null, emoji = false): string => {\n  if (svgContent) {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    svgElm.setAttribute('fill', 'currentColor');\n\n    if (!emoji) {\n      clearPathsAndFillColor(svgElm, color || 'currentColor');\n    }\n    return div.innerHTML;\n  }\n\n  return '';\n};\n\nexport const getIconName = (name: string, theme: IconTheme) => {\n  return `asset-icon-${name}-${theme}`;\n};\n\nexport const getEmojiName = (name: string) => {\n  return `asset-emoji-${name}`;\n};\n\nexport const getLogoName = (name: string) => {\n  return `asset-logo-${name}`;\n};\n"]}