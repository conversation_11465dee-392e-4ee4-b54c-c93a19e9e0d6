{"version": 3, "file": "icon.js", "sourceRoot": "", "sources": ["../../../../src/components/icon/icon.tsx"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AACvF,OAAO,KAAK,MAAM,0CAA0C,CAAC;AAC7D,OAAO,MAAM,MAAM,2CAA2C,CAAC;AAC/D,OAAO,IAAI,MAAM,0CAA0C,CAAC;AAE5D,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAQ5E,MAAM,OAAO,IAAI;IANjB;QAYmB,cAAS,GAAG,KAAK,CAAC;QAkCnC;;;WAGG;QACK,SAAI,GAAc,QAAQ,CAAC;QAEnC;;;WAGG;QACK,SAAI,GAAG,KAAK,CAAC;QAErB;;WAEG;QACsB,UAAK,GAAc,SAAS,CAAC;QAEtD;;WAEG;QACK,SAAI,GAAa,MAAM,CAAC;QAEhC;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAwDjC,kBAAa,GAAG,GAAG,EAAE;YACnB,IAAI,GAAG,CAAC;YACR,IAAI,CAAC;gBACH,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACzB,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC/C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBACvB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/C,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBACjC,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;oBACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACrD,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBAChC,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,sCAAsC;gBACtC,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;KA2BH;IArGC,iBAAiB;QACf,2DAA2D;QAC3D,kDAAkD;QAClD,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB;QAClB,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,EAAe,EAAE,EAAc;QACtD,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,oBAAoB,EAAE,CAAC;YAC1G,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,IAAK,MAAc,CAAC,oBAAoB,CAAC,CAAC,IAAiC,EAAE,EAAE;gBACnG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;oBAC3B,EAAE,CAAC,UAAU,EAAE,CAAC;oBAChB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;oBACpB,EAAE,EAAE,CAAC;gBACP,CAAC;YACH,CAAC,CAAC,CAAC,CAAC;YAEJ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,+CAA+C;YAC/C,qCAAqC;YACrC,EAAE,EAAE,CAAC;QACP,CAAC;IACH,CAAC;IAMD,QAAQ;QACN,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QAEvB,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YACxB,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAwBD,MAAM;QACJ,OAAO,CACL,EAAC,IAAI,qDACH,IAAI,EAAC,KAAK,EACV,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI;gBAChB,CAAC,mBAAmB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI;aACvC,IAEA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CACjB,WACE,KAAK,EAAE;gBACL,YAAY,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;gBAClC,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;gBACpC,YAAY,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;aACnC,EACD,SAAS,EAAE,IAAI,CAAC,UAAU,eACf,IAAI,CAAC,QAAQ,GACnB,CACR,CAAC,CAAC,CAAC,CACF,WAAK,KAAK,EAAC,YAAY,eAAY,IAAI,CAAC,QAAQ,GAAQ,CACzD,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Build, Component, Element, Host, Prop, State, Watch, h } from '@stencil/core';\nimport icons from 'blip-tokens/build/json/assets_icons.json';\nimport emojis from 'blip-tokens/build/json/assets_emojis.json';\nimport logo from 'blip-tokens/build/json/assets_logos.json';\nimport { IconSize, IconTheme, IconType } from './icon-interface';\nimport { formatSvg, getIconName, getEmojiName, getLogoName } from './utils';\n\n@Component({\n  tag: 'bds-icon',\n  assetsDirs: ['svg'],\n  styleUrl: 'icon.scss',\n  shadow: true,\n})\nexport class Icon {\n  private io?: IntersectionObserver;\n\n  @Element() el!: HTMLElement;\n\n  @State() private svgContent?: string;\n  @State() private isVisible = false;\n\n  /**\n   * Specifies the color to use.Specifies a color to use. The default is svg.\n   */\n  @Prop() color?: string;\n\n  /**\n   * Specifies the label to use for accessibility. Defaults to the icon name.\n   */\n  @Prop({ mutable: true, reflect: true }) ariaLabel: string;\n\n  /**\n   * Specifies whether the icon should horizontally flip when `dir` is `\"rtl\"`.\n   */\n  @Prop() flipRtl?: boolean;\n\n  /**\n   * Specifies which icon to use from the built-in set of icons.\n   */\n  @Prop() name?: string;\n\n  /**\n   * Specifies the exact `src` of an SVG file to use.\n   */\n  @Prop() src?: string;\n\n  /**\n   * A combination of both `name` and `src`. If a `src` url is detected\n   * it will set the `src` property. Otherwise it assumes it's a built-in named\n   * SVG and set the `name` property.\n   */\n  @Prop() icon?: any;\n\n  /**\n   * Icon size. Entered as one of the icon size design tokens. Can be one of:\n   * \"xxx-small\", \"xx-small\", \"x-small\", \"small\", \"medium\", \"large\", \"x-large\", \"xx-large\", \"xxx-large\", \"brand\".\n   */\n  @Prop() size?: IconSize = 'medium';\n\n  /**\n   * If enabled, ion-icon will be loaded lazily when it's visible in the viewport.\n   * Default, `false`.\n   */\n  @Prop() lazy = false;\n\n  /**\n   * Specifies the theme to use outline or solid icons. Defaults to outline.\n   */\n  @Prop({ reflect: true }) theme: IconTheme = 'outline';\n\n  /**\n   * Specifies the type of icon. If type is set to emoji, it will be able to set only emoji names on the name property.\n   */\n  @Prop() type: IconType = 'icon';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    // purposely do not return the promise here because loading\n    // the svg file should not hold up loading the app\n    // only load the svg if it's visible\n    this.waitUntilVisible(this.el, () => {\n      this.isVisible = true;\n      this.loadIcon();\n    });\n  }\n\n  disconnectedCallback(): void {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n\n  private waitUntilVisible(el: HTMLElement, cb: () => void): void {\n    if (Build.isBrowser && this.lazy && typeof window !== 'undefined' && (window as any).IntersectionObserver) {\n      const io = (this.io = new (window as any).IntersectionObserver((data: IntersectionObserverEntry[]) => {\n        if (data[0].isIntersecting) {\n          io.disconnect();\n          this.io = undefined;\n          cb();\n        }\n      }));\n\n      io.observe(el);\n    } else {\n      // browser doesn't support IntersectionObserver\n      // so just fallback to always show it\n      cb();\n    }\n  }\n\n  @Watch('name')\n  @Watch('src')\n  @Watch('icon')\n  @Watch('theme')\n  loadIcon(): void {\n    if (!this.name) return;\n\n    if (Build.isBrowser && this.isVisible) {\n      this.setSvgContent();\n    }\n\n    if (!this.ariaLabel) {\n      const label = this.name;\n      if (label) {\n        this.ariaLabel = label;\n      }\n    }\n  }\n\n  setSvgContent = () => {\n    let svg;\n    try {\n      if (this.type === 'icon') {\n        const key = getIconName(this.name, this.theme);\n        svg = atob(icons[key]);\n        this.svgContent = formatSvg(svg, this.color);\n      } else if (this.type === 'emoji') {\n        const key = getEmojiName(this.name);\n        svg = atob(emojis[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      } else if (this.type === 'logo') {\n        const key = getLogoName(this.name);\n        svg = atob(logo[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn('[Warning]: Failed to setSvgContent to', this.name);\n    }\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-icon': true,\n          [`bds-icon__size--${this.size}`]: true,\n        }}\n      >\n        {this.svgContent ? (\n          <div\n            class={{\n              'icon-inner': this.type === 'icon',\n              'emoji-inner': this.type === 'emoji',\n              'logo-inner': this.type === 'logo',\n            }}\n            innerHTML={this.svgContent}\n            data-test={this.dataTest}\n          ></div>\n        ) : (\n          <div class=\"icon-inner\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"]}