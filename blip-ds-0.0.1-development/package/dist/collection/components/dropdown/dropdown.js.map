{"version": 3, "file": "dropdown.js", "sourceRoot": "", "sources": ["../../../../src/components/dropdown/dropdown.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,SAAS,EACT,IAAI,EAEJ,CAAC,EACD,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EAEL,KAAK,GACN,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AA4BxF,MAAM,OAAO,WAAW;IALxB;QAWW,aAAQ,GAAiB,IAAI,CAAC;QAE9B,qBAAgB,GAAa,KAAK,CAAC;QACnC,iBAAY,GAAkB,OAAO,CAAC;QACtC,WAAM,GAAY,CAAC,CAAC;QACpB,UAAK,GAAG,IAAI,CAAC;QAEtB;;WAEG;QACY,eAAU,GAAgB,OAAO,CAAC;QAEjD;;WAEG;QAC4C,SAAI,GAAa,KAAK,CAAC;QAEtE;;WAEG;QACK,aAAQ,GAAyB,MAAM,CAAC;QAEhD;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QA8GzB,mBAAc,GAAG,GAAS,EAAE;YAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,CAAC,CAAC;QAEM,mBAAc,GAAG,CAAC,EAAe,EAAQ,EAAE;YACjD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACxB,CAAC,CAAC;QAEM,uBAAkB,GAAG,GAAG,EAAE;YAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC;QAEM,gBAAW,GAAG,GAAG,EAAE;YACzB,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAClB,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC;QAEM,eAAU,GAAG,GAAG,EAAE;YACxB,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAChC,CAAC;QACH,CAAC,CAAC;QAEM,uBAAkB,GAAG,CAAC,KAAiB,EAAE,EAAE;YACjD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QAEM,sBAAiB,GAAG,CAAC,KAA0B,EAAE,EAAE;YACzD,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;gBAChG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,cAAc,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC;YACpF,CAAC;QACH,CAAC,CAAC;KA6BH;IAzKC,iBAAiB;QACf,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClG,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9E,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;QAED,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC9D,CAAC;IAED,oBAAoB;QAClB,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACjE,CAAC;IAEO,mBAAmB,CAAC,KAA0B;QACpD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,oBAAoB;QAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;YAC5C,aAAa,EAAE,IAAI,CAAC,WAAW;YAC/B,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,CAAC;IAGS,aAAa,CAAC,IAAa;QACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACrC,IAAI,IAAI;YACN,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE,CAAC;gBAC5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,CAAC;IACL,CAAC;IAGS,iBAAiB;QACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAGD,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACzB,CAAC;IAGD,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAGD,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACpB,CAAC;IAGS,kBAAkB,CAAC,MAAe;QAC1C,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC7B,CAAC;QACD,OAAO;IACT,CAAC;IAGS,mBAAmB,CAAC,KAAmB;QAC/C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;gBAClB,MAAM;QACV,CAAC;IACH,CAAC;IAyCD,MAAM;QACJ,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;SACzB,CAAC;QACF,OAAO,CACL,EAAC,IAAI;YACH,6DAAM,IAAI,EAAC,oBAAoB,GAAQ;YACvC,4DACE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EACpC,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAI;oBACd,cAAc,EAAE,IAAI,CAAC,IAAI;iBAC1B,eACU,IAAI,CAAC,QAAQ,EACxB,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EACrC,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE;gBAEnC,4DAAK,KAAK,EAAC,SAAS,EAAC,KAAK,EAAE,aAAa;oBACvC,6DAAM,IAAI,EAAC,kBAAkB,GAAQ,CACjC,CACF;YACL,IAAI,CAAC,UAAU,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,CAC3C,4DAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAQ,CAChF,CACI,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import {\n  Component,\n  Host,\n  ComponentInterface,\n  h,\n  Element,\n  State,\n  Method,\n  Prop,\n  Event,\n  EventEmitter,\n  Watch,\n} from '@stencil/core';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type activeMode = 'hover' | 'click';\nexport type dropVerticalPosition = 'bottom' | 'top' | 'left' | 'right';\nexport type dropHorizontalPosition = 'left' | 'center' | 'right' | 'bottom' | 'top';\n//^^ dropHorizontalPosition: For version 2.0 change to values: \"start\", \"center\", \"end\". ^^//\nexport type subMenuState = 'close' | 'pending' | 'open';\n\nexport type DropdownPostionType =\n  | 'auto'\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom';\n\n@Component({\n  tag: 'bds-dropdown',\n  styleUrl: 'dropdown.scss',\n  shadow: true,\n})\nexport class BdsDropdown implements ComponentInterface {\n  private activatorElement?: Element;\n  private dropElement?: HTMLElement;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() stateOpenSubMenu?: boolean = false;\n  @State() stateSubMenu?: subMenuState = 'close';\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop() public activeMode?: activeMode = 'click';\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop({ mutable: true, reflect: true }) public open?: boolean = false;\n\n  /**\n   * Used to set drop position\n   */\n  @Prop() position?: DropdownPostionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * bdsToggle. Event to return selected date value.\n   */\n  @Event() bdsToggle?: EventEmitter;\n\n  componentWillLoad() {\n    this.activatorElement = this.hostElement.querySelector('[slot=\"dropdown-activator\"]').children[0];\n    this.intoView = getScrollParent(this.hostElement);\n    this.isPositionChanged;\n    if (this.activeMode == 'hover') {\n      this.activatorElement.addEventListener('mouseover', () => this.onMouseOver());\n      this.activatorElement.addEventListener('click', () => this.onMouseOver());\n      this.activatorElement.addEventListener('mouseout', () => this.onMouseOut());\n    } else {\n      this.activatorElement.addEventListener('click', () => this.toggle());\n    }\n  }\n\n  componentDidLoad() {\n    if (this.position != 'auto') {\n      this.centerDropElement(this.position);\n      this.setDefaultPlacement(this.position);\n    } else {\n      this.validatePositionDrop();\n    }\n\n    document.addEventListener('click', this.handleClickOutside);\n  }\n\n  disconnectedCallback() {\n    document.removeEventListener('click', this.handleClickOutside);\n  }\n\n  private setDefaultPlacement(value: DropdownPostionType) {\n    this.dropElement.classList.add(`dropdown__basic__${value}`);\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.hostElement,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.dropElement.classList.add(`dropdown__basic__${positionValue.y}-${positionValue.x}`);\n  }\n\n  @Watch('open')\n  protected isOpenChanged(open: boolean): void {\n    this.bdsToggle.emit({ value: open });\n    if (open)\n      if (this.position != 'auto') {\n        this.setDefaultPlacement(this.position);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('position')\n  protected isPositionChanged(): void {\n    this.setDefaultPlacement(this.position);\n  }\n\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Method()\n  async setOpen() {\n    this.open = true;\n  }\n\n  @Method()\n  async setClose() {\n    this.stateOpenSubMenu = false;\n    clearTimeout(this.delay);\n    this.open = false;\n  }\n\n  @Watch('stateOpenSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n    return;\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: subMenuState): void {\n    switch (state) {\n      case 'open':\n        this.open = true;\n        break;\n      case 'pending':\n        this.open = true;\n        break;\n      case 'close':\n        this.open = false;\n        break;\n    }\n  }\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  private refDropElement = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private onClickCloseButtom = () => {\n    this.open = false;\n  };\n\n  private onMouseOver = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 1;\n    }\n    this.stateOpenSubMenu = true;\n  };\n\n  private onMouseOut = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 0;\n      this.stateOpenSubMenu = false;\n    }\n  };\n\n  private handleClickOutside = (event: MouseEvent) => {\n    if (this.open && !this.hostElement.contains(event.target as Node)) {\n      this.setClose();\n    }\n  };\n\n  private centerDropElement = (value: DropdownPostionType) => {\n    const arrayPosition = value.split('-');\n    if ((arrayPosition[0] == 'left' || arrayPosition[0] == 'right') && arrayPosition[1] == 'center') {\n      this.dropElement.style.top = `calc(50% - ${this.dropElement.offsetHeight / 2}px)`;\n    }\n  };\n\n  render() {\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n    return (\n      <Host>\n        <slot name=\"dropdown-activator\"></slot>\n        <div\n          ref={(el) => this.refDropElement(el)}\n          class={{\n            dropdown: true,\n            dropdown__open: this.open,\n          }}\n          data-test={this.dataTest}\n          onMouseOver={() => this.onMouseOver()}\n          onMouseOut={() => this.onMouseOut()}\n        >\n          <div class=\"content\" style={zIndexSubmenu}>\n            <slot name=\"dropdown-content\"></slot>\n          </div>\n        </div>\n        {this.activeMode !== 'hover' && this.open && (\n          <div class={{ outzone: true }} onClick={() => this.onClickCloseButtom()}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"]}