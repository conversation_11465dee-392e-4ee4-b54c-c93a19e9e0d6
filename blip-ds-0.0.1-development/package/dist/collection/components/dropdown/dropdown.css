/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  position: relative;
  width: fit-content;
}

:host(.is_child_drop) {
  display: block;
  width: 100%;
}

.dropdown {
  position: absolute;
  pointer-events: none;
  padding: 2px;
  background-color: var(--color-surface-0, rgb(255, 255, 255));
  border-radius: 8px;
  box-shadow: 0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  min-width: 240px;
  width: max-content;
  opacity: 0;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  transition: opacity 0.5s;
  z-index: 90000;
}
.dropdown__open {
  pointer-events: auto;
  opacity: 1;
}
.dropdown__basic__top-center {
  bottom: calc(100% + 16px);
  left: calc(50% - 122px);
}
.dropdown__basic__top-left {
  bottom: calc(100% + 16px);
  left: 0;
}
.dropdown__basic__top-right {
  bottom: calc(100% + 16px);
  right: 0;
}
.dropdown__basic__bottom-center {
  top: calc(100% + 16px);
  left: calc(50% - 122px);
}
.dropdown__basic__bottom-right {
  top: calc(100% + 16px);
  right: 0;
}
.dropdown__basic__bottom-left {
  top: calc(100% + 16px);
  left: 0;
}
.dropdown__basic__right-center {
  right: calc(100% + 8px);
}
.dropdown__basic__right-top {
  right: calc(100% + 8px);
  top: 0;
}
.dropdown__basic__right-bottom {
  right: calc(100% + 8px);
  bottom: 0;
}
.dropdown__basic__left-center {
  left: calc(100% + 8px);
}
.dropdown__basic__left-top {
  left: calc(100% + 8px);
  top: 0;
}
.dropdown__basic__left-bottom {
  left: calc(100% + 8px);
  bottom: 0;
}
.dropdown:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 8px;
  box-shadow: var(--color-surface-0, rgb(255, 255, 255)) 0px 0px 0px 2px inset;
  pointer-events: none;
}

.outzone {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 80000;
}