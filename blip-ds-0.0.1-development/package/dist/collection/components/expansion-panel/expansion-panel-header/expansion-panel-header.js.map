{"version": 3, "file": "expansion-panel-header.js", "sourceRoot": "", "sources": ["../../../../../src/components/expansion-panel/expansion-panel-header/expansion-panel-header.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAsB,IAAI,EAAE,MAAM,eAAe,CAAC;AAO7E,MAAM,OAAO,oBAAoB;IAG/B,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,KAAK,EAAC,QAAQ;gBACjB,8DAAQ,CACJ;YACN,iEAAU,GAAG,EAAC,GAAG,EAAC,OAAO,EAAC,OAAO,IAC9B,IAAI,CAAC,IAAI,CACD,CACN,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, ComponentInterface, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel-header',\n  styleUrl: 'expansion-panel-header.scss',\n  shadow: true,\n})\nexport class ExpansionPanelHeader implements ComponentInterface {\n  @Prop() text?: string;\n\n  render() {\n    return (\n      <Host>\n        <div class=\"header\">\n          <slot />\n        </div>\n        <bds-typo tag=\"p\" variant=\"fs-12\">\n          {this.text}\n        </bds-typo>\n      </Host>\n    );\n  }\n}\n"]}