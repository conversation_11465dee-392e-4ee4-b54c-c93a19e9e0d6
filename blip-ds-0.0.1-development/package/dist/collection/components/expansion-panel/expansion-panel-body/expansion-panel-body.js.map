{"version": 3, "file": "expansion-panel-body.js", "sourceRoot": "", "sources": ["../../../../../src/components/expansion-panel/expansion-panel-body/expansion-panel-body.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAsB,MAAM,eAAe,CAAC;AAO7E,MAAM,OAAO,kBAAkB;IAL/B;QAMU,SAAI,GAAa,KAAK,CAAC;QACvB,SAAI,GAAY,IAAI,CAAC;KAoB9B;IAlBC,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,KAAK,EAAC,mBAAmB,EAAC,MAAM,EAAE,IAAI,CAAC,IAAI;gBAC9C,4DAAK,KAAK,EAAC,WAAW;oBACpB,8DAAa;oBACZ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACX,WAAK,KAAK,EAAC,MAAM;wBACf,aAAI,IAAI,CAAC,IAAI,CAAK,CACd,CACP,CAAC,CAAC,CAAC,CACF,WAAK,KAAK,EAAC,QAAQ,GAAO,CAC3B,CACG,CACF,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, Prop, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel-body',\n  styleUrl: 'expansion-panel-body.scss',\n  shadow: true,\n})\nexport class ExpansionPanelBody implements ComponentInterface {\n  @Prop() open?: boolean = false;\n  @Prop() text?: string = null;\n\n  render() {\n    return (\n      <Host>\n        <div class=\"expansion-content\" hidden={this.open}>\n          <div class=\"with-line\">\n            <slot></slot>\n            {this.text ? (\n              <div class=\"text\">\n                <p>{this.text}</p>\n              </div>\n            ) : (\n              <div class=\"circle\"></div>\n            )}\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}