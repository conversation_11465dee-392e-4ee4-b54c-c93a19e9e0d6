/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
}

.expansion-content {
  display: flex;
  padding-left: 22px;
}
.expansion-content .with-line {
  border-left: 2px solid #e3e3e3;
  padding-left: 9px;
  padding-top: 16px;
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  width: 100%;
}
.expansion-content .circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #e3e3e3;
  position: relative;
  right: 14px;
}
.expansion-content .text {
  right: 28px;
  width: 37px;
  height: 30px;
  position: relative;
  background: #d4d4d4;
  justify-content: center;
  border-radius: 8px;
  font-family: "Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  display: flex;
  align-items: center;
  color: #202c44;
  top: 10px;
}