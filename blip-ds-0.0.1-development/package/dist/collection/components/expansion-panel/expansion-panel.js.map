{"version": 3, "file": "expansion-panel.js", "sourceRoot": "", "sources": ["../../../../src/components/expansion-panel/expansion-panel.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAsB,MAAM,eAAe,CAAC;AAOvE,MAAM,OAAO,cAAc;IACzB,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,8DAAa,CACR,CACR,CAAC;IACJ,CAAC;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, h, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel',\n  styleUrl: 'expansion-panel.scss',\n  shadow: true,\n})\nexport class ExpansionPanel implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"]}