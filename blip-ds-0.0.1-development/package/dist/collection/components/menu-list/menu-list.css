/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
.menu-list {
  display: flex;
  width: fit-content;
  box-shadow: 0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));
  height: 56px;
  border-radius: 8px;
}
.menu-list bds-menu-list-item + bds-menu-list-item {
  border-left: 1px solid #d4d4d4;
}
.menu-list__left {
  width: 7px;
  height: 56px;
  border-radius: 8px 0px 0px 8px;
  background-color: #f6f6f6;
}
.menu-list__right {
  width: 7px;
  height: 56px;
  border-radius: 0px 8px 8px 0px;
  background-color: #f6f6f6;
}