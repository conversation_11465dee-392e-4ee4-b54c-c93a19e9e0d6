{"version": 3, "file": "loading-page.js", "sourceRoot": "", "sources": ["../../../../src/components/loading-page/loading-page.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAChE,OAAO,aAAa,MAAM,qCAAqC,CAAC;AAOhE,MAAM,OAAO,UAAU;IALvB;QAQE;;WAEG;QACK,aAAQ,GAAY,IAAI,CAAC;QAMjC,qDAAqD;QACrD,cAAS,GAAG,CAAC,UAAkB,EAAE,EAAE;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1C,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB,CAAC;YAErC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC,SAAS,CAAC;QACvB,CAAC,CAAC;QAEF,kBAAa,GAAG,GAAG,EAAE;YACnB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC;KAWH;IA/BC,iBAAiB;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAoBD,MAAM;QACJ,OAAO,CACL,EAAC,IAAI;YACH,4DAAK,KAAK,EAAC,mBAAmB,eAAY,IAAI,CAAC,QAAQ;gBACrD,4DAAK,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,GAAQ,CAClE,CACD,CACR,CAAC;IACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Host, Prop, State, h } from '@stencil/core';\nimport messageBallon from '../../assets/svg/message-ballon.svg';\n\n@Component({\n  tag: 'bds-loading-page',\n  styleUrl: 'loading-page.scss',\n  shadow: true,\n})\nexport class BdsLoading {\n  @State() private svgContent?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setSvgContent();\n  }\n\n  /**Function to transform the svg in a div element. */\n  formatSvg = (svgContent: string) => {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    return div.innerHTML;\n  };\n\n  setSvgContent = () => {\n    const innerHTML = messageBallon;\n\n    const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));\n    this.svgContent = this.formatSvg(svg);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div class=\"loading-container\" data-test={this.dataTest}>\n          <div class={{ page_loading: true }} innerHTML={this.svgContent}></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"]}