/*
  Font family is Nunito Sans. Its fallback is Helvetica
*/
/*
  Font sizes have been converted to rem considering a base size of 16px.
*/
/** Customs */
/** Animations */
/** Aligns */
/** Scoll Bar */
/** Hover & Pressed */
/*
*  Z-Index
*  
*  The z-index CSS property sets the z-order of a positioned element and its descendants or flex items. 
*  Overlapping elements with a larger z-index cover those with a smaller one.
*  
*  REF: https://developer.mozilla.org/en-US/docs/Web/CSS/z-index
*  REF: https://www.smashingmagazine.com/2019/04/z-index-component-based-web-application/  
*/
/**
* Define media query values
*/
:host {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 999;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--color-surface-4, rgb(20, 20, 20));
  opacity: 0.65;
  mix-blend-mode: multiply;
}

.page_loading {
  animation: growUp 0.8s ease-in-out infinite;
  width: 116px;
  height: 128px;
  color: var(--color-surface-1, rgb(246, 246, 246));
}

@keyframes growUp {
  from {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  60% {
    opacity: 1;
  }
  to {
    transform: scale(1.6);
    opacity: 0;
  }
}