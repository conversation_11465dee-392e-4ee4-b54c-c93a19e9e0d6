{"version": 3, "file": "pt_BR.js", "sourceRoot": "", "sources": ["../../../../../src/components/rict-text/languages/pt_BR.tsx"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,OAAO,GAAG;IACrB;QACE,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,YAAY;QACvB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,oBAAoB;QAChC,YAAY,EAAE,mBAAmB;QACjC,WAAW,EAAE,mBAAmB;QAChC,cAAc,EAAE,oBAAoB;QACpC,YAAY,EAAE,gBAAgB;QAC9B,KAAK,EAAE,SAAS;QAChB,EAAE,EAAE,UAAU;QACd,EAAE,EAAE,UAAU;QACd,EAAE,EAAE,UAAU;QACd,EAAE,EAAE,UAAU;QACd,EAAE,EAAE,UAAU;QACd,EAAE,EAAE,UAAU;QACd,gBAAgB,EAAE,mBAAmB;QACrC,MAAM,EAAE,UAAU;KACnB;CACF,CAAC", "sourcesContent": ["export const ptTerms = [\n  {\n    bold: 'Negrito',\n    italic: 'It<PERSON>lico',\n    strike: '<PERSON><PERSON><PERSON>',\n    underline: 'Sublin<PERSON>o',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: '<PERSON><PERSON><PERSON> à esquerda',\n    align_center: '<PERSON><PERSON>ar ao centro',\n    align_right: 'Alinhar à direita',\n    unordered_list: 'Lista não ordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Citação',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpar formatação',\n    expand: 'Expandir',\n  },\n];\n"]}