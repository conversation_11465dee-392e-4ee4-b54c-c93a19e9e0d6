{"version": 3, "file": "createOverlayComponent.js", "sourceRoot": "", "sources": ["../../src/react-component-lib/createOverlayComponent.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,QAAQ,MAAM,WAAW,CAAC;AAGjC,OAAO,EAA4B,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAgB/G,MAAM,CAAC,MAAM,sBAAsB,GAAG,CACpC,OAAe,EACf,UAA8D,EAC9D,aAAmB,EACnB,EAAE;IACF,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAE5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC9C,MAAM,mBAAmB,GAAG,KAAK,WAAW,YAAY,CAAC;IACzD,MAAM,mBAAmB,GAAG,KAAK,WAAW,YAAY,CAAC;IACzD,MAAM,oBAAoB,GAAG,KAAK,WAAW,aAAa,CAAC;IAC3D,MAAM,oBAAoB,GAAG,KAAK,WAAW,aAAa,CAAC;IAO3D,IAAI,YAAY,GAAG,KAAK,CAAC;IAEzB,MAAM,OAAQ,SAAQ,KAAK,CAAC,SAAgB;QAI1C,YAAY,KAAY;YACtB,KAAK,CAAC,KAAK,CAAC,CAAC;YAJf;;;;;eAAsB;YACtB;;;;;eAAoB;YAIlB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,CAAC;gBACpC,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,KAAK,WAAW;YACpB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,iBAAiB;YACf,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,oBAAoB;YAClB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;QAED,aAAa,CAAC,KAA2C;YACvD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACxC,CAAC;QAED,qBAAqB,CAAC,SAAgB;YAEpC,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBACzF,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAEK,kBAAkB,CAAC,SAAgB;;gBACvC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACnD,CAAC;gBAED,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;oBACzE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC1B,CAAC;gBACD,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;oBAC1F,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC7B,YAAY,GAAG,KAAK,CAAC;oBAOrB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;SAAA;QAEK,OAAO,CAAC,SAAiB;;gBAC7B,MAAM,KAA4F,IAAI,CAAC,KAAK,EAAtG,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,OAA0B,EAArB,MAAM,cAAvF,wFAAyF,CAAa,CAAC;gBAC7G,MAAM,YAAY,mCACb,MAAM,KACT,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,EAC5B,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,aAAa,EACzC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAc,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAChG,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAc,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EACnG,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAc,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,GACpG,CAAC;gBAEF,IAAI,CAAC,OAAO,GAAG,MAAM,UAAU,CAAC,MAAM,iCACjC,YAAY,KACf,SAAS,EAAE,IAAI,CAAC,EAAE,EAClB,cAAc,EAAE,EAAE,IAClB,CAAC;gBAEH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9C,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;gBAEnD,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC/B,CAAC;SAAA;QAED,MAAM;YAMJ,OAAO,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACxG,CAAC;KACF;IAED,OAAO,KAAK,CAAC,UAAU,CAAqB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACzD,OAAO,oBAAC,OAAO,oBAAM,KAAa,IAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAC5D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC"}