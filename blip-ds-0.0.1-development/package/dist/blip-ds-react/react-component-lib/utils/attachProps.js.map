{"version": 3, "file": "attachProps.js", "sourceRoot": "", "sources": ["../../../src/react-component-lib/utils/attachProps.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,QAAQ,CAAC;AAEzC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,IAAiB,EAAE,QAAa,EAAE,WAAgB,EAAE,EAAE,EAAE;IAElF,IAAI,IAAI,YAAY,OAAO,EAAE,CAAC;QAE5B,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,SAAS,KAAK,EAAE,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrC,IACE,IAAI,KAAK,UAAU;gBACnB,IAAI,KAAK,OAAO;gBAChB,IAAI,KAAK,KAAK;gBACd,IAAI,KAAK,OAAO;gBAChB,IAAI,KAAK,WAAW;gBACpB,IAAI,KAAK,cAAc,EACvB,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;gBAClE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAExE,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;oBACnC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACL,IAAY,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,QAAQ,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAC1B,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,SAAuB,EAAE,QAAa,EAAE,QAAa,EAAE,EAAE;IACpF,MAAM,YAAY,GAAW,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;IAClE,MAAM,YAAY,GAAW,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;IAElE,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;IAC7C,MAAM,mBAAmB,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACpF,MAAM,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/E,MAAM,eAAe,GAAa,EAAE,CAAC;IAGrC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;QACtC,IAAI,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAE1C,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnC,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAE7C,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC,CAAC;AAKF,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,eAAuB,EAAE,EAAE;IACjE,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,aAAa;YAChB,OAAO,UAAU,CAAC;IACtB,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AAMF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,eAAuB,EAAE,EAAE;IAC1D,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QACN,MAAM,SAAS,GAAG,IAAI,GAAG,uBAAuB,CAAC,eAAe,CAAC,CAAC;QAClE,IAAI,WAAW,GAAG,SAAS,IAAI,QAAQ,CAAC;QAExC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC9C,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC3C,WAAW,GAAG,OAAQ,OAAe,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC;QAClE,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CACvB,IAAiF,EACjF,SAAiB,EACjB,eAAmC,EACnC,EAAE;IACF,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACzD,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;IAG9C,IAAI,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;IACvD,CAAC;IAGD,IAAI,CAAC,gBAAgB,CACnB,SAAS,EACT,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,OAAO,CAAC,CAAQ;QAChD,IAAI,eAAe,EAAE,CAAC;YACpB,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,GAA4B,EAAE,EAAE;IAClD,MAAM,GAAG,GAAG,IAAI,GAAG,EAAkB,CAAC;IACrC,GAAgB,CAAC,OAAO,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC"}