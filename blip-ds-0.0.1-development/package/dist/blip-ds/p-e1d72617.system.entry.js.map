{"version": 3, "names": ["CardBody", "exports", "class_1", "prototype", "render", "h", "key"], "sources": ["src/components/card/card-body/card-body.tsx"], "sourcesContent": ["import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-body',\n  shadow: true,\n})\nexport class CardBody implements ComponentInterface {\n  render() {\n    return (\n      <bds-grid>\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "mappings": "8IAMaA,EAAQC,EAAA,2B,wBACnBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAAA,YAAAC,IAAA,4CACED,EAAQ,QAAAC,IAAA,6C,WAJK,G", "ignoreList": []}