{"version": 3, "file": "bds-step.entry.esm.js", "sources": ["src/components/stepper/step/step.scss?tag=bds-step&encapsulation=shadow", "src/components/stepper/step/step.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n@use '../../../globals/theme/color-legacy' as *;\n\n:host {\n  padding: 8px;\n\n  .step {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    &__content {\n      display: flex;\n      align-items: center;\n\n      &--completed {\n        color: $color-content-disable;\n      }\n\n      &--active {\n        color: $color-primary;\n        font-weight: bold;\n      }\n\n      &--pointer {\n        cursor: pointer;\n      }\n\n      &--disabled {\n        cursor: no-drop;\n      }\n\n      &__ellipse {\n        display: inline-flex;\n        justify-content: center;\n        border-radius: 50%;\n        background: $color-content-default;\n        margin-right: 4px;\n        min-width: 24px;\n        min-height: 24px;\n\n        & bds-typo,\n        bds-icon {\n          color: $color-surface-0;\n        }\n\n        &--completed {\n          background: $color-content-ghost;\n          & bds-typo,\n          bds-icon {\n            color: $color-surface-0;\n          }\n        }\n\n        &--active {\n          background: $color-surface-primary;\n          & bds-typo,\n          bds-icon {\n            color: $color-content-bright;\n          }\n        }\n\n        &--disabled {\n          background: $color-content-ghost;\n          & bds-typo,\n          bds-icon {\n            color: $color-surface-0;\n          }\n        }\n      }\n\n      &__text {\n        &--completed {\n          color: $color-content-ghost;\n        }\n\n        &--active {\n          color: $color-content-default;\n        }\n\n        &--disabled {\n          color: $color-content-ghost;\n        }\n      }\n    }\n  }\n\n  @media (max-width: $sm-screen) {\n    display: flex;\n    flex: inherit;\n  }\n}\n", "import { Component, ComponentInterface, h, Prop, Element } from '@stencil/core';\n@Component({\n  tag: 'bds-step',\n  styleUrl: 'step.scss',\n  shadow: true,\n})\nexport class BdsStep implements ComponentInterface {\n  @Element() el: HTMLBdsToastElement;\n  /**\n   * Used to define the last step component on the list\n   */\n  @Prop() last?: boolean = false;\n\n  /**\n   * Used to complete the step\n   */\n  @Prop() completed?: boolean = false;\n\n  /**\n   * Used to set the step as active\n   */\n  @Prop() active?: boolean = false;\n\n  /**\n   * Used to set the step as disabled\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Used to set the index of the steps\n   */\n  @Prop() index?: number = 0;\n\n  /**\n   * Used to set cursor pointer on the step (useful to allow clicks on the steps)\n   */\n  @Prop() pointer?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  render() {\n    return (\n      <div class=\"step\">\n        <div\n          class={{\n            step__content: true,\n            'step__content--active': this.active,\n            'step__content--completed': this.completed,\n            'step__content--disabled': this.disabled,\n            'step__content--pointer': this.pointer,\n            'step--last': this.last,\n          }}\n          data-test={this.dataTest}\n        >\n          <div\n            class={{\n              step__content__ellipse: true,\n              'step__content__ellipse--active': this.active,\n              'step__content__ellipse--completed': this.completed,\n              'step__content__ellipse--disabled': this.disabled,\n            }}\n          >\n            {this.completed && <bds-icon name=\"true\"></bds-icon>}\n            {!this.completed && <bds-typo>{this.index + 1}</bds-typo>}\n          </div>\n          <bds-typo\n            variant=\"fs-16\"\n            class={{\n              step__content__text: true,\n              'step__content__text--completed': this.completed && !this.active,\n              'step__content__text--active': this.active,\n              'step__content__text--disabled': this.disabled,\n            }}\n            bold={this.active ? 'bold' : 'regular'}\n          >\n            <slot />\n          </bds-typo>\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,OAAO,GAAG,g6DAAg6D;;MCMn6D,OAAO,GAAA,MAAA;AALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOE;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;AAE9B;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AAEnC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAEhC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAElC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,CAAC;AAE1B;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA0CjC;IAzCC,MAAM,GAAA;QACJ,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,MAAM,EAAA,EACf,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;gBACnB,uBAAuB,EAAE,IAAI,CAAC,MAAM;gBACpC,0BAA0B,EAAE,IAAI,CAAC,SAAS;gBAC1C,yBAAyB,EAAE,IAAI,CAAC,QAAQ;gBACxC,wBAAwB,EAAE,IAAI,CAAC,OAAO;gBACtC,YAAY,EAAE,IAAI,CAAC,IAAI;AACxB,aAAA,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAExB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,sBAAsB,EAAE,IAAI;gBAC5B,gCAAgC,EAAE,IAAI,CAAC,MAAM;gBAC7C,mCAAmC,EAAE,IAAI,CAAC,SAAS;gBACnD,kCAAkC,EAAE,IAAI,CAAC,QAAQ;aAClD,EAAA,EAEA,IAAI,CAAC,SAAS,IAAI,iEAAU,IAAI,EAAC,MAAM,EAAY,CAAA,EACnD,CAAC,IAAI,CAAC,SAAS,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAW,IAAI,CAAC,KAAK,GAAG,CAAC,CAAY,CACrD,EACN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAC,OAAO,EACf,KAAK,EAAE;AACL,gBAAA,mBAAmB,EAAE,IAAI;gBACzB,gCAAgC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM;gBAChE,6BAA6B,EAAE,IAAI,CAAC,MAAM;gBAC1C,+BAA+B,EAAE,IAAI,CAAC,QAAQ;AAC/C,aAAA,EACD,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,SAAS,EAAA,EAEtC,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACC,CACP,CACF;;;;;;;;"}