import{r as e,h as b,H as s}from"./p-C3J6Z5OX.js";const o=".sc-bds-table-h{width:100%;border-radius:8px;background-color:var(--color-surface-1, rgb(246, 246, 246));border:1px solid var(--color-border-3, rgba(0, 0, 0, 0.06))}.bds-table.sc-bds-table{display:table;width:100%;border-spacing:0px;-webkit-box-sizing:border-box;box-sizing:border-box;border-collapse:collapse}.scrollable.sc-bds-table-h{overflow-x:auto}.dense-table.sc-bds-table-h{height:-webkit-fit-content;height:-moz-fit-content;height:fit-content}";const t=class{constructor(b){e(this,b)}render(){return b(s,{key:"1dafc43ed87db9fa30a52fc0b9ff83105ef4ae31",class:{scrollable:this.scrollable,"dense-table":this.denseTable}},b("div",{key:"bfc3ab12650cc4e1f35d37c800d37677b842fbe3",class:"bds-table"},b("slot",{key:"98966e37c74a8f656e0a15c9b617e3fb7fb5ebde"})))}};t.style=o;export{t as bds_table};
//# sourceMappingURL=p-629d4e52.entry.js.map