var __awaiter=this&&this.__awaiter||function(t,e,i,o){function n(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(o.next(t))}catch(t){r(t)}}function a(t){try{c(o["throw"](t))}catch(t){r(t)}}function c(t){t.done?i(t.value):n(t.value).then(s,a)}c((o=o.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var i={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},o,n,r,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(t){return function(e){return c([t,e])}}function c(a){if(o)throw new TypeError("Generator is already executing.");while(s&&(s=0,a[0]&&(i=0)),i)try{if(o=1,n&&(r=a[0]&2?n["return"]:a[0]?n["throw"]||((r=n["return"])&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;if(n=0,r)a=[a[0]&2,r.value];switch(a[0]){case 0:case 1:r=a;break;case 4:i.label++;return{value:a[1],done:false};case 5:i.label++;n=a[1];a=[0];continue;case 7:a=i.ops.pop();i.trys.pop();continue;default:if(!(r=i.trys,r=r.length>0&&r[r.length-1])&&(a[0]===6||a[0]===2)){i=0;continue}if(a[0]===3&&(!r||a[1]>r[0]&&a[1]<r[3])){i.label=a[1];break}if(a[0]===6&&i.label<r[1]){i.label=r[1];r=a;break}if(r&&i.label<r[2]){i.label=r[2];i.ops.push(a);break}if(r[2])i.ops.pop();i.trys.pop();continue}a=e.call(t,i)}catch(t){a=[6,t];n=0}finally{o=r=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js","./p-KsAJij7V.system.js"],(function(t){"use strict";var e,i,o,n,r,s,a;return{setters:[function(t){e=t.r;i=t.c;o=t.h;n=t.H;r=t.a},function(t){s=t.g;a=t.p}],execute:function(){var c=':host{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;width:100%;max-width:100%;max-height:100%}:host input,:host textarea{-webkit-box-shadow:inherit;box-shadow:inherit}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-moz-placeholder,:host textarea::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input:-ms-input-placeholder,:host textarea:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-ms-input-placeholder,:host textarea::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::placeholder,:host textarea::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 9px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__text:-moz-placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-primary .input__container__text:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-primary .input__container__text:placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger .input__container__text:-moz-placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-danger .input__container__text:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-danger .input__container__text:placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__text:-moz-placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-success .input__container__text:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-success .input__container__text:placeholder-shown{color:var(--color-content-ghost, rgb(140, 140, 140))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 8px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;margin-right:8px;padding:2.5px}.input__icon--large{padding:4px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__text::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-disable, rgb(89, 89, 89));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__text::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140))}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap;gap:8px}.input__container__wrapper .inside-input-left{display:inline}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;width:100%;resize:none;cursor:inherit;-ms-flex-negative:99999;flex-shrink:99999}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text__chips{width:auto;min-width:216px;max-width:216px}:host{display:block}.select{position:relative;outline:none;overflow:hidden}.select__icon{color:var(--color-content-ghost, rgb(140, 140, 140));display:-ms-flexbox;display:flex}.select__icon bds-icon{margin-left:10px}.select .icon-hidden{visibility:hidden}.select__options{display:grid;background:var(--color-surface-0, rgb(255, 255, 255));width:100%;max-height:200px;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:absolute;left:0;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));overflow-y:auto;z-index:2;pointer-events:none;opacity:0}.select__options::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.select__options::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.select__options ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column}.select__options .selection-title{-ms-flex-order:-2;order:-2;width:100%;padding:8px 16px;-webkit-box-sizing:border-box;box-sizing:border-box}.select__options .select-all{-ms-flex-order:-3;order:-3;padding:8px 8px 8px 12px;-webkit-box-sizing:border-box;box-sizing:border-box;display:-ms-flexbox;display:flex;gap:8px;-ms-flex-align:center;align-items:center;outline:none;-ms-flex-direction:row;flex-direction:row}.select__options .content-divisor{display:block;width:100%;height:1px;background-color:var(--color-surface-1, rgb(246, 246, 246))}.select__options .content-divisor .divisor{display:block;margin:0 16px;height:1px;background-color:var(--color-border-2, rgba(0, 0, 0, 0.16))}.select__options .load-spinner{background-color:var(--color-surface-1, rgb(246, 246, 246));height:200px;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.select__options--open{opacity:1;pointer-events:auto}.select__options--position-top{bottom:calc(100% + 4px)}.select__options--position-bottom{top:calc(100% + 4px)}.select__options .options-checked{-ms-flex-order:-1;order:-1}';var l=t("bds_autocomplete",function(){function t(t){var o=this;e(this,t);this.bdsChange=i(this,"bdsChange");this.bdsSelectedChange=i(this,"bdsSelectedChange");this.bdsMultiselectedChange=i(this,"bdsMultiselectedChange");this.bdsInput=i(this,"bdsInput");this.bdsCancel=i(this,"bdsCancel");this.bdsFocus=i(this,"bdsFocus");this.bdsBlur=i(this,"bdsBlur");this.intoView=null;this.isPressed=false;this.isOpen=false;this.text="";this.textMultiselect="";this.placeholderState=this.placeholder;this.isFocused=false;this.validationDanger=false;this.validationMesage="";this.danger=false;this.success=false;this.disabled=false;this.searchOnlyTitle=true;this.label="";this.icon="";this.placeholder="";this.helperMessage="";this.errorMessage="";this.successMessage="";this.optionsPosition="auto";this.clearIconOnFocus=false;this.dataTest=null;this.loading=false;this.selectionType="single";this.selectionTitle="";this.selectedAll=true;this.refDropdown=function(t){o.dropElement=t};this.refIconDrop=function(t){o.iconDropElement=t};this.refCheckAllInput=function(t){o.checkAllInput=t};this.onFocus=function(){o.isFocused=true;o.isPressed=true;o.bdsFocus.emit()};this.onFocusout=function(){if(!o.isOpen){o.nativeInput.value=o.getText()}};this.onBlur=function(){var t;o.bdsBlur.emit();o.isPressed=false;if(!o.isOpen){o.isFocused=false;o.nativeInput.value=o.getText();if(o.selectionType=="multiple")o.cleanInputSelection()}if(o.selectionType=="multiple"&&((t=o.checkedOptions)===null||t===void 0?void 0:t.length)>0)o.getTextMultiselect(o.checkedOptions)};this.onClickWrapper=function(){o.onFocus();o.toggle();if(o.nativeInput){o.nativeInput.focus()}};this.toggle=function(){if(!o.disabled){o.isOpen=!o.isOpen}};this.getTextFromOption=function(t){var e;if(o.internalOptions){var i=o.internalOptions.find((function(e){return e.value==(t===null||t===void 0?void 0:t.value)}));if(i){return i.label}}return(t===null||t===void 0?void 0:t.titleText)?t.titleText:(e=t===null||t===void 0?void 0:t.innerText)!==null&&e!==void 0?e:""};this.getText=function(){var t=o.childOptions.find((function(t){return t.value==o.value}));return o.getTextFromOption(t)};this.getTextMultiselect=function(t){var e=(t===null||t===void 0?void 0:t.length)>0&&"".concat(t===null||t===void 0?void 0:t.length," selecionados");o.textMultiselect=e};this.handlerMultiselect=function(){o.updateListChecked(o.childOptions);o.nativeInput.value="";o.value=undefined;o.resetFilterOptions();if(o.childOptions.length!=o.checkedOptions.length){setTimeout((function(){o.checkAllInput.checked=false}),10)}};this.handleCheckAll=function(t){var e=t.detail.checked;for(var i=0,n=o.childOptions;i<n.length;i++){var r=n[i];if(e){r.toMark()}else{r.markOff()}}setTimeout((function(){o.updateListChecked(o.childOptions)}),10)};this.updateListChecked=function(t){for(var e=0,i=t;e<i.length;e++){var n=i[e];n.checked?n.classList.add("option-checked"):n.classList.remove("option-checked")}var r=Array.from(t).filter((function(t){return t.checked==true}));var s=r.map((function(t){return{value:t.value,label:t.textContent,checked:t.checked}}));o.checkedOptions=s};this.handler=function(t){var e=t.detail.value;o.value=e;o.toggle()};this.cleanInputSelection=function(){return __awaiter(o,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:if(!!this.disabled)return[3,2];this.value="";this.nativeInput.value="";this.isOpen=false;this.bdsCancel.emit({value:""});return[4,this.resetFilterOptions()];case 1:t.sent();t.label=2;case 2:return[2]}}))}))};this.changedInputValue=function(t){return __awaiter(o,void 0,void 0,(function(){var e;return __generator(this,(function(i){switch(i.label){case 0:e=t.target;if(e){this.value=e.value||""}this.bdsInput.emit(t);if(!this.nativeInput.value)return[3,2];return[4,this.filterOptions(this.nativeInput.value)];case 1:i.sent();return[3,5];case 2:this.value="";if(!this.isOpen)return[3,4];return[4,this.resetFilterOptions()];case 3:i.sent();return[3,5];case 4:this.setTimeoutFilter();i.label=5;case 5:if(this.isOpen===false){this.value=this.getSelectedValue();this.setTimeoutFilter()}return[2]}}))}))}}t.prototype.isOpenChanged=function(t){if(this.positionHeightDrop=="bottom"){this.iconDropElement.name=this.isOpen?"arrow-up":"arrow-down"}else{this.iconDropElement.name=this.isOpen?"arrow-down":"arrow-up"}if(t)if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}};t.prototype.itemSelectedChanged=function(){this.bdsSelectedChange.emit(this.selected)};t.prototype.valueChanged=function(){this.bdsChange.emit({value:this.value==null?this.value:this.value.toString()});for(var t=0,e=this.childOptions;t<e.length;t++){var i=e[t];i.selected=this.value===i.value}this.selected=this.childOptionSelected;this.text=this.getText()};t.prototype.handleWindow=function(t){if(!this.el.contains(t.target)){this.isOpen=false}};t.prototype.changeCheckedOptions=function(){var t;this.placeholderState=this.selectionType==="multiple"?((t=this.checkedOptions)===null||t===void 0?void 0:t.length)===0||this.checkedOptions===null?this.placeholder:"":this.placeholder;this.getTextMultiselect(this.checkedOptions);this.bdsMultiselectedChange.emit({value:this.checkedOptions})};t.prototype.parseOptions=function(){if(this.options){this.resetFilterOptions();try{this.internalOptions=typeof this.options==="string"?JSON.parse(this.options):this.options}catch(t){this.internalOptions=[]}}};t.prototype.changeSelectionType=function(){if(!this.options){for(var t=0,e=this.childOptions;t<e.length;t++){var i=e[t];if(this.selectionType==="multiple"){i.typeOption="checkbox";i.addEventListener("optionChecked",this.handlerMultiselect)}else{i.typeOption="default";i.selected=this.value===i.value;i.addEventListener("optionSelected",this.handler)}}}};t.prototype.componentWillLoad=function(){this.intoView=s(this.el);this.options&&this.parseOptions()};t.prototype.componentDidLoad=function(){if(!this.options){for(var t=0,e=this.childOptions;t<e.length;t++){var i=e[t];if(this.selectionType==="multiple"){i.typeOption="checkbox";i.addEventListener("optionChecked",this.handlerMultiselect)}else{i.typeOption="default";i.selected=this.value===i.value;i.addEventListener("optionSelected",this.handler)}}}this.text=this.getText();if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}};t.prototype.setDefaultPlacement=function(t){if(t=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}};t.prototype.validatePositionDrop=function(){var t=a({actionElement:this.el,changedElement:this.dropElement,intoView:this.intoView});this.positionHeightDrop=t.y;if(t.y=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}};Object.defineProperty(t.prototype,"childOptions",{get:function(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option")):Array.from(this.el.querySelectorAll("bds-select-option"))},enumerable:false,configurable:true});Object.defineProperty(t.prototype,"childOptionSelected",{get:function(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option")).find((function(t){return t.selected})):Array.from(this.el.querySelectorAll("bds-select-option")).find((function(t){return t.selected}))},enumerable:false,configurable:true});t.prototype.keyPressWrapper=function(t){var e,i,o,n;switch(t.key){case"Enter":this.toggle();break;case"ArrowDown":if(!this.disabled){this.isOpen=true}if(this.childOptionSelected){this.value=(e=this.childOptionSelected.nextSibling)===null||e===void 0?void 0:e.value;return}this.value=(i=this.el.firstElementChild)===null||i===void 0?void 0:i.value;break;case"ArrowUp":if(this.childOptionSelected){this.value=(o=this.childOptionSelected.previousSibling)===null||o===void 0?void 0:o.value;return}this.value=(n=this.el.lastElementChild)===null||n===void 0?void 0:n.value;break}};t.prototype.cleanMultipleSelection=function(){return __awaiter(this,void 0,void 0,(function(){var t,e,i,o;return __generator(this,(function(n){if(this.selectionType==="multiple"&&((t=this.checkedOptions)===null||t===void 0?void 0:t.length)>0){for(e=0,i=this.childOptions;e<i.length;e++){o=i[e];o.checked=false;o.classList.remove("option-checked")}this.checkedOptions=[];this.checkAllInput.checked=false;this.nativeInput.value="";this.value=undefined;this.resetFilterOptions()}else{this.cleanInputSelection()}return[2]}))}))};t.prototype.setTimeoutFilter=function(){var t=this;setTimeout((function(){t.resetFilterOptions()}),500)};t.prototype.filterOptions=function(t){return __awaiter(this,void 0,void 0,(function(){var e,i,o,n,r;return __generator(this,(function(s){switch(s.label){case 0:if(!!t)return[3,2];return[4,this.resetFilterOptions()];case 1:s.sent();s.label=2;case 2:for(e=0,i=this.childOptions;e<i.length;e++){o=i[e];n=this.searchOnlyTitle?this.getTextFromOption(o).toLowerCase():o.textContent.toLowerCase();r=t.toLowerCase();n.includes(r)?o.removeAttribute("invisible"):o.setAttribute("invisible","invisible")}return[2]}}))}))};t.prototype.resetFilterOptions=function(){return __awaiter(this,void 0,void 0,(function(){var t,e,i,o;return __generator(this,(function(n){t=this.childOptions;for(e=0,i=t;e<i.length;e++){o=i[e];o.removeAttribute("invisible")}return[2]}))}))};t.prototype.getSelectedValue=function(){var t;return(t=this.childOptionSelected)===null||t===void 0?void 0:t.value};t.prototype.renderIcon=function(){return this.icon&&o("div",{class:{input__icon:true,"input__icon--large":!!this.label}},o("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))};t.prototype.renderLabel=function(){return this.label&&o("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},o("bds-typo",{variant:"fs-12",bold:"bold"},this.label))};t.prototype.renderMessage=function(){var t=this.danger?"error":this.success?"checkball":"info";var e=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!e&&this.validationDanger)e=this.validationMesage;var i=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(e){return o("div",{class:i,part:"input__message"},o("div",{class:"input__message__icon"},o("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),o("bds-typo",{class:"input__message__text",variant:"fs-12"},e))}return undefined};t.prototype.render=function(){var t=this;var e,i;return o(n,{key:"47da2306938ead7f3213d58f83e97de5151ca080","aria-disabled":this.disabled?"true":null},o("div",{key:"2f41a9eecd8d97614d55664ea7303a7e6b08df3c",class:{input:true,select:true,"input--state-primary":!this.danger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":this.isPressed},onClick:this.onClickWrapper},this.renderIcon(),o("div",{key:"d4a2d0601d51d230890b4d174b7528a957f8b983",class:"input__container",tabindex:"0",onFocusout:this.onFocusout},this.renderLabel(),o("div",{key:"143fc2050114825c17c17a4b8ccb5a7f61e27390",class:{input__container__wrapper:true}},((e=this.textMultiselect)===null||e===void 0?void 0:e.length)>0&&o("bds-typo",{key:"f5e0c78723a41d3062d0aaa499b2325ae11ab024",variant:"fs-14",class:"inside-input-left"},this.textMultiselect),o("input",{key:"945e986b720854b838f86e4ea1ff55e1b4e729a0",class:{input__container__text:true},ref:function(e){return t.nativeInput=e},disabled:this.disabled,onBlur:this.onBlur,onFocus:this.onFocus,onInput:this.changedInputValue,placeholder:this.placeholderState,type:"text",value:this.text,"data-test":this.dataTest,onKeyDown:this.keyPressWrapper.bind(this)}))),o("div",{key:"312eaafa0abf780aba1bed1d168dd9cc34ad6d60",class:"select__icon"},o("bds-icon",{key:"ce8c766e9407b13c84270a20ee11017ea3cde6fc",size:"small",name:"error",theme:"solid",onClick:this.cleanInputSelection,class:{"icon-hidden":this.clearIconOnFocus&&(!this.isFocused||!this.isOpen)||!this.value}}),o("bds-icon",{key:"7f9bb1b87a173c68fac35d444abeb6bf5efeca36",ref:function(e){return t.refIconDrop(e)},size:"small",color:"inherit"}))),this.renderMessage(),this.loading?o("div",{ref:function(e){return t.refDropdown(e)},class:{select__options:true,"select__options--open":this.isOpen}},o("bds-loading-spinner",{class:"load-spinner",size:"small"})):o("div",{ref:function(e){return t.refDropdown(e)},class:{select__options:true,"select__options--open":this.isOpen}},this.selectionTitle&&this.selectionType=="multiple"&&o("bds-typo",{class:"selection-title",variant:"fs-10",bold:"bold"},this.selectionTitle),this.selectionType=="multiple"&&this.selectedAll&&o("bds-checkbox",{ref:this.refCheckAllInput,refer:"refer-multiselect",label:"Selecionar Todos",name:"chack-all",class:"select-all",onBdsChange:function(e){return t.handleCheckAll(e)}}),((i=this.checkedOptions)===null||i===void 0?void 0:i.length)>0&&o("span",{class:"content-divisor"},o("span",{class:"divisor"})),this.internalOptions?this.internalOptions.map((function(e,i){return o("bds-select-option",{onOptionSelected:t.handler,onOptionChecked:t.handlerMultiselect,selected:t.value===e.value,value:e.value,key:i,bulkOption:e.bulkOption,status:e.status,"type-option":t.selectionType=="multiple"?"checkbox":"default"},e.label)})):o("slot",null)))};Object.defineProperty(t.prototype,"el",{get:function(){return r(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{isOpen:["isOpenChanged"],selected:["itemSelectedChanged"],value:["valueChanged"],checkedOptions:["changeCheckedOptions"],options:["parseOptions"],selectionType:["changeSelectionType"]}},enumerable:false,configurable:true});return t}());l.style=c}}}));
//# sourceMappingURL=p-fad63102.system.entry.js.map