{"version": 3, "file": "p-DLWLbAMz.system.js", "sources": ["src/components/alert/alert-actions/alert-actions.scss?tag=bds-alert-actions&encapsulation=shadow", "src/components/alert/alert-actions/alert-actions.tsx"], "sourcesContent": [".alert__actions {\n  width: 100%;\n  display: inline-flex;\n  justify-content: flex-end;\n  align-items: center;\n  position: relative;\n  padding: 12px 16px;\n  box-sizing: border-box;\n\n  ::slotted(bds-button:nth-child(1)) {\n    margin-right: 16px !important;\n  }\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-alert-actions',\n  styleUrl: 'alert-actions.scss',\n  shadow: true,\n})\nexport class AlertActions implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"alert__actions\">\n        <slot />\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;MAAA,MAAM,eAAe,GAAG,wUAAwU;;YCOnV,YAAY,gCAAA,MAAA;;;;UACvB,MAAM,GAAA;cACJ,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAA,EACzB,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACJ;;;;;;;;;;;"}