{"version": 3, "names": ["colorLetter", "value", "color", "avatar<PERSON>s", "BdsAvatar", "exports", "class_1", "hostRef", "_this", "this", "typoSize", "iconSize", "name", "thumbnail", "size", "upload", "openUpload", "ellipsis", "dataTest", "handleOpenUpload", "e", "file", "el", "shadowRoot", "getElementById", "type", "key", "click", "selectTypoSize", "avatarBgColor", "letter", "currentColor", "find", "item", "prototype", "onUploadClick", "preventDefault", "bdsClickAvatar", "emit", "onFileInputChange", "event", "fileInput", "target", "files", "length", "selectedFile", "reader", "FileReader", "onload", "imageUrl", "result", "bdsImageUpload", "readAsDataURL", "componentWillRender", "<PERSON><PERSON><PERSON>b", "render", "arrayName", "split", "firstName", "shift", "char<PERSON>t", "toUpperCase", "lastName", "pop", "thumbnailStyle", "backgroundImage", "concat", "h", "Host", "id", "accept", "onChange", "style", "display", "class", "_a", "avatar", "onClick", "ev", "tabindex", "onKeyDown", "margin", "variant", "tag", "theme"], "sources": ["src/components/avatar/color-letter.ts", "src/components/avatar/avatar.scss?tag=bds-avatar&encapsulation=shadow", "src/components/avatar/avatar.tsx"], "sourcesContent": ["export const colorLetter = [\n  { value: 'A', color: 'system' },\n  { value: 'B', color: 'success' },\n  { value: 'C', color: 'warning' },\n  { value: 'D', color: 'error' },\n  { value: 'E', color: 'info' },\n  { value: 'F', color: 'system' },\n  { value: 'G', color: 'success' },\n  { value: 'H', color: 'warning' },\n  { value: 'I', color: 'error' },\n  { value: 'J', color: 'info' },\n  { value: 'K', color: 'system' },\n  { value: 'L', color: 'success' },\n  { value: 'M', color: 'warning' },\n  { value: 'N', color: 'error' },\n  { value: 'O', color: 'info' },\n  { value: 'P', color: 'system' },\n  { value: 'Q', color: 'success' },\n  { value: 'R', color: 'warning' },\n  { value: 'S', color: 'error' },\n  { value: 'T', color: 'info' },\n  { value: 'U', color: 'system' },\n  { value: 'V', color: 'success' },\n  { value: 'X', color: 'warning' },\n  { value: 'Y', color: 'error' },\n  { value: 'W', color: 'info' },\n  { value: 'Z', color: 'system' },\n];\n", "@use '../../globals/helpers' as *;\n\n$avatar-size-micro: 24px;\n$avatar-size-extra-small: 32px;\n$avatar-size-small: 40px;\n$avatar-size-standard: 56px;\n$avatar-size-large: 64px;\n$avatar-size-extra-large: 72px;\n\n:host {\n  position: relative;\n  display: block;\n  width: fit-content;\n}\n.avatar {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  appearance: none;\n  height: 100%;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &::before {\n      border-color: $color-focus;\n    }\n  }\n\n  &__ellipsis {\n    color: $color-surface-1;\n  }\n  &__text {\n    color: $color-content-default;\n  }\n\n  &__icon {\n    color: $color-content-default;\n  }\n\n  &__btn {\n    border-radius: 40px;\n    border: 1px solid $color-border-2;\n    box-sizing: border-box;\n    position: relative;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    &__img {\n      background-position: center;\n      background-size: cover;\n    }\n\n    &__text {\n      color: $color-content-default;\n      opacity: 1;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n    }\n\n    &__icon {\n      color: $color-content-default;\n      opacity: 1;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n    }\n\n    &__thumb {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n\n      &:before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        background-color: $color-content-default;\n        opacity: 0;\n        transition: all 0.5s;\n      }\n\n      &__icon {\n        position: relative;\n        color: $color-surface-1;\n        opacity: 0;\n        transition: all 0.5s;\n      }\n    }\n\n    &__name {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n\n      &__icon {\n        color: $color-content-default;\n      }\n    }\n\n    &__empty {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n\n      &__icon {\n        color: $color-content-default;\n      }\n    }\n  }\n\n  &__size {\n    &--micro {\n      width: $avatar-size-micro;\n      height: $avatar-size-micro;\n      min-width: $avatar-size-micro;\n      min-height: $avatar-size-micro;\n    }\n    &--extra-small {\n      width: $avatar-size-extra-small;\n      height: $avatar-size-extra-small;\n      min-width: $avatar-size-extra-small;\n      min-height: $avatar-size-extra-small;\n    }\n    &--small {\n      width: $avatar-size-small;\n      height: $avatar-size-small;\n      min-width: $avatar-size-small;\n      min-height: $avatar-size-small;\n    }\n    &--standard {\n      width: $avatar-size-standard;\n      height: $avatar-size-standard;\n      min-width: $avatar-size-standard;\n      min-height: $avatar-size-standard;\n    }\n    &--large {\n      width: $avatar-size-large;\n      height: $avatar-size-large;\n      min-width: $avatar-size-large;\n      min-height: $avatar-size-large;\n    }\n    &--extra-large {\n      width: $avatar-size-extra-large;\n      height: $avatar-size-extra-large;\n      min-width: $avatar-size-extra-large;\n      min-height: $avatar-size-extra-large;\n    }\n  }\n\n  &__color {\n    &--system {\n      & .avatar__btn {\n        background-color: $color-system;\n      }\n    }\n    &--warning {\n      & .avatar__btn {\n        background-color: $color-warning;\n      }\n    }\n    &--success {\n      & .avatar__btn {\n        background-color: $color-success;\n      }\n    }\n    &--info {\n      & .avatar__btn {\n        background-color: $color-info;\n      }\n    }\n    &--error {\n      & .avatar__btn {\n        background-color: $color-error;\n      }\n    }\n    &--surface {\n      & .avatar__btn {\n        background-color: $color-surface-2;\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &:hover {\n    .avatar {\n      &__btn {\n        &__thumb {\n          &:before {\n            opacity: 0.5;\n          }\n          &__icon {\n            opacity: 1;\n          }\n        }\n        &__text {\n          opacity: 0;\n        }\n        &__icon {\n          opacity: 0;\n        }\n        &__name {\n          opacity: 1;\n        }\n        &__empty {\n          opacity: 1;\n        }\n      }\n    }\n  }\n}\n\n.focus:focus-visible {\n  display: flex;\n  position: absolute;\n  border: 2px solid $color-focus;\n  border-radius: 4px;\n  width: 100%;\n  height: 100%;\n  top: -4px;\n  left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  outline: none;\n}\n", "import { Component, EventEmitter, h, Prop, Event, Host, State, Element } from '@stencil/core';\nimport { FontSize } from '../typo/typo';\nimport { IconSize } from '../icon/icon-interface';\nimport { colorLetter } from './color-letter';\n\nexport type avatarSize = 'micro' | 'extra-small' | 'small' | 'standard' | 'large' | 'extra-large';\nexport type colors = 'colorLetter' | 'system' | 'success' | 'warning' | 'error' | 'info' | 'surface';\n\n@Component({\n  tag: 'bds-avatar',\n  styleUrl: 'avatar.scss',\n  shadow: true,\n})\nexport class BdsAvatar {\n  @Element() el: HTMLElement;\n  private typoSize?: FontSize = 'fs-20';\n  private iconSize?: IconSize = 'large';\n  @State() hasThumb: boolean;\n  /**\n   * Name, Inserted for highlighted osuary name. Enter the full name.\n   */\n  @Prop() name?: string = null;\n  /**\n   * Thumbnail, Inserted to highlight user image. Url field.\n   */\n  @Prop({ mutable: true }) thumbnail?: string = null;\n  /**\n   * Size, Entered as one of the size. Can be one of:\n   * 'extra-small', 'small', 'standard', 'large', 'extra-large'.\n   */\n  @Prop() size?: avatarSize = 'standard';\n  /**\n   * Color, Entered as one of the color. Can be one of:\n   * 'system', 'success', 'warning', 'error', 'info'.\n   */\n  @Prop() color?: colors = 'colorLetter';\n  /**\n   * Upload, Serve to enable upload function on avatar.\n   */\n  @Prop() upload?: boolean = false;\n  /**\n   * When set to true, allows the avatar to be clicked to select and upload an image.\n   */\n  @Prop() openUpload?: boolean = false;\n  /**\n   * Ellipses, serves to indicate the user number in the listing.\n   */\n  @Prop() ellipsis?: number = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  @Event() bdsClickAvatar: EventEmitter;\n  @Event() bdsImageUpload: EventEmitter;\n\n  private onUploadClick(e) {\n    e.preventDefault();\n    this.bdsClickAvatar.emit(e);\n    if (this.openUpload) {\n      this.handleOpenUpload(e);\n    }\n  }\n\n  handleOpenUpload = (e) => {\n    const file = this.el.shadowRoot.getElementById('file-input');\n    if (e.type === 'click' || (e.type === 'keydown' && (e.key === 'Enter' || e.key === ' '))) {\n      file.click();\n    }\n  };\n\n  private onFileInputChange(event) {\n    const fileInput = event.target as HTMLInputElement;\n    const files = fileInput.files;\n\n    if (files && files.length > 0) {\n      const selectedFile = files[0];\n      const reader = new FileReader();\n\n      reader.onload = (e) => {\n        const imageUrl = e.target.result as string;\n        this.thumbnail = imageUrl;\n        this.bdsImageUpload.emit(imageUrl);\n      };\n\n      reader.readAsDataURL(selectedFile);\n    }\n  }\n\n  private selectTypoSize = (value): void => {\n    switch (value) {\n      case 'micro':\n        this.typoSize = 'fs-12';\n        this.iconSize = 'xx-small';\n        break;\n      case 'extra-small':\n        this.typoSize = 'fs-14';\n        this.iconSize = 'x-small';\n        break;\n      case 'small':\n        this.typoSize = 'fs-16';\n        this.iconSize = 'medium';\n        break;\n      case 'standard':\n        this.typoSize = 'fs-20';\n        this.iconSize = 'x-large';\n        break;\n      case 'large':\n        this.typoSize = 'fs-24';\n        this.iconSize = 'xxx-large';\n        break;\n      case 'extra-large':\n        this.typoSize = 'fs-32';\n        this.iconSize = 'xxx-large';\n        break;\n      default:\n        this.typoSize = 'fs-20';\n        this.iconSize = 'medium';\n    }\n  };\n\n  private avatarBgColor = (letter: string) => {\n    if (this.color != 'colorLetter') {\n      return this.color;\n    } else if (letter) {\n      const currentColor = colorLetter.find((item) => item.value === letter);\n      return currentColor.color;\n    }\n  };\n\n  componentWillRender() {\n    this.hasThumb = this.thumbnail ? (this.thumbnail.length !== 0 ? true : false) : false;\n  }\n\n  render(): HTMLElement {\n    const arrayName = this.name ? this.name.split(' ') : [];\n    const firstName = arrayName.length ? arrayName.shift().charAt(0).toUpperCase() : '';\n    const lastName = arrayName.length ? arrayName.pop().charAt(0).toUpperCase() : '';\n    this.selectTypoSize(this.size);\n    const thumbnailStyle = {\n      backgroundImage: `url(${this.hasThumb ? this.thumbnail : null})`,\n    };\n\n    return (\n      <Host>\n        <input\n          type=\"file\"\n          id=\"file-input\"\n          accept=\"image/*\"\n          onChange={(event) => this.onFileInputChange(event)}\n          style={{ display: 'none' }}\n        ></input>\n        <div\n          class={{\n            avatar: true,\n            [`avatar__color--${\n              this.name && !this.hasThumb\n                ? this.avatarBgColor(firstName)\n                : this.hasThumb && !this.name\n                  ? 'surface'\n                  : !this.name && !this.hasThumb\n                    ? 'surface'\n                    : this.name && this.hasThumb\n                      ? this.avatarBgColor(firstName)\n                      : null\n            }`]: true,\n            [`avatar__size--${this.size}`]: true,\n            upload: this.upload || this.openUpload,\n          }}\n          onClick={(ev) => this.onUploadClick(ev)}\n          tabindex=\"0\"\n          onKeyDown={(ev) => this.onUploadClick(ev)}\n          data-test={this.dataTest}\n        >\n          {this.ellipsis ? (\n            <div class=\"avatar__btn\">\n              <bds-typo margin={false} variant={this.typoSize} tag=\"span\">{`+${this.ellipsis}`}</bds-typo>\n            </div>\n          ) : this.thumbnail ? (\n            this.upload || this.openUpload ? (\n              <div class=\"avatar__btn\">\n                <div class={`avatar__btn__img avatar__size--${this.size}`} style={thumbnailStyle}></div>\n                <div class=\"avatar__btn__thumb\">\n                  <bds-icon\n                    class=\"avatar__btn__thumb__icon\"\n                    name=\"upload\"\n                    theme=\"outline\"\n                    size={this.iconSize}\n                  ></bds-icon>\n                </div>\n              </div>\n            ) : (\n              <div class=\"avatar__btn\">\n                <div class={`avatar__btn__img avatar__size--${this.size}`} style={thumbnailStyle}></div>\n              </div>\n            )\n          ) : this.name ? (\n            this.upload || this.openUpload ? (\n              <div class=\"avatar__btn\">\n                <bds-typo margin={false} class=\"avatar__btn__text\" variant={this.typoSize} tag=\"span\">\n                  {firstName + lastName}\n                </bds-typo>\n                <div class=\"avatar__btn__name\">\n                  <bds-icon\n                    class=\"avatar__btn__name__icon\"\n                    name=\"upload\"\n                    theme=\"outline\"\n                    size={this.iconSize}\n                  ></bds-icon>\n                </div>\n              </div>\n            ) : (\n              <div class=\"avatar__btn\">\n                <bds-typo margin={false} class=\"avatar__text\" variant={this.typoSize} tag=\"span\">\n                  {firstName + lastName}\n                </bds-typo>\n              </div>\n            )\n          ) : this.upload || this.openUpload ? (\n            <div class=\"avatar__btn\">\n              <bds-icon class=\"avatar__btn__icon\" name=\"user-default\" theme=\"outline\" size={this.iconSize}></bds-icon>\n              <div class=\"avatar__btn__empty\">\n                <bds-icon\n                  class=\"avatar__btn__empty__icon\"\n                  name=\"upload\"\n                  theme=\"outline\"\n                  size={this.iconSize}\n                ></bds-icon>\n              </div>\n            </div>\n          ) : this.name === null && !this.hasThumb ? (\n            <div class=\"avatar__btn\">\n              <bds-icon class=\"avatar__icon\" name=\"user-default\" theme=\"outline\" size={this.iconSize}></bds-icon>\n            </div>\n          ) : (\n            ''\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAO,IAAMA,EAAc,CACzB,CAAEC,MAAO,IAAKC,MAAO,UACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,SACrB,CAAED,MAAO,IAAKC,MAAO,QACrB,CAAED,MAAO,IAAKC,MAAO,UACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,SACrB,CAAED,MAAO,IAAKC,MAAO,QACrB,CAAED,MAAO,IAAKC,MAAO,UACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,SACrB,CAAED,MAAO,IAAKC,MAAO,QACrB,CAAED,MAAO,IAAKC,MAAO,UACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,SACrB,CAAED,MAAO,IAAKC,MAAO,QACrB,CAAED,MAAO,IAAKC,MAAO,UACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,WACrB,CAAED,MAAO,IAAKC,MAAO,SACrB,CAAED,MAAO,IAAKC,MAAO,QACrB,CAAED,MAAO,IAAKC,MAAO,WC1BvB,IAAMC,EAAY,gtI,ICaLC,EAASC,EAAA,wBALtB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,oGAOUA,KAAQC,SAAc,QACtBD,KAAQE,SAAc,QAKtBF,KAAIG,KAAY,KAICH,KAASI,UAAY,KAKtCJ,KAAIK,KAAgB,WAKpBL,KAAKP,MAAY,cAIjBO,KAAMM,OAAa,MAInBN,KAAUO,WAAa,MAIvBP,KAAQQ,SAAY,KAKpBR,KAAQS,SAAY,KAY5BT,KAAAU,iBAAmB,SAACC,GAClB,IAAMC,EAAOb,EAAKc,GAAGC,WAAWC,eAAe,cAC/C,GAAIJ,EAAEK,OAAS,SAAYL,EAAEK,OAAS,YAAcL,EAAEM,MAAQ,SAAWN,EAAEM,MAAQ,KAAO,CACxFL,EAAKM,O,CAET,EAoBQlB,KAAAmB,eAAiB,SAAC3B,GACxB,OAAQA,GACN,IAAK,QACHO,EAAKE,SAAW,QAChBF,EAAKG,SAAW,WAChB,MACF,IAAK,cACHH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,UAChB,MACF,IAAK,QACHH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,SAChB,MACF,IAAK,WACHH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,UAChB,MACF,IAAK,QACHH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,YAChB,MACF,IAAK,cACHH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,YAChB,MACF,QACEH,EAAKE,SAAW,QAChBF,EAAKG,SAAW,SAEtB,EAEQF,KAAAoB,cAAgB,SAACC,GACvB,GAAItB,EAAKN,OAAS,cAAe,CAC/B,OAAOM,EAAKN,K,MACP,GAAI4B,EAAQ,CACjB,IAAMC,EAAe/B,EAAYgC,MAAK,SAACC,GAAS,OAAAA,EAAKhC,QAAU6B,CAAf,IAChD,OAAOC,EAAa7B,K,CAExB,CAiHD,CAzLSI,EAAA4B,UAAAC,cAAA,SAAcf,GACpBA,EAAEgB,iBACF3B,KAAK4B,eAAeC,KAAKlB,GACzB,GAAIX,KAAKO,WAAY,CACnBP,KAAKU,iBAAiBC,E,GAWlBd,EAAA4B,UAAAK,kBAAA,SAAkBC,GAAlB,IAAAhC,EAAAC,KACN,IAAMgC,EAAYD,EAAME,OACxB,IAAMC,EAAQF,EAAUE,MAExB,GAAIA,GAASA,EAAMC,OAAS,EAAG,CAC7B,IAAMC,EAAeF,EAAM,GAC3B,IAAMG,EAAS,IAAIC,WAEnBD,EAAOE,OAAS,SAAC5B,GACf,IAAM6B,EAAW7B,EAAEsB,OAAOQ,OAC1B1C,EAAKK,UAAYoC,EACjBzC,EAAK2C,eAAeb,KAAKW,EAC3B,EAEAH,EAAOM,cAAcP,E,GA6CzBvC,EAAA4B,UAAAmB,oBAAA,WACE5C,KAAK6C,SAAW7C,KAAKI,UAAaJ,KAAKI,UAAU+B,SAAW,EAAI,KAAO,MAAS,K,EAGlFtC,EAAA4B,UAAAqB,OAAA,W,MAAA,IAAA/C,EAAAC,KACE,IAAM+C,EAAY/C,KAAKG,KAAOH,KAAKG,KAAK6C,MAAM,KAAO,GACrD,IAAMC,EAAYF,EAAUZ,OAASY,EAAUG,QAAQC,OAAO,GAAGC,cAAgB,GACjF,IAAMC,EAAWN,EAAUZ,OAASY,EAAUO,MAAMH,OAAO,GAAGC,cAAgB,GAC9EpD,KAAKmB,eAAenB,KAAKK,MACzB,IAAMkD,EAAiB,CACrBC,gBAAiB,OAAAC,OAAOzD,KAAK6C,SAAW7C,KAAKI,UAAY,KAAI,MAG/D,OACEsD,EAACC,EAAI,CAAA1C,IAAA,4CACHyC,EAAA,SAAAzC,IAAA,2CACED,KAAK,OACL4C,GAAG,aACHC,OAAO,UACPC,SAAU,SAAC/B,GAAU,OAAAhC,EAAK+B,kBAAkBC,EAAvB,EACrBgC,MAAO,CAAEC,QAAS,UAEpBN,EAAA,OAAAzC,IAAA,2CACEgD,OAAKC,EAAA,CACHC,OAAQ,MACRD,EAAC,kBAAAT,OACCzD,KAAKG,OAASH,KAAK6C,SACf7C,KAAKoB,cAAc6B,GACnBjD,KAAK6C,WAAa7C,KAAKG,KACrB,WACCH,KAAKG,OAASH,KAAK6C,SAClB,UACA7C,KAAKG,MAAQH,KAAK6C,SAChB7C,KAAKoB,cAAc6B,GACnB,OACP,KACLiB,EAAC,iBAAAT,OAAiBzD,KAAKK,OAAS,KAChC6D,EAAA5D,OAAQN,KAAKM,QAAUN,KAAKO,W,GAE9B6D,QAAS,SAACC,GAAO,OAAAtE,EAAK2B,cAAc2C,EAAnB,EACjBC,SAAS,IACTC,UAAW,SAACF,GAAO,OAAAtE,EAAK2B,cAAc2C,EAAnB,EAAsB,YAC9BrE,KAAKS,UAEfT,KAAKQ,SACJkD,EAAA,OAAKO,MAAM,eACTP,EAAU,YAAAc,OAAQ,MAAOC,QAASzE,KAAKC,SAAUyE,IAAI,QAAQ,IAAAjB,OAAIzD,KAAKQ,YAEtER,KAAKI,UACPJ,KAAKM,QAAUN,KAAKO,WAClBmD,EAAA,OAAKO,MAAM,eACTP,EAAK,OAAAO,MAAO,kCAAAR,OAAkCzD,KAAKK,MAAQ0D,MAAOR,IAClEG,EAAK,OAAAO,MAAM,sBACTP,EAAA,YACEO,MAAM,2BACN9D,KAAK,SACLwE,MAAM,UACNtE,KAAML,KAAKE,aAKjBwD,EAAA,OAAKO,MAAM,eACTP,EAAA,OAAKO,MAAO,kCAAAR,OAAkCzD,KAAKK,MAAQ0D,MAAOR,KAGpEvD,KAAKG,KACPH,KAAKM,QAAUN,KAAKO,WAClBmD,EAAK,OAAAO,MAAM,eACTP,EAAU,YAAAc,OAAQ,MAAOP,MAAM,oBAAoBQ,QAASzE,KAAKC,SAAUyE,IAAI,QAC5EzB,EAAYI,GAEfK,EAAK,OAAAO,MAAM,qBACTP,EAAA,YACEO,MAAM,0BACN9D,KAAK,SACLwE,MAAM,UACNtE,KAAML,KAAKE,aAKjBwD,EAAA,OAAKO,MAAM,eACTP,EAAA,YAAUc,OAAQ,MAAOP,MAAM,eAAeQ,QAASzE,KAAKC,SAAUyE,IAAI,QACvEzB,EAAYI,IAIjBrD,KAAKM,QAAUN,KAAKO,WACtBmD,EAAK,OAAAO,MAAM,eACTP,EAAA,YAAUO,MAAM,oBAAoB9D,KAAK,eAAewE,MAAM,UAAUtE,KAAML,KAAKE,WACnFwD,EAAK,OAAAO,MAAM,sBACTP,EAAA,YACEO,MAAM,2BACN9D,KAAK,SACLwE,MAAM,UACNtE,KAAML,KAAKE,aAIfF,KAAKG,OAAS,OAASH,KAAK6C,SAC9Ba,EAAK,OAAAO,MAAM,eACTP,EAAA,YAAUO,MAAM,eAAe9D,KAAK,eAAewE,MAAM,UAAUtE,KAAML,KAAKE,YAC1E,I,uHA5NI,I", "ignoreList": []}