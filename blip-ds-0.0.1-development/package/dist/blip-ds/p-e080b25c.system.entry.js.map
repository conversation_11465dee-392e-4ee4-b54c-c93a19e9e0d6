{"version": 3, "names": ["tableBodyCss", "TableBody", "exports", "class_1", "hostRef", "this", "multipleRows", "prototype", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "collapse", "render", "h", "Host", "key", "class", "host", "multiple"], "sources": ["src/components/table/table-body/table-body.scss?tag=bds-table-body&encapsulation=scoped", "src/components/table/table-body/table-body.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n    display: table-row-group;\n    height: 64px;\n  }\n\n  :host(.multiple) {\n    border-bottom: 1px solid $color-border-2;\n  }\n  \n  :host:last-child {\n    border-bottom: none;\n  }", "import { Component, h, Host, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-body',\n  styleUrl: 'table-body.scss',\n  scoped: true,\n})\nexport class TableBody {\n  @Element() private element: HTMLElement;\n  @State() multipleRows = false;\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('collapse') === 'true' || bdsTable.collapse === true)) {\n      this.multipleRows = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ host: true, multiple: this.multipleRows }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAe,gN,ICORC,EAASC,EAAA,4BALtB,SAAAC,EAAAC,G,UAOWC,KAAYC,aAAG,KAgBzB,CAdCH,EAAAI,UAAAC,kBAAA,WACE,IAAMC,EAAWJ,KAAKK,QAAQC,QAAQ,aACtC,GAAIF,IAAaA,EAASG,aAAa,cAAgB,QAAUH,EAASI,WAAa,MAAO,CAC5FR,KAAKC,aAAe,I,GAIxBH,EAAAI,UAAAO,OAAA,WACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAO,CAAEC,KAAM,KAAMC,SAAUf,KAAKC,eACxCS,EAAQ,QAAAE,IAAA,6C,4HAdM,I", "ignoreList": []}