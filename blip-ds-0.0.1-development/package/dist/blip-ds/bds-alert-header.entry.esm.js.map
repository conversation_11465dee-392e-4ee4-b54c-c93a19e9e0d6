{"version": 3, "file": "bds-alert-header.entry.esm.js", "sources": ["src/components/alert/alert-header/alert-header.scss?tag=bds-alert-header&encapsulation=shadow", "src/components/alert/alert-header/alert-header.tsx"], "sourcesContent": ["@use '../../../globals/colors' as *;\n\n.alert__header {\n  width: 100%;\n  min-height: 64px;\n  padding: 12px 16px;\n  box-sizing: border-box;\n  display: inline-flex;\n  align-items: center;\n\n  bds-icon {\n    min-width: 32px;\n  }\n\n  bds-typo {\n    margin-left: 8px;\n    color: $color-content-din;\n  }\n\n  &--system {\n    background: $color-system;\n\n    bds-typo {\n      color: $color-content-default;\n    }\n\n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--error {\n    background: $color-error;\n    bds-typo {\n      color: $color-content-default;\n    }\n    \n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--warning {\n    background: $color-warning;\n    bds-typo {\n      color: $color-content-default;\n    }\n    \n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--delete {\n    background: $color-delete;\n\n    bds-typo {\n      color: $color-content-bright;\n    }\n\n    .color-icon {\n      color: $color-content-bright;\n    }\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\nexport type AlertHeaderVariannt = 'system' | 'error' | 'warning' | 'delete';\n\n@Component({\n  tag: 'bds-alert-header',\n  styleUrl: 'alert-header.scss',\n  shadow: true,\n})\nexport class AlertHeader implements ComponentInterface {\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'system', 'error', 'warning', 'delete';\n   */\n  @Prop() variant?: AlertHeaderVariannt = 'system';\n\n  /**\n   * used for add icon the header. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n\n  render() {\n    return (\n      <div\n        class={{\n          alert__header: true,\n          [`alert__header--${this.variant}`]: true,\n        }}\n      >\n        {this.icon && <bds-icon class=\"color-icon\" theme=\"outline\" size=\"x-large\" name={this.icon}></bds-icon>}\n        <bds-typo variant=\"fs-16\" bold=\"bold\">\n          <slot />\n        </bds-typo>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAG,4xCAA4xC;;MCStyC,WAAW,GAAA,MAAA;AALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;;AAGG;AACK,QAAA,IAAO,CAAA,OAAA,GAAyB,QAAQ;AAEhD;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAiB9C;IAfC,MAAM,GAAA;QACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;AACnB,gBAAA,CAAC,kBAAkB,IAAI,CAAC,OAAO,CAAE,CAAA,GAAG,IAAI;AACzC,aAAA,EAAA,EAEA,IAAI,CAAC,IAAI,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,YAAY,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAa,CAAA,EACtG,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EACnC,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACC,CACP;;;;;;;"}