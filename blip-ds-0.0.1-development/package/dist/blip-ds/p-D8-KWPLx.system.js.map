{"version": 3, "file": "p-D8-KWPLx.system.js", "sources": ["@lazy-external-entrypoint?app-data=conditional"], "sourcesContent": ["export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\nexport const defineCustomElements = async (win, options) => {\n  if (typeof window === 'undefined') return undefined;\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAGY,YAAC,oBAAoB,mCAAG,OAAO,GAAG,EAAE,OAAO,KAAK;MAC5D,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,OAAO,SAAS;MACrD,EAAE,MAAM,aAAa,EAAE;MACvB,EAAE,OAAO,aAAa,CAAC,4BAA4B,EAAE,OAAO,CAAC;MAC7D;;;;;;;;"}