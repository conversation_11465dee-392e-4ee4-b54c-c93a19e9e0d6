{"version": 3, "names": ["navTreeCss", "NavTree", "exports", "class_1", "hostRef", "_this", "this", "itemsGroup", "isOpenAftAnimation", "navTreeChild", "numberElement", "collapse", "isOpen", "icon", "secondaryText", "dataTest", "loading", "disable", "handler", "prototype", "toggle", "reciveNumber", "number", "open", "close", "isOpenChanged", "value", "bdsToogleChange", "emit", "element", "_a", "closeAll", "componentWillLoad", "parentElement", "tagName", "querySelector", "handleKeyDown", "event", "key", "render", "h", "Host", "tabindex", "onKeyDown", "bind", "class", "_b", "onClick", "_c", "nav_main", "nav_main_active", "text", "concat", "size", "_d", "name", "color", "theme", "_e", "variant", "tag", "bold", "_f", "margin", "_g", "accordion", "accordion_open", "_h"], "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree&encapsulation=shadow", "src/components/nav-tree/nav-tree.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, Element, State, Prop, Method, Event, EventEmitter, Watch, h } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTree {\n  private itemsGroup?: HTMLBdsNavTreeGroupElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  @State() numberElement?: number = null;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * A prop for make the nav open.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * When de open or close of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    if (!this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    if (value) {\n      if (this.itemsGroup.collapse == 'single') {\n        this.itemsGroup?.closeAll(this.numberElement);\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.itemsGroup =\n      this.element.parentElement.tagName == 'BDS-NAV-TREE-GROUP' &&\n      (this.element.parentElement as HTMLBdsNavTreeGroupElement);\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item') === null ? false : true;\n  }\n\n  private handler = (): void => {\n    if (!this.loading && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter' && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              [`nav_main--disable`]: this.disable,\n            }}\n          >\n            <div\n              onClick={this.handler}\n              class={{\n                nav_main: true,\n                nav_main_active: this.isOpen,\n                [`nav_main--loading`]: this.loading,\n                [`nav_main--disable`]: this.disable,\n              }}\n              data-test={this.dataTest}\n              aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n            >\n              {this.loading ? (\n                <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n              ) : this.icon ? (\n                <bds-icon\n                  class={{\n                    [`icon-item`]: true,\n                    [`icon-item-active`]: this.isOpen,\n                  }}\n                  size=\"medium\"\n                  name={this.icon}\n                  color=\"inherit\"\n                  theme=\"outline\"\n                ></bds-icon>\n              ) : (\n                ''\n              )}\n              <div class=\"nav_main_text\">\n                {this.text && (\n                  <bds-typo\n                    class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                    variant=\"fs-14\"\n                    tag=\"span\"\n                    line-height=\"small\"\n                    bold={this.isOpen ? 'bold' : 'semi-bold'}\n                  >\n                    {this.text}\n                  </bds-typo>\n                )}\n                {this.secondaryText && (\n                  <bds-typo\n                    class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                    variant=\"fs-12\"\n                    line-height=\"small\"\n                    tag=\"span\"\n                    margin={false}\n                  >\n                    {this.secondaryText}\n                  </bds-typo>\n                )}\n              </div>\n              <div class=\"nav_main_content\">\n                <slot name=\"header-content\"></slot>\n              </div>\n              {this.navTreeChild && (\n                <bds-icon\n                  name=\"arrow-down\"\n                  class={{\n                    [`nav_main_arrow`]: true,\n                    [`nav_main_arrow_active`]: this.isOpen,\n                    [`nav_main_arrow--loading`]: this.loading,\n                  }}\n                ></bds-icon>\n              )}\n            </div>\n          </div>\n        </div>\n        <div\n          class={{\n            accordion: true,\n            accordion_open: this.isOpen && this.navTreeChild,\n          }}\n        >\n          <div class={{ ['container']: true, [`container--disable`]: this.disable }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gnDAAA,IAAMA,EAAa,i4K,ICSNC,EAAOC,EAAA,0BALpB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,yDAMUA,KAAUC,WAAgC,KAIzCD,KAAkBE,mBAAa,MAC/BF,KAAYG,aAAI,KAChBH,KAAaI,cAAY,KAI1BJ,KAAQK,SAAe,SAISL,KAAMM,OAAa,MAInDN,KAAIO,KAAY,KAQhBP,KAAaQ,cAAY,KAIzBR,KAAQS,SAAY,KAIpBT,KAAOU,QAAa,MAKpBV,KAAOW,QAAa,MA4CpBX,KAAOY,QAAG,WAChB,IAAKb,EAAKW,UAAYX,EAAKY,QAAS,CAClCZ,EAAKO,QAAUP,EAAKO,M,CAExB,CAiGD,CA1IOT,EAAAgB,UAAAC,OAAN,W,qFACE,IAAKd,KAAKW,QAAS,CACjBX,KAAKM,QAAUN,KAAKM,M,kBAKlBT,EAAAgB,UAAAE,aAAN,SAAmBC,G,qFACjBhB,KAAKI,cAAgBY,E,iBAIjBnB,EAAAgB,UAAAI,KAAN,W,qFACEjB,KAAKM,OAAS,K,iBAIVT,EAAAgB,UAAAK,MAAN,W,qFACElB,KAAKM,OAAS,M,iBAGNT,EAAAgB,UAAAM,cAAA,SAAcC,G,MACtBpB,KAAKqB,gBAAgBC,KAAK,CAAEF,MAAOA,EAAOG,QAASvB,KAAKuB,UACxD,GAAIH,EAAO,CACT,GAAIpB,KAAKC,WAAWI,UAAY,SAAU,EACxCmB,EAAAxB,KAAKC,cAAY,MAAAuB,SAAA,SAAAA,EAAAC,SAASzB,KAAKI,c,IAKrCP,EAAAgB,UAAAa,kBAAA,WACE1B,KAAKC,WACHD,KAAKuB,QAAQI,cAAcC,SAAW,sBACrC5B,KAAKuB,QAAQI,cAChB3B,KAAKG,aAAeH,KAAKuB,QAAQM,cAAc,uBAAyB,KAAO,MAAQ,I,EASjFhC,EAAAgB,UAAAiB,cAAA,SAAcC,GACpB,GAAIA,EAAMC,KAAO,UAAYhC,KAAKW,QAAS,CACzCX,KAAKM,QAAUN,KAAKM,M,GAIxBT,EAAAgB,UAAAoB,OAAA,W,kBACE,OACEC,EAACC,EAAI,CAAAH,IAAA,4CACHE,EAAA,OAAAF,IAAA,2CAAKI,SAAS,IAAIC,UAAWrC,KAAK8B,cAAcQ,KAAKtC,MAAOuC,MAAM,SAChEL,EAAA,OAAAF,IAAA,2CACEO,OAAKC,EAAA,GACHA,EAAC,qBAAsBxC,KAAKW,Q,IAG9BuB,EAAA,OAAAF,IAAA,2CACES,QAASzC,KAAKY,QACd2B,OAAKG,EAAA,CACHC,SAAU,KACVC,gBAAiB5C,KAAKM,QACtBoC,EAAC,qBAAsB1C,KAAKU,QAC5BgC,EAAC,qBAAsB1C,KAAKW,Q,GAC7B,YACUX,KAAKS,SAAQ,aACZT,KAAK6C,MAAQ7C,KAAKQ,eAAiB,KAAAsC,OAAK9C,KAAKQ,iBAExDR,KAAKU,QACJwB,EAAqB,uBAAAa,KAAK,gBACxB/C,KAAKO,KACP2B,EACE,YAAAK,OAAKS,EAAA,GACHA,EAAC,aAAc,KACfA,EAAC,oBAAqBhD,KAAKM,O,GAE7ByC,KAAK,SACLE,KAAMjD,KAAKO,KACX2C,MAAM,UACNC,MAAM,YACI,GAIdjB,EAAK,OAAAF,IAAA,2CAAAO,MAAM,iBACRvC,KAAK6C,MACJX,EAAA,YAAAF,IAAA,2CACEO,OAAKa,EAAA,GAAIA,EAAC,cAAe,KAAMA,EAAC,uBAAwBpD,KAAKU,QAAO0C,GACpEC,QAAQ,QACRC,IAAI,OACQ,sBACZC,KAAMvD,KAAKM,OAAS,OAAS,aAE5BN,KAAK6C,MAGT7C,KAAKQ,eACJ0B,EACE,YAAAF,IAAA,2CAAAO,OAAKiB,EAAA,GAAIA,EAAC,iBAAkB,KAAMA,EAAC,0BAA2BxD,KAAKU,QAAO8C,GAC1EH,QAAQ,QAAO,cACH,QACZC,IAAI,OACJG,OAAQ,OAEPzD,KAAKQ,gBAIZ0B,EAAK,OAAAF,IAAA,2CAAAO,MAAM,oBACTL,EAAA,QAAAF,IAAA,2CAAMiB,KAAK,oBAEZjD,KAAKG,cACJ+B,EAAA,YAAAF,IAAA,2CACEiB,KAAK,aACLV,OAAKmB,EAAA,GACHA,EAAC,kBAAmB,KACpBA,EAAC,yBAA0B1D,KAAKM,OAChCoD,EAAC,2BAA4B1D,KAAKU,Q,QAO9CwB,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACLoB,UAAW,KACXC,eAAgB5D,KAAKM,QAAUN,KAAKG,eAGtC+B,EAAA,OAAAF,IAAA,2CAAKO,OAAKsB,EAAA,GAAIA,EAAC,aAAc,KAAMA,EAAC,sBAAuB7D,KAAKW,QAAOkD,IACrE3B,EAAA,QAAAF,IAAA,+C,sPAnLQ,I", "ignoreList": []}