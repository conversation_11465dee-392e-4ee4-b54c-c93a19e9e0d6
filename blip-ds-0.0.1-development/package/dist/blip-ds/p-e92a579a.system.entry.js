System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,r,s;return{setters:[function(t){e=t.r;i=t.c;r=t.h;s=t.H}],execute:function(){var o=':host{position:relative;display:-ms-flexbox;display:flex;width:100%;height:32px}.track-bg{position:absolute;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;inset:0 8px;pointer-events:none}.track-bg .progress-bar{position:absolute;height:4px;border-radius:1rem;z-index:2}.track-bg .progress-bar-liner{background-color:var(--color-primary, rgb(30, 107, 241))}.track-bg .progress-bar-tooltip{position:absolute;top:-6px;right:-0.5rem}.track-bg .progress-bar-thumb{position:relative;width:1rem;height:1rem;border-radius:1rem;background-color:var(--color-primary, rgb(30, 107, 241));z-index:0}.track-bg .progress-bar-thumb::before{content:"";position:absolute;inset:0;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));border-radius:1rem;-webkit-transition:all 0.3s ease-in-out;transition:all 0.3s ease-in-out}.track-bg .progress-bar-hover .progress-bar-thumb::before{-webkit-transform:scale(2);transform:scale(2)}.track-bg::before{content:"";position:absolute;inset:0;height:4px;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16;border-radius:1rem}.track-bg .step{position:relative;width:2px;height:8px;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;background-color:var(--color-content-disable, rgb(89, 89, 89));border-bottom-left-radius:1rem;border-bottom-right-radius:1rem}.track-bg .step .label-step{margin-top:1rem}.element-min{position:relative;height:4px;background-color:var(--color-primary, rgb(30, 107, 241));border-top-left-radius:1rem;border-bottom-left-radius:1rem}.element-max{position:relative;height:4px;border-top-right-radius:1rem;border-bottom-right-radius:1rem}.input_slide{-webkit-appearance:none;-moz-appearance:none;appearance:none;margin:0;background:transparent;cursor:pointer;width:100%;height:4px;position:relative;border-radius:1rem;background:transparent;color:-internal-light-dark(transparent, transparent)}.input_slide.has_min{border-top-left-radius:0;border-bottom-left-radius:0;margin-left:0}.input_slide.has_max{border-top-right-radius:0;border-bottom-right-radius:0}.input_slide:hover .input_slide::-webkit-slider-thumb,.input_slide:hover .input_slide::-moz-range-thumb{-webkit-appearance:none}.input_slide::-webkit-slider-thumb,.input_slide::-moz-range-thumb{-webkit-appearance:none;position:relative;height:16px;width:16px;border-radius:50%;border:none}.group_slide{position:relative;width:100%}.group_slide .input_slide{width:inherit;position:absolute}.group_slide .input_slide_start{left:0}.group_slide .input_slide_end{right:0}.group_slide .input_slide::-webkit-slider-thumb,.group_slide .input_slide::-moz-range-thumb{-webkit-appearance:none}';var a=t("bds_slider",function(){function t(t){var r=this;e(this,t);this.bdsChange=i(this,"bdsChange");var s,o;this.inputValue=(o=(s=this.value)===null||s===void 0?void 0:s.toString())!==null&&o!==void 0?o:this.min?this.min.toString():"0";this.value=this.min?this.min:0;this.markers=false;this.label=false;this.type="fill";this.dataTest=null;this.refInputSlide=function(t){r.inputSlide=t};this.refBdsTooltip=function(t){r.bdsTooltip=t};this.refProgressBar=function(t){r.progressBar=t};this.valuePercent=function(t){var e=t;var i=e.min?parseInt(e.min):0;var r=parseInt(e.max);var s=parseInt(e.value);var o=(s-i)*100/(r-i);return o};this.onInputSlide=function(t){var e=t.target;r.progressBar.style.width="".concat(r.valuePercent(e),"%");var i=r.emiterChange(parseInt(e.value));r.inputValue=r.stepArray.length>0?i.name:e.value;r.bdsChange.emit(i)};this.onInputMouseEnter=function(){r.bdsTooltip.visible();r.progressBar.classList.add("progress-bar-hover")};this.onInputMouseLeave=function(){r.bdsTooltip.invisible();r.progressBar.classList.remove("progress-bar-hover")};this.emiterChange=function(t){if(r.internalOptions){return r.stepArray[t]}else{return r.stepArray.find((function(e){return parseInt(e.name)===t}))}}}t.prototype.componentWillLoad=function(){if(this.dataMarkers){if(typeof this.dataMarkers==="string"){this.internalOptions=JSON.parse(this.dataMarkers);this.stepArray=this.internalOptions}else{this.internalOptions=this.dataMarkers;this.stepArray=this.internalOptions}}else{this.stepArray=this.arrayToSteps((this.max-this.min)/this.step,Number.isInteger((this.max-this.min)/this.step))}};t.prototype.componentDidLoad=function(){this.progressBar.style.width="".concat(this.valuePercent(this.inputSlide),"%")};t.prototype.componentDidRender=function(){if(this.internalOptions){this.inputSlide.min="0";this.inputSlide.max="".concat(this.internalOptions.length-1);this.inputSlide.step="1"}else{this.inputSlide.min=this.min?"".concat(this.min):"";this.inputSlide.max=this.max?"".concat(this.max):"";this.inputSlide.step=this.step?"".concat(this.step):""}};t.prototype.componentDidUpdate=function(){this.progressBar.style.width="".concat(this.valuePercent(this.inputSlide),"%");var t=this.emiterChange(parseInt(this.inputSlide.value));this.inputValue=this.stepArray.length>0?t.name:this.inputSlide.value};t.prototype.arrayToSteps=function(t,e){var i=this;var r=e?t+1:t;var s=[];for(var o=0;o<r;o++){s.push(o)}return s.map((function(t){return{value:t,name:t*i.step+i.min}}))};t.prototype.render=function(){var t,e,i;var o=this;return r(s,{key:"29451c00acb28b35da8b8cc8cb632cf933b664a8"},r("input",{key:"e72694e41261abfa6094ec05bca761a5b90e3117",ref:this.refInputSlide,type:"range",class:{input_slide:true},value:this.value,onInput:this.onInputSlide,onMouseEnter:this.onInputMouseEnter,onMouseLeave:this.onInputMouseLeave,"data-test":this.dataTest}),r("div",{key:"66c0346da07f072e40e4d6e7ee819473b66d1e84",class:"track-bg"},r("div",{key:"30a91ac84bac7d436b36f7c88c2465158e4812cb",class:(t={},t["progress-bar"]=true,t["progress-bar-liner"]=this.type!=="no-linear",t),ref:this.refProgressBar},r("bds-tooltip",{key:"97cf038eb25945c3dcdf8e1d08495f2bf6ce0377",ref:this.refBdsTooltip,class:(e={},e["progress-bar-tooltip"]=true,e),position:"top-center","tooltip-text":this.inputValue},r("div",{key:"b21a646fcf26179271064c7256ccb1caaa2b5e38",class:(i={},i["progress-bar-thumb"]=true,i)}))),this.markers&&this.stepArray.map((function(t,e){return r("div",{key:e,class:"step"},o.label&&r("bds-typo",{class:"label-step",variant:"fs-10"},"".concat(t.name)))}))))};return t}());a.style=o}}}));
//# sourceMappingURL=p-e92a579a.system.entry.js.map