{"version": 3, "names": ["tabGroupCss", "BdsTabGroup", "exports", "class_1", "hostRef", "_this", "this", "tabItensElement", "tabItensSlideElement", "isSlideTabs", "alignTab", "tabRefSlide", "positionLeft", "contentScrollable", "align", "dtButtonPrev", "dtButtonNext", "getEventsDisable", "ItensElement", "for<PERSON>ach", "element", "addEventListener", "setInternalItens", "Array", "from", "checkSlideTabs", "headerElement", "headerSlideElement", "_a", "offsetWidth", "_b", "setFirstActive", "hasOpenDefined", "filter", "obj", "open", "length", "setnumberElement", "i", "numberElement", "arrayItens", "map", "item", "index", "Object", "assign", "label", "badge", "disable", "undefined", "error", "headerStyle", "contentStyle", "icon", "iconPosition", "iconTheme", "badgeShape", "badgeColor", "badgeIcon", "badgeAnimation", "badgeNumber", "badgePosition", "dataTest", "internalItens", "handleClick", "numberItem", "updateInternalItens", "bdsTabChange", "emit", "refHeaderElement", "el", "refHeaderSlideElement", "handleDisabled", "id", "bdsTabDisabled", "nextSlide", "minLeft", "calcNumber", "_c", "_d", "numberClicks", "parseInt", "toString", "newPosition", "_e", "prevSlide", "renderIcon", "Icon", "Theme", "h", "class", "tab_group__header__itens__item__typo__disable", "tab_group__header__itens__item__typo__error", "size", "name", "theme", "renderBadge", "<PERSON><PERSON><PERSON>", "Color", "Animation", "Number", "color", "number", "shape", "animation", "prototype", "componentWillRender", "getElementsByTagName", "componentDidLoad", "shadowRoot", "querySelectorAll", "connectedCallback", "isSlide", "window", "setInterval", "disconnectedCallback", "clearInterval", "handleKeyDown", "event", "key", "focus", "parseInlineStyle", "styleString", "split", "style", "trim", "reduce", "acc", "_f", "s", "property", "value", "camelProperty", "replace", "g", "toUpperCase", "render", "slidePosition", "left", "concat", "openTab", "find", "Host", "tab_group", "onClick", "variant", "tab_group__header", "tab_group__slide", "ref", "tab_group__header__itens", "tab_group__slide__itens", "bold", "tab_group__header__itens__item", "tab_group__header__itens__item__open", "tab_group__header__itens__item__disable", "tabindex", "onKeyDown", "ev", "tab_group__content", "tab_group__scrolled"], "sources": ["src/components/tabs/tab-group.scss?tag=bds-tab-group&encapsulation=shadow", "src/components/tabs/tab-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n.tab_group {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n\n  &__header {\n    padding: 4px 16px;\n    overflow: hidden;\n\n    &__itens {\n      display: flex;\n      flex-direction: row;\n      width: max-content;\n      gap: 32px;\n      margin: auto;\n\n      &__center {\n        justify-content: center;\n        margin: auto;\n      }\n\n      &__right {\n        justify-content: right;\n        margin: 0 0 0 auto;\n      }\n\n      &__left {\n        justify-content: left;\n        margin: 0 auto 0 0;\n      }\n\n      &__item {\n        cursor: pointer;\n        height: 46px;\n        gap: 4px;\n        width: auto;\n        display: flex;\n        align-items: center;\n        border-bottom: 2px solid transparent;\n        position: relative;\n\n        &__typo{\n          color: $color-content-disable;\n          &__disable {\n            color: $color-content-ghost;\n          }\n          &__error {\n            color: $color-surface-negative;\n          }\n        }\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: -4px;\n          border: 2px solid transparent;\n          border-radius: 4px;\n        }\n\n        &:focus-visible {\n          outline: none;\n\n          &::before {\n            border-color: $color-focus;\n          }\n        }\n\n        &__open {\n          color: $color-content-default;\n          border-color: $color-primary;\n        }\n        &__disable {\n          cursor: no-drop;\n        }\n      }\n    }\n  }\n\n  &__slide {\n    position: relative;\n    overflow: hidden;\n    padding: 0 16px;\n    height: 54px;\n    margin-left: 56px;\n    margin-right: 56px;\n\n    &-button {\n      position: absolute;\n      z-index: 1;\n      background-color: $color-surface-1;\n\n      &[icon='arrow-left'] {\n        left: 0;\n      }\n      &[icon='arrow-right'] {\n        right: 0;\n      }\n    }\n\n    &__itens {\n      position: absolute;\n      left: 56px;\n      width: max-content;\n      height: 48px;\n      display: flex;\n      flex-direction: row;\n      justify-content: center;\n      padding: 4px;\n      gap: 32px;\n      -webkit-transition: left 0.5s;\n      -moz-transition: left 0.5s;\n      transition: left 0.5s;\n    }\n  }\n  &__content {\n    height: 100%;\n  }\n\n  &__scrolled {\n    flex-shrink: 999;\n    overflow: none;\n\n    @include custom-scroll;\n  }\n}\n", "import { Component, h, Host, Element, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { Itens } from './tab-group-interface';\n\n@Component({\n  tag: 'bds-tab-group',\n  styleUrl: 'tab-group.scss',\n  shadow: true,\n})\nexport class BdsTabGroup {\n  private tabItensElement?: HTMLCollectionOf<HTMLBdsTabItemElement> = null;\n  private tabItensSlideElement?: NodeListOf<HTMLElement> = null;\n  private headerElement?: HTMLElement;\n  private headerSlideElement?: HTMLElement;\n  private isSlide?: number;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalItens: Itens[];\n\n  @State() isSlideTabs?: boolean = false;\n\n  @State() alignTab?: 'left' | 'scrolling' | 'right' = 'left';\n\n  @State() tabRefSlide?: number = 0;\n\n  @State() positionLeft?: number = 0;\n\n  @Prop() contentScrollable?: boolean = true;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * bdsTabChange. Event to return value when Tabs is change.\n   */\n  @Event() bdsTabChange?: EventEmitter;\n  /**\n   * bdsTabDisabled. Event to return value when Tabs disable is change.\n   */\n  @Event() bdsTabDisabled?: EventEmitter;\n\n  componentWillRender() {\n    this.tabItensElement = this.element.getElementsByTagName('bds-tab-item') as HTMLCollectionOf<HTMLBdsTabItemElement>;\n    this.setnumberElement();\n    this.setFirstActive();\n    this.setInternalItens(Array.from(this.tabItensElement));\n    this.getEventsDisable(Array.from(this.tabItensElement));\n  }\n\n  componentDidLoad() {\n    this.tabItensSlideElement = this.element.shadowRoot.querySelectorAll(\n      '.tab_group__header__itens__item',\n    ) as NodeListOf<HTMLElement>;\n  }\n\n  connectedCallback() {\n    this.isSlide = window.setInterval(() => {\n      this.isSlideTabs = this.checkSlideTabs();\n    }, 100);\n  }\n\n  private getEventsDisable = (ItensElement): void => {\n    ItensElement.forEach((element) => {\n      element.addEventListener(\n        'tabDisabled',\n        () => {\n          this.setInternalItens(Array.from(this.tabItensElement));\n        },\n        false,\n      );\n    });\n  };\n\n  disconnectedCallback() {\n    window.clearInterval(this.isSlide);\n  }\n\n  private checkSlideTabs = (): boolean => {\n    if (this.headerElement || this.headerSlideElement) {\n      if (this.headerSlideElement?.offsetWidth > this.headerElement?.offsetWidth) {\n        return true;\n      }\n    }\n  };\n\n  private setFirstActive = () => {\n    const hasOpenDefined = Array.from(this.tabItensElement).filter((obj) => obj.open);\n    if (!hasOpenDefined.length) {\n      this.tabItensElement[0].open = true;\n    }\n  };\n\n  private setnumberElement = () => {\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      this.tabItensElement[i].numberElement = i;\n    }\n  };\n\n  private setInternalItens = (ItensElement) => {\n    const arrayItens = ItensElement.map((item, index) => {\n      return {\n        label: item.label,\n        open: item.open,\n        numberElement: index,\n        badge: item.badge,\n        ...(item.disable !== undefined && { disable: item.disable }),\n        ...(item.error !== undefined && { error: item.error }),\n        ...(item.headerStyle !== undefined && { headerStyle: item.headerStyle }),\n        ...(item.contentStyle !== undefined && { contentStyle: item.contentStyle }),\n        ...(item.icon !== undefined && { icon: item.icon }),\n        ...(item.iconPosition !== undefined && { iconPosition: item.iconPosition }),\n        ...(item.iconTheme !== undefined && { iconTheme: item.iconTheme }),\n        ...(item.badgeShape !== undefined && { badgeShape: item.badgeShape }),\n        ...(item.badgeColor !== undefined && { badgeColor: item.badgeColor }),\n        ...(item.badgeIcon !== undefined && { badgeIcon: item.badgeIcon }),\n        ...(item.badgeAnimation !== undefined && { badgeAnimation: item.badgeAnimation }),\n        ...(item.badgeNumber !== undefined && { badgeNumber: item.badgeNumber }),\n        ...(item.badgePosition !== undefined && { badgePosition: item.badgePosition }),\n        ...(item.dataTest !== undefined && { dataTest: item.dataTest }),\n      };\n    });\n    return (this.internalItens = arrayItens);\n  };\n\n  private handleClick = (numberItem) => {\n    const updateInternalItens = this.internalItens.map((item) => {\n      return {\n        label: item.label,\n        open: false,\n        numberElement: item.numberElement,\n      };\n    });\n    this.internalItens = updateInternalItens;\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      if (this.tabItensElement[i].numberElement != numberItem) {\n        this.tabItensElement[i].open = false;\n      } else {\n        this.tabItensElement[i].open = true;\n        this.bdsTabChange.emit(this.tabItensElement[i]);\n      }\n    }\n  };\n\n  private refHeaderElement = (el: HTMLElement): void => {\n    this.headerElement = el;\n  };\n\n  private refHeaderSlideElement = (el: HTMLElement): void => {\n    this.headerSlideElement = el;\n  };\n\n  private handleDisabled = (id) => {\n    this.bdsTabDisabled.emit(this.tabItensElement[id]);\n  };\n\n  private nextSlide = () => {\n    const minLeft = this.headerElement?.offsetWidth - this.headerSlideElement?.offsetWidth;\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft - this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition < minLeft ? minLeft : newPosition;\n    this.alignTab = newPosition < minLeft ? 'right' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide + 1 : numberClicks;\n  };\n\n  private prevSlide = () => {\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft + this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition > 0 ? 0 : newPosition;\n    this.alignTab = newPosition > 0 ? 'left' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide - 1 : numberClicks;\n  };\n\n  private handleKeyDown(event, item) {\n    if (event.key == 'Enter') {\n      item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement);\n    }\n    if (event.key == 'ArrowRight') {\n      this.tabItensSlideElement[item.numberElement + 1].focus();\n    }\n    if (event.key == 'ArrowLeft') {\n      this.tabItensSlideElement[item.numberElement - 1].focus();\n    }\n  }\n\n  private parseInlineStyle(styleString: string): { [key: string]: string } {\n    if (!styleString) return {};\n    \n    return styleString\n      .split(';')\n      .filter(style => style.trim())\n      .reduce((acc, style) => {\n        const [property, value] = style.split(':').map(s => s.trim());\n        if (property && value) {\n          // Convert kebab-case to camelCase for CSS properties\n          const camelProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase());\n          acc[camelProperty] = value;\n        }\n        return acc;\n      }, {});\n  }\n\n  private renderIcon = (Icon, Theme, disable, error) => {\n    return (\n      <bds-icon\n        class={{ \n          tab_group__header__itens__item__typo__disable: disable,\n          tab_group__header__itens__item__typo__error: error \n        }}\n        size=\"x-small\"\n        name={Icon}\n        theme={Theme}\n      ></bds-icon>\n    );\n  };\n\n  private renderBadge = (Shape, Color, Icon, Animation, Number) => {\n    return (\n      <bds-grid justify-content=\"center\">\n        <bds-badge color={Color} icon={Icon} number={Number} shape={Shape} animation={Animation}></bds-badge>\n      </bds-grid>\n    );\n  };\n\n  render(): HTMLElement {\n    const slidePosition = { left: `${this.positionLeft}px` };\n    \n    // Find the currently open tab to get its headerStyle and contentStyle\n    const openTab = this.internalItens?.find(item => item.open);\n    const headerStyle = openTab?.headerStyle ? this.parseInlineStyle(openTab.headerStyle) : {};\n    const contentStyle = openTab?.contentStyle ? this.parseInlineStyle(openTab.contentStyle) : {};\n    \n    return (\n      <Host>\n        <div class={{ tab_group: true }}>\n          {this.isSlideTabs && this.alignTab != 'left' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-left\"\n              size=\"short\"\n              id=\"bds-tabs-button-left\"\n              onClick={() => this.prevSlide()}\n              dataTest={this.dtButtonPrev}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__header: true, tab_group__slide: this.isSlideTabs }} \n            ref={this.refHeaderElement}\n            style={headerStyle}\n          >\n            <div\n              class={{\n                tab_group__header__itens: true,\n                tab_group__slide__itens: this.isSlideTabs,\n                [`tab_group__header__itens__${this.align}`]: !this.isSlideTabs,\n              }}\n              ref={this.refHeaderSlideElement}\n              style={slidePosition}\n            >\n              {this.internalItens &&\n                this.internalItens.map((item, index) => {\n                  const bold = item.open == true ? 'bold' : 'regular';\n                  return (\n                    <div\n                      class={{\n                        tab_group__header__itens__item: true,\n                        tab_group__header__itens__item__open: item.open,\n                        tab_group__header__itens__item__disable: item.disable,\n                      }}\n                      key={index}\n                      tabindex=\"0\"\n                      onClick={() =>\n                        item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement)\n                      }\n                      onKeyDown={(ev) => this.handleKeyDown(ev, item)}\n                    >\n                      {item.iconPosition === 'left' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'left' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                      <bds-typo\n                        class={{ \n                          tab_group__header__itens__item__typo__disable: item.disable,\n                          tab_group__header__itens__item__typo__error: item.error \n                        }}\n                        variant=\"fs-16\"\n                        bold={bold}\n                      >\n                        {item.label}\n                      </bds-typo>\n                      {item.iconPosition === 'right' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'right' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                    </div>\n                  );\n                })}\n            </div>\n          </div>\n          {this.isSlideTabs && this.alignTab != 'right' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-right\"\n              size=\"short\"\n              id=\"bds-tabs-button-right\"\n              onClick={() => this.nextSlide()}\n              dataTest={this.dtButtonNext}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__content: true, tab_group__scrolled: this.contentScrollable }}\n            style={contentStyle}\n          >\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAA,IAAMA,EAAc,stF,ICQPC,EAAWC,EAAA,2BALxB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,gGAMUA,KAAeC,gBAA6C,KAC5DD,KAAoBE,qBAA6B,KAShDF,KAAWG,YAAa,MAExBH,KAAQI,SAAoC,OAE5CJ,KAAWK,YAAY,EAEvBL,KAAYM,aAAY,EAEzBN,KAAiBO,kBAAa,KAE9BP,KAAKQ,MAAgC,SAMrCR,KAAYS,aAAY,KAMxBT,KAAYU,aAAY,KA+BxBV,KAAAW,iBAAmB,SAACC,GAC1BA,EAAaC,SAAQ,SAACC,GACpBA,EAAQC,iBACN,eACA,WACEhB,EAAKiB,iBAAiBC,MAAMC,KAAKnB,EAAKE,iB,GAExC,MAEJ,GACF,EAMQD,KAAcmB,eAAG,W,QACvB,GAAIpB,EAAKqB,eAAiBrB,EAAKsB,mBAAoB,CACjD,KAAIC,EAAAvB,EAAKsB,sBAAkB,MAAAC,SAAA,SAAAA,EAAEC,eAAcC,EAAAzB,EAAKqB,iBAAa,MAAAI,SAAA,SAAAA,EAAED,aAAa,CAC1E,OAAO,I,EAGb,EAEQvB,KAAcyB,eAAG,WACvB,IAAMC,EAAiBT,MAAMC,KAAKnB,EAAKE,iBAAiB0B,QAAO,SAACC,GAAQ,OAAAA,EAAIC,IAAJ,IACxE,IAAKH,EAAeI,OAAQ,CAC1B/B,EAAKE,gBAAgB,GAAG4B,KAAO,I,CAEnC,EAEQ7B,KAAgB+B,iBAAG,WACzB,IAAK,IAAIC,EAAI,EAAGA,EAAIjC,EAAKE,gBAAgB6B,OAAQE,IAAK,CACpDjC,EAAKE,gBAAgB+B,GAAGC,cAAgBD,C,CAE5C,EAEQhC,KAAAgB,iBAAmB,SAACJ,GAC1B,IAAMsB,EAAatB,EAAauB,KAAI,SAACC,EAAMC,GACzC,OACEC,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,CAAAC,MAAOJ,EAAKI,MACZX,KAAMO,EAAKP,KACXI,cAAeI,EACfI,MAAOL,EAAKK,OACRL,EAAKM,UAAYC,WAAa,CAAED,QAASN,EAAKM,UAC9CN,EAAKQ,QAAUD,WAAa,CAAEC,MAAOR,EAAKQ,QAC1CR,EAAKS,cAAgBF,WAAa,CAAEE,YAAaT,EAAKS,cACtDT,EAAKU,eAAiBH,WAAa,CAAEG,aAAcV,EAAKU,eACxDV,EAAKW,OAASJ,WAAa,CAAEI,KAAMX,EAAKW,OACxCX,EAAKY,eAAiBL,WAAa,CAAEK,aAAcZ,EAAKY,eACxDZ,EAAKa,YAAcN,WAAa,CAAEM,UAAWb,EAAKa,YAClDb,EAAKc,aAAeP,WAAa,CAAEO,WAAYd,EAAKc,aACpDd,EAAKe,aAAeR,WAAa,CAAEQ,WAAYf,EAAKe,aACpDf,EAAKgB,YAAcT,WAAa,CAAES,UAAWhB,EAAKgB,YAClDhB,EAAKiB,iBAAmBV,WAAa,CAAEU,eAAgBjB,EAAKiB,iBAC5DjB,EAAKkB,cAAgBX,WAAa,CAAEW,YAAalB,EAAKkB,cACtDlB,EAAKmB,gBAAkBZ,WAAa,CAAEY,cAAenB,EAAKmB,gBAC1DnB,EAAKoB,WAAab,WAAa,CAAEa,SAAUpB,EAAKoB,UAExD,IACA,OAAQzD,EAAK0D,cAAgBvB,CAC/B,EAEQlC,KAAA0D,YAAc,SAACC,GACrB,IAAMC,EAAsB7D,EAAK0D,cAActB,KAAI,SAACC,GAClD,MAAO,CACLI,MAAOJ,EAAKI,MACZX,KAAM,MACNI,cAAeG,EAAKH,cAExB,IACAlC,EAAK0D,cAAgBG,EACrB,IAAK,IAAI5B,EAAI,EAAGA,EAAIjC,EAAKE,gBAAgB6B,OAAQE,IAAK,CACpD,GAAIjC,EAAKE,gBAAgB+B,GAAGC,eAAiB0B,EAAY,CACvD5D,EAAKE,gBAAgB+B,GAAGH,KAAO,K,KAC1B,CACL9B,EAAKE,gBAAgB+B,GAAGH,KAAO,KAC/B9B,EAAK8D,aAAaC,KAAK/D,EAAKE,gBAAgB+B,G,EAGlD,EAEQhC,KAAA+D,iBAAmB,SAACC,GAC1BjE,EAAKqB,cAAgB4C,CACvB,EAEQhE,KAAAiE,sBAAwB,SAACD,GAC/BjE,EAAKsB,mBAAqB2C,CAC5B,EAEQhE,KAAAkE,eAAiB,SAACC,GACxBpE,EAAKqE,eAAeN,KAAK/D,EAAKE,gBAAgBkE,GAChD,EAEQnE,KAASqE,UAAG,W,cAClB,IAAMC,IAAUhD,EAAAvB,EAAKqB,iBAAa,MAAAE,SAAA,SAAAA,EAAEC,eAAcC,EAAAzB,EAAKsB,sBAAoB,MAAAG,SAAA,SAAAA,EAAAD,aAC3E,IAAMgD,IAAaC,EAAAzE,EAAKsB,sBAAkB,MAAAmD,SAAA,SAAAA,EAAEjD,eAAckD,EAAA1E,EAAKqB,iBAAe,MAAAqD,SAAA,SAAAA,EAAAlD,aAC9E,IAAMmD,EAAeC,SAASJ,EAAWK,YACzC,IAAMC,EAAc9E,EAAKO,eAAewE,EAAA/E,EAAKqB,iBAAe,MAAA0D,SAAA,SAAAA,EAAAvD,aAE5DxB,EAAKO,aAAeuE,EAAcP,EAAUA,EAAUO,EACtD9E,EAAKK,SAAWyE,EAAcP,EAAU,QAAU,YAElDvE,EAAKM,YAAcqE,GAAgB3E,EAAKM,YAAcN,EAAKM,YAAc,EAAIqE,CAC/E,EAEQ1E,KAAS+E,UAAG,W,UAClB,IAAMR,IAAajD,EAAAvB,EAAKsB,sBAAkB,MAAAC,SAAA,SAAAA,EAAEC,eAAcC,EAAAzB,EAAKqB,iBAAe,MAAAI,SAAA,SAAAA,EAAAD,aAC9E,IAAMmD,EAAeC,SAASJ,EAAWK,YACzC,IAAMC,EAAc9E,EAAKO,eAAekE,EAAAzE,EAAKqB,iBAAe,MAAAoD,SAAA,SAAAA,EAAAjD,aAE5DxB,EAAKO,aAAeuE,EAAc,EAAI,EAAIA,EAC1C9E,EAAKK,SAAWyE,EAAc,EAAI,OAAS,YAE3C9E,EAAKM,YAAcqE,GAAgB3E,EAAKM,YAAcN,EAAKM,YAAc,EAAIqE,CAC/E,EA+BQ1E,KAAUgF,WAAG,SAACC,EAAMC,EAAOxC,EAASE,GAC1C,OACEuC,EACE,YAAAC,MAAO,CACLC,8CAA+C3C,EAC/C4C,4CAA6C1C,GAE/C2C,KAAK,UACLC,KAAMP,EACNQ,MAAOP,GAGb,EAEQlF,KAAA0F,YAAc,SAACC,EAAOC,EAAOX,EAAMY,EAAWC,GACpD,OACEX,EAAA,8BAA0B,UACxBA,EAAW,aAAAY,MAAOH,EAAO7C,KAAMkC,EAAMe,OAAQF,EAAQG,MAAON,EAAOO,UAAWL,IAGpF,CAmHD,CA7SChG,EAAAsG,UAAAC,oBAAA,WACEpG,KAAKC,gBAAkBD,KAAKc,QAAQuF,qBAAqB,gBACzDrG,KAAK+B,mBACL/B,KAAKyB,iBACLzB,KAAKgB,iBAAiBC,MAAMC,KAAKlB,KAAKC,kBACtCD,KAAKW,iBAAiBM,MAAMC,KAAKlB,KAAKC,iB,EAGxCJ,EAAAsG,UAAAG,iBAAA,WACEtG,KAAKE,qBAAuBF,KAAKc,QAAQyF,WAAWC,iBAClD,kC,EAIJ3G,EAAAsG,UAAAM,kBAAA,eAAA1G,EAAAC,KACEA,KAAK0G,QAAUC,OAAOC,aAAY,WAChC7G,EAAKI,YAAcJ,EAAKoB,gB,GACvB,I,EAeLtB,EAAAsG,UAAAU,qBAAA,WACEF,OAAOG,cAAc9G,KAAK0G,Q,EAwGpB7G,EAAAsG,UAAAY,cAAA,SAAcC,EAAO5E,GAC3B,GAAI4E,EAAMC,KAAO,QAAS,CACxB7E,EAAKM,QAAU1C,KAAKkE,eAAe9B,EAAKH,eAAiBjC,KAAK0D,YAAYtB,EAAKH,c,CAEjF,GAAI+E,EAAMC,KAAO,aAAc,CAC7BjH,KAAKE,qBAAqBkC,EAAKH,cAAgB,GAAGiF,O,CAEpD,GAAIF,EAAMC,KAAO,YAAa,CAC5BjH,KAAKE,qBAAqBkC,EAAKH,cAAgB,GAAGiF,O,GAI9CrH,EAAAsG,UAAAgB,iBAAA,SAAiBC,GACvB,IAAKA,EAAa,MAAO,GAEzB,OAAOA,EACJC,MAAM,KACN1F,QAAO,SAAA2F,GAAS,OAAAA,EAAMC,MAAN,IAChBC,QAAO,SAACC,EAAKH,GACN,IAAAI,EAAoBJ,EAAMD,MAAM,KAAKlF,KAAI,SAAAwF,GAAK,OAAAA,EAAEJ,MAAF,IAA7CK,EAAQF,EAAA,GAAEG,EAAKH,EAAA,GACtB,GAAIE,GAAYC,EAAO,CAErB,IAAMC,EAAgBF,EAASG,QAAQ,aAAa,SAACC,GAAM,OAAAA,EAAE,GAAGC,aAAL,IAC3DR,EAAIK,GAAiBD,C,CAEvB,OAAOJ,C,GACN,G,EAyBP5H,EAAAsG,UAAA+B,OAAA,W,MAAA,IAAAnI,EAAAC,K,MACE,IAAMmI,EAAgB,CAAEC,KAAM,GAAAC,OAAGrI,KAAKM,aAAY,OAGlD,IAAMgI,GAAUhH,EAAAtB,KAAKyD,iBAAe,MAAAnC,SAAA,SAAAA,EAAAiH,MAAK,SAAAnG,GAAQ,OAAAA,EAAKP,IAAL,IACjD,IAAMgB,GAAcyF,IAAO,MAAPA,SAAA,SAAAA,EAASzF,aAAc7C,KAAKmH,iBAAiBmB,EAAQzF,aAAe,GACxF,IAAMC,GAAewF,IAAO,MAAPA,SAAA,SAAAA,EAASxF,cAAe9C,KAAKmH,iBAAiBmB,EAAQxF,cAAgB,GAE3F,OACEqC,EAACqD,EAAI,CAAAvB,IAAA,4CACH9B,EAAA,OAAA8B,IAAA,2CAAK7B,MAAO,CAAEqD,UAAW,OACtBzI,KAAKG,aAAeH,KAAKI,UAAY,QACpC+E,EAAA,mBAAA8B,IAAA,2CACE7B,MAAM,0BACNrC,KAAK,aACLwC,KAAK,QACLpB,GAAG,uBACHuE,QAAS,WAAM,OAAA3I,EAAKgF,WAAL,EACfvB,SAAUxD,KAAKS,aACfkI,QAAQ,cAGZxD,EACE,OAAA8B,IAAA,2CAAA7B,MAAO,CAAEwD,kBAAmB,KAAMC,iBAAkB7I,KAAKG,aACzD2I,IAAK9I,KAAK+D,iBACVuD,MAAOzE,GAEPsC,EAAA,OAAA8B,IAAA,2CACE7B,OAAKsC,EAAA,CACHqB,yBAA0B,KAC1BC,wBAAyBhJ,KAAKG,aAC9BuH,EAAC,6BAAAW,OAA6BrI,KAAKQ,SAAWR,KAAKG,Y,GAErD2I,IAAK9I,KAAKiE,sBACVqD,MAAOa,GAENnI,KAAKyD,eACJzD,KAAKyD,cAActB,KAAI,SAACC,EAAMC,GAC5B,IAAM4G,EAAO7G,EAAKP,MAAQ,KAAO,OAAS,UAC1C,OACEsD,EACE,OAAAC,MAAO,CACL8D,+BAAgC,KAChCC,qCAAsC/G,EAAKP,KAC3CuH,wCAAyChH,EAAKM,SAEhDuE,IAAK5E,EACLgH,SAAS,IACTX,QAAS,WACP,OAAAtG,EAAKM,QAAU3C,EAAKmE,eAAe9B,EAAKH,eAAiBlC,EAAK2D,YAAYtB,EAAKH,cAA/E,EAEFqH,UAAW,SAACC,GAAO,OAAAxJ,EAAKgH,cAAcwC,EAAInH,EAAvB,GAElBA,EAAKY,eAAiB,QAAUZ,EAAKW,KAClChD,EAAKiF,WAAW5C,EAAKW,KAAMX,EAAKa,UAAWb,EAAKM,QAASN,EAAKQ,OAC9D,GACHR,EAAKmB,gBAAkB,QAAUnB,EAAKK,MACnC1C,EAAK2F,YACHtD,EAAKc,WACLd,EAAKe,WACLf,EAAKgB,UACLhB,EAAKiB,eACLjB,EAAKkB,aAEP,GACJ6B,EAAA,YACEC,MAAO,CACLC,8CAA+CjD,EAAKM,QACpD4C,4CAA6ClD,EAAKQ,OAEpD+F,QAAQ,QACRM,KAAMA,GAEL7G,EAAKI,OAEPJ,EAAKY,eAAiB,SAAWZ,EAAKW,KACnChD,EAAKiF,WAAW5C,EAAKW,KAAMX,EAAKa,UAAWb,EAAKM,QAASN,EAAKQ,OAC9D,GACHR,EAAKmB,gBAAkB,SAAWnB,EAAKK,MACpC1C,EAAK2F,YACHtD,EAAKc,WACLd,EAAKe,WACLf,EAAKgB,UACLhB,EAAKiB,eACLjB,EAAKkB,aAEP,G,MAMftD,KAAKG,aAAeH,KAAKI,UAAY,SACpC+E,EAAA,mBAAA8B,IAAA,2CACE7B,MAAM,0BACNrC,KAAK,cACLwC,KAAK,QACLpB,GAAG,wBACHuE,QAAS,WAAM,OAAA3I,EAAKsE,WAAL,EACfb,SAAUxD,KAAKU,aACfiI,QAAQ,cAGZxD,EAAA,OAAA8B,IAAA,2CACE7B,MAAO,CAAEoE,mBAAoB,KAAMC,oBAAqBzJ,KAAKO,mBAC7D+G,MAAOxE,GAEPqC,EAAA,QAAA8B,IAAA,+C,4HAnVY,I", "ignoreList": []}