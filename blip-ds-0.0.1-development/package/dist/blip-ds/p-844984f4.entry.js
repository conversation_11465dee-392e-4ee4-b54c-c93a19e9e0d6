import{r as t,c as a,h as e,H as i,a as s}from"./p-C3J6Z5OX.js";const r=[{value:"A",color:"system"},{value:"B",color:"success"},{value:"C",color:"warning"},{value:"D",color:"error"},{value:"E",color:"info"},{value:"F",color:"system"},{value:"G",color:"success"},{value:"H",color:"warning"},{value:"I",color:"error"},{value:"J",color:"info"},{value:"K",color:"system"},{value:"L",color:"success"},{value:"M",color:"warning"},{value:"N",color:"error"},{value:"O",color:"info"},{value:"P",color:"system"},{value:"Q",color:"success"},{value:"R",color:"warning"},{value:"S",color:"error"},{value:"T",color:"info"},{value:"U",color:"system"},{value:"V",color:"success"},{value:"X",color:"warning"},{value:"Y",color:"error"},{value:"W",color:"info"},{value:"Z",color:"system"}];const o=':host{position:relative;display:block;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.avatar{position:relative;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;-webkit-appearance:none;-moz-appearance:none;appearance:none;height:100%}.avatar::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.avatar:focus-visible{outline:none}.avatar:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.avatar__ellipsis{color:var(--color-surface-1, rgb(246, 246, 246))}.avatar__text{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__btn{border-radius:40px;border:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16));-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;width:100%;height:100%;overflow:hidden;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.avatar__btn__img{background-position:center;background-size:cover}.avatar__btn__text{color:var(--color-content-default, rgb(40, 40, 40));opacity:1;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__icon{color:var(--color-content-default, rgb(40, 40, 40));opacity:1;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__thumb{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer}.avatar__btn__thumb:before{content:"";position:absolute;inset:0;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0;-webkit-transition:all 0.5s;transition:all 0.5s}.avatar__btn__thumb__icon{position:relative;color:var(--color-surface-1, rgb(246, 246, 246));opacity:0;-webkit-transition:all 0.5s;transition:all 0.5s}.avatar__btn__name{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer;opacity:0;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__name__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__btn__empty{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer;opacity:0;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__empty__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__size--micro{width:24px;height:24px;min-width:24px;min-height:24px}.avatar__size--extra-small{width:32px;height:32px;min-width:32px;min-height:32px}.avatar__size--small{width:40px;height:40px;min-width:40px;min-height:40px}.avatar__size--standard{width:56px;height:56px;min-width:56px;min-height:56px}.avatar__size--large{width:64px;height:64px;min-width:64px;min-height:64px}.avatar__size--extra-large{width:72px;height:72px;min-width:72px;min-height:72px}.avatar__color--system .avatar__btn{background-color:var(--color-system, rgb(178, 223, 253))}.avatar__color--warning .avatar__btn{background-color:var(--color-warning, rgb(253, 233, 155))}.avatar__color--success .avatar__btn{background-color:var(--color-success, rgb(132, 235, 188))}.avatar__color--info .avatar__btn{background-color:var(--color-info, rgb(128, 227, 235))}.avatar__color--error .avatar__btn{background-color:var(--color-error, rgb(250, 190, 190))}.avatar__color--surface .avatar__btn{background-color:var(--color-surface-2, rgb(237, 237, 237));color:var(--color-content-disable, rgb(89, 89, 89))}.avatar:hover .avatar__btn__thumb:before{opacity:0.5}.avatar:hover .avatar__btn__thumb__icon{opacity:1}.avatar:hover .avatar__btn__text{opacity:0}.avatar:hover .avatar__btn__icon{opacity:0}.avatar:hover .avatar__btn__name{opacity:1}.avatar:hover .avatar__btn__empty{opacity:1}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}';const n=class{constructor(e){t(this,e);this.bdsClickAvatar=a(this,"bdsClickAvatar");this.bdsImageUpload=a(this,"bdsImageUpload");this.typoSize="fs-20";this.iconSize="large";this.name=null;this.thumbnail=null;this.size="standard";this.color="colorLetter";this.upload=false;this.openUpload=false;this.ellipsis=null;this.dataTest=null;this.handleOpenUpload=t=>{const a=this.el.shadowRoot.getElementById("file-input");if(t.type==="click"||t.type==="keydown"&&(t.key==="Enter"||t.key===" ")){a.click()}};this.selectTypoSize=t=>{switch(t){case"micro":this.typoSize="fs-12";this.iconSize="xx-small";break;case"extra-small":this.typoSize="fs-14";this.iconSize="x-small";break;case"small":this.typoSize="fs-16";this.iconSize="medium";break;case"standard":this.typoSize="fs-20";this.iconSize="x-large";break;case"large":this.typoSize="fs-24";this.iconSize="xxx-large";break;case"extra-large":this.typoSize="fs-32";this.iconSize="xxx-large";break;default:this.typoSize="fs-20";this.iconSize="medium"}};this.avatarBgColor=t=>{if(this.color!="colorLetter"){return this.color}else if(t){const a=r.find((a=>a.value===t));return a.color}}}onUploadClick(t){t.preventDefault();this.bdsClickAvatar.emit(t);if(this.openUpload){this.handleOpenUpload(t)}}onFileInputChange(t){const a=t.target;const e=a.files;if(e&&e.length>0){const t=e[0];const a=new FileReader;a.onload=t=>{const a=t.target.result;this.thumbnail=a;this.bdsImageUpload.emit(a)};a.readAsDataURL(t)}}componentWillRender(){this.hasThumb=this.thumbnail?this.thumbnail.length!==0?true:false:false}render(){const t=this.name?this.name.split(" "):[];const a=t.length?t.shift().charAt(0).toUpperCase():"";const s=t.length?t.pop().charAt(0).toUpperCase():"";this.selectTypoSize(this.size);const r={backgroundImage:`url(${this.hasThumb?this.thumbnail:null})`};return e(i,{key:"b4352d630f52526279c919971c8882e14d65000c"},e("input",{key:"567b5f3b5d2875e31ece31afbfa9e75c168e53c3",type:"file",id:"file-input",accept:"image/*",onChange:t=>this.onFileInputChange(t),style:{display:"none"}}),e("div",{key:"54b58c5162f627fcbb76e2b143f6186f0ba98070",class:{avatar:true,[`avatar__color--${this.name&&!this.hasThumb?this.avatarBgColor(a):this.hasThumb&&!this.name?"surface":!this.name&&!this.hasThumb?"surface":this.name&&this.hasThumb?this.avatarBgColor(a):null}`]:true,[`avatar__size--${this.size}`]:true,upload:this.upload||this.openUpload},onClick:t=>this.onUploadClick(t),tabindex:"0",onKeyDown:t=>this.onUploadClick(t),"data-test":this.dataTest},this.ellipsis?e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,variant:this.typoSize,tag:"span"},`+${this.ellipsis}`)):this.thumbnail?this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("div",{class:`avatar__btn__img avatar__size--${this.size}`,style:r}),e("div",{class:"avatar__btn__thumb"},e("bds-icon",{class:"avatar__btn__thumb__icon",name:"upload",theme:"outline",size:this.iconSize}))):e("div",{class:"avatar__btn"},e("div",{class:`avatar__btn__img avatar__size--${this.size}`,style:r})):this.name?this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,class:"avatar__btn__text",variant:this.typoSize,tag:"span"},a+s),e("div",{class:"avatar__btn__name"},e("bds-icon",{class:"avatar__btn__name__icon",name:"upload",theme:"outline",size:this.iconSize}))):e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,class:"avatar__text",variant:this.typoSize,tag:"span"},a+s)):this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("bds-icon",{class:"avatar__btn__icon",name:"user-default",theme:"outline",size:this.iconSize}),e("div",{class:"avatar__btn__empty"},e("bds-icon",{class:"avatar__btn__empty__icon",name:"upload",theme:"outline",size:this.iconSize}))):this.name===null&&!this.hasThumb?e("div",{class:"avatar__btn"},e("bds-icon",{class:"avatar__icon",name:"user-default",theme:"outline",size:this.iconSize})):""))}get el(){return s(this)}};n.style=o;export{n as bds_avatar};
//# sourceMappingURL=p-844984f4.entry.js.map