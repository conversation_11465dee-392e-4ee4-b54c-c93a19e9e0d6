{"version": 3, "names": ["progressBarCss", "BdsProgressBar", "constructor", "hostRef", "this", "percent", "size", "color", "text", "dataTest", "render", "styles", "width", "h", "Host", "key", "class", "progress_bar", "bar_behind", "progress", "style", "typo_progress", "variant"], "sources": ["src/components/progress-bar/progress-bar.scss?tag=bds-progress-bar&encapsulation=shadow", "src/components/progress-bar/progress-bar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n}\n\n.progress_bar {\n  box-sizing: border-box;\n  position: relative;\n  border-radius: 32px;\n  border: 1px solid $color-content-disable;\n  margin-bottom: 4px;\n\n  &.size_small {\n    height: 8px;\n    .bar_behind {\n      & .progress {\n        border-radius: 1px;\n      }\n    }\n  }\n\n  &.size_default {\n    height: 16px;\n    .bar_behind {\n      & .progress {\n        border-radius: 2px;\n      }\n    }\n  }\n\n  .bar_behind {\n    position: absolute;\n    inset: 0.5px 1px 1px 0.5px;\n    border-radius: 16px;\n    overflow: hidden;\n\n    & .progress {\n      position: absolute;\n      height: 100%;\n      @include animation();\n      overflow: hidden;\n\n      &.color {\n        &_default {\n          background-color: $color-extended-blue;\n        }\n        &_positive {\n          background-color: $color-extended-green;\n        }\n        &_information {\n          background-color: $color-extended-yellow;\n        }\n        &_warning {\n          background-color: $color-extended-red;\n        }\n      }\n\n      & .loading {\n        position: absolute;\n        left: -16px;\n        width: calc(100% + 16px);\n        height: 100%;\n        background: rgb(255, 255, 255);\n        background: linear-gradient(\n          90deg,\n          rgba(255, 255, 255, 0) 0%,\n          rgba(255, 255, 255, 0) 75%,\n          rgba(0, 0, 0, 0.26) 75%\n        );\n        background-size: 4px;\n        transform: skewX(-15deg);\n        animation-name: load;\n        animation-timing-function: linear;\n        animation-duration: 0.5s;\n        animation-iteration-count: infinite;\n      }\n    }\n  }\n}\n\n.typo_progress {\n  color: $color-content-default;\n}\n\n@keyframes load {\n  from {\n    left: -16px;\n  }\n  to {\n    left: 0;\n  }\n}\n", "import { Component, Host, Prop, h } from '@stencil/core';\n\nexport type progressBarSize = 'small' | 'default';\n\nexport type progressBarColor = 'default' | 'positive' | 'information' | 'warning';\n\n@Component({\n  tag: 'bds-progress-bar',\n  styleUrl: 'progress-bar.scss',\n  shadow: true,\n})\nexport class BdsProgressBar {\n  /**\n   * Percent, property to enter the progress bar status percentage value.\n   */\n  @Prop() percent?: number = 0;\n  /**\n   * Size, property to define size of component.\n   */\n  @Prop() size?: progressBarSize = 'default';\n  /**\n   * Text, property to define status of component.\n   */\n  @Prop() color?: progressBarColor = 'default';\n  /**\n   * Text, property to enable the bar info text.\n   */\n  @Prop() text?: string = '';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    const styles = { width: `${this.percent ? (this.percent > 100 ? 100 : this.percent) : 0}%` };\n    return (\n      <Host>\n        <div class={{ progress_bar: true, [`size_${this.size}`]: true }} data-test={this.dataTest}>\n          <div class={{ bar_behind: true }}>\n            <div class={{ progress: true, [`color_${this.color}`]: true }} style={styles}></div>\n          </div>\n        </div>\n        {this.text && (\n          <div class={{ typo_progress: true }}>\n            <bds-typo variant=\"fs-14\">{this.text}</bds-typo>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "6CAAA,MAAMA,EAAiB,8sE,MCWVC,EAAc,MAL3B,WAAAC,CAAAC,G,UASUC,KAAOC,QAAY,EAInBD,KAAIE,KAAqB,UAIzBF,KAAKG,MAAsB,UAI3BH,KAAII,KAAY,GAIhBJ,KAAQK,SAAY,IAmB7B,CAjBC,MAAAC,GACE,MAAMC,EAAS,CAAEC,MAAO,GAAGR,KAAKC,QAAWD,KAAKC,QAAU,IAAM,IAAMD,KAAKC,QAAW,MACtF,OACEQ,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAK,OAAAE,IAAA,2CAAAC,MAAO,CAAEC,aAAc,KAAM,CAAC,QAAQb,KAAKE,QAAS,MAAmB,YAAAF,KAAKK,UAC/EI,EAAA,OAAAE,IAAA,2CAAKC,MAAO,CAAEE,WAAY,OACxBL,EAAK,OAAAE,IAAA,2CAAAC,MAAO,CAAEG,SAAU,KAAM,CAAC,SAASf,KAAKG,SAAU,MAAQa,MAAOT,MAGzEP,KAAKI,MACJK,EAAA,OAAAE,IAAA,2CAAKC,MAAO,CAAEK,cAAe,OAC3BR,EAAA,YAAAE,IAAA,2CAAUO,QAAQ,SAASlB,KAAKI,O", "ignoreList": []}