{"version": 3, "names": ["cardTitleCss", "CardTitle", "exports", "class_1", "prototype", "render", "h", "key", "variant", "tag", "margin", "bold", "this", "text"], "sources": ["src/components/card/card-title/card-title.scss?tag=bds-card-title&encapsulation=shadow", "src/components/card/card-title/card-title.tsx"], "sourcesContent": [null, "import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-title',\n  styleUrl: 'card-title.scss',\n  shadow: true,\n})\nexport class CardTitle {\n  /**\n   *Set the card title.\n   */\n  @Prop() text?: string;\n\n  render() {\n    return (\n      <bds-typo variant=\"fs-20\" tag=\"h4\" margin={false} bold=\"bold\">\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAe,G,ICORC,EAASC,EAAA,4B,wBAMpBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAAA,YAAAC,IAAA,2CAAUC,QAAQ,QAAQC,IAAI,KAAKC,OAAQ,MAAOC,KAAK,QACpDC,KAAKC,K,WATQ,I", "ignoreList": []}