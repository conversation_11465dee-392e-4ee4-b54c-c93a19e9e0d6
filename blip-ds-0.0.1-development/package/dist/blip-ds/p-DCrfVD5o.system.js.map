{"version": 3, "file": "p-DCrfVD5o.system.js", "sources": ["src/components/rict-text/languages/pt_BR.tsx", "src/components/rict-text/languages/es_ES.tsx", "src/components/rict-text/languages/en_US.tsx", "src/components/rict-text/languages/index.ts", "src/components/rict-text/rich-text.scss?tag=bds-rich-text", "src/components/rict-text/rich-text.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    bold: 'Negrito',\n    italic: 'It<PERSON>lico',\n    strike: '<PERSON><PERSON><PERSON>',\n    underline: 'Sublin<PERSON>o',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: '<PERSON><PERSON><PERSON> à esquerda',\n    align_center: '<PERSON><PERSON>ar ao centro',\n    align_right: 'Alinhar à direita',\n    unordered_list: 'Lista não ordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Citação',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpar formatação',\n    expand: 'Expandir',\n  },\n];\n", "export const esTerms = [\n  {\n    bold: 'Negrita',\n    italic: '<PERSON>urs<PERSON>',\n    strike: 'Tacha<PERSON>',\n    underline: 'Subrayado',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: 'Alinear a la izquierda',\n    align_center: 'Alinear al centro',\n    align_right: 'Alinear a la derecha',\n    unordered_list: 'Lista desordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Cita',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpiar formato',\n    expand: 'Expandir',\n  },\n];\n", "export const enTerms = [\n  {\n    bold: 'Bold',\n    italic: 'Italic',\n    strike: 'Strikethrough',\n    underline: 'Underline',\n    link: 'Link',\n    code: 'Code',\n    align_left: 'Align left',\n    align_center: 'Align center',\n    align_right: 'Align right',\n    unordered_list: 'Unordered list',\n    ordered_list: 'Ordered list',\n    quote: 'Quote',\n    h1: 'Heading 1',\n    h2: 'Heading 2',\n    h3: 'Heading 3',\n    h4: 'Heading 4',\n    h5: 'Heading 5',\n    h6: 'Heading 6',\n    clear_formatting: 'Clear formatting',\n    expand: 'Expand',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "@use '../../globals/helpers' as *;\n\n.rich-text {\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 8px;\n  gap: 8px;\n  border: 1px solid $color-border-1;\n  border-radius: 16px;\n  background-color: $color-surface-1;\n\n  &-top {\n    .format-buttons {\n      order: 1;\n    }\n    .preview {\n      order: 2;\n    }\n  }\n\n  &-bottom {\n    .format-buttons {\n      order: 2;\n    }\n    .preview {\n      order: 1;\n    }\n  }\n\n  &.active {\n    border-color: $color-primary;\n    box-shadow: 0 0 0 2px $color-info;\n  }\n\n  .format-buttons {\n    display: none !important;\n\n    &-active {\n      display: flex !important;\n      position: relative;\n      background-color: $color-surface-0;\n      border: 1px solid $color-border-1;\n      border-radius: 16px;\n      padding: 8px;\n    }\n\n    .style-onhover {\n      position: absolute;\n      background-color: $color-surface-1;\n      border-radius: 32px;\n      bottom: -32px;\n      right: 0;\n      opacity: 0;\n      -webkit-transition: opacity ease-in-out 0.5s;\n      -moz-transition: opacity ease-in-out 0.5s;\n      transition: opacity ease-in-out 0.5s;\n      pointer-events: none;\n\n      &.active {\n        opacity: 1;\n      }\n    }\n\n    .accordion-header {\n      width: 100%;\n      position: relative;\n      padding-right: 40px;\n\n      .buttons-list {\n        column-gap: 8px;\n\n        .editor-bar {\n          width: 0;\n          margin-right: -8px;\n        }\n\n        & bds-tooltip {\n          -webkit-transition: height ease-in-out 0.25s;\n          -moz-transition: height ease-in-out 0.25s;\n          transition: height ease-in-out 0.25s;\n          height: 0px;\n\n          & > bds-button,\n          & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n            height: 0;\n            opacity: 0;\n            display: block;\n            overflow: hidden;\n            -webkit-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            -moz-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n          }\n\n          &.active {\n            height: 32px;\n            & > bds-button,\n            & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n              overflow: inherit;\n              height: 32px;\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .arrow-down {\n        position: absolute;\n        right: 0;\n        top: 0;\n        display: none;\n        &.active {\n          display: block;\n        }\n      }\n    }\n  }\n  .preview {\n    box-sizing: border-box;\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    padding: 8px;\n    -webkit-transition: height ease-in-out 0.25s;\n    -moz-transition: height ease-in-out 0.25s;\n    transition: height ease-in-out 0.25s;\n\n    .editor-uai-design-system {\n      min-height: 48px;\n      height: 100%;\n      background-color: transparent;\n      font-size: 1rem;\n      line-height: 1.5;\n      overflow-y: auto;\n      outline: none;\n      font-family: $font-family;\n      font-style: normal;\n      font-weight: normal;\n      color: $color-content-default;\n      @include custom-scroll;\n\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5,\n      h6,\n      ul,\n      ol,\n      blockquote {\n        margin: 0 0 8px 0;\n      }\n\n      h1 {\n        font-size: 32px;\n        font-weight: 600;\n      }\n      h2 {\n        font-size: 28px;\n        font-weight: 600;\n      }\n      h3 {\n        font-size: 24px;\n        font-weight: 600;\n      }\n      h4 {\n        font-size: 20px;\n        font-weight: 600;\n      }\n      h5 {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      h6 {\n        font-size: 12px;\n        font-weight: 600;\n      }\n\n      a {\n        text-decoration: none;\n        color: $color-primary;\n      }\n\n      blockquote {\n        padding: 4px 16px 4px 32px;\n        font-size: 14px;\n        position: relative;\n        display: inline-block;\n\n        &::before,\n        &::after {\n          content: '\"';\n          position: absolute;\n          font-size: 24px;\n          color: $color-content-ghost;\n        }\n\n        &::before {\n          left: 8px;\n          top: -6px;\n        }\n\n        &::after {\n          right: 0px;\n          bottom: 0px;\n        }\n      }\n      code {\n        font-family: monospace;\n        font-size: 12px;\n        background-color: $color-surface-2;\n        padding: 4px;\n        border-radius: 4px;\n      }\n    }\n  }\n}\n\n/* Editor */\n", "import { Element, Component, h, Host, Prop, State, Watch, Event, EventEmitter } from '@stencil/core';\nimport { getParentsUntil } from '../../utils/position-element';\nimport { languages } from './rich-text-interface';\nimport { termTranslate } from './languages';\n\nexport type positionBar = 'top' | 'bottom';\n\n@Component({\n  tag: 'bds-rich-text',\n  styleUrl: 'rich-text.scss',\n  shadow: false,\n})\nexport class RichText {\n  private buttonsListElement?: HTMLElement = null;\n  private buttonsEditElements?: HTMLCollectionOf<HTMLBdsTooltipElement> = null;\n  private editor?: HTMLElement = null;\n  private dropDownLink?: HTMLBdsDropdownElement = null;\n  private inputSetLink?: HTMLBdsInputElement;\n\n  @Element() el: HTMLElement;\n  @State() buttomBoldActive?: boolean = false;\n  @State() buttomItalicActive?: boolean = false;\n  @State() buttomStrikeActive?: boolean = false;\n  @State() buttomUnderlineActive?: boolean = false;\n  @State() buttomCodeActive?: boolean = false;\n  @State() buttomLinkActive?: boolean = false;\n  @State() buttomLinkValidDisabled?: boolean = true;\n  @State() buttomAlignLeftActive?: boolean = false;\n  @State() buttomAlignCenterActive?: boolean = false;\n  @State() buttomAlignRightActive?: boolean = false;\n  @State() buttomUnorderedListActive?: boolean = false;\n  @State() buttomOrderedListActive?: boolean = false;\n  @State() buttomQuoteActive?: boolean = false;\n  @State() buttomH1Active?: boolean = false;\n  @State() buttomH2Active?: boolean = false;\n  @State() buttomH3Active?: boolean = false;\n  @State() buttomH4Active?: boolean = false;\n  @State() buttomH5Active?: boolean = false;\n  @State() buttomH6Active?: boolean = false;\n  @State() buttomAccordionActive?: boolean = false;\n  @State() headerHeight?: string = '32px';\n  @State() hasSelectionRange?: boolean = false;\n  @State() selectedLinesList?: { element: HTMLElement }[] = null;\n  @State() treeElementsEditor?: HTMLElement[] = null;\n  @State() styleSectorActive?: string = null;\n  @State() styleOnHover?: string = 'teste';\n  @State() whenSelectionLink?: Range = null;\n  @State() linkButtonInput?: string = null;\n  @State() insideComponent?: boolean = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * weightButton to define if component has Bold Control.\n   */\n  @Prop() weightButton?: boolean = true;\n  /**\n   * italicButton to define if component has Italic Control.\n   */\n  @Prop() italicButton?: boolean = true;\n  /**\n   * strikeThroughbutton to define if component has Strike Control.\n   */\n  @Prop() strikeThroughButton?: boolean = true;\n  /**\n   * underlineButton to define if component has Underline Control.\n   */\n  @Prop() underlineButton?: boolean = true;\n  /**\n   * linkButton to define if component has Link Control.\n   */\n  @Prop() linkButton?: boolean = true;\n  /**\n   * codeButton to define if component has Code Control.\n   */\n  @Prop() codeButton?: boolean = true;\n  /**\n   * alignmentButtons to define if component has TextAlign Control.\n   */\n  @Prop() alignmentButtons?: boolean = true;\n  /**\n   * listButtons to define if component has List Control.\n   */\n  @Prop() listButtons?: boolean = true;\n  /**\n   * quoteButton to define if component has Quote Control.\n   */\n  @Prop() quoteButton?: boolean = true;\n  /**\n   * headingButtons to define if component has Heading Control.\n   */\n  @Prop() headingButtons?: boolean = true;\n  /**\n   * unstyledButton to define if component has Unstyled Control.\n   */\n  @Prop() unstyledButton?: boolean = true;\n  /**\n   * height is the prop to define height of component.\n   */\n  @Prop() height?: string = null;\n  /**\n   * maxHeight is the prop to define max height of component.\n   */\n  @Prop() maxHeight?: string = null;\n  /**\n   * positionBar is the prop to define max height of component.\n   */\n  @Prop() positionBar?: positionBar = 'top';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsRichTextChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsRichTextInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  componentDidLoad() {\n    if (this.editor.innerHTML.trim() === '') {\n      this.editor.innerHTML = '<p class=\"line\"><br></p>';\n    }\n    if (\n      this.weightButton ||\n      this.italicButton ||\n      this.strikeThroughButton ||\n      this.underlineButton ||\n      this.linkButton ||\n      this.codeButton ||\n      this.alignmentButtons ||\n      this.listButtons ||\n      this.quoteButton ||\n      this.headingButtons ||\n      this.unstyledButton\n    ) {\n      this.buttonsEditElements = this.buttonsListElement.getElementsByTagName(\n        'bds-tooltip',\n      ) as HTMLCollectionOf<HTMLBdsTooltipElement>;\n      this.accordionHeader(false);\n      this.editor.parentElement.style.height = `calc(100% - 56px)`;\n    } else {\n      this.editor.parentElement.style.height = `100%`;\n    }\n  }\n\n  @Watch('weightButton')\n  @Watch('italicButton')\n  @Watch('strikeThroughButton')\n  @Watch('underlineButton')\n  @Watch('linkButton')\n  @Watch('codeButton')\n  @Watch('alignmentButtons')\n  @Watch('listButtons')\n  @Watch('quoteButton')\n  @Watch('headingButtons')\n  @Watch('unstyledButton')\n  protected buttomsHeaderChanged(): void {\n    setTimeout(() => this.accordionHeader(this.buttomAccordionActive), 500);\n  }\n\n  @Watch('buttomAccordionActive')\n  protected buttomAccordionActiveChanged(): void {\n    this.accordionHeader(this.buttomAccordionActive);\n  }\n\n  private updateToolbarState() {\n    const selection = window.getSelection();\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    this.treeElementsEditor = getParentsUntil(parentElement, '.editor-uai-design-system');\n  }\n\n  // Coloca o cursor no final do editor\n  private accordionHeader(value: boolean) {\n    const allbuttonsEditElementsWidth = this.buttonsEditElements.length * 40;\n    const buttonsListWidth = this.buttonsListElement.offsetWidth;\n    const buttonAccordion = this.el.querySelector('#buttonAccordion') as HTMLBdsButtonElement;\n    if (buttonsListWidth < allbuttonsEditElementsWidth) {\n      buttonAccordion.classList.add('active');\n    } else {\n      buttonAccordion.classList.remove('active');\n    }\n    const diferrence = (buttonsListWidth * this.buttonsEditElements.length) / allbuttonsEditElementsWidth;\n    const numberOfColumns = Math.ceil(allbuttonsEditElementsWidth / buttonsListWidth);\n    const allbuttonsEditElements = Array.from(this.buttonsEditElements);\n    allbuttonsEditElements.slice(0, Math.floor(diferrence)).forEach((element) => {\n      element.classList.add('active');\n    });\n    if (value) {\n      allbuttonsEditElements.forEach((element) => {\n        element.classList.add('active');\n        this.editor.parentElement.style.height = `calc(100% - ${numberOfColumns * 32 + 24}px)`;\n      });\n    } else {\n      allbuttonsEditElements.slice(Math.floor(diferrence)).forEach((element) => {\n        element.classList.remove('active');\n        this.editor.parentElement.style.height = `calc(100% - 56px)`;\n      });\n    }\n  }\n\n  @Watch('treeElementsEditor')\n  protected treeElementsEditorChanged(value): void {\n    const tagList = value.map((element) => element?.tagName.toLowerCase());\n    const tagVerifyName = (tag) => tagList.includes(tag);\n    const getLine = value.find((el) => el?.classList.contains('line'));\n    this.buttomBoldActive = tagVerifyName('b');\n    this.buttomItalicActive = tagVerifyName('i');\n    this.buttomStrikeActive = tagVerifyName('strike');\n    this.buttomUnderlineActive = tagVerifyName('u');\n    this.buttomLinkActive = tagVerifyName('a');\n    this.buttomCodeActive = tagVerifyName('code');\n    this.buttomAlignLeftActive = getLine?.style.textAlign === 'left';\n    this.buttomAlignCenterActive = getLine?.style.textAlign === 'center';\n    this.buttomAlignRightActive = getLine?.style.textAlign === 'right';\n    this.buttomUnorderedListActive = tagList[0] === 'ul';\n    this.buttomOrderedListActive = tagList[0] === 'ol';\n    this.buttomQuoteActive = tagVerifyName('blockquote');\n    this.buttomH1Active = tagVerifyName('h1');\n    this.buttomH2Active = tagVerifyName('h2');\n    this.buttomH3Active = tagVerifyName('h3');\n    this.buttomH4Active = tagVerifyName('h4');\n    this.buttomH5Active = tagVerifyName('h5');\n    this.buttomH6Active = tagVerifyName('h6');\n  }\n\n  private refButtonsListElement = (el: HTMLElement): void => {\n    this.buttonsListElement = el;\n  };\n  private refeditorElement = (el: HTMLElement): void => {\n    this.editor = el;\n  };\n  private refDropDownLinkElement = (el: HTMLBdsDropdownElement): void => {\n    this.dropDownLink = el;\n  };\n\n  private refInputSetLink = (el: HTMLBdsInputElement): void => {\n    this.inputSetLink = el;\n  };\n\n  private clearToolbar = () => {\n    this.buttomBoldActive = false;\n    this.buttomItalicActive = false;\n    this.buttomStrikeActive = false;\n    this.buttomUnderlineActive = false;\n    this.buttomLinkActive = false;\n    this.buttomCodeActive = false;\n    this.buttomAlignLeftActive = false;\n    this.buttomAlignCenterActive = false;\n    this.buttomAlignRightActive = false;\n    this.buttomUnorderedListActive = false;\n    this.buttomOrderedListActive = false;\n    this.buttomQuoteActive = false;\n    this.buttomH1Active = false;\n    this.buttomH2Active = false;\n    this.buttomH3Active = false;\n    this.buttomH4Active = false;\n    this.buttomH5Active = false;\n    this.buttomH6Active = false;\n  };\n\n  private setheaderHeight = () => {\n    this.buttomAccordionActive = !this.buttomAccordionActive;\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    const range = selection.getRangeAt(0);\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  };\n\n  private onBlur = () => {\n    this.el.classList.remove('active');\n    if (this.insideComponent === false) {\n      this.clearToolbar();\n    }\n    this.bdsBlur.emit();\n  };\n\n  private onFocus = () => {\n    this.el.classList.add('active');\n    this.bdsFocus.emit();\n  };\n\n  // Função para ajustar parágrafos durante a edição\n  private onInput(ev: InputEvent) {\n    ev.preventDefault();\n    this.bdsRichTextInput.emit(ev);\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const currentNode = range.startContainer;\n\n    // Se o nó atual é uma `div`, converta-o em um `p`\n    if (currentNode.nodeType === Node.ELEMENT_NODE && (currentNode as HTMLElement).tagName === 'DIV') {\n      const divElement = currentNode as HTMLElement;\n\n      const pElement = document.createElement('p');\n      pElement.classList.add('line');\n      pElement.innerHTML = divElement.innerHTML;\n\n      divElement.parentNode.replaceChild(pElement, divElement);\n    }\n\n    // Garante que novas linhas (Enter) criem <p> ao invés de <div>\n    this.editor.querySelectorAll('div').forEach((div) => {\n      const p = document.createElement('p');\n      p.classList.add('line');\n      p.innerHTML = div.innerHTML;\n      div.replaceWith(p);\n    });\n  }\n\n  private onKeydown = (event: KeyboardEvent) => {\n    if (event.key === 'Backspace') {\n      const selection = window.getSelection();\n      if (!selection || selection.rangeCount === 0) return;\n\n      const range = selection.getRangeAt(0);\n      const startNode = range.startContainer;\n\n      // Encontra o elemento de bloco que contém o cursor\n      let blockElement = startNode.nodeType === Node.TEXT_NODE ? startNode.parentElement : (startNode as HTMLElement);\n\n      while (blockElement && !blockElement.classList.contains('line') && blockElement !== this.editor) {\n        blockElement = blockElement.parentElement;\n      }\n\n      // Se o elemento atual for um <blockquote> e estiver vazio, removê-lo\n      if (\n        blockElement &&\n        blockElement.tagName === 'BLOCKQUOTE' &&\n        blockElement.classList.contains('line') &&\n        blockElement.innerText.length <= 1\n      ) {\n        event.preventDefault(); // Impede a exclusão padrão\n        blockElement.remove(); // Remove apenas o blockquote vazio\n      }\n    }\n    if (this.editor.textContent.length === 0 && event.key === 'Backspace') {\n      event.preventDefault();\n      this.editor.innerHTML = `<p class=\"line\"><br></p>`;\n      this.setCursorToEnd();\n    }\n    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {\n      event.preventDefault(); // Impede o Ctrl+Z\n      event.stopPropagation(); // Evita que afete outros elementos\n    }\n  };\n\n  // Controle a navegação do componente\n  private onFocusEditorBar(ev: FocusEvent) {\n    const editorBar = ev.target as HTMLElement;\n    const NextButton = editorBar.nextElementSibling.querySelector('bds-button');\n    const ElementToFocus = NextButton.shadowRoot.querySelector('.focus') as HTMLElement;\n    ElementToFocus.focus();\n    this.buttomAccordionActive = true;\n  }\n\n  // Coloca o cursor no final do editor\n  private setCursorToEnd() {\n    const range = document.createRange();\n    const sel = window.getSelection();\n    range.selectNodeContents(this.editor);\n    range.collapse(false);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n\n  private tagName(tag: string, tagList: HTMLElement[]): boolean {\n    const value = tagList.map((element) => element?.tagName.toLowerCase());\n    return value.includes(tag);\n  }\n\n  private wrapSelection(ev: CustomEvent, tag: string, link?: string) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n\n    const tagList = getParentsUntil(parentElement, '.line');\n    const isTagApplied = this.tagName(tag, tagList);\n\n    // Se a seleção estiver vazia, cria um espaço invisível para edição\n    let content: DocumentFragment;\n    let collapsedCursor = false;\n\n    // Se a tag já está aplicada e o usuário quer remover\n    if (isTagApplied) {\n      const appliedTag = tagList.find((el) => el.tagName.toLowerCase() === tag);\n      if (appliedTag) {\n        const parent = appliedTag.parentElement;\n        const isFullSelection = range.toString().trim() === appliedTag.textContent.trim();\n        const isAtEndOfTag = range.endOffset === appliedTag.textContent.length;\n\n        if (isFullSelection && parent) {\n          // Remove a tag se toda a seleção corresponde ao conteúdo da tag\n          while (appliedTag.firstChild) {\n            parent.insertBefore(appliedTag.firstChild, appliedTag);\n          }\n          parent.removeChild(appliedTag);\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        } else if (isAtEndOfTag) {\n          // Se o cursor está no final da tag, move para fora dela\n          content = document.createDocumentFragment();\n          const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n          content.appendChild(placeholder);\n          collapsedCursor = true;\n          const newRange = document.createRange();\n          newRange.setStartAfter(appliedTag); // Define o início depois do elemento\n          newRange.setEndAfter(appliedTag);\n          newRange.insertNode(content);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n          this.updateToolbarState();\n        } else {\n          // Se o cursor está no final da tag, move para fora dela\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        }\n      }\n      return;\n    }\n\n    if (range.collapsed) {\n      content = document.createDocumentFragment();\n      const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n      content.appendChild(placeholder);\n      collapsedCursor = true;\n    } else {\n      content = range.extractContents();\n    }\n\n    // Remove tags desnecessárias dentro da seleção\n    content.querySelectorAll('*').forEach((element) => {\n      while (element.firstChild) {\n        element.parentNode.insertBefore(element.firstChild, element);\n      }\n      element.remove();\n    });\n\n    // Cria a nova tag e aplica o conteúdo extraído\n    const wrapper = document.createElement(tag);\n    if (tag === 'a' && link) {\n      wrapper.setAttribute('href', link);\n    }\n    wrapper.appendChild(content);\n    range.insertNode(wrapper);\n\n    // Remove tags vazias no editor\n    this.editor.querySelectorAll('*').forEach((el) => {\n      if (!el.textContent.trim() && el.children.length === 0) {\n        el.remove();\n      }\n    });\n\n    // Se o cursor estava no meio da edição, mantém a seleção original\n    const newRange = document.createRange();\n    if (collapsedCursor) {\n      newRange.setStart(wrapper, 0);\n      newRange.setEnd(wrapper, 1);\n    } else {\n      newRange.setStartBefore(wrapper.firstChild || wrapper);\n      newRange.setEndAfter(wrapper.lastChild || wrapper);\n    }\n    selection.removeAllRanges();\n\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    // Emite o evento para atualizar o estado\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  private wrapSelectionLine(tag: string, enableLinesReturn: boolean = false) {\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Verifica se todas as linhas já possuem a tag escolhida\n    const allAreSameTag = [...selectedLines].every((line) =>\n      tag === 'li' ? false : line.tagName.toLowerCase() === tag,\n    );\n\n    const returnSelected: HTMLElement[] = [...selectedLines].map((el) => {\n      const newElement = document.createElement(allAreSameTag ? 'p' : tag);\n      newElement.classList.add('line');\n      newElement.innerHTML = el.innerHTML;\n      el.replaceWith(newElement);\n      return newElement;\n    });\n\n    if (enableLinesReturn) {\n      this.selectedLinesList = returnSelected.map((element) => ({ element }));\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    const newRange = document.createRange();\n    let lastNode = returnSelected[0].lastChild;\n\n    // Se não houver filhos, cria um nó de texto para evitar erro\n    if (!lastNode) {\n      lastNode = document.createTextNode('');\n      returnSelected[0].appendChild(lastNode);\n    }\n\n    // Se o último nó for outro elemento, busca um nó de texto dentro dele\n    while (lastNode && lastNode.nodeType !== Node.TEXT_NODE) {\n      lastNode = lastNode.lastChild || lastNode;\n    }\n\n    // Define o range no final do último nó de texto\n    newRange.setStart(lastNode, lastNode.textContent?.length || 0);\n    newRange.setEnd(lastNode, lastNode.textContent?.length || 0);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para aplicar alinhamento ao texto selecionado\n  private alignText(ev: CustomEvent, alignment: 'left' | 'center' | 'right' | 'justify') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    let blockElement = range.startContainer as HTMLElement;\n\n    // Percorre os elementos até encontrar um bloco válido que tenha a classe \"line\"\n    while (blockElement && blockElement !== this.editor) {\n      if (blockElement.nodeType === Node.ELEMENT_NODE && blockElement.classList.contains('line')) {\n        break;\n      }\n      blockElement = blockElement.parentElement;\n    }\n\n    // Se encontrou um elemento de bloco com a classe \"line\"\n    if (blockElement && blockElement !== this.editor) {\n      // Verifica se o alinhamento já está aplicado\n      const currentAlignment = (blockElement as HTMLElement).style.textAlign;\n      if (currentAlignment === alignment) {\n        // Se já estiver alinhado, remove o alinhamento\n        (blockElement as HTMLElement).style.textAlign = '';\n      } else {\n        // Caso contrário, aplica o alinhamento\n        (blockElement as HTMLElement).style.textAlign = alignment;\n      }\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createHeading(ev: CustomEvent, type: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine(type, true);\n    const firstItemList = this.selectedLinesList[0]?.element;\n    const firstParent = firstItemList.parentElement.previousElementSibling;\n    const lastParent = firstItemList.parentElement.nextElementSibling;\n    const parent = firstItemList.parentElement;\n    if (parent.tagName.toLowerCase() === 'ul') {\n      this.selectedLinesList.forEach((item) => {\n        if (firstParent) {\n          firstParent.insertAdjacentElement('afterend', item.element);\n        } else if (lastParent) {\n          lastParent.insertAdjacentElement('beforebegin', item.element);\n        } else {\n          this.editor.insertAdjacentElement('afterbegin', item.element);\n        }\n      });\n      if (Array.from(parent.getElementsByTagName('li')).length == 0) {\n        parent.remove();\n      }\n    }\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createList(ev: CustomEvent, type: 'ol' | 'ul') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine('li', true);\n    const firstItemList = this.selectedLinesList[0].element;\n    const lastItemList = this.selectedLinesList[this.selectedLinesList.length - 1]?.element;\n    const wrapper = document.createElement(type);\n    const parent = firstItemList.parentElement;\n\n    if (!this.verifyList(firstItemList, lastItemList)) {\n      parent.insertBefore(wrapper, firstItemList);\n      this.selectedLinesList.forEach((item) => {\n        wrapper.appendChild(item.element);\n      });\n    } else {\n      const parentListElements = parent.getElementsByTagName('li');\n      const parentList = Array.from(parentListElements).map((element) => ({ element }));\n\n      if (parentList.length == this.selectedLinesList.length) {\n        if (type !== parent.tagName.toLowerCase()) {\n          wrapper.innerHTML = parent.innerHTML;\n          parent.parentNode.replaceChild(wrapper, parent);\n        } else {\n          this.selectedLinesList.forEach((item) => {\n            const tagList = parent.parentElement.tagName.toLowerCase() === 'li' ? 'li' : 'p';\n            const newElement = document.createElement(tagList);\n            newElement.classList.add('line');\n            newElement.innerHTML = item.element.innerHTML;\n            if (parent.parentElement.tagName.toLowerCase() === 'li') {\n              parent.parentElement.insertAdjacentElement('afterend', newElement);\n            } else {\n              parent.previousElementSibling.insertAdjacentElement('afterend', newElement);\n            }\n            parent.removeChild(item.element);\n          });\n          parent.remove();\n        }\n      } else {\n        // parent.insertBefore(wrapper, firstItemList);\n        firstItemList.previousElementSibling.insertAdjacentElement('beforeend', wrapper);\n        this.selectedLinesList.forEach((item) => {\n          wrapper.appendChild(item.element);\n        });\n      }\n    }\n  }\n\n  private addSelectionLink(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      this.dropDownLink.setOpen();\n    }\n    this.editor.focus();\n    const selection = window.getSelection();\n    this.whenSelectionLink = selection.getRangeAt(0);\n    const ElementToFocus = this.inputSetLink.shadowRoot.querySelector('.input__container__text') as HTMLInputElement;\n    ElementToFocus.focus();\n  }\n\n  private addLinkInput(ev: InputEvent) {\n    ev.preventDefault();\n    const input = ev.target as HTMLInputElement | null;\n    this.linkButtonInput = input.value;\n    if (this.linkButtonInput.length > 0) {\n      this.buttomLinkValidDisabled = false;\n    } else {\n      this.buttomLinkValidDisabled = true;\n    }\n  }\n\n  private createLinkKeyDown(ev: KeyboardEvent) {\n    if (ev.key == 'Enter') {\n      this.createLink(ev);\n    }\n  }\n\n  private createLink(ev) {\n    ev.preventDefault();\n    const selection = window.getSelection();\n    selection.removeAllRanges();\n    selection.addRange(this.whenSelectionLink);\n    this.wrapSelection(ev, 'a', this.linkButtonInput);\n    if (this.dropDownLink) {\n      this.dropDownLink.setClose();\n    }\n  }\n\n  private verifyList(firstItem: HTMLElement, lastItem: HTMLElement) {\n    const firstItemValue =\n      firstItem.parentElement.tagName.toLowerCase() === 'ul' || firstItem.parentElement.tagName.toLowerCase() === 'ol';\n    const lastItemValue =\n      lastItem.parentElement.tagName.toLowerCase() === 'ul' || lastItem.parentElement.tagName.toLowerCase() === 'ol';\n    return firstItemValue && lastItemValue;\n  }\n\n  // Função para limpar o HTML ao colar conteúdo\n  private handlePaste(event: ClipboardEvent) {\n    event.preventDefault(); // Bloqueia a colagem padrão\n    event.stopPropagation(); // Evita que afete outros elementos\n\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const plainText = clipboardData.getData('text/plain'); // Obtém apenas texto puro\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    if (parentElement.classList.contains('line')) {\n      parentElement.remove();\n    }\n\n    range.deleteContents(); // Remove qualquer seleção existente\n\n    // Converte cada linha do texto colado em <p class=\"line\">\n    const fragment = document.createDocumentFragment();\n    plainText.split('\\n').forEach((line) => {\n      if (line.trim()) {\n        const p = document.createElement('p');\n        p.classList.add('line');\n        p.textContent = line.trim();\n        fragment.appendChild(p);\n      }\n    });\n\n    // Insere o conteúdo processado no local do cursor\n    range.insertNode(fragment);\n\n    // Ajusta o cursor para o final do texto inserido\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  // Função para restaurar a formatação do texto selecionado\n  private clearFormatting(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Remove a formatação de cada linha\n    selectedLines.forEach((line) => {\n      line.innerHTML = line.textContent; // Remove todas as tags HTML\n      line.style.textAlign = ''; // Remove o alinhamento\n    });\n\n    this.wrapSelectionLine('p', true);\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`rich-text`]: true,\n          [`rich-text-${this.positionBar}`]: true,\n        }}\n        style={{ height: this.height, maxHeight: this.maxHeight }}\n        tabindex=\"0\"\n        onMouseEnter={() => (this.insideComponent = true)}\n        onMouseLeave={() => (this.insideComponent = false)}\n      >\n        <div class=\"preview\">\n          <div\n            data-test={this.dataTest}\n            ref={(el) => this.refeditorElement(el)}\n            contentEditable=\"true\"\n            class=\"editor-uai-design-system\"\n            tabindex=\"0\"\n            onBlur={this.onBlur}\n            onFocus={this.onFocus}\n            onMouseUp={() => this.updateToolbarState()}\n            onKeyUp={() => this.updateToolbarState()}\n            onKeyDown={(ev) => this.onKeydown(ev)}\n            onInput={(ev) => this.onInput(ev)}\n            onPaste={this.handlePaste.bind(this)}\n          ></div>\n        </div>\n\n        <bds-grid\n          class={{\n            [`format-buttons`]: true,\n            [`format-buttons-active`]:\n              this.weightButton ||\n              this.italicButton ||\n              this.strikeThroughButton ||\n              this.underlineButton ||\n              this.linkButton ||\n              this.codeButton ||\n              this.alignmentButtons ||\n              this.listButtons ||\n              this.quoteButton ||\n              this.headingButtons ||\n              this.unstyledButton,\n          }}\n        >\n          <div class=\"accordion-header\">\n            <bds-grid ref={(el) => this.refButtonsListElement(el)} class=\"buttons-list\" flex-wrap=\"wrap\">\n              <div onFocus={(ev) => this.onFocusEditorBar(ev)} tabindex=\"1\" class=\"editor-bar\"></div>\n              {this.weightButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'bold')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomBoldActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'b')}\n                    icon-left=\"text-style-bold\"\n                    aria-label={`${termTranslate(this.language, 'bold')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.italicButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'italic')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomItalicActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'i')}\n                    icon-left=\"text-style-italic\"\n                    aria-label={`${termTranslate(this.language, 'italic')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.strikeThroughButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'strike')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomStrikeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'strike')}\n                    icon-left=\"text-style-strikethrough\"\n                    aria-label={`${termTranslate(this.language, 'strike')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.underlineButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'underline')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnderlineActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'u')}\n                    icon-left=\"text-style-underline\"\n                    aria-label={`${termTranslate(this.language, 'underline')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.linkButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'link')}`} position=\"top-center\">\n                  {this.buttomLinkActive ? (\n                    <bds-button\n                      variant=\"solid\"\n                      color=\"content\"\n                      size=\"short\"\n                      onBdsClick={(ev) => this.wrapSelection(ev, 'a')}\n                      icon-left=\"link\"\n                      aria-label={`${termTranslate(this.language, 'link')}`}\n                    ></bds-button>\n                  ) : (\n                    <bds-dropdown\n                      ref={(el) => this.refDropDownLinkElement(el)}\n                      activeMode=\"click\"\n                      position=\"bottom-left\"\n                    >\n                      <div slot=\"dropdown-activator\">\n                        <bds-button\n                          slot=\"dropdown-activator\"\n                          variant=\"text\"\n                          color=\"content\"\n                          size=\"short\"\n                          onBdsClick={(ev) => this.addSelectionLink(ev)}\n                          icon-left=\"link\"\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </div>\n                      <bds-grid padding=\"half\" alignItems=\"center\" gap=\"half\" slot=\"dropdown-content\">\n                        <bds-input\n                          ref={this.refInputSetLink}\n                          onBdsInput={(ev) => this.addLinkInput(ev.detail)}\n                          style={{ flexShrink: '99999' }}\n                          placeholder=\"adcione o link aqui\"\n                          onKeyDown={(ev) => this.createLinkKeyDown(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                        ></bds-input>\n                        <bds-button\n                          disabled={this.buttomLinkValidDisabled}\n                          icon-left=\"check\"\n                          onBdsClick={(ev) => this.createLink(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </bds-grid>\n                    </bds-dropdown>\n                  )}\n                </bds-tooltip>\n              )}\n              {this.codeButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'code')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomCodeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'code')}\n                    icon-left=\"code\"\n                    aria-label={`${termTranslate(this.language, 'code')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_left')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignLeftActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'left')}\n                    icon-left=\"align-left\"\n                    aria-label={`${termTranslate(this.language, 'align_left')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_center')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignCenterActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'center')}\n                    icon-left=\"align-center\"\n                    aria-label={`${termTranslate(this.language, 'align_center')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_right')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignRightActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'right')}\n                    icon-left=\"align-right\"\n                    aria-label={`${termTranslate(this.language, 'align_right')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'unordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnorderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ul')}\n                    icon-left=\"unordered-list\"\n                    aria-label={`${termTranslate(this.language, 'unordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'ordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomOrderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ol')}\n                    icon-left=\"ordered-list\"\n                    aria-label={`${termTranslate(this.language, 'ordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.quoteButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'quote')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomQuoteActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'blockquote')}\n                    icon-left=\"quote\"\n                    aria-label={`${termTranslate(this.language, 'quote')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h1')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH1Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h1')}\n                    icon-left=\"h-1\"\n                    aria-label={`${termTranslate(this.language, 'h1')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h2')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH2Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h2')}\n                    icon-left=\"h-2\"\n                    aria-label={`${termTranslate(this.language, 'h2')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h3')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH3Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h3')}\n                    icon-left=\"h-3\"\n                    aria-label={`${termTranslate(this.language, 'h3')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h4')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH4Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h4')}\n                    icon-left=\"h-4\"\n                    aria-label={`${termTranslate(this.language, 'h4')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h5')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH5Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h5')}\n                    icon-left=\"h-5\"\n                    aria-label={`${termTranslate(this.language, 'h5')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h6')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH6Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h6')}\n                    icon-left=\"h-6\"\n                    aria-label={`${termTranslate(this.language, 'h6')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.unstyledButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'clear_formatting')}`} position=\"top-center\">\n                  <bds-button\n                    variant=\"text\"\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.clearFormatting(ev)}\n                    icon-left=\"unstyled\"\n                    aria-label={`${termTranslate(this.language, 'clear_formatting')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n            </bds-grid>\n            <bds-button\n              id=\"buttonAccordion\"\n              variant={this.buttomAccordionActive ? 'solid' : 'text'}\n              class=\"arrow-down\"\n              color=\"content\"\n              size=\"short\"\n              onBdsClick={() => this.setheaderHeight()}\n              icon-left={\n                this.positionBar == 'top'\n                  ? this.buttomAccordionActive\n                    ? 'arrow-up'\n                    : 'arrow-down'\n                  : this.buttomAccordionActive\n                    ? 'arrow-down'\n                    : 'arrow-up'\n              }\n            ></bds-button>\n          </div>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;YAAO,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,IAAI,EAAE,SAAS;YACf,QAAA,MAAM,EAAE,SAAS;YACjB,QAAA,MAAM,EAAE,SAAS;YACjB,QAAA,SAAS,EAAE,YAAY;YACvB,QAAA,IAAI,EAAE,MAAM;YACZ,QAAA,IAAI,EAAE,QAAQ;YACd,QAAA,UAAU,EAAE,oBAAoB;YAChC,QAAA,YAAY,EAAE,mBAAmB;YACjC,QAAA,WAAW,EAAE,mBAAmB;YAChC,QAAA,cAAc,EAAE,oBAAoB;YACpC,QAAA,YAAY,EAAE,gBAAgB;YAC9B,QAAA,KAAK,EAAE,SAAS;YAChB,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,gBAAgB,EAAE,mBAAmB;YACrC,QAAA,MAAM,EAAE,UAAU;YACnB,KAAA;aACF;;YCvBM,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,IAAI,EAAE,SAAS;YACf,QAAA,MAAM,EAAE,SAAS;YACjB,QAAA,MAAM,EAAE,SAAS;YACjB,QAAA,SAAS,EAAE,WAAW;YACtB,QAAA,IAAI,EAAE,MAAM;YACZ,QAAA,IAAI,EAAE,QAAQ;YACd,QAAA,UAAU,EAAE,wBAAwB;YACpC,QAAA,YAAY,EAAE,mBAAmB;YACjC,QAAA,WAAW,EAAE,sBAAsB;YACnC,QAAA,cAAc,EAAE,mBAAmB;YACnC,QAAA,YAAY,EAAE,gBAAgB;YAC9B,QAAA,KAAK,EAAE,MAAM;YACb,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,EAAE,EAAE,UAAU;YACd,QAAA,gBAAgB,EAAE,iBAAiB;YACnC,QAAA,MAAM,EAAE,UAAU;YACnB,KAAA;aACF;;YCvBM,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,IAAI,EAAE,MAAM;YACZ,QAAA,MAAM,EAAE,QAAQ;YAChB,QAAA,MAAM,EAAE,eAAe;YACvB,QAAA,SAAS,EAAE,WAAW;YACtB,QAAA,IAAI,EAAE,MAAM;YACZ,QAAA,IAAI,EAAE,MAAM;YACZ,QAAA,UAAU,EAAE,YAAY;YACxB,QAAA,YAAY,EAAE,cAAc;YAC5B,QAAA,WAAW,EAAE,aAAa;YAC1B,QAAA,cAAc,EAAE,gBAAgB;YAChC,QAAA,YAAY,EAAE,cAAc;YAC5B,QAAA,KAAK,EAAE,OAAO;YACd,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,EAAE,EAAE,WAAW;YACf,QAAA,gBAAgB,EAAE,kBAAkB;YACpC,QAAA,MAAM,EAAE,QAAQ;YACjB,KAAA;aACF;;YCjBM,MAAM,aAAa,GAAG,CAAC,IAAe,EAAE,MAAc,KAAY;YACvE,IAAA,IAAI,SAAS;gBACb,QAAQ,IAAI;YACV,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA;YACE,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;;YAEnD,IAAA,OAAO,SAAS;YAClB,CAAC;;YCtBD,MAAM,WAAW,GAAG,6nLAA6nL;;kBCYpoL,QAAQ,4BAAA,MAAA;YALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;YAMU,QAAA,IAAkB,CAAA,kBAAA,GAAiB,IAAI;YACvC,QAAA,IAAmB,CAAA,mBAAA,GAA6C,IAAI;YACpE,QAAA,IAAM,CAAA,MAAA,GAAiB,IAAI;YAC3B,QAAA,IAAY,CAAA,YAAA,GAA4B,IAAI;YAI3C,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;YAClC,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;YACpC,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;YACpC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;YACvC,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;YAClC,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;YAClC,QAAA,IAAuB,CAAA,uBAAA,GAAa,IAAI;YACxC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;YACvC,QAAA,IAAuB,CAAA,uBAAA,GAAa,KAAK;YACzC,QAAA,IAAsB,CAAA,sBAAA,GAAa,KAAK;YACxC,QAAA,IAAyB,CAAA,yBAAA,GAAa,KAAK;YAC3C,QAAA,IAAuB,CAAA,uBAAA,GAAa,KAAK;YACzC,QAAA,IAAiB,CAAA,iBAAA,GAAa,KAAK;YACnC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;YAChC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;YACvC,QAAA,IAAY,CAAA,YAAA,GAAY,MAAM;YAC9B,QAAA,IAAiB,CAAA,iBAAA,GAAa,KAAK;YACnC,QAAA,IAAiB,CAAA,iBAAA,GAAgC,IAAI;YACrD,QAAA,IAAkB,CAAA,kBAAA,GAAmB,IAAI;YACzC,QAAA,IAAiB,CAAA,iBAAA,GAAY,IAAI;YACjC,QAAA,IAAY,CAAA,YAAA,GAAY,OAAO;YAC/B,QAAA,IAAiB,CAAA,iBAAA,GAAW,IAAI;YAChC,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;YAC/B,QAAA,IAAe,CAAA,eAAA,GAAa,KAAK;YAC1C;;YAEG;YACK,QAAA,IAAQ,CAAA,QAAA,GAAe,OAAO;YACtC;;YAEG;YACK,QAAA,IAAY,CAAA,YAAA,GAAa,IAAI;YACrC;;YAEG;YACK,QAAA,IAAY,CAAA,YAAA,GAAa,IAAI;YACrC;;YAEG;YACK,QAAA,IAAmB,CAAA,mBAAA,GAAa,IAAI;YAC5C;;YAEG;YACK,QAAA,IAAe,CAAA,eAAA,GAAa,IAAI;YACxC;;YAEG;YACK,QAAA,IAAU,CAAA,UAAA,GAAa,IAAI;YACnC;;YAEG;YACK,QAAA,IAAU,CAAA,UAAA,GAAa,IAAI;YACnC;;YAEG;YACK,QAAA,IAAgB,CAAA,gBAAA,GAAa,IAAI;YACzC;;YAEG;YACK,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;YACpC;;YAEG;YACK,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;YACpC;;YAEG;YACK,QAAA,IAAc,CAAA,cAAA,GAAa,IAAI;YACvC;;YAEG;YACK,QAAA,IAAc,CAAA,cAAA,GAAa,IAAI;YACvC;;YAEG;YACK,QAAA,IAAM,CAAA,MAAA,GAAY,IAAI;YAC9B;;YAEG;YACK,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;YACjC;;YAEG;YACK,QAAA,IAAW,CAAA,WAAA,GAAiB,KAAK;YAEzC;;YAEG;YACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;YAoIxB,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,EAAe,KAAU;YACxD,YAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;YAC9B,SAAC;YACO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;YACnD,YAAA,IAAI,CAAC,MAAM,GAAG,EAAE;YAClB,SAAC;YACO,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,EAA0B,KAAU;YACpE,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE;YACxB,SAAC;YAEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,EAAuB,KAAU;YAC1D,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE;YACxB,SAAC;YAEO,QAAA,IAAY,CAAA,YAAA,GAAG,MAAK;YAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;YAC7B,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;YAC/B,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;YAC/B,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;YAClC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;YAC7B,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;YAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;YAClC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;YACpC,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;YACnC,YAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK;YACtC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;YACpC,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;YAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC7B,SAAC;YAEO,QAAA,IAAe,CAAA,eAAA,GAAG,MAAK;YAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,qBAAqB;YACxD,YAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,YAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;4BAAE;wBAC9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;;wBAErC,SAAS,CAAC,eAAe,EAAE;YAC3B,YAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC3B,SAAC;YAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAK;wBACpB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;YAClC,YAAA,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;4BAClC,IAAI,CAAC,YAAY,EAAE;;YAErB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,SAAC;YAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAK;wBACrB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACtB,SAAC;YAmCO,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,KAAoB,KAAI;YAC3C,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;YAC7B,gBAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,gBAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;gCAAE;4BAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc;;YAGtC,gBAAA,IAAI,YAAY,GAAG,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,GAAI,SAAyB;YAE/G,gBAAA,OAAO,YAAY,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;YAC/F,oBAAA,YAAY,GAAG,YAAY,CAAC,aAAa;;;YAI3C,gBAAA,IACE,YAAY;gCACZ,YAAY,CAAC,OAAO,KAAK,YAAY;YACrC,oBAAA,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvC,oBAAA,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAClC;YACA,oBAAA,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,oBAAA,YAAY,CAAC,MAAM,EAAE,CAAC;;;YAG1B,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;4BACrE,KAAK,CAAC,cAAc,EAAE;YACtB,gBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,0BAA0B;4BAClD,IAAI,CAAC,cAAc,EAAE;;YAEvB,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YACzD,gBAAA,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,gBAAA,KAAK,CAAC,eAAe,EAAE,CAAC;;YAE5B,SAAC;YA8yBF;gBA1hCC,gBAAgB,GAAA;oBACd,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACvC,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,0BAA0B;;oBAEpD,IACE,IAAI,CAAC,YAAY;YACjB,YAAA,IAAI,CAAC,YAAY;YACjB,YAAA,IAAI,CAAC,mBAAmB;YACxB,YAAA,IAAI,CAAC,eAAe;YACpB,YAAA,IAAI,CAAC,UAAU;YACf,YAAA,IAAI,CAAC,UAAU;YACf,YAAA,IAAI,CAAC,gBAAgB;YACrB,YAAA,IAAI,CAAC,WAAW;YAChB,YAAA,IAAI,CAAC,WAAW;YAChB,YAAA,IAAI,CAAC,cAAc;wBACnB,IAAI,CAAC,cAAc,EACnB;wBACA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CACrE,aAAa,CAC6B;YAC5C,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;wBAC3B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,iBAAA,CAAmB;;yBACvD;wBACL,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,IAAA,CAAM;;;gBAezC,oBAAoB,GAAA;YAC5B,QAAA,UAAU,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;;gBAI/D,4BAA4B,GAAA;YACpC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC;;gBAG1C,kBAAkB,GAAA;YACxB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;oBACvC,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;YACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;oBAC7G,IAAI,CAAC,kBAAkB,GAAG,eAAe,CAAC,aAAa,EAAE,2BAA2B,CAAC;;;YAI/E,IAAA,eAAe,CAAC,KAAc,EAAA;oBACpC,MAAM,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,EAAE;YACxE,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW;oBAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAyB;YACzF,QAAA,IAAI,gBAAgB,GAAG,2BAA2B,EAAE;YAClD,YAAA,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;yBAClC;YACL,YAAA,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;YAE5C,QAAA,MAAM,UAAU,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,2BAA2B;oBACrG,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,2BAA2B,GAAG,gBAAgB,CAAC;oBACjF,MAAM,sBAAsB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACnE,QAAA,sBAAsB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YAC1E,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;YACjC,SAAC,CAAC;oBACF,IAAI,KAAK,EAAE;YACT,YAAA,sBAAsB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YACzC,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC/B,gBAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,YAAA,EAAe,eAAe,GAAG,EAAE,GAAG,EAAE,KAAK;YACxF,aAAC,CAAC;;yBACG;YACL,YAAA,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YACvE,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;4BAClC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,iBAAA,CAAmB;YAC9D,aAAC,CAAC;;;YAKI,IAAA,yBAAyB,CAAC,KAAK,EAAA;YACvC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,WAAW,EAAE,CAAC;YACtE,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;YACpD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,aAAF,EAAE,KAAA,MAAA,GAAA,MAAA,GAAF,EAAE,CAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClE,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC;YAC1C,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;YAC5C,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,QAAQ,CAAC;YACjD,QAAA,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC;YAC/C,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC;YAC1C,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC;YAC7C,QAAA,IAAI,CAAC,qBAAqB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,MAAM;YAChE,QAAA,IAAI,CAAC,uBAAuB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,QAAQ;YACpE,QAAA,IAAI,CAAC,sBAAsB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,OAAO;oBAClE,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;oBACpD,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;YAClD,QAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,YAAY,CAAC;YACpD,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;YACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;YACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;YACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;YACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;YACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;;;YA8DnC,IAAA,OAAO,CAAC,EAAc,EAAA;oBAC5B,EAAE,CAAC,cAAc,EAAE;YACnB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YAE9B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAE7D,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc;;YAGxC,QAAA,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAK,WAA2B,CAAC,OAAO,KAAK,KAAK,EAAE;wBAChG,MAAM,UAAU,GAAG,WAA0B;wBAE7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;YAC5C,YAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;YAC9B,YAAA,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS;wBAEzC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;;;YAI1D,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;wBAClD,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;YACrC,YAAA,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;YACvB,YAAA,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS;YAC3B,YAAA,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YACpB,SAAC,CAAC;;;YAyCI,IAAA,gBAAgB,CAAC,EAAc,EAAA;YACrC,QAAA,MAAM,SAAS,GAAG,EAAE,CAAC,MAAqB;oBAC1C,MAAM,UAAU,GAAG,SAAS,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,CAAC;oBAC3E,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAgB;oBACnF,cAAc,CAAC,KAAK,EAAE;YACtB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;;;gBAI3B,cAAc,GAAA;YACpB,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE;YACpC,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE;YACjC,QAAA,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;YACrC,QAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACrB,GAAG,CAAC,eAAe,EAAE;YACrB,QAAA,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;;gBAGb,OAAO,CAAC,GAAW,EAAE,OAAsB,EAAA;YACjD,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,WAAW,EAAE,CAAC;YACtE,QAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;;YAGpB,IAAA,aAAa,CAAC,EAAe,EAAE,GAAW,EAAE,IAAa,EAAA;YAC/D,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7D,MAAM,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,eAAe,EAAE;;YAG1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;wBAAE;oBAEjD,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;YACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;oBAE7G,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,EAAE,OAAO,CAAC;oBACvD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;;YAG/C,QAAA,IAAI,OAAyB;oBAC7B,IAAI,eAAe,GAAG,KAAK;;oBAG3B,IAAI,YAAY,EAAE;wBAChB,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC;wBACzE,IAAI,UAAU,EAAE;YACd,gBAAA,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa;YACvC,gBAAA,MAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE;4BACjF,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,WAAW,CAAC,MAAM;YAEtE,gBAAA,IAAI,eAAe,IAAI,MAAM,EAAE;;YAE7B,oBAAA,OAAO,UAAU,CAAC,UAAU,EAAE;oCAC5B,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC;;YAExD,oBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gCAC9B,SAAS,CAAC,eAAe,EAAE;YAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;gCACzB,IAAI,CAAC,kBAAkB,EAAE;;iCACpB,IAAI,YAAY,EAAE;;YAEvB,oBAAA,OAAO,GAAG,QAAQ,CAAC,sBAAsB,EAAE;gCAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,oBAAA,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;gCAChC,eAAe,GAAG,IAAI;YACtB,oBAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;YACvC,oBAAA,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACnC,oBAAA,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC;YAChC,oBAAA,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;gCAC5B,SAAS,CAAC,eAAe,EAAE;YAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;gCAC5B,IAAI,CAAC,kBAAkB,EAAE;;iCACpB;;gCAEL,SAAS,CAAC,eAAe,EAAE;YAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;gCACzB,IAAI,CAAC,kBAAkB,EAAE;;;wBAG7B;;YAGF,QAAA,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,YAAA,OAAO,GAAG,QAAQ,CAAC,sBAAsB,EAAE;wBAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACtD,YAAA,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;wBAChC,eAAe,GAAG,IAAI;;yBACjB;YACL,YAAA,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE;;;oBAInC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YAChD,YAAA,OAAO,OAAO,CAAC,UAAU,EAAE;4BACzB,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;;wBAE9D,OAAO,CAAC,MAAM,EAAE;YAClB,SAAC,CAAC;;oBAGF,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;YAC3C,QAAA,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,EAAE;YACvB,YAAA,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;YAEpC,QAAA,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;YAC5B,QAAA,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;;YAGzB,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;YAC/C,YAAA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;4BACtD,EAAE,CAAC,MAAM,EAAE;;YAEf,SAAC,CAAC;;YAGF,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;oBACvC,IAAI,eAAe,EAAE;YACnB,YAAA,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7B,YAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;;yBACtB;wBACL,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC;wBACtD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;;oBAEpD,SAAS,CAAC,eAAe,EAAE;YAE3B,QAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAE5B,IAAI,CAAC,kBAAkB,EAAE;;YAGzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;YAGvD,IAAA,iBAAiB,CAAC,GAAW,EAAE,iBAAA,GAA6B,KAAK,EAAA;;YACvE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;wBAAE;oBAEjD,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAA6B;YACrD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAA2B;;YAGjD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe;YAE5C,QAAA,IAAI,WAAW,GAAG,SAAS,CAAC,aAAa;oBACzC,OAAO,WAAW,IAAI,WAAW,KAAK,OAAO,CAAC,aAAa,EAAE;YAC3D,YAAA,IAAI,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,aAAa,GAAI,WAA2B;wBAChH,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjD,gBAAA,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;;YAE5B,YAAA,WAAW,GAAI,WAAW,CAAC,WAA2B,KAAK,CAAA,EAAA,GAAA,WAAW,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAA2B,CAAA;;;YAInH,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,GAAI,OAAuB;YACvG,QAAA,OAAO,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE;YACzF,YAAA,UAAU,GAAG,UAAU,CAAC,aAAa;;oBAEvC,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACvD,YAAA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC;;;YAI/B,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAClD,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,GAAG,CAC1D;YAED,QAAA,MAAM,cAAc,GAAkB,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAI;YAClE,YAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;YACpE,YAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;YAChC,YAAA,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS;YACnC,YAAA,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC;YAC1B,YAAA,OAAO,UAAU;YACnB,SAAC,CAAC;oBAEF,IAAI,iBAAiB,EAAE;YACrB,YAAA,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;;;YAIzE,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;oBACvC,IAAI,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;;oBAG1C,IAAI,CAAC,QAAQ,EAAE;YACb,YAAA,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;wBACtC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC;;;oBAIzC,OAAO,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;YACvD,YAAA,QAAQ,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ;;;YAI3C,QAAA,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,KAAI,CAAC,CAAC;YAC9D,QAAA,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,KAAI,CAAC,CAAC;oBAC5D,SAAS,CAAC,eAAe,EAAE;YAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAE5B,IAAI,CAAC,kBAAkB,EAAE;YAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;;gBAIvD,SAAS,CAAC,EAAe,EAAE,SAAkD,EAAA;YACnF,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7D,MAAM,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,eAAe,EAAE;;YAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,IAAI,YAAY,GAAG,KAAK,CAAC,cAA6B;;oBAGtD,OAAO,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;YACnD,YAAA,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;4BAC1F;;YAEF,YAAA,YAAY,GAAG,YAAY,CAAC,aAAa;;;oBAI3C,IAAI,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;;YAEhD,YAAA,MAAM,gBAAgB,GAAI,YAA4B,CAAC,KAAK,CAAC,SAAS;YACtE,YAAA,IAAI,gBAAgB,KAAK,SAAS,EAAE;;YAEjC,gBAAA,YAA4B,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;6BAC7C;;YAEJ,gBAAA,YAA4B,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS;;;;oBAK7D,SAAS,CAAC,eAAe,EAAE;YAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAEzB,IAAI,CAAC,kBAAkB,EAAE;YAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;;gBAIvD,aAAa,CAAC,EAAe,EAAE,IAA4D,EAAA;;YACjG,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7D,MAAM,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,eAAe,EAAE;;YAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;wBAAE;YACjD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;YAClC,QAAA,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO;YACxD,QAAA,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,sBAAsB;YACtE,QAAA,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,kBAAkB;YACjE,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa;oBAC1C,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;wBACzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;4BACtC,IAAI,WAAW,EAAE;gCACf,WAAW,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC;;iCACtD,IAAI,UAAU,EAAE;gCACrB,UAAU,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC;;iCACxD;gCACL,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;;YAEjE,aAAC,CAAC;YACF,YAAA,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;4BAC7D,MAAM,CAAC,MAAM,EAAE;;;;;gBAMb,UAAU,CAAC,EAAe,EAAE,IAAiB,EAAA;;YACnD,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7D,MAAM,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,eAAe,EAAE;;YAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;wBAAE;YACjD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;oBAClC,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO;YACvD,QAAA,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO;oBACvF,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;YAC5C,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa;oBAE1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE;YACjD,YAAA,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC;wBAC3C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACtC,gBAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YACnC,aAAC,CAAC;;yBACG;wBACL,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC;wBAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;wBAEjF,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;4BACtD,IAAI,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;YACzC,oBAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;gCACpC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;;iCAC1C;gCACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;oCACtC,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG;oCAChF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;YAClD,wBAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;oCAChC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;oCAC7C,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;wCACvD,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC;;yCAC7D;wCACL,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC;;YAE7E,wBAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,qBAAC,CAAC;gCACF,MAAM,CAAC,MAAM,EAAE;;;6BAEZ;;4BAEL,aAAa,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,WAAW,EAAE,OAAO,CAAC;4BAChF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACtC,oBAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YACnC,iBAAC,CAAC;;;;YAKA,IAAA,gBAAgB,CAAC,EAAe,EAAA;YACtC,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;;YAE7B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACnB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;oBACvC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YAChD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,yBAAyB,CAAqB;oBAChH,cAAc,CAAC,KAAK,EAAE;;YAGhB,IAAA,YAAY,CAAC,EAAc,EAAA;oBACjC,EAAE,CAAC,cAAc,EAAE;YACnB,QAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,KAAK;oBAClC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;;yBAC/B;YACL,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI;;;YAI/B,IAAA,iBAAiB,CAAC,EAAiB,EAAA;YACzC,QAAA,IAAI,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE;YACrB,YAAA,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;;;YAIf,IAAA,UAAU,CAAC,EAAE,EAAA;oBACnB,EAAE,CAAC,cAAc,EAAE;YACnB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;oBACvC,SAAS,CAAC,eAAe,EAAE;YAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;oBAC1C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC;YACjD,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;gBAIxB,UAAU,CAAC,SAAsB,EAAE,QAAqB,EAAA;oBAC9D,MAAM,cAAc,GAClB,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI;oBAClH,MAAM,aAAa,GACjB,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI;oBAChH,OAAO,cAAc,IAAI,aAAa;;;YAIhC,IAAA,WAAW,CAAC,KAAqB,EAAA;YACvC,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,QAAA,KAAK,CAAC,eAAe,EAAE,CAAC;oBAExB,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,IAAK,MAAc,CAAC,aAAa;oBAC1E,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAEtD,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;YACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;oBAC7G,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;wBAC5C,aAAa,CAAC,MAAM,EAAE;;YAGxB,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;;YAGvB,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE;oBAClD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACrC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;4BACf,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;YACrC,gBAAA,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;YACvB,gBAAA,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE;YAC3B,gBAAA,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;YAE3B,SAAC,CAAC;;YAGF,QAAA,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;;oBAG1B,SAAS,CAAC,eAAe,EAAE;YAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;;;YAInB,IAAA,eAAe,CAAC,EAAe,EAAA;;YACrC,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;oBACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;wBAC7D,MAAM,CAAC,cAAc,EAAE;wBACvB,MAAM,CAAC,eAAe,EAAE;;YAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;YACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;wBAAE;oBAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACrC,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAA6B;YACrD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAA2B;;YAGjD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe;YAE5C,QAAA,IAAI,WAAW,GAAG,SAAS,CAAC,aAAa;oBACzC,OAAO,WAAW,IAAI,WAAW,KAAK,OAAO,CAAC,aAAa,EAAE;YAC3D,YAAA,IAAI,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,aAAa,GAAI,WAA2B;wBAChH,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjD,gBAAA,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;;YAE5B,YAAA,WAAW,GAAI,WAAW,CAAC,WAA2B,KAAK,CAAA,EAAA,GAAA,WAAW,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAA2B,CAAA;;;YAInH,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,GAAI,OAAuB;YACvG,QAAA,OAAO,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE;YACzF,YAAA,UAAU,GAAG,UAAU,CAAC,aAAa;;oBAEvC,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACvD,YAAA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC;;;YAI/B,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;wBAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;wBAClC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YAC5B,SAAC,CAAC;YAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC;;oBAGjC,SAAS,CAAC,eAAe,EAAE;YAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;;gBAG3B,MAAM,GAAA;;oBACJ,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;4BACL,CAAC,CAAA,SAAA,CAAW,GAAG,IAAI;YACnB,gBAAA,CAAC,aAAa,IAAI,CAAC,WAAW,CAAE,CAAA,GAAG,IAAI;YACxC,aAAA,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EACzD,QAAQ,EAAC,GAAG,EACZ,YAAY,EAAE,OAAO,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,EACjD,YAAY,EAAE,OAAO,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,EAAA,EAElD,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,SAAS,EAAA,EAClB,CACa,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EACtC,eAAe,EAAC,MAAM,EACtB,KAAK,EAAC,0BAA0B,EAChC,QAAQ,EAAC,GAAG,EACZ,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,SAAS,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAC1C,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EACxC,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EACrC,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EACjC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAC/B,CACH,EAEN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;4BACL,CAAC,CAAA,cAAA,CAAgB,GAAG,IAAI;YACxB,gBAAA,CAAC,CAAuB,qBAAA,CAAA,GACtB,IAAI,CAAC,YAAY;YACjB,oBAAA,IAAI,CAAC,YAAY;YACjB,oBAAA,IAAI,CAAC,mBAAmB;YACxB,oBAAA,IAAI,CAAC,eAAe;YACpB,oBAAA,IAAI,CAAC,UAAU;YACf,oBAAA,IAAI,CAAC,UAAU;YACf,oBAAA,IAAI,CAAC,gBAAgB;YACrB,oBAAA,IAAI,CAAC,WAAW;YAChB,oBAAA,IAAI,CAAC,WAAW;YAChB,oBAAA,IAAI,CAAC,cAAc;YACnB,oBAAA,IAAI,CAAC,cAAc;YACtB,aAAA,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC3B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAC,cAAc,eAAW,MAAM,EAAA,EAC1F,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,EAAC,KAAK,EAAC,YAAY,EAAO,CAAA,EACtF,IAAI,CAAC,YAAY,KAChB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACzF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,GAAG,OAAO,GAAG,MAAM,EACjD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,iBAAiB,EAAA,YAAA,EACf,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EACzC,CAAA,CACF,CACf,EACA,IAAI,CAAC,YAAY,KAChB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC3F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,kBAAkB,GAAG,OAAO,GAAG,MAAM,EACnD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,mBAAmB,EAAA,YAAA,EACjB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAC3C,CAAA,CACF,CACf,EACA,IAAI,CAAC,mBAAmB,KACvB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC3F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,kBAAkB,GAAG,OAAO,GAAG,MAAM,EACnD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAA,WAAA,EAC1C,0BAA0B,EAAA,YAAA,EACxB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAC3C,CAAA,CACF,CACf,EACA,IAAI,CAAC,eAAe,KACnB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC9F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,sBAAsB,EAAA,YAAA,EACpB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,EAC9C,CAAA,CACF,CACf,EACA,IAAI,CAAC,UAAU,KACd,oFAA2B,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,IACxF,IAAI,CAAC,gBAAgB,IACpB,CAAA,CAAA,YAAA,EAAA,EACE,OAAO,EAAC,OAAO,EACf,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,MAAM,EACJ,YAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAE,CAAA,EACzC,CAAA,KAEd,CAAA,CAAA,cAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAC5C,UAAU,EAAC,OAAO,EAClB,QAAQ,EAAC,aAAa,EAAA,EAEtB,CAAK,CAAA,KAAA,EAAA,EAAA,IAAI,EAAC,oBAAoB,EAAA,EAC5B,CAAA,CAAA,YAAA,EAAA,EACE,IAAI,EAAC,oBAAoB,EACzB,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAA,WAAA,EACnC,MAAM,EACJ,YAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,GACzC,CACV,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,MAAM,EAAC,UAAU,EAAC,QAAQ,EAAC,GAAG,EAAC,MAAM,EAAC,IAAI,EAAC,kBAAkB,EAAA,EAC7E,CAAA,CAAA,WAAA,EAAA,EACE,GAAG,EAAE,IAAI,CAAC,eAAe,EACzB,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,EAChD,KAAK,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,EAC9B,WAAW,EAAC,qBAAqB,EACjC,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAC7C,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,IAAG,GAAG,GAAG,IAAI,EACnC,CAAA,EACb,CACE,CAAA,YAAA,EAAA,EAAA,QAAQ,EAAE,IAAI,CAAC,uBAAuB,eAC5B,OAAO,EACjB,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EACvC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,IAAG,GAAG,GAAG,IAAI,EAAA,YAAA,EAClC,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAA,CACzC,CACL,CACE,CAChB,CACW,CACf,EACA,IAAI,CAAC,UAAU,KACd,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACzF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,GAAG,OAAO,GAAG,MAAM,EACjD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,CAAC,EAAA,WAAA,EACxC,MAAM,EAAA,YAAA,EACJ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAE,CAAA,EACzC,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC/F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,EAAA,WAAA,EACpC,YAAY,EAAA,YAAA,EACV,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAE,CAAA,EAC/C,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACjG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,OAAO,GAAG,MAAM,EACxD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAA,WAAA,EACtC,cAAc,EAAA,YAAA,EACZ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EACjD,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAChG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,sBAAsB,GAAG,OAAO,GAAG,MAAM,EACvD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAA,WAAA,EACrC,aAAa,EAAA,YAAA,EACX,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA,CAAE,EAChD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACnG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,yBAAyB,GAAG,OAAO,GAAG,MAAM,EAC1D,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACnC,gBAAgB,EAAA,YAAA,EACd,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAE,CAAA,EACnD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACjG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,OAAO,GAAG,MAAM,EACxD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACnC,cAAc,EAAA,YAAA,EACZ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EACjD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC1F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,iBAAiB,GAAG,OAAO,GAAG,MAAM,EAClD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,YAAY,CAAC,EAAA,WAAA,EAC9C,OAAO,EAAA,YAAA,EACL,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA,CAAE,EAC1C,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACrG,CAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,eAClC,UAAU,EAAA,YAAA,EACR,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAE,CAAA,EACrD,CAAA,CACF,CACf,CACQ,EACX,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAE,EAAC,iBAAiB,EACpB,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,YAAY,EAClB,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,EAAA,WAAA,EAEtC,IAAI,CAAC,WAAW,IAAI;8BAChB,IAAI,CAAC;YACL,sBAAE;YACF,sBAAE;8BACF,IAAI,CAAC;YACL,sBAAE;kCACA,UAAU,EAAA,CAEN,CACV,CACG,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;"}