System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,s,n,r;return{setters:[function(t){e=t.r;i=t.c;s=t.h;n=t.H;r=t.a}],execute:function(){var a=".bds-tabs{width:100%;display:-ms-flexbox;display:flex;z-index:1100;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-negative:0;flex-shrink:0;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;height:48px;padding:0 10px 0 10px}.bds-tabs--center{-ms-flex-pack:center;justify-content:center}.bds-tabs--left{-ms-flex-pack:start;justify-content:flex-start}.bds-tabs--right{-ms-flex-pack:end;justify-content:flex-end}.bds-tabs .bds-tabs__header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;overflow:hidden;-ms-flex-align:stretch;align-items:stretch;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.bds-tabs .bds-tabs__header-button-container{padding:0px;min-width:40px}";var o=t("bds_tabs",function(){function t(t){var s=this;e(this,t);this.scrollButtonClick=i(this,"scrollButtonClick");this.bdsTabInit=i(this,"bdsTabInit");this.SCROLL_BEHAVIOR="smooth";this.align="center";this.handleHeaderResize=function(){if(s.tabsHeaderChildElement.offsetWidth<s.tabsHeaderChildElement.scrollWidth){s.updateButtonsVisibility(true)}else{s.updateButtonsVisibility(false)}};this.updateButtonsVisibility=function(t){s.setLeftButtonVisibility(t);s.setRightButtonVisibility(t)};this.handleScrollButtonClick=function(t){s.scrollButtonClick.emit({direction:t})}}t.prototype.onScrollButtonClick=function(t){var e;t.preventDefault();var i={behavior:this.SCROLL_BEHAVIOR,top:0,left:t.detail.distance};(e=i.left)!==null&&e!==void 0?e:i.left=this.getDistance(i,t);this.tabsHeaderChildElement.scrollTo(i)};t.prototype.onSelectedTab=function(t){this.handleButtonOverlay(t.detail)};t.prototype.componentDidLoad=function(){this.getChildElements();this.attachEvents();this.setLeftButtonVisibility(false);this.setRightButtonVisibility(true);this.handleActiveTab()};t.prototype.handleActiveTab=function(){var t=Array.from(this.tabsHeaderChildElement.getElementsByTagName("bds-tab"));var e=t.find((function(t){return t.active}));if(e){this.bdsTabInit.emit(e.group)}else{var i=t[0];this.bdsTabInit.emit(i.group)}};t.prototype.getChildElements=function(){this.tabsHeaderChildElement=this.el.querySelector(".bds-tabs__header");this.leftButtonChildElement=this.el.querySelector("#bds-tabs-button-left");this.rightButtonChildElement=this.el.querySelector("#bds-tabs-button-right")};t.prototype.attachEvents=function(){var t=this;window.onresize=this.handleHeaderResize;this.tabsHeaderChildElement.onscroll=function(){return t.updateButtonsVisibility(t.tabsHeaderChildElement.scrollWidth>t.tabsHeaderChildElement.clientWidth)}};t.prototype.setRightButtonVisibility=function(t){if(t&&this.tabsHeaderChildElement.scrollWidth>Math.ceil(this.tabsHeaderChildElement.scrollLeft+this.tabsHeaderChildElement.clientWidth)){this.rightButtonChildElement.style.display="block"}else{this.rightButtonChildElement.style.display="none"}};t.prototype.setLeftButtonVisibility=function(t){this.leftButtonChildElement.style.display=this.tabsHeaderChildElement.scrollLeft>0&&t?"block":"none"};t.prototype.handleButtonOverlay=function(t){var e=this;var i=Array.from(this.tabsHeaderChildElement.getElementsByTagName("bds-tab")).find((function(e){return e.group==t}));var s=[this.leftButtonChildElement,this.rightButtonChildElement];s.forEach((function(t){if(e.isButtonOverlappingTab(t,i)){var s=e.getAdjutScrollDistance(t,i);e.scrollButtonClick.emit({distance:s})}}))};t.prototype.isButtonOverlappingTab=function(t,e){var i=e.getBoundingClientRect();var s=t.getBoundingClientRect();return this.elementIsOverlapping(s,i)};t.prototype.elementIsOverlapping=function(t,e){var i=t.x;var s=t.x+t.width;var n=e.x;var r=e.x+e.width;return i>=n&&i<=r||s>=n&&s<=r};t.prototype.getAdjutScrollDistance=function(t,e){var i=t.id=="bds-tabs-button-left"?"left":"right";var s=e.clientWidth+parseInt(getComputedStyle(e).marginRight)-t.offsetWidth;if(i=="right"){return e.parentElement.scrollLeft+s}else{return e.parentElement.scrollLeft-s}};t.prototype.getDistance=function(t,e){return e.detail.direction=="right"?t.left=this.tabsHeaderChildElement.scrollLeft+this.tabsHeaderChildElement.clientWidth:t.left=this.tabsHeaderChildElement.scrollLeft-this.tabsHeaderChildElement.clientWidth};t.prototype.render=function(){var t;var e=this;return s(n,{key:"e344015c075a0f42260d9dec090e7ce9b37ce308",class:(t={"bds-tabs":true},t["bds-tabs--".concat(this.align)]=true,t)},s("div",{key:"10e0dc4985640367dd72e8c12d33fa062c59b25f",class:"bds-tabs__header-button-container"},s("bds-button-icon",{key:"7563383118dc3374923b284e979e90ac4c426e7f",class:"bds-tabs__header-button",icon:"arrow-left",size:"short",id:"bds-tabs-button-left",onClick:function(){return e.handleScrollButtonClick("left")},variant:"secondary"})),s("div",{key:"5a929ca2b47aebb6b3390d63d24fb1382a8a2e50",class:"bds-tabs__header"},s("slot",{key:"4096ffc7f4a6ea5d9927d1674ee21915d64bad8f"})),s("div",{key:"ea79f43c91c548369f4d313e64243100560a1c2a",class:"bds-tabs__header-button-container"},s("bds-button-icon",{key:"1cfe8731e4b3844ba6ff060d255fa72c262a3e16",class:"bds-tabs__header-button",icon:"arrow-right",size:"short",id:"bds-tabs-button-right",onClick:function(){return e.handleScrollButtonClick("right")},variant:"secondary"})))};Object.defineProperty(t.prototype,"el",{get:function(){return r(this)},enumerable:false,configurable:true});return t}());o.style=a}}}));
//# sourceMappingURL=p-34f88f51.system.entry.js.map