{"version": 3, "file": "bds-card-title.entry.esm.js", "sources": ["src/components/card/card-title/card-title.scss?tag=bds-card-title&encapsulation=shadow", "src/components/card/card-title/card-title.tsx"], "sourcesContent": ["", "import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-title',\n  styleUrl: 'card-title.scss',\n  shadow: true,\n})\nexport class CardTitle {\n  /**\n   *Set the card title.\n   */\n  @Prop() text?: string;\n\n  render() {\n    return (\n      <bds-typo variant=\"fs-20\" tag=\"h4\" margin={false} bold=\"bold\">\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,YAAY,GAAG,EAAE;;MCOV,SAAS,GAAA,MAAA;;;;IAMpB,MAAM,GAAA;AACJ,QAAA,QACE,iEAAU,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,IAAI,EAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAC,MAAM,EAAA,EAC1D,IAAI,CAAC,IAAI,CACD;;;;;;;"}