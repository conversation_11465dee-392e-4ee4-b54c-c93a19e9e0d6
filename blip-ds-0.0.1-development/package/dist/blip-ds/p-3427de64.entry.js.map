{"version": 3, "names": ["tabPanelCss", "TabPanel", "constructor", "hostRef", "this", "isActive", "handleTabChange", "event", "detail", "group", "render", "h", "Host", "key", "class"], "sources": ["src/components/tabs/tab (depreciated)/tab-panel/tab-panel.scss?tag=bds-tab-panel", "src/components/tabs/tab (depreciated)/tab-panel/tab-panel.tsx"], "sourcesContent": ["@use '../../../../globals/helpers' as *;\n\n.bds-tab-panel {\n  display: none;\n  font-family: $font-family;\n  font-size: $fs-16;\n  font-style: normal;\n  font-weight: normal;\n\n  &--selected {\n    display: block;\n  }\n}\n", "import { Component, ComponentInterface, h, Host, Listen, Prop, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab-panel',\n  styleUrl: 'tab-panel.scss',\n})\nexport class TabPanel implements ComponentInterface {\n  /**\n   * Specifies the TabPanel group. Used to link it to the Tab.\n   */\n  @Prop() group!: string;\n\n  /**\n   * State to control if a tab panel is current active\n   */\n  @State() isActive = false;\n\n  @Listen('bdsTabChange', { target: 'body' })\n  @Listen('bdsTabInit', { target: 'body' })\n  handleTabChange(event: CustomEvent) {\n    this.isActive = event.detail == this.group;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tab-panel': true,\n          ['bds-tab-panel--selected']: this.isActive,\n        }}\n      >\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAc,2M,MCMPC,EAAQ,MAJrB,WAAAC,CAAAC,G,UAaWC,KAAQC,SAAG,KAoBrB,CAhBC,eAAAC,CAAgBC,GACdH,KAAKC,SAAWE,EAAMC,QAAUJ,KAAKK,K,CAGvC,MAAAC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACL,gBAAiB,KACjB,CAAC,2BAA4BV,KAAKC,WAGpCM,EAAQ,QAAAE,IAAA,6C", "ignoreList": []}