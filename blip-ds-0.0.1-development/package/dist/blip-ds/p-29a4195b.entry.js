import{r as i,c as t,h as s,a as e}from"./p-C3J6Z5OX.js";import{a as o,b as a,c as l}from"./p-BNEKIkjk.js";const h=':host{display:block;-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;position:relative}.carousel{display:block;-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;max-width:1920px;position:relative}.carousel_slide{width:100%;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;padding:0 48px}.carousel_slide::after{content:"";position:absolute;inset:-8px;border:2px solid transparent;border-radius:4px;pointer-events:none}.carousel_slide:focus-visible{outline:none}.carousel_slide:focus-visible::after{border-color:var(--color-focus, rgb(194, 38, 251))}.carousel_slide_fullwidth{padding:0}.carousel_slide_frame{width:100%;display:-ms-flexbox;display:flex;overflow:hidden;-webkit-transition:height ease-in-out 0.5s;-moz-transition:height ease-in-out 0.5s;transition:height ease-in-out 0.5s}.carousel_slide_frame_loading{opacity:0;pointer-events:none}.carousel_slide_frame *{-webkit-user-select:none;-ms-user-select:none;-moz-user-select:none;user-select:none;-webkit-user-drag:none;-khtml-user-drag:none;-moz-user-drag:none;-o-user-drag:none}.carousel_slide_frame *[slot=loop]{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:relative;right:0;-webkit-transition:right ease-in-out 0.75s;-moz-transition:right ease-in-out 0.75s;transition:right ease-in-out 0.75s}.carousel_slide_frame_repeater{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:relative;right:0;-webkit-transition:right ease-in-out 0.75s;-moz-transition:right ease-in-out 0.75s;transition:right ease-in-out 0.75s}.carousel_slide_loading{opacity:0;pointer-events:none;position:absolute;inset:0}.carousel_slide_loading_visible{opacity:1;pointer-events:all}.carousel_loading_bar{-webkit-box-sizing:border-box;box-sizing:border-box;padding:0 60px;margin-top:8px}.carousel_loading_bar_fullwidth{padding:0 4px}.carousel_buttons{position:absolute;width:100%;height:0px;top:calc(50% - 20px);left:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel_buttons_fullwidth{padding:0 8px}.carousel_bullets{position:relative;margin-top:8px}.carousel_bullets_inside{position:absolute;bottom:0px;width:100%;margin:0;padding:0px 16px;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel_bullets_card{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;display:-ms-inline-flexbox;display:inline-flex;gap:8px}.carousel_bullets_card_inside{border-top-left-radius:8px;border-top-right-radius:8px;padding:8px;background-color:var(--color-surface-0, rgb(255, 255, 255))}.carousel_bullets_item{width:16px;height:16px;border:2px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:50%;position:relative;-webkit-transform:rotate(45deg);transform:rotate(45deg);cursor:pointer}.carousel_bullets_item::before{content:"";position:absolute;inset:4px;border-radius:50%}.carousel_bullets_item::after{content:"";position:absolute;inset:-8px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);border:2px solid transparent;border-radius:4px}.carousel_bullets_item:focus-visible{outline:none}.carousel_bullets_item:focus-visible::after{border-color:var(--color-focus, rgb(194, 38, 251))}.carousel_bullets_item_active::before{background-color:var(--color-primary, rgb(30, 107, 241))}.carousel_bullets_item_conclude{position:absolute;inset:-2px;border-radius:50%;border:2px solid var(--color-content-disable, rgb(89, 89, 89))}.carousel_bullets_item_loader{position:absolute;inset:-2px;border-radius:50%;border:2px solid var(--color-primary, rgb(30, 107, 241));-webkit-animation:l18 linear;animation:l18 linear}@-webkit-keyframes l18{0%{-webkit-clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)}25%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)}50%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)}75%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)}100%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)}}@keyframes l18{0%{-webkit-clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)}25%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)}50%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)}75%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)}100%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)}}';const r=class{constructor(s){i(this,s);this.bdsChangeCarousel=t(this,"bdsChangeCarousel");this.itemsElement=null;this.bulletElement=null;this.bulletElements=[];this.itemActivated=1;this.seconds=0;this.isWhole=0;this.heightCarousel=240;this.framePressed=false;this.autoplayState="running";this.autoplay=false;this.autoplayTimeout=5e3;this.autoplayHoverPause=false;this.autoHeight=false;this.bullets="outside";this.bulletsPosition="center";this.infiniteLoop=false;this.arrows="outside";this.slidePerPage=1;this.gap="none";this.grab=true;this.loading=false;this.dtSlideContent=null;this.dtButtonPrev=null;this.dtButtonNext=null;this.secondsLimit=this.autoplayTimeout/1e3;this.setInternalItens=i=>{const t=Math.floor(i.length/this.slidePerPage);const s=i.length/this.slidePerPage;const e=o(s);this.internalItens=e;this.isWhole=i.length-this.slidePerPage*t};this.startCountSeconds=()=>{if(this.autoplay){this.incrementSeconds=setInterval((()=>{this.seconds+=.1}),100)}};this.updateHeight=i=>{const t=i[this.itemActivated*this.slidePerPage-this.slidePerPage];let s=240;if(this.slidePerPage>1){const t=this.isWhole>0&&this.itemActivated==this.internalItens.length?i.slice(this.internalItens.length-this.internalItens.length-this.slidePerPage,this.itemActivated*this.slidePerPage):i.slice(this.itemActivated*this.slidePerPage-this.slidePerPage,this.itemActivated*this.slidePerPage);s=a(t)[0]}else{s=t.offsetHeight}this.frame.style.height=`${s}px`};this.refFrame=i=>{this.frame=i};this.refThemeProviderArrows=i=>{this.themeProviderArrows=i};this.refFrameRepeater=i=>{this.frameRepeater=i};this.refBulletElement=i=>{if(i){this.bulletElement=i;this.bulletElements.push(i)}};this.onMouseOver=()=>{if(this.autoplayHoverPause){this.pauseAutoplay()}};this.onMouseOut=()=>{if(this.autoplayHoverPause){this.runAutoplay()}};this.onMouseDown=i=>{if(this.grab){this.framePressed=true;const t=this.frame.offsetLeft+this.element.offsetLeft;this.startX=i.pageX-t;this.endX=i.pageX-t;this.frame.style.cursor="grabbing"}};this.onMouseEnter=()=>{if(this.grab){this.frame.style.cursor="grab"}};this.onMouseUp=()=>{if(this.grab){this.framePressed=false;this.frame.style.cursor="grab";this.boundItems();if(this.autoplayHoverPause){this.pauseAutoplay()}}};this.onMouseMove=i=>{if(this.grab){if(!this.framePressed)return;i.preventDefault();const t=this.frame.offsetLeft+this.element.offsetLeft;this.endX=i.pageX-t}};this.boundItems=()=>{if(this.endX<this.startX){this.nextSlide();this.seconds=0}else if(this.endX>this.startX){this.prevSlide();this.seconds=0}};this.setKeydownNavigation=i=>{if(i.key==="Tab"){if(this.bulletElements.length>0){this.bulletElements[0].focus()}else if(this.bulletElement){this.bulletElement.focus()}}if(i.key==="ArrowRight"){this.nextSlide()}if(i.key==="ArrowLeft"){this.prevSlide()}}}componentWillLoad(){this.itemsElement=this.element.getElementsByTagName("bds-carousel-item");this.setInternalItens(Array.from(this.itemsElement));if(this.bullets==true){this.bullets="outside"}if(this.bullets==false){this.bullets="none"}}componentDidRender(){if(!this.loading){if(this.gap!="none"){this.frame.style.width=`calc(100% + ${l(this.gap)}px)`;this.frame.style.marginLeft=`-${l(this.gap)/2}px`}for(let i=0;i<this.itemsElement.length;i++){const t=this.frame.offsetWidth>=1920?1920:this.frame.offsetWidth;this.itemsElement[i].style.width=`${t/this.slidePerPage}px`;this.itemsElement[i].style.padding=`0 ${l(this.gap)/2}px`}if(this.autoHeight)this.updateHeight(Array.from(this.itemsElement))}if(this.arrows=="inside"){const i=(this.itemActivated-1)*(this.itemsElement.length/this.internalItens.length)+1;this.themeProviderArrows.theme=this.slidePerPage<=1?this.itemsElement[this.itemActivated-1].theme:this.itemsElement[Math.round(i)].theme}}componentDidLoad(){this.startCountSeconds()}itemActivatedChanged(){const i=this.internalItens.find((i=>i.id===this.itemActivated));const t=!this.frame?0:this.frame.offsetWidth*(this.itemActivated-1);if(this.frameRepeater){if(i.isWhole){const i=this.itemsElement[1].offsetWidth*(this.slidePerPage-this.isWhole);this.frameRepeater.style.right=`${t-i}px`}else{this.frameRepeater.style.right=`${t}px`}}this.bdsChangeCarousel.emit({value:i})}autoplayTimeoutChanged(){this.secondsLimit=this.autoplayTimeout/1e3}secondsChanged(){if(this.seconds>=this.secondsLimit){this.nextSlide();this.seconds=0}}isWholeChanged(){var i,t;if(this.internalItens!=undefined){if(this.isWhole>0){const s={id:((i=this.internalItens)===null||i===void 0?void 0:i.length)+1,label:`Frame - ${((t=this.internalItens)===null||t===void 0?void 0:t.length)+1}`,isWhole:true};this.internalItens=[...this.internalItens,s]}}}async buildCarousel(){this.itemsElement=this.element.getElementsByTagName("bds-carousel-item");this.loading=true;setTimeout((()=>(this.setInternalItens(Array.from(this.itemsElement)),this.loading=false,this.setActivated(1))),1e3)}async nextSlide(){if(this.itemActivated==this.internalItens.length){if(this.infiniteLoop||this.autoplay){this.itemActivated=1}else{this.itemActivated=this.itemActivated}}else{this.itemActivated=this.itemActivated+1}clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds()}async prevSlide(){if(this.itemActivated==1){if(this.infiniteLoop||this.autoplay){this.itemActivated=this.internalItens.length}else{this.itemActivated=this.itemActivated}}else{this.itemActivated=this.itemActivated-1}clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds()}async setActivated(i){this.itemActivated=i;clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds();this.autoplayState="running"}async pauseAutoplay(){clearInterval(this.incrementSeconds);this.autoplayState="paused"}async runAutoplay(){this.startCountSeconds();this.autoplayState="running"}render(){this.bulletElements=[];const i=this.arrows=="inside"?"bds-theme-provider":"div";const t=this.bulletsPosition=="center"?"center":this.bulletsPosition=="right"?"flex-end":this.bulletsPosition=="left"&&"flex-start";return s("div",{key:"aa7aebd1a8d211ab7c482337b1363dafe5e23d01",class:{carousel:true}},s("div",{key:"31a7d3f060d37f7241ff378dfc682d2c4d891ee5",class:{carousel_slide:true,carousel_slide_fullwidth:this.arrows!="outside",[`carousel_slide_state_${this.autoplayState}`]:this.autoplay},tabindex:"0",onKeyDown:i=>this.setKeydownNavigation(i),"data-test":this.dtSlideContent},s("div",{key:"5a590d1d40035790ad135954974a8977cdad30b1",ref:i=>this.refFrame(i),class:{carousel_slide_frame:true,carousel_slide_frame_loading:this.loading},onMouseOver:()=>this.onMouseOver(),onMouseOut:()=>this.onMouseOut(),onMouseDown:i=>this.onMouseDown(i),onMouseEnter:()=>this.onMouseEnter(),onMouseUp:()=>this.onMouseUp(),onMouseMove:i=>this.onMouseMove(i)},s("div",{key:"1478791fb1cdafab371e74d6fc7a8294260955ed",ref:i=>this.refFrameRepeater(i),class:{carousel_slide_frame_repeater:true}},s("slot",{key:"678c0af549a9171cb7c17db113026dee11c18093"}))),s("bds-grid",{key:"2fc13711f365a81f963958f3b5e16a9429967e13",class:{carousel_slide_loading:true,carousel_slide_loading_visible:this.loading}},s("bds-skeleton",{key:"bb484c25c5a651edd9cb9a41771c46837c13bdc8",height:"100%",shape:"square",width:"100%"})),this.arrows!="none"&&!this.loading&&s(i,{key:"163ea8a0fd5115c988f66532a01cedcf954e6803",ref:i=>this.refThemeProviderArrows(i),class:{carousel_buttons:true,carousel_buttons_fullwidth:this.arrows!="outside"}},s("bds-button",{key:"0ec0b57300c808f40e3f378d80474ec38bead9fd",variant:"text",iconLeft:"arrow-left",color:"content",onBdsClick:()=>this.prevSlide(),disabled:!this.infiniteLoop&&this.itemActivated<=1,dataTest:this.dtButtonPrev}),s("bds-button",{key:"653394988180d1c0a1b8ea2b8d28abdac0fa2676",variant:"text",iconLeft:"arrow-right",color:"content",onBdsClick:()=>this.nextSlide(),disabled:!this.infiniteLoop&&this.itemActivated>=this.internalItens.length,dataTest:this.dtButtonNext}))),this.internalItens.length>1&&this.bullets!="none"&&s("div",{key:"ead3dbd4e1c431bb7259dce22f5acbb224603b55",class:{carousel_bullets:true,carousel_bullets_inside:this.bullets=="inside"}},this.loading&&this.bullets!="inside"?s("bds-grid",{xxs:"12",gap:"1","justify-content":t,padding:this.arrows==="outside"?"x-7":"none"},s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"}),s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"}),s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"})):this.internalItens&&s("bds-grid",{xxs:"12","justify-content":t,padding:this.arrows==="outside"?"x-7":"none"},s("div",{class:{carousel_bullets_card:true,carousel_bullets_card_inside:this.bullets=="inside"}},this.internalItens.map(((i,t)=>s("div",{key:t,ref:i=>this.refBulletElement(i),class:{carousel_bullets_item:true,carousel_bullets_item_active:i.id==this.itemActivated},tabindex:"0",onClick:()=>this.setActivated(i.id)},i.id<this.itemActivated&&this.autoplay&&s("div",{class:{carousel_bullets_item_conclude:true}}),i.id==this.itemActivated&&this.autoplay&&s("div",{class:{carousel_bullets_item_loader:true},style:{animationDuration:`${this.autoplayTimeout/1e3-.1}s`,animationPlayState:this.autoplayState}}))))))),s("slot",{key:"53f9b8dc67817f99e57bfb72a5d210b93668df24",name:"after"}))}get element(){return e(this)}static get watchers(){return{itemActivated:["itemActivatedChanged"],autoplayTimeout:["autoplayTimeoutChanged"],seconds:["secondsChanged"],isWhole:["isWholeChanged"]}}};r.style=h;export{r as bds_carousel};
//# sourceMappingURL=p-29a4195b.entry.js.map