{"version": 3, "file": "p-BScsmuxb.system.js", "sources": ["src/components/table/table-cell/table-cell.scss?tag=bds-table-cell&encapsulation=scoped", "src/components/table/table-cell/table-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0 8px;\n  font-family: $font-family;\n  font-size: 14px;\n  vertical-align: middle;\n}\n.cell {\n  display: flex;\n  align-items: center;\n  min-height: 48px;\n  margin: 8px 0;\n  color: $color-content-default;\n  font-family: $font-family;\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.dense_cell {\n  margin: 0;\n}\n\n.cell_custom {\n  gap: 8px;\n}\n\n.cell_action {\n  flex-direction: row;\n  gap: 8px;\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type IconType = 'text' | 'custom' | 'emoji' | 'collapse';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n\n@Component({\n  tag: 'bds-table-cell',\n  styleUrl: 'table-cell.scss',\n  scoped: true,\n})\nexport class TableCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() type?: string = 'text';\n  @Prop() sortable = false;\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  renderContent(): HTMLElement {\n    return this.type === 'custom' ? (\n      <div class={{ cell:true, cell_custom:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'text' ? (\n      <div class={{ cell:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <bds-typo variant=\"fs-14\" bold={this.sortable ? 'bold' : 'regular'}>\n          <slot />\n        </bds-typo>\n      </div>\n    ) : this.type === 'action' ? (\n      <div class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'collapse' ? (\n      <td colSpan={2} class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </td>\n    ) : (\n      <slot />\n    );\n  }\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && bdsTable.getAttribute('dense-table') === 'true') {\n      this.isDense = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return <Host>{this.renderContent()}</Host>;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,YAAY,GAAG,mgCAAmgC;;YCW3gC,SAAS,6BAAA,MAAA;MALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;MAOW,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;MAChB,QAAA,IAAI,CAAA,IAAA,GAAY,MAAM;MACtB,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAChB,QAAA,IAAc,CAAA,cAAA,GAAmB,MAAM;MAoChD;UAlCC,aAAa,GAAA;cACX,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAC3B,WAAK,KAAK,EAAE,EAAE,IAAI,EAAC,IAAI,EAAE,WAAW,EAAC,IAAI,EAAE,UAAU,EAAC,IAAI,EAAE,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,cAAc,CAAA,CAAE,GAAE,IAAI,EAAE,EAAA,EACpG,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACJ,IACJ,IAAI,CAAC,IAAI,KAAK,MAAM,IACtB,WAAK,KAAK,EAAE,EAAE,IAAI,EAAC,IAAI,EAAE,UAAU,EAAC,IAAI,EAAE,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,cAAc,CAAA,CAAE,GAAE,IAAI,EAAE,EAAA,EAClF,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS,EAAA,EAChE,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACC,CACP,IACJ,IAAI,CAAC,IAAI,KAAK,QAAQ,IACxB,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,IAAI,EAAC,IAAI,EAAE,WAAW,EAAC,IAAI,EAAE,UAAU,EAAC,IAAI,EAAE,CAAC,CAAY,SAAA,EAAA,IAAI,CAAC,cAAc,CAAE,CAAA,GAAE,IAAI,EAAE,EAAA,EACpG,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACJ,IACJ,IAAI,CAAC,IAAI,KAAK,UAAU,IAC1B,CAAA,CAAA,IAAA,EAAA,EAAI,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAC,IAAI,EAAE,WAAW,EAAC,IAAI,EAAE,UAAU,EAAC,IAAI,EAAE,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,cAAc,CAAE,CAAA,GAAE,IAAI,EAAE,EAAA,EAC/G,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,CACL,KAEL,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACT;;UAGH,iBAAiB,GAAA;cACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;cAClD,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;MAC/D,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;;UAIvB,MAAM,GAAA;MACJ,QAAA,OAAO,CAAA,CAAC,IAAI,EAAE,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAA,IAAI,CAAC,aAAa,EAAE,CAAQ;;;;;;;;;;;;"}