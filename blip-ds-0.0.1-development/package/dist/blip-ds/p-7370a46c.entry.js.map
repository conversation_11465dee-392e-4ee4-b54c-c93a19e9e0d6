{"version": 3, "names": ["dropdownCss", "BdsDropdown", "constructor", "hostRef", "this", "intoView", "stateOpenSubMenu", "stateSubMenu", "zIndex", "delay", "activeMode", "open", "position", "dataTest", "onCloseSubMenu", "refDropElement", "el", "dropElement", "onClickCloseButtom", "onMouseOver", "onMouseOut", "handleClickOutside", "event", "hostElement", "contains", "target", "setClose", "centerDropElement", "value", "arrayPosition", "split", "style", "top", "offsetHeight", "componentWillLoad", "activatorElement", "querySelector", "children", "getScrollParent", "addEventListener", "toggle", "componentDidLoad", "setDefaultPlacement", "validatePositionDrop", "document", "disconnectedCallback", "removeEventListener", "classList", "add", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "x", "isOpenChanged", "bdsToggle", "emit", "isPositionChanged", "<PERSON><PERSON><PERSON>", "clearTimeout", "openSubMenuChanged", "active", "setTimeout", "stateSubMenuChanged", "state", "render", "zIndexSubmenu", "h", "Host", "key", "name", "ref", "class", "dropdown", "dropdown__open", "outzone", "onClick"], "sources": ["src/components/dropdown/dropdown.scss?tag=bds-dropdown&encapsulation=shadow", "src/components/dropdown/dropdown.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  position: relative;\n  width: fit-content;\n}\n\n:host(.is_child_drop) {\n  display: block;\n  width: 100%;\n}\n\n.dropdown {\n  position: absolute;\n  pointer-events: none;\n  padding: 2px;\n  background-color: $color-surface-0;\n  border-radius: 8px;\n  box-shadow: $shadow-2;\n  min-width: 240px;\n  width: max-content;\n  opacity: 0;\n  -webkit-transition: opacity 0.5s;\n  -moz-transition: opacity 0.5s;\n  transition: opacity 0.5s;\n  z-index: $zindex-modal;\n\n  &__open {\n    pointer-events: auto;\n    opacity: 1;\n  }\n\n  &__basic {\n    &__top-center {\n      bottom: calc(100% + 16px);\n      left: calc(50% - 122px);\n    }\n    &__top-left {\n      bottom: calc(100% + 16px);\n      left: 0;\n    }\n    &__top-right {\n      bottom: calc(100% + 16px);\n      right: 0;\n    }\n    &__bottom-center {\n      top: calc(100% + 16px);\n      left: calc(50% - 122px);\n    }\n    &__bottom-right {\n      top: calc(100% + 16px);\n      right: 0;\n    }\n    &__bottom-left {\n      top: calc(100% + 16px);\n      left: 0;\n    }\n    &__right-center {\n      right: calc(100% + 8px);\n    }\n    &__right-top {\n      right: calc(100% + 8px);\n      top: 0;\n    }\n    &__right-bottom {\n      right: calc(100% + 8px);\n      bottom: 0;\n    }\n    &__left-center {\n      left: calc(100% + 8px);\n    }\n    &__left-top {\n      left: calc(100% + 8px);\n      top: 0;\n    }\n    &__left-bottom {\n      left: calc(100% + 8px);\n      bottom: 0;\n    }\n  }\n  &:after {\n    content: '';\n    position: absolute;\n    inset: 0;\n    border-radius: 8px;\n    box-shadow: $color-surface-0 0px 0px 0px 2px inset;\n    pointer-events: none;\n  }\n}\n\n.outzone {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  z-index: $zindex-modal-overlay;\n}\n", "import {\n  Component,\n  Host,\n  ComponentInterface,\n  h,\n  Element,\n  State,\n  Method,\n  Prop,\n  Event,\n  EventEmitter,\n  Watch,\n} from '@stencil/core';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type activeMode = 'hover' | 'click';\nexport type dropVerticalPosition = 'bottom' | 'top' | 'left' | 'right';\nexport type dropHorizontalPosition = 'left' | 'center' | 'right' | 'bottom' | 'top';\n//^^ dropHorizontalPosition: For version 2.0 change to values: \"start\", \"center\", \"end\". ^^//\nexport type subMenuState = 'close' | 'pending' | 'open';\n\nexport type DropdownPostionType =\n  | 'auto'\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom';\n\n@Component({\n  tag: 'bds-dropdown',\n  styleUrl: 'dropdown.scss',\n  shadow: true,\n})\nexport class BdsDropdown implements ComponentInterface {\n  private activatorElement?: Element;\n  private dropElement?: HTMLElement;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() stateOpenSubMenu?: boolean = false;\n  @State() stateSubMenu?: subMenuState = 'close';\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop() public activeMode?: activeMode = 'click';\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop({ mutable: true, reflect: true }) public open?: boolean = false;\n\n  /**\n   * Used to set drop position\n   */\n  @Prop() position?: DropdownPostionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * bdsToggle. Event to return selected date value.\n   */\n  @Event() bdsToggle?: EventEmitter;\n\n  componentWillLoad() {\n    this.activatorElement = this.hostElement.querySelector('[slot=\"dropdown-activator\"]').children[0];\n    this.intoView = getScrollParent(this.hostElement);\n    this.isPositionChanged;\n    if (this.activeMode == 'hover') {\n      this.activatorElement.addEventListener('mouseover', () => this.onMouseOver());\n      this.activatorElement.addEventListener('click', () => this.onMouseOver());\n      this.activatorElement.addEventListener('mouseout', () => this.onMouseOut());\n    } else {\n      this.activatorElement.addEventListener('click', () => this.toggle());\n    }\n  }\n\n  componentDidLoad() {\n    if (this.position != 'auto') {\n      this.centerDropElement(this.position);\n      this.setDefaultPlacement(this.position);\n    } else {\n      this.validatePositionDrop();\n    }\n\n    document.addEventListener('click', this.handleClickOutside);\n  }\n\n  disconnectedCallback() {\n    document.removeEventListener('click', this.handleClickOutside);\n  }\n\n  private setDefaultPlacement(value: DropdownPostionType) {\n    this.dropElement.classList.add(`dropdown__basic__${value}`);\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.hostElement,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.dropElement.classList.add(`dropdown__basic__${positionValue.y}-${positionValue.x}`);\n  }\n\n  @Watch('open')\n  protected isOpenChanged(open: boolean): void {\n    this.bdsToggle.emit({ value: open });\n    if (open)\n      if (this.position != 'auto') {\n        this.setDefaultPlacement(this.position);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('position')\n  protected isPositionChanged(): void {\n    this.setDefaultPlacement(this.position);\n  }\n\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Method()\n  async setOpen() {\n    this.open = true;\n  }\n\n  @Method()\n  async setClose() {\n    this.stateOpenSubMenu = false;\n    clearTimeout(this.delay);\n    this.open = false;\n  }\n\n  @Watch('stateOpenSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n    return;\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: subMenuState): void {\n    switch (state) {\n      case 'open':\n        this.open = true;\n        break;\n      case 'pending':\n        this.open = true;\n        break;\n      case 'close':\n        this.open = false;\n        break;\n    }\n  }\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  private refDropElement = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private onClickCloseButtom = () => {\n    this.open = false;\n  };\n\n  private onMouseOver = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 1;\n    }\n    this.stateOpenSubMenu = true;\n  };\n\n  private onMouseOut = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 0;\n      this.stateOpenSubMenu = false;\n    }\n  };\n\n  private handleClickOutside = (event: MouseEvent) => {\n    if (this.open && !this.hostElement.contains(event.target as Node)) {\n      this.setClose();\n    }\n  };\n\n  private centerDropElement = (value: DropdownPostionType) => {\n    const arrayPosition = value.split('-');\n    if ((arrayPosition[0] == 'left' || arrayPosition[0] == 'right') && arrayPosition[1] == 'center') {\n      this.dropElement.style.top = `calc(50% - ${this.dropElement.offsetHeight / 2}px)`;\n    }\n  };\n\n  render() {\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n    return (\n      <Host>\n        <slot name=\"dropdown-activator\"></slot>\n        <div\n          ref={(el) => this.refDropElement(el)}\n          class={{\n            dropdown: true,\n            dropdown__open: this.open,\n          }}\n          data-test={this.dataTest}\n          onMouseOver={() => this.onMouseOver()}\n          onMouseOut={() => this.onMouseOut()}\n        >\n          <div class=\"content\" style={zIndexSubmenu}>\n            <slot name=\"dropdown-content\"></slot>\n          </div>\n        </div>\n        {this.activeMode !== 'hover' && this.open && (\n          <div class={{ outzone: true }} onClick={() => this.onClickCloseButtom()}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "2GAAA,MAAMA,EAAc,msD,MCyCPC,EAAW,MALxB,WAAAC,CAAAC,G,6CAWWC,KAAQC,SAAiB,KAEzBD,KAAgBE,iBAAa,MAC7BF,KAAYG,aAAkB,QAC9BH,KAAMI,OAAY,EAClBJ,KAAKK,MAAG,KAKFL,KAAUM,WAAgB,QAKMN,KAAIO,KAAa,MAKxDP,KAAQQ,SAAyB,OAKjCR,KAAQS,SAAY,KA8GpBT,KAAcU,eAAG,KACvBV,KAAKG,aAAe,OAAO,EAGrBH,KAAAW,eAAkBC,IACxBZ,KAAKa,YAAcD,CAAE,EAGfZ,KAAkBc,mBAAG,KAC3Bd,KAAKO,KAAO,KAAK,EAGXP,KAAWe,YAAG,KACpB,GAAIf,KAAKM,aAAe,QAAS,CAC/BN,KAAKI,OAAS,C,CAEhBJ,KAAKE,iBAAmB,IAAI,EAGtBF,KAAUgB,WAAG,KACnB,GAAIhB,KAAKM,aAAe,QAAS,CAC/BN,KAAKI,OAAS,EACdJ,KAAKE,iBAAmB,K,GAIpBF,KAAAiB,mBAAsBC,IAC5B,GAAIlB,KAAKO,OAASP,KAAKmB,YAAYC,SAASF,EAAMG,QAAiB,CACjErB,KAAKsB,U,GAIDtB,KAAAuB,kBAAqBC,IAC3B,MAAMC,EAAgBD,EAAME,MAAM,KAClC,IAAKD,EAAc,IAAM,QAAUA,EAAc,IAAM,UAAYA,EAAc,IAAM,SAAU,CAC/FzB,KAAKa,YAAYc,MAAMC,IAAM,cAAc5B,KAAKa,YAAYgB,aAAe,M,EA+BhF,CAzKC,iBAAAC,GACE9B,KAAK+B,iBAAmB/B,KAAKmB,YAAYa,cAAc,+BAA+BC,SAAS,GAC/FjC,KAAKC,SAAWiC,EAAgBlC,KAAKmB,aAErC,GAAInB,KAAKM,YAAc,QAAS,CAC9BN,KAAK+B,iBAAiBI,iBAAiB,aAAa,IAAMnC,KAAKe,gBAC/Df,KAAK+B,iBAAiBI,iBAAiB,SAAS,IAAMnC,KAAKe,gBAC3Df,KAAK+B,iBAAiBI,iBAAiB,YAAY,IAAMnC,KAAKgB,c,KACzD,CACLhB,KAAK+B,iBAAiBI,iBAAiB,SAAS,IAAMnC,KAAKoC,U,EAI/D,gBAAAC,GACE,GAAIrC,KAAKQ,UAAY,OAAQ,CAC3BR,KAAKuB,kBAAkBvB,KAAKQ,UAC5BR,KAAKsC,oBAAoBtC,KAAKQ,S,KACzB,CACLR,KAAKuC,sB,CAGPC,SAASL,iBAAiB,QAASnC,KAAKiB,mB,CAG1C,oBAAAwB,GACED,SAASE,oBAAoB,QAAS1C,KAAKiB,mB,CAGrC,mBAAAqB,CAAoBd,GAC1BxB,KAAKa,YAAY8B,UAAUC,IAAI,oBAAoBpB,I,CAG7C,oBAAAe,GACN,MAAMM,EAAgBC,EAAwB,CAC5CC,cAAe/C,KAAKmB,YACpB6B,eAAgBhD,KAAKa,YACrBZ,SAAUD,KAAKC,WAEjBD,KAAKa,YAAY8B,UAAUC,IAAI,oBAAoBC,EAAcI,KAAKJ,EAAcK,I,CAI5E,aAAAC,CAAc5C,GACtBP,KAAKoD,UAAUC,KAAK,CAAE7B,MAAOjB,IAC7B,GAAIA,EACF,GAAIP,KAAKQ,UAAY,OAAQ,CAC3BR,KAAKsC,oBAAoBtC,KAAKQ,S,KACzB,CACLR,KAAKuC,sB,EAKD,iBAAAe,GACRtD,KAAKsC,oBAAoBtC,KAAKQ,S,CAIhC,YAAM4B,GACJpC,KAAKO,MAAQP,KAAKO,I,CAIpB,aAAMgD,GACJvD,KAAKO,KAAO,I,CAId,cAAMe,GACJtB,KAAKE,iBAAmB,MACxBsD,aAAaxD,KAAKK,OAClBL,KAAKO,KAAO,K,CAIJ,kBAAAkD,CAAmBC,GAC3B,GAAIA,GAAU,MAAO,CACnB1D,KAAKG,aAAe,UACpBH,KAAKK,MAAQsD,WAAW3D,KAAKU,eAAgB,I,CAE/C,GAAIgD,GAAU,KAAM,CAClBF,aAAaxD,KAAKK,OAClBL,KAAKK,MAAQ,KACbL,KAAKG,aAAe,M,CAEtB,M,CAIQ,mBAAAyD,CAAoBC,GAC5B,OAAQA,GACN,IAAK,OACH7D,KAAKO,KAAO,KACZ,MACF,IAAK,UACHP,KAAKO,KAAO,KACZ,MACF,IAAK,QACHP,KAAKO,KAAO,MACZ,M,CA2CN,MAAAuD,GACE,MAAMC,EAAgB,CACpB3D,OAAQ,GAAGJ,KAAKI,UAElB,OACE4D,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAM,QAAAE,IAAA,2CAAAC,KAAK,uBACXH,EAAA,OAAAE,IAAA,2CACEE,IAAMxD,GAAOZ,KAAKW,eAAeC,GACjCyD,MAAO,CACLC,SAAU,KACVC,eAAgBvE,KAAKO,MAEZ,YAAAP,KAAKS,SAChBM,YAAa,IAAMf,KAAKe,cACxBC,WAAY,IAAMhB,KAAKgB,cAEvBgD,EAAA,OAAAE,IAAA,2CAAKG,MAAM,UAAU1C,MAAOoC,GAC1BC,EAAA,QAAAE,IAAA,2CAAMC,KAAK,uBAGdnE,KAAKM,aAAe,SAAWN,KAAKO,MACnCyD,EAAA,OAAAE,IAAA,2CAAKG,MAAO,CAAEG,QAAS,MAAQC,QAAS,IAAMzE,KAAKc,uB", "ignoreList": []}