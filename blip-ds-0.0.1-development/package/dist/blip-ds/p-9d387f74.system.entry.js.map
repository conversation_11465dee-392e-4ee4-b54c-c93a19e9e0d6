{"version": 3, "names": ["toastCss", "BdsToast", "exports", "class_1", "hostRef", "_this", "this", "icon", "actionType", "variant", "duration", "buttonAction", "show", "hide", "position", "dtButtonAction", "dtButtonClose", "_buttonClickHandler", "close", "toastButtonClick", "emit", "el", "mapIconName", "system", "error", "success", "warning", "undo", "redo", "notification", "prototype", "_key<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "key", "preventDefault", "create", "_a", "_b", "buttonText", "toastText", "toastTitle", "toast<PERSON><PERSON><PERSON>", "document", "querySelector", "concat", "append<PERSON><PERSON><PERSON>", "classList", "add", "createElement", "body", "setTimeout", "remove", "shadowRoot", "render", "h", "class", "toast", "theme", "name", "size", "bold", "innerHTML", "toast__action", "onKeyDown", "bind", "tabindex", "onClick", "dataTest"], "sources": ["src/components/toast/toast.scss?tag=bds-toast&encapsulation=shadow", "src/components/toast/toast.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n@mixin inherit-color() {\n  color: inherit;\n  background-color: inherit;\n}\n:host {\n  .show,\n  .hide {\n    display: flex;\n  }\n  .show {\n    opacity: 1;\n\n    &--top-right,\n    &--bottom-right {\n      animation: toastAnimationFadeInFromRight 1s;\n    }\n\n    &--top-left,\n    &--bottom-left {\n      animation: toastAnimationFadeInFromLeft 1s;\n    }\n  }\n  .hide {\n    transition: all 1s;\n    animation: toastAnimationFadeOut 0.5s;\n  }\n}\n\n.toast {\n  display: none;\n  position: relative;\n  box-sizing: border-box;\n  border-radius: 8px;\n  box-shadow: $shadow-3;\n  color: $color-content-default;\n  opacity: 0;\n  margin-top: 16px;\n  overflow: hidden;\n  gap: 16px;\n\n  &--action--icon {\n    min-width: 440px;\n    max-width: 440px;\n    padding: 8px 16px;\n\n    bds-icon-button {\n      height: 32px;\n    }\n\n    @media (max-width: $sm-screen) {\n      min-width: 220px;\n      width: 95%;\n      margin: 16px auto 0px auto;\n    }\n  }\n\n  &--action--button {\n    min-width: 440px;\n    max-width: 456px;\n    padding: 8px 16px;\n\n    @media (max-width: $sm-screen) {\n      min-width: 220px;\n      width: 95%;\n      margin: 16px auto 0px auto;\n    }\n  }\n\n  &--system {\n    background: $color-system;\n  }\n\n  &--error {\n    background: $color-error;\n  }\n\n  &--success {\n    background: $color-success;\n  }\n\n  &--warning {\n    background: $color-warning;\n  }\n\n  &--undo {\n    background-color: $color-system;\n  }\n\n  &--redo {\n    background-color: $color-system;\n  }\n  &--notification {\n    background-color: $color-surface-1;\n  }\n\n  &__icon {\n    position: relative;\n    display: flex;\n    align-items: center;\n    padding: 8px 0;\n  }\n\n  &__ballon {\n    display: flex;\n    position: absolute;\n    top: -8px;\n    left: -12px;\n    color: $color-system;\n    width: 72px;\n  }\n\n  &__content {\n    position: relative;\n    height: 100%;\n    width: 100%;\n    align-items: flex-start;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    padding: 8px 0;\n  }\n\n  &__action {\n    display: flex;\n    align-items: flex-start;\n\n    bds-button-icon,\n    bds-button {\n      position: relative;\n      &::before {\n        content: '';\n        position: absolute;\n        inset: -4px;\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n\n        &::before {\n          border-color: $color-focus;\n        }\n      }\n    }\n\n    &__button {\n      white-space: nowrap;\n    }\n  }\n}\n\n@keyframes toastAnimationFadeInFromRight {\n  0% {\n    opacity: 0;\n    right: -200px;\n  }\n  50% {\n    opacity: 0.9;\n    right: 1px;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes toastAnimationFadeInFromLeft {\n  0% {\n    opacity: 0;\n    left: -200px;\n  }\n  50% {\n    opacity: 0.9;\n    left: 1px;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes toastAnimationFadeOut {\n  0% {\n    opacity: 1;\n  }\n  30% {\n    max-height: 60px;\n  }\n  80% {\n    opacity: 0;\n    max-height: 30px;\n  }\n  100% {\n    max-height: 0px;\n  }\n}\n", "import { Component, ComponentInterface, h, Prop, Method, Element, Event, EventEmitter } from '@stencil/core';\nimport {\n  ActionType,\n  VariantType,\n  ButtonActionType,\n  CreateToastType,\n  IconVariantMap,\n  PositionType,\n} from './toast-interface';\n@Component({\n  tag: 'bds-toast',\n  styleUrl: 'toast.scss',\n  shadow: true,\n})\nexport class BdsToast implements ComponentInterface {\n  @Element() el: HTMLBdsToastElement;\n  /**\n   * used for add the icon. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n  /**\n   * ActionType. Defines if the button should have a button or an icon. Can be one of:\n   * 'icon', 'button';\n   */\n  @Prop() actionType: ActionType = 'button';\n  /**\n   * Variant. Defines the color of the toast. Can be one of:\n   * 'system', 'error', 'success', 'warning', 'undo', 'redo';\n   */\n  @Prop() variant: VariantType = 'system';\n  /**\n   * The title of the component:\n   */\n  @Prop() toastTitle: string;\n  /**\n   * The text content of the component:\n   */\n  @Prop() toastText: string;\n  /**\n   * If the action type is button, this will be the text of the button:\n   */\n  @Prop() buttonText: string;\n  /**\n   * Time to close the toast in seconds\n   * 0 = never close automatically (default value)\n   */\n  @Prop() duration = 0;\n  /**\n   * Define an action to the button toast. Can be one of:\n   * 'close', 'custom';\n   * if the action type is set to close, the button will close automatically.\n   * if the action type is set to custom, a function need to be passed when the toastButtonClick is emitted.\n   */\n  @Prop() buttonAction: ButtonActionType = 'close';\n  /**\n   * Controls the open event of the component:\n   */\n  @Prop() show = false;\n  /**\n   * Controls the hide event of the component:\n   */\n  @Prop() hide = false;\n  /**\n   * The toast position on the screen. Can be one of:\n   * 'top-right', 'top-left', 'bottom-right', 'bottom-left' (default value);\n   */\n  @Prop() position: PositionType = 'bottom-left';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonAction is the data-test to button action.\n   */\n  @Prop() dtButtonAction?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Event used to execute some action when the action button on the toast is clicked\n   */\n  @Event() toastButtonClick!: EventEmitter;\n  /**\n   * Sends an event to be used when creating an action when clicking the toast button\n   */\n  private _buttonClickHandler = () => {\n    if (this.buttonAction === 'close') this.close();\n    else {\n      this.toastButtonClick.emit(this.el);\n      this.close();\n    }\n  };\n\n  private _keyPressHandler(event) {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault();\n      if (this.buttonAction === 'close') this.close();\n      else {\n        this.toastButtonClick.emit(this.el);\n        this.close();\n      }\n    }\n  }\n\n  /**\n   * Can be used outside to open the toast\n   */\n  @Method()\n  async create({\n    actionType,\n    buttonAction,\n    buttonText,\n    icon,\n    toastText,\n    toastTitle,\n    variant,\n    duration,\n  }: CreateToastType) {\n    let toastContainer = document.querySelector(\n      `bds-toast-container.${variant === 'notification' ? 'top-right' : 'bottom-left'}`,\n    );\n\n    if (toastContainer) {\n      toastContainer.appendChild(this.el);\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n    } else {\n      toastContainer = document.createElement('bds-toast-container');\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n      document.body.appendChild(toastContainer);\n      toastContainer.appendChild(this.el);\n    }\n    this.el.actionType = actionType || 'button';\n    this.el.buttonAction = buttonAction || 'close';\n    this.el.buttonText = buttonText;\n    this.el.toastText = toastText;\n    this.el.toastTitle = toastTitle;\n    this.el.variant = variant || 'system';\n    this.el.duration = duration * 1000 || 0;\n    this.el.position = variant === 'notification' ? 'top-right' : 'bottom-left';\n\n    this.el.icon = icon ?? this.mapIconName[this.variant];\n\n    this.el.show = true;\n\n    if (this.el.duration > 0) {\n      setTimeout(() => {\n        this.el.hide = true;\n        setTimeout(() => {\n          this.el.remove();\n        }, 400);\n      }, this.el.duration);\n    }\n  }\n\n  /**\n   * Can be used outside the component to close the toast\n   */\n  @Method()\n  async close() {\n    if (this.el.shadowRoot) {\n      this.el.shadowRoot.querySelector('div').classList.remove('show');\n      this.el.shadowRoot.querySelector('div').classList.add('hide');\n    } else {\n      this.el.querySelector('div').classList.remove('show');\n      this.el.querySelector('div').classList.add('hide');\n    }\n\n    setTimeout(() => {\n      this.el.remove();\n    }, 400);\n  }\n\n  private mapIconName: IconVariantMap = {\n    system: 'bell',\n    error: 'error',\n    success: 'like',\n    warning: 'attention',\n    undo: 'undo',\n    redo: 'redo',\n    notification: 'notification',\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          toast: true,\n          [`toast--${this.variant}`]: true,\n          [`toast--action--${this.actionType}`]: true,\n          [`show show--${this.position}`]: this.show,\n          hide: this.hide,\n        }}\n      >\n        {this.variant === 'notification' && (\n          <bds-icon class=\"toast__ballon\" theme=\"solid\" name=\"blip-chat\" size=\"brand\" />\n        )}\n        {this.icon && <bds-icon class=\"toast__icon\" theme=\"outline\" size=\"medium\" name={this.icon} />}\n        <div class=\"toast__content\">\n          {this.toastTitle && (\n            <bds-typo variant=\"fs-14\" bold=\"bold\">\n              {this.toastTitle}\n            </bds-typo>\n          )}\n          {this.toastText && <bds-typo variant=\"fs-14\" innerHTML={this.toastText}></bds-typo>}\n        </div>\n        <div\n          class={{\n            toast__action: true,\n            [`toast__action__${this.actionType}`]: true,\n          }}\n        >\n          {this.actionType === 'button' ? (\n            <bds-button\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              onClick={() => this._buttonClickHandler()}\n              variant=\"secondary\"\n              size=\"standard\"\n              dataTest={this.dtButtonAction}\n            >\n              {this.buttonText}\n            </bds-button>\n          ) : (\n            <bds-button-icon\n              onClick={() => this._buttonClickHandler()}\n              size=\"short\"\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              variant=\"secondary\"\n              icon=\"close\"\n              dataTest={this.dtButtonClose}\n            />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAW,8lH,ICcJC,EAAQC,EAAA,uBALrB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,2DAU2BA,KAAIC,KAAY,KAKjCD,KAAUE,WAAe,SAKzBF,KAAOG,QAAgB,SAiBvBH,KAAQI,SAAG,EAOXJ,KAAYK,aAAqB,QAIjCL,KAAIM,KAAG,MAIPN,KAAIO,KAAG,MAKPP,KAAQQ,SAAiB,cAMzBR,KAAcS,eAAY,KAK1BT,KAAaU,cAAY,KAQzBV,KAAmBW,oBAAG,WAC5B,GAAIZ,EAAKM,eAAiB,QAASN,EAAKa,YACnC,CACHb,EAAKc,iBAAiBC,KAAKf,EAAKgB,IAChChB,EAAKa,O,CAET,EAiFQZ,KAAAgB,YAA8B,CACpCC,OAAQ,OACRC,MAAO,QACPC,QAAS,OACTC,QAAS,YACTC,KAAM,OACNC,KAAM,OACNC,aAAc,eA0DjB,CAhJS1B,EAAA2B,UAAAC,iBAAA,SAAiBC,GACvB,GAAIA,EAAMC,MAAQ,SAAWD,EAAMC,MAAQ,IAAK,CAC9CD,EAAME,iBACN,GAAI5B,KAAKK,eAAiB,QAASL,KAAKY,YACnC,CACHZ,KAAKa,iBAAiBC,KAAKd,KAAKe,IAChCf,KAAKY,O,IASLf,EAAA2B,UAAAK,OAAN,SAAAC,G,iDAAaC,G,qBACX7B,EAAU6B,EAAA7B,WACVG,EAAY0B,EAAA1B,aACZ2B,EAAUD,EAAAC,WACV/B,EAAI8B,EAAA9B,KACJgC,EAASF,EAAAE,UACTC,EAAUH,EAAAG,WACV/B,EAAO4B,EAAA5B,QACPC,EAAQ2B,EAAA3B,S,qCAEJ+B,EAAiBC,SAASC,cAC5B,uBAAAC,OAAuBnC,IAAY,eAAiB,YAAc,gBAGpE,GAAIgC,EAAgB,CAClBA,EAAeI,YAAYvC,KAAKe,IAChCoB,EAAeK,UAAUC,IAAItC,IAAY,eAAiB,YAAc,c,KACnE,CACLgC,EAAiBC,SAASM,cAAc,uBACxCP,EAAeK,UAAUC,IAAItC,IAAY,eAAiB,YAAc,eACxEiC,SAASO,KAAKJ,YAAYJ,GAC1BA,EAAeI,YAAYvC,KAAKe,G,CAElCf,KAAKe,GAAGb,WAAaA,GAAc,SACnCF,KAAKe,GAAGV,aAAeA,GAAgB,QACvCL,KAAKe,GAAGiB,WAAaA,EACrBhC,KAAKe,GAAGkB,UAAYA,EACpBjC,KAAKe,GAAGmB,WAAaA,EACrBlC,KAAKe,GAAGZ,QAAUA,GAAW,SAC7BH,KAAKe,GAAGX,SAAWA,EAAW,KAAQ,EACtCJ,KAAKe,GAAGP,SAAWL,IAAY,eAAiB,YAAc,cAE9DH,KAAKe,GAAGd,KAAOA,IAAI,MAAJA,SAAI,EAAJA,EAAQD,KAAKgB,YAAYhB,KAAKG,SAE7CH,KAAKe,GAAGT,KAAO,KAEf,GAAIN,KAAKe,GAAGX,SAAW,EAAG,CACxBwC,YAAW,WACT7C,EAAKgB,GAAGR,KAAO,KACfqC,YAAW,WACT7C,EAAKgB,GAAG8B,Q,GACP,IACL,GAAG7C,KAAKe,GAAGX,S,kBAQTP,EAAA2B,UAAAZ,MAAN,W,gGACE,GAAIZ,KAAKe,GAAG+B,WAAY,CACtB9C,KAAKe,GAAG+B,WAAWT,cAAc,OAAOG,UAAUK,OAAO,QACzD7C,KAAKe,GAAG+B,WAAWT,cAAc,OAAOG,UAAUC,IAAI,O,KACjD,CACLzC,KAAKe,GAAGsB,cAAc,OAAOG,UAAUK,OAAO,QAC9C7C,KAAKe,GAAGsB,cAAc,OAAOG,UAAUC,IAAI,O,CAG7CG,YAAW,WACT7C,EAAKgB,GAAG8B,Q,GACP,K,iBAaLhD,EAAA2B,UAAAuB,OAAA,W,QAAA,IAAAhD,EAAAC,KACE,OACEgD,EACE,OAAArB,IAAA,2CAAAsB,OAAKnB,EAAA,CACHoB,MAAO,MACPpB,EAAC,UAAAQ,OAAUtC,KAAKG,UAAY,KAC5B2B,EAAC,kBAAAQ,OAAkBtC,KAAKE,aAAe,KACvC4B,EAAC,cAAAQ,OAActC,KAAKQ,WAAaR,KAAKM,KACtCwB,EAAAvB,KAAMP,KAAKO,K,IAGZP,KAAKG,UAAY,gBAChB6C,EAAA,YAAArB,IAAA,2CAAUsB,MAAM,gBAAgBE,MAAM,QAAQC,KAAK,YAAYC,KAAK,UAErErD,KAAKC,MAAQ+C,EAAA,YAAArB,IAAA,2CAAUsB,MAAM,cAAcE,MAAM,UAAUE,KAAK,SAASD,KAAMpD,KAAKC,OACrF+C,EAAK,OAAArB,IAAA,2CAAAsB,MAAM,kBACRjD,KAAKkC,YACJc,EAAA,YAAArB,IAAA,2CAAUxB,QAAQ,QAAQmD,KAAK,QAC5BtD,KAAKkC,YAGTlC,KAAKiC,WAAae,EAAA,YAAArB,IAAA,2CAAUxB,QAAQ,QAAQoD,UAAWvD,KAAKiC,aAE/De,EAAA,OAAArB,IAAA,2CACEsB,OAAKlB,EAAA,CACHyB,cAAe,MACfzB,EAAC,kBAAAO,OAAkBtC,KAAKE,aAAe,K,IAGxCF,KAAKE,aAAe,SACnB8C,EACE,cAAAS,UAAWzD,KAAKyB,iBAAiBiC,KAAK1D,MACtC2D,SAAS,IACTC,QAAS,WAAM,OAAA7D,EAAKY,qBAAL,EACfR,QAAQ,YACRkD,KAAK,WACLQ,SAAU7D,KAAKS,gBAEdT,KAAKgC,YAGRgB,EAAA,mBACEY,QAAS,WAAM,OAAA7D,EAAKY,qBAAL,EACf0C,KAAK,QACLI,UAAWzD,KAAKyB,iBAAiBiC,KAAK1D,MACtC2D,SAAS,IACTxD,QAAQ,YACRF,KAAK,QACL4D,SAAU7D,KAAKU,iB,uHAxNR,I", "ignoreList": []}