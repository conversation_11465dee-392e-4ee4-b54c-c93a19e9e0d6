{"version": 3, "names": ["imageCss", "Image", "exports", "class_1", "hostRef", "this", "imageHasLoading", "objectFit", "dataTest", "imageLoaded", "loadError", "prototype", "componentDidLoad", "element", "style", "width", "height", "_a", "length", "loadImage", "src", "fetch", "response", "_b", "sent", "ok", "blob", "objectURL", "URL", "createObjectURL", "currentSrc", "render", "h", "Host", "key", "class", "empty_img", "alt", "filter", "concat", "brightness", "draggable", "shape", "type", "name"], "sources": ["src/components/image/image.scss?tag=bds-image&encapsulation=shadow", "src/components/image/image.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n\n  .img-feedback {\n    height: 76%;\n  }\n}\n\n:host(.empty_img) {\n  background-color: $color-surface-3;\n}\n", "import { Element, Component, Prop, Method, State, h, Host } from '@stencil/core';\n\nexport type ObjectFitValue = 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';\n\n@Component({\n  tag: 'bds-image',\n  styleUrl: 'image.scss',\n  shadow: true,\n})\nexport class Image {\n  private imageHasLoading: boolean = false;\n\n  @Element() element: HTMLElement;\n  /**\n   * URL of the main image.\n   */\n  @Prop({ reflect: true, mutable: true }) src?: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Width of the image.\n   */\n  @Prop() width?: string;\n\n  /**\n   * Height of the image.\n   */\n  @Prop() height?: string;\n\n  /**\n   * Specifies the object-fit style for the image. Can be: 'fill', 'contain', 'cover', 'none', 'scale-down'.\n   */\n  @Prop() objectFit?: ObjectFitValue = 'cover';\n\n  /**\n   * Brightness of the image.\n   */\n  @Prop() brightness?: number;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Indicates whether the main image has been successfully loaded.\n   */\n  @State() imageLoaded = false;\n\n  /**\n   * Indicates whether there was an error during image loading.\n   */\n  @State() loadError = false;\n\n  /**\n   * The current source URL of the image to be rendered.\n   */\n  @State() currentSrc: string;\n\n  componentDidLoad() {\n    this.element.style.width = this.width ? this.width : 'auto';\n    this.element.style.height = this.height?.length > 0 ? this.height : 'auto';\n  }\n\n  @Method()\n  async loadImage(): Promise<void> {\n    if (this.src) {\n      this.imageHasLoading = true;\n      try {\n        const response = await fetch(this.src);\n        if (response.ok) {\n          const blob = await response.blob();\n          const objectURL = URL.createObjectURL(blob);\n          this.currentSrc = objectURL;\n          this.imageLoaded = true;\n          this.imageHasLoading = false;\n        } else {\n          this.loadError = true;\n        }\n      } catch {\n        this.imageHasLoading = false;\n        this.loadError = true;\n      }\n    }\n  }\n\n  render(): JSX.Element {\n    if (!this.imageLoaded && !this.loadError) {\n      // Se a imagem ainda não foi carregada, chame o método loadImage\n      this.loadImage();\n    }\n    return (\n      <Host class={{ empty_img: !this.imageLoaded }}>\n        {this.imageLoaded ? (\n          <img\n            src={this.currentSrc}\n            alt={this.alt}\n            style={{\n              objectFit: this.objectFit,\n              width: '100%',\n              height: '100%',\n              filter: `brightness(${this.brightness})`,\n            }}\n            data-test={this.dataTest}\n            draggable={false}\n          />\n        ) : this.imageHasLoading ? (\n          <bds-skeleton shape=\"square\" width=\"100%\" height=\"100%\"></bds-skeleton>\n        ) : (\n          <bds-illustration\n            class=\"img-feedback\"\n            type=\"empty-states\"\n            name={this.loadError ? 'broken-image' : 'image-not-found'}\n            alt={this.alt}\n            data-test={this.dataTest}\n          ></bds-illustration>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAW,0P,ICSJC,EAAKC,EAAA,uBALlB,SAAAC,EAAAC,G,UAMUC,KAAeC,gBAAY,MA0B3BD,KAASE,UAAoB,QAU7BF,KAAQG,SAAY,KAKnBH,KAAWI,YAAG,MAKdJ,KAASK,UAAG,KAoEtB,CA7DCP,EAAAQ,UAAAC,iBAAA,W,MACEP,KAAKQ,QAAQC,MAAMC,MAAQV,KAAKU,MAAQV,KAAKU,MAAQ,OACrDV,KAAKQ,QAAQC,MAAME,SAASC,EAAAZ,KAAKW,UAAM,MAAAC,SAAA,SAAAA,EAAEC,QAAS,EAAIb,KAAKW,OAAS,M,EAIhEb,EAAAQ,UAAAQ,UAAN,W,4HACMd,KAAKe,IAAL,YACFf,KAAKC,gBAAkB,K,uCAEJ,SAAMe,MAAMhB,KAAKe,M,OAA5BE,EAAWC,EAAAC,O,IACbF,EAASG,GAAT,YACW,SAAMH,EAASI,Q,OAAtBA,EAAOH,EAAAC,OACPG,EAAYC,IAAIC,gBAAgBH,GACtCrB,KAAKyB,WAAaH,EAClBtB,KAAKI,YAAc,KACnBJ,KAAKC,gBAAkB,M,mBAEvBD,KAAKK,UAAY,K,+CAGnBL,KAAKC,gBAAkB,MACvBD,KAAKK,UAAY,K,qCAKvBP,EAAAQ,UAAAoB,OAAA,WACE,IAAK1B,KAAKI,cAAgBJ,KAAKK,UAAW,CAExCL,KAAKc,W,CAEP,OACEa,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAO,CAAEC,WAAY/B,KAAKI,cAC7BJ,KAAKI,YACJuB,EAAA,OACEZ,IAAKf,KAAKyB,WACVO,IAAKhC,KAAKgC,IACVvB,MAAO,CACLP,UAAWF,KAAKE,UAChBQ,MAAO,OACPC,OAAQ,OACRsB,OAAQ,cAAAC,OAAclC,KAAKmC,WAAU,MACtC,YACUnC,KAAKG,SAChBiC,UAAW,QAEXpC,KAAKC,gBACP0B,EAAc,gBAAAU,MAAM,SAAS3B,MAAM,OAAOC,OAAO,SAEjDgB,EAAA,oBACEG,MAAM,eACNQ,KAAK,eACLC,KAAMvC,KAAKK,UAAY,eAAiB,kBACxC2B,IAAKhC,KAAKgC,IACC,YAAAhC,KAAKG,W,4HA7GV,I", "ignoreList": []}