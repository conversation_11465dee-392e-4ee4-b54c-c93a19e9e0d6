{"version": 3, "file": "p-B47mPBRA.system.js", "sources": ["@stencil/core/internal/app-data", "@stencil/core/internal/app-globals", "node_modules/@stencil/core/internal/client/index.js?app-data=conditional"], "sourcesContent": ["export const NAMESPACE = 'blip-ds';\nexport const BUILD = /* blip-ds */ { allRenderFn: true, appendChildSlotFix: true, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: false, constructableCSS: true, cssAnnotations: true, devTools: false, element: false, event: true, experimentalScopedSlotChanges: false, experimentalSlotFixes: false, formAssociated: false, hasRenderFn: true, hostListener: true, hostListenerTarget: true, hostListenerTargetBody: true, hostListenerTargetDocument: false, hostListenerTargetParent: false, hostListenerTargetWindow: true, hotModuleReplacement: false, hydrateClientSide: false, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, hydratedSelectorName: \"hydrated\", initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: true, mode: false, modernPropertyDecls: false, observeAttribute: true, profile: false, prop: true, propBoolean: true, propMutable: true, propNumber: true, propString: true, reflect: true, scoped: true, scopedSlotTextContentFix: false, scriptDataOpts: true, shadowDelegatesFocus: false, shadowDom: true, slot: true, slotChildNodesFix: true, slotRelocation: true, state: true, style: true, svg: false, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: true, watchCallback: true };\nexport const Env = /* blip-ds */ {};\n", "export const globalScripts = () => {};\nexport const globalStyles = \"\";\n", "/*\n Stencil Client Platform v4.35.1 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/es2022-rewire-class-members.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\nvar PrimitiveType = /* @__PURE__ */ ((PrimitiveType2) => {\n  PrimitiveType2[\"Undefined\"] = \"undefined\";\n  PrimitiveType2[\"Null\"] = \"null\";\n  PrimitiveType2[\"String\"] = \"string\";\n  PrimitiveType2[\"Number\"] = \"number\";\n  PrimitiveType2[\"SpecialNumber\"] = \"number\";\n  PrimitiveType2[\"Boolean\"] = \"boolean\";\n  PrimitiveType2[\"BigInt\"] = \"bigint\";\n  return PrimitiveType2;\n})(PrimitiveType || {});\nvar NonPrimitiveType = /* @__PURE__ */ ((NonPrimitiveType2) => {\n  NonPrimitiveType2[\"Array\"] = \"array\";\n  NonPrimitiveType2[\"Date\"] = \"date\";\n  NonPrimitiveType2[\"Map\"] = \"map\";\n  NonPrimitiveType2[\"Object\"] = \"object\";\n  NonPrimitiveType2[\"RegularExpression\"] = \"regexp\";\n  NonPrimitiveType2[\"Set\"] = \"set\";\n  NonPrimitiveType2[\"Channel\"] = \"channel\";\n  NonPrimitiveType2[\"Symbol\"] = \"symbol\";\n  return NonPrimitiveType2;\n})(NonPrimitiveType || {});\nvar TYPE_CONSTANT = \"type\";\nvar VALUE_CONSTANT = \"value\";\nvar SERIALIZED_PREFIX = \"serialized:\";\n\n// src/utils/es2022-rewire-class-members.ts\nvar reWireGetterSetter = (instance, hostRef) => {\n  var _a;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n  members.map(([memberName, [memberFlags]]) => {\n    if ((BUILD2.state || BUILD2.prop) && (memberFlags & 31 /* Prop */ || memberFlags & 32 /* State */)) {\n      const ogValue = instance[memberName];\n      const ogDescriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(instance), memberName);\n      Object.defineProperty(instance, memberName, {\n        get() {\n          return ogDescriptor.get.call(this);\n        },\n        set(newValue) {\n          ogDescriptor.set.call(this, newValue);\n        },\n        configurable: true,\n        enumerable: true\n      });\n      instance[memberName] = hostRef.$instanceValues$.has(memberName) ? hostRef.$instanceValues$.get(memberName) : ogValue;\n    }\n  });\n};\n\n// src/client/client-host-ref.ts\nvar getHostRef = (ref) => {\n  if (ref.__stencil__getHostRef) {\n    return ref.__stencil__getHostRef();\n  }\n  return void 0;\n};\nvar registerInstance = (lazyInstance, hostRef) => {\n  lazyInstance.__stencil__getHostRef = () => hostRef;\n  hostRef.$lazyInstance$ = lazyInstance;\n  if (BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(lazyInstance, hostRef);\n  }\n};\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  if (BUILD3.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD3.method && BUILD3.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD3.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  const ref = hostRef;\n  hostElement.__stencil__getHostRef = () => ref;\n  if (!BUILD3.lazyLoad && BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(hostElement, hostRef);\n  }\n  return ref;\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD4.isTesting ? [\"STENCIL:\"] : [\n  \"%cstencil\",\n  \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"\n];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = (handler) => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD5.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(\n      `Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`\n    );\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD5.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${BUILD5.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`\n  ).then(\n    (importedModule) => {\n      if (!BUILD5.hotModuleReplacement) {\n        cmpModules.set(bundleId, importedModule);\n      }\n      return importedModule[exportName];\n    },\n    (e) => {\n      consoleError(e, hostRef.$hostElement$);\n    }\n  );\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\nvar setScopedSSR = (_opts) => {\n};\nvar needsScopedSSR = () => false;\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar COMMENT_NODE_ID = \"c\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar STENCIL_DOC_DATA = \"_stencilDocData\";\nvar DEFAULT_DOC_DATA = {\n  hostIds: 0,\n  rootLevelIds: 0,\n  staticComponents: /* @__PURE__ */ new Set()\n};\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\n  \"formAssociatedCallback\",\n  \"formResetCallback\",\n  \"formDisabledCallback\",\n  \"formStateRestoreCallback\"\n];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = (helpers) => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD6.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  var _a;\n  let supportsListenerOptions2 = false;\n  try {\n    (_a = win.document) == null ? void 0 : _a.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD6.constructableCSS ? /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD7.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD7.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD30, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = (path) => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD27 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null && v !== void 0;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = (text) => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/remote-value.ts\nvar RemoteValue = class _RemoteValue {\n  /**\n   * Deserializes a LocalValue serialized object back to its original JavaScript representation\n   *\n   * @param serialized The serialized LocalValue object\n   * @returns The original JavaScript value/object\n   */\n  static fromLocalValue(serialized) {\n    const type = serialized[TYPE_CONSTANT];\n    const value = VALUE_CONSTANT in serialized ? serialized[VALUE_CONSTANT] : void 0;\n    switch (type) {\n      case \"string\" /* String */:\n        return value;\n      case \"boolean\" /* Boolean */:\n        return value;\n      case \"bigint\" /* BigInt */:\n        return BigInt(value);\n      case \"undefined\" /* Undefined */:\n        return void 0;\n      case \"null\" /* Null */:\n        return null;\n      case \"number\" /* Number */:\n        if (value === \"NaN\") return NaN;\n        if (value === \"-0\") return -0;\n        if (value === \"Infinity\") return Infinity;\n        if (value === \"-Infinity\") return -Infinity;\n        return value;\n      case \"array\" /* Array */:\n        return value.map((item) => _RemoteValue.fromLocalValue(item));\n      case \"date\" /* Date */:\n        return new Date(value);\n      case \"map\" /* Map */:\n        const map2 = /* @__PURE__ */ new Map();\n        for (const [key, val] of value) {\n          const deserializedKey = typeof key === \"object\" && key !== null ? _RemoteValue.fromLocalValue(key) : key;\n          const deserializedValue = _RemoteValue.fromLocalValue(val);\n          map2.set(deserializedKey, deserializedValue);\n        }\n        return map2;\n      case \"object\" /* Object */:\n        const obj = {};\n        for (const [key, val] of value) {\n          obj[key] = _RemoteValue.fromLocalValue(val);\n        }\n        return obj;\n      case \"regexp\" /* RegularExpression */:\n        const { pattern, flags } = value;\n        return new RegExp(pattern, flags);\n      case \"set\" /* Set */:\n        const set = /* @__PURE__ */ new Set();\n        for (const item of value) {\n          set.add(_RemoteValue.fromLocalValue(item));\n        }\n        return set;\n      case \"symbol\" /* Symbol */:\n        return Symbol(value);\n      default:\n        throw new Error(`Unsupported type: ${type}`);\n    }\n  }\n  /**\n   * Utility method to deserialize multiple LocalValues at once\n   *\n   * @param serializedValues Array of serialized LocalValue objects\n   * @returns Array of deserialized JavaScript values\n   */\n  static fromLocalValueArray(serializedValues) {\n    return serializedValues.map((value) => _RemoteValue.fromLocalValue(value));\n  }\n  /**\n   * Verifies if the given object matches the structure of a serialized LocalValue\n   *\n   * @param obj Object to verify\n   * @returns boolean indicating if the object has LocalValue structure\n   */\n  static isLocalValueObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n      return false;\n    }\n    if (!obj.hasOwnProperty(TYPE_CONSTANT)) {\n      return false;\n    }\n    const type = obj[TYPE_CONSTANT];\n    const hasTypeProperty = Object.values({ ...PrimitiveType, ...NonPrimitiveType }).includes(type);\n    if (!hasTypeProperty) {\n      return false;\n    }\n    if (type !== \"null\" /* Null */ && type !== \"undefined\" /* Undefined */) {\n      return obj.hasOwnProperty(VALUE_CONSTANT);\n    }\n    return true;\n  }\n};\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/utils/serialize.ts\nfunction deserializeProperty(value) {\n  if (typeof value !== \"string\" || !value.startsWith(SERIALIZED_PREFIX)) {\n    return value;\n  }\n  return RemoteValue.fromLocalValue(JSON.parse(atob(value.slice(SERIALIZED_PREFIX.length))));\n}\n\n// src/utils/shadow-root.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nimport { globalStyles } from \"@stencil/core/internal/app-globals\";\nfunction createShadowRoot(cmpMeta) {\n  const shadowRoot = BUILD8.shadowDelegatesFocus ? this.attachShadow({\n    mode: \"open\",\n    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n  }) : this.attachShadow({ mode: \"open\" });\n  if (supportsConstructableStylesheets) {\n    const sheet = new CSSStyleSheet();\n    sheet.replaceSync(globalStyles);\n    shadowRoot.adoptedStyleSheets.push(sheet);\n  }\n}\n\n// src/utils/util.ts\nvar lowerPathParam = (fn) => (p) => fn(p.toLowerCase());\nvar isDtsFile = lowerPathParam((p) => p.endsWith(\".d.ts\") || p.endsWith(\".d.mts\") || p.endsWith(\".d.cts\"));\nvar isTsFile = lowerPathParam(\n  (p) => !isDtsFile(p) && (p.endsWith(\".ts\") || p.endsWith(\".mts\") || p.endsWith(\".cts\"))\n);\nvar isTsxFile = lowerPathParam(\n  (p) => p.endsWith(\".tsx\") || p.endsWith(\".mtsx\") || p.endsWith(\".ctsx\")\n);\nvar isJsxFile = lowerPathParam(\n  (p) => p.endsWith(\".jsx\") || p.endsWith(\".mjsx\") || p.endsWith(\".cjsx\")\n);\nvar isJsFile = lowerPathParam((p) => p.endsWith(\".js\") || p.endsWith(\".mjs\") || p.endsWith(\".cjs\"));\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/slot-polyfill-utils.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = internalCall(elm, \"childNodes\");\n  if (elm.tagName && elm.tagName.includes(\"-\") && elm[\"s-cr\"] && elm.tagName !== \"SLOT-FB\") {\n    getHostSlotNodes(childNodes, elm.tagName).forEach((slotNode) => {\n      if (slotNode.nodeType === 1 /* ElementNode */ && slotNode.tagName === \"SLOT-FB\") {\n        if (getSlotChildSiblings(slotNode, getSlotName(slotNode), false).length) {\n          slotNode.hidden = true;\n        } else {\n          slotNode.hidden = false;\n        }\n      }\n    });\n  }\n  let i2 = 0;\n  for (i2 = 0; i2 < childNodes.length; i2++) {\n    const childNode = childNodes[i2];\n    if (childNode.nodeType === 1 /* ElementNode */ && internalCall(childNode, \"childNodes\").length) {\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar getSlottedChildNodes = (childNodes) => {\n  const result = [];\n  for (let i2 = 0; i2 < childNodes.length; i2++) {\n    const slottedNode = childNodes[i2][\"s-nr\"] || void 0;\n    if (slottedNode && slottedNode.isConnected) {\n      result.push(slottedNode);\n    }\n  }\n  return result;\n};\nfunction getHostSlotNodes(childNodes, hostName, slotName) {\n  let i2 = 0;\n  let slottedNodes = [];\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && (!hostName || childNode[\"s-hn\"] === hostName) && (slotName === void 0 || getSlotName(childNode) === slotName)) {\n      slottedNodes.push(childNode);\n      if (typeof slotName !== \"undefined\") return slottedNodes;\n    }\n    slottedNodes = [...slottedNodes, ...getHostSlotNodes(childNode.childNodes, hostName, slotName)];\n  }\n  return slottedNodes;\n}\nvar getSlotChildSiblings = (slot, slotName, includeSlot = true) => {\n  const childNodes = [];\n  if (includeSlot && slot[\"s-sr\"] || !slot[\"s-sr\"]) childNodes.push(slot);\n  let node = slot;\n  while (node = node.nextSibling) {\n    if (getSlotName(node) === slotName && (includeSlot || !node[\"s-sr\"])) childNodes.push(node);\n  }\n  return childNodes;\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar addSlotRelocateNode = (newChild, slotNode, prepend, position) => {\n  if (newChild[\"s-ol\"] && newChild[\"s-ol\"].isConnected) {\n    return;\n  }\n  const slottedNodeLocation = document.createTextNode(\"\");\n  slottedNodeLocation[\"s-nr\"] = newChild;\n  if (!slotNode[\"s-cr\"] || !slotNode[\"s-cr\"].parentNode) return;\n  const parent = slotNode[\"s-cr\"].parentNode;\n  const appendMethod = prepend ? internalCall(parent, \"prepend\") : internalCall(parent, \"appendChild\");\n  if (BUILD9.hydrateClientSide && typeof position !== \"undefined\") {\n    slottedNodeLocation[\"s-oo\"] = position;\n    const childNodes = internalCall(parent, \"childNodes\");\n    const slotRelocateNodes = [slottedNodeLocation];\n    childNodes.forEach((n) => {\n      if (n[\"s-nr\"]) slotRelocateNodes.push(n);\n    });\n    slotRelocateNodes.sort((a, b) => {\n      if (!a[\"s-oo\"] || a[\"s-oo\"] < (b[\"s-oo\"] || 0)) return -1;\n      else if (!b[\"s-oo\"] || b[\"s-oo\"] < a[\"s-oo\"]) return 1;\n      return 0;\n    });\n    slotRelocateNodes.forEach((n) => appendMethod.call(parent, n));\n  } else {\n    appendMethod.call(parent, slottedNodeLocation);\n  }\n  newChild[\"s-ol\"] = slottedNodeLocation;\n  newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n};\nvar getSlotName = (node) => typeof node[\"s-sn\"] === \"string\" ? node[\"s-sn\"] : node.nodeType === 1 && node.getAttribute(\"slot\") || void 0;\nfunction patchSlotNode(node) {\n  if (node.assignedElements || node.assignedNodes || !node[\"s-sr\"]) return;\n  const assignedFactory = (elementsOnly) => (function(opts) {\n    const toReturn = [];\n    const slotName = this[\"s-sn\"];\n    if (opts == null ? void 0 : opts.flatten) {\n      console.error(`\n          Flattening is not supported for Stencil non-shadow slots.\n          You can use \\`.childNodes\\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `);\n    }\n    const parent = this[\"s-cr\"].parentElement;\n    const slottedNodes = parent.__childNodes ? parent.childNodes : getSlottedChildNodes(parent.childNodes);\n    slottedNodes.forEach((n) => {\n      if (slotName === getSlotName(n)) {\n        toReturn.push(n);\n      }\n    });\n    if (elementsOnly) {\n      return toReturn.filter((n) => n.nodeType === 1 /* ElementNode */);\n    }\n    return toReturn;\n  }).bind(node);\n  node.assignedElements = assignedFactory(true);\n  node.assignedNodes = assignedFactory(false);\n}\nfunction dispatchSlotChangeEvent(elm) {\n  elm.dispatchEvent(new CustomEvent(\"slotchange\", { bubbles: false, cancelable: false, composed: false }));\n}\nfunction findSlotFromSlottedNode(slottedNode, parentHost) {\n  var _a;\n  parentHost = parentHost || ((_a = slottedNode[\"s-ol\"]) == null ? void 0 : _a.parentElement);\n  if (!parentHost) return { slotNode: null, slotName: \"\" };\n  const slotName = slottedNode[\"s-sn\"] = getSlotName(slottedNode) || \"\";\n  const childNodes = internalCall(parentHost, \"childNodes\");\n  const slotNode = getHostSlotNodes(childNodes, parentHost.tagName, slotName)[0];\n  return { slotNode, slotName };\n}\n\n// src/runtime/dom-extras.ts\nvar patchPseudoShadowDom = (hostElementPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchInsertBefore(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD10.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD10.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      const childNodes = this.__childNodes || this.childNodes;\n      for (; i2 < childNodes.length; i2++) {\n        slotted = childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD10.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    if (slotNode) {\n      addSlotRelocateNode(newChild, slotNode);\n      const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const parent = internalCall(appendAfter, \"parentNode\");\n      const insertedNode = internalCall(parent, \"insertBefore\")(newChild, appendAfter.nextSibling);\n      dispatchSlotChangeEvent(slotNode);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const childNodes = this.__childNodes || this.childNodes;\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, toRemove[\"s-sn\"]);\n      if (slotNode && toRemove.isConnected) {\n        toRemove.remove();\n        updateFallbackSlotVisibility(this);\n        return;\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  HostElementPrototype.__prepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = (newChild[\"s-sn\"] = getSlotName(newChild)) || \"\";\n      const childNodes = internalCall(this, \"childNodes\");\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, slotName)[0];\n      if (slotNode) {\n        addSlotRelocateNode(newChild, slotNode, true);\n        const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        const parent = internalCall(appendAfter, \"parentNode\");\n        const toReturn = internalCall(parent, \"insertBefore\")(newChild, internalCall(appendAfter, \"nextSibling\"));\n        dispatchSlotChangeEvent(slotNode);\n        return toReturn;\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return HostElementPrototype.__prepend(newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.__append = HostElementPrototype.append;\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchInsertBefore = (HostElementPrototype) => {\n  const eleProto = HostElementPrototype;\n  if (eleProto.__insertBefore) return;\n  eleProto.__insertBefore = HostElementPrototype.insertBefore;\n  HostElementPrototype.insertBefore = function(newChild, currentChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    const slottedNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n    if (slotNode) {\n      let found = false;\n      slottedNodes.forEach((childNode) => {\n        if (childNode === currentChild || currentChild === null) {\n          found = true;\n          if (currentChild === null || slotName !== currentChild[\"s-sn\"]) {\n            this.appendChild(newChild);\n            return;\n          }\n          if (slotName === currentChild[\"s-sn\"]) {\n            addSlotRelocateNode(newChild, slotNode);\n            const parent = internalCall(currentChild, \"parentNode\");\n            internalCall(parent, \"insertBefore\")(newChild, currentChild);\n            dispatchSlotChangeEvent(slotNode);\n          }\n          return;\n        }\n      });\n      if (found) return newChild;\n    }\n    const parentNode = currentChild == null ? void 0 : currentChild.__parentNode;\n    if (parentNode && !this.isSameNode(parentNode)) {\n      return this.appendChild(newChild);\n    }\n    return this.__insertBefore(newChild, currentChild);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  patchHostOriginalAccessor(\"textContent\", hostElementPrototype);\n  Object.defineProperty(hostElementPrototype, \"textContent\", {\n    get: function() {\n      let text = \"\";\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => text += node.textContent || \"\");\n      return text;\n    },\n    set: function(value) {\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => {\n        if (node[\"s-ol\"]) node[\"s-ol\"].remove();\n        node.remove();\n      });\n      this.insertAdjacentHTML(\"beforeend\", value);\n    }\n  });\n};\nvar patchChildSlotNodes = (elm) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  patchHostOriginalAccessor(\"children\", elm);\n  Object.defineProperty(elm, \"children\", {\n    get() {\n      return this.childNodes.filter((n) => n.nodeType === 1);\n    }\n  });\n  Object.defineProperty(elm, \"childElementCount\", {\n    get() {\n      return this.children.length;\n    }\n  });\n  patchHostOriginalAccessor(\"firstChild\", elm);\n  Object.defineProperty(elm, \"firstChild\", {\n    get() {\n      return this.childNodes[0];\n    }\n  });\n  patchHostOriginalAccessor(\"lastChild\", elm);\n  Object.defineProperty(elm, \"lastChild\", {\n    get() {\n      return this.childNodes[this.childNodes.length - 1];\n    }\n  });\n  patchHostOriginalAccessor(\"childNodes\", elm);\n  Object.defineProperty(elm, \"childNodes\", {\n    get() {\n      const result = new FakeNodeList();\n      result.push(...getSlottedChildNodes(this.__childNodes));\n      return result;\n    }\n  });\n};\nvar patchSlottedNode = (node) => {\n  if (!node || node.__nextSibling !== void 0 || !globalThis.Node) return;\n  patchNextSibling(node);\n  patchPreviousSibling(node);\n  patchParentNode(node);\n  if (node.nodeType === Node.ELEMENT_NODE) {\n    patchNextElementSibling(node);\n    patchPreviousElementSibling(node);\n  }\n};\nvar patchNextSibling = (node) => {\n  if (!node || node.__nextSibling) return;\n  patchHostOriginalAccessor(\"nextSibling\", node);\n  Object.defineProperty(node, \"nextSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index + 1];\n      }\n      return this.__nextSibling;\n    }\n  });\n};\nvar patchNextElementSibling = (element) => {\n  if (!element || element.__nextElementSibling) return;\n  patchHostOriginalAccessor(\"nextElementSibling\", element);\n  Object.defineProperty(element, \"nextElementSibling\", {\n    get: function() {\n      var _a;\n      const parentEles = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentEles == null ? void 0 : parentEles.indexOf(this);\n      if (parentEles && index > -1) {\n        return parentEles[index + 1];\n      }\n      return this.__nextElementSibling;\n    }\n  });\n};\nvar patchPreviousSibling = (node) => {\n  if (!node || node.__previousSibling) return;\n  patchHostOriginalAccessor(\"previousSibling\", node);\n  Object.defineProperty(node, \"previousSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousSibling;\n    }\n  });\n};\nvar patchPreviousElementSibling = (element) => {\n  if (!element || element.__previousElementSibling) return;\n  patchHostOriginalAccessor(\"previousElementSibling\", element);\n  Object.defineProperty(element, \"previousElementSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousElementSibling;\n    }\n  });\n};\nvar patchParentNode = (node) => {\n  if (!node || node.__parentNode) return;\n  patchHostOriginalAccessor(\"parentNode\", node);\n  Object.defineProperty(node, \"parentNode\", {\n    get: function() {\n      var _a;\n      return ((_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode) || this.__parentNode;\n    },\n    set: function(value) {\n      this.__parentNode = value;\n    }\n  });\n};\nvar validElementPatches = [\"children\", \"nextElementSibling\", \"previousElementSibling\"];\nvar validNodesPatches = [\n  \"childNodes\",\n  \"firstChild\",\n  \"lastChild\",\n  \"nextSibling\",\n  \"previousSibling\",\n  \"textContent\",\n  \"parentNode\"\n];\nfunction patchHostOriginalAccessor(accessorName, node) {\n  let accessor;\n  if (validElementPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Element.prototype, accessorName);\n  } else if (validNodesPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Node.prototype, accessorName);\n  }\n  if (!accessor) {\n    accessor = Object.getOwnPropertyDescriptor(node, accessorName);\n  }\n  if (accessor) Object.defineProperty(node, \"__\" + accessorName, accessor);\n}\nfunction internalCall(node, method) {\n  if (\"__\" + method in node) {\n    const toReturn = node[\"__\" + method];\n    if (typeof toReturn !== \"function\") return toReturn;\n    return toReturn.bind(node);\n  } else {\n    if (typeof node[method] !== \"function\") return node[method];\n    return node[method].bind(node);\n  }\n}\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD11.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD11.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = (ref) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD11.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = (ref) => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD12.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD12.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD12.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD12.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD12.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD12.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD12.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD12.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = (inputElm) => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  var _a;\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const slottedNodes = [];\n  const shadowRootNodes = BUILD13.shadowDom && shadowRoot ? [] : null;\n  const vnode = newVNode(tagName, null);\n  vnode.$elm$ = hostElm;\n  const members = Object.entries(((_a = hostRef.$cmpMeta$) == null ? void 0 : _a.$members$) || {});\n  members.forEach(([memberName, [memberFlags, metaAttributeName]]) => {\n    var _a2, _b;\n    if (!(memberFlags & 31 /* Prop */)) {\n      return;\n    }\n    const attributeName = metaAttributeName || memberName;\n    const attrVal = hostElm.getAttribute(attributeName);\n    if (attrVal !== null) {\n      const attrPropVal = parsePropertyValue(\n        attrVal,\n        memberFlags,\n        BUILD13.formAssociated && !!(((_a2 = hostRef.$cmpMeta$) == null ? void 0 : _a2.$flags$) & 64 /* formAssociated */)\n      );\n      (_b = hostRef == null ? void 0 : hostRef.$instanceValues$) == null ? void 0 : _b.set(memberName, attrPropVal);\n    }\n  });\n  let scopeId2;\n  if (BUILD13.scoped) {\n    const cmpMeta = hostRef.$cmpMeta$;\n    if (cmpMeta && cmpMeta.$flags$ & 10 /* needsScopedEncapsulation */ && hostElm[\"s-sc\"]) {\n      scopeId2 = hostElm[\"s-sc\"];\n      hostElm.classList.add(scopeId2 + \"-h\");\n    } else if (hostElm[\"s-sc\"]) {\n      delete hostElm[\"s-sc\"];\n    }\n  }\n  if (win.document && (!plt.$orgLocNodes$ || !plt.$orgLocNodes$.size)) {\n    initializeDocumentHydrate(win.document.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  hostRef.$vnode$ = clientHydrate(\n    vnode,\n    childRenderNodes,\n    slotNodes,\n    shadowRootNodes,\n    hostElm,\n    hostElm,\n    hostId,\n    slottedNodes\n  );\n  let crIndex = 0;\n  const crLength = childRenderNodes.length;\n  let childRenderNode;\n  for (crIndex; crIndex < crLength; crIndex++) {\n    childRenderNode = childRenderNodes[crIndex];\n    const orgLocationId = childRenderNode.$hostId$ + \".\" + childRenderNode.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = childRenderNode.$elm$;\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName.toUpperCase();\n      if (childRenderNode.$tag$ === \"slot\") {\n        node[\"s-cr\"] = hostElm[\"s-cr\"];\n      }\n    }\n    if (childRenderNode.$tag$ === \"slot\") {\n      childRenderNode.$name$ = childRenderNode.$elm$[\"s-sn\"] || childRenderNode.$elm$[\"name\"] || null;\n      if (childRenderNode.$children$) {\n        childRenderNode.$flags$ |= 2 /* isSlotFallback */;\n        if (!childRenderNode.$elm$.childNodes.length) {\n          childRenderNode.$children$.forEach((c) => {\n            childRenderNode.$elm$.appendChild(c.$elm$);\n          });\n        }\n      } else {\n        childRenderNode.$flags$ |= 1 /* isSlotReference */;\n      }\n    }\n    if (orgLocationNode && orgLocationNode.isConnected) {\n      if (shadowRoot && orgLocationNode[\"s-en\"] === \"\") {\n        orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n      }\n      orgLocationNode.parentNode.removeChild(orgLocationNode);\n      if (!shadowRoot) {\n        node[\"s-oo\"] = parseInt(childRenderNode.$nodeId$);\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  }\n  const hosts = [];\n  const snLen = slottedNodes.length;\n  let snIndex = 0;\n  let slotGroup;\n  let snGroupIdx;\n  let snGroupLen;\n  let slottedItem;\n  for (snIndex; snIndex < snLen; snIndex++) {\n    slotGroup = slottedNodes[snIndex];\n    if (!slotGroup || !slotGroup.length) continue;\n    snGroupLen = slotGroup.length;\n    snGroupIdx = 0;\n    for (snGroupIdx; snGroupIdx < snGroupLen; snGroupIdx++) {\n      slottedItem = slotGroup[snGroupIdx];\n      if (!hosts[slottedItem.hostId]) {\n        hosts[slottedItem.hostId] = plt.$orgLocNodes$.get(slottedItem.hostId);\n      }\n      if (!hosts[slottedItem.hostId]) continue;\n      const hostEle = hosts[slottedItem.hostId];\n      if (!hostEle.shadowRoot || !shadowRoot) {\n        slottedItem.slot[\"s-cr\"] = hostEle[\"s-cr\"];\n        if (!slottedItem.slot[\"s-cr\"] && hostEle.shadowRoot) {\n          slottedItem.slot[\"s-cr\"] = hostEle;\n        } else {\n          slottedItem.slot[\"s-cr\"] = (hostEle.__childNodes || hostEle.childNodes)[0];\n        }\n        addSlotRelocateNode(slottedItem.node, slottedItem.slot, false, slottedItem.node[\"s-oo\"]);\n        if (BUILD13.experimentalSlotFixes) {\n          patchSlottedNode(slottedItem.node);\n        }\n      }\n      if (hostEle.shadowRoot && slottedItem.node.parentElement !== hostEle) {\n        hostEle.appendChild(slottedItem.node);\n      }\n    }\n  }\n  if (BUILD13.scoped && scopeId2 && slotNodes.length) {\n    slotNodes.forEach((slot) => {\n      slot.$elm$.parentElement.classList.add(scopeId2 + \"-s\");\n    });\n  }\n  if (BUILD13.shadowDom && shadowRoot && !shadowRoot.childNodes.length) {\n    let rnIdex = 0;\n    const rnLen = shadowRootNodes.length;\n    if (rnLen) {\n      for (rnIdex; rnIdex < rnLen; rnIdex++) {\n        shadowRoot.appendChild(shadowRootNodes[rnIdex]);\n      }\n      Array.from(hostElm.childNodes).forEach((node) => {\n        if (typeof node[\"s-en\"] !== \"string\" && typeof node[\"s-sn\"] !== \"string\") {\n          if (node.nodeType === 1 /* ElementNode */ && node.slot && node.hidden) {\n            node.removeAttribute(\"hidden\");\n          } else if (node.nodeType === 8 /* CommentNode */ || node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n            node.parentNode.removeChild(node);\n          }\n        }\n      });\n    }\n  }\n  plt.$orgLocNodes$.delete(hostElm[\"s-id\"]);\n  hostRef.$hostElement$ = hostElm;\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId, slottedNodes = []) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  const scopeId2 = hostElm[\"s-sc\"];\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = createSimpleVNode({\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          // If we don't add the initial classes to the VNode, the first `vdom-render.ts` patch\n          // won't try to reconcile them. Classes set on the node will be blown away.\n          $attrs$: { class: node.className || \"\" }\n        });\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        if (BUILD13.scoped && scopeId2) {\n          node[\"s-si\"] = scopeId2;\n          childVNode.$attrs$.class += \" \" + scopeId2;\n        }\n        const slotName = childVNode.$elm$.getAttribute(\"s-sn\");\n        if (typeof slotName === \"string\") {\n          if (childVNode.$tag$ === \"slot-fb\") {\n            addSlot(\n              slotName,\n              childIdSplt[2],\n              childVNode,\n              node,\n              parentVNode,\n              childRenderNodes,\n              slotNodes,\n              shadowRootNodes,\n              slottedNodes\n            );\n            if (BUILD13.scoped && scopeId2) {\n              node.classList.add(scopeId2);\n            }\n          }\n          childVNode.$elm$[\"s-sn\"] = slotName;\n          childVNode.$elm$.removeAttribute(\"s-sn\");\n        }\n        if (childVNode.$index$ !== void 0) {\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        }\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId,\n          slottedNodes\n        );\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = nonShadowNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        nonShadowNodes[i2],\n        hostId,\n        slottedNodes\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = createSimpleVNode({\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4] || \"0\",\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      });\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 3 /* TextNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (hostId === childVNode.$hostId$) {\n            if (!parentVNode.$children$) {\n              parentVNode.$children$ = [];\n            }\n            parentVNode.$children$[childVNode.$index$] = childVNode;\n          }\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childNodeType === COMMENT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 8 /* CommentNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 8 /* CommentNode */) {\n          childRenderNodes.push(childVNode);\n          node.remove();\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          const slotName = node[\"s-sn\"] = childIdSplt[5] || \"\";\n          addSlot(\n            slotName,\n            childIdSplt[2],\n            childVNode,\n            node,\n            parentVNode,\n            childRenderNodes,\n            slotNodes,\n            shadowRootNodes,\n            slottedNodes\n          );\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD13.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD13.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  } else {\n    if (node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n      node.remove();\n    }\n  }\n  return parentVNode;\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    const componentId = node[HYDRATE_ID] || node.getAttribute(HYDRATE_ID);\n    if (componentId) {\n      orgLocNodes.set(componentId, node);\n    }\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = 0; i2 < nonShadowNodes.length; i2++) {\n      initializeDocumentHydrate(nonShadowNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\nvar createSimpleVNode = (vnode) => {\n  const defaultVNode = {\n    $flags$: 0,\n    $hostId$: null,\n    $nodeId$: null,\n    $depth$: null,\n    $index$: \"0\",\n    $elm$: null,\n    $attrs$: null,\n    $children$: null,\n    $key$: null,\n    $name$: null,\n    $tag$: null,\n    $text$: null\n  };\n  return { ...defaultVNode, ...vnode };\n};\nfunction addSlot(slotName, slotId, childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes) {\n  node[\"s-sr\"] = true;\n  childVNode.$name$ = slotName || null;\n  childVNode.$tag$ = \"slot\";\n  const parentNodeId = (parentVNode == null ? void 0 : parentVNode.$elm$) ? parentVNode.$elm$[\"s-id\"] || parentVNode.$elm$.getAttribute(\"s-id\") : \"\";\n  if (BUILD13.shadowDom && shadowRootNodes && win.document) {\n    const slot = childVNode.$elm$ = win.document.createElement(childVNode.$tag$);\n    if (childVNode.$name$) {\n      childVNode.$elm$.setAttribute(\"name\", slotName);\n    }\n    if (parentNodeId && parentNodeId !== childVNode.$hostId$) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    } else {\n      node.parentNode.insertBefore(slot, node);\n    }\n    addSlottedNodes(slottedNodes, slotId, slotName, node, childVNode.$hostId$);\n    node.remove();\n    if (childVNode.$depth$ === \"0\") {\n      shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n    }\n  } else {\n    const slot = childVNode.$elm$;\n    const shouldMove = parentNodeId && parentNodeId !== childVNode.$hostId$ && parentVNode.$elm$.shadowRoot;\n    addSlottedNodes(slottedNodes, slotId, slotName, node, shouldMove ? parentNodeId : childVNode.$hostId$);\n    patchSlotNode(node);\n    if (shouldMove) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    }\n  }\n  childRenderNodes.push(childVNode);\n  slotNodes.push(childVNode);\n  if (!parentVNode.$children$) {\n    parentVNode.$children$ = [];\n  }\n  parentVNode.$children$[childVNode.$index$] = childVNode;\n}\nvar addSlottedNodes = (slottedNodes, slotNodeId, slotName, slotNode, hostId) => {\n  let slottedNode = slotNode.nextSibling;\n  slottedNodes[slotNodeId] = slottedNodes[slotNodeId] || [];\n  while (slottedNode && ((slottedNode[\"getAttribute\"] && slottedNode.getAttribute(\"slot\") || slottedNode[\"s-sn\"]) === slotName || slotName === \"\" && !slottedNode[\"s-sn\"] && (slottedNode.nodeType === 8 /* CommentNode */ && slottedNode.nodeValue.indexOf(\".\") !== 1 || slottedNode.nodeType === 3 /* TextNode */))) {\n    slottedNode[\"s-sn\"] = slotName;\n    slottedNodes[slotNodeId].push({ slot: slotNode, node: slottedNode, hostId });\n    slottedNode = slottedNode.nextSibling;\n  }\n};\nvar findCorrespondingNode = (node, type) => {\n  let sibling = node;\n  do {\n    sibling = sibling.nextSibling;\n  } while (sibling && (sibling.nodeType !== type || !sibling.nodeValue));\n  return sibling;\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = (selector) => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[\\s*part~=\\s*(\"[^\"]*\"|'[^']*')\\s*\\])/g, (_, keep) => {\n    const replaceBy = `__part-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  content = content.replace(/__part-(\\d+)__/g, (_, index) => placeholders[+index]);\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _safePartRe = /__part-(\\d+)__/g;\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = (selector) => {\n  const safeSelector2 = escapeRegExpSpecialCharacters(selector);\n  return new RegExp(\n    // First capture group: match any context before the selector that's not inside @supports selector()\n    // Using negative lookahead to avoid matching inside @supports selector(...) condition\n    `(^|[^@]|@(?!supports\\\\s+selector\\\\s*\\\\([^{]*?${safeSelector2}))(${safeSelector2}\\\\b)`,\n    \"g\"\n  );\n};\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = (input) => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = (input) => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = (input) => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = (cssText) => {\n  const supportsBlocks = [];\n  cssText = cssText.replace(/@supports\\s+selector\\s*\\(\\s*([^)]*)\\s*\\)/g, (_, selectorContent) => {\n    const placeholder = `__supports_${supportsBlocks.length}__`;\n    supportsBlocks.push(selectorContent);\n    return `@supports selector(${placeholder})`;\n  });\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  supportsBlocks.forEach((originalSelector, index) => {\n    cssText = cssText.replace(`__supports_${index}__`, originalSelector);\n  });\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i2 = 0; i2 < parts.length; i2++) {\n        const p = parts[i2].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i2 = m[4] - 1; i2 >= 0; i2--) {\n        const char = m[5][i2];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = (cssText) => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = (scopeSelector2) => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = (p) => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))(?=(?:[^()]*\\([^()]*\\))*[^()]*$)\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = !part.match(_safePartRe) && (shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1);\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map((shallowPart) => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector) => {\n  return processRules(cssText, (rule) => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId2) {\n    cssText = scopeSelectors(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map((ref) => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar expandPartSelectors = (cssText) => {\n  const partSelectorRe = /([^\\s,{][^,{]*?)::part\\(\\s*([^)]+?)\\s*\\)((?:[:.][^,{]*)*)/g;\n  return processRules(cssText, (rule) => {\n    if (rule.selector[0] === \"@\") {\n      return rule;\n    }\n    const selectors = rule.selector.split(\",\").map((sel) => {\n      const out = [sel.trim()];\n      let m;\n      while ((m = partSelectorRe.exec(sel)) !== null) {\n        const before = m[1].trimEnd();\n        const partNames = m[2].trim().split(/\\s+/);\n        const after = m[3] || \"\";\n        const partAttr = partNames.flatMap((p) => {\n          if (!rule.selector.includes(`[part~=\"${p}\"]`)) {\n            return [`[part~=\"${p}\"]`];\n          }\n          return [];\n        }).join(\"\");\n        const expanded = `${before} ${partAttr}${after}`;\n        if (!!partAttr && expanded !== sel.trim()) {\n          out.push(expanded);\n        }\n      }\n      return out.join(\", \");\n    });\n    rule.selector = selectors.join(\", \");\n    return rule;\n  });\n};\nvar scopeCss = (cssText, scopeId2, commentOriginalSelector) => {\n  const hostScopeId = scopeId2 + \"-h\";\n  const slotScopeId = scopeId2 + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const orgSelectors = [];\n  if (commentOriginalSelector) {\n    const processCommentedSelector = (rule) => {\n      const placeholder = `/*!@___${orgSelectors.length}___*/`;\n      const comment = `/*!@${rule.selector}*/`;\n      orgSelectors.push({ placeholder, comment });\n      rule.selector = placeholder + rule.selector;\n      return rule;\n    };\n    cssText = processRules(cssText, (rule) => {\n      if (rule.selector[0] !== \"@\") {\n        return processCommentedSelector(rule);\n      } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n        rule.content = processRules(rule.content, processCommentedSelector);\n        return rule;\n      }\n      return rule;\n    });\n  }\n  const scoped = scopeCssText(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  if (commentOriginalSelector) {\n    orgSelectors.forEach(({ placeholder, comment }) => {\n      cssText = cssText.replace(placeholder, comment);\n    });\n  }\n  scoped.slottedSelectors.forEach((slottedSelector) => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  cssText = expandPartSelectors(cssText);\n  return cssText;\n};\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType, isFormAssociated) => {\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && (propValue.startsWith(\"{\") && propValue.endsWith(\"}\") || propValue.startsWith(\"[\") && propValue.endsWith(\"]\"))) {\n    try {\n      propValue = JSON.parse(propValue);\n      return propValue;\n    } catch (e) {\n    }\n  }\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && propValue.startsWith(SERIALIZED_PREFIX)) {\n    propValue = deserializeProperty(propValue);\n    return propValue;\n  }\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD14.propBoolean && propType & 4 /* Boolean */) {\n      if (BUILD14.formAssociated && isFormAssociated && typeof propValue === \"string\") {\n        return propValue === \"\" || !!propValue;\n      } else {\n        return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n      }\n    }\n    if (BUILD14.propNumber && propType & 2 /* Number */) {\n      return typeof propValue === \"string\" ? parseFloat(propValue) : typeof propValue === \"number\" ? propValue : NaN;\n    }\n    if (BUILD14.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD21, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\nvar getElement = (ref) => BUILD15.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      if (BUILD16.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD17 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD17.attachStyles || !win.document) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : win.document;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD17.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = win.document.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD17.hydrateServerSide || BUILD17.hotModuleReplacement) && (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */ || cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */)) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(\n                styleElm,\n                (referenceNode2 == null ? void 0 : referenceNode2.parentNode) === styleContainerNode ? referenceNode2 : null\n              );\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD17.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    BUILD17.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if ((BUILD17.shadowDom || BUILD17.scoped) && BUILD17.cssAnnotations && flags & 10 /* needsScopedEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD17.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar convertScopedToShadow = (css) => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, \"$1{\");\nvar hydrateScopedToShadow = () => {\n  if (!win.document) {\n    return;\n  }\n  const styles2 = win.document.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let i2 = 0;\n  for (; i2 < styles2.length; i2++) {\n    registerStyle(styles2[i2].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles2[i2].innerHTML), true);\n  }\n};\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags, initialRender) => {\n  if (oldValue === newValue) {\n    return;\n  }\n  let isProp = isMemberInElement(elm, memberName);\n  let ln = memberName.toLowerCase();\n  if (BUILD18.vdomClass && memberName === \"class\") {\n    const classList = elm.classList;\n    const oldClasses = parseClassList(oldValue);\n    let newClasses = parseClassList(newValue);\n    if (BUILD18.hydrateClientSide && elm[\"s-si\"] && initialRender) {\n      newClasses.push(elm[\"s-si\"]);\n      oldClasses.forEach((c) => {\n        if (c.startsWith(elm[\"s-si\"])) newClasses.push(c);\n      });\n      newClasses = [...new Set(newClasses)];\n      classList.add(...newClasses);\n    } else {\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    }\n  } else if (BUILD18.vdomStyle && memberName === \"style\") {\n    if (BUILD18.updatable) {\n      for (const prop in oldValue) {\n        if (!newValue || newValue[prop] == null) {\n          if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.removeProperty(prop);\n          } else {\n            elm.style[prop] = \"\";\n          }\n        }\n      }\n    }\n    for (const prop in newValue) {\n      if (!oldValue || newValue[prop] !== oldValue[prop]) {\n        if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n          elm.style.setProperty(prop, newValue[prop]);\n        } else {\n          elm.style[prop] = newValue[prop];\n        }\n      }\n    }\n  } else if (BUILD18.vdomKey && memberName === \"key\") {\n  } else if (BUILD18.vdomRef && memberName === \"ref\") {\n    if (newValue) {\n      newValue(elm);\n    }\n  } else if (BUILD18.vdomListener && (BUILD18.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n    if (memberName[2] === \"-\") {\n      memberName = memberName.slice(3);\n    } else if (isMemberInElement(win, ln)) {\n      memberName = ln.slice(2);\n    } else {\n      memberName = ln[2] + memberName.slice(3);\n    }\n    if (oldValue || newValue) {\n      const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n      memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, capture);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, capture);\n      }\n    }\n  } else if (BUILD18.vdomPropOrAttr) {\n    const isComplex = isComplexType(newValue);\n    if ((isProp || isComplex && newValue !== null) && !isSvg) {\n      try {\n        if (!elm.tagName.includes(\"-\")) {\n          const n = newValue == null ? \"\" : newValue;\n          if (memberName === \"list\") {\n            isProp = false;\n          } else if (oldValue == null || elm[memberName] != n) {\n            if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n              elm[memberName] = n;\n            } else {\n              elm.setAttribute(memberName, n);\n            }\n          }\n        } else if (elm[memberName] !== newValue) {\n          elm[memberName] = newValue;\n        }\n      } catch (e) {\n      }\n    }\n    let xlink = false;\n    if (BUILD18.vdomXlink) {\n      if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n        memberName = ln;\n        xlink = true;\n      }\n    }\n    if (newValue == null || newValue === false) {\n      if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n        if (BUILD18.vdomXlink && xlink) {\n          elm.removeAttributeNS(XLINK_NS, memberName);\n        } else {\n          elm.removeAttribute(memberName);\n        }\n      }\n    } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex && elm.nodeType === 1 /* ElementNode */) {\n      newValue = newValue === true ? \"\" : newValue;\n      if (BUILD18.vdomXlink && xlink) {\n        elm.setAttributeNS(XLINK_NS, memberName, newValue);\n      } else {\n        elm.setAttribute(memberName, newValue);\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => {\n  if (typeof value === \"object\" && value && \"baseVal\" in value) {\n    value = value.baseVal;\n  }\n  if (!value || typeof value !== \"string\") {\n    return [];\n  }\n  return value.split(parseClassListRegex);\n};\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2, isInitialRender) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || {};\n  const newVnodeAttrs = newVnode.$attrs$ || {};\n  if (BUILD19.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(\n          elm,\n          memberName,\n          oldVnodeAttrs[memberName],\n          void 0,\n          isSvgMode2,\n          newVnode.$flags$,\n          isInitialRender\n        );\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(\n      elm,\n      memberName,\n      oldVnodeAttrs[memberName],\n      newVnodeAttrs[memberName],\n      isSvgMode2,\n      newVnode.$flags$,\n      isInitialRender\n    );\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD20.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (BUILD20.isDev && newVNode2.$elm$) {\n    consoleDevError(\n      `The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`\n    );\n  }\n  if (BUILD20.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(newVNode2.$text$);\n  } else if (BUILD20.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD20.isDebug || BUILD20.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : win.document.createTextNode(\"\");\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n  } else {\n    if (BUILD20.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    if (!win.document) {\n      throw new Error(\n        \"You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.\"\n      );\n    }\n    elm = newVNode2.$elm$ = BUILD20.svg ? win.document.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) : win.document.createElement(\n      !useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    );\n    if (BUILD20.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    if ((BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD20.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD20.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      patchSlotNode(elm);\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD20.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n      if (BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) {\n        addRemoveSlotScopedClass(contentRef, elm, newParentVNode.$elm$, oldParentVNode == null ? void 0 : oldParentVNode.$elm$);\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.__childNodes || host.childNodes).find(\n      (ref) => ref[\"s-cr\"]\n    );\n    const childNodeArray = Array.from(\n      parentElm.__childNodes || parentElm.childNodes\n    );\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.__childNodes || parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD20.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(referenceNode(childNode).parentNode, childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD20.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD20.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD20.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD20.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD20.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD20.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD20.slotRelocation) {\n          insertBefore(\n            referenceNode(oldStartVnode.$elm$).parentNode,\n            node,\n            referenceNode(oldStartVnode.$elm$)\n          );\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (BUILD20.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD20.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD20.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    if (isInitialRender && !leftVNode.$key$ && rightVNode.$key$) {\n      leftVNode.$key$ = rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => node && node[\"s-ol\"] || node;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD20.vdomText || text === null) {\n    if (BUILD20.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD20.vdomAttribute || BUILD20.reflect) {\n      if (BUILD20.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD20.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      }\n      updateElement(oldVNode, newVNode2, isSvgMode, isInitialRender);\n    }\n    if (BUILD20.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD20.updatable && BUILD20.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD20.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD20.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD20.vdomText && BUILD20.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD20.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  const children = elm.__childNodes || elm.childNodes;\n  for (const childNode of children) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.__childNodes || node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD20.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar nullifyVNodeRefs = (vNode) => {\n  if (BUILD20.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  if (BUILD20.scoped && typeof newNode[\"s-sn\"] === \"string\" && !!newNode[\"s-sr\"] && !!newNode[\"s-cr\"]) {\n    addRemoveSlotScopedClass(newNode[\"s-cr\"], newNode, parent, newNode.parentElement);\n  } else if (BUILD20.experimentalSlotFixes && typeof newNode[\"s-sn\"] === \"string\") {\n    if (parent.getRootNode().nodeType !== 11 /* DOCUMENT_FRAGMENT_NODE */) {\n      patchParentNode(newNode);\n    }\n    parent.insertBefore(newNode, reference);\n    const { slotNode } = findSlotFromSlottedNode(newNode);\n    if (slotNode) dispatchSlotChangeEvent(slotNode);\n    return newNode;\n  }\n  if (BUILD20.experimentalSlotFixes && parent.__insertBefore) {\n    return parent.__insertBefore(newNode, reference);\n  } else {\n    return parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  }\n};\nfunction addRemoveSlotScopedClass(reference, slotNode, newParent, oldParent) {\n  var _a, _b;\n  let scopeId2;\n  if (reference && typeof slotNode[\"s-sn\"] === \"string\" && !!slotNode[\"s-sr\"] && reference.parentNode && reference.parentNode[\"s-sc\"] && (scopeId2 = slotNode[\"s-si\"] || reference.parentNode[\"s-sc\"])) {\n    const scopeName = slotNode[\"s-sn\"];\n    const hostName = slotNode[\"s-hn\"];\n    (_a = newParent.classList) == null ? void 0 : _a.add(scopeId2 + \"-s\");\n    if (oldParent && ((_b = oldParent.classList) == null ? void 0 : _b.contains(scopeId2 + \"-s\"))) {\n      let child = (oldParent.__childNodes || oldParent.childNodes)[0];\n      let found = false;\n      while (child) {\n        if (child[\"s-sn\"] !== scopeName && child[\"s-hn\"] === hostName && !!child[\"s-sr\"]) {\n          found = true;\n          break;\n        }\n        child = child.nextSibling;\n      }\n      if (!found) oldParent.classList.remove(scopeId2 + \"-s\");\n    }\n  }\n}\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const isHostElement = isHost(renderFnResults);\n  const rootVnode = isHostElement ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD20.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD20.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD20.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD20.scoped || BUILD20.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) && !(cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */);\n  if (BUILD20.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD20.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"] && win.document) {\n          const orgLocationNode = BUILD20.isDebug || BUILD20.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : win.document.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD20.hydrateServerSide && (!BUILD20.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */)) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === (refNode.__parentNode || refNode.parentNode)) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          const parent = nodeToRelocate.__parentNode || nodeToRelocate.parentNode;\n          const nextSibling = nodeToRelocate.__nextSibling || nodeToRelocate.nextSibling;\n          if (!insertBeforeNode && parentNodeRef !== parent || nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD20.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */ && nodeToRelocate.tagName !== \"SLOT-FB\") {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](slotRefNode);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD20.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    const children = rootVnode.$elm$.__childNodes || rootVnode.$elm$.childNodes;\n    for (const childNode of children) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = (slotVNode) => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(\n    `<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`\n  );\n};\nvar originalLocationDebugNode = (nodeToRelocate) => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(\n    `org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`)\n  );\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD21.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    const index = ancestorComponent[\"s-p\"].push(\n      new Promise(\n        (r) => hostRef.$onRenderResolve$ = () => {\n          ancestorComponent[\"s-p\"].splice(index - 1, 1);\n          r();\n        }\n      )\n    );\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD21.taskQueue && BUILD21.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD21.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD21.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD21.lazyLoad && BUILD21.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event, elm));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    maybePromise = safeCall(instance, \"componentWillLoad\", void 0, elm);\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    maybePromise = safeCall(instance, \"componentWillUpdate\", void 0, elm);\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\", void 0, elm));\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD21.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD21.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD21.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD21.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD21.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD21.asyncLoading && rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD21.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD21.allRenderFn ? true : false;\n  const lazyLoad = BUILD21.lazyLoad ? true : false;\n  const taskQueue = BUILD21.taskQueue ? true : false;\n  const updatable = BUILD21.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD21.hasRenderFn || BUILD21.reflect) {\n      if (BUILD21.vdomRender || BUILD21.reflect) {\n        if (BUILD21.hydrateServerSide) {\n          return Promise.resolve(instance).then((value) => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD21.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  safeCall(instance, \"componentDidRender\", void 0, elm);\n  if (BUILD21.isDev) {\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD21.asyncLoading && BUILD21.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n    }\n    safeCall(instance, \"componentDidLoad\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD21.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidUpdate\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD21.method && BUILD21.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD21.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = (ref) => {\n  if (BUILD21.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = (who) => {\n  if (BUILD21.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n  if (BUILD21.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg, elm) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD21.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = (elm) => {\n  var _a, _b;\n  return BUILD21.hydratedClass ? elm.classList.add((_a = BUILD21.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD21.hydratedAttribute ? elm.setAttribute((_b = BUILD21.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = (elm) => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD22.lazyLoad && !hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`\n    );\n  }\n  const elm = BUILD22.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD22.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(\n    newVal,\n    cmpMeta.$members$[propName][0],\n    BUILD22.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n  );\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD22.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD22.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      }\n    }\n    if (!BUILD22.lazyLoad || instance) {\n      if (BUILD22.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD22.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD23.isTesting) {\n    if (prototype.__stencilAugmented) {\n      return;\n    }\n    prototype.__stencilAugmented = true;\n  }\n  if (BUILD23.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach((cbName) => {\n      const originalFormAssociatedCallback = prototype[cbName];\n      Object.defineProperty(prototype, cbName, {\n        value(...args) {\n          const hostRef = getHostRef(this);\n          const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : this;\n          if (!instance) {\n            hostRef.$onReadyPromise$.then((asyncInstance) => {\n              const cb = asyncInstance[cbName];\n              typeof cb === \"function\" && cb.call(asyncInstance, ...args);\n            });\n          } else {\n            const cb = BUILD23.lazyLoad ? instance[cbName] : originalFormAssociatedCallback;\n            typeof cb === \"function\" && cb.call(instance, ...args);\n          }\n        }\n      });\n    });\n  }\n  if (BUILD23.member && cmpMeta.$members$ || BUILD23.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD23.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD23.prop || BUILD23.state) && (memberFlags & 31 /* Prop */ || (!BUILD23.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        const { get: origGetter, set: origSetter } = Object.getOwnPropertyDescriptor(prototype, memberName) || {};\n        if (origGetter) cmpMeta.$members$[memberName][0] |= 2048 /* Getter */;\n        if (origSetter) cmpMeta.$members$[memberName][0] |= 4096 /* Setter */;\n        if (flags & 1 /* isElementConstructor */ || !origGetter) {\n          Object.defineProperty(prototype, memberName, {\n            get() {\n              if (BUILD23.lazyLoad) {\n                if ((cmpMeta.$members$[memberName][0] & 2048 /* Getter */) === 0) {\n                  return getValue(this, memberName);\n                }\n                const ref = getHostRef(this);\n                const instance = ref ? ref.$lazyInstance$ : prototype;\n                if (!instance) return;\n                return instance[memberName];\n              }\n              if (!BUILD23.lazyLoad) {\n                return origGetter ? origGetter.apply(this) : getValue(this, memberName);\n              }\n            },\n            configurable: true,\n            enumerable: true\n          });\n        }\n        Object.defineProperty(prototype, memberName, {\n          set(newValue) {\n            const ref = getHostRef(this);\n            if (BUILD23.isDev) {\n              if (\n                // we are proxying the instance (not element)\n                (flags & 1 /* isElementConstructor */) === 0 && // if the class has a setter, then the Element can update instance values, so ignore\n                (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0 && // the element is not constructing\n                (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 && // the member is a prop\n                (memberFlags & 31 /* Prop */) !== 0 && // the member is not mutable\n                (memberFlags & 1024 /* Mutable */) === 0\n              ) {\n                consoleDevWarn(\n                  `@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`\n                );\n              }\n            }\n            if (origSetter) {\n              const currentValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              if (typeof currentValue === \"undefined\" && ref.$instanceValues$.get(memberName)) {\n                newValue = ref.$instanceValues$.get(memberName);\n              } else if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                ref.$instanceValues$.set(memberName, currentValue);\n              }\n              origSetter.apply(this, [\n                parsePropertyValue(\n                  newValue,\n                  memberFlags,\n                  BUILD23.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n                )\n              ]);\n              newValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (!BUILD23.lazyLoad) {\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (BUILD23.lazyLoad) {\n              if ((flags & 1 /* isElementConstructor */) === 0 || (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0) {\n                setValue(this, memberName, newValue, cmpMeta);\n                if (flags & 1 /* isElementConstructor */ && !ref.$lazyInstance$) {\n                  ref.$onReadyPromise$.then(() => {\n                    if (cmpMeta.$members$[memberName][0] & 4096 /* Setter */ && ref.$lazyInstance$[memberName] !== ref.$instanceValues$.get(memberName)) {\n                      ref.$lazyInstance$[memberName] = newValue;\n                    }\n                  });\n                }\n                return;\n              }\n              const setterSetVal = () => {\n                const currentValue = ref.$lazyInstance$[memberName];\n                if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                  ref.$instanceValues$.set(memberName, currentValue);\n                }\n                ref.$lazyInstance$[memberName] = parsePropertyValue(\n                  newValue,\n                  memberFlags,\n                  BUILD23.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n                );\n                setValue(this, memberName, ref.$lazyInstance$[memberName], cmpMeta);\n              };\n              if (ref.$lazyInstance$) {\n                setterSetVal();\n              } else {\n                ref.$onReadyPromise$.then(() => setterSetVal());\n              }\n            }\n          }\n        });\n      } else if (BUILD23.lazyLoad && BUILD23.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD23.observeAttribute && (!BUILD23.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName) && BUILD23.lazyLoad) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD23.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          const propDesc = Object.getOwnPropertyDescriptor(prototype, propName);\n          newValue = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n          if (newValue !== this[propName] && (!propDesc.get || !!propDesc.set)) {\n            this[propName] = newValue;\n          }\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (BUILD23.reflect && m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if (BUILD24.lazyLoad && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(\n          `st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`,\n          `[Stencil] Load module for <${cmpMeta.$tagName$}>`\n        );\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD24.member && !Cstr.isProxied) {\n        if (BUILD24.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD24.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e, elm);\n      }\n      if (BUILD24.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD24.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$, elm);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD24.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD24.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD24.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (BUILD24.hydrateServerSide && BUILD24.shadowDom) {\n          if (cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */) {\n            style = scopeCss(style, scopeId2, true);\n          } else if (needsScopedSSR()) {\n            style = expandPartSelectors(style);\n          }\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD24.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance, elm) => {\n  if (BUILD24.lazyLoad) {\n    safeCall(instance, \"connectedCallback\", void 0, elm);\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD25.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD25.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD25.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD25.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          } else if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            const scopeId2 = getScopeId(cmpMeta, BUILD25.mode ? elm.getAttribute(\"s-mode\") : void 0);\n            elm[\"s-sc\"] = scopeId2;\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD25.slotRelocation && !hostId) {\n        if (BUILD25.hydrateServerSide || (BUILD25.slot || BUILD25.shadowDom) && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD25.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD25.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD25.prop && !BUILD25.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD25.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$, elm));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  if (!win.document) {\n    return;\n  }\n  const contentRefElm = elm[\"s-cr\"] = win.document.createComment(\n    BUILD25.isDebug ? `content-ref (host=${elm.localName})` : \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = (instance, elm) => {\n  if (BUILD26.lazyLoad) {\n    safeCall(instance, \"disconnectedCallback\", void 0, elm || instance);\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD26.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD26.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$, elm);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$, elm));\n    }\n  }\n  if (rootAppliedStyles.has(elm)) {\n    rootAppliedStyles.delete(elm);\n  }\n  if (elm.shadowRoot && rootAppliedStyles.has(elm.shadowRoot)) {\n    rootAppliedStyles.delete(elm.shadowRoot);\n  }\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD27.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD27.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD27.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD27.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD27.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD27.experimentalSlotFixes) {\n    if (BUILD27.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype);\n    }\n  } else {\n    if (BUILD27.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype);\n    }\n    if (BUILD27.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD27.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD27.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  if (BUILD27.hydrateClientSide && BUILD27.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __hasHostListenerAttached: false,\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      if (!this.__hasHostListenerAttached) {\n        const hostRef = getHostRef(this);\n        addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n        this.__hasHostListenerAttached = true;\n      }\n      connectedCallback(this);\n      if (originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          createShadowRoot.call(this, cmpMeta);\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(\n              `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`\n            );\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = (elm) => {\n  if (BUILD27.style && BUILD27.mode && !BUILD27.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD28 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD28.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  if (!win.document) {\n    console.warn(\"Stencil: No document found. Skipping bootstrapping lazy components.\");\n    return;\n  }\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = win.document.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ win.document.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", win.document.baseURI).href;\n  if (BUILD28.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD28.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  if (BUILD28.hydrateClientSide && BUILD28.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD28.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD28.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD28.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD28.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD28.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD28.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD28.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                createShadowRoot.call(self, cmpMeta);\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            } else if (!BUILD28.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n          plt.raf(() => {\n            var _a3;\n            const hostRef = getHostRef(this);\n            const i2 = deferredConnectedCallbacks.findIndex((host) => host === this);\n            if (i2 > -1) {\n              deferredConnectedCallbacks.splice(i2, 1);\n            }\n            if (((_a3 = hostRef == null ? void 0 : hostRef.$vnode$) == null ? void 0 : _a3.$elm$) instanceof Node && !hostRef.$vnode$.$elm$.isConnected) {\n              delete hostRef.$vnode$.$elm$;\n            }\n          });\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD28.experimentalSlotFixes) {\n        if (BUILD28.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype);\n        }\n      } else {\n        if (BUILD28.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype);\n        }\n        if (BUILD28.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD28.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD28.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD28.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD28.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function(hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD28.invisiblePrehydration && (BUILD28.hydratedClass || BUILD28.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    if (BUILD28.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD29 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD29.hostListener && listeners && win.document) {\n    if (BUILD29.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD29.hostListenerTarget ? getHostListenerTarget(win.document, elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    if (BUILD29.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n};\nvar getHostListenerTarget = (doc, elm, flags) => {\n  if (BUILD29.hostListenerTargetDocument && flags & 4 /* TargetDocument */) {\n    return doc;\n  }\n  if (BUILD29.hostListenerTargetWindow && flags & 8 /* TargetWindow */) {\n    return win;\n  }\n  if (BUILD29.hostListenerTargetBody && flags & 16 /* TargetBody */) {\n    return doc.body;\n  }\n  if (BUILD29.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) {\n    return elm.parentElement;\n  }\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = (opts) => Object.assign(plt, opts);\n\n// src/runtime/render.ts\nfunction render(vnode, container) {\n  const cmpMeta = {\n    $flags$: 0,\n    $tagName$: container.tagName\n  };\n  const ref = {\n    $flags$: 0,\n    $cmpMeta$: cmpMeta,\n    $hostElement$: container\n  };\n  renderVdom(ref, vnode);\n}\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc, staticComponents) => {\n  if (doc != null) {\n    const docData = STENCIL_DOC_DATA in doc ? doc[STENCIL_DOC_DATA] : { ...DEFAULT_DOC_DATA };\n    docData.staticComponents = new Set(staticComponents);\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc, doc.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach((orgLocationNode) => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n            if (typeof nodeRef[\"s-sn\"] === \"string\" && !nodeRef.getAttribute(\"slot\")) {\n              nodeRef.setAttribute(\"s-sn\", nodeRef[\"s-sn\"]);\n            }\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          } else if (nodeRef.nodeType === 8 /* CommentNode */) {\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${COMMENT_NODE_ID}.${childId}`;\n            nodeRef.parentNode.insertBefore(commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach((childNode) => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(\n          (node) => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]\n        );\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(\n            HYDRATE_CHILD_ID,\n            `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`\n          );\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n    if (typeof childElm[\"s-sn\"] === \"string\" && !childElm.getAttribute(\"slot\")) {\n      childElm.setAttribute(\"s-sn\", childElm[\"s-sn\"]);\n    }\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport {\n  BUILD30 as BUILD,\n  Build,\n  Env,\n  Fragment,\n  H,\n  H as HTMLElement,\n  HYDRATED_STYLE_ID,\n  Host,\n  NAMESPACE2 as NAMESPACE,\n  STENCIL_DEV_MODE,\n  addHostEventListeners,\n  bootstrapLazy,\n  cmpModules,\n  connectedCallback,\n  consoleDevError,\n  consoleDevInfo,\n  consoleDevWarn,\n  consoleError,\n  createEvent,\n  defineCustomElement,\n  disconnectedCallback,\n  forceModeUpdate,\n  forceUpdate,\n  getAssetPath,\n  getElement,\n  getHostRef,\n  getMode,\n  getRenderingRef,\n  getValue,\n  h,\n  insertVdomAnnotations,\n  isMemberInElement,\n  loadModule,\n  modeResolutionChain,\n  needsScopedSSR,\n  nextTick,\n  parsePropertyValue,\n  plt,\n  postUpdateComponent,\n  promiseResolve,\n  proxyComponent,\n  proxyCustomElement,\n  readTask,\n  registerHost,\n  registerInstance,\n  render,\n  renderVdom,\n  setAssetPath,\n  setErrorHandler,\n  setMode,\n  setNonce,\n  setPlatformHelpers,\n  setPlatformOptions,\n  setScopedSSR,\n  setValue,\n  styles,\n  supportsConstructableStylesheets,\n  supportsListenerOptions,\n  supportsShadow,\n  win,\n  writeTask\n};\n"], "names": ["module", "BUILD20", "BUILD21", "BUILD23"], "mappings": ";;;;;AAAY,YAAC,SAAS,gBAAG;MAClB,MAAM,KAAK,iBAAiB,EAAynB,oBAAoB,EAAE,UAAU,EAA0G,QAAQ,EAAE,IAAI,EAAwZ,cAAc,EAAE,IAAI,EAAkF,SAAS,EAAE,IAAkO,CAAC;;ACDphD,YAAC,aAAa,gBAAG,MAAM;MAC5B,MAAM,YAAY,GAAG,EAAE;;MCD9B;MACA;MACA;MACA,IAAI,SAAS,GAAG,MAAM,CAAC,cAAc;MACrC,IAAI,QAAQ,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK;MAChC,EAAE,KAAK,IAAI,IAAI,IAAI,GAAG;MACtB,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;MACjE,CAAC;;MAqED;MACA,IAAI,UAAU,GAAG,CAAC,GAAG,KAAK;MAC1B,EAAE,IAAI,GAAG,CAAC,qBAAqB,EAAE;MACjC,IAAI,OAAO,GAAG,CAAC,qBAAqB,EAAE;MACtC;MACA,EAAE,OAAO,MAAM;MACf,CAAC;AACE,UAAC,gBAAgB,gBAAG,CAAC,YAAY,EAAE,OAAO,KAAK;MAClD,EAAE,YAAY,CAAC,qBAAqB,GAAG,MAAM,OAAO;MACpD,EAAE,OAAO,CAAC,cAAc,GAAG,YAAY;MAIvC;MACA,IAAI,YAAY,GAAG,CAAC,WAAW,EAAE,OAAO,KAAK;MAC7C,EAAE,MAAM,OAAO,GAAG;MAClB,IAAI,OAAO,EAAE,CAAC;MACd,IAAI,aAAa,EAAE,WAAW;MAC9B,IAAI,SAAS,EAAE,OAAO;MACtB,IAAI,gBAAgB,kBAAkB,IAAI,GAAG;MAC7C,GAAG;MAIH,EAAwC;MACxC,IAAI,OAAO,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;MACrF;MACA,EAA2B;MAC3B,IAAI,OAAO,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;MAC/E,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;MAC3B,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;MAC5B;MACA,EAAE,MAAM,GAAG,GAAG,OAAO;MACrB,EAAE,WAAW,CAAC,qBAAqB,GAAG,MAAM,GAAG;MAI/C,EAAE,OAAO,GAAG;MACZ,CAAC;MACD,IAAI,iBAAiB,GAAG,CAAC,GAAG,EAAE,UAAU,KAAK,UAAU,IAAI,GAAG;MAQ9D,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,IAAgB,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;;MAUnE;MACA,IAAI,UAAU,mBAAmB,IAAI,GAAG,EAAE;MAE1C,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,KAAK;MACrD,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACzD,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc;MACzC,EAKS,IAAI,CAAC,QAAQ,EAAE;MACxB,IAAI,OAAO,MAAM;MACjB;MACA,EAAE,MAAMA,QAAM,GAAkC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAQ;MAChF,EAAE,IAAIA,QAAM,EAAE;MACd,IAAI,OAAOA,QAAM,CAAC,UAAU,CAAC;MAC7B;MACA;MACA,EAAE,OAAO;MACT;MACA;MACA;MACA;MACA,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,SAAS,EAA2E,EAAE,CAAC;MACzG,EAAE,CAAC,CAAC,IAAI;MACR,IAAI,CAAC,cAAc,KAAK;MACxB,MAAwC;MACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC;MAChD;MACA,MAAM,OAAO,cAAc,CAAC,UAAU,CAAC;MACvC,KAAK;MACL,IAAI,CAAC,CAAC,KAAK;MACX,MAAM,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;MAC5C;MACA,GAAG;MACH,CAAC;;MAED;MACA,IAAI,MAAM,mBAAmB,IAAI,GAAG,EAAE;MAkBtC,IAAI,YAAY,GAAG,kDAAkD;MAOrE,IAAI,WAAW,GAAG,wDAAwD;MAC1E,IAAI,QAAQ,GAAG,8BAA8B;AAU1C,UAAC,GAAG,gBAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG;MAGnD,IAAI,GAAG,GAAG;MACV,EAAE,OAAO,EAAE,CAAC;MACZ,EAAE,cAAc,EAAE,EAAE;MACpB,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE;MACnB,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,qBAAqB,CAAC,EAAE,CAAC;MACxC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;MACxF,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,EAAE,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;MAC3F,EAAE,EAAE,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK,IAAI,WAAW,CAAC,SAAS,EAAE,IAAI;MAC1D,CAAC;MAKD,IAAI,uBAAuB,mBAAmB,CAAC,MAAM;MACrD,EAAE,IAAI,EAAE;MACR,EAAE,IAAI,wBAAwB,GAAG,KAAK;MACtC,EAAE,IAAI;MACN,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,gBAAgB;MAC9D,MAAM,GAAG;MACT,MAAM,IAAI;MACV,MAAM,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;MAC3C,QAAQ,GAAG,GAAG;MACd,UAAU,wBAAwB,GAAG,IAAI;MACzC;MACA,OAAO;MACP,KAAK;MACL,GAAG,CAAC,OAAO,CAAC,EAAE;MACd;MACA,EAAE,OAAO,wBAAwB;MACjC,CAAC,GAAG;AACD,UAAC,cAAc,gBAAG,CAAC,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;MAC7C,IAAI,gCAAgC,GAA6B,gBAAgB,CAAC,MAAM;MACxF,EAAE,IAAI;MACN,IAAI,IAAI,aAAa,EAAE;MACvB,IAAI,OAAO,OAAO,IAAI,aAAa,EAAE,CAAC,WAAW,KAAK,UAAU;MAChE,GAAG,CAAC,OAAO,CAAC,EAAE;MACd;MACA,EAAE,OAAO,KAAK;MACd,CAAC,GAAG,CAAQ;MAIZ,IAAI,YAAY,GAAG,KAAK;MACxB,IAAI,aAAa,GAAG,EAAE;MACtB,IAAI,cAAc,GAAG,EAAE;MAEvB,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK;MAC1C,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;MAChB,EAAE,IAAI,CAAC,YAAY,EAAE;MACrB,IAAI,YAAY,GAAG,IAAI;MACvB,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,kBAAkB;MAClD,MAAM,QAAQ,CAAC,KAAK,CAAC;MACrB,KAAK,MAAM;MACX,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;MACpB;MACA;MACA,CAAC;MACD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK;MACzB,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MAC5C,IAAI,IAAI;MACR,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;MAClC,KAAK,CAAC,OAAO,CAAC,EAAE;MAChB,MAAM,YAAY,CAAC,CAAC,CAAC;MACrB;MACA;MACA,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;MAClB,CAAC;MAiBD,IAAI,KAAK,GAAG,MAAM;MAIlB,EAAE,OAAO,CAAC,aAAa,CAAC;MACxB,EAaS;MACT,IAAI,OAAO,CAAC,cAAc,CAAC;MAC3B,IAAI,IAAI,YAAY,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;MACjD,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;MACpB;MACA;MACA,CAAC;MACD,IAAI,QAAQ,GAAG,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;MAEhD,IAAI,SAAS,mBAAmB,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC;;MAe/D;MACA,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,MAAM;MAC5C,IAAI,aAAa,GAAG,CAAC,CAAC,KAAK;MAC3B,EAAE,CAAC,GAAG,OAAO,CAAC;MACd,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,UAAU;MAC3C,CAAC;;MAED;MACA,SAAS,wBAAwB,CAAC,GAAG,EAAE;MACvC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;MAChB,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,aAAa,CAAC,wBAAwB,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM;MACxK;;MAEA;MACA,IAAI,6BAA6B,GAAG,CAAC,IAAI,KAAK;MAC9C,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;MACpD,CAAC;;MAiGD;MACA,IAAI,cAAc,GAAG,EAAE;MACvB,QAAQ,CAAC,cAAc,EAAE;MACzB,EAAE,GAAG,EAAE,MAAM,GAAG;MAChB,EAAE,GAAG,EAAE,MAAM,GAAG;MAChB,EAAE,EAAE,EAAE,MAAM,EAAE;MACd,EAAE,MAAM,EAAE,MAAM,MAAM;MACtB,EAAE,SAAS,EAAE,MAAM;MACnB,CAAC,CAAC;MACF,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM;MACrB,EAAE,IAAI,EAAE,IAAI;MACZ,EAAE,KAAK,EAAE,KAAK;MACd,EAAE;MACF,CAAC,CAAC;MACF,IAAI,GAAG,GAAG,CAAC,KAAK,MAAM;MACtB,EAAE,IAAI,EAAE,KAAK;MACb,EAAE,KAAK,EAAE,IAAI;MACb,EAAE;MACF,CAAC,CAAC;MACF,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE,EAAE;MACzB,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;MACnB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;MAChC,IAAI,IAAI,GAAG,YAAY,OAAO,EAAE;MAChC,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;MAC7C,KAAK,MAAM;MACX,MAAM,OAAO,EAAE,CAAC,GAAG,CAAC;MACpB;MACA;MACA,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;MACpB,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK;MAC9B,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC;MACrB;MACA,EAAE,MAAM,uBAAuB;MAC/B;MACA,IAAI,MAAM,GAAG,CAAC,MAAM,KAAK;MACzB,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;MACnB,IAAI,OAAO,MAAM,CAAC,KAAK;MACvB,GAAG,MAAM;MACT,IAAI,MAAM,MAAM,CAAC,KAAK;MACtB;MACA,CAAC;MACD,IAAI,SAAS,GAAG,CAAC,MAAM,KAAK;MAC5B,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;MACpB,IAAI,OAAO,MAAM,CAAC,KAAK;MACvB,GAAG,MAAM;MACT,IAAI,MAAM,MAAM,CAAC,KAAK;MACtB;MACA,CAAC;MAaD,SAAS,gBAAgB,CAAC,OAAO,EAAE;MACnC,EAAE,MAAM,UAAU,GAGX,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;MAC1C,EAAE,IAAI,gCAAgC,EAAE;MACxC,IAAI,MAAM,KAAK,GAAG,IAAI,aAAa,EAAE;MACrC,IAAI,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC;MACnC,IAAI,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;MAC7C;MACA;MA2BA,IAAI,4BAA4B,GAAG,CAAC,GAAG,KAAK;MAC5C,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC;MACpD,EAAE,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,KAAK,SAAS,EAAE;MAC5F,IAAI,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;MACpE,MAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,CAAC,sBAAsB,QAAQ,CAAC,OAAO,KAAK,SAAS,EAAE;MACvF,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE;MACjF,UAAU,QAAQ,CAAC,MAAM,GAAG,IAAI;MAChC,SAAS,MAAM;MACf,UAAU,QAAQ,CAAC,MAAM,GAAG,KAAK;MACjC;MACA;MACA,KAAK,CAAC;MACN;MACA,EAAE,IAAI,EAAE,GAAG,CAAC;MACZ,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MAC7C,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC;MACpC,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,sBAAsB,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,MAAM,EAAE;MACpG,MAAM,4BAA4B,CAAC,SAAS,CAAC;MAC7C;MACA;MACA,CAAC;MACD,IAAI,oBAAoB,GAAG,CAAC,UAAU,KAAK;MAC3C,EAAE,MAAM,MAAM,GAAG,EAAE;MACnB,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MACjD,IAAI,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,MAAM;MACxD,IAAI,IAAI,WAAW,IAAI,WAAW,CAAC,WAAW,EAAE;MAChD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;MAC9B;MACA;MACA,EAAE,OAAO,MAAM;MACf,CAAC;MACD,SAAS,gBAAgB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE;MAC1D,EAAE,IAAI,EAAE,GAAG,CAAC;MACZ,EAAE,IAAI,YAAY,GAAG,EAAE;MACvB,EAAE,IAAI,SAAS;MACf,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MACvC,IAAI,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC;MAC9B,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,QAAQ,CAAC,KAAK,QAAQ,KAAK,MAAM,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,EAAE;MAC5I,MAAM,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;MAClC,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,OAAO,YAAY;MAC9D;MACA,IAAI,YAAY,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,gBAAgB,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;MACnG;MACA,EAAE,OAAO,YAAY;MACrB;MACA,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,GAAG,IAAI,KAAK;MACnE,EAAE,MAAM,UAAU,GAAG,EAAE;MACvB,EAAE,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;MACzE,EAAE,IAAI,IAAI,GAAG,IAAI;MACjB,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE;MAClC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,QAAQ,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;MAC/F;MACA,EAAE,OAAO,UAAU;MACnB,CAAC;MACD,IAAI,mBAAmB,GAAG,CAAC,cAAc,EAAE,QAAQ,KAAK;MACxD,EAAE,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,oBAAoB;MACvD,IAAI,IAAI,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE,EAAE;MACzE,MAAM,OAAO,IAAI;MACjB;MACA,IAAI,IAAI,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;MAC1D,MAAM,OAAO,IAAI;MACjB;MACA,IAAI,OAAO,KAAK;MAChB;MACA,EAAE,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;MAC3C,IAAI,OAAO,IAAI;MACf;MACA,EAAE,OAAO,QAAQ,KAAK,EAAE;MACxB,CAAC;MACD,IAAI,mBAAmB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK;MACrE,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE;MACxD,IAAI;MACJ;MACA,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;MACzD,EAAE,mBAAmB,CAAC,MAAM,CAAC,GAAG,QAAQ;MACxC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE;MACzD,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU;MAC5C,EAAE,MAAM,YAAY,GAA+C,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC;MACtG,EAaS;MACT,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;MAClD;MACA,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,mBAAmB;MACxC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;MACrC,CAAC;MACD,IAAI,WAAW,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,MAAM;MACxI,SAAS,aAAa,CAAC,IAAI,EAAE;MAC7B,EAAE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;MACpE,EAAE,MAAM,eAAe,GAAG,CAAC,YAAY,KAAK,CAAC,SAAS,IAAI,EAAE;MAC5D,IAAI,MAAM,QAAQ,GAAG,EAAE;MACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;MACjC,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;MAC9C,MAAM,OAAO,CAAC,KAAK,CAAC;AACpB;AACA;AACA;AACA,QAAQ,CAAC,CAAC;MACV;MACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,aAAa;MAC7C,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC;MAC1G,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;MAChC,MAAM,IAAI,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE;MACvC,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MACxB;MACA,KAAK,CAAC;MACN,IAAI,IAAI,YAAY,EAAE;MACtB,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,CAAC,mBAAmB;MACvE;MACA,IAAI,OAAO,QAAQ;MACnB,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;MACf,EAAE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC;MAC/C,EAAE,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC;MAC7C;MACA,SAAS,uBAAuB,CAAC,GAAG,EAAE;MACtC,EAAE,GAAG,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;MAC1G;MACA,SAAS,uBAAuB,CAAC,WAAW,EAAE,UAAU,EAAE;MAC1D,EAAE,IAAI,EAAE;MACR,EAAE,UAAU,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,aAAa,CAAC;MAC7F,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;MAC1D,EAAE,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE;MACvE,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;MAC3D,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChF,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE;MAC/B;MA6DA,IAAI,oBAAoB,GAAG,CAAC,oBAAoB,KAAK;MACrD,EAAE,oBAAoB,CAAC,aAAa,GAAG,oBAAoB,CAAC,WAAW;MACvE,EAAE,oBAAoB,CAAC,WAAW,GAAG,SAAS,QAAQ,EAAE;MACxD,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC;MAC1E,IAAI,IAAI,QAAQ,EAAE;MAClB,MAAM,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC7C,MAAM,MAAM,cAAc,GAAG,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACrE,MAAM,MAAM,WAAW,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;MACnE,MAAM,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC;MAC5D,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC;MAClG,MAAM,uBAAuB,CAAC,QAAQ,CAAC;MACvC,MAAM,4BAA4B,CAAC,IAAI,CAAC;MACxC,MAAM,OAAO,YAAY;MACzB;MACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;MACvC,GAAG;MACH,CAAC;MAmJD,IAAI,mBAAmB,GAAG,CAAC,GAAG,KAAK;MACnC,EAAE,MAAM,YAAY,SAAS,KAAK,CAAC;MACnC,IAAI,IAAI,CAAC,CAAC,EAAE;MACZ,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC;MACpB;MACA;MACA,EAAE,yBAAyB,CAAC,UAAU,EAAE,GAAG,CAAC;MAC5C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE;MACzC,IAAI,GAAG,GAAG;MACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC;MAC5D;MACA,GAAG,CAAC;MACJ,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,mBAAmB,EAAE;MAClD,IAAI,GAAG,GAAG;MACV,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;MACjC;MACA,GAAG,CAAC;MACJ,EAAE,yBAAyB,CAAC,YAAY,EAAE,GAAG,CAAC;MAC9C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;MAC3C,IAAI,GAAG,GAAG;MACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;MAC/B;MACA,GAAG,CAAC;MACJ,EAAE,yBAAyB,CAAC,WAAW,EAAE,GAAG,CAAC;MAC7C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;MAC1C,IAAI,GAAG,GAAG;MACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;MACxD;MACA,GAAG,CAAC;MACJ,EAAE,yBAAyB,CAAC,YAAY,EAAE,GAAG,CAAC;MAC9C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;MAC3C,IAAI,GAAG,GAAG;MACV,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;MACvC,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;MAC7D,MAAM,OAAO,MAAM;MACnB;MACA,GAAG,CAAC;MACJ,CAAC;MAoFD,IAAI,mBAAmB,GAAG,CAAC,UAAU,EAAE,oBAAoB,EAAE,wBAAwB,CAAC;MACtF,IAAI,iBAAiB,GAAG;MACxB,EAAE,YAAY;MACd,EAAE,YAAY;MACd,EAAE,WAAW;MACb,EAAE,aAAa;MACf,EAAE,iBAAiB;MACnB,EAAE,aAAa;MACf,EAAE;MACF,CAAC;MACD,SAAS,yBAAyB,CAAC,YAAY,EAAE,IAAI,EAAE;MACvD,EAAE,IAAI,QAAQ;MACd,EAAE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAClD,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC;MAC/E,GAAG,MAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;MACvD,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;MAC5E;MACA,EAAE,IAAI,CAAC,QAAQ,EAAE;MACjB,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC;MAClE;MACA,EAAE,IAAI,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,GAAG,YAAY,EAAE,QAAQ,CAAC;MAC1E;MACA,SAAS,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE;MACpC,EAAE,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,EAAE;MAC7B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;MACxC,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,OAAO,QAAQ;MACvD,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;MAC9B,GAAG,MAAM;MACT,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;MAC/D,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;MAClC;MACA;MAKA,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK;MAC3C,EAIS;MACT,IAAI,OAAO,MAAM;MACjB,MAAM;MACN,KAAK;MACL;MACA,CAAC;MACD,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,WAAW,KAAK;MACvC,EASS;MACT,IAAI,OAAO,MAAM;MACjB,MAAM;MACN,KAAK;MACL;MACA,CAAC;AA2DE,UAAC,CAAC,gBAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,QAAQ,KAAK;MAC9C,EAAE,IAAI,KAAK,GAAG,IAAI;MAClB,EAAE,IAAI,GAAG,GAAG,IAAI;MAChB,EAAE,IAAI,QAAQ,GAAG,IAAI;MACrB,EAAE,IAAI,MAAM,GAAG,KAAK;MACpB,EAAE,IAAI,UAAU,GAAG,KAAK;MACxB,EAAE,MAAM,aAAa,GAAG,EAAE;MAC1B,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK;MACtB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MAC1C,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;MACnB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;MAChC,QAAQ,IAAI,CAAC,KAAK,CAAC;MACnB,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;MAC9D,QAAQ,IAAI,MAAM,GAAG,OAAO,QAAQ,KAAK,UAAU,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;MAC9E,UAAU,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;MAC/B;MAKA,QAAQ,IAAI,MAAM,IAAI,UAAU,EAAE;MAClC,UAAU,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK;MACjE,SAAS,MAAM;MACf,UAAU,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;MACpE;MACA,QAAQ,UAAU,GAAG,MAAM;MAC3B;MACA;MACA,GAAG;MACH,EAAE,IAAI,CAAC,QAAQ,CAAC;MAChB,EAAE,IAAI,SAAS,EAAE;MAIjB,IAAI,IAAuB,SAAS,CAAC,GAAG,EAAE;MAC1C,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG;MACzB;MACA,IAAI,IAA8B,SAAS,CAAC,IAAI,EAAE;MAClD,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI;MAC/B;MACA,IAA2B;MAC3B,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK;MAC9D,MAAM,IAAI,SAAS,EAAE;MACrB,QAAQ,SAAS,CAAC,KAAK,GAAG,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;MAClI;MACA;MACA;MAMA,EAAE,IAA8B,OAAO,QAAQ,KAAK,UAAU,EAAE;MAChE,IAAI,OAAO,QAAQ;MACnB,MAAM,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,SAAS;MACzC,MAAM,aAAa;MACnB,MAAM;MACN,KAAK;MACL;MACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC;MACxC,EAAE,KAAK,CAAC,OAAO,GAAG,SAAS;MAC3B,EAAE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,KAAK,CAAC,UAAU,GAAG,aAAa;MACpC;MACA,EAAuB;MACvB,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG;MACrB;MACA,EAA8B;MAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ;MAC3B;MACA,EAAE,OAAO,KAAK;MACd;MACA,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;MAC9B,EAAE,MAAM,KAAK,GAAG;MAChB,IAAI,OAAO,EAAE,CAAC;MACd,IAAI,KAAK,EAAE,GAAG;MACd,IAAI,MAAM,EAAE,IAAI;MAChB,IAAI,KAAK,EAAE,IAAI;MACf,IAAI,UAAU,EAAE;MAChB,GAAG;MACH,EAA6B;MAC7B,IAAI,KAAK,CAAC,OAAO,GAAG,IAAI;MACxB;MACA,EAAuB;MACvB,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;MACtB;MACA,EAA8B;MAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI;MACvB;MACA,EAAE,OAAO,KAAK;MACd,CAAC;AACE,UAAC,IAAI,gBAAG;MACX,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;MAClD,IAAI,WAAW,GAAG;MAClB,EAAE,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;MACtE,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB;MACnF,CAAC;MACD,IAAI,eAAe,GAAG,CAAC,IAAI,MAAM;MACjC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO;MACtB,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU;MAC5B,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK;MAClB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;MACpB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK;MAClB,EAAE,KAAK,EAAE,IAAI,CAAC;MACd,CAAC,CAAC;MACF,IAAI,gBAAgB,GAAG,CAAC,IAAI,KAAK;MACjC,EAAE,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;MACvC,IAAI,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;MACxC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;MACnB,MAAM,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;MAC/B;MACA,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;MACpB,MAAM,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;MACjC;MACA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;MAC3D;MACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;MAC/C,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;MAC7B,EAAE,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;MACnC,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;MACzB,EAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;MAC3B,EAAE,OAAO,KAAK;MACd,CAAC;MAweD,IAAI,oBAAoB,GAAG,CAAC,QAAQ,KAAK;MACzC,EAAE,MAAM,aAAa,GAAG,6BAA6B,CAAC,QAAQ,CAAC;MAC/D,EAAE,OAAO,IAAI,MAAM;MACnB;MACA;MACA,IAAI,CAAC,6CAA6C,EAAE,aAAa,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC;MAC1F,IAAI;MACJ,GAAG;MACH,CAAC;MACqB,oBAAoB,CAAC,WAAW;MACnC,oBAAoB,CAAC,OAAO;MACrB,oBAAoB,CAAC,eAAe;MAgW9D,IAAI,kBAAkB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,gBAAgB,KAAK;MAYpE,EAAE,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;MACtD,IAAI,IAA2B,QAAQ,GAAG,CAAC,gBAAgB;MAC3D,MAEa;MACb,QAAQ,OAAO,SAAS,KAAK,OAAO,GAAG,KAAK,GAAG,SAAS,KAAK,EAAE,IAAI,CAAC,CAAC,SAAS;MAC9E;MACA;MACA,IAAI,IAA0B,QAAQ,GAAG,CAAC,eAAe;MACzD,MAAM,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,GAAG;MACpH;MACA,IAAI,IAA0B,QAAQ,GAAG,CAAC,eAAe;MACzD,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC;MAC9B;MACA,IAAI,OAAO,SAAS;MACpB;MACA,EAAE,OAAO,SAAS;MAClB,CAAC;AAUE,UAAC,UAAU,gBAAG,CAAC,GAAG,KAAwB,UAAU,CAAC,GAAG,CAAC,CAAC,aAAa;;MAE1E;AACG,UAAC,WAAW,gBAAG,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,KAAK;MACxC,EAAE,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;MAC7B,EAAE,OAAO;MACT,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK;MAItB,MAAM,OAAO,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE;MAClC,QAAQ,OAAO,EAAE,IAA2B;MAC5C,QAAQ,QAAQ,EAAE,IAA4B;MAC9C,QAAQ,UAAU,EAAE,IAA+B;MACnD,QAAQ;MACR,OAAO,CAAC;MACR;MACA,GAAG;MACH;MACA,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,KAAK;MACrC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MAC/B,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;MACvB,EAAE,OAAO,EAAE;MACX,CAAC;MAID,IAAI,iBAAiB,mBAAmB,IAAI,OAAO,EAAE;MACrD,IAAI,aAAa,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,KAAK;MACpD,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;MAClC,EAAE,IAAI,gCAAgC,IAAI,OAAO,EAAE;MACnD,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,aAAa,EAAE;MACxC,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;MACnC,MAAM,KAAK,GAAG,OAAO;MACrB,KAAK,MAAM;MACX,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;MAChC;MACA,GAAG,MAAM;MACT,IAAI,KAAK,GAAG,OAAO;MACnB;MACA,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;MAC7B,CAAC;MACD,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,KAAK;MACtD,EAAE,IAAI,EAAE;MACR,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAa,CAAC;MAC5C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;MACpC,EAAE,IAA6B,CAAC,GAAG,CAAC,QAAQ,EAAE;MAC9C,IAAI,OAAO,QAAQ;MACnB;MACA,EAAE,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,KAAK,EAAE,0BAA0B,kBAAkB,GAAG,GAAG,CAAC,QAAQ;MACpH,EAAE,IAAI,KAAK,EAAE;MACb,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;MACnC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,IAAI,kBAAkB;MACxE,MAAM,IAAI,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC;MACnE,MAAM,IAAI,QAAQ;MAClB,MAAM,IAAI,CAAC,aAAa,EAAE;MAC1B,QAAQ,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,EAAE,aAAa,mBAAmB,IAAI,GAAG,EAAE,CAAC;MAC5F;MACA,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;MACxC,QAEe;MACf,UAAU,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;MACxD,UAAU,QAAQ,CAAC,SAAS,GAAG,KAAK;MACpC,UAAU,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;MAChG,UAAU,IAAI,KAAK,IAAI,IAAI,EAAE;MAC7B,YAAY,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;MACjD;MAIA,UAAU,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,EAAE;MACnE,YAAY,IAAI,kBAAkB,CAAC,QAAQ,KAAK,MAAM,EAAE;MACxD,cAAc,MAAM,eAAe,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC;MACjG,cAAc,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC;MACrK,cAAc,kBAAkB,CAAC,YAAY;MAC7C,gBAAgB,QAAQ;MACxB,gBAAgB,CAAC,cAAc,IAAI,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,UAAU,MAAM,kBAAkB,GAAG,cAAc,GAAG;MACxH,eAAe;MACf,aAAa,MAAM,IAAI,MAAM,IAAI,kBAAkB,EAAE;MACrD,cAAc,IAAI,gCAAgC,EAAE;MACpD,gBAAgB,MAAM,UAAU,GAAG,IAAI,aAAa,EAAE;MACtD,gBAAgB,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC;MAC7C,gBAAgB,kBAAkB,CAAC,kBAAkB,GAAG,CAAC,UAAU,EAAE,GAAG,kBAAkB,CAAC,kBAAkB,CAAC;MAC9G,eAAe,MAAM;MACrB,gBAAgB,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC;MACxF,gBAAgB,IAAI,sBAAsB,EAAE;MAC5C,kBAAkB,sBAAsB,CAAC,SAAS,GAAG,KAAK,GAAG,sBAAsB,CAAC,SAAS;MAC7F,iBAAiB,MAAM;MACvB,kBAAkB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC;MACtD;MACA;MACA,aAAa,MAAM;MACnB,cAAc,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC;MACjD;MACA;MACA,UAAU,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,+BAA+B;MAChE,YAAY,kBAAkB,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC;MAC3D;MACA;MACA,QAAQ,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,0BAA0B;MACzD,UAAU,QAAQ,CAAC,SAAS,IAAI,WAAW;MAC3C;MACA,QAAQ,IAAI,aAAa,EAAE;MAC3B,UAAU,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;MACrC;MACA;MACA,KAAK,MAAM,IAAgC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnG,MAAM,kBAAkB,CAAC,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,CAAC,kBAAkB,EAAE,KAAK,CAAC;MAC/F;MACA;MACA,EAAE,OAAO,QAAQ;MACjB,CAAC;MACD,IAAI,YAAY,GAAG,CAAC,OAAO,KAAK;MAChC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;MACnC,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;MACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO;MAC/B,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,SAAS,CAAC;MACvE,EAAE,MAAM,QAAQ,GAAG,QAAQ;MAC3B,IAA2C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE;MAC9F,IAAI,OAEF,CAAC;MACH,EAAE,IAAuE,KAAK,GAAG,EAAE,iCAAiC;MACpH,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;MAC1B,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;MACtC;MACA,EAAE,eAAe,EAAE;MACnB,CAAC;MACD,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,KAAK,IAA0F,GAAG,CAAC,SAAS,CAAC;MAqB7I,IAAI,WAAW,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,KAAK;MACxF,EAAE,IAAI,QAAQ,KAAK,QAAQ,EAAE;MAC7B,IAAI;MACJ;MACA,EAAE,IAAI,MAAM,GAAG,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC;MACjD,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,WAAW,EAAE;MACnC,EAAE,IAAyB,UAAU,KAAK,OAAO,EAAE;MACnD,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS;MACnC,IAAI,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;MAC/C,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;MAC7C,IAOW;MACX,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACjF,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E;MACA,GAAG,MAAM,IAAyB,UAAU,KAAK,OAAO,EAAE;MAC1D,IAA2B;MAC3B,MAAM,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;MACnC,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;MACjD,UAAU,IAAkC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAChE,YAAY,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;MAC1C,WAAW,MAAM;MACjB,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;MAChC;MACA;MACA;MACA;MACA,IAAI,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;MACjC,MAAM,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1D,QAAQ,IAAkC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC9D,UAAU,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;MACrD,SAAS,MAAM;MACf,UAAU,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,GAAG,MAAM,IAAuB,UAAU,KAAK,KAAK,EAAE,CACnD,MAAM,IAAuB,UAAU,KAAK,KAAK,EAAE;MACtD,IAAI,IAAI,QAAQ,EAAE;MAClB,MAAM,QAAQ,CAAC,GAAG,CAAC;MACnB;MACA,GAAG,MAAM,IAA4B,CAAoB,CAAC,MAAM,CAAoC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACzJ,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC/B,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;MACtC,KAAK,MAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;MAC3C,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9B,KAAK,MAAM;MACX,MAAM,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9C;MACA,IAAI,IAAI,QAAQ,IAAI,QAAQ,EAAE;MAC9B,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,oBAAoB,CAAC;MAC/D,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAC9D,MAAM,IAAI,QAAQ,EAAE;MACpB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;MACnD;MACA,MAAM,IAAI,QAAQ,EAAE;MACpB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;MACnD;MACA;MACA,GAAG,MAAkC;MACrC,IAAI,MAAM,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC;MAC7C,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAM,EAAE;MAC9D,MAAM,IAAI;MACV,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxC,UAAU,MAAM,CAAC,GAAG,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,QAAQ;MACpD,UAAU,IAAI,UAAU,KAAK,MAAM,EAAE;MACrC,YAAY,MAAM,GAAG,KAAK;MAC1B,WAAW,MAAM,IAAI,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;MAC/D,YAAY,IAAI,OAAO,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE;MACxE,cAAc,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;MACjC,aAAa,MAAM;MACnB,cAAc,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;MAC7C;MACA;MACA,SAAS,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;MACjD,UAAU,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ;MACpC;MACA,OAAO,CAAC,OAAO,CAAC,EAAE;MAClB;MACA;MACA,IAAI,IAAI,KAAK,GAAG,KAAK;MACrB,IAA2B;MAC3B,MAAM,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;MACrD,QAAQ,UAAU,GAAG,EAAE;MACvB,QAAQ,KAAK,GAAG,IAAI;MACpB;MACA;MACA,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;MAChD,MAAM,IAAI,QAAQ,KAAK,KAAK,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;MACrE,QAAQ,IAAyB,KAAK,EAAE;MACxC,UAAU,GAAG,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC;MACrD,SAAS,MAAM;MACf,UAAU,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC;MACzC;MACA;MACA,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,iBAAiB,KAAK,KAAK,CAAC,SAAS,IAAI,GAAG,CAAC,QAAQ,KAAK,CAAC,oBAAoB;MACnH,MAAM,QAAQ,GAAG,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ;MAClD,MAAM,IAAyB,KAAK,EAAE;MACtC,QAAQ,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;MAC1D,OAAO,MAAM;MACb,QAAQ,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;MAC9C;MACA;MACA;MACA,CAAC;MACD,IAAI,mBAAmB,GAAG,IAAI;MAC9B,IAAI,cAAc,GAAG,CAAC,KAAK,KAAK;MAChC,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,EAAE;MAChE,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO;MACzB;MACA,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;MAC3C,IAAI,OAAO,EAAE;MACb;MACA,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;MACzC,CAAC;MACD,IAAI,oBAAoB,GAAG,SAAS;MACpC,IAAI,mBAAmB,GAAG,IAAI,MAAM,CAAC,oBAAoB,GAAG,GAAG,CAAC;;MAEhE;MACA,IAAI,aAAa,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,KAAK;MACzE,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,KAAK,EAAE,2BAA2B,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK;MACjI,EAAE,MAAM,aAAa,GAAG,QAAQ,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE;MAC1D,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE;MAC9C,EAAyB;MACzB,IAAI,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE;MAC1E,MAAM,IAAI,EAAE,UAAU,IAAI,aAAa,CAAC,EAAE;MAC1C,QAAQ,WAAW;MACnB,UAAU,GAAG;MACb,UAAU,UAAU;MACpB,UAAU,aAAa,CAAC,UAAU,CAAC;MACnC,UAAU,MAAM;MAChB,UAAU,UAAU;MACpB,UAAU,QAAQ,CAAC,OAEX,CAAC;MACT;MACA;MACA;MACA,EAAE,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE;MACxE,IAAI,WAAW;MACf,MAAM,GAAG;MACT,MAAM,UAAU;MAChB,MAAM,aAAa,CAAC,UAAU,CAAC;MAC/B,MAAM,aAAa,CAAC,UAAU,CAAC;MAC/B,MAAM,UAAU;MAChB,MAAM,QAAQ,CAAC,OAEX,CAAC;MACL;MACA,CAAC;MACD,SAAS,eAAe,CAAC,SAAS,EAAE;MACpC,EAAE,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;MAClC;MACA,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK;MACzD;MACA;MACA,IAAI;MACJ,GAAG;MACH;;MAEA;MACA,IAAI,OAAO;MACX,IAAI,UAAU;MACd,IAAI,WAAW;MACf,IAAI,kBAAkB,GAAG,KAAK;MAC9B,IAAI,2BAA2B,GAAG,KAAK;MACvC,IAAI,iBAAiB,GAAG,KAAK;MAC7B,IAAI,SAAS,GAAG,KAAK;MACrB,IAAI,SAAS,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,UAAU,KAAK;MAChE,EAAE,IAAI,EAAE;MACR,EAAE,MAAM,SAAS,GAAG,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC;MACzD,EAAE,IAAI,EAAE,GAAG,CAAC;MACZ,EAAE,IAAI,GAAG;MACT,EAAE,IAAI,SAAS;MACf,EAAE,IAAI,QAAQ;MACd,EAAE,IAA8B,CAAC,kBAAkB,EAAE;MACrD,IAAI,iBAAiB,GAAG,IAAI;MAC5B,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,MAAM,EAAE;MACpC,MAAM,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,UAAU;MAC/C;MACA;MACA,QAAQ,CAAC;MACT;MACA;MACA;MACA;MACA,QAAQ,CAAC;MACT,OAAO;MACP;MACA;MAMA,EAAE,IAAwB,SAAS,CAAC,MAAM,KAAK,IAAI,EAAE;MACrD,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC;MACzE,GAAG,MAAM,IAA8B,SAAS,CAAC,OAAO,GAAG,CAAC,wBAAwB;MACpF,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAAsF,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;MAC9I,IAA+B;MAC/B,MAAM,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC;MAC/C;MACA,GAAG,MAAM;MAIT,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;MACvB,MAAM,MAAM,IAAI,KAAK;MACrB,QAAQ;MACR,OAAO;MACP;MACA,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAGjB,GAAG,CAAC,QAAQ,CAAC,aAAa;MAClC,MAAM,CAAC,kBAAkB,IAAIC,KAAO,CAAC,cAAc,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,wBAAwB,SAAS,GAAG,SAAS,CAAC;MAC1H,KAAK;MAIL,IAA+B;MAC/B,MAAM,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC;MAC/C;MACA,IAAI,IAAuF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;MACtI,MAAM,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;MAC9C;MACA,IAAI,IAAI,SAAS,CAAC,UAAU,EAAE;MAC9B,MAAM,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;MAC3D,QAAQ,SAAS,GAAG,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC;MAC5D,QAAQ,IAAI,SAAS,EAAE;MACvB,UAAU,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC;MACpC;MACA;MACA;MAQA;MACA,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,WAAW;MAC3B,EAA8B;MAC9B,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,EAAE;MAChF,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;MACxB,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,UAAU;MAC9B,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE;MAC1C,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG;MACtE,MAAM,aAAa,CAAC,GAAG,CAAC;MACxB,MAAM,QAAQ,GAAG,cAAc,IAAI,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC;MACrG,MAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE;MAClF,QAEe;MACf,UAAU,yBAAyB,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC;MAChE;MACA;MACA,MAAyF;MACzF,QAAQ,wBAAwB,CAAC,UAAU,EAAE,GAAG,EAAE,cAAc,CAAC,KAAK,EAAE,cAAc,IAAI,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;MAC/H;MACA;MACA;MACA,EAAE,OAAO,GAAG;MACZ,CAAC;MAqBD,IAAI,yBAAyB,GAAG,CAAC,SAAS,EAAE,SAAS,KAAK;MAC1D,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC;MAClB,EAAE,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,UAAU,CAAC;MAStF,EAAE,KAAK,IAAI,EAAE,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;MAC7D,IAAI,MAAM,SAAS,GAAG,iBAAiB,CAAC,EAAE,CAAC;MAC3C,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,WAAW,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;MAChE,MAAM,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;MAC5F,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;MAChC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;MAChC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;MAChC,MAAM,iBAAiB,GAAG,IAAI;MAC9B;MACA,IAAI,IAAI,SAAS,EAAE;MACnB,MAAM,yBAAyB,CAAC,SAAS,EAAE,SAAS,CAAC;MACrD;MACA;MACA,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;MACnB,CAAC;MACD,IAAI,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK;MAC9E,EAAE,IAAI,YAAY,GAA6B,SAAS,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,UAAU,IAAI,SAAS;MAC7G,EAAE,IAAI,SAAS;MACf,EAAE,IAAyB,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,OAAO,KAAK,WAAW,EAAE;MAC5F,IAAI,YAAY,GAAG,YAAY,CAAC,UAAU;MAC1C;MACA,EAAE,OAAO,QAAQ,IAAI,MAAM,EAAE,EAAE,QAAQ,EAAE;MACzC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE;MAC1B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC;MACxD,MAAM,IAAI,SAAS,EAAE;MACrB,QAAQ,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,SAAS;MAC1C,QAAQ,YAAY,CAAC,YAAY,EAAE,SAAS,EAA2B,aAAa,CAAC,MAAM,CAAC,CAAS,CAAC;MACtG;MACA;MACA;MACA,CAAC;MACD,IAAI,YAAY,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK;MACjD,EAAE,KAAK,IAAI,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,EAAE;MACvD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;MAC/B,IAAI,IAAI,KAAK,EAAE;MACf,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK;MAC7B,MAAM,gBAAgB,CAAC,KAAK,CAAC;MAC7B,MAAM,IAAI,GAAG,EAAE;MACf,QAAoC;MACpC,UAAU,2BAA2B,GAAG,IAAI;MAC5C,UAAU,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE;MAC3B,YAAY,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;MAChC,WAAW,MAAM;MACjB,YAAY,yBAAyB,CAAC,GAAG,EAAE,IAAI,CAAC;MAChD;MACA;MACA,QAAQ,GAAG,CAAC,MAAM,EAAE;MACpB;MACA;MACA;MACA,CAAC;MACD,IAAI,cAAc,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,GAAG,KAAK,KAAK;MACtF,EAAE,IAAI,WAAW,GAAG,CAAC;MACrB,EAAE,IAAI,WAAW,GAAG,CAAC;MACrB,EAAE,IAAI,QAAQ,GAAG,CAAC;MAClB,EAAE,IAAI,EAAE,GAAG,CAAC;MACZ,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;MAClC,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;MAC9B,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;MACpC,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;MAClC,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;MAC9B,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;MACpC,EAAE,IAAI,IAAI;MACV,EAAE,IAAI,SAAS;MACf,EAAE,OAAO,WAAW,IAAI,SAAS,IAAI,WAAW,IAAI,SAAS,EAAE;MAC/D,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;MAC/B,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,KAAK,MAAM,IAAI,WAAW,IAAI,IAAI,EAAE;MACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,KAAK,MAAM,IAAI,aAAa,IAAI,IAAI,EAAE;MACtC,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,KAAK,MAAM,IAAI,WAAW,IAAI,IAAI,EAAE;MACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,KAAK,MAAM,IAAI,WAAW,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;MAC3E,MAAM,KAAK,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;MAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,KAAK,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE;MACvE,MAAM,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;MACtD,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,KAAK,MAAM,IAAI,WAAW,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE;MACzE,MAAM,IAA8B,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,IAAI,WAAW,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE;MACtG,QAAQ,yBAAyB,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;MACxE;MACA,MAAM,KAAK,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,CAAC;MACxD,MAAM,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC;MACjF,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,KAAK,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;MACzE,MAAM,IAA8B,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,IAAI,WAAW,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE;MACtG,QAAQ,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;MACtE;MACA,MAAM,KAAK,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC;MACxD,MAAM,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;MACrE,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;MACtC,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC1C,KAAK,MAAM;MACX,MAAM,QAAQ,GAAG,EAAE;MACnB,MAA2B;MAC3B,QAAQ,KAAK,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,SAAS,EAAE,EAAE,EAAE,EAAE;MACtD,UAAU,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE;MAChG,YAAY,QAAQ,GAAG,EAAE;MACzB,YAAY;MACZ;MACA;MACA;MACA,MAAM,IAAuB,QAAQ,IAAI,CAAC,EAAE;MAC5C,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;MACnC,QAAQ,IAAI,SAAS,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE;MACrD,UAAU,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC;MAC5E,SAAS,MAAM;MACf,UAAU,KAAK,CAAC,SAAS,EAAE,aAAa,EAAE,eAAe,CAAC;MAC1D,UAAU,KAAK,CAAC,QAAQ,CAAC,GAAG,MAAM;MAClC,UAAU,IAAI,GAAG,SAAS,CAAC,KAAK;MAChC;MACA,QAAQ,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC5C,OAAO,MAAM;MACb,QAAQ,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC;MAC7E,QAAQ,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;MAC5C;MACA,MAAM,IAAI,IAAI,EAAE;MAChB,QAAoC;MACpC,UAAU,YAAY;MACtB,YAAY,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,UAAU;MACzD,YAAY,IAAI;MAChB,YAAY,aAAa,CAAC,aAAa,CAAC,KAAK;MAC7C,WAAW;MACX;MAGA;MACA;MACA;MACA,EAAE,IAAI,WAAW,GAAG,SAAS,EAAE;MAC/B,IAAI,SAAS;MACb,MAAM,SAAS;MACf,MAAM,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK;MACtE,MAAM,SAAS;MACf,MAAM,KAAK;MACX,MAAM,WAAW;MACjB,MAAM;MACN,KAAK;MACL,GAAG,MAAM,IAAyB,WAAW,GAAG,SAAS,EAAE;MAC3D,IAAI,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;MAC/C;MACA,CAAC;MACD,IAAI,WAAW,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,eAAe,GAAG,KAAK,KAAK;MACtE,EAAE,IAAI,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;MAC5C,IAAI,IAA8B,SAAS,CAAC,KAAK,KAAK,MAAM,EAAE;MAC9D,MAAM,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;MACnD;MACA,IAAI,IAAuB,CAAC,eAAe,EAAE;MAC7C,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK;MACjD;MACA,IAAI,IAAI,eAAe,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE;MACjE,MAAM,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;MACxC;MACA,IAAI,OAAO,IAAI;MACf;MACA,EAAE,OAAO,KAAK;MACd,CAAC;MACD,IAAI,aAAa,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI;MAC1D,IAAI,KAAK,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,GAAG,KAAK,KAAK;MAC9D,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;MAC9C,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU;MACzC,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,UAAU;MAE1C,EAAE,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM;MAC/B,EAAE,IAAI,aAAa;MACnB,EAAE,IAAyB,IAAI,KAAK,IAAI,EAAE;MAI1C,IAAkD;MAOlD,MAAM,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,SAA0B,CAAC;MACpE;MACA,IAAI,IAAyB,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;MAC3E,MAAM,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC;MAC/E,KAAK,MAAM,IAAI,WAAW,KAAK,IAAI,EAAE;MACrC,MAAM,IAA6C,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;MAC7E,QAAQ,GAAG,CAAC,WAAW,GAAG,EAAE;MAC5B;MACA,MAAM,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;MAC7E,KAAK,MAAM;MACX;MACA,MAAM,CAAC,eAAe,IAAIA,KAAO,CAAC,SAAS,IAAI,WAAW,KAAK;MAC/D,MAAM;MACN,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;MAC1D;MAIA,GAAG,MAAM,IAAkD,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE;MAC1F,IAAI,aAAa,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;MAC/C,GAAG,MAAM,IAAwB,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;MAC3D,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI;MACnB;MACA,CAAC;MACD,IAAI,aAAa,GAAG,EAAE;MACtB,IAAI,4BAA4B,GAAG,CAAC,GAAG,KAAK;MAC5C,EAAE,IAAI,IAAI;MACV,EAAE,IAAI,gBAAgB;MACtB,EAAE,IAAI,CAAC;MACP,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU;MACrD,EAAE,KAAK,MAAM,SAAS,IAAI,QAAQ,EAAE;MACpC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;MAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU;MACnF,MAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;MACxC,MAAM,KAAK,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;MACzD,QAAQ,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC;MAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,IAAqF,CAAC,EAAE;MAC7K,UAAU,IAAI,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;MACnD,YAAY,IAAI,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC;MACzF,YAAY,2BAA2B,GAAG,IAAI;MAC9C,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,QAAQ;MACnD,YAAY,IAAI,gBAAgB,EAAE;MAClC,cAAc,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;MAC3E,cAAc,gBAAgB,CAAC,aAAa,GAAG,SAAS;MACxD,aAAa,MAAM;MACnB,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;MAC9C,cAAc,aAAa,CAAC,IAAI,CAAC;MACjC,gBAAgB,aAAa,EAAE,SAAS;MACxC,gBAAgB,gBAAgB,EAAE;MAClC,eAAe,CAAC;MAChB;MACA,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;MAC9B,cAAc,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK;MAClD,gBAAgB,IAAI,mBAAmB,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;MACtF,kBAAkB,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC;MAC3F,kBAAkB,IAAI,gBAAgB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;MACvE,oBAAoB,YAAY,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa;MAC/E;MACA;MACA,eAAe,CAAC;MAChB;MACA,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC,EAAE;MAC9E,YAAY,aAAa,CAAC,IAAI,CAAC;MAC/B,cAAc,gBAAgB,EAAE;MAChC,aAAa,CAAC;MACd;MACA;MACA;MACA;MACA,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,oBAAoB;MACpD,MAAM,4BAA4B,CAAC,SAAS,CAAC;MAC7C;MACA;MACA,CAAC;MACD,IAAI,gBAAgB,GAAG,CAAC,KAAK,KAAK;MAClC,EAAuB;MACvB,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;MACjE,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;MAC9D;MACA,CAAC;MACD,IAAI,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,KAAK;MACnD,EAAE,IAAsB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;MACvG,IAAI,wBAAwB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,aAAa,CAAC;MACrF;MASA,EAES;MACT,IAAI,OAAO,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;MAC5E;MACA,CAAC;MACD,SAAS,wBAAwB,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;MAC7E,EAAE,IAAI,EAAE,EAAE,EAAE;MACZ,EAAE,IAAI,QAAQ;MACd,EAAE,IAAI,SAAS,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;MACxM,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;MACtC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;MACrC,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;MACzE,IAAI,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAE;MACnG,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;MACrE,MAAM,IAAI,KAAK,GAAG,KAAK;MACvB,MAAM,OAAO,KAAK,EAAE;MACpB,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;MAC1F,UAAU,KAAK,GAAG,IAAI;MACtB,UAAU;MACV;MACA,QAAQ,KAAK,GAAG,KAAK,CAAC,WAAW;MACjC;MACA,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;MAC7D;MACA;MACA;MACA,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,GAAG,KAAK,KAAK;MACtE,EAAK,IAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACpB,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,aAAa;MACvC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;MACnC,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;MAC1D,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;MAC/C,EAAE,MAAM,SAAS,GAAG,aAAa,GAAG,eAAe,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC;MACpF,EAAE,WAAW,GAAG,OAAO,CAAC,OAAO;MAe/B,EAAE,IAAuB,OAAO,CAAC,gBAAgB,EAAE;MACnD,IAAI,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,EAAE;MAC/C,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG;MAChC,MAAM,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ;MAChF,KAAK;MACL;MACA,EAAE,IAAI,aAAa,IAAI,SAAS,CAAC,OAAO,EAAE;MAC1C,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;MACtD,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxF,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;MAC7C;MACA;MACA;MACA,EAAE,SAAS,CAAC,KAAK,GAAG,IAAI;MACxB,EAAE,SAAS,CAAC,OAAO,IAAI,CAAC;MACxB,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS;MAC7B,EAAE,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAuB,OAAO,CAAC,UAAU,IAAI,OAAO,CAAU;MAChG,EAA2C;MAC3C,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;MAC7B;MACA,EAAE,kBAAkB,GAAqB,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,GAAG,4BAA4B;MACpJ,EAA8B;MAC9B,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;MAChC,IAAI,2BAA2B,GAAG,KAAK;MACvC;MACA,EAAE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;MAC3C,EAA8B;MAC9B,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC;MACpB,IAAI,IAAI,iBAAiB,EAAE;MAC3B,MAAM,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;MACnD,MAAM,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;MAChD,QAAQ,MAAM,cAAc,GAAG,YAAY,CAAC,gBAAgB;MAC5D,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE;MACrD,UAAU,MAAM,eAAe,GAA8F,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;MAC5J,UAAU,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc;MAClD,UAAU,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,cAAc,CAAC;MAC3G;MACA;MACA,MAAM,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;MAChD,QAAQ,MAAM,cAAc,GAAG,YAAY,CAAC,gBAAgB;MAC5D,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,aAAa;MACtD,QAAQ,IAAI,WAAW,EAAE;MACzB,UAAU,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU;MACtD,UAAU,IAAI,gBAAgB,GAAG,WAAW,CAAC,WAAW;MACxD,UAAuJ;MACvJ,YAAY,IAAI,eAAe,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,eAAe;MACrG,YAAY,OAAO,eAAe,EAAE;MACpC,cAAc,IAAI,OAAO,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI;MAC9E,cAAc,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,MAAM,CAAC,IAAI,aAAa,MAAM,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;MAC3I,gBAAgB,OAAO,GAAG,OAAO,CAAC,WAAW;MAC7C,gBAAgB,OAAO,OAAO,KAAK,cAAc,KAAK,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;MACnG,kBAAkB,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,WAAW;MAC1E;MACA,gBAAgB,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;MAClD,kBAAkB,gBAAgB,GAAG,OAAO;MAC5C,kBAAkB;MAClB;MACA;MACA,cAAc,eAAe,GAAG,eAAe,CAAC,eAAe;MAC/D;MACA;MACA,UAAU,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,UAAU;MACjF,UAAU,MAAM,WAAW,GAAG,cAAc,CAAC,aAAa,IAAI,cAAc,CAAC,WAAW;MACxF,UAAU,IAAI,CAAC,gBAAgB,IAAI,aAAa,KAAK,MAAM,IAAI,WAAW,KAAK,gBAAgB,EAAE;MACjG,YAAY,IAAI,cAAc,KAAK,gBAAgB,EAAE;MACrD,cAAc,IAAsC,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;MACvG,gBAAgB,cAAc,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ;MACnF;MACA,cAAc,YAAY,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB,CAAC;MAC3E,cAAc,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,sBAAsB,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE;MAC3G,gBAAgB,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;MAC1F;MACA;MACA;MACA,UAAU,cAAc,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC;MACzG,SAAS,MAAM;MACf,UAAU,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,oBAAoB;MAC/D,YAAY,IAAI,aAAa,EAAE;MAC/B,cAAc,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;MACxF;MACA,YAAY,cAAc,CAAC,MAAM,GAAG,IAAI;MACxC;MACA;MACA;MACA;MACA,IAAI,IAAI,2BAA2B,EAAE;MACrC,MAAM,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;MACnD;MACA,IAAI,GAAG,CAAC,OAAO,IAAI,EAAE;MACrB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;MAC5B;MAYA,EAAE,UAAU,GAAG,MAAM;MACrB,CAAC;;MAcD;MACA,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,iBAAiB,KAAK;MACvD,EAAE,IAA4B,iBAAiB,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;MAC3G,IAAI,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI;MAC/C,MAAM,IAAI,OAAO;MACjB,QAAQ,CAAC,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,MAAM;MACjD,UAAU,iBAAiB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;MACvD,UAAU,CAAC,EAAE;MACb;MACA;MACA,KAAK;MACL;MACA,CAAC;MACD,IAAI,cAAc,GAAG,CAAC,OAAO,EAAE,aAAa,KAAK;MACjD,EAA8C;MAC9C,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;MACzB;MACA,EAAE,IAA4B,OAAO,CAAC,OAAO,GAAG,CAAC,6BAA6B;MAC9E,IAAI,OAAO,CAAC,OAAO,IAAI,GAAG;MAC1B,IAAI;MACJ;MACA,EAAE,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC;MACxD,EAAE,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC;MAC9D,EAAE,OAA2B,SAAS,CAAC,QAAQ,CAAC,CAAa;MAC7D,CAAC;MACD,IAAI,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa,KAAK;MAChD,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;MACnC,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;MAC/E,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;MAClE,EAAE,IAAI,CAAC,QAAQ,EAAE;MACjB,IAAI,MAAM,IAAI,KAAK;MACnB,MAAM,CAAC,wBAAwB,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,uNAAuN;MAClR,KAAK;MACL;MACA,EAAE,IAAI,YAAY;MAClB,EAAE,IAAI,aAAa,EAAE;MACrB,IAAkD;MAClD,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;MAC5B,MAAM,IAAI,OAAO,CAAC,iBAAiB,EAAE;MACrC,QAAQ,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;MAC1G,QAAQ,OAAO,CAAC,iBAAiB,GAAG,MAAM;MAC1C;MACA;MAEA,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,CAAC;MACvE,GAAG,MAAM;MAET,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,EAAE,MAAM,EAAE,GAAG,CAAC;MACzE;MAEA,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;MACpG,EAAE,WAAW,EAAE;MACf,EAAE,OAAO,OAAO,CAAC,YAAY,EAAE,MAAM,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;MACvF,CAAC;MACD,IAAI,OAAO,GAAG,CAAC,YAAY,EAAE,EAAE,KAAK,UAAU,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK;MACrG,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;MACrB,EAAE,EAAE,EAAE;MACN,CAAC,CAAC,GAAG,EAAE,EAAE;MACT,IAAI,UAAU,GAAG,CAAC,YAAY,KAAK,YAAY,YAAY,OAAO,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,UAAU;MAClJ,IAAI,eAAe,GAAG,OAAO,OAAO,EAAE,QAAQ,EAAE,aAAa,KAAK;MAClE,EAAE,IAAI,EAAE;MACR,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;MACnC,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;MACrE,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;MACxB,EAAE,IAAqB,aAAa,EAAE;MACtC,IAAI,YAAY,CAAC,OAAO,CAAC;MACzB;MACA,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;MAIrE,EAES;MACT,IAAI,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,aAAa,CAAC;MACrD;MAmBA,EAAE,IAA4B,EAAE,EAAE;MAClC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;MACxB,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM;MACxB;MACA,EAAE,SAAS,EAAE;MACb,EAAE,SAAS,EAAE;MACb,EAA4B;MAC5B,IAAI,MAAM,gBAAgB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;MAChE,IAAI,MAAM,UAAU,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;MACzD,IAAI,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;MACvC,MAAM,UAAU,EAAE;MAClB,KAAK,MAAM;MACX,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;MACpD,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;MAC1B,MAAM,gBAAgB,CAAC,MAAM,GAAG,CAAC;MACjC;MACA;MAGA,CAAC;MAED,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,aAAa,KAAK;MAK5D,EAAE,IAAI;MAEN,IAAI,QAAQ,GAAiB,QAAQ,CAAC,MAAM,EAAE,CAAuC;MACrF,IAAgC;MAChC,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;MAC5B;MACA,IAA+B;MAC/B,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;MAC1B;MACA,IAAgD;MAChD,MAAiD;MACjD,QAEe;MACf,UAAU,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;MACtD;MACA;MAQA;MACA,GAAG,CAAC,OAAO,CAAC,EAAE;MACd,IAAI,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;MAC1C;MAEA,EAAE,OAAO,IAAI;MACb,CAAC;MAED,IAAI,mBAAmB,GAAG,CAAC,OAAO,KAAK;MACvC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS;MAC7C,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;MACnC,EAAE,MAAM,aAAa,GAAG,UAAU,CAAC,YAAY,EAAE,OAAO,CAAC;MACzD,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;MAClE,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB;MAIvD,EAAE,QAAQ,CAAC,QAAQ,EAAE,oBAAoB,EAAE,MAAM,EAAE,GAAG,CAAC;MAKvD,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,0BAA0B,EAAE;MACxD,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;MACzB,IAAwD;MACxD,MAAM,eAAe,CAAC,GAAG,CAAC;MAC1B;MAIA,IAAI,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,CAAC;MAKvD,IAAI,aAAa,EAAE;MACnB,IAA8B;MAC9B,MAAM,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC;MACnC,MAAM,IAAI,CAAC,iBAAiB,EAAE;MAC9B,QAAQ,UAAU,CAAQ,CAAC;MAC3B;MACA;MACA,GAAG,MAAM;MAIT,IAAI,QAAQ,CAAC,QAAQ,EAAE,oBAAoB,EAAE,MAAM,EAAE,GAAG,CAAC;MAKzD,IAAI,aAAa,EAAE;MACnB;MACA,EAA0C;MAC1C,IAAI,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC;MACpC;MACA,EAA4B;MAC5B,IAAI,IAAI,OAAO,CAAC,iBAAiB,EAAE;MACnC,MAAM,OAAO,CAAC,iBAAiB,EAAE;MACjC,MAAM,OAAO,CAAC,iBAAiB,GAAG,MAAM;MACxC;MACA,IAAI,IAAI,OAAO,CAAC,OAAO,GAAG,GAAG,sBAAsB;MACnD,MAAM,QAAQ,CAAC,MAAM,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MACpD;MACA,IAAI,OAAO,CAAC,OAAO,IAAI,IAAyD;MAChF;MACA,CAAC;MAYD,IAAI,UAAU,GAAG,CAAC,GAAG,KAAK;MAI1B,EAAE,QAAQ,CAAC,MAAM,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;MAIjF,CAAC;MACD,IAAI,QAAQ,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,KAAK;MAC/C,EAAE,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;MACpC,IAAI,IAAI;MACR,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;MAClC,KAAK,CAAC,OAAO,CAAC,EAAE;MAChB,MAAM,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1B;MACA;MACA,EAAE,OAAO,MAAM;MACf,CAAC;MAYD,IAAI,eAAe,GAAG,CAAC,GAAG,KAAK;MAC/B,EAAK,IAAC,EAAE;MACR,EAAE,OAA+B,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,GAAGC,KAAO,CAAC,oBAAoB,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,CAAC,CAA2H;MAC5O,CAAC;;MAcD;MACA,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;MAChF,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,KAAK;MACnD,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;MACjC,EAAE,IAAwB,CAAC,OAAO,EAAE;MACpC,IAAI,MAAM,IAAI,KAAK;MACnB,MAAM,CAAC,gCAAgC,EAAE,OAAO,CAAC,SAAS,CAAC,yYAAyY;MACpc,KAAK;MACL;MACA,EAAE,MAAM,GAAG,GAAsB,OAAO,CAAC,aAAa,CAAM;MAC5D,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;MACvD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO;MAC/B,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;MAClE,EAAE,MAAM,GAAG,kBAAkB;MAC7B,IAAI,MAAM;MACV,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAE/B,CAAC;MACH,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;MACjE,EAAE,MAAM,cAAc,GAAG,MAAM,KAAK,MAAM,IAAI,CAAC,UAAU;MACzD,EAAE,IAAI,CAAsB,EAAE,KAAK,GAAG,CAAC,8BAA8B,IAAI,MAAM,KAAK,MAAM,KAAK,cAAc,EAAE;MAC/G,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;MAwBlD,IAAI,IAAyB,QAAQ,EAAE;MACvC,MAAM,IAA6B,OAAO,CAAC,UAAU,IAAI,KAAK,GAAG,GAAG,qBAAqB;MACzF,QAAQ,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;MACzD,QAAQ,IAAI,YAAY,EAAE;MAC1B,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,eAAe,KAAK;MAChD,YAAY,IAAI;MAChB,cAAc,QAAQ,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;MACjE,aAAa,CAAC,OAAO,CAAC,EAAE;MACxB,cAAc,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;MAClC;MACA,WAAW,CAAC;MACZ;MACA;MACA,MAAM,IAAyB,CAAC,KAAK,IAAI,CAAC,qBAAqB,EAAE,yBAAyB,MAAM,CAAC,oBAAoB;MACrH,QAAQ,IAAI,QAAQ,CAAC,qBAAqB,EAAE;MAC5C,UAAU,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;MAClF,YAAY;MACZ;MACA;MACA,QAAQ,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC;MACtC;MACA;MACA;MACA,CAAC;;MAED;MACA,IAAI,cAAc,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,KAAK;MAC/C,EAAE,IAAI,EAAE,EAAE,EAAE;MACZ,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;MA2BlC,EAAE,IAAsB,OAAO,CAAC,SAAS,IAA6B,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;MAC7G,IAAI,IAA6B,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;MACvE,MAAM,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;MACxC;MACA,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAC9E,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK;MACjD,MAAM,IAAuC,CAAC,WAAW,GAAG,EAAE,eAAe,CAAsB,KAAK,GAAG,CAAC,sBAAsB,WAAW,GAAG,EAAE,aAAa,EAAE;MACjK,QAAQ,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,IAAI,EAAE;MACjH,QAAQ,IAAI,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MAChE,QAAQ,IAAI,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MAChE,QAAQ,IAAI,KAAK,GAAG,CAAC,+BAA+B,CAAC,UAAU,EAAE;MACjE,UAAU,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;MACvD,YAAY,GAAG,GAAG;MAClB,cAAoC;MACpC,gBAAgB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,mBAAmB,CAAC,EAAE;MAClF,kBAAkB,OAAO,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC;MACnD;MACA,gBAAgB,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;MAC5C,gBAAgB,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,cAAc,GAAG,SAAS;MACrE,gBAAgB,IAAI,CAAC,QAAQ,EAAE;MAC/B,gBAAgB,OAAO,QAAQ,CAAC,UAAU,CAAC;MAC3C;MAIA,aAAa;MACb,YAAY,YAAY,EAAE,IAAI;MAC9B,YAAY,UAAU,EAAE;MACxB,WAAW,CAAC;MACZ;MACA,QAAQ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;MACrD,UAAU,GAAG,CAAC,QAAQ,EAAE;MACxB,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;MAgBxC,YAAY,IAAI,UAAU,EAAE;MAC5B,cAAc,MAAM,YAAY,GAAG,WAAW,GAAG,EAAE,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC;MAClH,cAAc,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;MAC/F,gBAAgB,QAAQ,GAAG,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC;MAC/D,eAAe,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,YAAY,EAAE;MAChF,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC;MAClE;MACA,cAAc,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE;MACrC,gBAAgB,kBAAkB;MAClC,kBAAkB,QAAQ;MAC1B,kBAAkB,WAEF;MAChB,eAAe,CAAC;MAChB,cAAc,QAAQ,GAAG,WAAW,GAAG,EAAE,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC;MACxG,cAAc,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC3D,cAAc;MACd;MAKA,YAAkC;MAClC,cAAc,IAAI,CAAC,KAAK,GAAG,CAAC,iCAAiC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,mBAAmB,CAAC,EAAE;MAChI,gBAAgB,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC7D,gBAAgB,IAAI,KAAK,GAAG,CAAC,+BAA+B,CAAC,GAAG,CAAC,cAAc,EAAE;MACjF,kBAAkB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM;MAClD,oBAAoB,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,iBAAiB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;MACzJ,sBAAsB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,QAAQ;MAC/D;MACA,mBAAmB,CAAC;MACpB;MACA,gBAAgB;MAChB;MACA,cAAc,MAAM,YAAY,GAAG,MAAM;MACzC,gBAAgB,MAAM,YAAY,GAAG,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC;MACnE,gBAAgB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,YAAY,EAAE;MAC3E,kBAAkB,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC;MACpE;MACA,gBAAgB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,kBAAkB;MACnE,kBAAkB,QAAQ;MAC1B,kBAAkB,WAEF,CAAC;MACjB,gBAAgB,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;MACnF,eAAe;MACf,cAAc,IAAI,GAAG,CAAC,cAAc,EAAE;MACtC,gBAAgB,YAAY,EAAE;MAC9B,eAAe,MAAM;MACrB,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,CAAC;MAC/D;MACA;MACA;MACA,SAAS,CAAC;MACV,OAAO,MAAM,IAA0C,KAAK,GAAG,CAAC,+BAA+B,WAAW,GAAG,EAAE,eAAe;MAC9H,QAAQ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;MACrD,UAAU,KAAK,CAAC,GAAG,IAAI,EAAE;MACzB,YAAY,IAAI,GAAG;MACnB,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;MACxC,YAAY,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,mBAAmB,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM;MAC5G,cAAc,IAAI,GAAG;MACrB,cAAc,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC;MAC3F,aAAa,CAAC;MACd;MACA,SAAS,CAAC;MACV;MACA,KAAK,CAAC;MACN,IAAI,IAAgC,CAAsB,KAAK,GAAG,CAAC,4BAA4B,EAAE;MACjG,MAAM,MAAM,kBAAkB,mBAAmB,IAAI,GAAG,EAAE;MAC1D,MAAM,SAAS,CAAC,wBAAwB,GAAG,SAAS,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;MAClF,QAAQ,GAAG,CAAC,GAAG,CAAC,MAAM;MACtB,UAAU,IAAI,GAAG;MACjB,UAAU,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;MAC3D,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAIC,KAAO,CAAC,QAAQ,EAAE;MACjE,YAAY,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;MACrC,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC;MACjC,WAAW,MAAM,IAAI,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;MAC7F,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,EAAE;MACtC,YAAY;MACZ,WAAW,MAAM,IAAI,QAAQ,IAAI,IAAI,EAAE;MACvC,YAAY,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;MAC5C,YAAY,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO;MACrE,YAAY,IAAI,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC,8BAA8B,IAAI,MAAM,GAAG,GAAG,uBAAuB,QAAQ,KAAK,QAAQ,EAAE;MAElI,cAAc,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;MAC9E,cAAc,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC;MACvF,cAAc,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,KAAK;MACvE,gBAAgB,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE;MACpD,kBAAkB,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACrF;MACA,eAAe,CAAC;MAChB;MACA,YAAY;MACZ;MACA,UAAU,MAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC;MAC/E,UAAU,QAAQ,GAAG,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAG,QAAQ;MAChG,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAChF,YAAY,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ;MACrC;MACA,SAAS,CAAC;MACV,OAAO;MACP,MAAM,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,IAAI;MAC1C,wBAAwB,IAAI,GAAG,CAAC;MAChC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MACrE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK;MAC7F,YAAY,IAAI,GAAG;MACnB,YAAY,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ;MAC7C,YAAY,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACtD,YAAY,IAAuB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,oBAAoB;MACjE,cAAc,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;MAChG;MACA,YAAY,OAAO,QAAQ;MAC3B,WAAW;MACX,SAAS;MACT,OAAO;MACP;MACA;MACA,EAAE,OAAO,IAAI;MACb,CAAC;;MAED;MACA,IAAI,mBAAmB,GAAG,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,KAAK;MACzE,EAAE,IAAI,IAAI;MACV,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,oCAAoC,CAAC,EAAE;MAClE,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;MACzB,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc;MAC3C,IAAI,IAAwB,QAAQ,EAAE;MACtC,MAAM,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,OAAqB,CAAC;MACnE,MAAM,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,EAAE;MAC9C,QAAQ,MAAM,OAAO,GAAG,UAAU,CAG1B,CAAC;MACT,QAAQ,IAAI,GAAG,MAAM,UAAU;MAC/B,QAAQ,OAAO,EAAE;MACjB,OAAO,MAAM;MACb,QAAQ,IAAI,GAAG,UAAU;MACzB;MACA,MAAM,IAAI,CAAC,IAAI,EAAE;MACjB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;MACrG;MACA,MAAM,IAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;MAC7C,QAAmC;MACnC,UAAU,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;MAC5C;MACA,QAAQ,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,kBAAkB;MACzD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI;MAC7B;MACA,MAAM,MAAM,cAAc,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC;MAC5E,MAA0B;MAC1B,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC;MAC5B;MACA,MAAM,IAAI;MACV,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC;MACzB,OAAO,CAAC,OAAO,CAAC,EAAE;MAClB,QAAQ,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;MAC5B;MACA,MAA0B;MAC1B,QAAQ,OAAO,CAAC,OAAO,IAAI,EAAE;MAC7B;MACA,MAAiC;MACjC,QAAQ,OAAO,CAAC,OAAO,IAAI,GAAG;MAC9B;MACA,MAAM,cAAc,EAAE;MACtB,MAAM,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;MACxD,KAAK,MAAM;MACX,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW;MAC5B,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS;MAClC,MAAM,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,oBAAoB;MAC9F;MACA,IAAI,IAAqB,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;MAC7C,MAAM,IAAI,KAAK;MACf,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;MAC1C,QAAQ,KAAK,GAAG,IAAI,CAAC,KAAK;MAC1B;MASA,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,OAA2B,CAAC;MAC9D,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;MACjC,QAAQ,MAAM,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC;MAQjF,QAAQ,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC;MAC5F,QAAQ,iBAAiB,EAAE;MAC3B;MACA;MACA;MACA,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB;MACvD,EAAE,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC;MACtD,EAAE,IAA4B,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAC9E,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;MAC5C,GAAG,MAAM;MACT,IAAI,QAAQ,EAAE;MACd;MACA,CAAC;MACD,IAAI,qBAAqB,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK;MAC/C,EAAwB;MACxB,IAAI,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,CAAC;MACxD;MACA,CAAC;;MAED;MACA,IAAI,iBAAiB,GAAG,CAAC,GAAG,KAAK;MACjC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC,EAAE;MACvD,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;MACnC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;MACrC,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,mBAAmB,EAAE,OAAO,CAAC,SAAS,CAAC;MAI3E,IAAI,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,oBAAoB,EAAE;MACnD,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;MAe1B,MAA6C;MAC7C,QAAQ,IAAwE;MAChF,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC,2BAA2B,CAAC,0BAA0B,EAAE;MACpF,UAAU,mBAAmB,CAAC,GAAG,CAAC;MAClC;MACA;MACA,MAAgC;MAChC,QAAQ,IAAI,iBAAiB,GAAG,GAAG;MACnC,QAAQ,OAAO,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,IAAI,iBAAiB,CAAC,IAAI,EAAE;MAC3F,UAAU,IAA6J,iBAAiB,CAAC,KAAK,CAAC,EAAE;MACjM,YAAY,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,GAAG,iBAAiB,CAAC;MACtF,YAAY;MACZ;MACA;MACA;MACA,MAAM,IAAkD,OAAO,CAAC,SAAS,EAAE;MAC3E,QAAQ,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK;MAC/E,UAAU,IAAI,WAAW,GAAG,EAAE,eAAe,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC7E,YAAY,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC;MACzC,YAAY,OAAO,GAAG,CAAC,UAAU,CAAC;MAClC,YAAY,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK;MACnC;MACA,SAAS,CAAC;MACV;MACA,MAEa;MACb,QAAQ,mBAAmB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC;MAClD;MACA,KAAK,MAAM;MACX,MAAM,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,WAAkB,CAAC;MACrE,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE;MAC7D,QAAQ,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;MAC1D,OAAO,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE;MACtE,QAAQ,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;MAC/F;MACA;MACA,IAAI,YAAY,EAAE;MAClB;MACA,CAAC;MACD,IAAI,mBAAmB,GAAG,CAAC,GAAG,KAAK;MACnC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;MACrB,IAAI;MACJ;MACA,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa;MAChE,IAA8D;MAC9D,GAAG;MACH,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;MAC9B,EAAE,YAAY,CAAC,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,UAAU,CAAC;MAClD,CAAC;MAID,IAAI,kBAAkB,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK;MAC5C,EAAwB;MACxB,IAAI,QAAQ,CAAC,QAAQ,EAAE,sBAAsB,EAAE,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC;MACvE;MACA,CAAC;MACD,IAAI,oBAAoB,GAAG,OAAO,GAAG,KAAK;MAC1C,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC,EAAE;MACvD,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;MACnC,IAA8B;MAC9B,MAAM,IAAI,OAAO,CAAC,aAAa,EAAE;MACjC,QAAQ,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;MAC/D,QAAQ,OAAO,CAAC,aAAa,GAAG,MAAM;MACtC;MACA;MACA,IAEW,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE;MAClE,MAAM,kBAAkB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;MACrD,KAAK,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE;MACpE,MAAM,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,kBAAkB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;MAC1F;MACA;MACA,EAAE,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MAClC,IAAI,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC;MACjC;MACA,EAAE,IAAI,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;MAC/D,IAAI,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;MAC5C;MACA,CAAC;;MA2HD;AACG,UAAC,aAAa,gBAAG,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,KAAK;MACnD,EAAE,IAAI,EAAE;MAKR,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;MACrB,IAAI,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC;MACvF,IAAI;MACJ;MACA,EAAE,MAAM,YAAY,GAAG,UAAU,CAAgB,CAAC;MAClD,EAAE,MAAM,OAAO,GAAG,EAAE;MACpB,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE;MACvC,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,cAAc;MAC5C,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI;MAChC,EAAE,MAAM,WAAW,mBAAmB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;MACzE,EAAE,MAAM,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;MACxE,EAAE,MAAM,0BAA0B,GAAG,EAAE;MACvC,EAAE,IAAI,eAAe;MACrB,EAAE,IAAI,eAAe,GAAG,IAAI;MAC5B,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC;MAC7B,EAAE,GAAG,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI;MAYvF,EAAE,IAAI,iBAAiB,GAAG,KAAK;MAC/B,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK;MAClC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,KAAK;MACvC,MAAM,IAAI,GAAG;MACb,MAAM,MAAM,OAAO,GAAG;MACtB,QAAQ,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;MAC/B,QAAQ,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;MACjC,QAAQ,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;MACjC,QAAQ,WAAW,EAAE,WAAW,CAAC,CAAC;MAClC,OAAO;MACP,MAAM,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,0BAA0B;MACvD,QAAQ,iBAAiB,GAAG,IAAI;MAChC;MACA,MAA0B;MAC1B,QAAQ,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;MAC1C;MACA,MAAgC;MAChC,QAAQ,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;MAC5C;MACA,MAA2B;MAC3B,QAAQ,OAAO,CAAC,gBAAgB,GAAG,EAAE;MACrC;MACA,MAAiC;MACjC,QAAQ,OAAO,CAAC,UAAU,GAAG,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE;MACtE;MAIA,MAAM,MAAM,OAAO,GAAwG,OAAO,CAAC,SAAS;MAC5I,MAAM,MAAM,WAAW,GAAG,cAAc,WAAW,CAAC;MACpD;MACA,QAAQ,WAAW,CAAC,IAAI,EAAE;MAC1B,UAAU,KAAK,CAAC,IAAI,CAAC;MACrB,UAAU,IAAI,CAAC,2BAA2B,GAAG,KAAK;MAClD,UAAU,IAAI,GAAG,IAAI;MACrB,UAAU,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;MACrC,UAAU,IAAyB,OAAO,CAAC,OAAO,GAAG,CAAC,+BAA+B;MACrF,YAAgC;MAChC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;MACpC,gBAAgB,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;MACpD,eAAe,MAAM;MACrB,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,MAAM,EAAE;MACrD,kBAAkB,MAAM,IAAI,KAAK;MACjC,oBAAoB,CAAC,0CAA0C,EAAE,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,6CAA6C;MACxK,mBAAmB;MACnB;MACA;MACA;MAGA;MACA;MACA,QAAQ,iBAAiB,GAAG;MAC5B,UAAU,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;MAC1C,UAAU,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;MACjD,YAAY,IAAI,CAAC,2BAA2B,GAAG,IAAI;MACnD,YAAY,qBAAqB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,WAAkB,CAAC;MAC5E;MACA,UAAU,IAAI,eAAe,EAAE;MAC/B,YAAY,YAAY,CAAC,eAAe,CAAC;MACzC,YAAY,eAAe,GAAG,IAAI;MAClC;MACA,UAAU,IAAI,eAAe,EAAE;MAC/B,YAAY,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;MACjD,WAAW,MAAM;MACjB,YAAY,GAAG,CAAC,GAAG,CAAC,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;MAClD;MACA;MACA,QAAQ,oBAAoB,GAAG;MAC/B,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAC;MACnD,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM;MACxB,YAAY,IAAI,GAAG;MACnB,YAAY,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;MAC5C,YAAY,MAAM,EAAE,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;MACpF,YAAY,IAAI,EAAE,GAAG,EAAE,EAAE;MACzB,cAAc,0BAA0B,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;MACtD;MACA,YAAY,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,KAAK,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE;MACzJ,cAAc,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK;MAC1C;MACA,WAAW,CAAC;MACZ;MACA,QAAQ,gBAAgB,GAAG;MAC3B,UAAU,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB;MAClD;MACA,OAAO;MACP,MAIa;MACb,QAAuC;MACvC,UAAU,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC;MACpD;MAIA,QAAwC;MACxC,UAAU,oBAAoB,CAAC,WAAW,CAAC,SAAS,CAAC;MACrD;MAIA;MASA,MAAM,OAAO,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC;MAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;MACvE,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;MAC7B,QAAQ,eAAe,CAAC,MAAM;MAC9B,UAAU,OAAO;MACjB,UAAU,cAAc,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;MAChD,SAAS;MACT;MACA,KAAK,CAAC;MACN,GAAG,CAAC;MACJ,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,IAAI,iBAAiB,EAAE;MAC3B,MAAM,UAAU,CAAC,WAAW,IAAI,WAAW;MAC3C;MACA,IAA+F;MAC/F,MAAM,UAAU,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,YAAY;MAC7D;MACA,IAAI,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE;MACrC,MAAM,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;MAChD,MAAM,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;MAC5F,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;MACzB,QAAQ,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;MAC/C;MACA,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,WAAW,GAAG,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;MAC5F;MACA;MACA,EAAE,eAAe,GAAG,KAAK;MACzB,EAAE,IAAI,0BAA0B,CAAC,MAAM,EAAE;MACzC,IAAI,0BAA0B,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;MACtE,GAAG,MAAM;MACT,IAEW;MACX,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;MACjE;MACA;MACA,EAAE,YAAY,EAAE;MAChB;MAOA,IAAI,qBAAqB,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,KAAK;MAChF,EAAE,IAA4B,SAAS,IAAI,GAAG,CAAC,QAAQ,EAAE;MAQzD,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK;MAC7C,MAAM,MAAM,MAAM,GAAgC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAM;MACvG,MAAM,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC;MACxD,MAAM,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC;MAC1C,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;MAC1C,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;MAC5G,KAAK,CAAC;MACN;MACA,CAAC;MACD,IAAI,iBAAiB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,CAAC,EAAE,KAAK;MACzD,EAAE,IAAI,EAAE;MACR,EAAE,IAAI;MACN,IAA0B;MAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,GAAG,GAAG,sBAAsB;MACrD,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;MAC3E,OAAO,MAAM;MACb,QAAQ,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;MAC5F;MACA;MAGA,GAAG,CAAC,OAAO,CAAC,EAAE;MACd,IAAI,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;MAC1C;MACA,CAAC;MACD,IAAI,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK;MAIjD,EAAE,IAAwC,KAAK,GAAG,CAAC,qBAAqB;MACxE,IAAI,OAAO,GAAG;MACd;MACA,EAAE,IAAsC,KAAK,GAAG,EAAE,mBAAmB;MACrE,IAAI,OAAO,GAAG,CAAC,IAAI;MACnB;MAIA,EAAE,OAAO,GAAG;MACZ,CAAC;MACD,IAAI,gBAAgB,GAAG,CAAC,KAAK,KAAK,uBAAuB,GAAG;MAC5D,EAAE,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC;MAC1C,EAAE,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,oBAAoB;MACzC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC;;MAEnC;AACG,UAAC,QAAQ,gBAAG,CAAC,KAAK,KAAK,GAAG,CAAC,OAAO,GAAG;;;;;;;;", "x_google_ignoreList": [2]}