{"version": 3, "file": "p-D86gUxBu.system.js", "sources": ["src/components/card/card-body/card-body.tsx"], "sourcesContent": ["import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-body',\n  shadow: true,\n})\nexport class CardBody implements ComponentInterface {\n  render() {\n    return (\n      <bds-grid>\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;kBAMa,QAAQ,4BAAA,MAAA;;;;gBACnB,MAAM,GAAA;oBACJ,QACE,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACE,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACC;;;;;;;;;;"}