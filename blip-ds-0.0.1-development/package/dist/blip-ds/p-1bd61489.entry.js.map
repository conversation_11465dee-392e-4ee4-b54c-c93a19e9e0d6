{"version": 3, "names": ["chipClickableCss", "ChipClickable", "constructor", "hostRef", "this", "visible", "color", "size", "clickable", "close", "disabled", "dataTest", "dtButtonClose", "handleClickKey", "event", "key", "preventDefault", "chipClickableClick", "emit", "handleClick", "handleCloseChip", "chipClickableClose", "id", "element", "handleCloseKey", "getSizeAvatarChip", "getSizeIconChip", "render", "h", "Host", "class", "chip_clickable", "onClick", "bind", "onKeyDown", "tabindex", "icon", "avatar", "name", "thumbnail", "variant", "bold", "theme", "tooltipCss", "<PERSON><PERSON><PERSON>", "isMouseOver", "tooltipText", "position", "max<PERSON><PERSON><PERSON>", "invisible", "setVisibility", "value", "componentWillLoad", "textVerify", "replace", "maxWidtTooltip", "tooltipTextChanged", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "styleTooltip", "onMouseEnter", "onMouseLeave", "tooltip__tip", "style", "tooltip__tip__text"], "sources": ["src/components/chip-clickable/chip-clickable.scss?tag=bds-chip-clickable&encapsulation=shadow", "src/components/chip-clickable/chip-clickable.tsx", "src/components/tooltip/tooltip.scss?tag=bds-tooltip&encapsulation=shadow", "src/components/tooltip/tooltip.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  height: max-content;\n  border-radius: 4px;\n  box-sizing: border-box;\n  max-width: 100%;\n\n  .chip_clickable {\n    display: flex;\n    min-width: 32px;\n    width: fit-content;\n    height: 24px;\n    border-radius: 12px;\n    padding: 2px 6px;\n    align-items: center;\n    box-sizing: border-box;\n    justify-content: center;\n    position: relative;\n    z-index: 1; // Keep chips behind other screen components\n    flex-shrink: 0; // Prevent chips from shrinking in flex layout\n\n    &--container-text {\n      &--full {\n        width: 100%;\n      }\n      &--min {\n        width: calc(100% - 36px);\n      }\n      &--half {\n        width: calc(100% - 16px);\n      }\n    }\n\n    &--hide {\n      display: none;\n      padding: 0;\n      border: none;\n    }\n\n    .chip_focus:focus {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      padding: 2px;\n      border-radius: 4px;\n      outline: $color-focus solid 2px;\n    }\n\n    &--click {\n      cursor: pointer;\n      .chip_darker {\n        opacity: 0;\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        border-radius: inherit;\n        z-index: 1;\n        backdrop-filter: brightness(1);\n        box-sizing: border-box;\n      }\n    }\n    &--click:hover {\n      .chip_darker {\n        opacity: 1;\n        backdrop-filter: brightness(0.9);\n      }\n    }\n    &--click:active {\n      .chip_darker {\n        opacity: 1;\n        backdrop-filter: brightness(0.8);\n      }\n    }\n    &--disabled {\n      cursor: default;\n      background-color: $color-surface-3;\n      .chip_clickable--icon {\n        color: $color-content-default;\n      }\n      .chip_clickable--text {\n        color: $color-content-default;\n      }\n      .chip_clickable--close {\n        cursor: default;\n      }\n    }\n\n    &--text {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      margin: 0;\n      padding: 0 2px;\n      z-index: 2;\n      font-family: $font-family;\n      line-height: 1;\n    }\n    &--icon {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      padding-right: 2px;\n      z-index: 2;\n    }\n    &--close {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      padding-left: 2px;\n      mix-blend-mode: hard-light;\n      opacity: 0.5;\n      z-index: 2;\n      position: relative;\n      cursor: pointer;\n\n      .close_focus:focus {\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        left: -2px;\n        border-radius: 4px;\n        outline: $color-focus solid 2px;\n      }\n    }\n    &--tall {\n      height: 32px;\n      border-radius: 16px;\n      padding: 4px 8px;\n\n      .chip_clickable--text {\n        height: 20px;\n        line-height: 1.1;\n      }\n      .chip_clickable--icon {\n        height: 20px;\n        padding-right: 4px;\n      }\n      .chip_clickable--close {\n        height: 20px;\n        padding-left: 4px;\n      }\n    }\n    &--default {\n      background-color: $color-system;\n      color: $color-content-din;\n    }\n    &--info {\n      background-color: $color-info;\n      color: $color-content-din;\n    }\n    &--success {\n      background-color: $color-success;\n      color: $color-content-din;\n    }\n    &--warning {\n      background-color: $color-warning;\n      color: $color-content-din;\n    }\n    &--danger {\n      background-color: $color-error;\n      color: $color-content-din;\n    }\n    &--outline {\n      border: 1px solid $color-border-1;\n      color: $color-content-default;\n    }\n    &:focus-visible {\n      outline: none;\n    }\n  }\n}\n", "import { Component, Host, h, Prop, Event, EventEmitter, Element, State } from '@stencil/core';\n\nexport type ColorChipClickable = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline';\nexport type Size = 'standard' | 'tall';\n\n@Component({\n  tag: 'bds-chip-clickable',\n  styleUrl: 'chip-clickable.scss',\n  shadow: true,\n})\nexport class ChipClickable {\n  @Element() private element: HTMLElement;\n  @State() visible = true;\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for add avatar left container. Uses the bds-avatar component.\n   */\n  @Prop() avatar?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipClickable = 'default';\n  /**\n   * used for change the size chip. Uses one of them.\n   */\n  @Prop() size?: Size = 'standard';\n  /**\n   * it makes the chip clickable.\n   */\n  @Prop() clickable?: boolean = false;\n  /**\n   * used for delete the chip.\n   */\n  @Prop() close?: boolean = false;\n  /**\n   * the chip gone stay disabled while this prop be true.\n   */\n  @Prop() disabled?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   *  Triggered after a mouse click on close icon, return id element. Only fired when close is true.\n   */\n  @Event() chipClickableClose: EventEmitter;\n  @Event() chipClickableClick: EventEmitter;\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleClick(event) {\n    if (!this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleCloseChip(event) {\n    event.preventDefault();\n    this.chipClickableClose.emit({ id: this.element.id });\n  }\n\n  private handleCloseKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClose.emit({ id: this.element.id });\n    }\n  }\n\n  private getSizeAvatarChip() {\n    if (this.size === 'tall') {\n      return 'extra-small';\n    } else return 'micro';\n  }\n\n  private getSizeIconChip() {\n    if (this.size === 'tall') {\n      return 'medium';\n    } else return 'x-small';\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_clickable: true,\n            [`chip_clickable--${this.color}`]: true && !this.disabled,\n            [`chip_clickable--${this.size}`]: true,\n            'chip_clickable--hide': !this.visible,\n            'chip_clickable--click': this.clickable,\n            'chip_clickable--disabled': this.disabled,\n          }}\n          onClick={this.handleClick.bind(this)}\n          data-test={this.dataTest}\n        >\n          {this.clickable && !this.disabled && (\n            <div class=\"chip_focus\" onKeyDown={this.handleClickKey.bind(this)} tabindex=\"0\"></div>\n          )}\n          {this.clickable && !this.disabled && <div class=\"chip_darker\"></div>}\n          {this.icon && !this.avatar && (\n            <div class=\"chip_clickable--icon\">\n              <bds-icon size={this.getSizeIconChip()} name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.avatar && (\n            <div class=\"chip_clickable--avatar\">\n              <bds-avatar size={this.getSizeAvatarChip()} thumbnail={this.avatar}></bds-avatar>\n            </div>\n          )}\n          <div\n            class={\n              this.close && (this.icon || this.avatar)\n                ? `chip_clickable--container-text--min`\n                : !this.close && !this.icon && !this.avatar\n                  ? `chip_clickable--container-text--full`\n                  : `chip_clickable--container-text--half`\n            }\n          >\n            <bds-typo no-wrap=\"true\" class=\"chip_clickable--text\" variant=\"fs-12\" bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n          {this.close && (\n            <div class=\"chip_clickable--close\" data-test={this.dtButtonClose} onClick={this.handleCloseChip.bind(this)}>\n              {!this.disabled && (\n                <div class=\"close_focus\" onKeyDown={this.handleCloseKey.bind(this)} tabindex=\"0\"></div>\n              )}\n              <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n            </div>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n$tooltip-horizontal-margin: 10px;\n$tooltip-border-width: 6px;\n\n.tooltip {\n  &__wrapper {\n    display: inline-block;\n    position: relative;\n  }\n\n  &__tip {\n    position: absolute;\n    left: 50%;\n    border-radius: 8px;\n    padding: 8px;\n    background: $color-content-default;\n    z-index: $zindex-modal;\n    white-space: normal;\n    width: max-content;\n    min-width: 32px;\n    max-width: 320px;\n    box-shadow: $shadow-2;\n    visibility: hidden;\n    transition: all 0.2s ease-in-out;\n    box-sizing: border-box;\n    cursor: default;\n    &--visible {\n      visibility: visible;\n    }\n\n    &::before {\n      content: '';\n      left: 50%;\n      border: solid transparent;\n      height: 0;\n      width: 0;\n      position: absolute;\n      pointer-events: none;\n      margin-left: -$tooltip-border-width;\n      border-width: $tooltip-border-width;\n    }\n\n    &--top-center,\n    &--top-left,\n    &--top-right {\n      bottom: calc(100% + 10px);\n      &::before {\n        top: 100%;\n        border-top-color: $color-content-default;\n      }\n    }\n\n    &--top-left {\n      left: 0;\n      transform: translateX(-15%);\n      &::before {\n        left: calc(15% + #{$tooltip-border-width});\n      }\n    }\n\n    &--top-right {\n      left: initial;\n      right: 0;\n      transform: translateX(15%);\n      &::before {\n        left: calc(85% - #{$tooltip-border-width});\n      }\n    }\n\n    &--bottom-center,\n    &--top-center {\n      transform: translateX(-50%);\n    }\n\n    &--left-center,\n    &--right-center {\n      transform: translateX(0) translateY(-50%);\n    }\n\n    &--right-center,\n    &--right-top,\n    &--right-bottom {\n      left: calc(100% + #{$tooltip-horizontal-margin});\n      top: 50%;\n      &::before {\n        left: -5px;\n        top: 50%;\n        transform: translateX(0) translateY(-50%);\n        border-right-color: $color-content-default;\n      }\n    }\n\n    &--right-top {\n      top: 0;\n      &::before {\n        top: 40%;\n      }\n    }\n\n    &--right-bottom {\n      top: initial;\n      bottom: 0;\n      &::before {\n        top: 60%;\n      }\n    }\n\n    &--bottom-center,\n    &--bottom-right,\n    &--bottom-left {\n      top: calc(100% + 10px);\n      &::before {\n        bottom: 100%;\n        border-bottom-color: $color-content-default;\n      }\n    }\n\n    &--bottom-right {\n      left: initial;\n      right: 0;\n      transform: translateX(15%);\n      &::before {\n        left: calc(85% - #{$tooltip-border-width});\n      }\n    }\n\n    &--bottom-left {\n      left: 0;\n      transform: translateX(-15%);\n      &::before {\n        left: calc(15% + #{$tooltip-border-width});\n      }\n    }\n\n    &--left-center,\n    &--left-top,\n    &--left-bottom {\n      left: auto;\n      right: calc(100% + #{$tooltip-horizontal-margin});\n      top: 50%;\n      &::before {\n        left: auto;\n        right: -11px;\n        top: 50%;\n        transform: translateX(0) translateY(-50%);\n        border-left-color: $color-content-default;\n      }\n    }\n\n    &--left-top {\n      top: 0;\n      &::before {\n        top: 40%;\n      }\n    }\n\n    &--left-bottom {\n      top: initial;\n      bottom: 0;\n      &::before {\n        top: 60%;\n      }\n    }\n    &__text {\n      pre {\n        margin: 0;\n        display: flex;\n        font-family: inherit;\n        white-space: break-spaces;\n      }\n\n      .text {\n        color: $color-surface-1;\n      }\n    }\n  }\n}\n", "import { Component, h, Method, Prop, State, Watch } from '@stencil/core';\n\nexport type TooltipPostionType =\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom';\n\n@Component({\n  tag: 'bds-tooltip',\n  styleUrl: 'tooltip.scss',\n  shadow: true,\n})\nexport class Tooltip {\n  /**\n   * Used to set tooltip visibility\n   */\n  @State() isMouseOver = false;\n  @State() textVerify: string;\n  @State() maxWidtTooltip: string;\n\n  /**\n   * Used to set tooltip text\n   */\n  @Prop({ mutable: true }) tooltipText = 'Tooltip';\n\n  /**\n   * Used to disable tooltip when the button are avalible\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Used to set tooltip position\n   */\n  @Prop() position: TooltipPostionType = 'left-center';\n\n  /**\n   * Used to set tooltip max width\n   */\n  @Prop() maxWidth?: string = '320px';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async visible() {\n    this.isMouseOver = true;\n  }\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async invisible() {\n    this.isMouseOver = false;\n  }\n\n  private setVisibility(value: boolean) {\n    if (this.disabled) {\n      this.isMouseOver = false;\n      return;\n    }\n    this.isMouseOver = value;\n  }\n\n  componentWillLoad() {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  @Watch('tooltipText')\n  tooltipTextChanged(): void {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n  }\n\n  @Watch('maxWidth')\n  maxWidthChanged(): void {\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  render() {\n    const styleTooltip = {\n      maxWidth: this.maxWidtTooltip,\n    };\n    return (\n      <div class=\"tooltip__wrapper\">\n        <div\n          onMouseEnter={() => this.setVisibility(true)}\n          onMouseLeave={() => this.setVisibility(false)}\n          data-test={this.dataTest}\n        >\n          <slot />\n        </div>\n        <div\n          class={{\n            tooltip__tip: true,\n            [`tooltip__tip--${this.position}`]: true,\n            'tooltip__tip--visible': this.isMouseOver,\n          }}\n          style={styleTooltip}\n        >\n          <div class={{ tooltip__tip__text: true }}>\n            <pre>\n              <bds-typo class=\"text\" no-wrap=\"false\" variant=\"fs-12\">\n                {this.textVerify}\n              </bds-typo>\n            </pre>\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAmB,iyH,MCUZC,EAAa,MAL1B,WAAAC,CAAAC,G,oHAOWC,KAAOC,QAAG,KAYXD,KAAKE,MAAwB,UAI7BF,KAAIG,KAAU,WAIdH,KAASI,UAAa,MAItBJ,KAAKK,MAAa,MAIlBL,KAAQM,SAAa,MAIrBN,KAAQO,SAAY,KAMpBP,KAAaQ,cAAY,IAmGlC,CA5FS,cAAAC,CAAeC,GACrB,IAAKA,EAAMC,MAAQ,SAAWD,EAAMC,MAAQ,OAASX,KAAKM,SAAU,CAClEI,EAAME,iBACNZ,KAAKa,mBAAmBC,M,EAIpB,WAAAC,CAAYL,GAClB,IAAKV,KAAKM,SAAU,CAClBI,EAAME,iBACNZ,KAAKa,mBAAmBC,M,EAIpB,eAAAE,CAAgBN,GACtBA,EAAME,iBACNZ,KAAKiB,mBAAmBH,KAAK,CAAEI,GAAIlB,KAAKmB,QAAQD,I,CAG1C,cAAAE,CAAeV,GACrB,IAAKA,EAAMC,MAAQ,SAAWD,EAAMC,MAAQ,OAASX,KAAKM,SAAU,CAClEI,EAAME,iBACNZ,KAAKiB,mBAAmBH,KAAK,CAAEI,GAAIlB,KAAKmB,QAAQD,I,EAI5C,iBAAAG,GACN,GAAIrB,KAAKG,OAAS,OAAQ,CACxB,MAAO,a,MACF,MAAO,O,CAGR,eAAAmB,GACN,GAAItB,KAAKG,OAAS,OAAQ,CACxB,MAAO,Q,MACF,MAAO,S,CAGhB,MAAAoB,GACE,OACEC,EAACC,EAAI,CAAAd,IAAA,4CACHa,EAAA,OAAAb,IAAA,2CACEe,MAAO,CACLC,eAAgB,KAChB,CAAC,mBAAmB3B,KAAKE,UAAmBF,KAAKM,SACjD,CAAC,mBAAmBN,KAAKG,QAAS,KAClC,wBAAyBH,KAAKC,QAC9B,wBAAyBD,KAAKI,UAC9B,2BAA4BJ,KAAKM,UAEnCsB,QAAS5B,KAAKe,YAAYc,KAAK7B,MACpB,YAAAA,KAAKO,UAEfP,KAAKI,YAAcJ,KAAKM,UACvBkB,EAAA,OAAAb,IAAA,2CAAKe,MAAM,aAAaI,UAAW9B,KAAKS,eAAeoB,KAAK7B,MAAO+B,SAAS,MAE7E/B,KAAKI,YAAcJ,KAAKM,UAAYkB,EAAK,OAAAb,IAAA,2CAAAe,MAAM,gBAC/C1B,KAAKgC,OAAShC,KAAKiC,QAClBT,EAAA,OAAAb,IAAA,2CAAKe,MAAM,wBACTF,EAAA,YAAAb,IAAA,2CAAUR,KAAMH,KAAKsB,kBAAmBY,KAAMlC,KAAKgC,QAGtDhC,KAAKiC,QACJT,EAAK,OAAAb,IAAA,2CAAAe,MAAM,0BACTF,EAAA,cAAAb,IAAA,2CAAYR,KAAMH,KAAKqB,oBAAqBc,UAAWnC,KAAKiC,UAGhET,EAAA,OAAAb,IAAA,2CACEe,MACE1B,KAAKK,QAAUL,KAAKgC,MAAQhC,KAAKiC,QAC7B,uCACCjC,KAAKK,QAAUL,KAAKgC,OAAShC,KAAKiC,OACjC,uCACA,wCAGRT,EAAA,YAAAb,IAAA,qDAAkB,OAAOe,MAAM,uBAAuBU,QAAQ,QAAQC,KAAK,QACzEb,EAAA,QAAAb,IAAA,+CAGHX,KAAKK,OACJmB,EAAA,OAAAb,IAAA,2CAAKe,MAAM,wBAAuB,YAAY1B,KAAKQ,cAAeoB,QAAS5B,KAAKgB,gBAAgBa,KAAK7B,QACjGA,KAAKM,UACLkB,EAAA,OAAAb,IAAA,2CAAKe,MAAM,cAAcI,UAAW9B,KAAKoB,eAAeS,KAAK7B,MAAO+B,SAAS,MAE/EP,EAAA,YAAAb,IAAA,2CAAUR,KAAK,UAAUmC,MAAM,QAAQJ,KAAK,Y,0CC9I1D,MAAMK,EAAa,+/G,MCqBNC,EAAO,MALpB,WAAA1C,CAAAC,G,UASWC,KAAWyC,YAAG,MAOEzC,KAAW0C,YAAG,UAKd1C,KAAQM,SAAI,MAK7BN,KAAQ2C,SAAuB,cAK/B3C,KAAQ4C,SAAY,QAKpB5C,KAAQO,SAAY,IAyE7B,CAnEC,aAAMN,GACJD,KAAKyC,YAAc,I,CAOrB,eAAMI,GACJ7C,KAAKyC,YAAc,K,CAGb,aAAAK,CAAcC,GACpB,GAAI/C,KAAKM,SAAU,CACjBN,KAAKyC,YAAc,MACnB,M,CAEFzC,KAAKyC,YAAcM,C,CAGrB,iBAAAC,GACEhD,KAAKiD,WAAajD,KAAK0C,YAAc1C,KAAK0C,YAAYQ,QAAQ,SAAU,QAAU,GAClFlD,KAAKmD,eAAiBnD,KAAK4C,Q,CAI7B,kBAAAQ,GACEpD,KAAKiD,WAAajD,KAAK0C,YAAc1C,KAAK0C,YAAYQ,QAAQ,SAAU,QAAU,E,CAIpF,eAAAG,GACErD,KAAKmD,eAAiBnD,KAAK4C,Q,CAG7B,MAAArB,GACE,MAAM+B,EAAe,CACnBV,SAAU5C,KAAKmD,gBAEjB,OACE3B,EAAA,OAAAb,IAAA,2CAAKe,MAAM,oBACTF,EACE,OAAAb,IAAA,2CAAA4C,aAAc,IAAMvD,KAAK8C,cAAc,MACvCU,aAAc,IAAMxD,KAAK8C,cAAc,OAAM,YAClC9C,KAAKO,UAEhBiB,EAAA,QAAAb,IAAA,8CAEFa,EAAA,OAAAb,IAAA,2CACEe,MAAO,CACL+B,aAAc,KACd,CAAC,iBAAiBzD,KAAK2C,YAAa,KACpC,wBAAyB3C,KAAKyC,aAEhCiB,MAAOJ,GAEP9B,EAAA,OAAAb,IAAA,2CAAKe,MAAO,CAAEiC,mBAAoB,OAChCnC,EAAA,OAAAb,IAAA,4CACEa,EAAA,YAAAb,IAAA,2CAAUe,MAAM,OAAM,UAAS,QAAQU,QAAQ,SAC5CpC,KAAKiD,e", "ignoreList": []}