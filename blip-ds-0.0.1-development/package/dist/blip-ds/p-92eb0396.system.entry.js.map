{"version": 3, "names": ["menuSeparationCss", "BdsMenuSeparation", "exports", "class_1", "hostRef", "this", "value", "size", "prototype", "render", "h", "key", "class", "_a", "menuseparation", "concat", "variant", "tag"], "sources": ["src/components/menu/menu-separation/menu-separation.scss?tag=bds-menu-separation&encapsulation=shadow", "src/components/menu/menu-separation/menu-separation.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuseparation {\n  display: flex;\n  align-items: center;\n  padding: 0 16px;\n\n  &__small {\n    margin: 8px 0;\n  }\n  &__default {\n    margin: 12px 0;\n  }\n  &__large {\n    margin: 16px 0;\n  }\n\n  & .dividor-item{\n    height: 1px;\n    width: 100%;\n    background-color: $color-neutral-medium-wave;\n  }\n\n  & .title-item{\n    margin-right: 8px;\n    margin-top: -4px;\n    color: $color-neutral-medium-elephant;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\n\nexport type divisorSize = 'small' | 'default' | 'large';\n\n@Component({\n  tag: 'bds-menu-separation',\n  styleUrl: 'menu-separation.scss',\n  shadow: true,\n})\nexport class BdsMenuSeparation {\n  /**\n   * Value. Used to insert a title to the divider.\n   */\n  @Prop() value?: string = null;\n  /**\n   * Size. Used to set the size of the divider.\n   */\n  @Prop() size?: string = null;\n  render() {\n    return (\n      <div\n        class={{\n          menuseparation: true,\n          [`menuseparation__${this.size}`]: true,\n        }}\n      >\n        {this.value && (\n          <bds-typo class=\"title-item\" variant=\"fs-10\" tag=\"span\">\n            {this.value}\n          </bds-typo>\n        )}\n        <div class=\"dividor-item\"></div>\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAoB,oX,ICSbC,EAAiBC,EAAA,iCAL9B,SAAAC,EAAAC,G,UASUC,KAAKC,MAAY,KAIjBD,KAAIE,KAAY,IAkBzB,CAjBCJ,EAAAK,UAAAC,OAAA,W,MACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,OAAKC,EAAA,CACHC,eAAgB,MAChBD,EAAC,mBAAAE,OAAmBV,KAAKE,OAAS,K,IAGnCF,KAAKC,OACJI,EAAA,YAAAC,IAAA,2CAAUC,MAAM,aAAaI,QAAQ,QAAQC,IAAI,QAC9CZ,KAAKC,OAGVI,EAAA,OAAAC,IAAA,2CAAKC,MAAM,iB,WAtBW,I", "ignoreList": []}