{"version": 3, "file": "bds-card-footer.entry.esm.js", "sources": ["src/components/card/card-footer/card-footer.scss?tag=bds-card-footer&encapsulation=shadow", "src/components/card/card-footer/card-footer.tsx"], "sourcesContent": [":host {\n    width: 100%;\n}", "import { Component, ComponentInterface, h, Prop } from '@stencil/core';\n\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n\n@Component({\n  tag: 'bds-card-footer',\n  styleUrl: 'card-footer.scss',\n  shadow: true,\n})\nexport class CardFooter implements ComponentInterface {\n  /**\n   * Prop for internal elements alignment. Will follow the same values of css.\n   */\n  @Prop() align?: justifyContent = 'flex-end';\n  render() {\n    return (\n      <bds-grid xxs=\"12\" direction=\"row\" gap=\"2\" justifyContent={this.align}>\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,aAAa,GAAG,mBAAmB;;MCS5B,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAoB,UAAU;AAQ5C;IAPC,MAAM,GAAA;AACJ,QAAA,QACE,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAC,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,GAAG,EAAC,GAAG,EAAC,cAAc,EAAE,IAAI,CAAC,KAAK,EAAA,EACnE,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACC;;;;;;;"}