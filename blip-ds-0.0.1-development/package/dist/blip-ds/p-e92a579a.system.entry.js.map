{"version": 3, "names": ["sliderCss", "Slide<PERSON>", "exports", "class_1", "hostRef", "_this", "this", "inputValue", "_b", "_a", "value", "toString", "min", "markers", "label", "type", "dataTest", "refInputSlide", "el", "inputSlide", "refBdsTooltip", "bdsTooltip", "refProgressBar", "progressBar", "valuePercent", "element", "input", "parseInt", "max", "val", "percentage", "onInputSlide", "ev", "target", "style", "width", "concat", "valueName", "emiterChange", "<PERSON><PERSON><PERSON><PERSON>", "length", "name", "bdsChange", "emit", "onInputMouseEnter", "visible", "classList", "add", "onInputMouseLeave", "invisible", "remove", "internalOptions", "find", "item", "prototype", "componentWillLoad", "dataMarkers", "JSON", "parse", "arrayToSteps", "step", "Number", "isInteger", "componentDidLoad", "componentDidRender", "componentDidUpdate", "int", "numberToCalc", "valueSteps", "i", "push", "map", "term", "render", "h", "Host", "key", "ref", "class", "input_slide", "onInput", "onMouseEnter", "onMouseLeave", "_c", "_d", "position", "_e", "index", "variant"], "sources": ["src/components/slider/slider.scss?tag=bds-slider&encapsulation=shadow", "src/components/slider/slider.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  position: relative;\n  display: flex;\n  width: 100%;\n  height: 32px;\n}\n\n.track-bg {\n  position: absolute;\n  display: flex;\n  justify-content: space-between;\n  inset: 0 8px;\n  pointer-events: none;\n\n  .progress-bar {\n    position: absolute;\n    height: 4px;\n    border-radius: 1rem;\n    z-index: 2;\n    &-liner {\n      background-color: $color-primary;\n    }\n    &-tooltip {\n      position: absolute;\n      top: -6px;\n      right: -.5rem;\n    }\n    &-thumb {\n      position: relative;\n      width: 1rem;\n      height: 1rem;\n      border-radius: 1rem;\n      background-color: $color-primary;\n      z-index: 0;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        background-color: $color-hover;\n        border-radius: 1rem;\n        transition: all .3s ease-in-out;\n      }\n    }\n\n    &-hover {\n      .progress-bar{\n        &-thumb {\n          &::before {\n            transform: scale(2);\n          }\n        }\n      }\n    }\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    height: 4px;\n    background-color: $color-content-default;\n    opacity: .16;\n    border-radius: 1rem;\n  }\n\n  & .step {\n    position: relative;\n    width: 2px;\n    height: 8px;\n    display: flex;\n    justify-content: center;\n    background-color: $color-content-disable;\n    border-bottom-left-radius: 1rem;\n    border-bottom-right-radius: 1rem;\n    & .label-step {\n      margin-top: 1rem;\n    }\n  }\n}\n\n.element-min {\n  position: relative;\n  height: 4px;\n  background-color: $color-primary;\n  border-top-left-radius: 1rem;\n  border-bottom-left-radius: 1rem;\n}\n.element-max {\n  position: relative;\n  height: 4px;\n  border-top-right-radius: 1rem;\n  border-bottom-right-radius: 1rem;\n}\n\n.input_slide {\n  -webkit-appearance: none;\n  appearance: none;\n  margin: 0;\n  background: transparent;\n  cursor: pointer;\n  width: 100%;\n  height: 4px;\n  position: relative;\n  border-radius: 1rem;\n  background: transparent;\n  color: -internal-light-dark(transparent, transparent);\n\n  &.has_min {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n    margin-left: 0;\n  }\n  \n  &.has_max {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  &:hover{ \n    .input_slide::-webkit-slider-thumb,\n    .input_slide::-moz-range-thumb {\n      -webkit-appearance: none;\n    }\n  }\n\n}\n\n/* Thumb: webkit */\n.input_slide::-webkit-slider-thumb,\n.input_slide::-moz-range-thumb {\n  -webkit-appearance: none;\n  position: relative;\n  height: 16px;\n  width: 16px;\n  border-radius: 50%;\n  border: none;\n}\n\n\n\n.group_slide {\n  position: relative;\n  width: 100%;\n\n  & .input_slide {\n    width: inherit;\n    position: absolute;\n  }\n  \n  & .input_slide_start {\n    left: 0;\n  }\n  & .input_slide_end {\n    right: 0;\n  }\n  & .input_slide::-webkit-slider-thumb,\n  .input_slide::-moz-range-thumb  {\n    -webkit-appearance: none;\n  }\n}\n", "import { Component, Host, h, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { typeRange, StepOption } from './slider-interface';\n\n@Component({\n  tag: 'bds-slider',\n  styleUrl: 'slider.scss',\n  shadow: true,\n})\nexport class Slider {\n  private inputSlide?: HTMLInputElement;\n  private bdsTooltip?: HTMLBdsTooltipElement;\n  private progressBar?: HTMLElement;\n\n  @State() stepArray?: StepOption[];\n  @State() internalOptions?: StepOption[];\n  @State() inputValue?: string = this.value?.toString() ?? (this.min ? this.min.toString() : '0');\n\n  /**\n   * Step, property to insert steps into the input range.\n   */\n  @Prop() step?: number;\n\n  /**\n   * Min, property to set the minimum value of the range.\n   */\n  @Prop() min?: number;\n\n  /**\n   * Max, property to set the maximum value of the range.\n   */\n  @Prop() max?: number;\n\n  /**\n   * Value, prop to define value of input.\n   */\n  @Prop() value?: number = this.min ? this.min : 0;\n\n  /**\n   * Markers, Prop to enable markers.\n   */\n  @Prop() markers?: boolean = false;\n\n  /**\n   * Label, Prop to enable Label.\n   */\n  @Prop() label?: boolean = false;\n  /**\n   * Type, prop to select type of slider.\n   */\n  @Prop() type?: typeRange = 'fill';\n\n  /**\n   * Data Markers, prop to select ype of markers.\n   */\n  @Prop() dataMarkers?: string | StepOption[];\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * bdsChange. Event to return selected date value.\n   */\n  @Event() bdsChange?: EventEmitter;\n\n  componentWillLoad() {\n    if (this.dataMarkers) {\n      if (typeof this.dataMarkers === 'string') {\n        this.internalOptions = JSON.parse(this.dataMarkers);\n        this.stepArray = this.internalOptions;\n      } else {\n        this.internalOptions = this.dataMarkers;\n        this.stepArray = this.internalOptions;\n      }\n    } else {\n      this.stepArray = this.arrayToSteps(\n        (this.max - this.min) / this.step,\n        Number.isInteger((this.max - this.min) / this.step),\n      ) as StepOption[];\n    }\n  }\n\n  componentDidLoad() {\n    this.progressBar.style.width = `${this.valuePercent(this.inputSlide)}%`;\n  }\n  componentDidRender() {\n    if (this.internalOptions) {\n      this.inputSlide.min = '0';\n      this.inputSlide.max = `${this.internalOptions.length - 1}`;\n      this.inputSlide.step = '1';\n    } else {\n      this.inputSlide.min = this.min ? `${this.min}` : '';\n      this.inputSlide.max = this.max ? `${this.max}` : '';\n      this.inputSlide.step = this.step ? `${this.step}` : '';\n    }\n  }\n\n  componentDidUpdate() {\n    this.progressBar.style.width = `${this.valuePercent(this.inputSlide)}%`;\n    const valueName = this.emiterChange(parseInt(this.inputSlide.value));\n    this.inputValue = this.stepArray.length > 0 ? valueName.name : this.inputSlide.value;\n  }\n\n  private refInputSlide = (el: HTMLInputElement): void => {\n    this.inputSlide = el;\n  };\n\n  private refBdsTooltip = (el: HTMLBdsTooltipElement): void => {\n    this.bdsTooltip = el;\n  };\n\n  private refProgressBar = (el: HTMLElement): void => {\n    this.progressBar = el;\n  };\n\n  private valuePercent = (element: HTMLInputElement | null): number => {\n    const input = element;\n    const min = input.min ? parseInt(input.min) : 0;\n    const max = parseInt(input.max);\n    const val = parseInt(input.value);\n    const percentage = ((val - min) * 100) / (max - min);\n    return percentage;\n  };\n\n  private onInputSlide = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    this.progressBar.style.width = `${this.valuePercent(input)}%`;\n    const valueName = this.emiterChange(parseInt(input.value));\n    this.inputValue = this.stepArray.length > 0 ? valueName.name : input.value;\n    this.bdsChange.emit(valueName);\n  };\n\n  private onInputMouseEnter = (): void => {\n    this.bdsTooltip.visible();\n    this.progressBar.classList.add(`progress-bar-hover`);\n  };\n\n  private onInputMouseLeave = (): void => {\n    this.bdsTooltip.invisible();\n    this.progressBar.classList.remove(`progress-bar-hover`);\n  };\n\n  private emiterChange = (value: number): StepOption => {\n    if (this.internalOptions) {\n      return this.stepArray[value];\n    } else {\n      return this.stepArray.find((item) => parseInt(item.name) === value);\n    }\n  };\n\n  private arrayToSteps(value: number, int: boolean): unknown {\n    const numberToCalc = int ? value + 1 : value;\n    const valueSteps = [];\n    for (let i = 0; i < numberToCalc; i++) {\n      valueSteps.push(i);\n    }\n    return valueSteps.map((term) => ({ value: term, name: term * this.step + this.min }));\n  }\n\n  render() {\n    return (\n      <Host>\n        <input\n          ref={this.refInputSlide}\n          type=\"range\"\n          class={{\n            input_slide: true,\n          }}\n          value={this.value as number}\n          onInput={this.onInputSlide}\n          onMouseEnter={this.onInputMouseEnter}\n          onMouseLeave={this.onInputMouseLeave}\n          data-test={this.dataTest}\n        />\n        <div class=\"track-bg\">\n          <div\n            class={{ [`progress-bar`]: true, [`progress-bar-liner`]: this.type !== 'no-linear' }}\n            ref={this.refProgressBar}\n          >\n            <bds-tooltip\n              ref={this.refBdsTooltip}\n              class={{ [`progress-bar-tooltip`]: true }}\n              position=\"top-center\"\n              tooltip-text={this.inputValue}\n            >\n              <div class={{ [`progress-bar-thumb`]: true }}></div>\n            </bds-tooltip>\n          </div>\n          {this.markers &&\n            this.stepArray.map((item, index) => (\n              <div key={index} class={`step`}>\n                {this.label && <bds-typo class=\"label-step\" variant=\"fs-10\">{`${item.name}`}</bds-typo>}\n              </div>\n            ))}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAY,wnF,ICQLC,EAAMC,EAAA,wBALnB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,qDAYWA,KAAAC,YAAsBC,GAAAC,EAAAH,KAAKI,SAAO,MAAAD,SAAA,SAAAA,EAAAE,cAAU,MAAAH,SAAA,EAAAA,EAAKF,KAAKM,IAAMN,KAAKM,IAAID,WAAa,IAoBnFL,KAAAI,MAAiBJ,KAAKM,IAAMN,KAAKM,IAAM,EAKvCN,KAAOO,QAAa,MAKpBP,KAAKQ,MAAa,MAIlBR,KAAIS,KAAe,OAUnBT,KAAQU,SAAY,KA6CpBV,KAAAW,cAAgB,SAACC,GACvBb,EAAKc,WAAaD,CACpB,EAEQZ,KAAAc,cAAgB,SAACF,GACvBb,EAAKgB,WAAaH,CACpB,EAEQZ,KAAAgB,eAAiB,SAACJ,GACxBb,EAAKkB,YAAcL,CACrB,EAEQZ,KAAAkB,aAAe,SAACC,GACtB,IAAMC,EAAQD,EACd,IAAMb,EAAMc,EAAMd,IAAMe,SAASD,EAAMd,KAAO,EAC9C,IAAMgB,EAAMD,SAASD,EAAME,KAC3B,IAAMC,EAAMF,SAASD,EAAMhB,OAC3B,IAAMoB,GAAeD,EAAMjB,GAAO,KAAQgB,EAAMhB,GAChD,OAAOkB,CACT,EAEQxB,KAAAyB,aAAe,SAACC,GACtB,IAAMN,EAAQM,EAAGC,OACjB5B,EAAKkB,YAAYW,MAAMC,MAAQ,GAAAC,OAAG/B,EAAKmB,aAAaE,GAAM,KAC1D,IAAMW,EAAYhC,EAAKiC,aAAaX,SAASD,EAAMhB,QACnDL,EAAKE,WAAaF,EAAKkC,UAAUC,OAAS,EAAIH,EAAUI,KAAOf,EAAMhB,MACrEL,EAAKqC,UAAUC,KAAKN,EACtB,EAEQ/B,KAAiBsC,kBAAG,WAC1BvC,EAAKgB,WAAWwB,UAChBxC,EAAKkB,YAAYuB,UAAUC,IAAI,qBACjC,EAEQzC,KAAiB0C,kBAAG,WAC1B3C,EAAKgB,WAAW4B,YAChB5C,EAAKkB,YAAYuB,UAAUI,OAAO,qBACpC,EAEQ5C,KAAAgC,aAAe,SAAC5B,GACtB,GAAIL,EAAK8C,gBAAiB,CACxB,OAAO9C,EAAKkC,UAAU7B,E,KACjB,CACL,OAAOL,EAAKkC,UAAUa,MAAK,SAACC,GAAS,OAAA1B,SAAS0B,EAAKZ,QAAU/B,CAAxB,G,CAEzC,CAkDD,CArICP,EAAAmD,UAAAC,kBAAA,WACE,GAAIjD,KAAKkD,YAAa,CACpB,UAAWlD,KAAKkD,cAAgB,SAAU,CACxClD,KAAK6C,gBAAkBM,KAAKC,MAAMpD,KAAKkD,aACvClD,KAAKiC,UAAYjC,KAAK6C,e,KACjB,CACL7C,KAAK6C,gBAAkB7C,KAAKkD,YAC5BlD,KAAKiC,UAAYjC,KAAK6C,e,MAEnB,CACL7C,KAAKiC,UAAYjC,KAAKqD,cACnBrD,KAAKsB,IAAMtB,KAAKM,KAAON,KAAKsD,KAC7BC,OAAOC,WAAWxD,KAAKsB,IAAMtB,KAAKM,KAAON,KAAKsD,M,GAKpDzD,EAAAmD,UAAAS,iBAAA,WACEzD,KAAKiB,YAAYW,MAAMC,MAAQ,GAAAC,OAAG9B,KAAKkB,aAAalB,KAAKa,YAAW,I,EAEtEhB,EAAAmD,UAAAU,mBAAA,WACE,GAAI1D,KAAK6C,gBAAiB,CACxB7C,KAAKa,WAAWP,IAAM,IACtBN,KAAKa,WAAWS,IAAM,GAAAQ,OAAG9B,KAAK6C,gBAAgBX,OAAS,GACvDlC,KAAKa,WAAWyC,KAAO,G,KAClB,CACLtD,KAAKa,WAAWP,IAAMN,KAAKM,IAAM,GAAAwB,OAAG9B,KAAKM,KAAQ,GACjDN,KAAKa,WAAWS,IAAMtB,KAAKsB,IAAM,GAAAQ,OAAG9B,KAAKsB,KAAQ,GACjDtB,KAAKa,WAAWyC,KAAOtD,KAAKsD,KAAO,GAAAxB,OAAG9B,KAAKsD,MAAS,E,GAIxDzD,EAAAmD,UAAAW,mBAAA,WACE3D,KAAKiB,YAAYW,MAAMC,MAAQ,GAAAC,OAAG9B,KAAKkB,aAAalB,KAAKa,YAAW,KACpE,IAAMkB,EAAY/B,KAAKgC,aAAaX,SAASrB,KAAKa,WAAWT,QAC7DJ,KAAKC,WAAaD,KAAKiC,UAAUC,OAAS,EAAIH,EAAUI,KAAOnC,KAAKa,WAAWT,K,EAkDzEP,EAAAmD,UAAAK,aAAA,SAAajD,EAAewD,GAA5B,IAAA7D,EAAAC,KACN,IAAM6D,EAAeD,EAAMxD,EAAQ,EAAIA,EACvC,IAAM0D,EAAa,GACnB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAcE,IAAK,CACrCD,EAAWE,KAAKD,E,CAElB,OAAOD,EAAWG,KAAI,SAACC,GAAI,OAAQ9D,MAAO8D,EAAM/B,KAAM+B,EAAOnE,EAAKuD,KAAOvD,EAAKO,IAAnD,G,EAG7BT,EAAAmD,UAAAmB,OAAA,W,UAAA,IAAApE,EAAAC,KACE,OACEoE,EAACC,EAAI,CAAAC,IAAA,4CACHF,EACE,SAAAE,IAAA,2CAAAC,IAAKvE,KAAKW,cACVF,KAAK,QACL+D,MAAO,CACLC,YAAa,MAEfrE,MAAOJ,KAAKI,MACZsE,QAAS1E,KAAKyB,aACdkD,aAAc3E,KAAKsC,kBACnBsC,aAAc5E,KAAK0C,kBACR,YAAA1C,KAAKU,WAElB0D,EAAK,OAAAE,IAAA,2CAAAE,MAAM,YACTJ,EACE,OAAAE,IAAA,2CAAAE,OAAKK,EAAA,GAAIA,EAAC,gBAAiB,KAAMA,EAAC,sBAAuB7E,KAAKS,OAAS,YAAWoE,GAClFN,IAAKvE,KAAKgB,gBAEVoD,EACE,eAAAE,IAAA,2CAAAC,IAAKvE,KAAKc,cACV0D,OAAKM,EAAA,GAAIA,EAAC,wBAAyB,KAAIA,GACvCC,SAAS,aAAY,eACP/E,KAAKC,YAEnBmE,EAAK,OAAAE,IAAA,2CAAAE,OAAKQ,EAAA,GAAIA,EAAC,sBAAuB,KAAIA,OAG7ChF,KAAKO,SACJP,KAAKiC,UAAUgC,KAAI,SAAClB,EAAMkC,GAAK,OAC7Bb,EAAA,OAAKE,IAAKW,EAAOT,MAAO,QACrBzE,EAAKS,OAAS4D,EAAA,YAAUI,MAAM,aAAaU,QAAQ,SAAS,GAAApD,OAAGiB,EAAKZ,OAF1C,K,WAtLxB,I", "ignoreList": []}