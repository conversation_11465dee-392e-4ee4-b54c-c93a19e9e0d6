import{r as t,c as s,h as i,H as e}from"./p-C3J6Z5OX.js";import{g as o,d as n}from"./p-BNEKIkjk.js";const a=".menu{position:fixed;pointer-events:none;top:0;left:0;padding:2px;background-color:var(--color-surface-1, rgb(246, 246, 246));border-radius:8px;-webkit-box-shadow:0px 8px 12px rgba(0, 0, 0, 0.08);box-shadow:0px 8px 12px rgba(0, 0, 0, 0.08);width:240px;opacity:0;-webkit-transition:opacity 0.5s;-moz-transition:opacity 0.5s;transition:opacity 0.5s;z-index:90000}.menu__open{pointer-events:auto;opacity:1}.outzone{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:80000}";const h=class{constructor(i){t(this,i);this.bdsToggle=s(this,"bdsToggle");this.refElement=null;this.intoView=null;this.menupositionTop=0;this.menupositionLeft=0;this.menu=null;this.position="right";this.open=false;this.refMenuElement=t=>{this.menuElement=t};this.onClickCloseButtom=t=>{this.open=false;t.stopPropagation()}}componentWillLoad(){this.refElement=document.getElementById(this.menu);this.intoView=o(this.refElement)}async toggle(){this.open=!this.open}openMenu(){this.bdsToggle.emit({value:this.open});if(this.open){const t=n({actionElement:this.refElement,changedElement:this.menuElement,intoView:this.intoView});this.menupositionTop=t.top;this.menupositionLeft=t.left}}render(){const t={top:`${this.menupositionTop}px`,left:`${this.menupositionLeft}px`};return i(e,{key:"4f7c34c68dd427bfd59e014c4f4ff610e98e359c"},i("div",{key:"1185c26ff01c1a7c14f13381ef70ac1d8c82cad6",ref:this.refMenuElement,class:{menu:true,[`menu__${this.position}`]:true,[`menu__open`]:this.open},style:t},i("slot",{key:"6a6bfcfec6098e494319186be20dc702971cad25"})),this.open&&i("div",{key:"56a7a0e7d3575373fe08af98f862cb6ca77d8729",class:{outzone:true},onClick:t=>this.onClickCloseButtom(t)}))}static get watchers(){return{open:["openMenu"]}}};h.style=a;export{h as bds_menu};
//# sourceMappingURL=p-c60c579d.entry.js.map