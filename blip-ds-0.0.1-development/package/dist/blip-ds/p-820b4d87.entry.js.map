{"version": 3, "names": ["CardSubtitle", "render", "h", "key", "variant", "tag", "bold", "margin", "this", "text"], "sources": ["src/components/card/card-subtitle/card-subtitle.tsx"], "sourcesContent": ["import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-subtitle',\n  shadow: true,\n})\nexport class CardSubtitle {\n  /**\n   *Set the card subtitle.\n   */\n  @Prop() text?: string;\n  render() {\n    return (\n      <bds-typo variant=\"fs-12\" tag=\"p\" bold=\"regular\" margin={false}>\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "mappings": "4CAMaA,EAAY,M,yBAKvB,MAAAC,GACE,OACEC,EAAA,YAAAC,IAAA,2CAAUC,QAAQ,QAAQC,IAAI,IAAIC,KAAK,UAAUC,OAAQ,OACtDC,KAAKC,K", "ignoreList": []}