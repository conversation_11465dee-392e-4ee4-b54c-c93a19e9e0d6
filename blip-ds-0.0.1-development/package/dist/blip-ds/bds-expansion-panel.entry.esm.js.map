{"version": 3, "file": "bds-expansion-panel.entry.esm.js", "sources": ["src/components/expansion-panel/expansion-panel.scss?tag=bds-expansion-panel&encapsulation=shadow", "src/components/expansion-panel/expansion-panel.tsx"], "sourcesContent": ["* {\n  -webkit-transition: all 0.5s;\n  -moz-transition: all 0.5s;\n  transition: all 0.5s;\n}\n\n:host {\n  display: block;\n}\n\n", "import { Component, Host, h, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel',\n  styleUrl: 'expansion-panel.scss',\n  shadow: true,\n})\nexport class ExpansionPanel implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,iBAAiB,GAAG,iGAAiG;;MCO9G,cAAc,GAAA,MAAA;;;;IACzB,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;"}