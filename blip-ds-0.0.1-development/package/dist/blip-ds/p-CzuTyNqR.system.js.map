{"version": 3, "file": "p-CzuTyNqR.system.js", "sources": ["src/components/selects/select.scss?tag=bds-select-chips&encapsulation=shadow", "src/components/selects/select-chips/select-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, Element, h, Prop, Method, Event, EventEmitter, Listen, Watch, State } from '@stencil/core';\nimport { Option, SelectChangeEvent, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\nimport { emailValidation, whitespaceValidation } from '../../../utils/validations';\nimport { InputChipsTypes } from '../../input-chips/input-chips-interface';\n\n@Component({\n  tag: 'bds-select-chips',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class SelectChips {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @State() internalOptions: Option[];\n\n  @Element() el!: HTMLElement;\n\n  @State() isOpen? = false;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() selectedOptions: { label: string; value: any }[] = [];\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  @State() selectedOption: number;\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true }) options?: string | Option[];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * Used for add prefix on new option select.\n   */\n  @Prop({ reflect: true }) newPrefix?: string = '';\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Set maximum length value for the chip content\n   */\n\n  @Prop() maxlength?: number;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() canAddNew?: boolean = true;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() notFoundMessage?: string = 'No results found';\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSelectChipsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('options')\n  protected optionsChanged(): void {\n    if (typeof this.options === 'string') {\n      try {\n        this.internalOptions = JSON.parse(this.options);\n      } catch (e) {}\n    } else {\n      this.internalOptions = this.options;\n    }\n  }\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.handleChangeChipsValue();\n\n    if (this.internalChips.length > 0) {\n      this.selectedOptions = this.internalChips.map((item) => {\n        return {\n          label: item,\n          value: `${this.validValueChip(item, this.childOptions)}`,\n        };\n      });\n    }\n  }\n\n  private validValueChip(value, internalOptions: HTMLBdsSelectOptionElement[]): string {\n    const selectOption = internalOptions?.find((option) => option.textContent == value);\n    return `${selectOption ? selectOption.value : value}`;\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async getChips(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n    this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  async componentDidLoad() {\n    await this.resetFilterOptions();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  async connectedCallback() {\n    for (const option of this.childOptions) {\n      option.addEventListener('optionSelected', this.handler);\n    }\n  }\n\n  private get childOptionsEnabled(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(\n          this.el.shadowRoot.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'),\n        )\n      : Array.from(this.el.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'));\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'))\n      : Array.from(this.el.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'));\n  }\n\n  private handleChangeChipsValue = async () => {\n    await this.resetFilterOptions();\n  };\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n      return;\n    }\n\n    for (const option of this.childOptions) {\n      const isExistsChip = this.existsChip(option.textContent, await this.getChips());\n      const optionTextLower = option.textContent.toLowerCase();\n      const termLower = term.toLowerCase();\n\n      if (isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n\n      if (term && optionTextLower.includes(termLower) && !isExistsChip) {\n        option.removeAttribute('invisible');\n      }\n\n      if (term && !optionTextLower.includes(termLower) && !isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n    }\n  }\n\n  private async resetFilterOptions() {\n    for (const option of this.childOptions) {\n      if (this.existsChip(option.textContent, await this.getChips())) {\n        option.setAttribute('invisible', 'invisible');\n      } else {\n        option.removeAttribute('invisible');\n      }\n    }\n  }\n\n  private refDropdown = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private existsChip(optionChip: string, chips: string[]) {\n    return chips.some((chip) => optionChip === chip);\n  }\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handler = async (event: CustomEvent) => {\n    const {\n      detail: { value },\n    } = event;\n    this.selectedOption = value;\n    const text = this.getText(value);\n    await this.addChip(text);\n\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n    this.toggle();\n  };\n\n  private handlerNewOption = async (text: string) => {\n    await this.addChip(text);\n    this.toggle();\n  };\n\n  private enableCreateOption(): boolean {\n    return !!(this.childOptionsEnabled.length === 0 && this.nativeInput && this.nativeInput.value);\n  }\n\n  private async addChip(chip: string) {\n    await this.setChip(chip);\n    this.nativeInput.value = '';\n  }\n\n  private getText = (value: string) => {\n    const el: HTMLBdsSelectOptionElement = this.childOptions.find((option) => option.value === value);\n    return this.getTextFromOption(el);\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.textContent?.trim() ?? '');\n  };\n\n  private setFocusWrapper = (): void => {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private removeFocusWrapper = (): void => {\n    this.nativeInput.blur();\n  };\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsSelectChipsInput.emit(ev);\n    this.changedInputValue();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        if (this.canAddNew !== false) {\n          this.handleDelimiters();\n          this.setChip(this.value);\n          this.value = '';\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowUp':\n        if (!this.disabled) {\n          this.isOpen = false;\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.handleChangeChipsValue;\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        break;\n    }\n  };\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    this.changedInputValue;\n    const {\n      detail: { value },\n    } = event;\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private changedInputValue = async () => {\n    this.value = this.nativeInput.value;\n\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      await this.resetFilterOptions();\n    }\n\n    if (this.value && this.isOpen === false) {\n      this.isOpen = true;\n    }\n  };\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n            >\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  private generateKey(value: string) {\n    return value.toLowerCase().replace(/ /g, '-');\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n\n    let internalOptions: Option[] = [];\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        try {\n          internalOptions = JSON.parse(this.options);\n        } catch (e) {}\n      } else {\n        internalOptions = this.options;\n      }\n    }\n\n    return (\n      <div class=\"select\" tabindex=\"0\" onFocus={this.setFocusWrapper} onBlur={this.removeFocusWrapper}>\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null} onClick={this.toggle}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                {this.internalChips.length > 0 && (\n                  <span style={{ height: this.height, maxHeight: this.maxHeight }} class=\"inside-input-left\">\n                    {this.renderChips()}\n                  </span>\n                )}\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class={{ input__container__text: true }}\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n        >\n          {internalOptions.map((option) => (\n            <bds-select-option\n              key={this.generateKey(option.value)}\n              onOptionSelected={this.handler}\n              value={option.value}\n              status={option.status}\n            >\n              {option.label}\n            </bds-select-option>\n          ))}\n          <slot />\n          {this.canAddNew === true && this.enableCreateOption() && (\n            <bds-select-option\n              id=\"option-add\"\n              value=\"add\"\n              onClick={() => this.handlerNewOption(this.nativeInput.value)}\n            >\n              {this.newPrefix}\n              {this.nativeInput.value}\n            </bds-select-option>\n          )}\n          {!this.canAddNew && this.enableCreateOption() && (\n            <bds-select-option id=\"no-option\" value=\"add\">\n              {this.notFoundMessage}\n            </bds-select-option>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;MAAA,MAAM,SAAS,GAAG,snUAAsnU;;YCW3nU,WAAW,+BAAA,MAAA;MALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;MAeW,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MAEf,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;MAE7B,QAAA,IAAe,CAAA,eAAA,GAAoC,EAAE;MAC9D;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;MAC3C;;MAEG;MACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;MAE3B;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;MAEtB,QAAA,IAAa,CAAA,aAAA,GAAa,EAAE;MAYrC;;;;MAIG;MACsB,QAAA,IAAK,CAAA,KAAA,GAAsB,EAAE;MAEtD;;MAEG;MACsB,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;MAEhD;;MAEG;MACsB,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;MAEnD;;MAEG;MACqC,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MACvD;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MAOjE;;MAEG;MACsB,QAAA,IAAY,CAAA,YAAA,GAAI,EAAE;MAE3C;;MAEG;MACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAE1C;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;MAEnB;;MAEG;MACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;MAE3C;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;MAEpC;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAa,IAAI;MAElC;;MAEG;MACK,QAAA,IAAe,CAAA,eAAA,GAAY,kBAAkB;MAErD;;;MAGG;MACK,QAAA,IAAI,CAAA,IAAA,GAAoB,MAAM;MAEtC;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAI,KAAK;MAE3B;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAG,KAAK;MAC7B;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;MACnC;;MAEG;MACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;MACrD;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;MAE/B;;MAEG;MACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;MAEjC;;MAEG;MACqC,QAAA,IAAe,CAAA,eAAA,GAA+B,MAAM;MAS5F;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAwNxB,QAAA,IAAsB,CAAA,sBAAA,GAAG,YAAW;MAC1C,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;MACjC,SAAC;MAqCO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAU;MAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;MACvB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;MAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;MAC3B,SAAC;MAMO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;MAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;MAE9B,SAAC;MAEO,QAAA,IAAA,CAAA,OAAO,GAAG,OAAO,KAAkB,KAAI;kBAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;MACT,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;kBAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;MAChC,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;MAExB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;MAClF,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;kBACnD,IAAI,CAAC,MAAM,EAAE;MACf,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,OAAO,IAAY,KAAI;MAChD,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;kBACxB,IAAI,CAAC,MAAM,EAAE;MACf,SAAC;MAWO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAa,KAAI;MAClC,YAAA,MAAM,EAAE,GAA+B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC;MACjG,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;MACnC,SAAC;MAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,GAA+B,KAAY;;MACtE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;MACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;sBACxF,IAAI,cAAc,EAAE;0BAClB,OAAO,cAAc,CAAC,KAAK;;;kBAG/B,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAE,SAAS,IAAG,GAAG,CAAC,SAAS,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,WAAW,0CAAE,IAAI,EAAE,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;MAC1E,SAAC;MAEO,QAAA,IAAe,CAAA,eAAA,GAAG,MAAW;MACnC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAE5B,SAAC;MAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAW;MACtC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;MACzB,SAAC;MAUO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;kBAClC,IAAI,CAAC,OAAO,EAAE;MACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAE5B,SAAC;MAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;MAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;MACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACvB,SAAC;MAOO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;MACzC,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;kBAClD,IAAI,KAAK,EAAE;sBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;MAEhC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;kBACjC,IAAI,CAAC,iBAAiB,EAAE;MAC1B,SAAC;MAEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;MACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;MACf,gBAAA,KAAK,OAAO;MACV,oBAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;8BAC5B,IAAI,CAAC,gBAAgB,EAAE;MACvB,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;MACxB,wBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;MACf,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;MAClF,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;MAErD,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;0BAEpB;MACF,gBAAA,KAAK,WAAW;MACd,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;0BAEpB;MACF,gBAAA,KAAK,SAAS;MACZ,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;0BAErB;MACF,gBAAA,KAAK,WAAW;MAChB,gBAAA,KAAK,QAAQ;0BACX,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;8BAChF,IAAI,CAAC,cAAc,EAAE;MAErB,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;MAClF,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;0BAErD;;MAEN,SAAC;MAkEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,YAAW;kBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;MAEnC,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;sBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;uBAC3C;MACL,gBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;kBAGjC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;MACvC,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;MAEtB,SAAC;MAoPF;MArqBW,IAAA,aAAa,CAAC,MAAe,EAAA;MACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;MACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;mBAC9D;MACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;MAErE,QAAA,IAAI,MAAM;MACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;uBACzC;sBACL,IAAI,CAAC,oBAAoB,EAAE;;;MAKjC,IAAA,YAAY,CAAC,EAAS,EAAA;MACpB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,MAA0B,CAAC,EAAE;MACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;UAKb,cAAc,GAAA;MACtB,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;MACpC,YAAA,IAAI;sBACF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;MAC/C,YAAA,OAAO,CAAC,EAAE;;mBACP;MACL,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO;;;MAIvC;;MAEG;UAEO,YAAY,GAAA;MACpB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;MACd,YAAA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;MAClC,gBAAA,IAAI;0BACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;sBAC3C,OAAA,EAAA,EAAM;MACN,oBAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;uBAEpB;MACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;;;mBAE5B;MACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;UAKjB,oBAAoB,GAAA;cAC5B,IAAI,CAAC,sBAAsB,EAAE;cAE7B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;MACjC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;sBACrD,OAAO;MACL,oBAAA,KAAK,EAAE,IAAI;MACX,oBAAA,KAAK,EAAE,CAAA,EAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAE,CAAA;uBACzD;MACH,aAAC,CAAC;;;UAIE,cAAc,CAAC,KAAK,EAAE,eAA6C,EAAA;MACzE,QAAA,MAAM,YAAY,GAAG,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,MAAA,GAAA,MAAA,GAAA,eAAe,CAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC;MACnF,QAAA,OAAO,CAAG,EAAA,YAAY,GAAG,YAAY,CAAC,KAAK,GAAG,KAAK,EAAE;;MAGvD;;MAEG;MAEH,IAAA,MAAM,OAAO,GAAA;MACX,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;MAG7B;;MAEG;MAEH,IAAA,MAAM,QAAQ,GAAA;cACZ,OAAO,IAAI,CAAC,aAAa;;MAG3B;;MAEG;MAEH,IAAA,MAAM,KAAK,GAAA;MACT,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;MACvB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;UAIjB,MAAM,GAAG,CAAC,KAAa,EAAA;cACrB,IAAI,CAAC,gBAAgB,EAAE;cACvB,IAAI,KAAK,EAAE;MACT,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;mBACd;MACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;MAE1B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;MAIjB,IAAA,MAAM,QAAQ,GAAA;MACZ,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAI1B,IAAA,MAAM,WAAW,GAAA;MACf,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;UAGzB,iBAAiB,GAAA;cACf,IAAI,CAAC,YAAY,EAAE;cACnB,IAAI,CAAC,cAAc,EAAE;cACrB,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;;MAG1C,IAAA,MAAM,gBAAgB,GAAA;MACpB,QAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;MAC/B,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;mBACzC;kBACL,IAAI,CAAC,oBAAoB,EAAE;;;MAIvB,IAAA,mBAAmB,CAAC,KAAgC,EAAA;MAC1D,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;kBACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;UAIlC,oBAAoB,GAAA;cAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;kBAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;kBACtB,cAAc,EAAE,IAAI,CAAC,WAAW;kBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;MACxB,SAAA,CAAC;MACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;MACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;kBAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;MAI1C,IAAA,MAAM,iBAAiB,GAAA;MACrB,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;kBACtC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;MAI3D,IAAA,IAAY,mBAAmB,GAAA;cAC7B,OAAO,IAAI,CAAC;MACV,cAAE,KAAK,CAAC,IAAI,CACR,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,qEAAqE,CAAC;MAE9G,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,qEAAqE,CAAC,CAAC;;MAGjH,IAAA,IAAY,YAAY,GAAA;cACtB,OAAO,IAAI,CAAC;MACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,oDAAoD,CAAC;MACtG,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,oDAAoD,CAAC,CAAC;;UAOxF,MAAM,aAAa,CAAC,IAAY,EAAA;cACtC,IAAI,CAAC,IAAI,EAAE;MACT,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;kBAC/B;;MAGF,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;kBAC/E,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE;MACxD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE;kBAEpC,IAAI,YAAY,EAAE;MAChB,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;MAG/C,YAAA,IAAI,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE;MAChE,gBAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;MAGrC,YAAA,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE;MACjE,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;;;MAK3C,IAAA,MAAM,kBAAkB,GAAA;MAC9B,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE;MAC9D,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;uBACxC;MACL,gBAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;;;UAajC,UAAU,CAAC,UAAkB,EAAE,KAAe,EAAA;MACpD,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC;;UA2B1C,kBAAkB,GAAA;cACxB,OAAO,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;UAGxF,MAAM,OAAO,CAAC,IAAY,EAAA;MAChC,QAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;MACxB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;;UA4BrB,aAAa,GAAA;MACnB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;kBACzB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;mBAC9D;MACL,YAAA,OAAO,IAAI;;;UAgBP,YAAY,GAAA;MAClB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;MACnB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;MAgDhB,IAAA,6BAA6B,CAAC,KAAa,EAAA;MACjD,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;MACzD,YAAA,OAAO,EAAE;;MAGX,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAE/D,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;MACtC,YAAA,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;MAGlC,QAAA,OAAO,QAAQ;;UAGT,gBAAgB,GAAA;MACtB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;MACpC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;MAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;kBAAE;cAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;MAC9C,QAAA,IAAI,CAAC,SAAS;kBAAE;cAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;cAC1D,IAAI,CAAC,QAAQ,EAAE;kBACb,IAAI,CAAC,gBAAgB,EAAE;kBACvB;;cAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;MAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;kBACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;MAChC,SAAC,CAAC;cAEF,IAAI,CAAC,gBAAgB,EAAE;;UAGjB,MAAM,YAAY,CAAC,KAAqC,EAAA;cAE9D,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;MAET,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;MAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;kBAAE;cAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;MAC9C,QAAA,IAAI,CAAC,SAAS;kBAAE;cAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;cAC1D,IAAI,CAAC,QAAQ,EAAE;kBACb,IAAI,CAAC,gBAAgB,EAAE;kBACvB;;cAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;MAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;MACrB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;MACpB,SAAC,CAAC;cAEF,IAAI,CAAC,gBAAgB,EAAE;;UAiBjB,gBAAgB,CAAC,KAAK,GAAG,EAAE,EAAA;MACjC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK;MAC9B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;MAGZ,IAAA,OAAO,CAAC,IAAY,EAAA;MAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;kBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;MAC3F,YAAA,IAAI,MAAM;sBAAE;;MAGd,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;kBAC/B;;cAGF,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC;;MAG5C,IAAA,YAAY,CAAC,IAAY,EAAA;MAC/B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE;cAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;MACzD,YAAA,OAAO,KAAK;;MAEd,QAAA,OAAO,IAAI;;UAGL,cAAc,GAAA;MACpB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;;MAGzE,IAAA,UAAU,CAAC,KAAkC,EAAA;cACnD,MAAM,EACJ,MAAM,EAAE,EAAE,EAAE,EAAE,GACf,GAAG,KAAK;cAET,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;MACzF,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;MAClF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;UAG7C,WAAW,GAAA;MACjB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;MAC9B,YAAA,OAAO,EAAE;;cAGX,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;MAC5C,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE;kBAC3B,MAAM,KAAK,GAAG,EAAE;MAChB,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;MACxB,gBAAA,QACE,CACE,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAEtD,IAAI,CACc;;uBAElB;MACL,gBAAA,QACE,CAAa,CAAA,aAAA,EAAA,EAAA,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,cAAA,EAAe,IAAI,EAAA,EAC5D,CACE,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAEtD,CAAG,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA,IAAA,CAAM,CACX,CACT;;MAGpB,SAAC,CAAC;;UAGI,UAAU,GAAA;MAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,WAAW,EAAE,IAAI;MACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;UAIG,WAAW,GAAA;MACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,uBAAuB,EAAE,IAAI;sBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;mBACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;UAIG,aAAa,GAAA;cACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;MACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;MAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;MAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;cAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;MAClB,cAAE;oBACA,IAAI,CAAC;MACL,kBAAE;wBACA,gBAAgB;cAExB,IAAI,OAAO,EAAE;MACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;MAIV,QAAA,OAAO,SAAS;;MAGV,IAAA,WAAW,CAAC,KAAa,EAAA;cAC/B,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;UAG/C,MAAM,GAAA;cACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;cAElD,IAAI,eAAe,GAAa,EAAE;MAClC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;MAChB,YAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;MACpC,gBAAA,IAAI;0BACF,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;MAC1C,gBAAA,OAAO,CAAC,EAAE;;uBACP;MACL,gBAAA,eAAe,GAAG,IAAI,CAAC,OAAO;;;MAIlC,QAAA,QACE,4DAAK,KAAK,EAAC,QAAQ,EAAC,QAAQ,EAAC,GAAG,EAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,IAAI,CAAC,kBAAkB,EAAA,EAC7F,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAA,eAAA,EAAiB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAA,EACrG,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,KAAK,EAAE,IAAI;sBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;MAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;sBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;sBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;MACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MAC5B,gBAAA,gBAAgB,EAAE,SAAS;MAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAA,EAE3B,IAAI,CAAC,UAAU,EAAE,EAClB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,KAC5B,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAC,mBAAmB,IACvF,IAAI,CAAC,WAAW,EAAE,CACd,CACR,EACD,CACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,EACjC,QAAQ,EAAE,MAAM,IAAI,CAAC,YAAY,EACjC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAA,WAAA,EACZ,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,EACxB,CAAA,CACL,CACF,EACN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAA,CAAY,CACjF,EACL,IAAI,CAAC,OAAO,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI;sBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;MACrC,aAAA,EAAA,EAEA,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,MAC1B,CAAA,CAAA,mBAAA,EAAA,EACE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EACnC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAC9B,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,MAAM,EAAE,MAAM,CAAC,MAAM,EAEpB,EAAA,MAAM,CAAC,KAAK,CACK,CACrB,CAAC,EACF,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACP,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE,KACnD,CAAA,CAAA,mBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,EAAE,EAAC,YAAY,EACf,KAAK,EAAC,KAAK,EACX,OAAO,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAA,EAE3D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,WAAW,CAAC,KAAK,CACL,CACrB,EACA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAC3C,CAAA,CAAA,mBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAmB,EAAE,EAAC,WAAW,EAAC,KAAK,EAAC,KAAK,EAAA,EAC1C,IAAI,CAAC,eAAe,CACH,CACrB,CACG,CACF;;;;;;;;;;;;;;;;;;"}