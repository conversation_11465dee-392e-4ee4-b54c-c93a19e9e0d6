@use "colors" as *;

/** Customs */

@mixin no-select {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}

/** Animations */

@mixin animation {
  transition: all 0.3s;
  transition-property: all;
  transition-duration: 0.3s;
  transition-timing-function: ease;
  transition-delay: 0s;
}

/** Aligns */

@mixin flex-align-middle {
  display: flex;
  align-items: center;
  justify-content: center;
}

/** Scoll Bar */

@mixin custom-scroll() {
  &::-webkit-scrollbar {
    width: 16px;
    background-color: $color-shadow-0;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;

    border: 4px solid transparent;
    border-radius: 10px;
    background-clip: content-box;

    background-color: $color-border-1;
  }
}

/** Hover & Pressed */

@mixin hover-and-pressed() {
  &:hover {
    &:before {
      background-color: $color-content-default;
      opacity: 0.08;
    }
  }
  &:active {
    &:before {
      background-color: $color-content-default;
      opacity: 0.16;
    }
  }
}
