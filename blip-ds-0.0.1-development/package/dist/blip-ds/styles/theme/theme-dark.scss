$color-dark-brand: rgba(0, 150, 250, 1);
$color-dark-primary: rgba(73, 139, 255, 1);
$color-dark-secondary: rgba(255, 255, 255, 1);
$color-dark-surface-0: rgba(66, 66, 66, 1);
$color-dark-surface-1: rgba(57, 57, 57, 1);
$color-dark-surface-2: rgba(31, 31, 31, 1);
$color-dark-surface-3: rgba(20, 20, 20, 1);
$color-dark-surface-4: rgba(10, 10, 10, 1);
$color-dark-surface-positive: rgba(1, 86, 47, 1);
$color-dark-surface-negative: rgba(87, 0, 0, 1);
$color-dark-surface-primary: rgba(12, 80, 197, 1);
$color-dark-content-default: rgba(255, 255, 255, 1);
$color-dark-content-disable: rgba(148, 148, 148, 1);
$color-dark-content-ghost: rgba(102, 102, 102, 1);
$color-dark-content-bright: rgba(255, 255, 255, 1);
$color-dark-content-din: rgba(0, 0, 0, 1);
$color-dark-border-1: rgba(255,255,255,.20);
$color-dark-border-2: rgba(255,255,255,.16);
$color-dark-border-3: rgba(255,255,255,.06);
$color-dark-positive: rgba(107, 255, 188, 1);
$color-dark-negative: rgba(255, 184, 184, 1);
$color-dark-info: rgba(0, 79, 86, 1);
$color-dark-system: rgba(0, 60, 100, 1);
$color-dark-focus: rgba(194, 38, 251, 1);
$color-dark-success: rgba(53, 94, 75, 1);
$color-dark-warning: rgba(96, 89, 59, 1);
$color-dark-error: rgba(123, 61, 61, 1);
$color-dark-delete: rgba(182, 12, 12, 1);

//Extended
$color-dark-extended-blue: rgba(25, 104, 240, 1);
$color-dark-extended-blue-bright: rgba(178, 223, 253, 1);
$color-dark-extended-ocean: rgba(0, 211, 228, 1);
$color-dark-extended-ocean-bright: rgba(128, 227, 235, 1);
$color-dark-extended-green: rgba(53, 222, 144, 1);
$color-dark-extended-green-bright: rgba(132, 235, 188, 1);
$color-dark-extended-yellow: rgba(251, 207, 35, 1);
$color-dark-extended-yellow-bright: rgba(253, 233, 155, 1);
$color-dark-extended-orange: rgba(240, 99, 5, 1);
$color-dark-extended-orange-bright: rgba(252, 170, 115, 1);
$color-dark-extended-red: rgba(230, 15, 15, 1);
$color-dark-extended-red-bright: rgba(249, 159, 159, 1);
$color-dark-extended-pink: rgba(251, 75, 193, 1);
$color-dark-extended-pink-bright: rgba(253, 155, 220, 1);
$color-dark-extended-gray: rgba(102, 102, 102, 1);
$color-dark-extended-gray-bright: rgba(199, 199, 199, 1);

//Actions
$color-dark-hover: rgba(255, 255, 255, 0.16);
$color-dark-pressed: rgba(255, 255, 255, 0.08);
