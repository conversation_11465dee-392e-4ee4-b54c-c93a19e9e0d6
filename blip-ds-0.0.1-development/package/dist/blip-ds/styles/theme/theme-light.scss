$color-light-brand: rgba(0, 150, 250, 1);
$color-light-primary: rgba(30, 107, 241, 1);
$color-light-secondary: rgba(41, 41, 41, 1);
$color-light-surface-0: rgba(255, 255, 255, 1);
$color-light-surface-1: rgba(246, 246, 246, 1);
$color-light-surface-2: rgba(237, 237, 237, 1);
$color-light-surface-3: rgba(227, 227, 227, 1);
$color-light-surface-4: rgba(20, 20, 20, 1);
$color-light-surface-positive: rgba(1, 114, 62, 1);
$color-light-surface-negative: rgba(138, 0, 0, 1);
$color-light-surface-primary: rgba(30, 107, 241, 1);
$color-light-content-default: rgba(40, 40, 40, 1);
$color-light-content-disable: rgba(89, 89, 89, 1);
$color-light-content-ghost: rgba(140, 140, 140, 1);
$color-light-content-bright: rgba(255, 255, 255, 1);
$color-light-content-din: rgba(0, 0, 0, 1);
$color-light-border-1: rgba(0,0,0,.20);
$color-light-border-2: rgba(0,0,0,.16);
$color-light-border-3: rgba(0,0,0,.06);
$color-light-positive: rgba(0, 122, 66, 1);
$color-light-negative: rgba(168, 11, 11, 1);
$color-light-info: rgba(128, 227, 235, 1);
$color-light-system: rgba(178, 223, 253, 1);
$color-light-focus: rgba(194, 38, 251, 1);
$color-light-success: rgba(132, 235, 188, 1);
$color-light-warning: rgba(253, 233, 155, 1);
$color-light-error: rgba(250, 190, 190, 1);
$color-light-delete: rgba(230, 15, 15, 1);

//Extended
$color-light-extended-blue: rgba(25, 104, 240, 1);
$color-light-extended-blue-bright: rgba(178, 223, 253, 1);
$color-light-extended-ocean: rgba(0, 211, 228, 1);
$color-light-extended-ocean-bright: rgba(128, 227, 235, 1);
$color-light-extended-green: rgba(53, 222, 144, 1);
$color-light-extended-green-bright: rgba(132, 235, 188, 1);
$color-light-extended-yellow: rgba(251, 207, 35, 1);
$color-light-extended-yellow-bright: rgba(253, 233, 155, 1);
$color-light-extended-orange: rgba(240, 99, 5, 1);
$color-light-extended-orange-bright: rgba(252, 170, 115, 1);
$color-light-extended-red: rgba(230, 15, 15, 1);
$color-light-extended-red-bright: rgba(249, 159, 159, 1);
$color-light-extended-pink: rgba(251, 75, 193, 1);
$color-light-extended-pink-bright: rgba(253, 155, 220, 1);
$color-light-extended-gray: rgba(102, 102, 102, 1);
$color-light-extended-gray-bright: rgba(199, 199, 199, 1);

//Actions
$color-light-hover: rgba(0, 0, 0, 0.08);
$color-light-pressed: rgba(0, 0, 0, 0.16);
