{"version": 3, "file": "p-CDItTnQA.system.js", "sources": ["src/components/chip-clickable/chip-clickable.scss?tag=bds-chip-clickable&encapsulation=shadow", "src/components/chip-clickable/chip-clickable.tsx", "src/components/tooltip/tooltip.scss?tag=bds-tooltip&encapsulation=shadow", "src/components/tooltip/tooltip.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  height: max-content;\n  border-radius: 4px;\n  box-sizing: border-box;\n  max-width: 100%;\n\n  .chip_clickable {\n    display: flex;\n    min-width: 32px;\n    width: fit-content;\n    height: 24px;\n    border-radius: 12px;\n    padding: 2px 6px;\n    align-items: center;\n    box-sizing: border-box;\n    justify-content: center;\n    position: relative;\n    z-index: 1; // Keep chips behind other screen components\n    flex-shrink: 0; // Prevent chips from shrinking in flex layout\n\n    &--container-text {\n      &--full {\n        width: 100%;\n      }\n      &--min {\n        width: calc(100% - 36px);\n      }\n      &--half {\n        width: calc(100% - 16px);\n      }\n    }\n\n    &--hide {\n      display: none;\n      padding: 0;\n      border: none;\n    }\n\n    .chip_focus:focus {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      padding: 2px;\n      border-radius: 4px;\n      outline: $color-focus solid 2px;\n    }\n\n    &--click {\n      cursor: pointer;\n      .chip_darker {\n        opacity: 0;\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        border-radius: inherit;\n        z-index: 1;\n        backdrop-filter: brightness(1);\n        box-sizing: border-box;\n      }\n    }\n    &--click:hover {\n      .chip_darker {\n        opacity: 1;\n        backdrop-filter: brightness(0.9);\n      }\n    }\n    &--click:active {\n      .chip_darker {\n        opacity: 1;\n        backdrop-filter: brightness(0.8);\n      }\n    }\n    &--disabled {\n      cursor: default;\n      background-color: $color-surface-3;\n      .chip_clickable--icon {\n        color: $color-content-default;\n      }\n      .chip_clickable--text {\n        color: $color-content-default;\n      }\n      .chip_clickable--close {\n        cursor: default;\n      }\n    }\n\n    &--text {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      margin: 0;\n      padding: 0 2px;\n      z-index: 2;\n      font-family: $font-family;\n      line-height: 1;\n    }\n    &--icon {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      padding-right: 2px;\n      z-index: 2;\n    }\n    &--close {\n      display: flex;\n      align-items: center;\n      height: 16px;\n      padding-left: 2px;\n      mix-blend-mode: hard-light;\n      opacity: 0.5;\n      z-index: 2;\n      position: relative;\n      cursor: pointer;\n\n      .close_focus:focus {\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        left: -2px;\n        border-radius: 4px;\n        outline: $color-focus solid 2px;\n      }\n    }\n    &--tall {\n      height: 32px;\n      border-radius: 16px;\n      padding: 4px 8px;\n\n      .chip_clickable--text {\n        height: 20px;\n        line-height: 1.1;\n      }\n      .chip_clickable--icon {\n        height: 20px;\n        padding-right: 4px;\n      }\n      .chip_clickable--close {\n        height: 20px;\n        padding-left: 4px;\n      }\n    }\n    &--default {\n      background-color: $color-system;\n      color: $color-content-din;\n    }\n    &--info {\n      background-color: $color-info;\n      color: $color-content-din;\n    }\n    &--success {\n      background-color: $color-success;\n      color: $color-content-din;\n    }\n    &--warning {\n      background-color: $color-warning;\n      color: $color-content-din;\n    }\n    &--danger {\n      background-color: $color-error;\n      color: $color-content-din;\n    }\n    &--outline {\n      border: 1px solid $color-border-1;\n      color: $color-content-default;\n    }\n    &:focus-visible {\n      outline: none;\n    }\n  }\n}\n", "import { Component, Host, h, Prop, Event, EventEmitter, Element, State } from '@stencil/core';\n\nexport type ColorChipClickable = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline';\nexport type Size = 'standard' | 'tall';\n\n@Component({\n  tag: 'bds-chip-clickable',\n  styleUrl: 'chip-clickable.scss',\n  shadow: true,\n})\nexport class ChipClickable {\n  @Element() private element: HTMLElement;\n  @State() visible = true;\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for add avatar left container. Uses the bds-avatar component.\n   */\n  @Prop() avatar?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipClickable = 'default';\n  /**\n   * used for change the size chip. Uses one of them.\n   */\n  @Prop() size?: Size = 'standard';\n  /**\n   * it makes the chip clickable.\n   */\n  @Prop() clickable?: boolean = false;\n  /**\n   * used for delete the chip.\n   */\n  @Prop() close?: boolean = false;\n  /**\n   * the chip gone stay disabled while this prop be true.\n   */\n  @Prop() disabled?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   *  Triggered after a mouse click on close icon, return id element. Only fired when close is true.\n   */\n  @Event() chipClickableClose: EventEmitter;\n  @Event() chipClickableClick: EventEmitter;\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleClick(event) {\n    if (!this.disabled) {\n      event.preventDefault();\n      this.chipClickableClick.emit();\n    }\n  }\n\n  private handleCloseChip(event) {\n    event.preventDefault();\n    this.chipClickableClose.emit({ id: this.element.id });\n  }\n\n  private handleCloseKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      this.chipClickableClose.emit({ id: this.element.id });\n    }\n  }\n\n  private getSizeAvatarChip() {\n    if (this.size === 'tall') {\n      return 'extra-small';\n    } else return 'micro';\n  }\n\n  private getSizeIconChip() {\n    if (this.size === 'tall') {\n      return 'medium';\n    } else return 'x-small';\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_clickable: true,\n            [`chip_clickable--${this.color}`]: true && !this.disabled,\n            [`chip_clickable--${this.size}`]: true,\n            'chip_clickable--hide': !this.visible,\n            'chip_clickable--click': this.clickable,\n            'chip_clickable--disabled': this.disabled,\n          }}\n          onClick={this.handleClick.bind(this)}\n          data-test={this.dataTest}\n        >\n          {this.clickable && !this.disabled && (\n            <div class=\"chip_focus\" onKeyDown={this.handleClickKey.bind(this)} tabindex=\"0\"></div>\n          )}\n          {this.clickable && !this.disabled && <div class=\"chip_darker\"></div>}\n          {this.icon && !this.avatar && (\n            <div class=\"chip_clickable--icon\">\n              <bds-icon size={this.getSizeIconChip()} name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.avatar && (\n            <div class=\"chip_clickable--avatar\">\n              <bds-avatar size={this.getSizeAvatarChip()} thumbnail={this.avatar}></bds-avatar>\n            </div>\n          )}\n          <div\n            class={\n              this.close && (this.icon || this.avatar)\n                ? `chip_clickable--container-text--min`\n                : !this.close && !this.icon && !this.avatar\n                  ? `chip_clickable--container-text--full`\n                  : `chip_clickable--container-text--half`\n            }\n          >\n            <bds-typo no-wrap=\"true\" class=\"chip_clickable--text\" variant=\"fs-12\" bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n          {this.close && (\n            <div class=\"chip_clickable--close\" data-test={this.dtButtonClose} onClick={this.handleCloseChip.bind(this)}>\n              {!this.disabled && (\n                <div class=\"close_focus\" onKeyDown={this.handleCloseKey.bind(this)} tabindex=\"0\"></div>\n              )}\n              <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n            </div>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n$tooltip-horizontal-margin: 10px;\n$tooltip-border-width: 6px;\n\n.tooltip {\n  &__wrapper {\n    display: inline-block;\n    position: relative;\n  }\n\n  &__tip {\n    position: absolute;\n    left: 50%;\n    border-radius: 8px;\n    padding: 8px;\n    background: $color-content-default;\n    z-index: $zindex-modal;\n    white-space: normal;\n    width: max-content;\n    min-width: 32px;\n    max-width: 320px;\n    box-shadow: $shadow-2;\n    visibility: hidden;\n    transition: all 0.2s ease-in-out;\n    box-sizing: border-box;\n    cursor: default;\n    &--visible {\n      visibility: visible;\n    }\n\n    &::before {\n      content: '';\n      left: 50%;\n      border: solid transparent;\n      height: 0;\n      width: 0;\n      position: absolute;\n      pointer-events: none;\n      margin-left: -$tooltip-border-width;\n      border-width: $tooltip-border-width;\n    }\n\n    &--top-center,\n    &--top-left,\n    &--top-right {\n      bottom: calc(100% + 10px);\n      &::before {\n        top: 100%;\n        border-top-color: $color-content-default;\n      }\n    }\n\n    &--top-left {\n      left: 0;\n      transform: translateX(-15%);\n      &::before {\n        left: calc(15% + #{$tooltip-border-width});\n      }\n    }\n\n    &--top-right {\n      left: initial;\n      right: 0;\n      transform: translateX(15%);\n      &::before {\n        left: calc(85% - #{$tooltip-border-width});\n      }\n    }\n\n    &--bottom-center,\n    &--top-center {\n      transform: translateX(-50%);\n    }\n\n    &--left-center,\n    &--right-center {\n      transform: translateX(0) translateY(-50%);\n    }\n\n    &--right-center,\n    &--right-top,\n    &--right-bottom {\n      left: calc(100% + #{$tooltip-horizontal-margin});\n      top: 50%;\n      &::before {\n        left: -5px;\n        top: 50%;\n        transform: translateX(0) translateY(-50%);\n        border-right-color: $color-content-default;\n      }\n    }\n\n    &--right-top {\n      top: 0;\n      &::before {\n        top: 40%;\n      }\n    }\n\n    &--right-bottom {\n      top: initial;\n      bottom: 0;\n      &::before {\n        top: 60%;\n      }\n    }\n\n    &--bottom-center,\n    &--bottom-right,\n    &--bottom-left {\n      top: calc(100% + 10px);\n      &::before {\n        bottom: 100%;\n        border-bottom-color: $color-content-default;\n      }\n    }\n\n    &--bottom-right {\n      left: initial;\n      right: 0;\n      transform: translateX(15%);\n      &::before {\n        left: calc(85% - #{$tooltip-border-width});\n      }\n    }\n\n    &--bottom-left {\n      left: 0;\n      transform: translateX(-15%);\n      &::before {\n        left: calc(15% + #{$tooltip-border-width});\n      }\n    }\n\n    &--left-center,\n    &--left-top,\n    &--left-bottom {\n      left: auto;\n      right: calc(100% + #{$tooltip-horizontal-margin});\n      top: 50%;\n      &::before {\n        left: auto;\n        right: -11px;\n        top: 50%;\n        transform: translateX(0) translateY(-50%);\n        border-left-color: $color-content-default;\n      }\n    }\n\n    &--left-top {\n      top: 0;\n      &::before {\n        top: 40%;\n      }\n    }\n\n    &--left-bottom {\n      top: initial;\n      bottom: 0;\n      &::before {\n        top: 60%;\n      }\n    }\n    &__text {\n      pre {\n        margin: 0;\n        display: flex;\n        font-family: inherit;\n        white-space: break-spaces;\n      }\n\n      .text {\n        color: $color-surface-1;\n      }\n    }\n  }\n}\n", "import { Component, h, Method, Prop, State, Watch } from '@stencil/core';\n\nexport type TooltipPostionType =\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom';\n\n@Component({\n  tag: 'bds-tooltip',\n  styleUrl: 'tooltip.scss',\n  shadow: true,\n})\nexport class Tooltip {\n  /**\n   * Used to set tooltip visibility\n   */\n  @State() isMouseOver = false;\n  @State() textVerify: string;\n  @State() maxWidtTooltip: string;\n\n  /**\n   * Used to set tooltip text\n   */\n  @Prop({ mutable: true }) tooltipText = 'Tooltip';\n\n  /**\n   * Used to disable tooltip when the button are avalible\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Used to set tooltip position\n   */\n  @Prop() position: TooltipPostionType = 'left-center';\n\n  /**\n   * Used to set tooltip max width\n   */\n  @Prop() maxWidth?: string = '320px';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async visible() {\n    this.isMouseOver = true;\n  }\n\n  /**\n   * Method for change the visibility of tooltip.\n   */\n  @Method()\n  async invisible() {\n    this.isMouseOver = false;\n  }\n\n  private setVisibility(value: boolean) {\n    if (this.disabled) {\n      this.isMouseOver = false;\n      return;\n    }\n    this.isMouseOver = value;\n  }\n\n  componentWillLoad() {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  @Watch('tooltipText')\n  tooltipTextChanged(): void {\n    this.textVerify = this.tooltipText ? this.tooltipText.replace(/<br>/gi, '\\r\\n') : '';\n  }\n\n  @Watch('maxWidth')\n  maxWidthChanged(): void {\n    this.maxWidtTooltip = this.maxWidth;\n  }\n\n  render() {\n    const styleTooltip = {\n      maxWidth: this.maxWidtTooltip,\n    };\n    return (\n      <div class=\"tooltip__wrapper\">\n        <div\n          onMouseEnter={() => this.setVisibility(true)}\n          onMouseLeave={() => this.setVisibility(false)}\n          data-test={this.dataTest}\n        >\n          <slot />\n        </div>\n        <div\n          class={{\n            tooltip__tip: true,\n            [`tooltip__tip--${this.position}`]: true,\n            'tooltip__tip--visible': this.isMouseOver,\n          }}\n          style={styleTooltip}\n        >\n          <div class={{ tooltip__tip__text: true }}>\n            <pre>\n              <bds-typo class=\"text\" no-wrap=\"false\" variant=\"fs-12\">\n                {this.textVerify}\n              </bds-typo>\n            </pre>\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,gBAAgB,GAAG,0yHAA0yH;;YCUtzH,aAAa,iCAAA,MAAA;MAL1B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MAOW,QAAA,IAAO,CAAA,OAAA,GAAG,IAAI;MASvB;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAwB,SAAS;MAC9C;;MAEG;MACK,QAAA,IAAI,CAAA,IAAA,GAAU,UAAU;MAChC;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;MACnC;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAa,KAAK;MAC/B;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;MAClC;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAEhC;;;MAGG;MACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;MAmGtC;MA5FS,IAAA,cAAc,CAAC,KAAK,EAAA;MAC1B,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;kBAClE,KAAK,CAAC,cAAc,EAAE;MACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;;;MAI1B,IAAA,WAAW,CAAC,KAAK,EAAA;MACvB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;kBAClB,KAAK,CAAC,cAAc,EAAE;MACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;;;MAI1B,IAAA,eAAe,CAAC,KAAK,EAAA;cAC3B,KAAK,CAAC,cAAc,EAAE;MACtB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;;MAG/C,IAAA,cAAc,CAAC,KAAK,EAAA;MAC1B,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;kBAClE,KAAK,CAAC,cAAc,EAAE;MACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;;;UAIjD,iBAAiB,GAAA;MACvB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;MACxB,YAAA,OAAO,aAAa;;;MACf,YAAA,OAAO,OAAO;;UAGf,eAAe,GAAA;MACrB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;MACxB,YAAA,OAAO,QAAQ;;;MACV,YAAA,OAAO,SAAS;;UAGzB,MAAM,GAAA;cACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,cAAc,EAAE,IAAI;MACpB,gBAAA,CAAC,CAAmB,gBAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,GAAW,CAAC,IAAI,CAAC,QAAQ;MACzD,gBAAA,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;MACtC,gBAAA,sBAAsB,EAAE,CAAC,IAAI,CAAC,OAAO;sBACrC,uBAAuB,EAAE,IAAI,CAAC,SAAS;sBACvC,0BAA0B,EAAE,IAAI,CAAC,QAAQ;mBAC1C,EACD,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EACzB,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAEvB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,KAC/B,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,YAAY,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,GAAO,CACvF,EACA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,aAAa,EAAO,CAAA,EACnE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KACxB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAA,CAAa,CAChE,CACP,EACA,IAAI,CAAC,MAAM,KACV,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,wBAAwB,EAAA,EACjC,CAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAY,IAAI,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAA,CAAe,CAC7E,CACP,EACD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EACH,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM;MACrC,kBAAE,CAAqC,mCAAA;MACvC,kBAAE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;MACnC,sBAAE,CAAsC,oCAAA;MACxC,sBAAE,CAAA,oCAAA,CAAsC,EAAA,EAG9C,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,SAAA,EAAkB,MAAM,EAAC,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EAC/E,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACJ,CACP,EACL,IAAI,CAAC,KAAK,KACT,4DAAK,KAAK,EAAC,uBAAuB,EAAA,WAAA,EAAY,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EACvG,CAAC,IAAI,CAAC,QAAQ,KACb,4DAAK,KAAK,EAAC,aAAa,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,EAAA,CAAO,CACxF,EACD,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,EAAY,CAAA,CAC3D,CACP,CACG,CACD;;;;;;MClJb,MAAM,UAAU,GAAG,ggHAAggH;;YCqBtgH,OAAO,0BAAA,MAAA;MALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;MAME;;MAEG;MACM,QAAA,IAAW,CAAA,WAAA,GAAG,KAAK;MAI5B;;MAEG;MACsB,QAAA,IAAW,CAAA,WAAA,GAAG,SAAS;MAEhD;;MAEG;MACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAE1C;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAuB,aAAa;MAEpD;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,OAAO;MAEnC;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAyEjC;MAvEC;;MAEG;MAEH,IAAA,MAAM,OAAO,GAAA;MACX,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;MAGzB;;MAEG;MAEH,IAAA,MAAM,SAAS,GAAA;MACb,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;MAGlB,IAAA,aAAa,CAAC,KAAc,EAAA;MAClC,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;MACjB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;kBACxB;;MAEF,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;UAG1B,iBAAiB,GAAA;cACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE;MACpF,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ;;UAIrC,kBAAkB,GAAA;cAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE;;UAItF,eAAe,GAAA;MACb,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ;;UAGrC,MAAM,GAAA;MACJ,QAAA,MAAM,YAAY,GAAG;kBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc;eAC9B;MACD,QAAA,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,kBAAkB,EAAA,EAC3B,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,YAAY,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAC5C,YAAY,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAA,WAAA,EAClC,IAAI,CAAC,QAAQ,EAAA,EAExB,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,YAAY,EAAE,IAAI;MAClB,gBAAA,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;sBACxC,uBAAuB,EAAE,IAAI,CAAC,WAAW;MAC1C,aAAA,EACD,KAAK,EAAE,YAAY,EAAA,EAEnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAA,EACtC,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACE,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,MAAM,aAAS,OAAO,EAAC,OAAO,EAAC,OAAO,IACnD,IAAI,CAAC,UAAU,CACP,CACP,CACF,CACF,CACF;;;;;;;;;;;;;;;"}