System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,s,o,r;return{setters:[function(t){e=t.r;s=t.h;o=t.H;r=t.a}],execute:function(){var i=".sc-bds-table-row-h{display:table-row;height:64px;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16))}.sc-bds-table-row-h .collapse-body.sc-bds-table-row{padding:16px;max-height:100px;text-align:left;opacity:1;-webkit-transition:all ease 0.5s;transition:all ease 0.5s}.sc-bds-table-row-h:last-child{border-bottom:none}.clickable--true.sc-bds-table-row-h:hover{background-color:var(--color-hover, rgba(0, 0, 0, 0.08));border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16));cursor:pointer}.clickable--true.sc-bds-table-row-h{border-bottom:none}.selected--true.sc-bds-table-row-h{border-radius:8px;outline:2px solid var(--color-primary, rgb(30, 107, 241));outline-offset:-1px;border-bottom:none}.dense-row.sc-bds-table-row-h{height:auto}.collapse-body.sc-bds-table-row-h{height:-webkit-fit-content;height:-moz-fit-content;height:fit-content}.arrow.sc-bds-table-row{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(180deg);transform:rotate(180deg)}.active.sc-bds-table-row{-webkit-transform:rotate(0deg);transform:rotate(0deg)}.collapse.sc-bds-table-row-h{height:0}.collapse.sc-bds-table-row-h .collapse-body.sc-bds-table-row{padding:0;max-height:0;opacity:0;overflow:hidden;-webkit-transition:all ease-in-out 0.5s;transition:all ease-in-out 0.5s}.collapse.sc-bds-table-row-h th.sc-bds-table-row{padding:0}";var a=t("bds_table_row",function(){function t(t){var s=this;e(this,t);this.isDense=false;this.isCollapsed=true;this.colspanNumber=null;this.clickable=false;this.selected=false;this.toggleCollapse=function(t){if(s.collapse){var e=document.querySelector('[body-collapse="'.concat(t,'"]'));e.classList.toggle("collapse");s.isCollapsed=!s.isCollapsed}}}t.prototype.componentWillLoad=function(){this.bdsTable=this.element.closest("bds-table");this.collapseRow=document.querySelector('[body-collapse="'.concat(this.dataTarget,'"]'));this.colspanNumber=document.querySelector("bds-table-row").children.length;if(this.bdsTable&&(this.bdsTable.getAttribute("dense-table")==="true"||this.bdsTable.denseTable===true)){this.isDense=true}if(this.bdsTable&&(this.bdsTable.getAttribute("collapse")==="true"||this.bdsTable.collapse===true)){this.collapse=true;this.clickable=true}if(this.collapseRow){this.collapseRow.classList.add("collapse");this.collapseRow.classList.add("collapse-body")}};t.prototype.componentWillUpdate=function(){var t=this.element.closest("bds-table");if(t&&(t.getAttribute("dense-table")==="true"||t.denseTable===true)){this.isDense=true}};t.prototype.render=function(){var t;var e=this;if(this.bodyCollapse){return s("th",{colSpan:this.colspanNumber},s("div",{class:"collapse-body"},s("slot",null)))}else{var r=this.element.closest("bds-table-header")===this.element.parentElement;return s(o,{class:(t={host:true},t["clickable--".concat(this.clickable)]=!r&&this.clickable===true?true:false,t["selected--".concat(this.selected)]=true,t["dense-row"]=this.isDense,t),onClick:function(){return e.toggleCollapse(e.dataTarget)}},this.collapse&&s("bds-table-cell",{type:"custom"},!r&&s("bds-icon",{class:{arrow:true,active:this.isCollapsed},name:"arrow-down"})),s("slot",null))}};Object.defineProperty(t.prototype,"element",{get:function(){return r(this)},enumerable:false,configurable:true});return t}());a.style=i}}}));
//# sourceMappingURL=p-941447ee.system.entry.js.map