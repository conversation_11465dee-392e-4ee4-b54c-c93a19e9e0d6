import{r as a,c as t,h as r,H as i}from"./p-C3J6Z5OX.js";const e=".host{position:relative;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content}.avatar__group{display:-ms-flexbox;display:flex}.avatar__group>*:nth-child(1){z-index:1}.avatar__group>*:nth-child(2){z-index:2}.avatar__group>*:nth-child(3){z-index:3}.avatar__group>*:nth-child(4){z-index:4}.avatar__group>*:nth-child(5){z-index:5}.avatar__group>*:nth-child(6){z-index:6;width:auto}.avatar__group>*:nth-child(6) div{background-color:#292929;padding:0 16px;width:auto}.avatar__group__click--true{cursor:pointer}.avatar__group .avatar{position:relative}.avatar__group__size--extra-small{margin-left:8px}.avatar__group__size--extra-small>*{margin-left:-8px}.avatar__group__size--extra-small>*:nth-child(6) div{padding:0 8px}.avatar__group__size--extra-small .avatar{height:32px}.avatar__group__size--small{margin-left:8px}.avatar__group__size--small>*{margin-left:-8px}.avatar__group__size--small .avatar{height:40px}.avatar__group__size--standard{margin-left:16px}.avatar__group__size--standard>*{margin-left:-16px}.avatar__group__size--standard .avatar{height:56px}.avatar__group__size--large{margin-left:16px}.avatar__group__size--large>*{margin-left:-16px}.avatar__group__size--large .avatar{height:64px}.avatar__group__size--extra-large{margin-left:16px}.avatar__group__size--extra-large>*{margin-left:-16px}.avatar__group__size--extra-large .avatar{height:72px}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}";const s=class{constructor(r){a(this,r);this.bdsClickAvatarGroup=t(this,"bdsClickAvatarGroup");this.size="standard";this.avatarBgColor=a=>{const t=["system","success","warning","error","info"];return t[a]}}handleClickGroup(a){a.preventDefault();this.bdsClickAvatarGroup.emit(a)}handleClickKey(a){if((a.key==="Enter"||a.key===" ")&&this.canClick){a.preventDefault();this.bdsClickAvatarGroup.emit()}}parseUsers(){if(this.users){try{this.internalUsers=typeof this.users==="string"?JSON.parse(this.users):this.users}catch(a){this.internalUsers=[]}}}componentWillLoad(){this.users&&this.parseUsers();this.leftoversUsers=this.internalUsers.length-5}render(){return r(i,{key:"31b6ae002cf06ab2e205c4658d5af725f8fd3409",class:"host"},r("div",{key:"e22014bf3f77fc82b4ee3c5517ea7d74b1be8d76",class:{avatar__group:true,[`avatar__group__size--${this.size}`]:true,[`avatar__group__click--${this.canClick}`]:true},tabindex:"0",onClick:a=>this.handleClickGroup(a)},this.internalUsers?this.internalUsers.slice(0,6).map(((a,t,i)=>t+1===i.length&&this.internalUsers.length>5?r("bds-avatar",{key:t,name:a.name,color:"surface",size:this.size,ellipsis:this.leftoversUsers}):r("bds-avatar",{key:t,id:a.id,name:a.name,thumbnail:a.thumbnail,color:this.avatarBgColor(t),size:this.size}))):r("slot",null)),this.canClick?r("div",{class:"focus",tabindex:"0",onClick:()=>this.handleClickKey}):"")}};s.style=e;export{s as bds_avatar_group};
//# sourceMappingURL=p-d7dc7289.entry.js.map