{"version": 3, "names": ["menuListCss", "MenuList", "exports", "class_1", "prototype", "render", "h", "Host", "key", "class"], "sources": ["src/components/menu-list/menu-list.scss?tag=bds-menu-list&encapsulation=shadow", "src/components/menu-list/menu-list.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$menu-list-width: 7px;\n$menu-list-height: 56px;\n$menu-list-border: 8px;\n\n$menu-list-border-left: 8px 0px 0px 8px;\n$menu-list-border-right: 0px 8px 8px 0px;\n\n.menu-list {\n  display: flex;\n  width: fit-content;\n  box-shadow: $shadow-2;\n  height: $menu-list-height;\n  border-radius: $menu-list-border;\n\n  bds-menu-list-item + bds-menu-list-item {\n    border-left: 1px solid $color-neutral-medium-wave;\n  }\n\n  &__left {\n    width: $menu-list-width;\n    height: $menu-list-height;\n    border-radius: $menu-list-border-left;\n    background-color: $color-neutral-light-snow;\n  }\n\n  &__right {\n    width: $menu-list-width;\n    height: $menu-list-height;\n    border-radius: $menu-list-border-right;\n    background-color: $color-neutral-light-snow;\n  }\n}\n", "import { Component, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-menu-list',\n  styleUrl: 'menu-list.scss',\n  shadow: true,\n})\nexport class MenuList {\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div class=\"menu-list\">\n          <div class=\"menu-list__left\"></div>\n          <slot></slot>\n          <div class=\"menu-list__right\"></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAc,ojB,ICOPC,EAAQC,EAAA,2B,wBACnBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,aACTH,EAAK,OAAAE,IAAA,2CAAAC,MAAM,oBACXH,EAAa,QAAAE,IAAA,6CACbF,EAAA,OAAAE,IAAA,2CAAKC,MAAM,sB,WAPA,I", "ignoreList": []}