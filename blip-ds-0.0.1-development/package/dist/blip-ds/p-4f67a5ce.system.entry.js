var __awaiter=this&&this.__awaiter||function(t,i,e,r){function n(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r["throw"](t))}catch(t){o(t)}}function c(t){t.done?e(t.value):n(t.value).then(s,a)}c((r=r.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,n,o,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(t){return function(i){return c([t,i])}}function c(a){if(r)throw new TypeError("Generator is already executing.");while(s&&(s=0,a[0]&&(e=0)),e)try{if(r=1,n&&(o=a[0]&2?n["return"]:a[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;if(n=0,o)a=[a[0]&2,o.value];switch(a[0]){case 0:case 1:o=a;break;case 4:e.label++;return{value:a[1],done:false};case 5:e.label++;n=a[1];a=[0];continue;case 7:a=e.ops.pop();e.trys.pop();continue;default:if(!(o=e.trys,o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){e.label=a[1];break}if(a[0]===6&&e.label<o[1]){e.label=o[1];o=a;break}if(o&&e.label<o[2]){e.label=o[2];e.ops.push(a);break}if(o[2])e.ops.pop();e.trys.pop();continue}a=i.call(t,e)}catch(t){a=[6,t];n=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(t,i,e){if(e||arguments.length===2)for(var r=0,n=i.length,o;r<n;r++){if(o||!(r in i)){if(!o)o=Array.prototype.slice.call(i,0,r);o[r]=i[r]}}return t.concat(o||Array.prototype.slice.call(i))};System.register(["./p-B47mPBRA.system.js","./p-DLraUrU1.system.js"],(function(t){"use strict";var i,e,r,n,o,s;return{setters:[function(t){i=t.r;e=t.c;r=t.h;n=t.H},function(t){o=t.w;s=t.e}],execute:function(){var a=':host{display:display}:host{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}:host input,:host textarea{-webkit-box-shadow:inherit;box-shadow:inherit}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-moz-placeholder,:host textarea::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input:-ms-input-placeholder,:host textarea:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-ms-input-placeholder,:host textarea::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::placeholder,:host textarea::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 9px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 8px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;margin-right:8px;padding:2.5px}.input__icon--large{padding:4px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;gap:6px;min-height:24px;max-width:100%;overflow-y:auto;overflow-x:hidden;word-break:break-word;}.input__container__wrapper::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;min-width:80px;max-width:100%;-ms-flex:1;flex:1;border:none;outline:none;background:transparent;resize:none;cursor:inherit;overflow:hidden;}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-disable, rgb(89, 89, 89));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}';var c=t("bds_input_chips",function(){function t(t){var r=this;i(this,t);this.bdsChange=e(this,"bdsChange");this.bdsChangeChips=e(this,"bdsChangeChips");this.bdsInputChipsFocus=e(this,"bdsInputChipsFocus");this.bdsBlur=e(this,"bdsBlur");this.bdsInputChipsInput=e(this,"bdsInputChipsInput");this.bdsExtendedQuantityInput=e(this,"bdsExtendedQuantityInput");this.bdsSubmit=e(this,"bdsSubmit");this.InputSize=null;this.validationDanger=false;this.inputAvalible=true;this.isPressed=false;this.validationMesage="";this.internalChips=[];this.chips=[];this.blurCreation=false;this.type="text";this.label="";this.icon="";this.delimiters=/,|;/;this.errorMessage="";this.danger=false;this.success=false;this.value="";this.duplicated=false;this.disableSubmit=false;this.disabled=false;this.helperMessage="";this.successMessage="";this.inputName="";this.placeholder="";this.counterLength=false;this.dataTest=null;this.dtButtonClose=null;this.onClickWrapper=function(){r.onFocus();if(r.nativeInput){r.nativeInput.focus()}};this.onFocus=function(){r.bdsInputChipsFocus.emit();r.isPressed=true};this.onInput=function(t){var i=t.target;if(i){r.value=i.value||""}r.bdsInputChipsInput.emit(t)};this.keyPressWrapper=function(t){switch(t.key){case"Enter":r.handleDelimiters();r.setChip(r.value);r.value="";r.bdsChange.emit({data:r.internalChips,value:r.getLastChip()});r.bdsChangeChips.emit({data:r.internalChips,value:r.getLastChip()});break;case"Backspace":case"Delete":if((r.value===null||r.value.length<=0)&&r.internalChips.length){r.removeLastChip();r.bdsChange.emit({data:r.internalChips,value:r.getLastChip()});r.bdsChangeChips.emit({data:r.internalChips,value:r.getLastChip()})}break}}}t.prototype.valueChanged=function(){if(this.chips){if(typeof this.chips==="string"){try{this.internalChips=JSON.parse(this.chips)}catch(t){this.internalChips=[]}}else{this.internalChips=this.chips}}else{this.internalChips=[]}};t.prototype.internalValueChanged=function(){this.minMaxValidation()};t.prototype.isValid=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.validateChips()]}))}))};t.prototype.get=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.internalChips]}))}))};t.prototype.clear=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.internalChips=[];this.value="";return[2]}))}))};t.prototype.add=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){this.handleDelimiters();if(t){this.setChip(t)}else{this.setChip(this.value)}this.value="";return[2]}))}))};t.prototype.setFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.nativeInput.focus();return[2]}))}))};t.prototype.removeFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.nativeInput.blur();return[2]}))}))};t.prototype.componentDidLoad=function(){this.minMaxValidation()};t.prototype.componentWillLoad=function(){this.valueChanged()};t.prototype.validateChips=function(){var t=this;if(this.type==="email"){return!this.internalChips.some((function(i){return!t.validateChip(i)}))}else{return true}};t.prototype.handleOnBlur=function(){this.bdsBlur.emit(this.internalChips);if(this.internalChips.length>0){this.bdsSubmit.emit({value:this.internalChips})}this.handleDelimiters();this.isPressed=false;if(this.blurCreation){this.setChip(this.value);this.value=""}};t.prototype.minMaxValidation=function(){if(!this.maxChipsLength==undefined){this.inputAvalible=true}else if(this.internalChips.length>=this.maxChipsLength){this.inputAvalible=false;this.bdsExtendedQuantityInput.emit({value:!this.inputAvalible})}else{this.inputAvalible=true}};t.prototype.getLastChip=function(){return this.internalChips[this.internalChips.length-1]};t.prototype.handleDelimiters=function(){var t=this;var i=this.nativeInput.value;this.value=i?i.trim():"";if(i.length===0)return;var e=i.match(this.delimiters);if(!e)return;var r=this.verifyAndSubstituteDelimiters(i);if(!r){this.clearInputValues();return}var n=r.split(this.delimiters);n.forEach((function(i){t.setChip(i)}));this.clearInputValues()};t.prototype.verifyAndSubstituteDelimiters=function(t){if(t.length===1&&t[0].match(this.delimiters)){return""}var i=t.replace(/;/g,",").replace(/\,+|;+/g,",");if(i[0].match(this.delimiters)){i=i.substring(1)}return i};t.prototype.handleChange=function(t){return __awaiter(this,void 0,void 0,(function(){var i,e,r,n;var o=this;return __generator(this,(function(s){i=t.detail.value;this.value=i?i.trim():"";if(i.length===0)return[2];e=i.match(this.delimiters);if(!e)return[2];r=this.verifyAndSubstituteDelimiters(i);if(!r){this.clearInputValues();return[2]}n=r.split(this.delimiters);n.forEach((function(t){o.setChip(t.trimStart())}));this.clearInputValues();this.bdsChange.emit({data:this.internalChips,value:this.getLastChip()});this.bdsChangeChips.emit({data:this.internalChips,value:this.getLastChip()});return[2]}))}))};t.prototype.clearInputValues=function(t){if(t===void 0){t=""}this.nativeInput.value=t;this.value=t};t.prototype.setChip=function(t){if(!this.duplicated){var i=this.internalChips.some((function(i){return i.toLowerCase()===t.toLowerCase()}));if(i)return}if(!o(t)){return}this.internalChips=__spreadArray(__spreadArray([],this.internalChips,true),[t],false)};t.prototype.validateChip=function(t){var i=t.trim();if(this.type==="email"&&s(i)){return false}return true};t.prototype.removeLastChip=function(){this.internalChips=this.internalChips.slice(0,this.internalChips.length-1)};t.prototype.removeChip=function(t){var i=t.detail.id;this.internalChips=this.internalChips.filter((function(t,e){return e.toString()!==i}));this.bdsChange.emit({data:this.internalChips,value:this.getLastChip()});this.bdsChangeChips.emit({data:this.internalChips,value:this.getLastChip()})};t.prototype.renderChips=function(){var t=this;if(!this.internalChips.length){return[]}return this.internalChips.map((function(i,e){var n=e.toString();var o=20;if(i.length<=o){return r("bds-chip-clickable",{id:n,key:n,color:"outline",close:!t.disabled,onChipClickableClose:function(i){return t.removeChip(i)},dtButtonClose:t.dtButtonClose},i)}else{return r("bds-tooltip",{key:n,position:"top-center","tooltip-text":i},r("bds-chip-clickable",{id:n,key:n,color:"outline",close:!t.disabled,onChipClickableClose:function(i){return t.removeChip(i)},dtButtonClose:t.dtButtonClose},"".concat(i.slice(0,o),"...")))}}))};t.prototype.renderIcon=function(){return this.icon&&r("div",{class:{input__icon:true,"input__icon--large":!!this.label}},r("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))};t.prototype.renderLabel=function(){return this.label&&r("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},r("bds-typo",{variant:"fs-12",bold:"bold"},this.label))};t.prototype.renderMessage=function(){var t=this.danger?"error":this.success?"checkball":"info";var i=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!i&&this.validationDanger)i=this.validationMesage;var e=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(i){return r("div",{class:e,part:"input__message"},r("div",{class:"input__message__icon"},r("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),r("bds-typo",{class:"input__message__text",variant:"fs-12"},i))}return undefined};t.prototype.render=function(){var t=this;var i=this.isPressed&&!this.disabled;var e=this.maxHeight||"80px";return r(n,{key:"c682a9905bc1bbdbceaa54cbbdebab923e0803a9","aria-disabled":this.disabled?"true":null},r("div",{key:"172a048d81591655fe4a13f387f4cf2d47f02bc8",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":i},onClick:this.onClickWrapper,onKeyDown:this.keyPressWrapper,part:"input-container"},this.renderIcon(),r("div",{key:"95a0579433aa22fc5bce731fd5689f4b47f32b7a",class:"input__container"},this.renderLabel(),r("div",{key:"bd8221fd70876b912baf5765ce6cf8c546121993",class:"input__container__wrapper",style:{maxHeight:e}},this.internalChips.length>0&&this.renderChips(),this.inputAvalible&&r("input",{key:"0424c72fdd1595c5acb88aa89beef8b84081402d",ref:function(i){return t.nativeInput=i},class:"input__container__text",name:this.inputName,maxlength:this.maxlength,placeholder:this.placeholder,onInput:this.onInput,onFocus:this.onFocus,onBlur:function(){return t.handleOnBlur()},onChange:function(){return t.handleChange},value:this.value,disabled:this.disabled,"data-test":this.dataTest}))),this.counterLength&&r("bds-counter-text",{key:"5dcbd43cf9bac1444f9a05880c2315adb3ffcb72",length:this.internalChips.length,max:this.maxChipsLength,active:i}),this.success&&r("bds-icon",{key:"51801e5c5471306eccc0d88773295c50b19e7f80",class:"icon-success",name:"checkb",theme:"outline",size:"xxx-small"}),r("slot",{key:"56decc59c8872669d0a1f46050d4386da9e628ff",name:"input-right"})),this.renderMessage())};Object.defineProperty(t,"watchers",{get:function(){return{chips:["valueChanged"],internalChips:["internalValueChanged"]}},enumerable:false,configurable:true});return t}());c.style=a}}}));
//# sourceMappingURL=p-4f67a5ce.system.entry.js.map