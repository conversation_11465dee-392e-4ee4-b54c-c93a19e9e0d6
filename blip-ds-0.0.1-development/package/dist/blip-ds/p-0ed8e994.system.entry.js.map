{"version": 3, "names": ["clearPathsAndFillColor", "svg", "color", "paths", "getElementsByTagName", "i", "length", "setAttribute", "formatSvg", "svgContent", "emoji", "div", "document", "createElement", "innerHTML", "svgElm", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeAttribute", "getIconName", "name", "theme", "concat", "getEmojiName", "getLogoName", "iconCss", "Icon", "exports", "class_1", "hostRef", "_this", "this", "isVisible", "size", "lazy", "type", "dataTest", "setSvg<PERSON><PERSON>nt", "key", "atob", "icons", "emojis", "logo", "err", "console", "warn", "prototype", "connectedCallback", "waitUntilVisible", "el", "loadIcon", "disconnectedCallback", "io", "disconnect", "undefined", "cb", "window", "IntersectionObserver", "io_1", "data", "isIntersecting", "observe", "aria<PERSON><PERSON><PERSON>", "label", "render", "h", "Host", "role", "class", "_a"], "sources": ["src/components/icon/utils.ts", "src/components/icon/icon.scss?tag=bds-icon&encapsulation=shadow", "src/components/icon/icon.tsx"], "sourcesContent": ["import { IconTheme } from './icon-interface';\n\nconst clearPathsAndFillColor = (svg: Element, color: string): void => {\n  const paths = svg.getElementsByTagName('path');\n\n  for (let i = 0; i < paths.length; i++) {\n    paths[i].setAttribute('fill', color);\n  }\n\n  svg.setAttribute('fill', color);\n};\n\nexport const formatSvg = (svgContent: string | null, color: string | null, emoji = false): string => {\n  if (svgContent) {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    svgElm.setAttribute('fill', 'currentColor');\n\n    if (!emoji) {\n      clearPathsAndFillColor(svgElm, color || 'currentColor');\n    }\n    return div.innerHTML;\n  }\n\n  return '';\n};\n\nexport const getIconName = (name: string, theme: IconTheme) => {\n  return `asset-icon-${name}-${theme}`;\n};\n\nexport const getEmojiName = (name: string) => {\n  return `asset-emoji-${name}`;\n};\n\nexport const getLogoName = (name: string) => {\n  return `asset-logo-${name}`;\n};\n", "@use '../../globals/helpers' as *;\n\n@mixin size($size) {\n  width: $size;\n  min-width: $size;\n  height: $size;\n  max-height: $size;\n  min-height: $size;\n}\n\n$icon-brand: 64px;\n$icon-xxx-large: 40px;\n$icon-xx-large: 36px;\n$icon-x-large: 32px;\n$icon-large: 28px;\n$icon-medium: 24px;\n$icon-small: 20px;\n$icon-x-small: 16px;\n$icon-xx-small: 12px;\n\n:host {\n  width: 1em;\n  height: 1em;\n  color: $color-content-default;\n\n  fill: currentColor;\n\n  box-sizing: content-box !important;\n}\n\n.icon-inner {\n  max-width: 100%;\n  height: 100%;\n}\n\n.emoji-inner {\n  max-width: 100%;\n  height: 100%;\n  display: flex;\n}\n\n:host(.bds-icon) {\n  display: inline-block;\n  svg {\n    fill: currentColor;\n    width: 100%;\n    min-width: 100%;\n  }\n}\n\n:host(.bds-icon__size--brand) {\n  @include size($icon-brand);\n}\n\n:host(.bds-icon__size--xxx-large) {\n  @include size($icon-xxx-large);\n}\n\n:host(.bds-icon__size--xx-large) {\n  @include size($icon-xx-large);\n}\n\n:host(.bds-icon__size--x-large) {\n  @include size($icon-x-large);\n}\n\n:host(.bds-icon__size--large) {\n  @include size($icon-large);\n}\n\n:host(.bds-icon__size--medium) {\n  @include size($icon-medium);\n}\n\n:host(.bds-icon__size--small) {\n  @include size($icon-small);\n}\n\n:host(.bds-icon__size--x-small) {\n  @include size($icon-x-small);\n}\n\n:host(.bds-icon__size--xx-small) {\n  @include size($icon-xx-small);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Build, Component, Element, Host, Prop, State, Watch, h } from '@stencil/core';\nimport icons from 'blip-tokens/build/json/assets_icons.json';\nimport emojis from 'blip-tokens/build/json/assets_emojis.json';\nimport logo from 'blip-tokens/build/json/assets_logos.json';\nimport { IconSize, IconTheme, IconType } from './icon-interface';\nimport { formatSvg, getIconName, getEmojiName, getLogoName } from './utils';\n\n@Component({\n  tag: 'bds-icon',\n  assetsDirs: ['svg'],\n  styleUrl: 'icon.scss',\n  shadow: true,\n})\nexport class Icon {\n  private io?: IntersectionObserver;\n\n  @Element() el!: HTMLElement;\n\n  @State() private svgContent?: string;\n  @State() private isVisible = false;\n\n  /**\n   * Specifies the color to use.Specifies a color to use. The default is svg.\n   */\n  @Prop() color?: string;\n\n  /**\n   * Specifies the label to use for accessibility. Defaults to the icon name.\n   */\n  @Prop({ mutable: true, reflect: true }) ariaLabel: string;\n\n  /**\n   * Specifies whether the icon should horizontally flip when `dir` is `\"rtl\"`.\n   */\n  @Prop() flipRtl?: boolean;\n\n  /**\n   * Specifies which icon to use from the built-in set of icons.\n   */\n  @Prop() name?: string;\n\n  /**\n   * Specifies the exact `src` of an SVG file to use.\n   */\n  @Prop() src?: string;\n\n  /**\n   * A combination of both `name` and `src`. If a `src` url is detected\n   * it will set the `src` property. Otherwise it assumes it's a built-in named\n   * SVG and set the `name` property.\n   */\n  @Prop() icon?: any;\n\n  /**\n   * Icon size. Entered as one of the icon size design tokens. Can be one of:\n   * \"xxx-small\", \"xx-small\", \"x-small\", \"small\", \"medium\", \"large\", \"x-large\", \"xx-large\", \"xxx-large\", \"brand\".\n   */\n  @Prop() size?: IconSize = 'medium';\n\n  /**\n   * If enabled, ion-icon will be loaded lazily when it's visible in the viewport.\n   * Default, `false`.\n   */\n  @Prop() lazy = false;\n\n  /**\n   * Specifies the theme to use outline or solid icons. Defaults to outline.\n   */\n  @Prop({ reflect: true }) theme: IconTheme = 'outline';\n\n  /**\n   * Specifies the type of icon. If type is set to emoji, it will be able to set only emoji names on the name property.\n   */\n  @Prop() type: IconType = 'icon';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    // purposely do not return the promise here because loading\n    // the svg file should not hold up loading the app\n    // only load the svg if it's visible\n    this.waitUntilVisible(this.el, () => {\n      this.isVisible = true;\n      this.loadIcon();\n    });\n  }\n\n  disconnectedCallback(): void {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n\n  private waitUntilVisible(el: HTMLElement, cb: () => void): void {\n    if (Build.isBrowser && this.lazy && typeof window !== 'undefined' && (window as any).IntersectionObserver) {\n      const io = (this.io = new (window as any).IntersectionObserver((data: IntersectionObserverEntry[]) => {\n        if (data[0].isIntersecting) {\n          io.disconnect();\n          this.io = undefined;\n          cb();\n        }\n      }));\n\n      io.observe(el);\n    } else {\n      // browser doesn't support IntersectionObserver\n      // so just fallback to always show it\n      cb();\n    }\n  }\n\n  @Watch('name')\n  @Watch('src')\n  @Watch('icon')\n  @Watch('theme')\n  loadIcon(): void {\n    if (!this.name) return;\n\n    if (Build.isBrowser && this.isVisible) {\n      this.setSvgContent();\n    }\n\n    if (!this.ariaLabel) {\n      const label = this.name;\n      if (label) {\n        this.ariaLabel = label;\n      }\n    }\n  }\n\n  setSvgContent = () => {\n    let svg;\n    try {\n      if (this.type === 'icon') {\n        const key = getIconName(this.name, this.theme);\n        svg = atob(icons[key]);\n        this.svgContent = formatSvg(svg, this.color);\n      } else if (this.type === 'emoji') {\n        const key = getEmojiName(this.name);\n        svg = atob(emojis[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      } else if (this.type === 'logo') {\n        const key = getLogoName(this.name);\n        svg = atob(logo[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn('[Warning]: Failed to setSvgContent to', this.name);\n    }\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-icon': true,\n          [`bds-icon__size--${this.size}`]: true,\n        }}\n      >\n        {this.svgContent ? (\n          <div\n            class={{\n              'icon-inner': this.type === 'icon',\n              'emoji-inner': this.type === 'emoji',\n              'logo-inner': this.type === 'logo',\n            }}\n            innerHTML={this.svgContent}\n            data-test={this.dataTest}\n          ></div>\n        ) : (\n          <div class=\"icon-inner\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "o3g7GAEA,IAAMA,EAAyB,SAACC,EAAcC,GAC5C,IAAMC,EAAQF,EAAIG,qBAAqB,QAEvC,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAMG,OAAQD,IAAK,CACrCF,EAAME,GAAGE,aAAa,OAAQL,E,CAGhCD,EAAIM,aAAa,OAAQL,EAC3B,EAEO,IAAMM,EAAY,SAACC,EAA2BP,EAAsBQ,GAAA,GAAAA,SAAA,GAAAA,EAAA,KAAa,CACtF,GAAID,EAAY,CACd,IAAME,EAAMC,SAASC,cAAc,OACnCF,EAAIG,UAAYL,EAEhB,IAAMM,EAASJ,EAAIK,kBAEnBD,EAAOE,gBAAgB,SACvBF,EAAOE,gBAAgB,UACvBF,EAAOR,aAAa,OAAQ,gBAE5B,IAAKG,EAAO,CACVV,EAAuBe,EAAQb,GAAS,e,CAE1C,OAAOS,EAAIG,S,CAGb,MAAO,EACT,EAEO,IAAMI,EAAc,SAACC,EAAcC,GACxC,MAAO,cAAAC,OAAcF,EAAI,KAAAE,OAAID,EAC/B,EAEO,IAAME,EAAe,SAACH,GAC3B,MAAO,eAAAE,OAAeF,EACxB,EAEO,IAAMI,EAAc,SAACJ,GAC1B,MAAO,cAAAE,OAAcF,EACvB,EC1CA,IAAMK,EAAU,0xC,ICcHC,EAAIC,EAAA,sBANjB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,UAYmBA,KAASC,UAAG,MAsCrBD,KAAIE,KAAc,SAMlBF,KAAIG,KAAG,MAKUH,KAAKV,MAAc,UAKpCU,KAAII,KAAa,OAKjBJ,KAAQK,SAAY,KAwD5BL,KAAaM,cAAG,WACd,IAAInC,EACJ,IACE,GAAI4B,EAAKK,OAAS,OAAQ,CACxB,IAAMG,EAAMnB,EAAYW,EAAKV,KAAMU,EAAKT,OACxCnB,EAAMqC,KAAKC,EAAMF,IACjBR,EAAKpB,WAAaD,EAAUP,EAAK4B,EAAK3B,M,MACjC,GAAI2B,EAAKK,OAAS,QAAS,CAChC,IAAMG,EAAMf,EAAaO,EAAKV,MAC9BlB,EAAMqC,KAAKE,EAAOH,IAClBR,EAAKpB,WAAaD,EAAUP,EAAK4B,EAAK3B,MAAO,K,MACxC,GAAI2B,EAAKK,OAAS,OAAQ,CAC/B,IAAMG,EAAMd,EAAYM,EAAKV,MAC7BlB,EAAMqC,KAAKG,EAAKJ,IAChBR,EAAKpB,WAAaD,EAAUP,EAAK4B,EAAK3B,MAAO,K,EAE/C,MAAOwC,GAEPC,QAAQC,KAAK,wCAAyCf,EAAKV,K,CAE/D,CA2BD,CArGCQ,EAAAkB,UAAAC,kBAAA,eAAAjB,EAAAC,KAIEA,KAAKiB,iBAAiBjB,KAAKkB,IAAI,WAC7BnB,EAAKE,UAAY,KACjBF,EAAKoB,UACP,G,EAGFtB,EAAAkB,UAAAK,qBAAA,WACE,GAAIpB,KAAKqB,GAAI,CACXrB,KAAKqB,GAAGC,aACRtB,KAAKqB,GAAKE,S,GAIN1B,EAAAkB,UAAAE,iBAAA,SAAiBC,EAAiBM,GAAlC,IAAAzB,EAAAC,KACN,GAAuBA,KAAKG,aAAesB,SAAW,aAAgBA,OAAeC,qBAAsB,CACzG,IAAMC,EAAM3B,KAAKqB,GAAK,IAAKI,OAAeC,sBAAqB,SAACE,GAC9D,GAAIA,EAAK,GAAGC,eAAgB,CAC1BF,EAAGL,aACHvB,EAAKsB,GAAKE,UACVC,G,KAIJG,EAAGG,QAAQZ,E,KACN,CAGLM,G,GAQJ3B,EAAAkB,UAAAI,SAAA,WACE,IAAKnB,KAAKX,KAAM,OAEhB,GAAuBW,KAAKC,UAAW,CACrCD,KAAKM,e,CAGP,IAAKN,KAAK+B,UAAW,CACnB,IAAMC,EAAQhC,KAAKX,KACnB,GAAI2C,EAAO,CACThC,KAAK+B,UAAYC,C,IA2BvBnC,EAAAkB,UAAAkB,OAAA,W,MACE,OACEC,EAACC,EAAI,CAAA5B,IAAA,2CACH6B,KAAK,MACLC,OAAKC,EAAA,CACH,WAAY,MACZA,EAAC,mBAAA/C,OAAmBS,KAAKE,OAAS,K,IAGnCF,KAAKrB,WACJuD,EACE,OAAAG,MAAO,CACL,aAAcrC,KAAKI,OAAS,OAC5B,cAAeJ,KAAKI,OAAS,QAC7B,aAAcJ,KAAKI,OAAS,QAE9BpB,UAAWgB,KAAKrB,WACL,YAAAqB,KAAKK,WAGlB6B,EAAK,OAAAG,MAAM,aAAwB,YAAArC,KAAKK,W,yYAnKjC,I", "ignoreList": []}