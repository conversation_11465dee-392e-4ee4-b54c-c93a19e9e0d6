{"version": 3, "file": "bds-table-header.entry.esm.js", "sources": ["src/components/table/table-header/table-header.scss?tag=bds-table-header&encapsulation=scoped", "src/components/table/table-header/table-header.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-header-group;\n  border-bottom: 1px solid $color-border-1;\n}", "import { Component, h, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-header',\n  styleUrl: 'table-header.scss',\n  scoped: true,\n})\nexport class TableHeader {\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAG,sHAAsH;;MCOhI,WAAW,GAAA,MAAA;;;;IACtB,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;"}