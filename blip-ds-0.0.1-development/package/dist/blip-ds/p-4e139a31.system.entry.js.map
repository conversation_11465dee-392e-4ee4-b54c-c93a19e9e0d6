{"version": 3, "names": ["avatarGroupCss", "AvatarGroup", "exports", "class_1", "hostRef", "this", "size", "avatarBgColor", "number", "colors", "prototype", "handleClickGroup", "e", "preventDefault", "bdsClickAvatarGroup", "emit", "handleClickKey", "event", "key", "canClick", "parseUsers", "users", "internalUsers", "JSON", "parse", "componentWillLoad", "leftoversUsers", "length", "render", "_this", "h", "Host", "class", "_a", "avatar__group", "concat", "tabindex", "onClick", "slice", "map", "user", "i", "row", "name", "color", "ellipsis", "id", "thumbnail"], "sources": ["src/components/avatar-group/avatar-group.scss?tag=bds-avatar-group&encapsulation=shadow", "src/components/avatar-group/avatar-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$avatar-size-extra-small :32px;\n$avatar-size-small :40px;\n$avatar-size-standard :56px;\n$avatar-size-large :64px;\n$avatar-size-extra-large :72px;\n\n.host {\n  position: relative;\n  height: fit-content;\n}\n.avatar__group {\n  display: flex;\n\n  & > *{\n    &:nth-child(1) {\n      z-index: 1;\n    }\n    &:nth-child(2) {\n      z-index: 2;\n    }\n    &:nth-child(3) {\n      z-index: 3;\n    }\n    &:nth-child(4) {\n      z-index: 4;\n    }\n    &:nth-child(5) {\n      z-index: 5;\n    }\n    &:nth-child(6) {\n      z-index: 6;\n      width: auto;\n      div {\n        background-color: $color-neutral-dark-onix;\n        padding: 0 16px;\n        width: auto;\n      }\n    }\n  }\n\n  &__click {\n    &--true {\n      cursor: pointer;\n    }\n  }\n\n  .avatar {\n    position: relative;\n  }\n  &__size {\n    &--extra-small{\n      margin-left: 8px ;\n      & > *{\n        &:nth-child(6) {\n          div {\n            padding: 0 8px;\n          }\n        }\n        margin-left: -8px ;\n      }\n      .avatar {\n        height: $avatar-size-extra-small;\n      }\n    }\n    &--small{\n      margin-left: 8px ;\n      & > *{\n        margin-left: -8px ;\n      }\n      .avatar {\n        height: $avatar-size-small;\n        \n      }\n    }\n    &--standard{\n      margin-left: 16px ;\n      & > *{\n        margin-left: -16px ;\n      }\n      .avatar {\n        height: $avatar-size-standard;\n      }\n    }\n    &--large{\n      margin-left: 16px ;\n      & > *{\n        margin-left: -16px ;\n      }\n      .avatar {\n        height: $avatar-size-large;\n      }\n    }\n    &--extra-large{\n      margin-left: 16px ;\n      & > *{\n        margin-left: -16px ;\n      }\n      .avatar {\n        height: $avatar-size-extra-large;\n      }\n    }\n  }\n}\n\n.focus:focus-visible {\n  display: flex;\n  position: absolute;\n  border: 2px solid $color-focus;\n  border-radius: 4px;\n  width: 100%;\n  height: 100%;\n  top: -4px;\n  left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  outline: none;\n}\n", "import { Component, h, Host, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { AvatarDataList } from './avatar-group-interface';\nimport { colors } from '../avatar/avatar';\n\nexport type avatarSize = 'extra-small' | 'small' | 'standard';\n\n@Component({\n  tag: 'bds-avatar-group',\n  styleUrl: 'avatar-group.scss',\n  shadow: true,\n})\nexport class AvatarGroup {\n  @State() internalUsers: AvatarDataList[];\n  @State() leftoversUsers: number;\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'extra-small', 'small', 'standard', 'large', 'extra-large'.\n   */\n  @Prop() size?: avatarSize = 'standard';\n  /**\n  * The users of the select\n  * Should be passed this way:\n  * users='[\n      {\"id\": \"1\", \"name\": \"<PERSON>\", \"thumbnail\": \"https://gcdn.pbrd.co/images/9Kt8iMvR10Lf.jpg?o=1\"},\n      {\"id\": \"2\", \"name\": \"<PERSON>\", \"thumbnail\": \"https://gcdn.pbrd.co/images/XAlbTPDwjZ2d.jpg?o=1\"},\n      {\"id\": \"3\", \"name\": \"Jim Halpert\", \"thumbnail\": \"https://gcdn.pbrd.co/images/tK0Ygb0KAHUm.jpg?o=1\"},\n      {\"id\": \"4\", \"name\": \"Pam Beesly\", \"thumbnail\": \"https://gcdn.pbrd.co/images/8NZSnCGfB9BD.jpg?o=1\"},\n      {\"id\": \"5\", \"name\": \"Ryan Howard\", \"thumbnail\": \"https://gcdn.pbrd.co/images/6wwIWI1EzzVq.jpg?o=1\"},\n      {\"id\": \"6\", \"name\": \"Andy Bernard\", \"thumbnail\": \"https://gcdn.pbrd.co/images/5dPYFWixftY4.jpg?o=1\"}\n    ]'\n  * users can also be passed as child by using bds-avatar-group component, but passing as a child you may have some compatibility problems with Angular.\n  */\n  @Prop() users?: string | AvatarDataList[];\n  @Prop() canClick?: boolean;\n  @Event() bdsClickAvatarGroup: EventEmitter;\n\n  handleClickGroup(e) {\n    e.preventDefault();\n    this.bdsClickAvatarGroup.emit(e);\n  }\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && this.canClick) {\n      event.preventDefault();\n      this.bdsClickAvatarGroup.emit();\n    }\n  }\n\n  parseUsers() {\n    if (this.users) {\n      try {\n        this.internalUsers = typeof this.users === 'string' ? JSON.parse(this.users) : this.users;\n      } catch (e) {\n        this.internalUsers = [];\n      }\n    }\n  }\n  private avatarBgColor = (number: number): colors => {\n    const colors = ['system', 'success', 'warning', 'error', 'info'];\n    return colors[number] as colors;\n  };\n  componentWillLoad() {\n    this.users && this.parseUsers();\n    this.leftoversUsers = this.internalUsers.length - 5;\n  }\n  render() {\n    return (\n      <Host class=\"host\">\n        <div\n          class={{\n            avatar__group: true,\n            [`avatar__group__size--${this.size}`]: true,\n            [`avatar__group__click--${this.canClick}`]: true,\n          }}\n          tabindex=\"0\"\n          onClick={(e) => this.handleClickGroup(e)}\n        >\n          {this.internalUsers ? (\n            this.internalUsers\n              .slice(0, 6)\n              .map((user, i, row) =>\n                i + 1 === row.length && this.internalUsers.length > 5 ? (\n                  <bds-avatar\n                    key={i}\n                    name={user.name}\n                    color=\"surface\"\n                    size={this.size}\n                    ellipsis={this.leftoversUsers}\n                  ></bds-avatar>\n                ) : (\n                  <bds-avatar\n                    key={i}\n                    id={user.id}\n                    name={user.name}\n                    thumbnail={user.thumbnail}\n                    color={this.avatarBgColor(i)}\n                    size={this.size}\n                  ></bds-avatar>\n                ),\n              )\n          ) : (\n            <slot />\n          )}\n        </div>\n        {this.canClick ? <div class=\"focus\" tabindex=\"0\" onClick={() => this.handleClickKey}></div> : ''}\n      </Host>\n    );\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAiB,+kD,ICWVC,EAAWC,EAAA,8BALxB,SAAAC,EAAAC,G,iEAYUC,KAAIC,KAAgB,WAuCpBD,KAAAE,cAAgB,SAACC,GACvB,IAAMC,EAAS,CAAC,SAAU,UAAW,UAAW,QAAS,QACzD,OAAOA,EAAOD,EAChB,CAgDD,CAxECL,EAAAO,UAAAC,iBAAA,SAAiBC,GACfA,EAAEC,iBACFR,KAAKS,oBAAoBC,KAAKH,E,EAGxBT,EAAAO,UAAAM,eAAA,SAAeC,GACrB,IAAKA,EAAMC,MAAQ,SAAWD,EAAMC,MAAQ,MAAQb,KAAKc,SAAU,CACjEF,EAAMJ,iBACNR,KAAKS,oBAAoBC,M,GAI7BZ,EAAAO,UAAAU,WAAA,WACE,GAAIf,KAAKgB,MAAO,CACd,IACEhB,KAAKiB,qBAAuBjB,KAAKgB,QAAU,SAAWE,KAAKC,MAAMnB,KAAKgB,OAAShB,KAAKgB,K,CACpF,MAAOT,GACPP,KAAKiB,cAAgB,E,IAQ3BnB,EAAAO,UAAAe,kBAAA,WACEpB,KAAKgB,OAAShB,KAAKe,aACnBf,KAAKqB,eAAiBrB,KAAKiB,cAAcK,OAAS,C,EAEpDxB,EAAAO,UAAAkB,OAAA,W,MAAA,IAAAC,EAAAxB,KACE,OACEyB,EAACC,EAAK,CAAAb,IAAA,2CAAAc,MAAM,QACVF,EAAA,OAAAZ,IAAA,2CACEc,OAAKC,EAAA,CACHC,cAAe,MACfD,EAAC,wBAAAE,OAAwB9B,KAAKC,OAAS,KACvC2B,EAAC,yBAAAE,OAAyB9B,KAAKc,WAAa,K,GAE9CiB,SAAS,IACTC,QAAS,SAACzB,GAAM,OAAAiB,EAAKlB,iBAAiBC,EAAtB,GAEfP,KAAKiB,cACJjB,KAAKiB,cACFgB,MAAM,EAAG,GACTC,KAAI,SAACC,EAAMC,EAAGC,GACb,OAAAD,EAAI,IAAMC,EAAIf,QAAUE,EAAKP,cAAcK,OAAS,EAClDG,EACE,cAAAZ,IAAKuB,EACLE,KAAMH,EAAKG,KACXC,MAAM,UACNtC,KAAMuB,EAAKvB,KACXuC,SAAUhB,EAAKH,iBAGjBI,EAAA,cACEZ,IAAKuB,EACLK,GAAIN,EAAKM,GACTH,KAAMH,EAAKG,KACXI,UAAWP,EAAKO,UAChBH,MAAOf,EAAKtB,cAAckC,GAC1BnC,KAAMuB,EAAKvB,MAff,IAoBJwB,EAAQ,cAGXzB,KAAKc,SAAWW,EAAA,OAAKE,MAAM,QAAQI,SAAS,IAAIC,QAAS,WAAM,OAAAR,EAAKb,cAAL,IAA8B,G,WA7F9E,I", "ignoreList": []}