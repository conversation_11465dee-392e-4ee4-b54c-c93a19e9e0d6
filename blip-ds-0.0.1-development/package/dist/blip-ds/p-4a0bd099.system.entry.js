var __awaiter=this&&this.__awaiter||function(t,n,e,i){function r(t){return t instanceof e?t:new e((function(n){n(t)}))}return new(e||(e=Promise))((function(e,o){function s(t){try{u(i.next(t))}catch(t){o(t)}}function c(t){try{u(i["throw"](t))}catch(t){o(t)}}function u(t){t.done?e(t.value):r(t.value).then(s,c)}u((i=i.apply(t,n||[])).next())}))};var __generator=this&&this.__generator||function(t,n){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},i,r,o,s;return s={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function c(t){return function(n){return u([t,n])}}function u(c){if(i)throw new TypeError("Generator is already executing.");while(s&&(s=0,c[0]&&(e=0)),e)try{if(i=1,r&&(o=c[0]&2?r["return"]:c[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;if(r=0,o)c=[c[0]&2,o.value];switch(c[0]){case 0:case 1:o=c;break;case 4:e.label++;return{value:c[1],done:false};case 5:e.label++;r=c[1];c=[0];continue;case 7:c=e.ops.pop();e.trys.pop();continue;default:if(!(o=e.trys,o=o.length>0&&o[o.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!o||c[1]>o[0]&&c[1]<o[3])){e.label=c[1];break}if(c[0]===6&&e.label<o[1]){e.label=o[1];o=c;break}if(o&&e.label<o[2]){e.label=o[2];e.ops.push(c);break}if(o[2])e.ops.pop();e.trys.pop();continue}c=n.call(t,e)}catch(t){c=[6,t];r=0}finally{i=o=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js","./p-KsAJij7V.system.js"],(function(t){"use strict";var n,e,i,r,o,s;return{setters:[function(t){n=t.r;e=t.c;i=t.h;r=t.H},function(t){o=t.g;s=t.d}],execute:function(){var c=".menu{position:fixed;pointer-events:none;top:0;left:0;padding:2px;background-color:var(--color-surface-1, rgb(246, 246, 246));border-radius:8px;-webkit-box-shadow:0px 8px 12px rgba(0, 0, 0, 0.08);box-shadow:0px 8px 12px rgba(0, 0, 0, 0.08);width:240px;opacity:0;-webkit-transition:opacity 0.5s;-moz-transition:opacity 0.5s;transition:opacity 0.5s;z-index:90000}.menu__open{pointer-events:auto;opacity:1}.outzone{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:80000}";var u=t("bds_menu",function(){function t(t){var i=this;n(this,t);this.bdsToggle=e(this,"bdsToggle");this.refElement=null;this.intoView=null;this.menupositionTop=0;this.menupositionLeft=0;this.menu=null;this.position="right";this.open=false;this.refMenuElement=function(t){i.menuElement=t};this.onClickCloseButtom=function(t){i.open=false;t.stopPropagation()}}t.prototype.componentWillLoad=function(){this.refElement=document.getElementById(this.menu);this.intoView=o(this.refElement)};t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.open=!this.open;return[2]}))}))};t.prototype.openMenu=function(){this.bdsToggle.emit({value:this.open});if(this.open){var t=s({actionElement:this.refElement,changedElement:this.menuElement,intoView:this.intoView});this.menupositionTop=t.top;this.menupositionLeft=t.left}};t.prototype.render=function(){var t;var n=this;var e={top:"".concat(this.menupositionTop,"px"),left:"".concat(this.menupositionLeft,"px")};return i(r,{key:"4f7c34c68dd427bfd59e014c4f4ff610e98e359c"},i("div",{key:"1185c26ff01c1a7c14f13381ef70ac1d8c82cad6",ref:this.refMenuElement,class:(t={menu:true},t["menu__".concat(this.position)]=true,t["menu__open"]=this.open,t),style:e},i("slot",{key:"6a6bfcfec6098e494319186be20dc702971cad25"})),this.open&&i("div",{key:"56a7a0e7d3575373fe08af98f862cb6ca77d8729",class:{outzone:true},onClick:function(t){return n.onClickCloseButtom(t)}}))};Object.defineProperty(t,"watchers",{get:function(){return{open:["openMenu"]}},enumerable:false,configurable:true});return t}());u.style=c}}}));
//# sourceMappingURL=p-4a0bd099.system.entry.js.map