import{r as o,c as r,h as e,a as t}from"./p-C3J6Z5OX.js";const i='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';const a=class{constructor(e){o(this,e);this.bdsAccordionCloseAll=r(this,"bdsAccordionCloseAll");this.bdsAccordionOpenAll=r(this,"bdsAccordionOpenAll");this.accordionsElement=null;this.collapse="single";this.divisor=true}async closeAll(o){this.bdsAccordionCloseAll.emit();for(let r=0;r<this.accordionsElement.length;r++){if(this.collapse!="multiple"){if(o!=r)this.accordionsElement[r].close()}else{this.accordionsElement[r].close()}}}async openAll(o){this.bdsAccordionOpenAll.emit();for(let r=0;r<this.accordionsElement.length;r++){if(this.collapse!="multiple"){if(o!=r)this.accordionsElement[r].open()}else{this.accordionsElement[r].open()}}}divisorChanged(o){if(this.accordionsElement){for(let r=0;r<this.accordionsElement.length;r++){this.accordionsElement[r].divisor=o}}}componentWillRender(){this.accordionsElement=this.element.getElementsByTagName("bds-accordion");for(let o=0;o<this.accordionsElement.length;o++){this.accordionsElement[o].reciveNumber(o);this.accordionsElement[o].divisor=this.divisor}}render(){return e("div",{key:"44b6dac1fe73f585accb3841012bec42ff8a0d1d",class:"accordion_group"},e("slot",{key:"dea06f9bc51a0a18c36108706a3cf7f9b1e2128e"}))}get element(){return t(this)}static get watchers(){return{divisor:["divisorChanged"]}}};a.style=i;export{a as bds_accordion_group};
//# sourceMappingURL=p-d06054c1.entry.js.map