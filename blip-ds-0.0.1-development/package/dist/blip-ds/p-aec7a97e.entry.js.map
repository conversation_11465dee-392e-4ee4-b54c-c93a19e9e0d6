{"version": 3, "names": ["imageCss", "Image", "constructor", "hostRef", "this", "imageHasLoading", "objectFit", "dataTest", "imageLoaded", "loadError", "componentDidLoad", "element", "style", "width", "height", "_a", "length", "loadImage", "src", "response", "fetch", "ok", "blob", "objectURL", "URL", "createObjectURL", "currentSrc", "render", "h", "Host", "key", "class", "empty_img", "alt", "filter", "brightness", "draggable", "shape", "type", "name"], "sources": ["src/components/image/image.scss?tag=bds-image&encapsulation=shadow", "src/components/image/image.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n\n  .img-feedback {\n    height: 76%;\n  }\n}\n\n:host(.empty_img) {\n  background-color: $color-surface-3;\n}\n", "import { Element, Component, Prop, Method, State, h, Host } from '@stencil/core';\n\nexport type ObjectFitValue = 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';\n\n@Component({\n  tag: 'bds-image',\n  styleUrl: 'image.scss',\n  shadow: true,\n})\nexport class Image {\n  private imageHasLoading: boolean = false;\n\n  @Element() element: HTMLElement;\n  /**\n   * URL of the main image.\n   */\n  @Prop({ reflect: true, mutable: true }) src?: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Width of the image.\n   */\n  @Prop() width?: string;\n\n  /**\n   * Height of the image.\n   */\n  @Prop() height?: string;\n\n  /**\n   * Specifies the object-fit style for the image. Can be: 'fill', 'contain', 'cover', 'none', 'scale-down'.\n   */\n  @Prop() objectFit?: ObjectFitValue = 'cover';\n\n  /**\n   * Brightness of the image.\n   */\n  @Prop() brightness?: number;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Indicates whether the main image has been successfully loaded.\n   */\n  @State() imageLoaded = false;\n\n  /**\n   * Indicates whether there was an error during image loading.\n   */\n  @State() loadError = false;\n\n  /**\n   * The current source URL of the image to be rendered.\n   */\n  @State() currentSrc: string;\n\n  componentDidLoad() {\n    this.element.style.width = this.width ? this.width : 'auto';\n    this.element.style.height = this.height?.length > 0 ? this.height : 'auto';\n  }\n\n  @Method()\n  async loadImage(): Promise<void> {\n    if (this.src) {\n      this.imageHasLoading = true;\n      try {\n        const response = await fetch(this.src);\n        if (response.ok) {\n          const blob = await response.blob();\n          const objectURL = URL.createObjectURL(blob);\n          this.currentSrc = objectURL;\n          this.imageLoaded = true;\n          this.imageHasLoading = false;\n        } else {\n          this.loadError = true;\n        }\n      } catch {\n        this.imageHasLoading = false;\n        this.loadError = true;\n      }\n    }\n  }\n\n  render(): JSX.Element {\n    if (!this.imageLoaded && !this.loadError) {\n      // Se a imagem ainda não foi carregada, chame o método loadImage\n      this.loadImage();\n    }\n    return (\n      <Host class={{ empty_img: !this.imageLoaded }}>\n        {this.imageLoaded ? (\n          <img\n            src={this.currentSrc}\n            alt={this.alt}\n            style={{\n              objectFit: this.objectFit,\n              width: '100%',\n              height: '100%',\n              filter: `brightness(${this.brightness})`,\n            }}\n            data-test={this.dataTest}\n            draggable={false}\n          />\n        ) : this.imageHasLoading ? (\n          <bds-skeleton shape=\"square\" width=\"100%\" height=\"100%\"></bds-skeleton>\n        ) : (\n          <bds-illustration\n            class=\"img-feedback\"\n            type=\"empty-states\"\n            name={this.loadError ? 'broken-image' : 'image-not-found'}\n            alt={this.alt}\n            data-test={this.dataTest}\n          ></bds-illustration>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "yDAAA,MAAMA,EAAW,0P,MCSJC,EAAK,MALlB,WAAAC,CAAAC,G,UAMUC,KAAeC,gBAAY,MA0B3BD,KAASE,UAAoB,QAU7BF,KAAQG,SAAY,KAKnBH,KAAWI,YAAG,MAKdJ,KAASK,UAAG,KAoEtB,CA7DC,gBAAAC,G,MACEN,KAAKO,QAAQC,MAAMC,MAAQT,KAA<PERSON>,MAAQT,KAAKS,MAAQ,OACrDT,KAAKO,QAAQC,MAAME,SAASC,EAAAX,KAAKU,UAAM,MAAAC,SAAA,SAAAA,EAAEC,QAAS,EAAIZ,KAAKU,OAAS,M,CAItE,eAAMG,GACJ,GAAIb,KAAKc,IAAK,CACZd,KAAKC,gBAAkB,KACvB,IACE,MAAMc,QAAiBC,MAAMhB,KAAKc,KAClC,GAAIC,EAASE,GAAI,CACf,MAAMC,QAAaH,EAASG,OAC5B,MAAMC,EAAYC,IAAIC,gBAAgBH,GACtClB,KAAKsB,WAAaH,EAClBnB,KAAKI,YAAc,KACnBJ,KAAKC,gBAAkB,K,KAClB,CACLD,KAAKK,UAAY,I,EAEnB,MAAAM,GACAX,KAAKC,gBAAkB,MACvBD,KAAKK,UAAY,I,GAKvB,MAAAkB,GACE,IAAKvB,KAAKI,cAAgBJ,KAAKK,UAAW,CAExCL,KAAKa,W,CAEP,OACEW,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAO,CAAEC,WAAY5B,KAAKI,cAC7BJ,KAAKI,YACJoB,EAAA,OACEV,IAAKd,KAAKsB,WACVO,IAAK7B,KAAK6B,IACVrB,MAAO,CACLN,UAAWF,KAAKE,UAChBO,MAAO,OACPC,OAAQ,OACRoB,OAAQ,cAAc9B,KAAK+B,eAC5B,YACU/B,KAAKG,SAChB6B,UAAW,QAEXhC,KAAKC,gBACPuB,EAAc,gBAAAS,MAAM,SAASxB,MAAM,OAAOC,OAAO,SAEjDc,EAAA,oBACEG,MAAM,eACNO,KAAK,eACLC,KAAMnC,KAAKK,UAAY,eAAiB,kBACxCwB,IAAK7B,KAAK6B,IACC,YAAA7B,KAAKG,W", "ignoreList": []}