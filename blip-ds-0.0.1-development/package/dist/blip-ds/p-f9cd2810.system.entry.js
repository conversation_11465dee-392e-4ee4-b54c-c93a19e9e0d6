System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,r,n,o;return{setters:[function(t){e=t.r;i=t.c;r=t.h;n=t.H;o=t.a}],execute:function(){var a=':host{display:block;width:100%}.tab_group{width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative}.tab_group__header{padding:4px 16px;overflow:hidden}.tab_group__header__itens{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:-webkit-max-content;width:-moz-max-content;width:max-content;gap:32px;margin:auto}.tab_group__header__itens__center{-ms-flex-pack:center;justify-content:center;margin:auto}.tab_group__header__itens__right{-ms-flex-pack:right;justify-content:right;margin:0 0 0 auto}.tab_group__header__itens__left{-ms-flex-pack:left;justify-content:left;margin:0 auto 0 0}.tab_group__header__itens__item{cursor:pointer;height:46px;gap:4px;width:auto;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;border-bottom:2px solid transparent;position:relative}.tab_group__header__itens__item__typo{color:var(--color-content-disable, rgb(89, 89, 89))}.tab_group__header__itens__item__typo__disable{color:var(--color-content-ghost, rgb(140, 140, 140))}.tab_group__header__itens__item__typo__error{color:var(--color-surface-negative, rgb(138, 0, 0))}.tab_group__header__itens__item::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.tab_group__header__itens__item:focus-visible{outline:none}.tab_group__header__itens__item:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.tab_group__header__itens__item__open{color:var(--color-content-default, rgb(40, 40, 40));border-color:var(--color-primary, rgb(30, 107, 241))}.tab_group__header__itens__item__disable{cursor:no-drop}.tab_group__slide{position:relative;overflow:hidden;padding:0 16px;height:54px;margin-left:56px;margin-right:56px}.tab_group__slide-button{position:absolute;z-index:1;background-color:var(--color-surface-1, rgb(246, 246, 246))}.tab_group__slide-button[icon=arrow-left]{left:0}.tab_group__slide-button[icon=arrow-right]{right:0}.tab_group__slide__itens{position:absolute;left:56px;width:-webkit-max-content;width:-moz-max-content;width:max-content;height:48px;display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:center;justify-content:center;padding:4px;gap:32px;-webkit-transition:left 0.5s;-moz-transition:left 0.5s;transition:left 0.5s}.tab_group__content{height:100%}.tab_group__scrolled{-ms-flex-negative:999;flex-shrink:999;overflow:none}.tab_group__scrolled::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.tab_group__scrolled::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}';var s=t("bds_tab_group",function(){function t(t){var n=this;e(this,t);this.bdsTabChange=i(this,"bdsTabChange");this.bdsTabDisabled=i(this,"bdsTabDisabled");this.tabItensElement=null;this.tabItensSlideElement=null;this.isSlideTabs=false;this.alignTab="left";this.tabRefSlide=0;this.positionLeft=0;this.contentScrollable=true;this.align="center";this.dtButtonPrev=null;this.dtButtonNext=null;this.getEventsDisable=function(t){t.forEach((function(t){t.addEventListener("tabDisabled",(function(){n.setInternalItens(Array.from(n.tabItensElement))}),false)}))};this.checkSlideTabs=function(){var t,e;if(n.headerElement||n.headerSlideElement){if(((t=n.headerSlideElement)===null||t===void 0?void 0:t.offsetWidth)>((e=n.headerElement)===null||e===void 0?void 0:e.offsetWidth)){return true}}};this.setFirstActive=function(){var t=Array.from(n.tabItensElement).filter((function(t){return t.open}));if(!t.length){n.tabItensElement[0].open=true}};this.setnumberElement=function(){for(var t=0;t<n.tabItensElement.length;t++){n.tabItensElement[t].numberElement=t}};this.setInternalItens=function(t){var e=t.map((function(t,e){return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({label:t.label,open:t.open,numberElement:e,badge:t.badge},t.disable!==undefined&&{disable:t.disable}),t.error!==undefined&&{error:t.error}),t.headerStyle!==undefined&&{headerStyle:t.headerStyle}),t.contentStyle!==undefined&&{contentStyle:t.contentStyle}),t.icon!==undefined&&{icon:t.icon}),t.iconPosition!==undefined&&{iconPosition:t.iconPosition}),t.iconTheme!==undefined&&{iconTheme:t.iconTheme}),t.badgeShape!==undefined&&{badgeShape:t.badgeShape}),t.badgeColor!==undefined&&{badgeColor:t.badgeColor}),t.badgeIcon!==undefined&&{badgeIcon:t.badgeIcon}),t.badgeAnimation!==undefined&&{badgeAnimation:t.badgeAnimation}),t.badgeNumber!==undefined&&{badgeNumber:t.badgeNumber}),t.badgePosition!==undefined&&{badgePosition:t.badgePosition}),t.dataTest!==undefined&&{dataTest:t.dataTest})}));return n.internalItens=e};this.handleClick=function(t){var e=n.internalItens.map((function(t){return{label:t.label,open:false,numberElement:t.numberElement}}));n.internalItens=e;for(var i=0;i<n.tabItensElement.length;i++){if(n.tabItensElement[i].numberElement!=t){n.tabItensElement[i].open=false}else{n.tabItensElement[i].open=true;n.bdsTabChange.emit(n.tabItensElement[i])}}};this.refHeaderElement=function(t){n.headerElement=t};this.refHeaderSlideElement=function(t){n.headerSlideElement=t};this.handleDisabled=function(t){n.bdsTabDisabled.emit(n.tabItensElement[t])};this.nextSlide=function(){var t,e,i,r,o;var a=((t=n.headerElement)===null||t===void 0?void 0:t.offsetWidth)-((e=n.headerSlideElement)===null||e===void 0?void 0:e.offsetWidth);var s=((i=n.headerSlideElement)===null||i===void 0?void 0:i.offsetWidth)/((r=n.headerElement)===null||r===void 0?void 0:r.offsetWidth);var _=parseInt(s.toString());var d=n.positionLeft-((o=n.headerElement)===null||o===void 0?void 0:o.offsetWidth);n.positionLeft=d<a?a:d;n.alignTab=d<a?"right":"scrolling";n.tabRefSlide=_<=n.tabRefSlide?n.tabRefSlide+1:_};this.prevSlide=function(){var t,e,i;var r=((t=n.headerSlideElement)===null||t===void 0?void 0:t.offsetWidth)/((e=n.headerElement)===null||e===void 0?void 0:e.offsetWidth);var o=parseInt(r.toString());var a=n.positionLeft+((i=n.headerElement)===null||i===void 0?void 0:i.offsetWidth);n.positionLeft=a>0?0:a;n.alignTab=a>0?"left":"scrolling";n.tabRefSlide=o<=n.tabRefSlide?n.tabRefSlide-1:o};this.renderIcon=function(t,e,i,n){return r("bds-icon",{class:{tab_group__header__itens__item__typo__disable:i,tab_group__header__itens__item__typo__error:n},size:"x-small",name:t,theme:e})};this.renderBadge=function(t,e,i,n,o){return r("bds-grid",{"justify-content":"center"},r("bds-badge",{color:e,icon:i,number:o,shape:t,animation:n}))}}t.prototype.componentWillRender=function(){this.tabItensElement=this.element.getElementsByTagName("bds-tab-item");this.setnumberElement();this.setFirstActive();this.setInternalItens(Array.from(this.tabItensElement));this.getEventsDisable(Array.from(this.tabItensElement))};t.prototype.componentDidLoad=function(){this.tabItensSlideElement=this.element.shadowRoot.querySelectorAll(".tab_group__header__itens__item")};t.prototype.connectedCallback=function(){var t=this;this.isSlide=window.setInterval((function(){t.isSlideTabs=t.checkSlideTabs()}),100)};t.prototype.disconnectedCallback=function(){window.clearInterval(this.isSlide)};t.prototype.handleKeyDown=function(t,e){if(t.key=="Enter"){e.disable?this.handleDisabled(e.numberElement):this.handleClick(e.numberElement)}if(t.key=="ArrowRight"){this.tabItensSlideElement[e.numberElement+1].focus()}if(t.key=="ArrowLeft"){this.tabItensSlideElement[e.numberElement-1].focus()}};t.prototype.parseInlineStyle=function(t){if(!t)return{};return t.split(";").filter((function(t){return t.trim()})).reduce((function(t,e){var i=e.split(":").map((function(t){return t.trim()})),r=i[0],n=i[1];if(r&&n){var o=r.replace(/-([a-z])/g,(function(t){return t[1].toUpperCase()}));t[o]=n}return t}),{})};t.prototype.render=function(){var t;var e=this;var i;var o={left:"".concat(this.positionLeft,"px")};var a=(i=this.internalItens)===null||i===void 0?void 0:i.find((function(t){return t.open}));var s=(a===null||a===void 0?void 0:a.headerStyle)?this.parseInlineStyle(a.headerStyle):{};var _=(a===null||a===void 0?void 0:a.contentStyle)?this.parseInlineStyle(a.contentStyle):{};return r(n,{key:"413f1ecafde31333a22d2ba54a35194170453230"},r("div",{key:"d2a8ebe711dc8ee0e2b5a8f65044689089ecfafb",class:{tab_group:true}},this.isSlideTabs&&this.alignTab!="left"&&r("bds-button-icon",{key:"5ef00fd2515df1d10565d7c5029472a762a73fc3",class:"tab_group__slide-button",icon:"arrow-left",size:"short",id:"bds-tabs-button-left",onClick:function(){return e.prevSlide()},dataTest:this.dtButtonPrev,variant:"secondary"}),r("div",{key:"01d649f752b869ed63f5132eb832622b7b5bea8c",class:{tab_group__header:true,tab_group__slide:this.isSlideTabs},ref:this.refHeaderElement,style:s},r("div",{key:"8afbfb17c968ffd80a9be0a376e6319e0f774e08",class:(t={tab_group__header__itens:true,tab_group__slide__itens:this.isSlideTabs},t["tab_group__header__itens__".concat(this.align)]=!this.isSlideTabs,t),ref:this.refHeaderSlideElement,style:o},this.internalItens&&this.internalItens.map((function(t,i){var n=t.open==true?"bold":"regular";return r("div",{class:{tab_group__header__itens__item:true,tab_group__header__itens__item__open:t.open,tab_group__header__itens__item__disable:t.disable},key:i,tabindex:"0",onClick:function(){return t.disable?e.handleDisabled(t.numberElement):e.handleClick(t.numberElement)},onKeyDown:function(i){return e.handleKeyDown(i,t)}},t.iconPosition==="left"&&t.icon?e.renderIcon(t.icon,t.iconTheme,t.disable,t.error):"",t.badgePosition==="left"&&t.badge?e.renderBadge(t.badgeShape,t.badgeColor,t.badgeIcon,t.badgeAnimation,t.badgeNumber):"",r("bds-typo",{class:{tab_group__header__itens__item__typo__disable:t.disable,tab_group__header__itens__item__typo__error:t.error},variant:"fs-16",bold:n},t.label),t.iconPosition==="right"&&t.icon?e.renderIcon(t.icon,t.iconTheme,t.disable,t.error):"",t.badgePosition==="right"&&t.badge?e.renderBadge(t.badgeShape,t.badgeColor,t.badgeIcon,t.badgeAnimation,t.badgeNumber):"")})))),this.isSlideTabs&&this.alignTab!="right"&&r("bds-button-icon",{key:"19944db7058ee43b5d3b215e946a5fbb976ec608",class:"tab_group__slide-button",icon:"arrow-right",size:"short",id:"bds-tabs-button-right",onClick:function(){return e.nextSlide()},dataTest:this.dtButtonNext,variant:"secondary"}),r("div",{key:"2810b14cb861ce76027c4b60e42bd50dca8beb0b",class:{tab_group__content:true,tab_group__scrolled:this.contentScrollable},style:_},r("slot",{key:"e42d74ff44c8a0a37aa76195e3a8c3fa8d56870b"}))))};Object.defineProperty(t.prototype,"element",{get:function(){return o(this)},enumerable:false,configurable:true});return t}());s.style=a}}}));
//# sourceMappingURL=p-f9cd2810.system.entry.js.map