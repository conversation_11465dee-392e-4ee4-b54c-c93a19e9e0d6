{"version": 3, "names": ["navTreeCss", "NavTreeItem", "exports", "class_1", "hostRef", "_this", "this", "navTreeParent", "navTreeChild", "itensElement", "collapse", "icon", "secondaryText", "isOpen", "loading", "disable", "dataTest", "handler", "i", "length", "element", "toggle", "prototype", "isOpenChanged", "value", "bdsToogleChange", "emit", "componentWillLoad", "_a", "parentElement", "tagName", "_b", "querySelector", "componentWillRender", "querySelectorAll", "handleKeyDown", "event", "key", "render", "h", "Host", "tabindex", "onKeyDown", "bind", "class", "_c", "nav_tree_item", "nav_tree_item_active", "nav_tree_item_button", "nav_tree_item_button_active", "onClick", "text", "concat", "size", "_d", "name", "color", "theme", "_e", "variant", "tag", "bold", "_f", "margin", "_g", "accordion", "accordion_open"], "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree-item&encapsulation=shadow", "src/components/nav-tree/nav-tree-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, h, Element, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree-item',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTreeItem {\n  private navTreeParent?: HTMLBdsNavTreeElement | HTMLBdsNavTreeItemElement = null;\n  private navTreeChild?: HTMLBdsNavTreeItemElement = null;\n  private itensElement?: NodeListOf<HTMLBdsNavTreeItemElement> = null;\n\n  @Element() private element: HTMLElement;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * When de activation of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    // if (this.navTreeChild) this.navTreeChild.isOpen = value;\n  }\n\n  componentWillLoad() {\n    this.navTreeParent =\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE' && (this.element.parentElement as HTMLBdsNavTreeElement)) ||\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE-ITEM' && (this.element.parentElement as HTMLBdsNavTreeItemElement)) ||\n      null;\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item');\n  }\n  componentWillRender() {\n    if (this.navTreeParent) {\n      this.itensElement = this.navTreeParent.querySelectorAll(\n        'bds-nav-tree-item',\n      ) as NodeListOf<HTMLBdsNavTreeItemElement>;\n    }\n  }\n\n  private handler = () => {\n    if (!this.loading && !this.disable) {\n      if (this.navTreeParent && this.navTreeParent.collapse == 'single' && this.itensElement) {\n        for (let i = 0; i < this.itensElement.length; i++) {\n          if (this.itensElement[i] != this.element) this.itensElement[i].isOpen = false;\n        }\n      }\n      this.toggle();\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.handler();\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              nav_tree_item: true,\n              nav_tree_item_active: this.isOpen,\n              nav_tree_item_button: !this.navTreeChild,\n              nav_tree_item_button_active: !this.navTreeChild && this.isOpen,\n              [`nav_tree_item--loading`]: this.loading,\n              [`nav_tree_item--disable`]: this.disable,\n            }}\n            onClick={() => this.handler()}\n            data-test={this.dataTest}\n            aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n          >\n            {this.loading ? (\n              <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n            ) : this.icon ? (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.isOpen,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme=\"outline\"\n              ></bds-icon>\n            ) : (\n              ''\n            )}\n            <div class=\"nav_tree_item_content\">\n              {this.text && (\n                <bds-typo\n                  class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                  variant=\"fs-14\"\n                  tag=\"span\"\n                  line-height=\"small\"\n                  bold={this.isOpen ? 'bold' : 'semi-bold'}\n                >\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo\n                  class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                  variant=\"fs-12\"\n                  line-height=\"small\"\n                  tag=\"span\"\n                  margin={false}\n                >\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n            <div class=\"nav_tree_item_slot\">\n              <slot name=\"header-content\"></slot>\n            </div>\n            {this.navTreeChild && (\n              <bds-icon\n                class={{\n                  [`nav_main_arrow`]: true,\n                  [`nav_main_arrow_active`]: this.isOpen,\n                  [`nav_main_arrow--loading`]: this.loading,\n                }}\n                name=\"arrow-down\"\n              ></bds-icon>\n            )}\n          </div>\n        </div>\n        {this.navTreeChild && (\n          <div\n            class={{\n              accordion: true,\n              accordion_open: this.isOpen,\n            }}\n          >\n            <div class=\"container\">\n              <slot></slot>\n            </div>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "gnDAAA,IAAMA,EAAa,i4K,ICSNC,EAAWC,EAAA,+BALxB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,yDAMUA,KAAaC,cAAuD,KACpED,KAAYE,aAA+B,KAC3CF,KAAYG,aAA2C,KAMvDH,KAAQI,SAAe,SAIvBJ,KAAIK,KAAY,KAQhBL,KAAaM,cAAY,KAION,KAAMO,OAAa,MAInDP,KAAOQ,QAAa,MAKpBR,KAAOS,QAAa,MAIpBT,KAAQU,SAAY,KAgCpBV,KAAOW,QAAG,WAChB,IAAKZ,EAAKS,UAAYT,EAAKU,QAAS,CAClC,GAAIV,EAAKE,eAAiBF,EAAKE,cAAcG,UAAY,UAAYL,EAAKI,aAAc,CACtF,IAAK,IAAIS,EAAI,EAAGA,EAAIb,EAAKI,aAAaU,OAAQD,IAAK,CACjD,GAAIb,EAAKI,aAAaS,IAAMb,EAAKe,QAASf,EAAKI,aAAaS,GAAGL,OAAS,K,EAG5ER,EAAKgB,Q,CAET,CA+FD,CAjIOlB,EAAAmB,UAAAD,OAAN,W,qFACEf,KAAKO,QAAUP,KAAKO,O,iBAIZV,EAAAmB,UAAAC,cAAA,SAAcC,GACtBlB,KAAKmB,gBAAgBC,KAAK,CAAEF,MAAOA,EAAOJ,QAASd,KAAKc,S,EAI1DjB,EAAAmB,UAAAK,kBAAA,W,QACErB,KAAKC,gBACFqB,EAAAtB,KAAKc,QAAQS,iBAAa,MAAAD,SAAA,SAAAA,EAAEE,UAAW,gBAAmBxB,KAAKc,QAAQS,iBACvEE,EAAAzB,KAAKc,QAAQS,iBAAa,MAAAE,SAAA,SAAAA,EAAED,UAAW,qBAAwBxB,KAAKc,QAAQS,eAC7E,KACFvB,KAAKE,aAAeF,KAAKc,QAAQY,cAAc,oB,EAEjD7B,EAAAmB,UAAAW,oBAAA,WACE,GAAI3B,KAAKC,cAAe,CACtBD,KAAKG,aAAeH,KAAKC,cAAc2B,iBACrC,oB,GAgBE/B,EAAAmB,UAAAa,cAAA,SAAcC,GACpB,GAAIA,EAAMC,KAAO,QAAS,CACxB/B,KAAKW,S,GAITd,EAAAmB,UAAAgB,OAAA,W,cAAA,IAAAjC,EAAAC,KACE,OACEiC,EAACC,EAAI,CAAAH,IAAA,4CACHE,EAAA,OAAAF,IAAA,2CAAKI,SAAS,IAAIC,UAAWpC,KAAK6B,cAAcQ,KAAKrC,MAAOsC,MAAM,SAChEL,EAAA,OAAAF,IAAA,2CACEO,OAAKC,EAAA,CACHC,cAAe,KACfC,qBAAsBzC,KAAKO,OAC3BmC,sBAAuB1C,KAAKE,aAC5ByC,6BAA8B3C,KAAKE,cAAgBF,KAAKO,QACxDgC,EAAC,0BAA2BvC,KAAKQ,QACjC+B,EAAC,0BAA2BvC,KAAKS,Q,GAEnCmC,QAAS,WAAM,OAAA7C,EAAKY,SAAL,EAAc,YAClBX,KAAKU,SAAQ,aACZV,KAAK6C,MAAQ7C,KAAKM,eAAiB,KAAAwC,OAAK9C,KAAKM,iBAExDN,KAAKQ,QACJyB,EAAqB,uBAAAc,KAAK,gBACxB/C,KAAKK,KACP4B,EACE,YAAAK,OAAKU,EAAA,GACHA,EAAC,aAAc,KACfA,EAAC,oBAAqBhD,KAAKO,O,GAE7BwC,KAAK,SACLE,KAAMjD,KAAKK,KACX6C,MAAM,UACNC,MAAM,YACI,GAIdlB,EAAK,OAAAF,IAAA,2CAAAO,MAAM,yBACRtC,KAAK6C,MACJZ,EAAA,YAAAF,IAAA,2CACEO,OAAKc,EAAA,GAAIA,EAAC,cAAe,KAAMA,EAAC,uBAAwBpD,KAAKQ,QAAO4C,GACpEC,QAAQ,QACRC,IAAI,OACQ,sBACZC,KAAMvD,KAAKO,OAAS,OAAS,aAE5BP,KAAK6C,MAGT7C,KAAKM,eACJ2B,EACE,YAAAF,IAAA,2CAAAO,OAAKkB,EAAA,GAAIA,EAAC,iBAAkB,KAAMA,EAAC,0BAA2BxD,KAAKQ,QAAOgD,GAC1EH,QAAQ,QAAO,cACH,QACZC,IAAI,OACJG,OAAQ,OAEPzD,KAAKM,gBAIZ2B,EAAK,OAAAF,IAAA,2CAAAO,MAAM,sBACTL,EAAA,QAAAF,IAAA,2CAAMkB,KAAK,oBAEZjD,KAAKE,cACJ+B,EAAA,YAAAF,IAAA,2CACEO,OAAKoB,EAAA,GACHA,EAAC,kBAAmB,KACpBA,EAAC,yBAA0B1D,KAAKO,OAChCmD,EAAC,2BAA4B1D,KAAKQ,Q,GAEpCyC,KAAK,iBAKZjD,KAAKE,cACJ+B,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACLqB,UAAW,KACXC,eAAgB5D,KAAKO,SAGvB0B,EAAK,OAAAF,IAAA,2CAAAO,MAAM,aACTL,EAAA,QAAAF,IAAA,+C,sPAvKU,I", "ignoreList": []}