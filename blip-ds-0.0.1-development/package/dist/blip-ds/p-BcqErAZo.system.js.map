{"version": 3, "file": "p-BcqErAZo.system.js", "sources": ["src/components/table/table-header-cell/table-header-cell.scss?tag=bds-table-th&encapsulation=scoped", "src/components/table/table-header-cell/table-header-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0px 8px;\n}\n.th_cell {\n  display: flex;\n  align-items: center;\n  height: 64px;\n  gap: 8px;\n  font-family: $font-family;\n  box-sizing: border-box;\n\n  &--sortable-true:hover, &--sortable-false:hover  {\n    cursor: pointer;\n  }\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n.dense-th {\n  min-height: 48px;\n  height: auto;\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n@Component({\n  tag: 'bds-table-th',\n  styleUrl: 'table-header-cell.scss',\n  scoped: true,\n})\nexport class TableHeaderCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() sortable = false;\n  @Prop() arrow = '';\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div\n          class={{\n            th_cell: true,\n            [`th_cell--sortable-${this.sortable}`]: true,\n            'dense-th': this.isDense,\n            [`justify--${this.justifyContent}`]:true\n          }}\n        >\n          <bds-typo bold={this.sortable ? 'bold' : 'semi-bold'} variant=\"fs-14\">\n            <slot />\n          </bds-typo>\n          {this.sortable ? (\n            <bds-icon\n              size=\"small\"\n              name={this.arrow === 'asc' ? 'arrow-down' : this.arrow === 'dsc' ? 'arrow-up' : ''}\n            ></bds-icon>\n          ) : ''\n            // <div style={{ width: '20px' }}></div>\n          }\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,kBAAkB,GAAG,wyBAAwyB;;YCQtzB,eAAe,2BAAA,MAAA;MAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;MAOW,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;MAChB,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAChB,QAAA,IAAK,CAAA,KAAA,GAAG,EAAE;MACV,QAAA,IAAc,CAAA,cAAA,GAAmB,MAAM;MAkChD;UAhCC,iBAAiB,GAAA;cACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;MAClD,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;MACjG,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;;UAGvB,MAAM,GAAA;cACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,OAAO,EAAE,IAAI;MACb,gBAAA,CAAC,qBAAqB,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;sBAC5C,UAAU,EAAE,IAAI,CAAC,OAAO;MACxB,gBAAA,CAAC,YAAY,IAAI,CAAC,cAAc,CAAE,CAAA,GAAE;MACrC,aAAA,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,WAAW,EAAE,OAAO,EAAC,OAAO,EAAA,EACnE,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACC,EACV,IAAI,CAAC,QAAQ,IACZ,gBACE,IAAI,EAAC,OAAO,EACZ,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,YAAY,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,UAAU,GAAG,EAAE,EAAA,CACxE,IACV;;MAGA,SAAA,CACD;;;;;;;;;;;;"}