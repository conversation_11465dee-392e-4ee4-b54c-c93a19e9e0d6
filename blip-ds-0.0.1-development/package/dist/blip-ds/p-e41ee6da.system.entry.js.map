{"version": 3, "names": ["ptTerms", "uploaded", "dropHere", "dropOrClick", "formatError", "esTerms", "enTerms", "termTranslate", "lang", "string", "translate", "map", "term", "patternSvg", "bdsUploadCss", "BdsUpload", "exports", "class_1", "hostRef", "_this", "this", "files", "haveFiles", "hover", "size", "internalAccepts", "language", "dataAccept", "dtInputFiles", "dtLabelAddFile", "dtButtonDelete", "validationFiles", "File", "index", "filetype", "concat", "name", "split", "pop", "validate", "includes", "deleteFile", "handleDrop", "Event", "dt", "dataTransfer", "handleFiles", "multiple", "__spread<PERSON><PERSON>y", "bdsUploadChange", "emit", "value", "refInputElement", "el", "inputElement", "prototype", "dataAcceptChanged", "JSON", "parse", "_a", "filesChanged", "length", "i", "formatErrorChanged", "error", "setTimeout", "componentWillLoad", "componentDidLoad", "for<PERSON>ach", "eventName", "dropArea", "shadowRoot", "addEventListener", "preventDefaults", "hoverFile", "e", "preventDefault", "stopPropagation", "boolean", "onUploadClick", "getSize", "listSize", "push", "fileToDelete", "filter", "item", "bdsUploadDelete", "splice", "deleteAllFiles", "handleKeyDown", "event", "key", "click", "render", "h", "class", "variant", "bold", "<PERSON><PERSON><PERSON>", "subtitle", "context", "names", "id", "icon", "onClick", "italic", "upload__edit", "htmlFor", "tabindex", "onKeyDown", "bind", "src", "background", "ref", "type", "accept", "toString", "onChange", "$event", "target"], "sources": ["src/components/upload/languages/pt_BR.tsx", "src/components/upload/languages/es_ES.tsx", "src/components/upload/languages/en_US.tsx", "src/components/upload/languages/index.ts", "src/assets/svg/pattern.svg", "src/components/upload/bds-upload.scss?tag=bds-upload&encapsulation=shadow", "src/components/upload/bds-upload.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    uploaded: 'Arquivos enviados',\n    dropHere: 'Solte aqui para anexar o arquivo',\n    dropOrClick: 'Arraste e solte seus arquivos aqui ou clique para fazer upload do arquivo',\n    formatError: 'Ocorreu um erro ao anexar o arquivo, tente novamente ou selecione outro arquivo',\n  },\n];\n", "export const esTerms = [\n  {\n    uploaded: 'Archivos subidos',\n    dropHere: 'Soltar aquí para adjuntar archivo',\n    dropOrClick: 'Arrastre y suelte sus archivos aquí o haga clic para cargar el archivo',\n    formatError: 'Se produjo un error al adjuntar el archivo, inténtelo nuevamente o seleccione otro archivo',\n  },\n];\n", "export const enTerms = [\n  {\n    uploaded: 'Files uploaded',\n    dropHere: 'Drop here to attach file',\n    dropOrClick: 'Drag and drop your files here or click to upload file',\n    formatError: 'There was an error attaching the file, please try again or select another file',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "<svg width=\"384\" height=\"80\" viewBox=\"0 0 384 80\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_3788_215640)\">\n<line x1=\"-10.9767\" y1=\"74.3843\" x2=\"22.7778\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"22.7777\" y1=\"74.3843\" x2=\"56.5322\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"56.532\" y1=\"74.3843\" x2=\"90.2866\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"90.2869\" y1=\"74.3843\" x2=\"124.042\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"124.041\" y1=\"74.3843\" x2=\"157.796\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"157.796\" y1=\"74.3843\" x2=\"191.551\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"191.551\" y1=\"74.3843\" x2=\"225.305\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"225.305\" y1=\"74.3843\" x2=\"259.06\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"259.06\" y1=\"74.3843\" x2=\"292.814\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"292.814\" y1=\"74.3843\" x2=\"326.569\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"326.569\" y1=\"74.3843\" x2=\"360.323\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"360.324\" y1=\"74.3843\" x2=\"394.078\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_3788_215640\">\n<rect width=\"384\" height=\"80\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n", "@use '../../globals/helpers' as *;\n\n.upload {\n  min-width: 400px;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n\n  .upload-header {\n    display: flex;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    align-items: center;\n    gap: 8px;\n    color: $color-content-default;\n\n    &_text {\n      color: $color-content-default;\n      display: flex;\n      flex-direction: column;\n    }\n  }\n}\n\n.upload__edit--label {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border: 1px solid $color-border-1;\n  border-radius: 8px;\n  cursor: pointer;\n  font-weight: normal;\n  box-sizing: border-box;\n  padding: 23px 16px;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &::before {\n      border-color: $color-focus;\n    }\n  }\n\n  .upload__img--visible {\n    display: flex;\n    width: 100%;\n    height: 100%;\n    border-radius: 8px;\n    position: absolute;\n    background-color: $color-surface-2;\n    z-index: 1;\n  }\n\n  .text-box {\n    display: flex;\n    padding: 8px;\n    width: 100%;\n    text-align: center;\n    z-index: 2;\n    .text {\n      color: $color-content-default;\n      width: 100%;\n      flex-wrap: wrap;\n    }\n  }\n  .text-box--hover {\n    background-color: $color-surface-2;\n\n    .text {\n      color: $color-primary;\n    }\n  }\n}\n\n.upload__edit--label:hover {\n  border: 2px solid $color-primary;\n  box-sizing: border-box;\n  padding: 22px 16px;\n  cursor: pointer;\n  text-decoration: underline $color-primary;\n  color: $color-brand;\n\n  .text {\n    color: $color-primary;\n  }\n}\n\n.upload__edit--hover {\n  background-size: cover;\n  border: 1px dashed $color-surface-4;\n  color: $color-primary;\n  font-weight: bold;\n  border-radius: 8px;\n}\n\n.upload__img--invisible {\n  display: none;\n}\n\n.list-preview {\n  border-top: 1px solid $color-border-1;\n  border-bottom: 1px solid $color-border-1;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.upload__preview {\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n\n  padding: 16px 0;\n\n  .preview {\n    display: flex;\n    padding: 0 16px;\n    align-items: center;\n    justify-content: center;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    gap: 8px;\n\n    &-text {\n      font-family: $font-family;\n      font-size: 0.875rem;\n      font-weight: 700;\n      margin: 0;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      overflow: hidden;\n      width: 100%;\n      color: $color-content-default;\n    }\n    &-icon {\n      color: $color-content-default;\n    }\n    &-icon:hover {\n      cursor: pointer;\n    }\n  }\n}\n\n.preview-length {\n  display: flex;\n  justify-content: end;\n  padding-top: 16px;\n  text-align: end;\n}\n\n.upload__edit input {\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0;\n  width: 0;\n  height: 100%;\n}\n", "import { Component, h, Element, State, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\nimport { termTranslate, languages } from './languages';\nimport background from '../../assets/svg/pattern.svg';\n\n@Component({\n  tag: 'bds-upload',\n  styleUrl: 'bds-upload.scss',\n  shadow: true,\n})\nexport class BdsUpload {\n  private inputElement?: HTMLInputElement;\n\n  @Element() private dropArea: HTMLElement;\n  @State() files: File[] = [];\n  @State() haveFiles = false;\n  @State() hover = false;\n  @State() background: string;\n  @State() size: number[] = [];\n  @State() internalAccepts: string[] = [];\n  @State() formatError = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * Used for add a text on title.\n   */\n  @Prop() titleName: string;\n  /**\n   * Used for add a text on subtitle.\n   */\n  @Prop() subtitle: string;\n  /**\n   * Used for add a error message. In case a verify.\n   */\n  @Prop({ reflect: true, mutable: true }) error: string;\n  /**\n   * Used to allow upload multiple files.\n   */\n  @Prop() multiple: boolean;\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() accept: string;\n\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() dataAccept: string[] | string = [];\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtInputFiles is the data-test to button clear.\n   */\n  @Prop() dtInputFiles?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtLabelAddFile is the data-test to button clear.\n   */\n  @Prop() dtLabelAddFile?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonDelete is the data-test to button clear.\n   */\n  @Prop() dtButtonDelete?: string = null;\n  /**\n   * Event emited when delete a item from the list.\n   */\n  @Event() bdsUploadDelete: EventEmitter;\n  /**\n   * Event emited when change the value of Upload.\n   */\n  @Event() bdsUploadChange: EventEmitter;\n\n  @Watch('dataAccept')\n  protected dataAcceptChanged(): void {\n    if (this.dataAccept) {\n      if (typeof this.dataAccept === 'string') {\n        try {\n          this.internalAccepts = JSON.parse(this.dataAccept);\n        } catch {\n          this.internalAccepts = [];\n        }\n      } else {\n        this.internalAccepts = this.dataAccept;\n      }\n    } else {\n      this.internalAccepts = [];\n    }\n  }\n\n  @Watch('files')\n  protected filesChanged(): void {\n    if (this.files.length > 0) {\n      for (let i = 0; i < this.files.length; i++) {\n        if (this.internalAccepts.length > 0) {\n          this.validationFiles(this.files[i], i);\n        }\n      }\n    }\n  }\n\n  @Watch('formatError')\n  protected formatErrorChanged(value): void {\n    if (value) {\n      this.error = termTranslate(this.language, 'formatError');\n      setTimeout(() => (this.error = null), 5000);\n    }\n  }\n\n  componentWillLoad() {\n    this.dataAcceptChanged();\n  }\n\n  componentDidLoad() {\n    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragenter', 'dragover'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(false), false);\n    });\n    this.dropArea.shadowRoot.addEventListener('drop', this.handleDrop, false);\n  }\n\n  validationFiles = (File: File, index: number) => {\n    const filetype = `.${File.name.split('.').pop()}`;\n    const validate = this.internalAccepts.includes(filetype);\n    if (validate) {\n      this.formatError = false;\n      return;\n    } else {\n      this.formatError = true;\n      this.deleteFile(index);\n      return;\n    }\n  };\n\n  /**\n   * Recive the file data using drag and drop.\n   */\n  handleDrop = (Event) => {\n    this.haveFiles = true;\n    const dt = Event.dataTransfer;\n    const files = dt.files;\n    this.handleFiles(files);\n  };\n\n  /**\n   * Verify if allow the state recive one or more items.\n   */\n  handleFiles = (files) => {\n    if (!this.multiple) {\n      this.files = [files[0]];\n    } else {\n      this.files = [...this.files, ...files];\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  };\n  /**\n   * Prevent the screen to reload.\n   */\n  preventDefaults(e) {\n    e.preventDefault();\n    e.stopPropagation();\n  }\n  /**\n   * Definy if are hover to aply styles in drop area.\n   */\n  hoverFile(boolean) {\n    this.hover = boolean;\n  }\n  /**\n   * Recive the file data using click.\n   */\n  public onUploadClick(files) {\n    if (files.length > 0) {\n      if (!this.multiple) {\n        this.files = [files[0]];\n      } else {\n        this.files = [...this.files, ...files];\n      }\n      this.haveFiles = true;\n      this.getSize();\n    } else {\n      return false;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n  /**\n   * Return the size information from the file.\n   */\n  getSize() {\n    this.files.map((size: any) => {\n      const listSize = size.size;\n      this.size.push(listSize);\n    });\n  }\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteFile(index) {\n    const fileToDelete = this.files.filter((item, i) => i == index && item);\n    this.bdsUploadDelete.emit({ value: fileToDelete });\n    this.files.splice(index, 1);\n    this.files = [...this.files];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteAllFiles() {\n    this.bdsUploadDelete.emit({ value: this.files });\n    this.files = [];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  private refInputElement = (el: HTMLInputElement): void => {\n    this.inputElement = el as HTMLInputElement;\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.inputElement.click();\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"upload\">\n        <div class=\"upload-header\">\n          <bds-icon class=\"upload-header_icon\" size=\"xxx-large\" name=\"upload\"></bds-icon>\n          <div class=\"upload-header_text\">\n            <bds-typo variant=\"fs-16\" bold=\"bold\" aria-label={this.titleName}>\n              {this.titleName}\n            </bds-typo>\n            <bds-typo variant=\"fs-14\" bold=\"regular\" aria-label={this.subtitle}>\n              {this.subtitle}\n            </bds-typo>\n          </div>\n        </div>\n        {this.error ? (\n          <bds-banner context=\"inside\" variant=\"error\" aria-label={this.error}>\n            {this.error}\n          </bds-banner>\n        ) : (\n          ''\n        )}\n        {this.haveFiles ? (\n          <div>\n            <div class=\"list-preview\">\n              {this.files.map((names: any, index) => (\n                <div class=\"upload__preview\" key={index} id=\"drop-area\">\n                  <div class=\"preview\" id=\"preview\">\n                    <bds-icon size=\"x-small\" name=\"attach\"></bds-icon>\n                    <p class=\"preview-text\" id=\"preview-text\" aria-label={names.name}>\n                      {names.name}\n                    </p>\n                    <bds-button-icon\n                      class=\"preview-icon\"\n                      size=\"short\"\n                      icon=\"trash\"\n                      variant=\"secondary\"\n                      onClick={() => this.deleteFile(index)}\n                      aria-label={`delete ${names.name}`}\n                      data-test={`${this.dtButtonDelete}-${index}`}\n                    ></bds-button-icon>\n                  </div>\n                </div>\n              ))}\n            </div>\n            {this.multiple ? (\n              <bds-typo\n                variant=\"fs-14\"\n                italic\n                class=\"preview-length\"\n                aria-label={termTranslate(this.language, 'uploaded')}\n              >\n                {this.files.length > 1 ? `${this.files.length} ${termTranslate(this.language, 'uploaded')}` : ''}\n              </bds-typo>\n            ) : (\n              ''\n            )}\n          </div>\n        ) : (\n          ''\n        )}\n        <div class={{ upload__edit: true }}>\n          <label\n            class={{ 'upload__edit--label': true, 'upload__edit--hover': this.hover }}\n            id=\"file-label\"\n            htmlFor=\"file\"\n            data-test={this.dtLabelAddFile}\n            tabindex=\"0\"\n            onKeyDown={this.handleKeyDown.bind(this)}\n          >\n            <div class={{ 'text-box': true, 'text-box--hover': this.hover }} id=\"file-text_box\">\n              {this.hover ? (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropHere')}\n                >\n                  {termTranslate(this.language, 'dropHere')}\n                </bds-typo>\n              ) : (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropOrClick')}\n                >\n                  {termTranslate(this.language, 'dropOrClick')}\n                </bds-typo>\n              )}\n            </div>\n            <img class={{ 'upload__img--invisible': true, 'upload__img--visible': this.hover }} src={background} />\n          </label>\n          <input\n            ref={this.refInputElement}\n            type=\"file\"\n            name=\"files[]\"\n            id=\"file\"\n            class=\"upload__input\"\n            multiple={this.multiple}\n            accept={this.internalAccepts.length > 0 ? this.internalAccepts.toString() : this.accept}\n            onChange={($event: any) => this.onUploadClick($event.target.files)}\n            data-test={this.dtInputFiles}\n          />\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "01DAAO,IAAMA,EAAU,CACrB,CACEC,SAAU,oBACVC,SAAU,mCACVC,YAAa,4EACbC,YAAa,oFCLV,IAAMC,EAAU,CACrB,CACEJ,SAAU,mBACVC,SAAU,oCACVC,YAAa,yEACbC,YAAa,+FCLV,IAAME,EAAU,CACrB,CACEL,SAAU,iBACVC,SAAU,2BACVC,YAAa,wDACbC,YAAa,mFCCV,IAAMG,EAAgB,SAACC,EAAiBC,GAC7C,IAAIC,EACJ,OAAQF,GACN,IAAK,QACHE,EAAYV,EAAQW,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,IAAK,QACHC,EAAYL,EAAQM,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,IAAK,QACHC,EAAYJ,EAAQK,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,QACEC,EAAYV,EAAQW,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAEtC,OAAOC,CACT,ECtBA,IAAMG,EAAa,ikDCAnB,IAAMC,EAAe,+/G,ICSRC,EAASC,EAAA,wBALtB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,wGASWA,KAAKC,MAAW,GAChBD,KAASE,UAAG,MACZF,KAAKG,MAAG,MAERH,KAAII,KAAa,GACjBJ,KAAeK,gBAAa,GAC5BL,KAAWhB,YAAG,MAIfgB,KAAQM,SAAe,QAyBvBN,KAAUO,WAAsB,GAMhCP,KAAYQ,aAAY,KAMxBR,KAAcS,eAAY,KAM1BT,KAAcU,eAAY,KAkElCV,KAAAW,gBAAkB,SAACC,EAAYC,GAC7B,IAAMC,EAAW,IAAAC,OAAIH,EAAKI,KAAKC,MAAM,KAAKC,OAC1C,IAAMC,EAAWpB,EAAKM,gBAAgBe,SAASN,GAC/C,GAAIK,EAAU,CACZpB,EAAKf,YAAc,MACnB,M,KACK,CACLe,EAAKf,YAAc,KACnBe,EAAKsB,WAAWR,GAChB,M,CAEJ,EAKAb,KAAAsB,WAAa,SAACC,GACZxB,EAAKG,UAAY,KACjB,IAAMsB,EAAKD,EAAME,aACjB,IAAMxB,EAAQuB,EAAGvB,MACjBF,EAAK2B,YAAYzB,EACnB,EAKAD,KAAA0B,YAAc,SAACzB,GACb,IAAKF,EAAK4B,SAAU,CAClB5B,EAAKE,MAAQ,CAACA,EAAM,G,KACf,CACLF,EAAKE,MAAK2B,4BAAA,GAAO7B,EAAKE,MAAK,MAAKA,EAAK,K,CAEvCF,EAAK8B,gBAAgBC,KAAK,CAAEC,MAAOhC,EAAKE,OAC1C,EAwEQD,KAAAgC,gBAAkB,SAACC,GACzBlC,EAAKmC,aAAeD,CACtB,CAmHD,CArRWpC,EAAAsC,UAAAC,kBAAA,WACR,GAAIpC,KAAKO,WAAY,CACnB,UAAWP,KAAKO,aAAe,SAAU,CACvC,IACEP,KAAKK,gBAAkBgC,KAAKC,MAAMtC,KAAKO,W,CACvC,MAAAgC,GACAvC,KAAKK,gBAAkB,E,MAEpB,CACLL,KAAKK,gBAAkBL,KAAKO,U,MAEzB,CACLP,KAAKK,gBAAkB,E,GAKjBR,EAAAsC,UAAAK,aAAA,WACR,GAAIxC,KAAKC,MAAMwC,OAAS,EAAG,CACzB,IAAK,IAAIC,EAAI,EAAGA,EAAI1C,KAAKC,MAAMwC,OAAQC,IAAK,CAC1C,GAAI1C,KAAKK,gBAAgBoC,OAAS,EAAG,CACnCzC,KAAKW,gBAAgBX,KAAKC,MAAMyC,GAAIA,E,KAOlC7C,EAAAsC,UAAAQ,mBAAA,SAAmBZ,GAAnB,IAAAhC,EAAAC,KACR,GAAI+B,EAAO,CACT/B,KAAK4C,MAAQzD,EAAca,KAAKM,SAAU,eAC1CuC,YAAW,kBAAO9C,EAAK6C,MAAQ,IAApB,GAA2B,I,GAI1C/C,EAAAsC,UAAAW,kBAAA,WACE9C,KAAKoC,mB,EAGPvC,EAAAsC,UAAAY,iBAAA,eAAAhD,EAAAC,KACE,CAAC,YAAa,WAAY,YAAa,QAAQgD,SAAQ,SAACC,GACtDlD,EAAKmD,SAASC,WAAWC,iBAAiBH,EAAWlD,EAAKsD,gBAAiB,OAC3EtD,EAAKmD,SAASC,WAAWC,iBAAiBH,GAAW,WAAM,OAAAlD,EAAKuD,UAAU,KAAf,GAAsB,MACnF,IACA,CAAC,YAAa,YAAYN,SAAQ,SAACC,GACjClD,EAAKmD,SAASC,WAAWC,iBAAiBH,GAAW,WAAM,OAAAlD,EAAKsD,eAAL,GAAsB,OACjFtD,EAAKmD,SAASC,WAAWC,iBAAiBH,GAAW,WAAM,OAAAlD,EAAKuD,UAAU,KAAf,GAAsB,MACnF,IACA,CAAC,YAAa,QAAQN,SAAQ,SAACC,GAC7BlD,EAAKmD,SAASC,WAAWC,iBAAiBH,GAAW,WAAM,OAAAlD,EAAKsD,eAAL,GAAsB,OACjFtD,EAAKmD,SAASC,WAAWC,iBAAiBH,GAAW,WAAM,OAAAlD,EAAKuD,UAAU,MAAf,GAAuB,MACpF,IACAtD,KAAKkD,SAASC,WAAWC,iBAAiB,OAAQpD,KAAKsB,WAAY,M,EAwCrEzB,EAAAsC,UAAAkB,gBAAA,SAAgBE,GACdA,EAAEC,iBACFD,EAAEE,iB,EAKJ5D,EAAAsC,UAAAmB,UAAA,SAAUI,GACR1D,KAAKG,MAAQuD,C,EAKR7D,EAAAsC,UAAAwB,cAAA,SAAc1D,GACnB,GAAIA,EAAMwC,OAAS,EAAG,CACpB,IAAKzC,KAAK2B,SAAU,CAClB3B,KAAKC,MAAQ,CAACA,EAAM,G,KACf,CACLD,KAAKC,MAAK2B,4BAAA,GAAO5B,KAAKC,MAAK,MAAKA,EAAK,K,CAEvCD,KAAKE,UAAY,KACjBF,KAAK4D,S,KACA,CACL,OAAO,K,CAET5D,KAAK6B,gBAAgBC,KAAK,CAAEC,MAAO/B,KAAKC,O,EAK1CJ,EAAAsC,UAAAyB,QAAA,eAAA7D,EAAAC,KACEA,KAAKC,MAAMV,KAAI,SAACa,GACd,IAAMyD,EAAWzD,EAAKA,KACtBL,EAAKK,KAAK0D,KAAKD,EACjB,G,EAMIhE,EAAAsC,UAAAd,WAAN,SAAiBR,G,2FACTkD,EAAe/D,KAAKC,MAAM+D,QAAO,SAACC,EAAMvB,GAAM,OAAAA,GAAK7B,GAASoD,CAAd,IACpDjE,KAAKkE,gBAAgBpC,KAAK,CAAEC,MAAOgC,IACnC/D,KAAKC,MAAMkE,OAAOtD,EAAO,GACzBb,KAAKC,MAAK2B,cAAA,GAAO5B,KAAKC,MAAK,MAC3B,GAAID,KAAKC,MAAMwC,SAAW,EAAG,CAC3BzC,KAAKE,UAAY,K,KACZ,CACLF,KAAKE,UAAY,I,CAEnBF,KAAK6B,gBAAgBC,KAAK,CAAEC,MAAO/B,KAAKC,Q,iBAOpCJ,EAAAsC,UAAAiC,eAAN,W,qFACEpE,KAAKkE,gBAAgBpC,KAAK,CAAEC,MAAO/B,KAAKC,QACxCD,KAAKC,MAAQ,GACb,GAAID,KAAKC,MAAMwC,SAAW,EAAG,CAC3BzC,KAAKE,UAAY,K,KACZ,CACLF,KAAKE,UAAY,I,CAEnBF,KAAK6B,gBAAgBC,KAAK,CAAEC,MAAO/B,KAAKC,Q,iBAOlCJ,EAAAsC,UAAAkC,cAAA,SAAcC,GACpB,GAAIA,EAAMC,KAAO,QAAS,CACxBvE,KAAKkC,aAAasC,O,GAItB3E,EAAAsC,UAAAsC,OAAA,eAAA1E,EAAAC,KACE,OACE0E,EAAA,OAAAH,IAAA,2CAAKI,MAAM,UACTD,EAAK,OAAAH,IAAA,2CAAAI,MAAM,iBACTD,EAAU,YAAAH,IAAA,2CAAAI,MAAM,qBAAqBvE,KAAK,YAAYY,KAAK,WAC3D0D,EAAK,OAAAH,IAAA,2CAAAI,MAAM,sBACTD,EAAA,YAAAH,IAAA,2CAAUK,QAAQ,QAAQC,KAAK,OAAmB,aAAA7E,KAAK8E,WACpD9E,KAAK8E,WAERJ,EAAA,YAAAH,IAAA,2CAAUK,QAAQ,QAAQC,KAAK,UAAS,aAAa7E,KAAK+E,UACvD/E,KAAK+E,YAIX/E,KAAK4C,MACJ8B,EAAY,cAAAM,QAAQ,SAASJ,QAAQ,QAAoB,aAAA5E,KAAK4C,OAC3D5C,KAAK4C,OACK,GAId5C,KAAKE,UACJwE,EAAA,WACEA,EAAA,OAAKC,MAAM,gBACR3E,KAAKC,MAAMV,KAAI,SAAC0F,EAAYpE,GAAK,OAChC6D,EAAK,OAAAC,MAAM,kBAAkBJ,IAAK1D,EAAOqE,GAAG,aAC1CR,EAAA,OAAKC,MAAM,UAAUO,GAAG,WACtBR,EAAA,YAAUtE,KAAK,UAAUY,KAAK,WAC9B0D,EAAA,KAAGC,MAAM,eAAeO,GAAG,eAA2B,aAAAD,EAAMjE,MACzDiE,EAAMjE,MAET0D,EACE,mBAAAC,MAAM,eACNvE,KAAK,QACL+E,KAAK,QACLP,QAAQ,YACRQ,QAAS,WAAM,OAAArF,EAAKsB,WAAWR,EAAhB,EACH,uBAAAE,OAAUkE,EAAMjE,MACjB,eAAAD,OAAGhB,EAAKW,eAAc,KAAAK,OAAIF,MAdX,KAoBnCb,KAAK2B,SACJ+C,EAAA,YACEE,QAAQ,QACRS,OAAM,KACNV,MAAM,iBACM,aAAAxF,EAAca,KAAKM,SAAU,aAExCN,KAAKC,MAAMwC,OAAS,EAAI,GAAA1B,OAAGf,KAAKC,MAAMwC,OAAM,KAAA1B,OAAI5B,EAAca,KAAKM,SAAU,aAAgB,IACrF,IAIT,GAIRoE,EAAA,OAAAH,IAAA,2CAAKI,MAAO,CAAEW,aAAc,OAC1BZ,EAAA,SAAAH,IAAA,2CACEI,MAAO,CAAE,sBAAuB,KAAM,sBAAuB3E,KAAKG,OAClE+E,GAAG,aACHK,QAAQ,OAAM,YACHvF,KAAKS,eAChB+E,SAAS,IACTC,UAAWzF,KAAKqE,cAAcqB,KAAK1F,OAEnC0E,EAAK,OAAAH,IAAA,2CAAAI,MAAO,CAAE,WAAY,KAAM,kBAAmB3E,KAAKG,OAAS+E,GAAG,iBACjElF,KAAKG,MACJuE,EAAA,YACEC,MAAM,OACNC,QAAQ,QACRC,KAAK,UAAS,aACF1F,EAAca,KAAKM,SAAU,aAExCnB,EAAca,KAAKM,SAAU,aAGhCoE,EAAA,YACEC,MAAM,OACNC,QAAQ,QACRC,KAAK,UAAS,aACF1F,EAAca,KAAKM,SAAU,gBAExCnB,EAAca,KAAKM,SAAU,iBAIpCoE,EAAA,OAAAH,IAAA,2CAAKI,MAAO,CAAE,yBAA0B,KAAM,uBAAwB3E,KAAKG,OAASwF,IAAKC,KAE3FlB,EAAA,SAAAH,IAAA,2CACEsB,IAAK7F,KAAKgC,gBACV8D,KAAK,OACL9E,KAAK,UACLkE,GAAG,OACHP,MAAM,gBACNhD,SAAU3B,KAAK2B,SACfoE,OAAQ/F,KAAKK,gBAAgBoC,OAAS,EAAIzC,KAAKK,gBAAgB2F,WAAahG,KAAK+F,OACjFE,SAAU,SAACC,GAAgB,OAAAnG,EAAK4D,cAAcuC,EAAOC,OAAOlG,MAAjC,EAAuC,YACvDD,KAAKQ,gB,yTAnVN,I", "ignoreList": []}