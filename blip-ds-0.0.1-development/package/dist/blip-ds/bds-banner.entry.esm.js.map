{"version": 3, "file": "bds-banner.entry.esm.js", "sources": ["src/components/banner/banner.scss?tag=bds-banner&encapsulation=shadow", "src/components/banner/banner.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: fit-content;\n\n  font-family: $font-family;\n  font-size: $fs-14;\n  font-weight: $font-weight-bold;\n\n  .banner {\n    &__holder {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      width: 100%;\n      padding: 8px 16px;\n      min-height: 40px;\n      color: $color-content-default;\n\n      &--context--inside {\n        border-radius: 8px;\n      }\n\n      &--align--left {\n        justify-content: flex-start;\n      }\n\n      &--align--right {\n        justify-content: flex-start;\n      }\n\n      &--warning {\n        background-color: $color-warning;\n      }\n\n      &--system {\n        background-color: $color-system;\n      }\n\n      &--info {\n        background-color: $color-info;\n      }\n\n      &--error {\n        background-color: $color-error;\n      }\n\n      &--success {\n        background-color: $color-success;\n      }\n    }\n\n    &__content {\n      display: flex;\n      align-items: center;\n      width: 100%;\n\n      &--left {\n        justify-content: flex-start;\n      }\n\n      &--center {\n        justify-content: flex-start;\n      }\n\n      &--right {\n        justify-content: flex-start;\n      }\n      .slot {\n        display: flex;\n        justify-content: space-between;\n        margin-left: 8px;\n        width: 100%;\n        color: $color-content-default;\n      }\n    }\n\n    &__action {\n      display: inline-flex;\n      align-items: center;\n\n      .close {\n        cursor: pointer;\n        display: flex;\n        margin-left: 8px;\n      }\n    }\n  }\n}\n\n.space_left {\n  display: flex;\n}\n\n:host(.banner--hide) {\n  display: none;\n}\n", "import {\n  Component,\n  Host,\n  h,\n  ComponentInterface,\n  Prop,\n  State,\n  Method,\n  Event,\n  EventEmitter,\n  Element,\n} from '@stencil/core';\n\nexport type BannerVariant = 'system' | 'warning' | 'info' | 'error' | 'success';\nexport type BannerAlign = 'left' | 'right' | 'center';\nexport type ButtonClose = 'true' | 'false';\nexport type Link = 'true' | 'false';\nexport type Context = 'inside' | 'outside';\n@Component({\n  tag: 'bds-banner',\n  styleUrl: 'banner.scss',\n  shadow: true,\n})\nexport class Banner implements ComponentInterface {\n  @Element() el: HTMLBdsBannerElement;\n  @State() visible = true;\n  /**\n   * Set the banner aligment, it can be one of: 'center', 'right' or 'left'.\n   */\n  @Prop() bannerAlign?: BannerAlign = 'center';\n  /**\n   * Set if show up the close button.\n   */\n  @Prop() buttonClose?: ButtonClose = 'false';\n  /**\n   * Set if the banner is external or internal.\n   */\n  @Prop() context?: Context = 'outside';\n  /**\n   * Set the banner varient, it can be 'system' or 'warning'.\n   */\n  @Prop() variant?: BannerVariant = 'system';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Emitted when the banner is closed.\n   */\n  @Event() bdsBannerClose!: EventEmitter;\n  @Method()\n  async toggle() {\n    this.visible = !this.visible;\n  }\n\n  private _buttonClickHandler = () => {\n    this.bdsBannerClose.emit();\n    this.visible = false;\n  };\n\n  render() {\n    return (\n      <Host class={{ banner: true, 'banner--hide': !this.visible }}>\n        <div\n          class={{\n            banner__holder: true,\n            [`banner__holder--align--${this.bannerAlign}`]: true,\n            [`banner__holder--${this.variant}`]: true,\n            [`banner__holder--context--${this.context}`]: true,\n          }}\n        >\n          <div\n            class={{\n              banner__content: true,\n            }}\n          >\n            {this.variant === 'warning' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"warning\"></bds-icon>\n            )}\n            {this.variant === 'system' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"info\"></bds-icon>\n            )}\n            {this.variant === 'info' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"message-ballon\"></bds-icon>\n            )}\n            {this.variant === 'error' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"error\"></bds-icon>\n            )}\n            {this.variant === 'success' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"checkball\"></bds-icon>\n            )}\n            <div class=\"slot\">\n              <slot></slot>\n            </div>\n          </div>\n          <div\n            class={{\n              banner__action: true,\n            }}\n          >\n            {this.buttonClose === 'true' && (\n              <div class=\"close\" onClick={() => this._buttonClickHandler()}>\n                <bds-button-icon\n                  dataTest={this.dtButtonClose}\n                  icon=\"close\"\n                  size=\"short\"\n                  variant=\"secondary\"\n                ></bds-button-icon>\n              </div>\n            )}\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,SAAS,GAAG,kjEAAkjE;;MCuBvjE,MAAM,GAAA,MAAA;AALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAOW,QAAA,IAAO,CAAA,OAAA,GAAG,IAAI;AACvB;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAiB,QAAQ;AAC5C;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAiB,OAAO;AAC3C;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,SAAS;AACrC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAmB,QAAQ;AAE1C;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AAU7B,QAAA,IAAmB,CAAA,mBAAA,GAAG,MAAK;AACjC,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;AAC1B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACtB,SAAC;AAyDF;AAhEC,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;;IAQ9B,MAAM,GAAA;AACJ,QAAA,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAA,EAC1D,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,CAAC,0BAA0B,IAAI,CAAC,WAAW,CAAE,CAAA,GAAG,IAAI;AACpD,gBAAA,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAE,CAAA,GAAG,IAAI;AACzC,gBAAA,CAAC,4BAA4B,IAAI,CAAC,OAAO,CAAE,CAAA,GAAG,IAAI;aACnD,EAAA,EAED,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;aACtB,EAAA,EAEA,IAAI,CAAC,OAAO,KAAK,SAAS,KACzB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,SAAS,EAAA,CAAY,CACrF,EACA,IAAI,CAAC,OAAO,KAAK,QAAQ,KACxB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,MAAM,EAAA,CAAY,CAClF,EACA,IAAI,CAAC,OAAO,KAAK,MAAM,KACtB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,gBAAgB,EAAA,CAAY,CAC5F,EACA,IAAI,CAAC,OAAO,KAAK,OAAO,KACvB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAA,CAAY,CACnF,EACA,IAAI,CAAC,OAAO,KAAK,SAAS,KACzB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,WAAW,EAAA,CAAY,CACvF,EACD,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,MAAM,EAAA,EACf,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,CACF,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;AACrB,aAAA,EAEA,EAAA,IAAI,CAAC,WAAW,KAAK,MAAM,KAC1B,4DAAK,KAAK,EAAC,OAAO,EAAC,OAAO,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE,EAAA,EAC1D,CACE,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,QAAQ,EAAE,IAAI,CAAC,aAAa,EAC5B,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACF,CAAA,CACf,CACP,CACG,CACF,CACD;;;;;;;;"}