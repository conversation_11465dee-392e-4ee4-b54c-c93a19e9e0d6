System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,i,s;return{setters:[function(e){t=e.r;i=e.h;s=e.H}],execute:function(){var a='@-webkit-keyframes pulse{0%{scale:100%;opacity:1}20%{scale:140%;opacity:0}21%{scale:100%;opacity:1}40%{scale:140%;opacity:0}41%{scale:140%;opacity:0}100%{scale:140%;opacity:0}}@keyframes pulse{0%{scale:100%;opacity:1}20%{scale:140%;opacity:0}21%{scale:100%;opacity:1}40%{scale:140%;opacity:0}41%{scale:140%;opacity:0}100%{scale:140%;opacity:0}}.color--system{background-color:var(--color-system, rgb(178, 223, 253));color:var(--color-system, rgb(178, 223, 253))}.color--danger{background-color:var(--color-error, rgb(250, 190, 190));color:var(--color-error, rgb(250, 190, 190))}.color--warning{background-color:var(--color-warning, rgb(253, 233, 155));color:var(--color-warning, rgb(253, 233, 155))}.color--success{background-color:var(--color-success, rgb(132, 235, 188));color:var(--color-success, rgb(132, 235, 188))}.color--neutral{background-color:var(--color-surface-3, rgb(227, 227, 227));color:var(--color-surface-3, rgb(227, 227, 227))}:host{display:-ms-inline-flexbox;display:inline-flex}.chip_size{min-width:24px}.chip_badge .status{width:8px;height:8px}.chip_badge .status--circle{width:0;height:0;border:4px solid;border-radius:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .status--circle-true::before{content:"";width:8px;height:8px;position:absolute;border:1px solid;border-radius:8px;-webkit-animation:pulse 2s ease-out infinite;animation:pulse 2s ease-out infinite}.chip_badge .status--triangle{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:8px solid}.chip_badge .status--triangle-reverse{width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:8px solid}.chip_badge .status--polygon{width:0;height:0;border:4px solid;rotate:45deg}.chip_badge .status--square{width:0;height:0;border:4px solid}.chip_badge .icon{position:relative}.chip_badge .icon bds-icon{position:absolute}.chip_badge .icon .bds-icon{color:var(--color-content-default, rgb(40, 40, 40))}.chip_badge .icon--circle{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .icon--circle .trim{width:24px;height:24px;border-radius:16px}.chip_badge .icon--circle .trim-true::before{content:"";width:24px;height:24px;left:-2px;top:-2px;position:absolute;border:2px solid;border-radius:16px;-webkit-animation:pulse 2s ease-out infinite;animation:pulse 2s ease-out infinite}.chip_badge .icon--triangle{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:end;align-items:end}.chip_badge .icon--triangle .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 0, 100% 100%, 0 100%);clip-path:polygon(50% 0, 100% 100%, 0 100%)}.chip_badge .icon--triangle-reverse{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:start;align-items:start}.chip_badge .icon--triangle-reverse .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 100%, 0 0, 100% 0);clip-path:polygon(50% 100%, 0 0, 100% 0)}.chip_badge .icon--polygon{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .icon--polygon .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)}.chip_badge .icon--square{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .icon--square .trim{width:24px;height:24px}.chip_badge .number{display:-ms-flexbox;display:flex;height:24px;padding:0 8px;border-radius:16px;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;position:relative}.chip_badge .number--true::before{content:"";width:100%;height:24px;left:-2px;top:-2px;position:absolute;border:2px solid;border-radius:16px;-webkit-animation:pulse 2s ease-out infinite;animation:pulse 2s ease-out infinite}.chip_badge .number .number_text{color:var(--color-content-default, rgb(40, 40, 40))}.chip_badge .empty{display:-ms-flexbox;display:flex;min-height:24px;min-width:24px;position:relative}.chip_badge .empty--true::before{content:"";width:100%;height:24px;left:-2px;top:-2px;position:absolute;border:2px solid;border-radius:16px;-webkit-animation:pulse 2s ease-out infinite;animation:pulse 2s ease-out infinite}.chip_badge .empty--circle{border-radius:50%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .empty--circle .trim{width:24px;height:24px;border-radius:16px}.chip_badge .empty--circle .trim-true::before{content:"";width:24px;height:24px;left:-2px;top:-2px;position:absolute;border:2px solid;border-radius:16px;-webkit-animation:pulse 2s ease-out infinite;animation:pulse 2s ease-out infinite}.chip_badge .empty--triangle{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:end;align-items:end}.chip_badge .empty--triangle .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 0, 100% 100%, 0 100%);clip-path:polygon(50% 0, 100% 100%, 0 100%)}.chip_badge .empty--triangle-reverse{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:start;align-items:start}.chip_badge .empty--triangle-reverse .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 100%, 0 0, 100% 0);clip-path:polygon(50% 100%, 0 0, 100% 0)}.chip_badge .empty--polygon{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .empty--polygon .trim{width:24px;height:24px;-webkit-clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);clip-path:polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)}.chip_badge .empty--square{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.chip_badge .empty--square .trim{width:24px;height:24px}';var r=e("bds_badge",function(){function e(e){t(this,e);this.type="status";this.color="system";this.shape="circle";this.icon=null;this.animation=false;this.dataTest=null}e.prototype.componentWillLoad=function(){if(this.icon===null&&this.number){this.type="number"}else if(!this.number&&this.icon){this.type="icon"}else if(this.number&&this.icon){this.type="number"}else if(this.number===0){this.type="empty"}};e.prototype.numberChanged=function(e){if(e===0){this.type="empty"}else if(this.icon===null&&e!==null){this.type="number"}};e.prototype.render=function(){var e,t,a,r,n,c;return i(s,{key:"d20ef9793af71ebc0945fceb95ca517c85de527b"},i("div",{key:"9d4aa070ca534a0edf680daadb1cd78dd99ad695",class:(e={chip_badge:true,chip_size:this.number!==0?true:false},e["chip_badge--".concat(this.shape)]=true,e["chip_badge--".concat(this.color)]=true,e),"data-test":this.dataTest},this.type==="status"&&i("div",{key:"17db29aefd614b84e2ad4e14f677e0e9f1270a0a",class:(t={status:true},t["status--".concat(this.shape)]=true,t["color--".concat(this.color)]=true,t["status--circle-".concat(this.animation)]=true,t)}),this.type==="icon"&&i("div",{key:"5ad822383ab49895d3f9135d36b803c9afec9df3",class:(a={icon:true},a["icon--".concat(this.shape)]=true,a)},i("div",{key:"d6f9358bd0a441c85043587cda77bfca483a277c",class:(r={},r["color--".concat(this.color)]=true,r.trim=true,r["trim-".concat(this.animation)]=true,r)}),i("bds-icon",{key:"8b760e7b1ddb604659bf886c28d39d88b7711ad2",size:"xxx-small",name:this.icon})),this.type==="number"&&i("div",{key:"3da3d77e5107792d256b9892be77e15a693bf8a5",class:(n={number:true},n["color--".concat(this.color)]=true,n["number--".concat(this.animation)]=true,n)},i("bds-typo",{key:"7b6eb54efcb34abd18fcdd232d8a589d4288383d",class:"number_text",variant:"fs-12",bold:"bold",margin:false},this.number>=999?"999+":this.number)),this.type==="empty"&&i("div",{key:"b2c5b644be228c6df3ec179d3c091f6ccdddb19e",class:(c={empty:true},c["color--".concat(this.color)]=true,c["empty--".concat(this.shape)]=true,c["empty--".concat(this.animation)]=true,c)})))};Object.defineProperty(e,"watchers",{get:function(){return{number:["numberChanged"]}},enumerable:false,configurable:true});return e}());r.style=a}}}));
//# sourceMappingURL=p-392dd470.system.entry.js.map