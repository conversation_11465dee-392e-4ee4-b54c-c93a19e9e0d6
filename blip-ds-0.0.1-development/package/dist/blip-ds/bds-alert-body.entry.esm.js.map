{"version": 3, "file": "bds-alert-body.entry.esm.js", "sources": ["src/components/alert/alert-body/alert-body.scss?tag=bds-alert-body&encapsulation=shadow", "src/components/alert/alert-body/alert-body.tsx"], "sourcesContent": [".alert__body {  \n  position: relative;\n  width: 100%;\n  padding: 12px 16px;\n  margin-bottom: 8px;\n  box-sizing: border-box;\n\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-alert-body',\n  styleUrl: 'alert-body.scss',\n  shadow: true,\n})\nexport class AlertBody implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"alert__body\">\n        <bds-typo variant=\"fs-14\" bold=\"regular\" slot=\"body\">\n          <slot />\n        </bds-typo>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,YAAY,GAAG,oIAAoI;;MCO5I,SAAS,GAAA,MAAA;;;;IACpB,MAAM,GAAA;QACJ,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EACtB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAA,EAClD,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACC,CACP;;;;;;;"}