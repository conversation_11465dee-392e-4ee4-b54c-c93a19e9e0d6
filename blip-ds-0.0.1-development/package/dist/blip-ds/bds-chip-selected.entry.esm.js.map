{"version": 3, "file": "bds-chip-selected.entry.esm.js", "sources": ["src/components/chip-selected/chip-selected.scss?tag=bds-chip-selected&encapsulation=shadow", "src/components/chip-selected/chip-selected.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  height: fit-content;\n  border-radius: 4px;\n  box-sizing: border-box;\n  max-width: 100%;\n\n  .chip {\n    display: flex;\n    min-width: 32px;\n    width: max-content;\n    height: 32px;\n    border-radius: 16px;\n    padding: 2px 4px;\n    align-items: center;\n    box-sizing: border-box;\n    justify-content: center;\n    position: relative;\n    z-index: 0;\n\n    .chip_focus:focus {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      padding: 2px;\n      border-radius: 4px;\n      outline: $color-focus solid 2px;\n    }\n\n    .chip_darker {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      border-radius: inherit;\n      z-index: 1;\n      backdrop-filter: brightness(1);\n      box-sizing: border-box;\n    }\n\n    &--icon {\n      display: flex;\n      align-items: center;\n      padding-left: 4px;\n      height: 20px;\n      z-index: 2;\n    }\n    &--text {\n      display: flex;\n      align-items: center;\n      height: 20px;\n      z-index: 2;\n      margin: 0 8px;\n      flex-wrap: nowrap;\n      font-family: $font-family;\n    }\n    &--tall {\n      height: 40px;\n      border-radius: 24px;\n    }\n    &--default {\n      background-color: $color-system;\n      color: $color-content-default;\n    }\n    &--info {\n      background-color: $color-info;\n      color: $color-content-default;\n    }\n    &--success {\n      background-color: $color-success;\n      color: $color-content-default;\n    }\n    &--warning {\n      background-color: $color-warning;\n      color: $color-content-default;\n    }\n    &--danger {\n      background-color: $color-error;\n      color: $color-content-default;\n    }\n    &--outline {\n      border: 1px solid $color-border-1;\n      color: $color-content-default;\n      padding: 2px 3px;\n      .chip_darker {\n        height: calc(100% + 2px);\n      }\n    }\n    &:hover {\n      cursor: pointer;\n      .chip_darker {\n        backdrop-filter: brightness(0.9);\n      }\n    }\n    &:active {\n      cursor: pointer;\n      .chip_darker {\n        backdrop-filter: brightness(0.8);\n      }\n    }\n    &:focus-visible {\n      outline: none;\n    }\n  }\n\n  .chip_selected {\n    display: flex;\n    min-width: 32px;\n    width: max-content;\n    height: 32px;\n    border-radius: 16px;\n    padding: 2px;\n    align-items: center;\n    box-sizing: border-box;\n    background-color: $color-surface-1;\n    border: 2px solid $color-content-default;\n\n    &--container-text {\n      &--full {\n        width: 100%;\n      }\n      &--half {\n        width: calc(100% - 20px);\n      }\n    }\n\n    &--icon {\n      display: flex;\n      align-items: center;\n      height: 20px;\n      padding-left: 4px;\n      color: $color-content-default;\n    }\n    &--text {\n      display: flex;\n      align-items: center;\n      height: 20px;\n      margin: 0 8px;\n      flex-wrap: nowrap;\n      color: $color-content-default;\n      font-family: $font-family;\n    }\n    &--tall {\n      height: 40px;\n      border-radius: 24px;\n    }\n    &:hover {\n      opacity: 38%;\n      cursor: pointer;\n    }\n    &:active {\n      opacity: 38%;\n    }\n  }\n\n  .chip_disabled {\n    display: flex;\n    min-width: 32px;\n    width: max-content;\n    height: 32px;\n    border-radius: 16px;\n    padding: 2px 4px;\n    align-items: center;\n    box-sizing: border-box;\n    justify-content: center;\n    position: relative;\n    z-index: 0;\n    background-color: $color-surface-3;\n\n    &--icon {\n      display: flex;\n      align-items: center;\n      padding-left: 4px;\n      width: 16px;\n      height: 20px;\n      color: $color-content-default;\n      z-index: 2;\n    }\n    &--text {\n      display: flex;\n      align-items: center;\n      height: 20px;\n      z-index: 2;\n      margin: 0 8px;\n      flex-wrap: nowrap;\n      color: $color-content-default;\n      font-family: $font-family;\n    }\n    &--tall {\n      height: 40px;\n      border-radius: 24px;\n    }\n  }\n  .chip_disabled:hover {\n    cursor: default;\n  }\n}\n", "import { Component, Host, h, Prop, Event, EventEmitter, State, Element } from '@stencil/core';\n\nexport type ColorChipSelected = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline';\nexport type Size = 'standard' | 'tall';\n\n@Component({\n  tag: 'bds-chip-selected',\n  styleUrl: 'chip-selected.scss',\n  shadow: true,\n})\nexport class ChipSelected {\n  @Element() el?: HTMLElement;\n  @State() isSelected = false;\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipSelected = 'default';\n  /**\n   * used for change the chip size. Use one of them;\n   */\n  @Prop() size?: Size = 'standard';\n  /**\n   * used for set the initial setup for true;\n   */\n  @Prop() selected?: boolean = false;\n  /**\n   * When 'true', no events will be dispatched\n   */\n  @Prop() disabled?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Event() chipClick: EventEmitter;\n\n  private handleKeyDown(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      event.preventDefault();\n      if (this.isSelected) {\n        this.isSelected = false;\n      } else {\n        this.isSelected = true;\n      }\n      this.chipClick.emit({ selected: this.isSelected });\n    }\n  }\n\n  private handleClick(event) {\n    if (!this.disabled) {\n      event.preventDefault();\n      if (this.isSelected) {\n        this.isSelected = false;\n      } else {\n        this.isSelected = true;\n      }\n      this.chipClick.emit({ selected: this.isSelected });\n    }\n  }\n\n  componentWillLoad() {\n    this.el.focus();\n    this.isSelected = this.selected;\n  }\n\n  private getDisabledChip() {\n    return this.disabled ? { chip_disabled: true, [`chip_disabled--${this.size}`]: true } : {};\n  }\n\n  private getStyleChip() {\n    return this.isSelected\n      ? { chip_selected: true, [`chip_selected--${this.size}`]: true }\n      : { [`chip--${this.color}`]: true, [`chip--${this.size}`]: true };\n  }\n\n  private getStyleText() {\n    if (this.isSelected) {\n      const chipSelected = { 'chip_selected--text': true };\n      return chipSelected;\n    }\n  }\n\n  private getSizeIconChip() {\n    if (this.size === 'tall') {\n      return 'medium';\n    } else return 'x-small';\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip: true,\n            ...this.getStyleChip(),\n            ...this.getDisabledChip(),\n          }}\n          onClick={(ev) => this.handleClick(ev)}\n          data-test={this.dataTest}\n        >\n          {!this.disabled && <div class=\"chip_focus\" onKeyDown={this.handleKeyDown.bind(this)} tabindex=\"0\"></div>}\n          {!this.isSelected && !this.disabled && <div class=\"chip_darker\"></div>}\n          {this.icon && !this.isSelected && (\n            <div class=\"chip--icon\">\n              <bds-icon size={this.getSizeIconChip()} name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.isSelected && (\n            <div class=\"chip_selected--icon\">\n              <bds-icon size={this.getSizeIconChip()} name=\"checkball\"></bds-icon>\n            </div>\n          )}\n          <div class={this.isSelected ? `chip_selected--container-text--half` : `chip_selected--container-text--full`}>\n            <bds-typo class={{ 'chip--text': true, ...this.getStyleText() }} variant=\"fs-12\" no-wrap bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,eAAe,GAAG,ulJAAulJ;;MCUlmJ,YAAY,GAAA,MAAA;AALzB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAOW,QAAA,IAAU,CAAA,UAAA,GAAG,KAAK;AAK3B;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAuB,SAAS;AAC7C;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAU,UAAU;AAChC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAClC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAClC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAyFjC;AArFS,IAAA,aAAa,CAAC,KAAK,EAAA;AACzB,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClE,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;iBAClB;AACL,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAExB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;;;AAI9C,IAAA,WAAW,CAAC,KAAK,EAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK;;iBAClB;AACL,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAExB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;;;IAItD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;;IAGzB,eAAe,GAAA;QACrB,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAA,CAAE,GAAG,IAAI,EAAE,GAAG,EAAE;;IAGpF,YAAY,GAAA;QAClB,OAAO,IAAI,CAAC;AACV,cAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAkB,eAAA,EAAA,IAAI,CAAC,IAAI,CAAA,CAAE,GAAG,IAAI;cAC5D,EAAE,CAAC,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,EAAE,CAAC,CAAA,MAAA,EAAS,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE;;IAG7D,YAAY,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,YAAY,GAAG,EAAE,qBAAqB,EAAE,IAAI,EAAE;AACpD,YAAA,OAAO,YAAY;;;IAIf,eAAe,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACxB,YAAA,OAAO,QAAQ;;;AACV,YAAA,OAAO,SAAS;;IAGzB,MAAM,GAAA;AACJ,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,IAAI,EACP,EAAA,IAAI,CAAC,YAAY,EAAE,CAAA,EACnB,IAAI,CAAC,eAAe,EAAE,CAAA,EAE3B,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAC1B,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAEvB,CAAC,IAAI,CAAC,QAAQ,IAAI,4DAAK,KAAK,EAAC,YAAY,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC,GAAG,EAAO,CAAA,EACvG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,aAAa,EAAO,CAAA,EACrE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,KAC5B,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,YAAY,EAAA,EACrB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAA,CAAa,CAChE,CACP,EACA,IAAI,CAAC,UAAU,KACd,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,qBAAqB,EAAA,EAC9B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAC,WAAW,EAAY,CAAA,CAChE,CACP,EACD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,CAAA,mCAAA,CAAqC,GAAG,CAAqC,mCAAA,CAAA,EAAA,EACzG,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAI,MAAA,CAAA,MAAA,CAAA,EAAA,YAAY,EAAE,IAAI,IAAK,IAAI,CAAC,YAAY,EAAE,GAAI,OAAO,EAAC,OAAO,EAAS,SAAA,EAAA,IAAA,EAAA,IAAI,EAAC,MAAM,EAAA,EAClG,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACJ,CACP,CACF,CACD;;;;;;;;"}