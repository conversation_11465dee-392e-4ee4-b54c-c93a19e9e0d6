import{r as t,h as r}from"./p-C3J6Z5OX.js";const e=":host{display:block}:host *{color:var(--color-content-default, rgb(40, 40, 40))}.button--icon{-webkit-transform:rotate(180deg);transform:rotate(180deg);color:var(--color-content-ghost, rgb(140, 140, 140))}.breadcrumb__button--0{padding-left:0}.breadcrumb__button--0 .button--icon{display:none}.breadcrumb__button--1{padding-left:8px}.breadcrumb__button--2{padding-left:16px}.breadcrumb__button--3{padding-left:24px}.breadcrumb__button--4{padding-left:32px}.breadcrumb__link--text{color:var(--color-content-disable, rgb(89, 89, 89))}.breadcrumb__link{text-decoration:none}";const o=class{constructor(r){t(this,r);this.items=[];this.parsedItems=[];this.isDropdownOpen=false}parseItems(t){if(typeof t==="string"){try{this.parsedItems=JSON.parse(t)}catch(t){this.parsedItems=[]}}else{this.parsedItems=t}}componentWillLoad(){this.parseItems(this.items)}toggleDropdown(){this.isDropdownOpen=!this.isDropdownOpen}render(){if(!this.parsedItems||this.parsedItems.length===0){return r("p",null,"Sem itens para exibir no Breadcrumb.")}const t=this.parsedItems.length<=3?this.parsedItems:[this.parsedItems[0],{label:"...",href:null},this.parsedItems[this.parsedItems.length-1]];return r("nav",{"aria-label":"breadcrumb"},r("bds-grid",{direction:"row","align-items":"center"},t.map(((e,o)=>r("bds-grid",{class:{breadcrumb__item:true,"breadcrumb__item--active":o===t.length-1},"aria-current":o===t.length-1?"page":null},e.label==="..."?r("bds-dropdown",{"active-mode":"click",position:"auto"},r("bds-grid",{slot:"dropdown-content"},r("bds-grid",{direction:"column",padding:"1",gap:"half"},this.parsedItems.slice(1,-1).map(((t,e)=>r("bds-grid",{class:`breadcrumb__button--${e}`},t.href?r("a",{href:t.href,class:`breadcrumb__link breadcrumb__button--${e}`},r("bds-grid",{"align-items":"center",gap:"half"},r("bds-icon",{name:"reply",theme:"outline",class:"button--icon",size:"x-small"}),r("bds-button",{variant:"text",color:"content",size:"short"},t.label))):r("span",null,t.label)))))),r("bds-grid",{slot:"dropdown-activator","align-items":"center"},r("bds-button",{variant:"text",color:"content",size:"short",onClick:()=>this.toggleDropdown(),"icon-left":"more-options-horizontal"}),r("bds-icon",{name:"arrow-right",size:"x-small"}))):e.href?r("bds-grid",{direction:"row"},r("bds-typo",{variant:"fs-12",margin:false,class:"breadcrumb__link--text"},r("a",{href:e.href,class:"breadcrumb__link"},e.label)),r("bds-icon",{name:"arrow-right",size:"x-small"})):r("bds-grid",{direction:"row"},r("bds-typo",{variant:"fs-12",bold:"semi-bold",margin:false},e.label)))))))}static get watchers(){return{items:["parseItems"]}}};o.style=e;export{o as bds_breadcrumb};
//# sourceMappingURL=p-455ef450.entry.js.map