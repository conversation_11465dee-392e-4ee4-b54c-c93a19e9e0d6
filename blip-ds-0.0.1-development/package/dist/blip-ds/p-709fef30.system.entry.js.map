{"version": 3, "names": ["buttonGroupCss", "ButtonGroup", "exports", "class_1", "hostRef", "this", "activeIndexes", "Set", "size", "direction", "color", "multiple", "prototype", "componentDidLoad", "buttons", "el", "getElementsByTagName", "setupButtons", "componentDidUpdate", "handlePropChanges", "_this", "i", "button", "this_1", "setAttribute", "toString", "addEventListener", "selectButton", "set<PERSON><PERSON><PERSON>", "updateButtonPosition", "updateButtonDirection", "updateButtonSize", "updateButtonColor", "length", "activateButton", "index", "has", "delete", "add", "clear", "updateButtonStates", "clickedIndex", "isActive", "classList", "remove", "buttonSelected", "emit", "id", "setPosition", "setDirection", "setSize", "setColor", "render", "h", "Host", "key", "class"], "sources": ["src/components/button/button-group.scss?tag=bds-button-group&encapsulation=shadow", "src/components/button/button-group.tsx"], "sourcesContent": [":host {\n    width: fit-content;\n}", "import { Component, h, Element, State, Event, EventEmitter, Prop, Host, Watch, Method } from '@stencil/core';\nimport { direction } from '../grid/grid-interface';\nimport { ButtonSize } from './button';\n\ninterface HTMLBdsButtonElement extends HTMLElement {\n  setVariant(variant: string): void;\n  setColor(color: string): void;\n  setSize(size: string): void;\n  setDirection(direction: string): void;\n  isActive(active: boolean): void;\n  setPosition(position: string): void;\n}\n\n@Component({\n  tag: 'bds-button-group',\n  styleUrl: 'button-group.scss',\n  shadow: true,\n})\nexport class ButtonGroup {\n  @Element() el!: HTMLElement;\n\n  @State() activeIndexes: Set<number> = new Set();\n\n  /**\n   * Size of the buttons. Can be one of:\n   * 'medium', 'large'.\n   */\n  @Prop({ mutable: true }) size?: ButtonSize = 'medium';\n\n  /**\n   * Direction of the button group layout. Can be one of:\n   * 'row', 'column'.\n   */\n  @Prop({ mutable: true }) direction?: direction = 'row';\n\n  /**\n   * Color scheme for the buttons. Default is 'primary'.\n   */\n  @Prop({ mutable: true }) color?: string = 'primary';\n\n  /**\n   * Allows multiple buttons to be selected simultaneously if true.\n   */\n  @Prop({ mutable: true }) multiple? = false;\n\n  @Event() buttonSelected: EventEmitter;\n\n  private buttons: HTMLCollectionOf<HTMLBdsButtonElement>;\n\n  componentDidLoad() {\n    this.buttons = this.el.getElementsByTagName('bds-button') as HTMLCollectionOf<HTMLBdsButtonElement>;\n    this.setupButtons();\n  }\n\n  componentDidUpdate() {\n    this.setupButtons();\n  }\n\n  @Watch('size')\n  @Watch('direction')\n  @Watch('color')\n  @Watch('multiple')\n  handlePropChanges() {\n    // Re-setup buttons when props change\n    this.setupButtons();\n  }\n\n  setupButtons() {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      button.setAttribute('data-index', i.toString());\n      button.addEventListener('click', () => this.selectButton(i));\n      button.setVariant('outline');\n      this.updateButtonPosition(i);\n      this.updateButtonDirection(i);\n      this.updateButtonSize(i);\n      this.updateButtonColor(i);\n    }\n  }\n\n  @Method()\n  async activateButton(index: number) {\n    if (index >= 0 && index < this.buttons.length) {\n      this.selectButton(index);\n    }\n  }\n\n  selectButton(index: number) {\n    if (this.multiple) {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.delete(index);\n      } else {\n        this.activeIndexes.add(index);\n      }\n    } else {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.clear();\n      } else {\n        this.activeIndexes.clear();\n        this.activeIndexes.add(index);\n      }\n    }\n    this.updateButtonStates(index);\n  }\n\n  updateButtonStates(clickedIndex: number) {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      if (this.activeIndexes.has(i)) {\n        button.isActive(true);\n        button.setVariant('solid');\n        button.classList.add('active');\n      } else {\n        button.isActive(false);\n        button.setVariant('outline');\n        button.classList.remove('active');\n      }\n      if (i === clickedIndex) {\n        this.buttonSelected.emit(button.id);\n      }\n    }\n  }\n\n  updateButtonPosition(index: number) {\n    const button = this.buttons[index];\n    if (index === 0) {\n      button.setPosition('first');\n    } else if (index === this.buttons.length - 1) {\n      button.setPosition('last');\n    } else {\n      button.setPosition('middle');\n    }\n  }\n\n  updateButtonDirection(index: number) {\n    const button = this.buttons[index];\n    this.direction === 'row' ? button.setDirection('row') : button.setDirection('column');\n  }\n\n  updateButtonSize(index: number) {\n    const button = this.buttons[index];\n    this.size === 'medium' ? button.setSize('medium') : button.setSize('large');\n  }\n\n  updateButtonColor(index: number) {\n    const button = this.buttons[index];\n    button.setColor(this.color);\n  }\n\n  render() {\n    return (\n      <Host class=\"button_group\">\n        <bds-grid direction={this.direction}>\n          <slot></slot>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gnDAAA,IAAMA,EAAiB,4E,ICkBVC,EAAWC,EAAA,8BALxB,SAAAC,EAAAC,G,uDAQWC,KAAAC,cAA6B,IAAIC,IAMjBF,KAAIG,KAAgB,SAMpBH,KAASI,UAAe,MAKxBJ,KAAKK,MAAY,UAKjBL,KAAQM,SAAI,KAmHtC,CA7GCR,EAAAS,UAAAC,iBAAA,WACER,KAAKS,QAAUT,KAAKU,GAAGC,qBAAqB,cAC5CX,KAAKY,c,EAGPd,EAAAS,UAAAM,mBAAA,WACEb,KAAKY,c,EAOPd,EAAAS,UAAAO,kBAAA,WAEEd,KAAKY,c,EAGPd,EAAAS,UAAAK,aAAA,eAAAG,EAAAf,K,eACWgB,GACP,IAAMC,EAASC,EAAKT,QAAQO,GAC5BC,EAAOE,aAAa,aAAcH,EAAEI,YACpCH,EAAOI,iBAAiB,SAAS,WAAM,OAAAN,EAAKO,aAAaN,EAAlB,IACvCC,EAAOM,WAAW,WAClBL,EAAKM,qBAAqBR,GAC1BE,EAAKO,sBAAsBT,GAC3BE,EAAKQ,iBAAiBV,GACtBE,EAAKS,kBAAkBX,E,aARzB,IAAK,IAAIA,EAAI,EAAGA,EAAIhB,KAAKS,QAAQmB,OAAQZ,IAAG,C,EAAnCA,E,GAaLlB,EAAAS,UAAAsB,eAAN,SAAqBC,G,qFACnB,GAAIA,GAAS,GAAKA,EAAQ9B,KAAKS,QAAQmB,OAAQ,CAC7C5B,KAAKsB,aAAaQ,E,kBAItBhC,EAAAS,UAAAe,aAAA,SAAaQ,GACX,GAAI9B,KAAKM,SAAU,CACjB,GAAIN,KAAKC,cAAc8B,IAAID,GAAQ,CACjC9B,KAAKC,cAAc+B,OAAOF,E,KACrB,CACL9B,KAAKC,cAAcgC,IAAIH,E,MAEpB,CACL,GAAI9B,KAAKC,cAAc8B,IAAID,GAAQ,CACjC9B,KAAKC,cAAciC,O,KACd,CACLlC,KAAKC,cAAciC,QACnBlC,KAAKC,cAAcgC,IAAIH,E,EAG3B9B,KAAKmC,mBAAmBL,E,EAG1BhC,EAAAS,UAAA4B,mBAAA,SAAmBC,GACjB,IAAK,IAAIpB,EAAI,EAAGA,EAAIhB,KAAKS,QAAQmB,OAAQZ,IAAK,CAC5C,IAAMC,EAASjB,KAAKS,QAAQO,GAC5B,GAAIhB,KAAKC,cAAc8B,IAAIf,GAAI,CAC7BC,EAAOoB,SAAS,MAChBpB,EAAOM,WAAW,SAClBN,EAAOqB,UAAUL,IAAI,S,KAChB,CACLhB,EAAOoB,SAAS,OAChBpB,EAAOM,WAAW,WAClBN,EAAOqB,UAAUC,OAAO,S,CAE1B,GAAIvB,IAAMoB,EAAc,CACtBpC,KAAKwC,eAAeC,KAAKxB,EAAOyB,G,IAKtC5C,EAAAS,UAAAiB,qBAAA,SAAqBM,GACnB,IAAMb,EAASjB,KAAKS,QAAQqB,GAC5B,GAAIA,IAAU,EAAG,CACfb,EAAO0B,YAAY,Q,MACd,GAAIb,IAAU9B,KAAKS,QAAQmB,OAAS,EAAG,CAC5CX,EAAO0B,YAAY,O,KACd,CACL1B,EAAO0B,YAAY,S,GAIvB7C,EAAAS,UAAAkB,sBAAA,SAAsBK,GACpB,IAAMb,EAASjB,KAAKS,QAAQqB,GAC5B9B,KAAKI,YAAc,MAAQa,EAAO2B,aAAa,OAAS3B,EAAO2B,aAAa,S,EAG9E9C,EAAAS,UAAAmB,iBAAA,SAAiBI,GACf,IAAMb,EAASjB,KAAKS,QAAQqB,GAC5B9B,KAAKG,OAAS,SAAWc,EAAO4B,QAAQ,UAAY5B,EAAO4B,QAAQ,Q,EAGrE/C,EAAAS,UAAAoB,kBAAA,SAAkBG,GAChB,IAAMb,EAASjB,KAAKS,QAAQqB,GAC5Bb,EAAO6B,SAAS9C,KAAKK,M,EAGvBP,EAAAS,UAAAwC,OAAA,WACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAM,gBACVH,EAAA,YAAAE,IAAA,2CAAU9C,UAAWJ,KAAKI,WACxB4C,EAAa,QAAAE,IAAA,8C,8UAvIC,I", "ignoreList": []}