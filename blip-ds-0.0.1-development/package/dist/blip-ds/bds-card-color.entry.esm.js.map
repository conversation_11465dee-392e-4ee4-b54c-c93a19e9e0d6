{"version": 3, "file": "bds-card-color.entry.esm.js", "sources": ["src/components/card-color/card-color.scss?tag=bds-card-color&encapsulation=shadow", "src/components/card-color/card-color.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$border-radius: 8px;\n.card {\n  cursor: pointer;\n}\n.card-color {\n  width: 239px;\n  height: 136px;\n  display: flex;\n  flex-direction: column;\n  border-radius: $border-radius;\n  box-shadow: $shadow-1;\n  margin-left: 8px;\n  margin-top: 8px;\n\n  &--color {\n    flex: 2;\n    border-top-left-radius: $border-radius;\n    border-top-right-radius: $border-radius;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    &-brand {\n      background-color: $color-brand;\n    }\n\n    &-primary {\n      background-color: $color-primary;\n    }\n\n    &-secondary {\n      background-color: $color-secondary;\n    }\n\n    &-surface-1 {\n      background-color: $color-surface-1;\n    }\n\n    &-surface-2 {\n      background-color: $color-surface-2;\n    }\n\n    &-surface-3 {\n      background-color: $color-surface-3;\n    }\n\n    &-surface-4 {\n      background-color: $color-surface-4;\n    }\n\n    &-content-default {\n      background-color: $color-content-default;\n    }\n\n    &-content-disable {\n      background-color: $color-content-disable;\n    }\n\n    &-content-ghost {\n      background-color: $color-content-ghost;\n    }\n\n    &-content-bright {\n      background-color: $color-content-bright;\n    }\n\n    &-content-din {\n      background-color: $color-content-din;\n    }\n\n    &-border-1 {\n      background-color: $color-border-1;\n    }\n\n    &-border-2 {\n      background-color: $color-border-2;\n    }\n\n    &-border-3 {\n      background-color: $color-border-3;\n    }\n\n    &-info {\n      background-color: $color-info;\n    }\n\n    &-system {\n      background-color: $color-system;\n    }\n\n    &-focus {\n      background-color: $color-focus;\n    }\n\n    &-success {\n      background-color: $color-success;\n    }\n\n    &-warning {\n      background-color: $color-warning;\n    }\n\n    &-error {\n      background-color: $color-error;\n    }\n\n    &-delete {\n      background-color: $color-delete;\n    }\n\n    &-extended-blue {\n      background-color: $color-extended-blue;\n    }\n\n    &-extended-ocean {\n      background-color: $color-extended-ocean;\n    }\n\n    &-extended-green {\n      background-color: $color-extended-green;\n    }\n\n    &-extended-yellow {\n      background-color: $color-extended-yellow;\n    }\n\n    &-extended-orange {\n      background-color: $color-extended-orange;\n    }\n\n    &-extended-red {\n      background-color: $color-extended-red;\n    }\n\n    &-extended-pink {\n      background-color: $color-extended-pink;\n    }\n\n    &-extended-gray {\n      background-color: $color-extended-gray;\n    }\n  }\n}\n\n.card-text {\n  animation: invert-motion 1s;\n}\n\n.card-text-copie {\n  animation: motion 3s 1;\n}\n\n@keyframes motion {\n  0% {\n    height: 0;\n  }\n  30% {\n    height: 50%;\n  }\n  85% {\n    letter-spacing: 0;\n  }\n}\n\n@keyframes invert-motion {\n  0% {\n    height: 100%;\n  }\n  30% {\n    height: 50%;\n  }\n  85% {\n    letter-spacing: 0;\n  }\n}\n", "import { Component, h, Prop, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-color',\n  styleUrl: 'card-color.scss',\n  shadow: true,\n})\nexport class CardColor {\n  @State() showMessage = false;\n  /**\n   * Specifies name color, use Figma docs in Blip DS.\n   */\n  @Prop() name!: string;\n\n  /**\n   * Specifies HEX color, use Figma docs in Blip DS.\n   */\n  @Prop() hex?: string;\n\n  /**\n   * Specifies if the hex is a linear gradient\n   */\n  @Prop() gradient = false;\n\n  /**\n   * Specifies variabel sass color, _variables.scss.\n   */\n  @Prop() variable!: string;\n\n  /**\n   * If true, the text will be white\n   */\n  @Prop() lightText = false;\n\n  handleCopyVariable = (variable) => {\n    const value = `$${variable}`;\n    navigator.clipboard.writeText(value);\n\n    this.showMessage = true;\n\n    // Ocultar a mensagem após 3 segundos\n    setTimeout(() => {\n      this.showMessage = false;\n    }, 3000);\n  };\n\n  render(): HTMLDivElement {\n    return (\n      <bds-paper class=\"card\" width=\"240px\" height=\"140px\" onClick={() => this.handleCopyVariable(this.variable)}>\n        <bds-grid direction=\"column\" height=\"100%\">\n          <bds-grid\n            height=\"70%\"\n            xxs=\"12\"\n            class={{\n              'card-color--color': true,\n              [`card-color--${this.variable}`]: true,\n            }}\n          ></bds-grid>\n          <bds-grid justify-content=\"center\" align-items=\"center\" height=\"30%\">\n            {!this.showMessage ? (\n              <bds-typo class=\"card-text\" variant=\"fs-14\" bold=\"bold\">\n                ${this.variable}\n              </bds-typo>\n            ) : (\n              <bds-typo class=\"card-text-copie\" variant=\"fs-14\" bold=\"bold\">\n                Cor copiada!\n              </bds-typo>\n            )}\n          </bds-grid>\n        </bds-grid>\n      </bds-paper>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,YAAY,GAAG,ymHAAymH;;MCOjnH,SAAS,GAAA,MAAA;AALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAMW,QAAA,IAAW,CAAA,WAAA,GAAG,KAAK;AAW5B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAOxB;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AAEzB,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,QAAQ,KAAI;AAChC,YAAA,MAAM,KAAK,GAAG,CAAI,CAAA,EAAA,QAAQ,EAAE;AAC5B,YAAA,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;AAEpC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;YAGvB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK;aACzB,EAAE,IAAI,CAAC;AACV,SAAC;AA6BF;IA3BC,MAAM,GAAA;AACJ,QAAA,QACE,CAAW,CAAA,WAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAA,EACxG,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,SAAS,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAA,EACxC,CACE,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,MAAM,EAAC,KAAK,EACZ,GAAG,EAAC,IAAI,EACR,KAAK,EAAE;AACL,gBAAA,mBAAmB,EAAE,IAAI;AACzB,gBAAA,CAAC,eAAe,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;AACvC,aAAA,EACS,CAAA,EACZ,CAA0B,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,iBAAA,EAAA,QAAQ,EAAa,aAAA,EAAA,QAAQ,EAAC,MAAM,EAAC,KAAK,EACjE,EAAA,CAAC,IAAI,CAAC,WAAW,IAChB,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,WAAW,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,OACnD,IAAI,CAAC,QAAQ,CACN,KAEX,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAElD,EAAA,cAAA,CAAA,CACZ,CACQ,CACF,CACD;;;;;;;"}