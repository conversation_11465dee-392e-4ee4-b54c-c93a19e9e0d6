{"version": 3, "names": ["tabCss", "Tab", "exports", "class_1", "hostRef", "this", "active", "isActive", "prototype", "handleTabChange", "event", "detail", "group", "onClick", "bdsTabChange", "emit", "render", "bold", "h", "Host", "key", "class", "_a", "bind", "variant", "label"], "sources": ["src/components/tabs/tab (depreciated)/tab/tab.scss?tag=bds-tab", "src/components/tabs/tab (depreciated)/tab/tab.tsx"], "sourcesContent": ["@use '../../../../globals/helpers' as *;\n\n.bds-tab {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  box-sizing: content-box;\n  min-width: fit-content;\n  max-width: 270px;\n  height: 46px;\n  max-height: 48px;\n  \n  cursor: pointer;\n  text-align: center;\n  color: $color-content-disable;\n  border-bottom: 2px solid transparent;\n\n  &:not(:last-child) {\n    margin-right: 32px;\n  }\n\n  &:hover {\n    color: $color-content-default;\n  }\n\n  &--selected {\n    animation-name: selectFade;\n    animation-duration: 0.75s;\n    animation-fill-mode: forwards;\n  }\n\n  &__text {\n    min-width: 90px;\n    max-width: 270px;\n  }\n\n  @keyframes selectFade {\n    from{\n       border-bottom: 2px solid transparent;\n       color: $color-content-default;\n    }\n    to{\n       border-bottom: 2px solid $color-brand;\n      color: $color-content-default;\n    }\n  }\n}\n\n@media (max-width: 599px) {\n  .bds-tab {\n    min-width: 110px;\n    text-overflow: ellipsis;\n  }\n}\n", "import { Component, ComponentInterface, EventEmitter, Event, h, Prop, Host, Listen, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab',\n  styleUrl: 'tab.scss',\n})\nexport class Tab implements ComponentInterface {\n  /**\n   * Specifies the Tab group. Used to link it to the TabPanel.\n   */\n  @Prop() group!: string;\n\n  /**\n   * The text to be shown at the Tab\n   */\n  @Prop() label!: string;\n\n  /**\n   * Prop to control externally if a tab will be active by default\n   */\n  @Prop() active = false;\n\n  /**\n   * State to control if a tab is current active\n   */\n  @State() isActive = false;\n\n  /**\n   * Event to emmit when the active tab should be updated\n   */\n  @Event() bdsTabChange: EventEmitter;\n\n  @Listen('bdsTabChange', { target: 'body' })\n  @Listen('bdsTabInit', { target: 'body' })\n  handleTabChange(event: CustomEvent) {\n    this.isActive = event.detail == this.group;\n  }\n\n  async onClick() {\n    this.bdsTabChange.emit(this.group);\n  }\n\n  render(): HTMLElement {\n    const bold = this.isActive ? 'bold' : 'regular';\n    return (\n      <Host\n        class={{\n          'bds-tab': true,\n          ['bds-tab--selected']: this.isActive,\n        }}\n        onClick={this.onClick.bind(this)}\n      >\n        <div class=\"bds-tab__text\">\n          <bds-typo variant=\"fs-16\" bold={bold}>\n            {this.label}\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAS,uzC,ICMFC,EAAGC,EAAA,qBAJhB,SAAAC,EAAAC,G,mDAkBUC,KAAMC,OAAG,MAKRD,KAAQE,SAAG,KAmCrB,CA1BCJ,EAAAK,UAAAC,gBAAA,SAAgBC,GACdL,KAAKE,SAAWG,EAAMC,QAAUN,KAAKO,K,EAGjCT,EAAAK,UAAAK,QAAN,W,qFACER,KAAKS,aAAaC,KAAKV,KAAKO,O,iBAG9BT,EAAAK,UAAAQ,OAAA,W,MACE,IAAMC,EAAOZ,KAAKE,SAAW,OAAS,UACtC,OACEW,EAACC,EAAI,CAAAC,IAAA,2CACHC,OAAKC,EAAA,CACH,UAAW,MACXA,EAAC,qBAAsBjB,KAAKE,S,GAE9BM,QAASR,KAAKQ,QAAQU,KAAKlB,OAE3Ba,EAAK,OAAAE,IAAA,2CAAAC,MAAM,iBACTH,EAAA,YAAAE,IAAA,2CAAUI,QAAQ,QAAQP,KAAMA,GAC7BZ,KAAKoB,Q,WAhDF,I", "ignoreList": []}