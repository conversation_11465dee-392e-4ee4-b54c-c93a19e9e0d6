import{r as e,c as n,h as s,a as t}from"./p-C3J6Z5OX.js";const i=':Host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;cursor:pointer;text-decoration:underline;white-space:nowrap;margin-left:16px;-ms-flex-order:2;order:2}.banner__link{position:relative}.banner__link::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.banner__link:focus-visible{outline:none}.banner__link:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}';const r=class{constructor(s){e(this,s);this.bdsBannerLink=n(this,"bdsBannerLink");this.target="blank";this.dataTest=null;this._buttonClickHandler=()=>{this.bdsBannerLink.emit(this.el);window.open(this.link,`_${this.target}`)}}handleKeyDown(e){if(e.key=="Enter"){this.bdsBannerLink.emit(this.el);window.open(this.link,`_${this.target}`)}}render(){const e="a";return s(e,{key:"c8568adcbe7eb31b2bcf5ac88f08357a950d30c5",class:{banner__link:true},onClick:()=>this._buttonClickHandler(),"data-test":this.dataTest,tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)},s("slot",{key:"eaaa13f59a5662c02459cbb12cb1cfa78c5a1120"}))}get el(){return t(this)}};r.style=i;export{r as bds_banner_link};
//# sourceMappingURL=p-355fcc8c.entry.js.map