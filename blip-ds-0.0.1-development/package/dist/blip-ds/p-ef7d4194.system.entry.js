var __awaiter=this&&this.__awaiter||function(o,r,t,e){function i(o){return o instanceof t?o:new t((function(r){r(o)}))}return new(t||(t=Promise))((function(t,n){function a(o){try{s(e.next(o))}catch(o){n(o)}}function c(o){try{s(e["throw"](o))}catch(o){n(o)}}function s(o){o.done?t(o.value):i(o.value).then(a,c)}s((e=e.apply(o,r||[])).next())}))};var __generator=this&&this.__generator||function(o,r){var t={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},e,i,n,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(o){return function(r){return s([o,r])}}function s(c){if(e)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(t=0)),t)try{if(e=1,i&&(n=c[0]&2?i["return"]:c[0]?i["throw"]||((n=i["return"])&&n.call(i),0):i.next)&&!(n=n.call(i,c[1])).done)return n;if(i=0,n)c=[c[0]&2,n.value];switch(c[0]){case 0:case 1:n=c;break;case 4:t.label++;return{value:c[1],done:false};case 5:t.label++;i=c[1];c=[0];continue;case 7:c=t.ops.pop();t.trys.pop();continue;default:if(!(n=t.trys,n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){t.label=c[1];break}if(c[0]===6&&t.label<n[1]){t.label=n[1];n=c;break}if(n&&t.label<n[2]){t.label=n[2];t.ops.push(c);break}if(n[2])t.ops.pop();t.trys.pop();continue}c=r.call(o,t)}catch(o){c=[6,o];i=0}finally{e=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(o){"use strict";var r,t,e,i;return{setters:[function(o){r=o.r;t=o.c;e=o.h;i=o.a}],execute:function(){var n='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';var a=o("bds_accordion_group",function(){function o(o){r(this,o);this.bdsAccordionCloseAll=t(this,"bdsAccordionCloseAll");this.bdsAccordionOpenAll=t(this,"bdsAccordionOpenAll");this.accordionsElement=null;this.collapse="single";this.divisor=true}o.prototype.closeAll=function(o){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(t){this.bdsAccordionCloseAll.emit();for(r=0;r<this.accordionsElement.length;r++){if(this.collapse!="multiple"){if(o!=r)this.accordionsElement[r].close()}else{this.accordionsElement[r].close()}}return[2]}))}))};o.prototype.openAll=function(o){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(t){this.bdsAccordionOpenAll.emit();for(r=0;r<this.accordionsElement.length;r++){if(this.collapse!="multiple"){if(o!=r)this.accordionsElement[r].open()}else{this.accordionsElement[r].open()}}return[2]}))}))};o.prototype.divisorChanged=function(o){if(this.accordionsElement){for(var r=0;r<this.accordionsElement.length;r++){this.accordionsElement[r].divisor=o}}};o.prototype.componentWillRender=function(){this.accordionsElement=this.element.getElementsByTagName("bds-accordion");for(var o=0;o<this.accordionsElement.length;o++){this.accordionsElement[o].reciveNumber(o);this.accordionsElement[o].divisor=this.divisor}};o.prototype.render=function(){return e("div",{key:"44b6dac1fe73f585accb3841012bec42ff8a0d1d",class:"accordion_group"},e("slot",{key:"dea06f9bc51a0a18c36108706a3cf7f9b1e2128e"}))};Object.defineProperty(o.prototype,"element",{get:function(){return i(this)},enumerable:false,configurable:true});Object.defineProperty(o,"watchers",{get:function(){return{divisor:["divisorChanged"]}},enumerable:false,configurable:true});return o}());a.style=n}}}));
//# sourceMappingURL=p-ef7d4194.system.entry.js.map