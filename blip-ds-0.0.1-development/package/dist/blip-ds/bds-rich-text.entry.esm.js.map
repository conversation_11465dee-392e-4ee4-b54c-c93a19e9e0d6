{"version": 3, "file": "bds-rich-text.entry.esm.js", "sources": ["src/components/rict-text/languages/pt_BR.tsx", "src/components/rict-text/languages/es_ES.tsx", "src/components/rict-text/languages/en_US.tsx", "src/components/rict-text/languages/index.ts", "src/components/rict-text/rich-text.scss?tag=bds-rich-text", "src/components/rict-text/rich-text.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    bold: 'Negrito',\n    italic: 'It<PERSON>lico',\n    strike: '<PERSON><PERSON><PERSON>',\n    underline: 'Sublin<PERSON>o',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: '<PERSON><PERSON><PERSON> à esquerda',\n    align_center: '<PERSON><PERSON>ar ao centro',\n    align_right: 'Alinhar à direita',\n    unordered_list: 'Lista não ordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Citação',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpar formatação',\n    expand: 'Expandir',\n  },\n];\n", "export const esTerms = [\n  {\n    bold: 'Negrita',\n    italic: '<PERSON>urs<PERSON>',\n    strike: 'Tacha<PERSON>',\n    underline: 'Subrayado',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: 'Alinear a la izquierda',\n    align_center: 'Alinear al centro',\n    align_right: 'Alinear a la derecha',\n    unordered_list: 'Lista desordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Cita',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpiar formato',\n    expand: 'Expandir',\n  },\n];\n", "export const enTerms = [\n  {\n    bold: 'Bold',\n    italic: 'Italic',\n    strike: 'Strikethrough',\n    underline: 'Underline',\n    link: 'Link',\n    code: 'Code',\n    align_left: 'Align left',\n    align_center: 'Align center',\n    align_right: 'Align right',\n    unordered_list: 'Unordered list',\n    ordered_list: 'Ordered list',\n    quote: 'Quote',\n    h1: 'Heading 1',\n    h2: 'Heading 2',\n    h3: 'Heading 3',\n    h4: 'Heading 4',\n    h5: 'Heading 5',\n    h6: 'Heading 6',\n    clear_formatting: 'Clear formatting',\n    expand: 'Expand',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "@use '../../globals/helpers' as *;\n\n.rich-text {\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 8px;\n  gap: 8px;\n  border: 1px solid $color-border-1;\n  border-radius: 16px;\n  background-color: $color-surface-1;\n\n  &-top {\n    .format-buttons {\n      order: 1;\n    }\n    .preview {\n      order: 2;\n    }\n  }\n\n  &-bottom {\n    .format-buttons {\n      order: 2;\n    }\n    .preview {\n      order: 1;\n    }\n  }\n\n  &.active {\n    border-color: $color-primary;\n    box-shadow: 0 0 0 2px $color-info;\n  }\n\n  .format-buttons {\n    display: none !important;\n\n    &-active {\n      display: flex !important;\n      position: relative;\n      background-color: $color-surface-0;\n      border: 1px solid $color-border-1;\n      border-radius: 16px;\n      padding: 8px;\n    }\n\n    .style-onhover {\n      position: absolute;\n      background-color: $color-surface-1;\n      border-radius: 32px;\n      bottom: -32px;\n      right: 0;\n      opacity: 0;\n      -webkit-transition: opacity ease-in-out 0.5s;\n      -moz-transition: opacity ease-in-out 0.5s;\n      transition: opacity ease-in-out 0.5s;\n      pointer-events: none;\n\n      &.active {\n        opacity: 1;\n      }\n    }\n\n    .accordion-header {\n      width: 100%;\n      position: relative;\n      padding-right: 40px;\n\n      .buttons-list {\n        column-gap: 8px;\n\n        .editor-bar {\n          width: 0;\n          margin-right: -8px;\n        }\n\n        & bds-tooltip {\n          -webkit-transition: height ease-in-out 0.25s;\n          -moz-transition: height ease-in-out 0.25s;\n          transition: height ease-in-out 0.25s;\n          height: 0px;\n\n          & > bds-button,\n          & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n            height: 0;\n            opacity: 0;\n            display: block;\n            overflow: hidden;\n            -webkit-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            -moz-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n          }\n\n          &.active {\n            height: 32px;\n            & > bds-button,\n            & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n              overflow: inherit;\n              height: 32px;\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .arrow-down {\n        position: absolute;\n        right: 0;\n        top: 0;\n        display: none;\n        &.active {\n          display: block;\n        }\n      }\n    }\n  }\n  .preview {\n    box-sizing: border-box;\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    padding: 8px;\n    -webkit-transition: height ease-in-out 0.25s;\n    -moz-transition: height ease-in-out 0.25s;\n    transition: height ease-in-out 0.25s;\n\n    .editor-uai-design-system {\n      min-height: 48px;\n      height: 100%;\n      background-color: transparent;\n      font-size: 1rem;\n      line-height: 1.5;\n      overflow-y: auto;\n      outline: none;\n      font-family: $font-family;\n      font-style: normal;\n      font-weight: normal;\n      color: $color-content-default;\n      @include custom-scroll;\n\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5,\n      h6,\n      ul,\n      ol,\n      blockquote {\n        margin: 0 0 8px 0;\n      }\n\n      h1 {\n        font-size: 32px;\n        font-weight: 600;\n      }\n      h2 {\n        font-size: 28px;\n        font-weight: 600;\n      }\n      h3 {\n        font-size: 24px;\n        font-weight: 600;\n      }\n      h4 {\n        font-size: 20px;\n        font-weight: 600;\n      }\n      h5 {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      h6 {\n        font-size: 12px;\n        font-weight: 600;\n      }\n\n      a {\n        text-decoration: none;\n        color: $color-primary;\n      }\n\n      blockquote {\n        padding: 4px 16px 4px 32px;\n        font-size: 14px;\n        position: relative;\n        display: inline-block;\n\n        &::before,\n        &::after {\n          content: '\"';\n          position: absolute;\n          font-size: 24px;\n          color: $color-content-ghost;\n        }\n\n        &::before {\n          left: 8px;\n          top: -6px;\n        }\n\n        &::after {\n          right: 0px;\n          bottom: 0px;\n        }\n      }\n      code {\n        font-family: monospace;\n        font-size: 12px;\n        background-color: $color-surface-2;\n        padding: 4px;\n        border-radius: 4px;\n      }\n    }\n  }\n}\n\n/* Editor */\n", "import { Element, Component, h, Host, Prop, State, Watch, Event, EventEmitter } from '@stencil/core';\nimport { getParentsUntil } from '../../utils/position-element';\nimport { languages } from './rich-text-interface';\nimport { termTranslate } from './languages';\n\nexport type positionBar = 'top' | 'bottom';\n\n@Component({\n  tag: 'bds-rich-text',\n  styleUrl: 'rich-text.scss',\n  shadow: false,\n})\nexport class RichText {\n  private buttonsListElement?: HTMLElement = null;\n  private buttonsEditElements?: HTMLCollectionOf<HTMLBdsTooltipElement> = null;\n  private editor?: HTMLElement = null;\n  private dropDownLink?: HTMLBdsDropdownElement = null;\n  private inputSetLink?: HTMLBdsInputElement;\n\n  @Element() el: HTMLElement;\n  @State() buttomBoldActive?: boolean = false;\n  @State() buttomItalicActive?: boolean = false;\n  @State() buttomStrikeActive?: boolean = false;\n  @State() buttomUnderlineActive?: boolean = false;\n  @State() buttomCodeActive?: boolean = false;\n  @State() buttomLinkActive?: boolean = false;\n  @State() buttomLinkValidDisabled?: boolean = true;\n  @State() buttomAlignLeftActive?: boolean = false;\n  @State() buttomAlignCenterActive?: boolean = false;\n  @State() buttomAlignRightActive?: boolean = false;\n  @State() buttomUnorderedListActive?: boolean = false;\n  @State() buttomOrderedListActive?: boolean = false;\n  @State() buttomQuoteActive?: boolean = false;\n  @State() buttomH1Active?: boolean = false;\n  @State() buttomH2Active?: boolean = false;\n  @State() buttomH3Active?: boolean = false;\n  @State() buttomH4Active?: boolean = false;\n  @State() buttomH5Active?: boolean = false;\n  @State() buttomH6Active?: boolean = false;\n  @State() buttomAccordionActive?: boolean = false;\n  @State() headerHeight?: string = '32px';\n  @State() hasSelectionRange?: boolean = false;\n  @State() selectedLinesList?: { element: HTMLElement }[] = null;\n  @State() treeElementsEditor?: HTMLElement[] = null;\n  @State() styleSectorActive?: string = null;\n  @State() styleOnHover?: string = 'teste';\n  @State() whenSelectionLink?: Range = null;\n  @State() linkButtonInput?: string = null;\n  @State() insideComponent?: boolean = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * weightButton to define if component has Bold Control.\n   */\n  @Prop() weightButton?: boolean = true;\n  /**\n   * italicButton to define if component has Italic Control.\n   */\n  @Prop() italicButton?: boolean = true;\n  /**\n   * strikeThroughbutton to define if component has Strike Control.\n   */\n  @Prop() strikeThroughButton?: boolean = true;\n  /**\n   * underlineButton to define if component has Underline Control.\n   */\n  @Prop() underlineButton?: boolean = true;\n  /**\n   * linkButton to define if component has Link Control.\n   */\n  @Prop() linkButton?: boolean = true;\n  /**\n   * codeButton to define if component has Code Control.\n   */\n  @Prop() codeButton?: boolean = true;\n  /**\n   * alignmentButtons to define if component has TextAlign Control.\n   */\n  @Prop() alignmentButtons?: boolean = true;\n  /**\n   * listButtons to define if component has List Control.\n   */\n  @Prop() listButtons?: boolean = true;\n  /**\n   * quoteButton to define if component has Quote Control.\n   */\n  @Prop() quoteButton?: boolean = true;\n  /**\n   * headingButtons to define if component has Heading Control.\n   */\n  @Prop() headingButtons?: boolean = true;\n  /**\n   * unstyledButton to define if component has Unstyled Control.\n   */\n  @Prop() unstyledButton?: boolean = true;\n  /**\n   * height is the prop to define height of component.\n   */\n  @Prop() height?: string = null;\n  /**\n   * maxHeight is the prop to define max height of component.\n   */\n  @Prop() maxHeight?: string = null;\n  /**\n   * positionBar is the prop to define max height of component.\n   */\n  @Prop() positionBar?: positionBar = 'top';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsRichTextChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsRichTextInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  componentDidLoad() {\n    if (this.editor.innerHTML.trim() === '') {\n      this.editor.innerHTML = '<p class=\"line\"><br></p>';\n    }\n    if (\n      this.weightButton ||\n      this.italicButton ||\n      this.strikeThroughButton ||\n      this.underlineButton ||\n      this.linkButton ||\n      this.codeButton ||\n      this.alignmentButtons ||\n      this.listButtons ||\n      this.quoteButton ||\n      this.headingButtons ||\n      this.unstyledButton\n    ) {\n      this.buttonsEditElements = this.buttonsListElement.getElementsByTagName(\n        'bds-tooltip',\n      ) as HTMLCollectionOf<HTMLBdsTooltipElement>;\n      this.accordionHeader(false);\n      this.editor.parentElement.style.height = `calc(100% - 56px)`;\n    } else {\n      this.editor.parentElement.style.height = `100%`;\n    }\n  }\n\n  @Watch('weightButton')\n  @Watch('italicButton')\n  @Watch('strikeThroughButton')\n  @Watch('underlineButton')\n  @Watch('linkButton')\n  @Watch('codeButton')\n  @Watch('alignmentButtons')\n  @Watch('listButtons')\n  @Watch('quoteButton')\n  @Watch('headingButtons')\n  @Watch('unstyledButton')\n  protected buttomsHeaderChanged(): void {\n    setTimeout(() => this.accordionHeader(this.buttomAccordionActive), 500);\n  }\n\n  @Watch('buttomAccordionActive')\n  protected buttomAccordionActiveChanged(): void {\n    this.accordionHeader(this.buttomAccordionActive);\n  }\n\n  private updateToolbarState() {\n    const selection = window.getSelection();\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    this.treeElementsEditor = getParentsUntil(parentElement, '.editor-uai-design-system');\n  }\n\n  // Coloca o cursor no final do editor\n  private accordionHeader(value: boolean) {\n    const allbuttonsEditElementsWidth = this.buttonsEditElements.length * 40;\n    const buttonsListWidth = this.buttonsListElement.offsetWidth;\n    const buttonAccordion = this.el.querySelector('#buttonAccordion') as HTMLBdsButtonElement;\n    if (buttonsListWidth < allbuttonsEditElementsWidth) {\n      buttonAccordion.classList.add('active');\n    } else {\n      buttonAccordion.classList.remove('active');\n    }\n    const diferrence = (buttonsListWidth * this.buttonsEditElements.length) / allbuttonsEditElementsWidth;\n    const numberOfColumns = Math.ceil(allbuttonsEditElementsWidth / buttonsListWidth);\n    const allbuttonsEditElements = Array.from(this.buttonsEditElements);\n    allbuttonsEditElements.slice(0, Math.floor(diferrence)).forEach((element) => {\n      element.classList.add('active');\n    });\n    if (value) {\n      allbuttonsEditElements.forEach((element) => {\n        element.classList.add('active');\n        this.editor.parentElement.style.height = `calc(100% - ${numberOfColumns * 32 + 24}px)`;\n      });\n    } else {\n      allbuttonsEditElements.slice(Math.floor(diferrence)).forEach((element) => {\n        element.classList.remove('active');\n        this.editor.parentElement.style.height = `calc(100% - 56px)`;\n      });\n    }\n  }\n\n  @Watch('treeElementsEditor')\n  protected treeElementsEditorChanged(value): void {\n    const tagList = value.map((element) => element?.tagName.toLowerCase());\n    const tagVerifyName = (tag) => tagList.includes(tag);\n    const getLine = value.find((el) => el?.classList.contains('line'));\n    this.buttomBoldActive = tagVerifyName('b');\n    this.buttomItalicActive = tagVerifyName('i');\n    this.buttomStrikeActive = tagVerifyName('strike');\n    this.buttomUnderlineActive = tagVerifyName('u');\n    this.buttomLinkActive = tagVerifyName('a');\n    this.buttomCodeActive = tagVerifyName('code');\n    this.buttomAlignLeftActive = getLine?.style.textAlign === 'left';\n    this.buttomAlignCenterActive = getLine?.style.textAlign === 'center';\n    this.buttomAlignRightActive = getLine?.style.textAlign === 'right';\n    this.buttomUnorderedListActive = tagList[0] === 'ul';\n    this.buttomOrderedListActive = tagList[0] === 'ol';\n    this.buttomQuoteActive = tagVerifyName('blockquote');\n    this.buttomH1Active = tagVerifyName('h1');\n    this.buttomH2Active = tagVerifyName('h2');\n    this.buttomH3Active = tagVerifyName('h3');\n    this.buttomH4Active = tagVerifyName('h4');\n    this.buttomH5Active = tagVerifyName('h5');\n    this.buttomH6Active = tagVerifyName('h6');\n  }\n\n  private refButtonsListElement = (el: HTMLElement): void => {\n    this.buttonsListElement = el;\n  };\n  private refeditorElement = (el: HTMLElement): void => {\n    this.editor = el;\n  };\n  private refDropDownLinkElement = (el: HTMLBdsDropdownElement): void => {\n    this.dropDownLink = el;\n  };\n\n  private refInputSetLink = (el: HTMLBdsInputElement): void => {\n    this.inputSetLink = el;\n  };\n\n  private clearToolbar = () => {\n    this.buttomBoldActive = false;\n    this.buttomItalicActive = false;\n    this.buttomStrikeActive = false;\n    this.buttomUnderlineActive = false;\n    this.buttomLinkActive = false;\n    this.buttomCodeActive = false;\n    this.buttomAlignLeftActive = false;\n    this.buttomAlignCenterActive = false;\n    this.buttomAlignRightActive = false;\n    this.buttomUnorderedListActive = false;\n    this.buttomOrderedListActive = false;\n    this.buttomQuoteActive = false;\n    this.buttomH1Active = false;\n    this.buttomH2Active = false;\n    this.buttomH3Active = false;\n    this.buttomH4Active = false;\n    this.buttomH5Active = false;\n    this.buttomH6Active = false;\n  };\n\n  private setheaderHeight = () => {\n    this.buttomAccordionActive = !this.buttomAccordionActive;\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    const range = selection.getRangeAt(0);\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  };\n\n  private onBlur = () => {\n    this.el.classList.remove('active');\n    if (this.insideComponent === false) {\n      this.clearToolbar();\n    }\n    this.bdsBlur.emit();\n  };\n\n  private onFocus = () => {\n    this.el.classList.add('active');\n    this.bdsFocus.emit();\n  };\n\n  // Função para ajustar parágrafos durante a edição\n  private onInput(ev: InputEvent) {\n    ev.preventDefault();\n    this.bdsRichTextInput.emit(ev);\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const currentNode = range.startContainer;\n\n    // Se o nó atual é uma `div`, converta-o em um `p`\n    if (currentNode.nodeType === Node.ELEMENT_NODE && (currentNode as HTMLElement).tagName === 'DIV') {\n      const divElement = currentNode as HTMLElement;\n\n      const pElement = document.createElement('p');\n      pElement.classList.add('line');\n      pElement.innerHTML = divElement.innerHTML;\n\n      divElement.parentNode.replaceChild(pElement, divElement);\n    }\n\n    // Garante que novas linhas (Enter) criem <p> ao invés de <div>\n    this.editor.querySelectorAll('div').forEach((div) => {\n      const p = document.createElement('p');\n      p.classList.add('line');\n      p.innerHTML = div.innerHTML;\n      div.replaceWith(p);\n    });\n  }\n\n  private onKeydown = (event: KeyboardEvent) => {\n    if (event.key === 'Backspace') {\n      const selection = window.getSelection();\n      if (!selection || selection.rangeCount === 0) return;\n\n      const range = selection.getRangeAt(0);\n      const startNode = range.startContainer;\n\n      // Encontra o elemento de bloco que contém o cursor\n      let blockElement = startNode.nodeType === Node.TEXT_NODE ? startNode.parentElement : (startNode as HTMLElement);\n\n      while (blockElement && !blockElement.classList.contains('line') && blockElement !== this.editor) {\n        blockElement = blockElement.parentElement;\n      }\n\n      // Se o elemento atual for um <blockquote> e estiver vazio, removê-lo\n      if (\n        blockElement &&\n        blockElement.tagName === 'BLOCKQUOTE' &&\n        blockElement.classList.contains('line') &&\n        blockElement.innerText.length <= 1\n      ) {\n        event.preventDefault(); // Impede a exclusão padrão\n        blockElement.remove(); // Remove apenas o blockquote vazio\n      }\n    }\n    if (this.editor.textContent.length === 0 && event.key === 'Backspace') {\n      event.preventDefault();\n      this.editor.innerHTML = `<p class=\"line\"><br></p>`;\n      this.setCursorToEnd();\n    }\n    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {\n      event.preventDefault(); // Impede o Ctrl+Z\n      event.stopPropagation(); // Evita que afete outros elementos\n    }\n  };\n\n  // Controle a navegação do componente\n  private onFocusEditorBar(ev: FocusEvent) {\n    const editorBar = ev.target as HTMLElement;\n    const NextButton = editorBar.nextElementSibling.querySelector('bds-button');\n    const ElementToFocus = NextButton.shadowRoot.querySelector('.focus') as HTMLElement;\n    ElementToFocus.focus();\n    this.buttomAccordionActive = true;\n  }\n\n  // Coloca o cursor no final do editor\n  private setCursorToEnd() {\n    const range = document.createRange();\n    const sel = window.getSelection();\n    range.selectNodeContents(this.editor);\n    range.collapse(false);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n\n  private tagName(tag: string, tagList: HTMLElement[]): boolean {\n    const value = tagList.map((element) => element?.tagName.toLowerCase());\n    return value.includes(tag);\n  }\n\n  private wrapSelection(ev: CustomEvent, tag: string, link?: string) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n\n    const tagList = getParentsUntil(parentElement, '.line');\n    const isTagApplied = this.tagName(tag, tagList);\n\n    // Se a seleção estiver vazia, cria um espaço invisível para edição\n    let content: DocumentFragment;\n    let collapsedCursor = false;\n\n    // Se a tag já está aplicada e o usuário quer remover\n    if (isTagApplied) {\n      const appliedTag = tagList.find((el) => el.tagName.toLowerCase() === tag);\n      if (appliedTag) {\n        const parent = appliedTag.parentElement;\n        const isFullSelection = range.toString().trim() === appliedTag.textContent.trim();\n        const isAtEndOfTag = range.endOffset === appliedTag.textContent.length;\n\n        if (isFullSelection && parent) {\n          // Remove a tag se toda a seleção corresponde ao conteúdo da tag\n          while (appliedTag.firstChild) {\n            parent.insertBefore(appliedTag.firstChild, appliedTag);\n          }\n          parent.removeChild(appliedTag);\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        } else if (isAtEndOfTag) {\n          // Se o cursor está no final da tag, move para fora dela\n          content = document.createDocumentFragment();\n          const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n          content.appendChild(placeholder);\n          collapsedCursor = true;\n          const newRange = document.createRange();\n          newRange.setStartAfter(appliedTag); // Define o início depois do elemento\n          newRange.setEndAfter(appliedTag);\n          newRange.insertNode(content);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n          this.updateToolbarState();\n        } else {\n          // Se o cursor está no final da tag, move para fora dela\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        }\n      }\n      return;\n    }\n\n    if (range.collapsed) {\n      content = document.createDocumentFragment();\n      const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n      content.appendChild(placeholder);\n      collapsedCursor = true;\n    } else {\n      content = range.extractContents();\n    }\n\n    // Remove tags desnecessárias dentro da seleção\n    content.querySelectorAll('*').forEach((element) => {\n      while (element.firstChild) {\n        element.parentNode.insertBefore(element.firstChild, element);\n      }\n      element.remove();\n    });\n\n    // Cria a nova tag e aplica o conteúdo extraído\n    const wrapper = document.createElement(tag);\n    if (tag === 'a' && link) {\n      wrapper.setAttribute('href', link);\n    }\n    wrapper.appendChild(content);\n    range.insertNode(wrapper);\n\n    // Remove tags vazias no editor\n    this.editor.querySelectorAll('*').forEach((el) => {\n      if (!el.textContent.trim() && el.children.length === 0) {\n        el.remove();\n      }\n    });\n\n    // Se o cursor estava no meio da edição, mantém a seleção original\n    const newRange = document.createRange();\n    if (collapsedCursor) {\n      newRange.setStart(wrapper, 0);\n      newRange.setEnd(wrapper, 1);\n    } else {\n      newRange.setStartBefore(wrapper.firstChild || wrapper);\n      newRange.setEndAfter(wrapper.lastChild || wrapper);\n    }\n    selection.removeAllRanges();\n\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    // Emite o evento para atualizar o estado\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  private wrapSelectionLine(tag: string, enableLinesReturn: boolean = false) {\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Verifica se todas as linhas já possuem a tag escolhida\n    const allAreSameTag = [...selectedLines].every((line) =>\n      tag === 'li' ? false : line.tagName.toLowerCase() === tag,\n    );\n\n    const returnSelected: HTMLElement[] = [...selectedLines].map((el) => {\n      const newElement = document.createElement(allAreSameTag ? 'p' : tag);\n      newElement.classList.add('line');\n      newElement.innerHTML = el.innerHTML;\n      el.replaceWith(newElement);\n      return newElement;\n    });\n\n    if (enableLinesReturn) {\n      this.selectedLinesList = returnSelected.map((element) => ({ element }));\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    const newRange = document.createRange();\n    let lastNode = returnSelected[0].lastChild;\n\n    // Se não houver filhos, cria um nó de texto para evitar erro\n    if (!lastNode) {\n      lastNode = document.createTextNode('');\n      returnSelected[0].appendChild(lastNode);\n    }\n\n    // Se o último nó for outro elemento, busca um nó de texto dentro dele\n    while (lastNode && lastNode.nodeType !== Node.TEXT_NODE) {\n      lastNode = lastNode.lastChild || lastNode;\n    }\n\n    // Define o range no final do último nó de texto\n    newRange.setStart(lastNode, lastNode.textContent?.length || 0);\n    newRange.setEnd(lastNode, lastNode.textContent?.length || 0);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para aplicar alinhamento ao texto selecionado\n  private alignText(ev: CustomEvent, alignment: 'left' | 'center' | 'right' | 'justify') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    let blockElement = range.startContainer as HTMLElement;\n\n    // Percorre os elementos até encontrar um bloco válido que tenha a classe \"line\"\n    while (blockElement && blockElement !== this.editor) {\n      if (blockElement.nodeType === Node.ELEMENT_NODE && blockElement.classList.contains('line')) {\n        break;\n      }\n      blockElement = blockElement.parentElement;\n    }\n\n    // Se encontrou um elemento de bloco com a classe \"line\"\n    if (blockElement && blockElement !== this.editor) {\n      // Verifica se o alinhamento já está aplicado\n      const currentAlignment = (blockElement as HTMLElement).style.textAlign;\n      if (currentAlignment === alignment) {\n        // Se já estiver alinhado, remove o alinhamento\n        (blockElement as HTMLElement).style.textAlign = '';\n      } else {\n        // Caso contrário, aplica o alinhamento\n        (blockElement as HTMLElement).style.textAlign = alignment;\n      }\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createHeading(ev: CustomEvent, type: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine(type, true);\n    const firstItemList = this.selectedLinesList[0]?.element;\n    const firstParent = firstItemList.parentElement.previousElementSibling;\n    const lastParent = firstItemList.parentElement.nextElementSibling;\n    const parent = firstItemList.parentElement;\n    if (parent.tagName.toLowerCase() === 'ul') {\n      this.selectedLinesList.forEach((item) => {\n        if (firstParent) {\n          firstParent.insertAdjacentElement('afterend', item.element);\n        } else if (lastParent) {\n          lastParent.insertAdjacentElement('beforebegin', item.element);\n        } else {\n          this.editor.insertAdjacentElement('afterbegin', item.element);\n        }\n      });\n      if (Array.from(parent.getElementsByTagName('li')).length == 0) {\n        parent.remove();\n      }\n    }\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createList(ev: CustomEvent, type: 'ol' | 'ul') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine('li', true);\n    const firstItemList = this.selectedLinesList[0].element;\n    const lastItemList = this.selectedLinesList[this.selectedLinesList.length - 1]?.element;\n    const wrapper = document.createElement(type);\n    const parent = firstItemList.parentElement;\n\n    if (!this.verifyList(firstItemList, lastItemList)) {\n      parent.insertBefore(wrapper, firstItemList);\n      this.selectedLinesList.forEach((item) => {\n        wrapper.appendChild(item.element);\n      });\n    } else {\n      const parentListElements = parent.getElementsByTagName('li');\n      const parentList = Array.from(parentListElements).map((element) => ({ element }));\n\n      if (parentList.length == this.selectedLinesList.length) {\n        if (type !== parent.tagName.toLowerCase()) {\n          wrapper.innerHTML = parent.innerHTML;\n          parent.parentNode.replaceChild(wrapper, parent);\n        } else {\n          this.selectedLinesList.forEach((item) => {\n            const tagList = parent.parentElement.tagName.toLowerCase() === 'li' ? 'li' : 'p';\n            const newElement = document.createElement(tagList);\n            newElement.classList.add('line');\n            newElement.innerHTML = item.element.innerHTML;\n            if (parent.parentElement.tagName.toLowerCase() === 'li') {\n              parent.parentElement.insertAdjacentElement('afterend', newElement);\n            } else {\n              parent.previousElementSibling.insertAdjacentElement('afterend', newElement);\n            }\n            parent.removeChild(item.element);\n          });\n          parent.remove();\n        }\n      } else {\n        // parent.insertBefore(wrapper, firstItemList);\n        firstItemList.previousElementSibling.insertAdjacentElement('beforeend', wrapper);\n        this.selectedLinesList.forEach((item) => {\n          wrapper.appendChild(item.element);\n        });\n      }\n    }\n  }\n\n  private addSelectionLink(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      this.dropDownLink.setOpen();\n    }\n    this.editor.focus();\n    const selection = window.getSelection();\n    this.whenSelectionLink = selection.getRangeAt(0);\n    const ElementToFocus = this.inputSetLink.shadowRoot.querySelector('.input__container__text') as HTMLInputElement;\n    ElementToFocus.focus();\n  }\n\n  private addLinkInput(ev: InputEvent) {\n    ev.preventDefault();\n    const input = ev.target as HTMLInputElement | null;\n    this.linkButtonInput = input.value;\n    if (this.linkButtonInput.length > 0) {\n      this.buttomLinkValidDisabled = false;\n    } else {\n      this.buttomLinkValidDisabled = true;\n    }\n  }\n\n  private createLinkKeyDown(ev: KeyboardEvent) {\n    if (ev.key == 'Enter') {\n      this.createLink(ev);\n    }\n  }\n\n  private createLink(ev) {\n    ev.preventDefault();\n    const selection = window.getSelection();\n    selection.removeAllRanges();\n    selection.addRange(this.whenSelectionLink);\n    this.wrapSelection(ev, 'a', this.linkButtonInput);\n    if (this.dropDownLink) {\n      this.dropDownLink.setClose();\n    }\n  }\n\n  private verifyList(firstItem: HTMLElement, lastItem: HTMLElement) {\n    const firstItemValue =\n      firstItem.parentElement.tagName.toLowerCase() === 'ul' || firstItem.parentElement.tagName.toLowerCase() === 'ol';\n    const lastItemValue =\n      lastItem.parentElement.tagName.toLowerCase() === 'ul' || lastItem.parentElement.tagName.toLowerCase() === 'ol';\n    return firstItemValue && lastItemValue;\n  }\n\n  // Função para limpar o HTML ao colar conteúdo\n  private handlePaste(event: ClipboardEvent) {\n    event.preventDefault(); // Bloqueia a colagem padrão\n    event.stopPropagation(); // Evita que afete outros elementos\n\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const plainText = clipboardData.getData('text/plain'); // Obtém apenas texto puro\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    if (parentElement.classList.contains('line')) {\n      parentElement.remove();\n    }\n\n    range.deleteContents(); // Remove qualquer seleção existente\n\n    // Converte cada linha do texto colado em <p class=\"line\">\n    const fragment = document.createDocumentFragment();\n    plainText.split('\\n').forEach((line) => {\n      if (line.trim()) {\n        const p = document.createElement('p');\n        p.classList.add('line');\n        p.textContent = line.trim();\n        fragment.appendChild(p);\n      }\n    });\n\n    // Insere o conteúdo processado no local do cursor\n    range.insertNode(fragment);\n\n    // Ajusta o cursor para o final do texto inserido\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  // Função para restaurar a formatação do texto selecionado\n  private clearFormatting(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Remove a formatação de cada linha\n    selectedLines.forEach((line) => {\n      line.innerHTML = line.textContent; // Remove todas as tags HTML\n      line.style.textAlign = ''; // Remove o alinhamento\n    });\n\n    this.wrapSelectionLine('p', true);\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`rich-text`]: true,\n          [`rich-text-${this.positionBar}`]: true,\n        }}\n        style={{ height: this.height, maxHeight: this.maxHeight }}\n        tabindex=\"0\"\n        onMouseEnter={() => (this.insideComponent = true)}\n        onMouseLeave={() => (this.insideComponent = false)}\n      >\n        <div class=\"preview\">\n          <div\n            data-test={this.dataTest}\n            ref={(el) => this.refeditorElement(el)}\n            contentEditable=\"true\"\n            class=\"editor-uai-design-system\"\n            tabindex=\"0\"\n            onBlur={this.onBlur}\n            onFocus={this.onFocus}\n            onMouseUp={() => this.updateToolbarState()}\n            onKeyUp={() => this.updateToolbarState()}\n            onKeyDown={(ev) => this.onKeydown(ev)}\n            onInput={(ev) => this.onInput(ev)}\n            onPaste={this.handlePaste.bind(this)}\n          ></div>\n        </div>\n\n        <bds-grid\n          class={{\n            [`format-buttons`]: true,\n            [`format-buttons-active`]:\n              this.weightButton ||\n              this.italicButton ||\n              this.strikeThroughButton ||\n              this.underlineButton ||\n              this.linkButton ||\n              this.codeButton ||\n              this.alignmentButtons ||\n              this.listButtons ||\n              this.quoteButton ||\n              this.headingButtons ||\n              this.unstyledButton,\n          }}\n        >\n          <div class=\"accordion-header\">\n            <bds-grid ref={(el) => this.refButtonsListElement(el)} class=\"buttons-list\" flex-wrap=\"wrap\">\n              <div onFocus={(ev) => this.onFocusEditorBar(ev)} tabindex=\"1\" class=\"editor-bar\"></div>\n              {this.weightButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'bold')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomBoldActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'b')}\n                    icon-left=\"text-style-bold\"\n                    aria-label={`${termTranslate(this.language, 'bold')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.italicButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'italic')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomItalicActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'i')}\n                    icon-left=\"text-style-italic\"\n                    aria-label={`${termTranslate(this.language, 'italic')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.strikeThroughButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'strike')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomStrikeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'strike')}\n                    icon-left=\"text-style-strikethrough\"\n                    aria-label={`${termTranslate(this.language, 'strike')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.underlineButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'underline')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnderlineActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'u')}\n                    icon-left=\"text-style-underline\"\n                    aria-label={`${termTranslate(this.language, 'underline')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.linkButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'link')}`} position=\"top-center\">\n                  {this.buttomLinkActive ? (\n                    <bds-button\n                      variant=\"solid\"\n                      color=\"content\"\n                      size=\"short\"\n                      onBdsClick={(ev) => this.wrapSelection(ev, 'a')}\n                      icon-left=\"link\"\n                      aria-label={`${termTranslate(this.language, 'link')}`}\n                    ></bds-button>\n                  ) : (\n                    <bds-dropdown\n                      ref={(el) => this.refDropDownLinkElement(el)}\n                      activeMode=\"click\"\n                      position=\"bottom-left\"\n                    >\n                      <div slot=\"dropdown-activator\">\n                        <bds-button\n                          slot=\"dropdown-activator\"\n                          variant=\"text\"\n                          color=\"content\"\n                          size=\"short\"\n                          onBdsClick={(ev) => this.addSelectionLink(ev)}\n                          icon-left=\"link\"\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </div>\n                      <bds-grid padding=\"half\" alignItems=\"center\" gap=\"half\" slot=\"dropdown-content\">\n                        <bds-input\n                          ref={this.refInputSetLink}\n                          onBdsInput={(ev) => this.addLinkInput(ev.detail)}\n                          style={{ flexShrink: '99999' }}\n                          placeholder=\"adcione o link aqui\"\n                          onKeyDown={(ev) => this.createLinkKeyDown(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                        ></bds-input>\n                        <bds-button\n                          disabled={this.buttomLinkValidDisabled}\n                          icon-left=\"check\"\n                          onBdsClick={(ev) => this.createLink(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </bds-grid>\n                    </bds-dropdown>\n                  )}\n                </bds-tooltip>\n              )}\n              {this.codeButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'code')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomCodeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'code')}\n                    icon-left=\"code\"\n                    aria-label={`${termTranslate(this.language, 'code')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_left')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignLeftActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'left')}\n                    icon-left=\"align-left\"\n                    aria-label={`${termTranslate(this.language, 'align_left')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_center')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignCenterActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'center')}\n                    icon-left=\"align-center\"\n                    aria-label={`${termTranslate(this.language, 'align_center')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_right')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignRightActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'right')}\n                    icon-left=\"align-right\"\n                    aria-label={`${termTranslate(this.language, 'align_right')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'unordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnorderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ul')}\n                    icon-left=\"unordered-list\"\n                    aria-label={`${termTranslate(this.language, 'unordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'ordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomOrderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ol')}\n                    icon-left=\"ordered-list\"\n                    aria-label={`${termTranslate(this.language, 'ordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.quoteButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'quote')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomQuoteActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'blockquote')}\n                    icon-left=\"quote\"\n                    aria-label={`${termTranslate(this.language, 'quote')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h1')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH1Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h1')}\n                    icon-left=\"h-1\"\n                    aria-label={`${termTranslate(this.language, 'h1')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h2')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH2Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h2')}\n                    icon-left=\"h-2\"\n                    aria-label={`${termTranslate(this.language, 'h2')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h3')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH3Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h3')}\n                    icon-left=\"h-3\"\n                    aria-label={`${termTranslate(this.language, 'h3')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h4')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH4Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h4')}\n                    icon-left=\"h-4\"\n                    aria-label={`${termTranslate(this.language, 'h4')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h5')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH5Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h5')}\n                    icon-left=\"h-5\"\n                    aria-label={`${termTranslate(this.language, 'h5')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h6')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH6Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h6')}\n                    icon-left=\"h-6\"\n                    aria-label={`${termTranslate(this.language, 'h6')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.unstyledButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'clear_formatting')}`} position=\"top-center\">\n                  <bds-button\n                    variant=\"text\"\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.clearFormatting(ev)}\n                    icon-left=\"unstyled\"\n                    aria-label={`${termTranslate(this.language, 'clear_formatting')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n            </bds-grid>\n            <bds-button\n              id=\"buttonAccordion\"\n              variant={this.buttomAccordionActive ? 'solid' : 'text'}\n              class=\"arrow-down\"\n              color=\"content\"\n              size=\"short\"\n              onBdsClick={() => this.setheaderHeight()}\n              icon-left={\n                this.positionBar == 'top'\n                  ? this.buttomAccordionActive\n                    ? 'arrow-up'\n                    : 'arrow-down'\n                  : this.buttomAccordionActive\n                    ? 'arrow-down'\n                    : 'arrow-up'\n              }\n            ></bds-button>\n          </div>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM,OAAO,GAAG;AACrB,IAAA;AACE,QAAA,IAAI,EAAE,SAAS;AACf,QAAA,MAAM,EAAE,SAAS;AACjB,QAAA,MAAM,EAAE,SAAS;AACjB,QAAA,SAAS,EAAE,YAAY;AACvB,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,UAAU,EAAE,oBAAoB;AAChC,QAAA,YAAY,EAAE,mBAAmB;AACjC,QAAA,WAAW,EAAE,mBAAmB;AAChC,QAAA,cAAc,EAAE,oBAAoB;AACpC,QAAA,YAAY,EAAE,gBAAgB;AAC9B,QAAA,KAAK,EAAE,SAAS;AAChB,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,gBAAgB,EAAE,mBAAmB;AACrC,QAAA,MAAM,EAAE,UAAU;AACnB,KAAA;CACF;;ACvBM,MAAM,OAAO,GAAG;AACrB,IAAA;AACE,QAAA,IAAI,EAAE,SAAS;AACf,QAAA,MAAM,EAAE,SAAS;AACjB,QAAA,MAAM,EAAE,SAAS;AACjB,QAAA,SAAS,EAAE,WAAW;AACtB,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,UAAU,EAAE,wBAAwB;AACpC,QAAA,YAAY,EAAE,mBAAmB;AACjC,QAAA,WAAW,EAAE,sBAAsB;AACnC,QAAA,cAAc,EAAE,mBAAmB;AACnC,QAAA,YAAY,EAAE,gBAAgB;AAC9B,QAAA,KAAK,EAAE,MAAM;AACb,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,EAAE,EAAE,UAAU;AACd,QAAA,gBAAgB,EAAE,iBAAiB;AACnC,QAAA,MAAM,EAAE,UAAU;AACnB,KAAA;CACF;;ACvBM,MAAM,OAAO,GAAG;AACrB,IAAA;AACE,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,MAAM,EAAE,QAAQ;AAChB,QAAA,MAAM,EAAE,eAAe;AACvB,QAAA,SAAS,EAAE,WAAW;AACtB,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,UAAU,EAAE,YAAY;AACxB,QAAA,YAAY,EAAE,cAAc;AAC5B,QAAA,WAAW,EAAE,aAAa;AAC1B,QAAA,cAAc,EAAE,gBAAgB;AAChC,QAAA,YAAY,EAAE,cAAc;AAC5B,QAAA,KAAK,EAAE,OAAO;AACd,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,EAAE,EAAE,WAAW;AACf,QAAA,gBAAgB,EAAE,kBAAkB;AACpC,QAAA,MAAM,EAAE,QAAQ;AACjB,KAAA;CACF;;ACjBM,MAAM,aAAa,GAAG,CAAC,IAAe,EAAE,MAAc,KAAY;AACvE,IAAA,IAAI,SAAS;IACb,QAAQ,IAAI;AACV,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C;AACF,QAAA;AACE,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;;AAEnD,IAAA,OAAO,SAAS;AAClB,CAAC;;ACtBD,MAAM,WAAW,GAAG,6nLAA6nL;;MCYpoL,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;AAMU,QAAA,IAAkB,CAAA,kBAAA,GAAiB,IAAI;AACvC,QAAA,IAAmB,CAAA,mBAAA,GAA6C,IAAI;AACpE,QAAA,IAAM,CAAA,MAAA,GAAiB,IAAI;AAC3B,QAAA,IAAY,CAAA,YAAA,GAA4B,IAAI;AAI3C,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAClC,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;AACpC,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;AACpC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;AACvC,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAClC,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAClC,QAAA,IAAuB,CAAA,uBAAA,GAAa,IAAI;AACxC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;AACvC,QAAA,IAAuB,CAAA,uBAAA,GAAa,KAAK;AACzC,QAAA,IAAsB,CAAA,sBAAA,GAAa,KAAK;AACxC,QAAA,IAAyB,CAAA,yBAAA,GAAa,KAAK;AAC3C,QAAA,IAAuB,CAAA,uBAAA,GAAa,KAAK;AACzC,QAAA,IAAiB,CAAA,iBAAA,GAAa,KAAK;AACnC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAqB,CAAA,qBAAA,GAAa,KAAK;AACvC,QAAA,IAAY,CAAA,YAAA,GAAY,MAAM;AAC9B,QAAA,IAAiB,CAAA,iBAAA,GAAa,KAAK;AACnC,QAAA,IAAiB,CAAA,iBAAA,GAAgC,IAAI;AACrD,QAAA,IAAkB,CAAA,kBAAA,GAAmB,IAAI;AACzC,QAAA,IAAiB,CAAA,iBAAA,GAAY,IAAI;AACjC,QAAA,IAAY,CAAA,YAAA,GAAY,OAAO;AAC/B,QAAA,IAAiB,CAAA,iBAAA,GAAW,IAAI;AAChC,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AAC/B,QAAA,IAAe,CAAA,eAAA,GAAa,KAAK;AAC1C;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAe,OAAO;AACtC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAa,IAAI;AACrC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAa,IAAI;AACrC;;AAEG;AACK,QAAA,IAAmB,CAAA,mBAAA,GAAa,IAAI;AAC5C;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAa,IAAI;AACxC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,IAAI;AACnC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,IAAI;AACnC;;AAEG;AACK,QAAA,IAAgB,CAAA,gBAAA,GAAa,IAAI;AACzC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;AACpC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;AACpC;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAa,IAAI;AACvC;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAa,IAAI;AACvC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAY,IAAI;AAC9B;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AACjC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAiB,KAAK;AAEzC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAoIxB,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,EAAe,KAAU;AACxD,YAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;AAC9B,SAAC;AACO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;AACnD,YAAA,IAAI,CAAC,MAAM,GAAG,EAAE;AAClB,SAAC;AACO,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,EAA0B,KAAU;AACpE,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE;AACxB,SAAC;AAEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,EAAuB,KAAU;AAC1D,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE;AACxB,SAAC;AAEO,QAAA,IAAY,CAAA,YAAA,GAAG,MAAK;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;AAClC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK;AAClC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;AACpC,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;AACnC,YAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK;AACtC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;AACpC,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC7B,SAAC;AAEO,QAAA,IAAe,CAAA,eAAA,GAAG,MAAK;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,qBAAqB;AACxD,YAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,YAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;gBAAE;YAC9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;;YAErC,SAAS,CAAC,eAAe,EAAE;AAC3B,YAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC3B,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAK;YACpB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;AAClC,YAAA,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;gBAClC,IAAI,CAAC,YAAY,EAAE;;AAErB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACrB,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAK;YACrB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,SAAC;AAmCO,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,KAAoB,KAAI;AAC3C,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;AAC7B,gBAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,gBAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;oBAAE;gBAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc;;AAGtC,gBAAA,IAAI,YAAY,GAAG,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,GAAI,SAAyB;AAE/G,gBAAA,OAAO,YAAY,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;AAC/F,oBAAA,YAAY,GAAG,YAAY,CAAC,aAAa;;;AAI3C,gBAAA,IACE,YAAY;oBACZ,YAAY,CAAC,OAAO,KAAK,YAAY;AACrC,oBAAA,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;AACvC,oBAAA,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAClC;AACA,oBAAA,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,YAAY,CAAC,MAAM,EAAE,CAAC;;;AAG1B,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;gBACrE,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,0BAA0B;gBAClD,IAAI,CAAC,cAAc,EAAE;;AAEvB,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AACzD,gBAAA,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,gBAAA,KAAK,CAAC,eAAe,EAAE,CAAC;;AAE5B,SAAC;AA8yBF;IA1hCC,gBAAgB,GAAA;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,0BAA0B;;QAEpD,IACE,IAAI,CAAC,YAAY;AACjB,YAAA,IAAI,CAAC,YAAY;AACjB,YAAA,IAAI,CAAC,mBAAmB;AACxB,YAAA,IAAI,CAAC,eAAe;AACpB,YAAA,IAAI,CAAC,UAAU;AACf,YAAA,IAAI,CAAC,UAAU;AACf,YAAA,IAAI,CAAC,gBAAgB;AACrB,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,cAAc,EACnB;YACA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CACrE,aAAa,CAC6B;AAC5C,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,iBAAA,CAAmB;;aACvD;YACL,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,IAAA,CAAM;;;IAezC,oBAAoB,GAAA;AAC5B,QAAA,UAAU,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;;IAI/D,4BAA4B,GAAA;AACpC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC;;IAG1C,kBAAkB,GAAA;AACxB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;QACvC,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;AACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;QAC7G,IAAI,CAAC,kBAAkB,GAAG,eAAe,CAAC,aAAa,EAAE,2BAA2B,CAAC;;;AAI/E,IAAA,eAAe,CAAC,KAAc,EAAA;QACpC,MAAM,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,EAAE;AACxE,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAyB;AACzF,QAAA,IAAI,gBAAgB,GAAG,2BAA2B,EAAE;AAClD,YAAA,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;aAClC;AACL,YAAA,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAE5C,QAAA,MAAM,UAAU,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,2BAA2B;QACrG,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,2BAA2B,GAAG,gBAAgB,CAAC;QACjF,MAAM,sBAAsB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;AACnE,QAAA,sBAAsB,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AAC1E,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjC,SAAC,CAAC;QACF,IAAI,KAAK,EAAE;AACT,YAAA,sBAAsB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACzC,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC/B,gBAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,YAAA,EAAe,eAAe,GAAG,EAAE,GAAG,EAAE,KAAK;AACxF,aAAC,CAAC;;aACG;AACL,YAAA,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACvE,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,iBAAA,CAAmB;AAC9D,aAAC,CAAC;;;AAKI,IAAA,yBAAyB,CAAC,KAAK,EAAA;AACvC,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,WAAW,EAAE,CAAC;AACtE,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;AACpD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,aAAF,EAAE,KAAA,MAAA,GAAA,MAAA,GAAF,EAAE,CAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC;AAC1C,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;AAC5C,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,QAAQ,CAAC;AACjD,QAAA,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,GAAG,CAAC;AAC/C,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC;AAC1C,QAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC;AAC7C,QAAA,IAAI,CAAC,qBAAqB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,MAAM;AAChE,QAAA,IAAI,CAAC,uBAAuB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,QAAQ;AACpE,QAAA,IAAI,CAAC,sBAAsB,GAAG,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,KAAK,CAAC,SAAS,MAAK,OAAO;QAClE,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;QACpD,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;AAClD,QAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,YAAY,CAAC;AACpD,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC;;;AA8DnC,IAAA,OAAO,CAAC,EAAc,EAAA;QAC5B,EAAE,CAAC,cAAc,EAAE;AACnB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;AAE9B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;AAE7D,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc;;AAGxC,QAAA,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAK,WAA2B,CAAC,OAAO,KAAK,KAAK,EAAE;YAChG,MAAM,UAAU,GAAG,WAA0B;YAE7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AAC5C,YAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AAC9B,YAAA,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS;YAEzC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;;;AAI1D,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;YAClD,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AACrC,YAAA,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AACvB,YAAA,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS;AAC3B,YAAA,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;AACpB,SAAC,CAAC;;;AAyCI,IAAA,gBAAgB,CAAC,EAAc,EAAA;AACrC,QAAA,MAAM,SAAS,GAAG,EAAE,CAAC,MAAqB;QAC1C,MAAM,UAAU,GAAG,SAAS,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,CAAC;QAC3E,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAgB;QACnF,cAAc,CAAC,KAAK,EAAE;AACtB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;;;IAI3B,cAAc,GAAA;AACpB,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE;AACpC,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE;AACjC,QAAA,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;AACrC,QAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrB,GAAG,CAAC,eAAe,EAAE;AACrB,QAAA,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;;IAGb,OAAO,CAAC,GAAW,EAAE,OAAsB,EAAA;AACjD,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,WAAW,EAAE,CAAC;AACtE,QAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;;AAGpB,IAAA,aAAa,CAAC,EAAe,EAAE,GAAW,EAAE,IAAa,EAAA;AAC/D,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,MAAM,CAAC,cAAc,EAAE;YACvB,MAAM,CAAC,eAAe,EAAE;;AAG1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE;QAEjD,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;AACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;QAE7G,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,EAAE,OAAO,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;;AAG/C,QAAA,IAAI,OAAyB;QAC7B,IAAI,eAAe,GAAG,KAAK;;QAG3B,IAAI,YAAY,EAAE;YAChB,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC;YACzE,IAAI,UAAU,EAAE;AACd,gBAAA,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa;AACvC,gBAAA,MAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE;gBACjF,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,WAAW,CAAC,MAAM;AAEtE,gBAAA,IAAI,eAAe,IAAI,MAAM,EAAE;;AAE7B,oBAAA,OAAO,UAAU,CAAC,UAAU,EAAE;wBAC5B,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC;;AAExD,oBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;oBAC9B,SAAS,CAAC,eAAe,EAAE;AAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACzB,IAAI,CAAC,kBAAkB,EAAE;;qBACpB,IAAI,YAAY,EAAE;;AAEvB,oBAAA,OAAO,GAAG,QAAQ,CAAC,sBAAsB,EAAE;oBAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACtD,oBAAA,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;oBAChC,eAAe,GAAG,IAAI;AACtB,oBAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;AACvC,oBAAA,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACnC,oBAAA,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC;AAChC,oBAAA,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;oBAC5B,SAAS,CAAC,eAAe,EAAE;AAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC5B,IAAI,CAAC,kBAAkB,EAAE;;qBACpB;;oBAEL,SAAS,CAAC,eAAe,EAAE;AAC3B,oBAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACzB,IAAI,CAAC,kBAAkB,EAAE;;;YAG7B;;AAGF,QAAA,IAAI,KAAK,CAAC,SAAS,EAAE;AACnB,YAAA,OAAO,GAAG,QAAQ,CAAC,sBAAsB,EAAE;YAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AACtD,YAAA,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;YAChC,eAAe,GAAG,IAAI;;aACjB;AACL,YAAA,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE;;;QAInC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AAChD,YAAA,OAAO,OAAO,CAAC,UAAU,EAAE;gBACzB,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;;YAE9D,OAAO,CAAC,MAAM,EAAE;AAClB,SAAC,CAAC;;QAGF,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AAC3C,QAAA,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,EAAE;AACvB,YAAA,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;AAEpC,QAAA,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC;AAC5B,QAAA,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;;AAGzB,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AAC/C,YAAA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtD,EAAE,CAAC,MAAM,EAAE;;AAEf,SAAC,CAAC;;AAGF,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;QACvC,IAAI,eAAe,EAAE;AACnB,YAAA,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7B,YAAA,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;;aACtB;YACL,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC;YACtD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;;QAEpD,SAAS,CAAC,eAAe,EAAE;AAE3B,QAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAE5B,IAAI,CAAC,kBAAkB,EAAE;;AAGzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;AAGvD,IAAA,iBAAiB,CAAC,GAAW,EAAE,iBAAA,GAA6B,KAAK,EAAA;;AACvE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE;QAEjD,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAA6B;AACrD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAA2B;;AAGjD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe;AAE5C,QAAA,IAAI,WAAW,GAAG,SAAS,CAAC,aAAa;QACzC,OAAO,WAAW,IAAI,WAAW,KAAK,OAAO,CAAC,aAAa,EAAE;AAC3D,YAAA,IAAI,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,aAAa,GAAI,WAA2B;YAChH,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACjD,gBAAA,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;;AAE5B,YAAA,WAAW,GAAI,WAAW,CAAC,WAA2B,KAAK,CAAA,EAAA,GAAA,WAAW,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAA2B,CAAA;;;AAInH,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,GAAI,OAAuB;AACvG,QAAA,OAAO,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE;AACzF,YAAA,UAAU,GAAG,UAAU,CAAC,aAAa;;QAEvC,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACvD,YAAA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC;;;AAI/B,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAClD,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,GAAG,CAC1D;AAED,QAAA,MAAM,cAAc,GAAkB,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAI;AAClE,YAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;AACpE,YAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AAChC,YAAA,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS;AACnC,YAAA,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC;AAC1B,YAAA,OAAO,UAAU;AACnB,SAAC,CAAC;QAEF,IAAI,iBAAiB,EAAE;AACrB,YAAA,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;;;AAIzE,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;QACvC,IAAI,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;;QAG1C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACtC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC;;;QAIzC,OAAO,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE;AACvD,YAAA,QAAQ,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ;;;AAI3C,QAAA,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,KAAI,CAAC,CAAC;AAC9D,QAAA,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,KAAI,CAAC,CAAC;QAC5D,SAAS,CAAC,eAAe,EAAE;AAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAE5B,IAAI,CAAC,kBAAkB,EAAE;AAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;;IAIvD,SAAS,CAAC,EAAe,EAAE,SAAkD,EAAA;AACnF,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,MAAM,CAAC,cAAc,EAAE;YACvB,MAAM,CAAC,eAAe,EAAE;;AAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,IAAI,YAAY,GAAG,KAAK,CAAC,cAA6B;;QAGtD,OAAO,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;AACnD,YAAA,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC1F;;AAEF,YAAA,YAAY,GAAG,YAAY,CAAC,aAAa;;;QAI3C,IAAI,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;;AAEhD,YAAA,MAAM,gBAAgB,GAAI,YAA4B,CAAC,KAAK,CAAC,SAAS;AACtE,YAAA,IAAI,gBAAgB,KAAK,SAAS,EAAE;;AAEjC,gBAAA,YAA4B,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE;;iBAC7C;;AAEJ,gBAAA,YAA4B,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS;;;;QAK7D,SAAS,CAAC,eAAe,EAAE;AAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;QAEzB,IAAI,CAAC,kBAAkB,EAAE;AAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;;;IAIvD,aAAa,CAAC,EAAe,EAAE,IAA4D,EAAA;;AACjG,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,MAAM,CAAC,cAAc,EAAE;YACvB,MAAM,CAAC,eAAe,EAAE;;AAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE;AACjD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;AAClC,QAAA,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,OAAO;AACxD,QAAA,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,sBAAsB;AACtE,QAAA,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,kBAAkB;AACjE,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa;QAC1C,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;gBACtC,IAAI,WAAW,EAAE;oBACf,WAAW,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC;;qBACtD,IAAI,UAAU,EAAE;oBACrB,UAAU,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC;;qBACxD;oBACL,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;;AAEjE,aAAC,CAAC;AACF,YAAA,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC7D,MAAM,CAAC,MAAM,EAAE;;;;;IAMb,UAAU,CAAC,EAAe,EAAE,IAAiB,EAAA;;AACnD,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,MAAM,CAAC,cAAc,EAAE;YACvB,MAAM,CAAC,eAAe,EAAE;;AAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;YAAE;AACjD,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO;AACvD,QAAA,MAAM,YAAY,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,OAAO;QACvF,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;AAC5C,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa;QAE1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE;AACjD,YAAA,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC;YAC3C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACtC,gBAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AACnC,aAAC,CAAC;;aACG;YACL,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;YAEjF,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBACtD,IAAI,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE;AACzC,oBAAA,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;oBACpC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;;qBAC1C;oBACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;wBACtC,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG;wBAChF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AAClD,wBAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;wBAC7C,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;4BACvD,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC;;6BAC7D;4BACL,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC;;AAE7E,wBAAA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,qBAAC,CAAC;oBACF,MAAM,CAAC,MAAM,EAAE;;;iBAEZ;;gBAEL,aAAa,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,WAAW,EAAE,OAAO,CAAC;gBAChF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACtC,oBAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AACnC,iBAAC,CAAC;;;;AAKA,IAAA,gBAAgB,CAAC,EAAe,EAAA;AACtC,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;AAC7D,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;;AAE7B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AACnB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;QACvC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAChD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,yBAAyB,CAAqB;QAChH,cAAc,CAAC,KAAK,EAAE;;AAGhB,IAAA,YAAY,CAAC,EAAc,EAAA;QACjC,EAAE,CAAC,cAAc,EAAE;AACnB,QAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;AAClD,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,KAAK;QAClC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;;aAC/B;AACL,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI;;;AAI/B,IAAA,iBAAiB,CAAC,EAAiB,EAAA;AACzC,QAAA,IAAI,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;;;AAIf,IAAA,UAAU,CAAC,EAAE,EAAA;QACnB,EAAE,CAAC,cAAc,EAAE;AACnB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;QACvC,SAAS,CAAC,eAAe,EAAE;AAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC;AACjD,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;;IAIxB,UAAU,CAAC,SAAsB,EAAE,QAAqB,EAAA;QAC9D,MAAM,cAAc,GAClB,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI;QAClH,MAAM,aAAa,GACjB,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI;QAChH,OAAO,cAAc,IAAI,aAAa;;;AAIhC,IAAA,WAAW,CAAC,KAAqB,EAAA;AACvC,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,QAAA,KAAK,CAAC,eAAe,EAAE,CAAC;QAExB,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,IAAK,MAAc,CAAC,aAAa;QAC1E,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEtD,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,uBAAuB;AACpD,QAAA,MAAM,aAAa,GACjB,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,aAAa,GAAI,cAA8B;QAC7G,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC5C,aAAa,CAAC,MAAM,EAAE;;AAGxB,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;;AAGvB,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE;QAClD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;gBACf,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AACrC,gBAAA,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;AACvB,gBAAA,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE;AAC3B,gBAAA,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;AAE3B,SAAC,CAAC;;AAGF,QAAA,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;;QAG1B,SAAS,CAAC,eAAe,EAAE;AAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;;;AAInB,IAAA,eAAe,CAAC,EAAe,EAAA;;AACrC,QAAA,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;QACxB,IAAI,MAAM,YAAY,aAAa,IAAI,MAAM,CAAC,GAAG,KAAK,OAAO,EAAE;YAC7D,MAAM,CAAC,cAAc,EAAE;YACvB,MAAM,CAAC,eAAe,EAAE;;AAE1B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE;AACvC,QAAA,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC;YAAE;QAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,cAA6B;AACrD,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,YAA2B;;AAGjD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe;AAE5C,QAAA,IAAI,WAAW,GAAG,SAAS,CAAC,aAAa;QACzC,OAAO,WAAW,IAAI,WAAW,KAAK,OAAO,CAAC,aAAa,EAAE;AAC3D,YAAA,IAAI,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,aAAa,GAAI,WAA2B;YAChH,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACjD,gBAAA,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;;AAE5B,YAAA,WAAW,GAAI,WAAW,CAAC,WAA2B,KAAK,CAAA,EAAA,GAAA,WAAW,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAA2B,CAAA;;;AAInH,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,GAAI,OAAuB;AACvG,QAAA,OAAO,UAAU,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,EAAE;AACzF,YAAA,UAAU,GAAG,UAAU,CAAC,aAAa;;QAEvC,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACvD,YAAA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC;;;AAI/B,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC;;QAGjC,SAAS,CAAC,eAAe,EAAE;AAC3B,QAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;;IAG3B,MAAM,GAAA;;QACJ,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;gBACL,CAAC,CAAA,SAAA,CAAW,GAAG,IAAI;AACnB,gBAAA,CAAC,aAAa,IAAI,CAAC,WAAW,CAAE,CAAA,GAAG,IAAI;AACxC,aAAA,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EACzD,QAAQ,EAAC,GAAG,EACZ,YAAY,EAAE,OAAO,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,EACjD,YAAY,EAAE,OAAO,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,EAAA,EAElD,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,SAAS,EAAA,EAClB,CACa,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EACtC,eAAe,EAAC,MAAM,EACtB,KAAK,EAAC,0BAA0B,EAChC,QAAQ,EAAC,GAAG,EACZ,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,SAAS,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAC1C,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EACxC,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EACrC,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EACjC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAC/B,CACH,EAEN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;gBACL,CAAC,CAAA,cAAA,CAAgB,GAAG,IAAI;AACxB,gBAAA,CAAC,CAAuB,qBAAA,CAAA,GACtB,IAAI,CAAC,YAAY;AACjB,oBAAA,IAAI,CAAC,YAAY;AACjB,oBAAA,IAAI,CAAC,mBAAmB;AACxB,oBAAA,IAAI,CAAC,eAAe;AACpB,oBAAA,IAAI,CAAC,UAAU;AACf,oBAAA,IAAI,CAAC,UAAU;AACf,oBAAA,IAAI,CAAC,gBAAgB;AACrB,oBAAA,IAAI,CAAC,WAAW;AAChB,oBAAA,IAAI,CAAC,WAAW;AAChB,oBAAA,IAAI,CAAC,cAAc;AACnB,oBAAA,IAAI,CAAC,cAAc;AACtB,aAAA,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC3B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAC,cAAc,eAAW,MAAM,EAAA,EAC1F,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,EAAC,KAAK,EAAC,YAAY,EAAO,CAAA,EACtF,IAAI,CAAC,YAAY,KAChB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACzF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,GAAG,OAAO,GAAG,MAAM,EACjD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,iBAAiB,EAAA,YAAA,EACf,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EACzC,CAAA,CACF,CACf,EACA,IAAI,CAAC,YAAY,KAChB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC3F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,kBAAkB,GAAG,OAAO,GAAG,MAAM,EACnD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,mBAAmB,EAAA,YAAA,EACjB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAC3C,CAAA,CACF,CACf,EACA,IAAI,CAAC,mBAAmB,KACvB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC3F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,kBAAkB,GAAG,OAAO,GAAG,MAAM,EACnD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAA,WAAA,EAC1C,0BAA0B,EAAA,YAAA,EACxB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAC3C,CAAA,CACF,CACf,EACA,IAAI,CAAC,eAAe,KACnB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC9F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,sBAAsB,EAAA,YAAA,EACpB,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,EAC9C,CAAA,CACF,CACf,EACA,IAAI,CAAC,UAAU,KACd,oFAA2B,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,IACxF,IAAI,CAAC,gBAAgB,IACpB,CAAA,CAAA,YAAA,EAAA,EACE,OAAO,EAAC,OAAO,EACf,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,EAAA,WAAA,EACrC,MAAM,EACJ,YAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAE,CAAA,EACzC,CAAA,KAEd,CAAA,CAAA,cAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAC5C,UAAU,EAAC,OAAO,EAClB,QAAQ,EAAC,aAAa,EAAA,EAEtB,CAAK,CAAA,KAAA,EAAA,EAAA,IAAI,EAAC,oBAAoB,EAAA,EAC5B,CAAA,CAAA,YAAA,EAAA,EACE,IAAI,EAAC,oBAAoB,EACzB,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAA,WAAA,EACnC,MAAM,EACJ,YAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,GACzC,CACV,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,MAAM,EAAC,UAAU,EAAC,QAAQ,EAAC,GAAG,EAAC,MAAM,EAAC,IAAI,EAAC,kBAAkB,EAAA,EAC7E,CAAA,CAAA,WAAA,EAAA,EACE,GAAG,EAAE,IAAI,CAAC,eAAe,EACzB,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,EAChD,KAAK,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,EAC9B,WAAW,EAAC,qBAAqB,EACjC,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAC7C,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,IAAG,GAAG,GAAG,IAAI,EACnC,CAAA,EACb,CACE,CAAA,YAAA,EAAA,EAAA,QAAQ,EAAE,IAAI,CAAC,uBAAuB,eAC5B,OAAO,EACjB,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EACvC,QAAQ,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,IAAI,IAAG,GAAG,GAAG,IAAI,EAAA,YAAA,EAClC,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAA,CACzC,CACL,CACE,CAChB,CACW,CACf,EACA,IAAI,CAAC,UAAU,KACd,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACzF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,gBAAgB,GAAG,OAAO,GAAG,MAAM,EACjD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,CAAC,EAAA,WAAA,EACxC,MAAM,EAAA,YAAA,EACJ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAE,CAAA,EACzC,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC/F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,EAAA,WAAA,EACpC,YAAY,EAAA,YAAA,EACV,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAE,CAAA,EAC/C,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACjG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,OAAO,GAAG,MAAM,EACxD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAA,WAAA,EACtC,cAAc,EAAA,YAAA,EACZ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EACjD,CAAA,CACF,CACf,EACA,IAAI,CAAC,gBAAgB,KACpB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAChG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,sBAAsB,GAAG,OAAO,GAAG,MAAM,EACvD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAA,WAAA,EACrC,aAAa,EAAA,YAAA,EACX,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA,CAAE,EAChD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACnG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,yBAAyB,GAAG,OAAO,GAAG,MAAM,EAC1D,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACnC,gBAAgB,EAAA,YAAA,EACd,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAE,CAAA,EACnD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACjG,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,OAAO,GAAG,MAAM,EACxD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACnC,cAAc,EAAA,YAAA,EACZ,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAE,CAAA,EACjD,CAAA,CACF,CACf,EACA,IAAI,CAAC,WAAW,KACf,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EAC1F,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,iBAAiB,GAAG,OAAO,GAAG,MAAM,EAClD,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,YAAY,CAAC,EAAA,WAAA,EAC9C,OAAO,EAAA,YAAA,EACL,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA,CAAE,EAC1C,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA,CAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACvF,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,MAAM,EAC/C,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,WAAA,EACtC,KAAK,EAAA,YAAA,EACH,CAAA,EAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAE,CAAA,EACvC,CAAA,CACF,CACf,EACA,IAAI,CAAC,cAAc,KAClB,CAA2B,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,cAAA,EAAA,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,EACrG,CAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,eAClC,UAAU,EAAA,YAAA,EACR,CAAG,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAE,CAAA,EACrD,CAAA,CACF,CACf,CACQ,EACX,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAE,EAAC,iBAAiB,EACpB,OAAO,EAAE,IAAI,CAAC,qBAAqB,GAAG,OAAO,GAAG,MAAM,EACtD,KAAK,EAAC,YAAY,EAClB,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,UAAU,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,EAAA,WAAA,EAEtC,IAAI,CAAC,WAAW,IAAI;kBAChB,IAAI,CAAC;AACL,sBAAE;AACF,sBAAE;kBACF,IAAI,CAAC;AACL,sBAAE;sBACA,UAAU,EAAA,CAEN,CACV,CACG,CACN;;;;;;;;;;;;;;;;;;;;;;;"}