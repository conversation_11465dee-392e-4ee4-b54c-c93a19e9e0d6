{"version": 3, "names": ["menuCss", "BdsMenu", "constructor", "hostRef", "this", "refElement", "intoView", "menupositionTop", "menupositionLeft", "menu", "position", "open", "refMenuElement", "el", "menuElement", "onClickCloseButtom", "event", "stopPropagation", "componentWillLoad", "document", "getElementById", "getScrollParent", "toggle", "openMenu", "bdsToggle", "emit", "value", "positionValue", "positionElement", "actionElement", "changedElement", "top", "left", "render", "menuPosition", "h", "Host", "key", "ref", "class", "style", "outzone", "onClick", "ev"], "sources": ["src/components/menu/menu.scss?tag=bds-menu&encapsulation=shadow", "src/components/menu/menu.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.menu {\n  position: fixed;\n  pointer-events: none;\n  top: 0;\n  left: 0;\n  padding: 2px;\n  background-color: $color-surface-1;\n  border-radius: 8px;\n  box-shadow: 0px 8px 12px rgba(0, 0, 0, 0.08);\n  width: 240px;\n  opacity: 0;\n  -webkit-transition: opacity 0.5s;\n  -moz-transition: opacity 0.5s;\n  transition: opacity 0.5s;\n  z-index: $zindex-modal;\n\n  &__open {\n    pointer-events: auto;\n    opacity: 1;\n  }\n}\n\n.outzone {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  z-index: $zindex-modal-overlay;\n}\n", "import { Component, Host, ComponentInterface, h, State, Method, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { getScrollParent, positionElement } from '../../utils/position-element';\n\nexport type menuPosition = 'bottom' | 'right';\n\n@Component({\n  tag: 'bds-menu',\n  styleUrl: 'menu.scss',\n  shadow: true,\n})\nexport class BdsMenu implements ComponentInterface {\n  private menuElement?: HTMLElement;\n\n  @State() refElement?: HTMLElement = null;\n  @State() intoView?: HTMLElement = null;\n  @State() menupositionTop?: number = 0;\n  @State() menupositionLeft?: number = 0;\n  /**\n   * Menu. Used to link the minus with the action button.\n   */\n  @Prop() menu?: string = null;\n  /**\n   * Position. Used to position the Menu. Either on the left or on the bottom.\n   */\n  @Prop() position?: menuPosition = 'right';\n  /**\n   * Open. Used to open/close the menu.\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * bdsToggle. Event to return selected date value.\n   */\n  @Event() bdsToggle?: EventEmitter;\n\n  componentWillLoad() {\n    this.refElement = document.getElementById(this.menu);\n    this.intoView = getScrollParent(this.refElement);\n  }\n\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Watch('open')\n  protected openMenu() {\n    this.bdsToggle.emit({ value: this.open });\n    if (this.open) {\n      const positionValue = positionElement({\n        actionElement: this.refElement,\n        changedElement: this.menuElement,\n        intoView: this.intoView,\n      });\n      this.menupositionTop = positionValue.top;\n      this.menupositionLeft = positionValue.left;\n    }\n  }\n\n  private refMenuElement = (el: HTMLElement): void => {\n    this.menuElement = el;\n  };\n\n  private onClickCloseButtom = (event) => {\n    this.open = false;\n    event.stopPropagation();\n  };\n\n  render() {\n    const menuPosition = {\n      top: `${this.menupositionTop}px`,\n      left: `${this.menupositionLeft}px`,\n    };\n\n    return (\n      <Host>\n        <div\n          ref={this.refMenuElement}\n          class={{\n            menu: true,\n            [`menu__${this.position}`]: true,\n            [`menu__open`]: this.open,\n          }}\n          style={menuPosition}\n        >\n          <slot></slot>\n        </div>\n        {this.open && <div class={{ outzone: true }} onClick={(ev) => this.onClickCloseButtom(ev)}></div>}\n      </Host>\n    );\n  }\n}\n"], "mappings": "oGAAA,MAAMA,EAAU,ie,MCUHC,EAAO,MALpB,WAAAC,CAAAC,G,6CAQ<PERSON>,KAAUC,WAAiB,KAC3BD,KAAQE,SAAiB,KACzBF,KAAeG,gBAAY,EAC3BH,KAAgBI,iBAAY,EAI7BJ,KAAIK,KAAY,KAIhBL,KAAQM,SAAkB,QAQ3BN,KAAIO,KAAa,MA+BhBP,KAAAQ,eAAkBC,IACxBT,KAAKU,YAAcD,CAAE,EAGfT,KAAAW,mBAAsBC,IAC5BZ,KAAKO,KAAO,MACZK,EAAMC,iBAAiB,CA0B1B,CAxDC,iBAAAC,GACEd,KAAKC,WAAac,SAASC,eAAehB,KAAKK,MAC/CL,KAAKE,SAAWe,EAAgBjB,KAAKC,W,CAIvC,YAAMiB,GACJlB,KAAKO,MAAQP,KAAKO,I,CAIV,QAAAY,GACRnB,KAAKoB,UAAUC,KAAK,CAAEC,MAAOtB,KAAKO,OAClC,GAAIP,KAAKO,KAAM,CACb,MAAMgB,EAAgBC,EAAgB,CACpCC,cAAezB,KAAKC,WACpByB,eAAgB1B,KAAKU,YACrBR,SAAUF,KAAKE,WAEjBF,KAAKG,gBAAkBoB,EAAcI,IACrC3B,KAAKI,iBAAmBmB,EAAcK,I,EAa1C,MAAAC,GACE,MAAMC,EAAe,CACnBH,IAAK,GAAG3B,KAAKG,oBACbyB,KAAM,GAAG5B,KAAKI,sBAGhB,OACE2B,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,IAAKlC,KAAKQ,eACV2B,MAAO,CACL9B,KAAM,KACN,CAAC,SAASL,KAAKM,YAAa,KAC5B,CAAC,cAAeN,KAAKO,MAEvB6B,MAAON,GAEPC,EAAA,QAAAE,IAAA,8CAEDjC,KAAKO,MAAQwB,EAAA,OAAAE,IAAA,2CAAKE,MAAO,CAAEE,QAAS,MAAQC,QAAUC,GAAOvC,KAAKW,mBAAmB4B,K", "ignoreList": []}