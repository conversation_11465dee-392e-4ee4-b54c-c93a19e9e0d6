{"version": 3, "file": "bds-pagination.entry.esm.js", "sources": ["src/components/pagination/languages.tsx", "src/components/pagination/pagination.scss?tag=bds-pagination&encapsulation=shadow", "src/components/pagination/pagination.tsx"], "sourcesContent": ["export const en_US = \n    {\n        itemsPerPage: 'Items per page',\n        of: 'of',\n        items: 'items',\n        pages: 'pages'\n    }\n;\n\nexport const pt_BR = \n    {\n        itemsPerPage: 'Itens por página',\n        of: 'de',\n        items: 'itens',\n        pages: 'páginas'\n    }\n;\n\nexport const es_MX = \n    {\n        itemsPerPage: 'Itens por página',\n        of: 'de',\n        items: 'itens',\n        pages: 'páginas'\n    }\n;", "@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n\n  .actions {\n    &_select {\n      width: 74px;\n    }\n  }\n}\n\n:host(.full_width) {\n  width: 100%;\n}\n\n@media screen and (max-width: 905px) {\n  .items_per_page {\n    display: none;\n  }\n  .actions {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n@media screen and (max-width: 600px) {\n  .actions {\n    &--text {\n      display: none;\n    }\n  }\n}\n", "import { Component, Host, h, Element, Prop, State, Event, EventEmitter, Watch } from '@stencil/core';\nimport { getScrollParent } from '../../utils/position-element';\nimport { pt_BR, en_US, es_MX } from './languages';\n\nexport type PaginationOptionsPositionType = 'auto' | 'top' | 'bottom';\n@Component({\n  tag: 'bds-pagination',\n  styleUrl: 'pagination.scss',\n  shadow: true,\n})\nexport class Pagination {\n  // Elemento HTML nativo onde o componente será renderizado\n  @Element() private el!: HTMLElement;\n\n  /**\n   * Estado que armazena o valor selecionado no seletor de página.\n   * Inicialmente, é configurado com a página inicial (startedPage).\n   */\n  @State() value: number = this.startedPage;\n\n  // Estado que armazena o valor selecionado no seletor de itens por página\n  @State() itemValue: number;\n\n  /**\n   * Estado que controla se o seletor de opções de página está aberto ou fechado.\n   */\n  @State() openSelect: boolean;\n\n  /**\n   * Estado que armazena o número de páginas, gerado com base no total de itens e itens por página.\n   */\n  @State() paginationNumbers = [];\n\n  // Estado que armazena o número de itens por página selecionado\n  @State() itemsPerPage: number;\n\n  // Estado que guarda o elemento pai com rolagem (se houver)\n  @State() intoView?: HTMLElement = null;\n\n  /**\n   * Propriedade para receber o número total de páginas, baseado no total de itens e itens por página.\n   */\n  @Prop({ mutable: true, reflect: true }) pages?: number;\n\n  /**\n   * Propriedade que define a página inicial ao renderizar o componente.\n   */\n  @Prop() startedPage?: number;\n\n  /**\n   * Define a posição do menu de opções. Pode ser 'bottom' ou 'top'.\n   * Padrão é 'auto', que ajusta automaticamente a posição.\n   */\n  @Prop() optionsPosition?: PaginationOptionsPositionType = 'auto';\n\n  // Propriedade que controla se o contador de páginas será exibido\n  @Prop() pageCounter?: boolean = false;\n\n  // Propriedade para receber as opções de itens por página (por exemplo, [10, 20, 30])\n  @Prop({ mutable: true, reflect: true }) itemsPage?: any;\n\n  // Propriedade que define o número total de itens que serão paginados\n  @Prop() numberItems?: number;\n\n  // Propriedade para definir o idioma do componente (opcional)\n  @Prop() language?: string = 'pt_BR';\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão inicial.\n   * dtButtonInitial é o data-test para o botão inicial.\n   */\n  @Prop() dtButtonInitial?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de página anterior.\n   * dtButtonPrev é o data-test para o botão anterior.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar o seletor de número de páginas.\n   * dtSelectNumber é o data-test para o seletor de número de páginas.\n   */\n  @Prop() dtSelectNumber?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de próxima página.\n   * dtButtonNext é o data-test para o botão próximo.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão final.\n   * dtButtonEnd é o data-test para o botão final.\n   */\n  @Prop() dtButtonEnd?: string = null;\n\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsPaginationChange: EventEmitter;\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsItemsPerPageChange: EventEmitter;\n\n  // Variável que armazena o número do primeiro item sendo exibido na página atual\n  startItem: number;\n\n  // Variável que armazena o número do último item sendo exibido na página atual\n  endItem: number;\n\n  componentWillLoad() {\n    this.countPage();\n    this.intoView = getScrollParent(this.el);\n    this.processItemsPage();\n    if (this.pageCounter) {\n      this.itemValue = this.itemsPage[0];\n    }\n    this.itemSelected(this.itemValue);\n    this.countItem();\n  }\n\n  @Watch('pages')\n  @Watch('startedPage')\n  pagesChanged(): void {\n    this.countPage();\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsPaginationChange.emit(this.value);\n  }\n\n  processItemsPage() {\n    if (typeof this.itemsPage === 'string') {\n      try {\n        this.itemsPage = JSON.parse(this.itemsPage.replace(/'/g, '\"'));\n      } catch (error) {\n        this.itemsPage = [];\n      }\n    }\n  }\n\n  countItem() {\n    if (this.pageCounter) {\n      const pages = this.numberItems / this.itemValue;\n      this.pages = Math.ceil(pages);\n    }\n  }\n\n  countPage() {\n    if (this.paginationNumbers.length !== 0) {\n      this.paginationNumbers = [];\n    }\n    if (this.paginationNumbers.length === 0) {\n      for (let i = 1; i <= this.pages; i++) {\n        this.paginationNumbers.push(i);\n      }\n      if (this.startedPage && this.startedPage < this.pages) {\n        this.value = this.startedPage;\n      } else {\n        this.value = this.paginationNumbers[0];\n      }\n    }\n  }\n\n  nextPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.value + 1;\n      this.updateItemRange();\n    }\n  };\n\n  previewPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.value - 1;\n      this.updateItemRange();\n    }\n  };\n\n  firstPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.paginationNumbers[0];\n      this.updateItemRange();\n    }\n  };\n\n  lastPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.pages;\n      this.updateItemRange();\n    }\n  };\n\n  openOptions = () => {\n    this.openSelect = !this.openSelect;\n  };\n\n  onBlur = () => {\n    this.openSelect = false;\n  };\n\n  optionSelected(index) {\n    this.value = index;\n    this.openOptions();\n    this.updateItemRange();\n  }\n\n  @Watch('itemValue')\n  itemSelected(index) {\n    this.itemValue = index;\n    this.itemsPerPage = index;\n    this.openOptions();\n    this.countItem();\n    this.updateItemRange();\n    this.bdsItemsPerPageChange.emit(this.itemsPerPage);\n  }\n\n  updateItemRange() {\n    this.startItem = (this.value - 1) * this.itemsPerPage + 1;\n    this.endItem = Math.min(this.value * this.itemsPerPage, this.numberItems);\n  }\n\n  get currentLanguage() {\n    switch (this.language) {\n      case 'en_US':\n        return en_US;\n      case 'es_MX':\n        return es_MX;\n      default:\n        return pt_BR;\n    }\n  }\n\n  render() {\n    const { currentLanguage } = this;\n    return (\n      <Host class={{ full_width: this.pageCounter }}>\n        <bds-grid justify-content=\"space-between\">\n          {this.itemsPerPage && this.itemsPage && (\n            <bds-grid gap=\"1\" align-items=\"center\" class=\"items_per_page\">\n              <bds-typo variant=\"fs-14\">{currentLanguage.itemsPerPage}:</bds-typo>\n              <bds-select class=\"actions_select\" value={this.itemValue} options-position={this.optionsPosition}>\n                {this.itemsPage?.map((el, index) => (\n                  <bds-select-option key={index} value={el} onClick={() => this.itemSelected(el)}>\n                    {el}\n                  </bds-select-option>\n                ))}\n              </bds-select>\n              <bds-typo variant=\"fs-14\" no-wrap=\"true\">\n                {this.startItem}-{this.endItem} {currentLanguage.of} {this.numberItems}\n              </bds-typo>\n            </bds-grid>\n          )}\n\n          <bds-grid gap=\"1\" align-items=\"center\" class=\"actions\">\n            <bds-button-icon\n              onBdsClick={(ev) => this.firstPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-first\"\n              dataTest={this.dtButtonInitial}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.previewPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-left\"\n              dataTest={this.dtButtonPrev}\n            ></bds-button-icon>\n\n            <bds-select class=\"actions_select\" value={this.value} options-position={this.optionsPosition}>\n              {this.paginationNumbers.map((el, index) => (\n                <bds-select-option key={index} value={el} onClick={() => this.optionSelected(el)}>\n                  {el}\n                </bds-select-option>\n              ))}\n            </bds-select>\n            {this.pageCounter && (\n              <bds-typo class=\"actions--text\" variant=\"fs-14\" no-wrap=\"true\">\n                {currentLanguage.of} {this.pages} {currentLanguage.pages}\n              </bds-typo>\n            )}\n            <bds-button-icon\n              onBdsClick={(ev) => this.nextPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-right\"\n              dataTest={this.dtButtonNext}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.lastPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-last\"\n              dataTest={this.dtButtonEnd}\n            ></bds-button-icon>\n          </bds-grid>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK,GACd;AACI,IAAA,YAAY,EAAE,gBAAgB;AAC9B,IAAA,EAAE,EAAE,IAAI;AACR,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,KAAK,EAAE;CACV;AAGE,MAAM,KAAK,GACd;AACI,IAAA,YAAY,EAAE,kBAAkB;AAChC,IAAA,EAAE,EAAE,IAAI;AACR,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,KAAK,EAAE;CACV;AAGE,MAAM,KAAK,GACd;AACI,IAAA,YAAY,EAAE,kBAAkB;AAChC,IAAA,EAAE,EAAE,IAAI;AACR,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,KAAK,EAAE;CACV;;ACxBL,MAAM,aAAa,GAAG,0RAA0R;;MCUnS,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AASE;;;AAGG;AACM,QAAA,IAAA,CAAA,KAAK,GAAW,IAAI,CAAC,WAAW;AAUzC;;AAEG;AACM,QAAA,IAAiB,CAAA,iBAAA,GAAG,EAAE;;AAMtB,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;AAYtC;;;AAGG;AACK,QAAA,IAAe,CAAA,eAAA,GAAmC,MAAM;;AAGxD,QAAA,IAAW,CAAA,WAAA,GAAa,KAAK;;AAS7B,QAAA,IAAQ,CAAA,QAAA,GAAY,OAAO;AAEnC;;;AAGG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AAEvC;;;AAGG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AAEpC;;;AAGG;AACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;AAEtC;;;AAGG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AAEpC;;;AAGG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;AA0EnC,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,KAAY,KAAI;AAC1B,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,YAAA,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;gBACnB,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;gBAC3B,IAAI,CAAC,eAAe,EAAE;;AAE1B,SAAC;AAED,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,KAAY,KAAI;AAC7B,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,YAAA,IAAI,EAAE,GAAG,CAAC,EAAE;gBACV,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;gBAC3B,IAAI,CAAC,eAAe,EAAE;;AAE1B,SAAC;AAED,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,KAAY,KAAI;AAC3B,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,YAAA,IAAI,EAAE,GAAG,CAAC,EAAE;gBACV,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,eAAe,EAAE;;AAE1B,SAAC;AAED,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,KAAY,KAAI;AAC1B,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,YAAA,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;gBACnB,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;gBACvB,IAAI,CAAC,eAAe,EAAE;;AAE1B,SAAC;AAED,QAAA,IAAW,CAAA,WAAA,GAAG,MAAK;AACjB,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU;AACpC,SAAC;AAED,QAAA,IAAM,CAAA,MAAA,GAAG,MAAK;AACZ,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACzB,SAAC;AAsGF;IAvMC,iBAAiB,GAAA;QACf,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;AAEpC,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,SAAS,EAAE;;IAKlB,YAAY,GAAA;QACV,IAAI,CAAC,SAAS,EAAE;;IAIlB,YAAY,GAAA;QACV,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;IAG3C,gBAAgB,GAAA;AACd,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AACtC,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;YAC9D,OAAO,KAAK,EAAE;AACd,gBAAA,IAAI,CAAC,SAAS,GAAG,EAAE;;;;IAKzB,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS;YAC/C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAIjC,SAAS,GAAA;QACP,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE;;QAE7B,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AACvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEhC,YAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE;AACrD,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;;iBACxB;gBACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;;AAiD5C,IAAA,cAAc,CAAC,KAAK,EAAA;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,eAAe,EAAE;;AAIxB,IAAA,YAAY,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;QACzB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,eAAe,EAAE;QACtB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;;IAGpD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC;AACzD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC;;AAG3E,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,QAAQ,IAAI,CAAC,QAAQ;AACnB,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,KAAK;AACd,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,KAAK;AACd,YAAA;AACE,gBAAA,OAAO,KAAK;;;IAIlB,MAAM,GAAA;;AACJ,QAAA,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI;AAChC,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,EAAA,EAC3C,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,iBAAA,EAA0B,eAAe,EAAA,EACtC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,KAClC,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAC,GAAG,EAAA,aAAA,EAAa,QAAQ,EAAC,KAAK,EAAC,gBAAgB,EAAA,EAC3D,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,OAAO,EAAA,EAAE,eAAe,CAAC,YAAY,EAAa,GAAA,CAAA,EACpE,CAAY,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,gBAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAA,kBAAA,EAAoB,IAAI,CAAC,eAAe,EAC7F,EAAA,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,MAC7B,CAAA,CAAA,mBAAA,EAAA,EAAmB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA,EAC3E,EAAE,CACe,CACrB,CAAC,CACS,EACb,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAA,SAAA,EAAS,MAAM,EAAA,EACrC,IAAI,CAAC,SAAS,OAAG,IAAI,CAAC,OAAO,OAAG,eAAe,CAAC,EAAE,OAAG,IAAI,CAAC,WAAW,CAC7D,CACF,CACZ,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAC,GAAG,EAAA,aAAA,EAAa,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAA,EACpD,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EACtC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,aAAa,EAClB,QAAQ,EAAE,IAAI,CAAC,eAAe,EACb,CAAA,EACnB,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACxC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,YAAY,EACjB,QAAQ,EAAE,IAAI,CAAC,YAAY,EACV,CAAA,EAEnB,CAAY,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,gBAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAA,kBAAA,EAAoB,IAAI,CAAC,eAAe,EACzF,EAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,MACpC,CAAA,CAAA,mBAAA,EAAA,EAAmB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EAAA,EAC7E,EAAE,CACe,CACrB,CAAC,CACS,EACZ,IAAI,CAAC,WAAW,KACf,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,aAAS,MAAM,EAAA,EAC3D,eAAe,CAAC,EAAE,OAAG,IAAI,CAAC,KAAK,OAAG,eAAe,CAAC,KAAK,CAC/C,CACZ,EACD,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EACrC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,aAAa,EAClB,QAAQ,EAAE,IAAI,CAAC,YAAY,EACV,CAAA,EACnB,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EACrC,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,YAAY,EACjB,QAAQ,EAAE,IAAI,CAAC,WAAW,EACT,CAAA,CACV,CACF,CACN;;;;;;;;;;;;;;"}