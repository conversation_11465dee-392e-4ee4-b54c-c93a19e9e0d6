{"version": 3, "names": ["tableRowCss", "TableRow", "exports", "class_1", "hostRef", "_this", "this", "isDense", "isCollapsed", "colspanNumber", "clickable", "selected", "toggleCollapse", "target", "collapse", "body", "document", "querySelector", "concat", "classList", "toggle", "prototype", "componentWillLoad", "bdsTable", "element", "closest", "collapseRow", "dataTarget", "children", "length", "getAttribute", "denseTable", "add", "componentWillUpdate", "render", "bodyCollapse", "h", "colSpan", "class", "isFirstRow", "parentElement", "Host", "_a", "host", "onClick", "type", "arrow", "active", "name"], "sources": ["src/components/table/table-row/table-row.scss?tag=bds-table-row&encapsulation=scoped", "src/components/table/table-row/table-row.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-row;\n  height: 64px;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  border-bottom: 1px solid $color-border-2;\n\n  .collapse-body {\n    padding: 16px;\n    max-height: 100px;\n    text-align: left;\n    opacity: 1;\n    transition: all ease 0.5s;\n  }\n}\n\n:host:last-child {\n  border-bottom: none;\n}\n\n:host(.clickable--true):hover {\n  background-color: $color-hover;\n  border-bottom: 1px solid $color-border-2;\n  cursor: pointer;\n}\n\n:host(.clickable--true) {\n  border-bottom: none;\n}\n\n:host(.selected--true) {\n  border-radius: 8px;\n  outline: 2px solid $color-primary;\n  outline-offset: -1px;\n  border-bottom: none;\n}\n\n:host(.dense-row) {\n  height: auto;\n}\n\n:host(.collapse-body) {\n  height: fit-content;\n}  \n.arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(180deg);\n  }\n  .active {\n      transform: rotate(0deg);\n    }\n\n:host(.collapse) {\n  height: 0;\n\n  .collapse-body {\n    padding: 0;\n    max-height: 0;\n    opacity: 0;\n    overflow: hidden;\n    transition: all ease-in-out 0.5s;\n  }\n\n  th {\n      padding: 0;\n    }\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-row',\n  styleUrl: 'table-row.scss',\n  scoped: true,\n})\nexport class TableRow {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @State() collapse: boolean;\n  @State() isCollapsed = true;\n  @State() colspanNumber: number = null;\n  @State() bdsTable: HTMLBdsTableElement;\n  @State() collapseRow: HTMLBdsTableRowElement;\n  /**\n   * Prop to make hover animation.\n   */\n  @Prop({ mutable: true, reflect: true }) clickable?: boolean = false;\n  /**\n   * Prop to highlight the row selected.\n   */\n  @Prop() selected?: boolean = false;\n  @Prop() bodyCollapse?: string;\n  @Prop() dataTarget?: string;\n\n  toggleCollapse = (target) => {\n    if (this.collapse) {\n      const body = document.querySelector(`[body-collapse=\"${target}\"]`);\n      body.classList.toggle('collapse');\n      this.isCollapsed = !this.isCollapsed;\n    }\n  };\n\n  componentWillLoad() {\n    this.bdsTable = this.element.closest('bds-table');\n    this.collapseRow = document.querySelector(`[body-collapse=\"${this.dataTarget}\"]`);\n    this.colspanNumber = document.querySelector(`bds-table-row`).children.length;\n\n    if (this.bdsTable && (this.bdsTable.getAttribute('dense-table') === 'true' || this.bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n    if (this.bdsTable && (this.bdsTable.getAttribute('collapse') === 'true' || this.bdsTable.collapse === true)) {\n      this.collapse = true;\n      this.clickable = true;\n    }\n\n    if (this.collapseRow) {\n      this.collapseRow.classList.add('collapse');\n      this.collapseRow.classList.add('collapse-body');\n    }\n  }\n\n  componentWillUpdate() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n\n  render(): HTMLElement {\n    if (this.bodyCollapse) {\n      return (\n        <th colSpan={this.colspanNumber}>\n          <div class=\"collapse-body\">\n            <slot></slot>\n          </div>\n        </th>\n      );\n    } else {\n      const isFirstRow = this.element.closest('bds-table-header') === this.element.parentElement;\n      return (\n        <Host\n          class={{\n            host: true,\n            [`clickable--${this.clickable}`]: !isFirstRow && this.clickable === true ? true : false,\n            [`selected--${this.selected}`]: true,\n            'dense-row': this.isDense,\n          }}\n          onClick={() => this.toggleCollapse(this.dataTarget)}\n        >\n          {this.collapse && (\n            <bds-table-cell type=\"custom\">\n              {!isFirstRow && <bds-icon class={{ arrow: true, active: this.isCollapsed }} name=\"arrow-down\"></bds-icon>}\n            </bds-table-cell>\n          )}\n          <slot />\n        </Host>\n      );\n    }\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAc,m8C,ICOPC,EAAQC,EAAA,2BALrB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,UAOWA,KAAOC,QAAG,MAEVD,KAAWE,YAAG,KACdF,KAAaG,cAAW,KAMOH,KAASI,UAAa,MAItDJ,KAAQK,SAAa,MAI7BL,KAAAM,eAAiB,SAACC,GAChB,GAAIR,EAAKS,SAAU,CACjB,IAAMC,EAAOC,SAASC,cAAc,mBAAAC,OAAmBL,EAAM,OAC7DE,EAAKI,UAAUC,OAAO,YACtBf,EAAKG,aAAeH,EAAKG,W,CAE7B,CA2DD,CAzDCL,EAAAkB,UAAAC,kBAAA,WACEhB,KAAKiB,SAAWjB,KAAKkB,QAAQC,QAAQ,aACrCnB,KAAKoB,YAAcV,SAASC,cAAc,mBAAAC,OAAmBZ,KAAKqB,WAAU,OAC5ErB,KAAKG,cAAgBO,SAASC,cAAc,iBAAiBW,SAASC,OAEtE,GAAIvB,KAAKiB,WAAajB,KAAKiB,SAASO,aAAa,iBAAmB,QAAUxB,KAAKiB,SAASQ,aAAe,MAAO,CAChHzB,KAAKC,QAAU,I,CAEjB,GAAID,KAAKiB,WAAajB,KAAKiB,SAASO,aAAa,cAAgB,QAAUxB,KAAKiB,SAAST,WAAa,MAAO,CAC3GR,KAAKQ,SAAW,KAChBR,KAAKI,UAAY,I,CAGnB,GAAIJ,KAAKoB,YAAa,CACpBpB,KAAKoB,YAAYP,UAAUa,IAAI,YAC/B1B,KAAKoB,YAAYP,UAAUa,IAAI,gB,GAInC7B,EAAAkB,UAAAY,oBAAA,WACE,IAAMV,EAAWjB,KAAKkB,QAAQC,QAAQ,aACtC,GAAIF,IAAaA,EAASO,aAAa,iBAAmB,QAAUP,EAASQ,aAAe,MAAO,CACjGzB,KAAKC,QAAU,I,GAInBJ,EAAAkB,UAAAa,OAAA,W,MAAA,IAAA7B,EAAAC,KACE,GAAIA,KAAK6B,aAAc,CACrB,OACEC,EAAI,MAAAC,QAAS/B,KAAKG,eAChB2B,EAAK,OAAAE,MAAM,iBACTF,EAAa,c,KAId,CACL,IAAMG,EAAajC,KAAKkB,QAAQC,QAAQ,sBAAwBnB,KAAKkB,QAAQgB,cAC7E,OACEJ,EAACK,EAAI,CACHH,OAAKI,EAAA,CACHC,KAAM,MACND,EAAC,cAAAxB,OAAcZ,KAAKI,aAAe6B,GAAcjC,KAAKI,YAAc,KAAO,KAAO,MAClFgC,EAAC,aAAAxB,OAAaZ,KAAKK,WAAa,KAChC+B,EAAA,aAAapC,KAAKC,Q,GAEpBqC,QAAS,WAAM,OAAAvC,EAAKO,eAAeP,EAAKsB,WAAzB,GAEdrB,KAAKQ,UACJsB,EAAA,kBAAgBS,KAAK,WACjBN,GAAcH,EAAA,YAAUE,MAAO,CAAEQ,MAAO,KAAMC,OAAQzC,KAAKE,aAAewC,KAAK,gBAGrFZ,EAAQ,a,6HA/EG,I", "ignoreList": []}