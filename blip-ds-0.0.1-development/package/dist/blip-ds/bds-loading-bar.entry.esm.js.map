{"version": 3, "file": "bds-loading-bar.entry.esm.js", "sources": ["src/components/loading-bar/loading-bar.scss?tag=bds-loading-bar&encapsulation=shadow", "src/components/loading-bar/loading-bar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n}\n\n.loading_bar {\n  box-sizing: border-box;\n  position: relative;\n  border-radius: 32px;\n  border: 1px solid $color-content-disable;\n  margin-bottom: 8px;\n\n  &.size_small {\n    height: 8px;\n    .bar_behind {\n      & .loading {\n        border-radius: 1px;\n      }\n    }\n  }\n\n  &.size_default {\n    height: 16px;\n    .bar_behind {\n      & .loading {\n        border-radius: 2px;\n      }\n    }\n  }\n\n  .bar_behind {\n    position: absolute;\n    inset: 0.5px 1px 1px 0.5px;\n    border-radius: 16px;\n    overflow: hidden;\n\n    & .loading {\n      position: absolute;\n      height: 100%;\n      background-color: $color-extended-blue;\n      @include animation();\n      overflow: hidden;\n\n      & .loader {\n        position: absolute;\n        left: -16px;\n        width: calc(100% + 16px);\n        height: 100%;\n        background: rgb(255, 255, 255);\n        background: linear-gradient(\n          90deg,\n          rgba(255, 255, 255, 0) 0%,\n          rgba(255, 255, 255, 0) 75%,\n          rgba(0, 0, 0, 0.26) 75%\n        );\n        background-size: 4px;\n        transform: skewX(-15deg);\n        animation-name: load;\n        animation-timing-function: linear;\n        animation-duration: 0.5s;\n        animation-iteration-count: infinite;\n      }\n    }\n  }\n}\n\n.typo_loading {\n  padding-left: 8px;\n  padding-right: 8px;\n  color: $color-content-default;\n}\n\n@keyframes load {\n  from {\n    left: -16px;\n  }\n  to {\n    left: 0;\n  }\n}\n", "import { Component, Host, Prop, h } from '@stencil/core';\n\nexport type loadingBarSize = 'small' | 'default';\n\n@Component({\n  tag: 'bds-loading-bar',\n  styleUrl: 'loading-bar.scss',\n  shadow: true,\n})\nexport class BdsloadingBar {\n  /**\n   * Percent, property to enter the loading bar status percentage value.\n   */\n  @Prop() percent?: number = 0;\n  /**\n   * Size, property to define size of component.\n   */\n  @Prop() size?: loadingBarSize = 'default';\n  /**\n   * Text, property to enable the bar info text.\n   */\n  @Prop() text?: string = '';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    const styles = { width: `${this.percent ? (this.percent > 100 ? 100 : this.percent) : 0}%` };\n    return (\n      <Host>\n        <div class={{ loading_bar: true, [`size_${this.size}`]: true }} data-test={this.dataTest}>\n          <div class={{ bar_behind: true }}>\n            <div class={{ loading: true }} style={styles}>\n              <div class=\"loader\"></div>\n            </div>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,aAAa,GAAG,u1DAAu1D;;MCSh2D,aAAa,GAAA,MAAA;AAL1B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAY,CAAC;AAC5B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAoB,SAAS;AACzC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE1B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAgBjC;IAdC,MAAM,GAAA;AACJ,QAAA,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAG,EAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA,CAAA,CAAG,EAAE;QAC5F,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI,EAAE,EAAa,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EACtF,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAA,EAC9B,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAA,EAC1C,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,QAAQ,EAAA,CAAO,CACtB,CACF,CACF,CACD;;;;;;;"}