{"version": 3, "names": ["modalCss", "BdsModal", "constructor", "hostRef", "this", "open", "closeButton", "size", "outzoneClose", "enterClose", "dtOutzone", "dtButtonClose", "listener", "event", "key", "toggle", "handleMouseClick", "onClickOutzone", "isOpenChanged", "document", "addEventListener", "bdsModalChanged", "emit", "modalStatus", "removeEventListener", "render", "h", "class", "modal__dialog", "outzone", "onClick", "modal", "name", "tabindex", "dataTest", "slot"], "sources": ["src/components/modal/modal.scss?tag=bds-modal&encapsulation=shadow", "src/components/modal/modal.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.modal__dialog {\n  opacity: 0;\n  visibility: hidden;\n  width: 100%;\n  height: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: opacity 0.3s ease-in-out;\n  z-index: $zindex-modal-overlay;\n  display: none;\n\n  & .outzone {\n    position: absolute;\n    inset: 0;\n    background-color: $color-content-din;\n    opacity: 0.7;\n  }\n\n  &--dynamic {\n    overflow-y: auto;\n    padding-top: 40px;\n    padding-bottom: 40px;\n    height: -webkit-fill-available;\n  }\n\n  .modal {\n    position: relative;\n    margin: auto;\n    width: 592px;\n    height: 368px;\n    border-radius: 8px;\n    background: $color-surface-1;\n    box-shadow: $shadow-3;\n    padding: 32px;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n\n    &--dynamic {\n      height: auto;\n      width: auto;\n      max-width: 1000px;\n    }\n    .close-button {\n      position: relative;\n      color: $color-content-default;\n      align-self: flex-end;\n      margin-bottom: 16px;\n      cursor: pointer;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: -4px;\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n      &:focus-visible {\n        outline: none;\n\n        &::before {\n          border-color: $color-focus;\n        }\n      }\n    }\n\n    .slot {\n      &--dynamic {\n        flex: 1 1 auto;\n      }\n    }\n  }\n\n  &--open {\n    opacity: 1;\n    visibility: visible;\n    display: flex;\n  }\n}\n", "import { Component, ComponentInterface, h, Method, Event, EventEmitter, Prop, Watch } from '@stencil/core';\n\nexport type sizes = 'fixed' | 'dynamic';\n@Component({\n  tag: 'bds-modal',\n  styleUrl: 'modal.scss',\n  shadow: true,\n})\nexport class BdsModal implements ComponentInterface {\n  /**\n   * Used to open/close the modal\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public closeButton?: boolean = true;\n\n  /**\n   * Used to change the modal heights.\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public size?: sizes = 'fixed';\n\n  /**\n   * If true, the modal will close clicking outside the component.\n   */\n  @Prop() outzoneClose?: boolean = true;\n\n  /**\n   * If true, the modal will close keydown Enter.\n   */\n  @Prop() enterClose?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtOutzone is the data-test to button close.\n   */\n  @Prop() dtOutzone?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n\n  /**\n   * Emitted when modal status has changed.\n   */\n  @Event() bdsModalChanged!: EventEmitter;\n\n  /**\n   * Can be used outside to open/close the modal\n   */\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Watch('open')\n  protected isOpenChanged(): void {\n    if (this.open) {\n      document.addEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'opened' });\n    } else {\n      document.removeEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'closed' });\n    }\n  }\n\n  private listener = (event) => {\n    if (this.enterClose && (event.key == 'Enter' || event.key == 'Escape')) {\n      this.toggle();\n    }\n  };\n\n  private handleMouseClick = (): void => {\n    this.open = false;\n  };\n\n  private onClickOutzone = () => {\n    if (this.outzoneClose) {\n      this.open = false;\n    }\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          modal__dialog: true,\n          'modal__dialog--open': this.open,\n          [`modal__dialog--${this.size}`]: true,\n        }}\n      >\n        <div class={{ outzone: true }} onClick={() => this.onClickOutzone()} data-test={this.dtOutzone}></div>\n        <div class={{ modal: true, [`modal--${this.size}`]: true }}>\n          {this.closeButton && (\n            <bds-icon\n              size=\"medium\"\n              class=\"close-button\"\n              name=\"close\"\n              tabindex=\"0\"\n              onClick={this.handleMouseClick}\n              dataTest={this.dtButtonClose}\n            />\n          )}\n          {this.size == 'fixed' && <slot></slot>}\n          {this.size !== 'fixed' && (\n            <div class={{ slot: true, [`slot--${this.size}`]: true }}>\n              <slot></slot>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAW,8qD,MCQJC,EAAQ,MALrB,WAAAC,CAAAC,G,yDAaSC,KAAIC,KAAa,MASjBD,KAAWE,YAAa,KASxBF,KAAIG,KAAW,QAKdH,KAAYI,aAAa,KAKzBJ,KAAUK,WAAa,KAMvBL,KAASM,UAAY,KAMrBN,KAAaO,cAAY,KA0BzBP,KAAAQ,SAAYC,IAClB,GAAIT,KAAKK,aAAeI,EAAMC,KAAO,SAAWD,EAAMC,KAAO,UAAW,CACtEV,KAAKW,Q,GAIDX,KAAgBY,iBAAG,KACzBZ,KAAKC,KAAO,KAAK,EAGXD,KAAca,eAAG,KACvB,GAAIb,KAAKI,aAAc,CACrBJ,KAAKC,KAAO,K,EAmCjB,CA9DC,YAAMU,GACJX,KAAKC,MAAQD,KAAKC,I,CAIV,aAAAa,GACR,GAAId,KAAKC,KAAM,CACbc,SAASC,iBAAiB,UAAWhB,KAAKQ,SAAU,OACpDR,KAAKiB,gBAAgBC,KAAK,CAAEC,YAAa,U,KACpC,CACLJ,SAASK,oBAAoB,UAAWpB,KAAKQ,SAAU,OACvDR,KAAKiB,gBAAgBC,KAAK,CAAEC,YAAa,U,EAoB7C,MAAAE,GACE,OACEC,EACE,OAAAZ,IAAA,2CAAAa,MAAO,CACLC,cAAe,KACf,sBAAuBxB,KAAKC,KAC5B,CAAC,kBAAkBD,KAAKG,QAAS,OAGnCmB,EAAK,OAAAZ,IAAA,2CAAAa,MAAO,CAAEE,QAAS,MAAQC,QAAS,IAAM1B,KAAKa,iBAA6B,YAAAb,KAAKM,YACrFgB,EAAA,OAAAZ,IAAA,2CAAKa,MAAO,CAAEI,MAAO,KAAM,CAAC,UAAU3B,KAAKG,QAAS,OACjDH,KAAKE,aACJoB,EAAA,YAAAZ,IAAA,2CACEP,KAAK,SACLoB,MAAM,eACNK,KAAK,QACLC,SAAS,IACTH,QAAS1B,KAAKY,iBACdkB,SAAU9B,KAAKO,gBAGlBP,KAAKG,MAAQ,SAAWmB,EAAa,QAAAZ,IAAA,6CACrCV,KAAKG,OAAS,SACbmB,EAAA,OAAAZ,IAAA,2CAAKa,MAAO,CAAEQ,KAAM,KAAM,CAAC,SAAS/B,KAAKG,QAAS,OAChDmB,EAAA,QAAAZ,IAAA,+C", "ignoreList": []}