{"version": 3, "names": ["tableHeaderCellCss", "TableHeaderCell", "exports", "class_1", "hostRef", "this", "isDense", "sortable", "arrow", "justifyContent", "prototype", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "denseTable", "render", "h", "Host", "key", "class", "_a", "th_cell", "concat", "bold", "variant", "size", "name"], "sources": ["src/components/table/table-header-cell/table-header-cell.scss?tag=bds-table-th&encapsulation=scoped", "src/components/table/table-header-cell/table-header-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0px 8px;\n}\n.th_cell {\n  display: flex;\n  align-items: center;\n  height: 64px;\n  gap: 8px;\n  font-family: $font-family;\n  box-sizing: border-box;\n\n  &--sortable-true:hover, &--sortable-false:hover  {\n    cursor: pointer;\n  }\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n.dense-th {\n  min-height: 48px;\n  height: auto;\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n@Component({\n  tag: 'bds-table-th',\n  styleUrl: 'table-header-cell.scss',\n  scoped: true,\n})\nexport class TableHeaderCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() sortable = false;\n  @Prop() arrow = '';\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div\n          class={{\n            th_cell: true,\n            [`th_cell--sortable-${this.sortable}`]: true,\n            'dense-th': this.isDense,\n            [`justify--${this.justifyContent}`]:true\n          }}\n        >\n          <bds-typo bold={this.sortable ? 'bold' : 'semi-bold'} variant=\"fs-14\">\n            <slot />\n          </bds-typo>\n          {this.sortable ? (\n            <bds-icon\n              size=\"small\"\n              name={this.arrow === 'asc' ? 'arrow-down' : this.arrow === 'dsc' ? 'arrow-up' : ''}\n            ></bds-icon>\n          ) : ''\n            // <div style={{ width: '20px' }}></div>\n          }\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAqB,+xB,ICQdC,EAAeC,EAAA,0BAL5B,SAAAC,EAAAC,G,UAOWC,KAAOC,QAAG,MACXD,KAAQE,SAAG,MACXF,KAAKG,MAAG,GACRH,KAAcI,eAAmB,MAkC1C,CAhCCN,EAAAO,UAAAC,kBAAA,WACE,IAAMC,EAAWP,KAAKQ,QAAQC,QAAQ,aACtC,GAAIF,IAAaA,EAASG,aAAa,iBAAmB,QAAUH,EAASI,aAAe,MAAO,CACjGX,KAAKC,QAAU,I,GAGnBH,EAAAO,UAAAO,OAAA,W,MACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,OAAKC,EAAA,CACHC,QAAS,MACTD,EAAC,qBAAAE,OAAqBnB,KAAKE,WAAa,KACxCe,EAAA,YAAYjB,KAAKC,QACjBgB,EAAC,YAAAE,OAAYnB,KAAKI,iBAAkB,K,IAGtCS,EAAA,YAAAE,IAAA,2CAAUK,KAAMpB,KAAKE,SAAW,OAAS,YAAamB,QAAQ,SAC5DR,EAAA,QAAAE,IAAA,8CAEDf,KAAKE,SACJW,EAAA,YACES,KAAK,QACLC,KAAMvB,KAAKG,QAAU,MAAQ,aAAeH,KAAKG,QAAU,MAAQ,WAAa,KAEhF,I,4HAhCc,I", "ignoreList": []}