var __awaiter=this&&this.__awaiter||function(e,t,r,o){function n(e){return e instanceof r?e:new r((function(t){t(e)}))}return new(r||(r=Promise))((function(r,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o["throw"](e))}catch(e){i(e)}}function c(e){e.done?r(e.value):n(e.value).then(a,s)}c((o=o.apply(e,t||[])).next())}))};var __generator=this&&this.__generator||function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},o,n,i,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(e){return function(t){return c([e,t])}}function c(s){if(o)throw new TypeError("Generator is already executing.");while(a&&(a=0,s[0]&&(r=0)),r)try{if(o=1,n&&(i=s[0]&2?n["return"]:s[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;if(n=0,i)s=[s[0]&2,i.value];switch(s[0]){case 0:case 1:i=s;break;case 4:r.label++;return{value:s[1],done:false};case 5:r.label++;n=s[1];s=[0];continue;case 7:s=r.ops.pop();r.trys.pop();continue;default:if(!(i=r.trys,i=i.length>0&&i[i.length-1])&&(s[0]===6||s[0]===2)){r=0;continue}if(s[0]===3&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(s[0]===6&&r.label<i[1]){r.label=i[1];i=s;break}if(i&&r.label<i[2]){r.label=i[2];r.ops.push(s);break}if(i[2])r.ops.pop();r.trys.pop();continue}s=t.call(e,r)}catch(e){s=[6,e];n=0}finally{o=i=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,r,o,n,i;return{setters:[function(e){t=e.r;r=e.c;o=e.h;n=e.H;i=e.a}],execute:function(){var a=':host{display:block;margin:-4px;padding:4px;width:100%;position:relative;overflow:hidden;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.nav_main{display:-ms-flexbox;display:flex;gap:8px;-ms-flex-align:center;align-items:center;padding:8px;position:relative;cursor:pointer;border-radius:8px;border:1px solid transparent;overflow:hidden}.nav_main--loading{cursor:wait}.nav_main--disable{opacity:0.5;cursor:not-allowed}.nav_main:before{content:"";position:absolute;inset:0}.nav_main:hover:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.nav_main:active:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}.nav_main:hover,.nav_main_active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_main_active:before{background-color:var(--color-content-default, rgb(40, 40, 40));border-color:var(--color-hover, rgba(0, 0, 0, 0.08));opacity:0.08}.nav_main--disable:before,.nav_main--disable:hover{border-color:transparent;background-color:transparent}.nav_main .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.nav_main .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.nav_main_text{position:relative;display:-ms-flexbox;display:flex;gap:2px;-ms-flex-direction:column;flex-direction:column}.nav_main_text .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.nav_main_text .title-item--loading{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_text .subtitle-item{color:var(--color-content-default, rgb(40, 40, 40))}.nav_main_text .subtitle-item--loading{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_content{width:100%;-ms-flex-negative:99999;flex-shrink:99999}.nav_main_arrow{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.nav_main_arrow--disable{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_arrow_active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion{display:grid;grid-template-rows:0fr;-webkit-transition:all ease 0.5s;-moz-transition:all ease 0.5s;transition:all ease 0.5s}.accordion_open{grid-template-rows:1fr;padding:8px 0}.accordion .container{overflow:hidden;position:relative;padding-left:23px}.accordion .container:before{content:"";position:absolute;width:2px;inset:0;left:23px;top:8px;bottom:8px;border-radius:8px;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));opacity:0.8}.accordion .container--disable:before{background-color:transparent}.nav_tree_item{position:relative;display:-ms-flexbox;display:flex;gap:8px;-ms-flex-pack:justify;justify-content:space-between;-ms-flex-align:center;align-items:center;cursor:pointer;padding:8px;padding-left:22px}.nav_tree_item--loading{cursor:wait}.nav_tree_item--disable{opacity:0.5;cursor:not-allowed}.nav_tree_item--disable:before,.nav_tree_item--disable:hover{border-color:transparent;background-color:transparent}.nav_tree_item .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.nav_tree_item .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.nav_tree_item_content{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.nav_tree_item_slot{width:100%;-ms-flex-negative:99999;flex-shrink:99999}.nav_tree_item:before{content:"";position:absolute;width:2px;inset:0;top:8px;bottom:8px;border-radius:8px;background-color:transparent;-webkit-transition:background-color ease 0.8s;-moz-transition:background-color ease 0.8s;transition:background-color ease 0.8s}.nav_tree_item:hover:before{background-color:var(--color-pressed, rgba(0, 0, 0, 0.16));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item_active:before{background-color:var(--color-primary, rgb(30, 107, 241));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item_active:hover:before{background-color:var(--color-primary, rgb(30, 107, 241));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item .icon-arrow{position:relative;-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.nav_tree_item .icon-arrow-active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.nav_tree_item_button{padding:8px;margin-left:14px;border-radius:8px;border:1px solid transparent}.nav_tree_item_button:before{left:-15px}.nav_tree_item_button:after{content:"";position:absolute;inset:0;border-radius:8px;background-color:transparent}.nav_tree_item_button:hover,.nav_tree_item_button_active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_tree_item_button:hover:after,.nav_tree_item_button_active:after{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.nav_tree_item_button:active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_tree_item_button:active:after{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}.focus{position:relative}.focus:before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.focus:focus-visible{outline:none}.focus:focus-visible:before{border-color:var(--color-focus, rgb(194, 38, 251))}';var s=e("bds_nav_tree",function(){function e(e){var o=this;t(this,e);this.bdsToogleChange=r(this,"bdsToogleChange");this.itemsGroup=null;this.isOpenAftAnimation=false;this.navTreeChild=null;this.numberElement=null;this.collapse="single";this.isOpen=false;this.icon=null;this.secondaryText=null;this.dataTest=null;this.loading=false;this.disable=false;this.handler=function(){if(!o.loading&&!o.disable){o.isOpen=!o.isOpen}}}e.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){if(!this.disable){this.isOpen=!this.isOpen}return[2]}))}))};e.prototype.reciveNumber=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.numberElement=e;return[2]}))}))};e.prototype.open=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.isOpen=true;return[2]}))}))};e.prototype.close=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.isOpen=false;return[2]}))}))};e.prototype.isOpenChanged=function(e){var t;this.bdsToogleChange.emit({value:e,element:this.element});if(e){if(this.itemsGroup.collapse=="single"){(t=this.itemsGroup)===null||t===void 0?void 0:t.closeAll(this.numberElement)}}};e.prototype.componentWillLoad=function(){this.itemsGroup=this.element.parentElement.tagName=="BDS-NAV-TREE-GROUP"&&this.element.parentElement;this.navTreeChild=this.element.querySelector("bds-nav-tree-item")===null?false:true};e.prototype.handleKeyDown=function(e){if(e.key=="Enter"&&!this.disable){this.isOpen=!this.isOpen}};e.prototype.render=function(){var e,t,r,i,a,s,c;return o(n,{key:"80ac5273c12d66e3dd437a0d97e155cfae225360"},o("div",{key:"cad8aaf5175c6c220371b37967c698d0a1ded530",tabindex:"0",onKeyDown:this.handleKeyDown.bind(this),class:"focus"},o("div",{key:"6b5d037ad94347ee0c923919b5e9c61f64ea32e7",class:(e={},e["nav_main--disable"]=this.disable,e)},o("div",{key:"cb9f7fd56b3f6092c9490955feda6ef9733f015d",onClick:this.handler,class:(t={nav_main:true,nav_main_active:this.isOpen},t["nav_main--loading"]=this.loading,t["nav_main--disable"]=this.disable,t),"data-test":this.dataTest,"aria-label":this.text+(this.secondaryText&&": ".concat(this.secondaryText))},this.loading?o("bds-loading-spinner",{size:"extra-small"}):this.icon?o("bds-icon",{class:(r={},r["icon-item"]=true,r["icon-item-active"]=this.isOpen,r),size:"medium",name:this.icon,color:"inherit",theme:"outline"}):"",o("div",{key:"c27f99a63d3daff9ebc5d14126117ee1d6df765c",class:"nav_main_text"},this.text&&o("bds-typo",{key:"750964e347e14973f2690364c3c7104eae15b8af",class:(i={},i["title-item"]=true,i["title-item--loading"]=this.loading,i),variant:"fs-14",tag:"span","line-height":"small",bold:this.isOpen?"bold":"semi-bold"},this.text),this.secondaryText&&o("bds-typo",{key:"e3de7f19865b637c4ac1af10bbcabd973a2787a0",class:(a={},a["subtitle-item"]=true,a["subtitle-item--loading"]=this.loading,a),variant:"fs-12","line-height":"small",tag:"span",margin:false},this.secondaryText)),o("div",{key:"83b1e2d7edad3678e2437e04b91158dd841a42b2",class:"nav_main_content"},o("slot",{key:"1ba5665ad7028a10c9e9e79081efeee60570cdc1",name:"header-content"})),this.navTreeChild&&o("bds-icon",{key:"369c145d451124cf001ef1677e9244438adee834",name:"arrow-down",class:(s={},s["nav_main_arrow"]=true,s["nav_main_arrow_active"]=this.isOpen,s["nav_main_arrow--loading"]=this.loading,s)})))),o("div",{key:"1da4e53b786d2d2b79a84f833ef433b8ff4bbc93",class:{accordion:true,accordion_open:this.isOpen&&this.navTreeChild}},o("div",{key:"a34a1324f95be2dbb10d743c1350ee344b99e4ed",class:(c={},c["container"]=true,c["container--disable"]=this.disable,c)},o("slot",{key:"d25ef5dbbe277995f17b1a22d4f0bf08434a035c"}))))};Object.defineProperty(e.prototype,"element",{get:function(){return i(this)},enumerable:false,configurable:true});Object.defineProperty(e,"watchers",{get:function(){return{isOpen:["isOpenChanged"]}},enumerable:false,configurable:true});return e}());s.style=a}}}));
//# sourceMappingURL=p-09e183f9.system.entry.js.map