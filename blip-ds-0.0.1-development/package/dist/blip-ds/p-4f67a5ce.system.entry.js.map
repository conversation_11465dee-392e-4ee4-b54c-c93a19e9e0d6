{"version": 3, "names": ["inputChipsCss", "InputChips", "exports", "class_1", "hostRef", "_this", "this", "InputSize", "validationDanger", "inputAvalible", "isPressed", "validationMesage", "internalChips", "chips", "blurCreation", "type", "label", "icon", "delimiters", "errorMessage", "danger", "success", "value", "duplicated", "disableSubmit", "disabled", "helperMessage", "successMessage", "inputName", "placeholder", "counterLength", "dataTest", "dtButtonClose", "onClickWrapper", "onFocus", "nativeInput", "focus", "bdsInputChipsFocus", "emit", "onInput", "ev", "input", "target", "bdsInputChipsInput", "keyPressWrapper", "event", "key", "handleDelimiters", "setChip", "bdsChange", "data", "getLastChip", "bdsChangeChips", "length", "removeLastChip", "prototype", "valueChanged", "JSON", "parse", "_a", "internalValueChanged", "minMaxValidation", "<PERSON><PERSON><PERSON><PERSON>", "validateChips", "get", "clear", "add", "setFocus", "removeFocus", "blur", "componentDidLoad", "componentWillLoad", "some", "chip", "validateChip", "handleOnBlur", "bdsBlur", "bdsSubmit", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "bdsExtendedQuantityInput", "trim", "existTerm", "match", "newValue", "verifyAndSubstituteDelimiters", "clearInputValues", "words", "split", "for<PERSON>ach", "word", "replace", "substring", "handleChange", "detail", "trimStart", "name", "exists", "toLowerCase", "whitespaceValidation", "__spread<PERSON><PERSON>y", "trimmedName", "emailValidation", "slice", "removeChip", "id", "filter", "_chip", "index", "toString", "renderChips", "map", "limit", "h", "color", "close", "onChipClickableClose", "position", "concat", "renderIcon", "class", "input__icon", "size", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "render", "defaultMaxHeight", "maxHeight", "Host", "onClick", "onKeyDown", "style", "ref", "maxlength", "onBlur", "onChange", "max", "active"], "sources": ["src/components/input-chips/input-chips.scss?tag=bds-input-chips&encapsulation=shadow", "src/components/input-chips/input-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n\n:host {\n  display: display;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      gap: 6px;\n      min-height: 24px;\n      max-width: 100%;\n      overflow-y: auto;\n      overflow-x: hidden; /* Force no horizontal scroll */\n      word-break: break-word; /* Break long text in chips */\n      @include custom-scroll;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      min-width: 80px; /* Reduced from 120px */\n      max-width: 100%;\n      flex: 1;\n      border: none;\n      outline: none;\n      background: transparent;\n      resize: none;\n      cursor: inherit;\n      overflow: hidden; /* Prevent text overflow */\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n", "import { Component, Host, h, Prop, Method, Event, EventEmitter, Watch, State } from '@stencil/core';\nimport { emailValidation, whitespaceValidation } from '../../utils/validations';\nimport { InputChipsTypes } from './input-chips-interface';\n\n@Component({\n  tag: 'bds-input-chips',\n  styleUrl: 'input-chips.scss',\n  shadow: true,\n})\nexport class InputChips {\n  private nativeInput?: HTMLInputElement;\n\n  @State() InputSize?: number = null;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Used to enable or disable input\n   */\n  @State() inputAvalible?: boolean = true;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string[] | string = [];\n\n  /**\n   * When true, the press enter will be simulated on blur event.\n   */\n  @Prop() blurCreation = false;\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   *  Set maximum length value for the chip content\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   *  Set maximum length value for chips\n   */\n  @Prop() maxChipsLength?: number;\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n\n  /**\n   * Disabled input\n   */\n  @Prop({ reflect: true }) disabled?: boolean = false;\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Passing true to display a counter of available size, it is necessary to\n   * pass another maxlength property.\n   */\n  @Prop() counterLength? = false;\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsFocus!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsBlur!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsInput!: EventEmitter;\n\n  /**\n   * Emitted when a maximum value defined by the \"max-chips-length\" prop is entered\n   */\n  @Event() bdsExtendedQuantityInput!: EventEmitter;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.minMaxValidation();\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async get(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentDidLoad() {\n    this.minMaxValidation();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n  }\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsInputChipsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit(this.internalChips);\n    if (this.internalChips.length > 0) {\n      this.bdsSubmit.emit({ value: this.internalChips });\n    }\n    this.handleDelimiters();\n    this.isPressed = false;\n    if (this.blurCreation) {\n      this.setChip(this.value);\n      this.value = '';\n    }\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInputChipsInput.emit(ev);\n  };\n\n  private minMaxValidation() {\n    if (!this.maxChipsLength == undefined) {\n      this.inputAvalible = true;\n    } else if (this.internalChips.length >= this.maxChipsLength) {\n      this.inputAvalible = false;\n      this.bdsExtendedQuantityInput.emit({ value: !this.inputAvalible });\n    } else {\n      this.inputAvalible = true;\n    }\n  }\n\n  private getLastChip(): string {\n    return this.internalChips[this.internalChips.length - 1];\n  }\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.handleDelimiters();\n        this.setChip(this.value);\n        this.value = '';\n        this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n        this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        }\n        break;\n    }\n  };\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    const {\n      detail: { value },\n    } = event;\n\n    // console.log('TRACE [input-chips] handleChange 1:', { value });\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      // Reduce the limit to prevent chips from being too wide and causing scroll issues\n      const limit = 20;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n            dtButtonClose={this.dtButtonClose}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n              dtButtonClose={this.dtButtonClose}\n            >\n              {`${chip.slice(0, limit)}...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n    // Set default maxHeight if not provided to prevent UI breaking\n    const defaultMaxHeight = this.maxHeight || '80px';\n    \n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div \n              class=\"input__container__wrapper\"\n              style={{ maxHeight: defaultMaxHeight }}\n            >\n              {/* Chips and input are now siblings in the same flex container */}\n              {this.internalChips.length > 0 && this.renderChips()}\n              {this.inputAvalible && (\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class=\"input__container__text\"\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                />\n              )}\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text length={this.internalChips.length} max={this.maxChipsLength} active={isPressed} />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"checkb\" theme=\"outline\" size=\"xxx-small\" />}\n          <slot name=\"input-right\"></slot>\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "mappings": "g5DAAA,IAAMA,EAAgB,+5Q,ICSTC,EAAUC,EAAA,6BALvB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,uUAQWA,KAASC,UAAY,KAKrBD,KAAgBE,iBAAa,MAI7BF,KAAaG,cAAa,KAI1BH,KAASI,UAAI,MAKbJ,KAAgBK,iBAAI,GAEpBL,KAAaM,cAAa,GAOVN,KAAKO,MAAsB,GAK5CP,KAAYQ,aAAG,MAMfR,KAAIS,KAAoB,OAKxBT,KAAKU,MAAI,GAcQV,KAAIW,KAAY,GAKjCX,KAAUY,WAAI,MAKGZ,KAAYa,aAAI,GAKDb,KAAMc,OAAI,MAIVd,KAAOe,QAAa,MAIpBf,KAAKgB,MAAmB,GAKxDhB,KAAUiB,WAAa,MAKvBjB,KAAakB,cAAG,MAKClB,KAAQmB,SAAa,MAKtCnB,KAAaoB,cAAY,GAIRpB,KAAcqB,eAAY,GAI3CrB,KAASsB,UAAY,GAKrBtB,KAAWuB,YAAY,GAMvBvB,KAAawB,cAAI,MAYjBxB,KAAQyB,SAAY,KAKpBzB,KAAa0B,cAAY,KA0HzB1B,KAAc2B,eAAG,WACvB5B,EAAK6B,UACL,GAAI7B,EAAK8B,YAAa,CACpB9B,EAAK8B,YAAYC,O,CAErB,EAEQ9B,KAAO4B,QAAG,WAChB7B,EAAKgC,mBAAmBC,OACxBjC,EAAKK,UAAY,IACnB,EAeQJ,KAAAiC,QAAU,SAACC,GACjB,IAAMC,EAAQD,EAAGE,OACjB,GAAID,EAAO,CACTpC,EAAKiB,MAAQmB,EAAMnB,OAAS,E,CAE9BjB,EAAKsC,mBAAmBL,KAAKE,EAC/B,EAiBQlC,KAAAsC,gBAAkB,SAACC,GACzB,OAAQA,EAAMC,KACZ,IAAK,QACHzC,EAAK0C,mBACL1C,EAAK2C,QAAQ3C,EAAKiB,OAClBjB,EAAKiB,MAAQ,GACbjB,EAAK4C,UAAUX,KAAK,CAAEY,KAAM7C,EAAKO,cAAeU,MAAOjB,EAAK8C,gBAC5D9C,EAAK+C,eAAed,KAAK,CAAEY,KAAM7C,EAAKO,cAAeU,MAAOjB,EAAK8C,gBACjE,MACF,IAAK,YACL,IAAK,SACH,IAAK9C,EAAKiB,QAAU,MAAQjB,EAAKiB,MAAM+B,QAAU,IAAMhD,EAAKO,cAAcyC,OAAQ,CAChFhD,EAAKiD,iBACLjD,EAAK4C,UAAUX,KAAK,CAAEY,KAAM7C,EAAKO,cAAeU,MAAOjB,EAAK8C,gBAC5D9C,EAAK+C,eAAed,KAAK,CAAEY,KAAM7C,EAAKO,cAAeU,MAAOjB,EAAK8C,e,CAEnE,MAEN,CA6QD,CAlaWhD,EAAAoD,UAAAC,aAAA,WACR,GAAIlD,KAAKO,MAAO,CACd,UAAWP,KAAKO,QAAU,SAAU,CAClC,IACEP,KAAKM,cAAgB6C,KAAKC,MAAMpD,KAAKO,M,CACrC,MAAA8C,GACArD,KAAKM,cAAgB,E,MAElB,CACLN,KAAKM,cAAgBN,KAAKO,K,MAEvB,CACLP,KAAKM,cAAgB,E,GAKfT,EAAAoD,UAAAK,qBAAA,WACRtD,KAAKuD,kB,EAOD1D,EAAAoD,UAAAO,QAAN,W,qFACE,SAAOxD,KAAKyD,gB,QAOR5D,EAAAoD,UAAAS,IAAN,W,qFACE,SAAO1D,KAAKM,c,QAORT,EAAAoD,UAAAU,MAAN,W,qFACE3D,KAAKM,cAAgB,GACrBN,KAAKgB,MAAQ,G,iBAITnB,EAAAoD,UAAAW,IAAN,SAAU5C,G,qFACRhB,KAAKyC,mBACL,GAAIzB,EAAO,CACThB,KAAK0C,QAAQ1B,E,KACR,CACLhB,KAAK0C,QAAQ1C,KAAKgB,M,CAEpBhB,KAAKgB,MAAQ,G,iBAITnB,EAAAoD,UAAAY,SAAN,W,qFACE7D,KAAK6B,YAAYC,Q,iBAIbjC,EAAAoD,UAAAa,YAAN,W,qFACE9D,KAAK6B,YAAYkC,O,iBAGnBlE,EAAAoD,UAAAe,iBAAA,WACEhE,KAAKuD,kB,EAGP1D,EAAAoD,UAAAgB,kBAAA,WACEjE,KAAKkD,c,EAGCrD,EAAAoD,UAAAQ,cAAA,eAAA1D,EAAAC,KACN,GAAIA,KAAKS,OAAS,QAAS,CACzB,OAAQT,KAAKM,cAAc4D,MAAK,SAACC,GAAS,OAACpE,EAAKqE,aAAaD,EAAnB,G,KACrC,CACL,OAAO,I,GAgBHtE,EAAAoD,UAAAoB,aAAA,WACNrE,KAAKsE,QAAQtC,KAAKhC,KAAKM,eACvB,GAAIN,KAAKM,cAAcyC,OAAS,EAAG,CACjC/C,KAAKuE,UAAUvC,KAAK,CAAEhB,MAAOhB,KAAKM,e,CAEpCN,KAAKyC,mBACLzC,KAAKI,UAAY,MACjB,GAAIJ,KAAKQ,aAAc,CACrBR,KAAK0C,QAAQ1C,KAAKgB,OAClBhB,KAAKgB,MAAQ,E,GAYTnB,EAAAoD,UAAAM,iBAAA,WACN,IAAKvD,KAAKwE,gBAAkBC,UAAW,CACrCzE,KAAKG,cAAgB,I,MAChB,GAAIH,KAAKM,cAAcyC,QAAU/C,KAAKwE,eAAgB,CAC3DxE,KAAKG,cAAgB,MACrBH,KAAK0E,yBAAyB1C,KAAK,CAAEhB,OAAQhB,KAAKG,e,KAC7C,CACLH,KAAKG,cAAgB,I,GAIjBN,EAAAoD,UAAAJ,YAAA,WACN,OAAO7C,KAAKM,cAAcN,KAAKM,cAAcyC,OAAS,E,EAuBhDlD,EAAAoD,UAAAR,iBAAA,eAAA1C,EAAAC,KACN,IAAMgB,EAAQhB,KAAK6B,YAAYb,MAC/BhB,KAAKgB,MAAQA,EAAQA,EAAM2D,OAAS,GAEpC,GAAI3D,EAAM+B,SAAW,EAAG,OAExB,IAAM6B,EAAY5D,EAAM6D,MAAM7E,KAAKY,YACnC,IAAKgE,EAAW,OAEhB,IAAME,EAAW9E,KAAK+E,8BAA8B/D,GACpD,IAAK8D,EAAU,CACb9E,KAAKgF,mBACL,M,CAGF,IAAMC,EAAQH,EAASI,MAAMlF,KAAKY,YAClCqE,EAAME,SAAQ,SAACC,GACbrF,EAAK2C,QAAQ0C,EACf,IAEApF,KAAKgF,kB,EAGCnF,EAAAoD,UAAA8B,8BAAA,SAA8B/D,GACpC,GAAIA,EAAM+B,SAAW,GAAK/B,EAAM,GAAG6D,MAAM7E,KAAKY,YAAa,CACzD,MAAO,E,CAGT,IAAIkE,EAAW9D,EAAMqE,QAAQ,KAAM,KAAKA,QAAQ,UAAW,KAE3D,GAAIP,EAAS,GAAGD,MAAM7E,KAAKY,YAAa,CACtCkE,EAAWA,EAASQ,UAAU,E,CAGhC,OAAOR,C,EAGKjF,EAAAoD,UAAAsC,aAAN,SAAmBhD,G,4GAEbvB,EACRuB,EAAKiD,OAAAxE,MAIThB,KAAKgB,MAAQA,EAAQA,EAAM2D,OAAS,GAEpC,GAAI3D,EAAM+B,SAAW,EAAG,UAElB6B,EAAY5D,EAAM6D,MAAM7E,KAAKY,YACnC,IAAKgE,EAAW,UAEVE,EAAW9E,KAAK+E,8BAA8B/D,GACpD,IAAK8D,EAAU,CACb9E,KAAKgF,mBACL,S,CAGIC,EAAQH,EAASI,MAAMlF,KAAKY,YAClCqE,EAAME,SAAQ,SAACC,GACbrF,EAAK2C,QAAQ0C,EAAKK,YACpB,IAEAzF,KAAKgF,mBACLhF,KAAK2C,UAAUX,KAAK,CAAEY,KAAM5C,KAAKM,cAAeU,MAAOhB,KAAK6C,gBAC5D7C,KAAK8C,eAAed,KAAK,CAAEY,KAAM5C,KAAKM,cAAeU,MAAOhB,KAAK6C,gB,iBAG3DhD,EAAAoD,UAAA+B,iBAAA,SAAiBhE,GAAA,GAAAA,SAAA,GAAAA,EAAA,EAAU,CACjChB,KAAK6B,YAAYb,MAAQA,EACzBhB,KAAKgB,MAAQA,C,EAGPnB,EAAAoD,UAAAP,QAAA,SAAQgD,GACd,IAAK1F,KAAKiB,WAAY,CACpB,IAAM0E,EAAS3F,KAAKM,cAAc4D,MAAK,SAACC,GAAS,OAAAA,EAAKyB,gBAAkBF,EAAKE,aAA5B,IACjD,GAAID,EAAQ,M,CAGd,IAAKE,EAAqBH,GAAO,CAC/B,M,CAGF1F,KAAKM,cAAawF,4BAAA,GAAO9F,KAAKM,cAAa,OAAEoF,GAAI,M,EAG3C7F,EAAAoD,UAAAmB,aAAA,SAAasB,GACnB,IAAMK,EAAcL,EAAKf,OACzB,GAAI3E,KAAKS,OAAS,SAAWuF,EAAgBD,GAAc,CACzD,OAAO,K,CAET,OAAO,I,EAGDlG,EAAAoD,UAAAD,eAAA,WACNhD,KAAKM,cAAgBN,KAAKM,cAAc2F,MAAM,EAAGjG,KAAKM,cAAcyC,OAAS,E,EAGvElD,EAAAoD,UAAAiD,WAAA,SAAW3D,GAEL,IAAA4D,EACR5D,EAAKiD,OAAAW,GAETnG,KAAKM,cAAgBN,KAAKM,cAAc8F,QAAO,SAACC,EAAOC,GAAU,OAAAA,EAAMC,aAAeJ,CAArB,IACjEnG,KAAK2C,UAAUX,KAAK,CAAEY,KAAM5C,KAAKM,cAAeU,MAAOhB,KAAK6C,gBAC5D7C,KAAK8C,eAAed,KAAK,CAAEY,KAAM5C,KAAKM,cAAeU,MAAOhB,KAAK6C,e,EAG3DhD,EAAAoD,UAAAuD,YAAA,eAAAzG,EAAAC,KACN,IAAKA,KAAKM,cAAcyC,OAAQ,CAC9B,MAAO,E,CAGT,OAAO/C,KAAKM,cAAcmG,KAAI,SAACtC,EAAMmC,GACnC,IAAMH,EAAKG,EAAMC,WAEjB,IAAMG,EAAQ,GACd,GAAIvC,EAAKpB,QAAU2D,EAAO,CACxB,OACEC,EAAA,sBACER,GAAIA,EACJ3D,IAAK2D,EACLS,MAAM,UACNC,OAAQ9G,EAAKoB,SACb2F,qBAAsB,SAACvE,GAAU,OAAAxC,EAAKmG,WAAW3D,EAAhB,EACjCb,cAAe3B,EAAK2B,eAEnByC,E,KAGA,CACL,OACEwC,EAAa,eAAAnE,IAAK2D,EAAIY,SAAS,aAAY,eAAe5C,GACxDwC,EACE,sBAAAR,GAAIA,EACJ3D,IAAK2D,EACLS,MAAM,UACNC,OAAQ9G,EAAKoB,SACb2F,qBAAsB,SAACvE,GAAU,OAAAxC,EAAKmG,WAAW3D,EAAhB,EACjCb,cAAe3B,EAAK2B,eAEnB,GAAAsF,OAAG7C,EAAK8B,MAAM,EAAGS,GAAM,Q,CAKlC,G,EAGM7G,EAAAoD,UAAAgE,WAAA,WACN,OACEjH,KAAKW,MACHgG,EAAA,OACEO,MAAO,CACLC,YAAa,KACb,uBAAwBnH,KAAKU,QAG/BiG,EAAU,YAAAS,KAAMpH,KAAKU,MAAQ,SAAW,QAASgF,KAAM1F,KAAKW,KAAMiG,MAAM,Y,EAMxE/G,EAAAoD,UAAAoE,YAAA,WACN,OACErH,KAAKU,OACHiG,EAAA,SACEO,MAAO,CACLI,wBAAyB,KACzB,mCAAoCtH,KAAKI,YAAcJ,KAAKmB,WAG9DwF,EAAA,YAAUY,QAAQ,QAAQC,KAAK,QAC5BxH,KAAKU,O,EAORb,EAAAoD,UAAAwE,cAAA,WACN,IAAM9G,EAAOX,KAAKc,OAAS,QAAUd,KAAKe,QAAU,YAAc,OAClE,IAAI2G,EAAU1H,KAAKc,OAASd,KAAKa,aAAeb,KAAKe,QAAUf,KAAKqB,eAAiBrB,KAAKoB,cAE1F,IAAKsG,GAAW1H,KAAKE,iBAAkBwH,EAAU1H,KAAKK,iBAEtD,IAAMsH,EACJ3H,KAAKc,QAAUd,KAAKE,iBAChB,wCACAF,KAAKe,QACH,yCACA,iBAER,GAAI2G,EAAS,CACX,OACEf,EAAA,OAAKO,MAAOS,EAAQC,KAAK,kBACvBjB,EAAK,OAAAO,MAAM,wBACTP,EAAA,YAAUS,KAAK,UAAU1B,KAAM/E,EAAMkH,MAAM,UAAUjB,MAAM,aAE7DD,EAAA,YAAUO,MAAM,uBAAuBK,QAAQ,SAC5CG,G,CAMT,OAAOjD,S,EAGT5E,EAAAoD,UAAA6E,OAAA,eAAA/H,EAAAC,KACE,IAAMI,EAAYJ,KAAKI,YAAcJ,KAAKmB,SAE1C,IAAM4G,EAAmB/H,KAAKgI,WAAa,OAE3C,OACErB,EAACsB,EAAI,CAAAzF,IAAA,2DAAgBxC,KAAKmB,SAAW,OAAS,MAC5CwF,EAAA,OAAAnE,IAAA,2CACE0E,MAAO,CACL/E,MAAO,KACP,wBAAyBnC,KAAKc,SAAWd,KAAKE,iBAC9C,sBAAuBF,KAAKc,QAAUd,KAAKE,iBAC3C,uBAAwBF,KAAKe,QAC7B,wBAAyBf,KAAKmB,SAC9B,iBAAkBnB,KAAKU,MACvB,iBAAkBN,GAEpB8H,QAASlI,KAAK2B,eACdwG,UAAWnI,KAAKsC,gBAChBsF,KAAK,mBAEJ5H,KAAKiH,aACNN,EAAK,OAAAnE,IAAA,2CAAA0E,MAAM,oBACRlH,KAAKqH,cACNV,EACE,OAAAnE,IAAA,2CAAA0E,MAAM,4BACNkB,MAAO,CAAEJ,UAAWD,IAGnB/H,KAAKM,cAAcyC,OAAS,GAAK/C,KAAKwG,cACtCxG,KAAKG,eACJwG,EACE,SAAAnE,IAAA,2CAAA6F,IAAK,SAAClG,GAAK,OAAMpC,EAAK8B,YAAcM,CAAzB,EACX+E,MAAM,yBACNxB,KAAM1F,KAAKsB,UACXgH,UAAWtI,KAAKsI,UAChB/G,YAAavB,KAAKuB,YAClBU,QAASjC,KAAKiC,QACdL,QAAS5B,KAAK4B,QACd2G,OAAQ,WAAM,OAAAxI,EAAKsE,cAAL,EACdmE,SAAU,WAAM,OAAAzI,EAAKwF,YAAL,EAChBvE,MAAOhB,KAAKgB,MACZG,SAAUnB,KAAKmB,SAAQ,YACZnB,KAAKyB,aAKvBzB,KAAKwB,eACJmF,EAAA,oBAAAnE,IAAA,2CAAkBO,OAAQ/C,KAAKM,cAAcyC,OAAQ0F,IAAKzI,KAAKwE,eAAgBkE,OAAQtI,IAExFJ,KAAKe,SAAW4F,EAAA,YAAAnE,IAAA,2CAAU0E,MAAM,eAAexB,KAAK,SAASmC,MAAM,UAAUT,KAAK,cACnFT,EAAA,QAAAnE,IAAA,2CAAMkD,KAAK,iBAEZ1F,KAAKyH,gB,0KAhlBS,I", "ignoreList": []}