{"version": 3, "names": ["listCss", "ListItem", "exports", "class_1", "hostRef", "_this", "this", "internalChips", "internalActionsButtons", "checked", "typeList", "avatar<PERSON><PERSON>", "avatar<PERSON><PERSON><PERSON><PERSON>", "icon", "value", "text", "secondaryText", "chips", "actionsButtons", "clickable", "active", "borderRadius", "size", "dataTest", "handler", "clickActionButtons", "data", "event", "elementButton", "<PERSON><PERSON><PERSON>", "bdsClickActionButtom", "emit", "prototype", "componentWillLoad", "hasActionAreaSlot", "hostElement", "querySelector", "hasContentAreaSlot", "chipsChanged", "actionsButtonsChanged", "checkedChanged", "isChecked", "bdsChecked", "JSON", "parse", "renderChips", "length", "map", "chip", "index", "id", "toString", "limit", "h", "key", "color", "position", "concat", "slice", "renderActionsButtons", "button", "variant", "onClick", "ev", "render", "hasInput", "hasLeftInput", "has<PERSON><PERSON><PERSON>", "Host", "tabindex", "class", "_a", "list_item", "border_radius", "input_list", "refer", "label", "name", "disabled", "thumbnail", "_b", "theme", "_c", "_d", "tag", "bold", "_e", "_f"], "sources": ["src/components/list/list.scss?tag=bds-list-item&encapsulation=shadow", "src/components/list/list-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { TypeList } from './list';\nexport type ItemSize = 'tall' | 'standard' | 'short';\n@Component({\n  tag: 'bds-list-item',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class ListItem {\n  private hasActionAreaSlot: boolean;\n  private hasContentAreaSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() internalChips: string[] = [];\n\n  @State() internalActionsButtons: string[] = [];\n\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n  /**\n   * Typelis. Used toselect type of item list.\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Value. Used to insert a value in list item.\n   */\n  @Prop() value: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text?: string = null;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * The actions buttons on the component\n   * Should be passed this way:\n   * actions-buttons='[\"copy\", \"settings-general\", \"more-options-horizontal\"]'\n   */\n  @Prop({ mutable: true }) actionsButtons: string | string[] = [];\n\n  /**\n   * Clickable. Used to define if the item is clickable or not.\n   */\n  @Prop() clickable?: boolean = false;\n\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop() active?: boolean = false;\n  /**\n   * Enable rounded border on item\n   */\n  @Prop() borderRadius?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: ItemSize = 'standard';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsChecked!: EventEmitter;\n\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionButtom!: EventEmitter;\n\n  componentWillLoad() {\n    this.hasActionAreaSlot = !!this.hostElement.querySelector('[slot=\"action-area\"]');\n    this.hasContentAreaSlot = !!this.hostElement.querySelector('[slot=\"content-area\"]');\n    this.chipsChanged();\n    this.actionsButtonsChanged();\n  }\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChecked.emit({\n      value: this.value,\n      text: this.text,\n      secondaryText: this.secondaryText,\n      typeList: this.typeList,\n      checked: isChecked,\n    });\n  }\n\n  @Watch('chips')\n  protected chipsChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        this.internalChips = JSON.parse(this.chips);\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('actionsButtons')\n  protected actionsButtonsChanged(): void {\n    if (this.actionsButtons) {\n      if (typeof this.actionsButtons === 'string') {\n        this.internalActionsButtons = JSON.parse(this.actionsButtons);\n      } else {\n        this.internalActionsButtons = this.actionsButtons;\n      }\n    } else {\n      this.internalActionsButtons = [];\n    }\n  }\n\n  private handler = (): void => {\n    this.typeList == 'radio' ? (this.checked = true) : (this.checked = !this.checked);\n  };\n\n  private clickActionButtons = (data, event): void => {\n    const elementButton = event.composedPath()[0];\n    this.bdsClickActionButtom.emit({\n      value: this.value,\n      icon: data,\n      elementButton: elementButton,\n    });\n  };\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable id={id} key={id} color=\"default\">\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable id={id} key={id} color=\"default\">\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderActionsButtons() {\n    if (!this.internalActionsButtons.length) {\n      return [];\n    }\n\n    return this.internalActionsButtons.map((button, index) => {\n      const id = index.toString();\n      return (\n        <bds-button-icon\n          key={id}\n          variant=\"secondary\"\n          icon={button}\n          size=\"short\"\n          onClick={(ev) => this.clickActionButtons(button, ev)}\n        ></bds-button-icon>\n      );\n    });\n  }\n\n  render() {\n    const hasInput =\n      this.clickable == true || this.typeList == 'checkbox' || this.typeList == 'radio' || this.typeList == 'switch';\n    const hasLeftInput = this.typeList == 'checkbox' || this.typeList == 'radio';\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <Host>\n        <div\n          onClick={this.handler}\n          tabindex=\"0\"\n          class={{\n            list_item: true,\n            clickable: hasInput,\n            border_radius: this.borderRadius,\n            [`list_item_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.active && <div class=\"active\"></div>}\n          {hasLeftInput && (\n            <div class={{ input_list: true }}>\n              {this.typeList == 'radio' && <bds-radio value={this.value} checked={this.checked}></bds-radio>}\n              {this.typeList == 'checkbox' && (\n                <bds-checkbox refer=\"\" label=\"\" name=\"cb1\" disabled={false} checked={this.checked}></bds-checkbox>\n              )}\n            </div>\n          )}\n          {hasAvatar ? (\n            <bds-avatar\n              class=\"avatar-item\"\n              name={this.avatarName}\n              thumbnail={this.avatarThumbnail}\n              size=\"extra-small\"\n            ></bds-avatar>\n          ) : (\n            this.icon && (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.active,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme={this.active ? 'solid' : 'outline'}\n              ></bds-icon>\n            )\n          )}\n          <div class={{ [`content-slot`]: true }}>\n            <slot></slot>\n          </div>\n          {(this.text || this.secondaryText) && (\n            <div\n              class={{\n                [`content-item`]: true,\n                [`grow-up`]: !this.hasActionAreaSlot && !this.hasContentAreaSlot && this.internalChips.length < 0,\n              }}\n            >\n              {this.text && (\n                <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\" bold={this.active ? 'bold' : 'regular'}>\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo class=\"subtitle-item\" variant=\"fs-12\" line-height=\"small\" tag=\"span\">\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n          )}\n          <div class={{ [`content-area`]: true, [`grow-up`]: true }}>\n            {this.internalChips.length > 0 && <div class=\"internal-chips\">{this.renderChips()}</div>}\n            <slot name=\"content-area\"></slot>\n          </div>\n          {(!this.typeList || this.typeList == 'default') && (\n            <div class={{ [`action-area`]: true }}>\n              {this.internalActionsButtons.length > 0 && (\n                <div class=\"internal-actions-buttons\">{this.renderActionsButtons()}</div>\n              )}\n              <slot name=\"action-area\"></slot>\n            </div>\n          )}\n          {this.typeList == 'switch' && <bds-switch refer=\"\" name=\"\" checked={this.checked}></bds-switch>}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAA,IAAMA,EAAU,g1E,ICQHC,EAAQC,EAAA,2BALrB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,wGAWWA,KAAaC,cAAa,GAE1BD,KAAsBE,uBAAa,GAEJF,KAAOG,QAAa,MAIpDH,KAAQI,SAAc,KAItBJ,KAAUK,WAAY,KAItBL,KAAeM,gBAAY,KAI3BN,KAAIO,KAAY,KAIhBP,KAAKQ,MAAW,KAIhBR,KAAIS,KAAY,KAIhBT,KAAaU,cAAY,KAORV,KAAKW,MAAsB,GAO3BX,KAAcY,eAAsB,GAKrDZ,KAASa,UAAa,MAKtBb,KAAMc,OAAa,MAInBd,KAAYe,aAAa,MAMzBf,KAAIgB,KAAc,WAIlBhB,KAAQiB,SAAY,KAuDpBjB,KAAOkB,QAAG,WAChBnB,EAAKK,UAAY,QAAWL,EAAKI,QAAU,KAASJ,EAAKI,SAAWJ,EAAKI,OAC3E,EAEQH,KAAAmB,mBAAqB,SAACC,EAAMC,GAClC,IAAMC,EAAgBD,EAAME,eAAe,GAC3CxB,EAAKyB,qBAAqBC,KAAK,CAC7BjB,MAAOT,EAAKS,MACZD,KAAMa,EACNE,cAAeA,GAEnB,CAsID,CA7LCzB,EAAA6B,UAAAC,kBAAA,WACE3B,KAAK4B,oBAAsB5B,KAAK6B,YAAYC,cAAc,wBAC1D9B,KAAK+B,qBAAuB/B,KAAK6B,YAAYC,cAAc,yBAC3D9B,KAAKgC,eACLhC,KAAKiC,uB,EAIGpC,EAAA6B,UAAAQ,eAAA,SAAeC,GACvBnC,KAAKoC,WAAWX,KAAK,CACnBjB,MAAOR,KAAKQ,MACZC,KAAMT,KAAKS,KACXC,cAAeV,KAAKU,cACpBN,SAAUJ,KAAKI,SACfD,QAASgC,G,EAKHtC,EAAA6B,UAAAM,aAAA,WACR,GAAIhC,KAAKW,MAAO,CACd,UAAWX,KAAKW,QAAU,SAAU,CAClCX,KAAKC,cAAgBoC,KAAKC,MAAMtC,KAAKW,M,KAChC,CACLX,KAAKC,cAAgBD,KAAKW,K,MAEvB,CACLX,KAAKC,cAAgB,E,GAKfJ,EAAA6B,UAAAO,sBAAA,WACR,GAAIjC,KAAKY,eAAgB,CACvB,UAAWZ,KAAKY,iBAAmB,SAAU,CAC3CZ,KAAKE,uBAAyBmC,KAAKC,MAAMtC,KAAKY,e,KACzC,CACLZ,KAAKE,uBAAyBF,KAAKY,c,MAEhC,CACLZ,KAAKE,uBAAyB,E,GAiB1BL,EAAA6B,UAAAa,YAAA,WACN,IAAKvC,KAAKC,cAAcuC,OAAQ,CAC9B,MAAO,E,CAGT,OAAOxC,KAAKC,cAAcwC,KAAI,SAACC,EAAMC,GACnC,IAAMC,EAAKD,EAAME,WACjB,IAAMC,EAAQ,GACd,GAAIJ,EAAKF,QAAUM,EAAO,CACxB,OACEC,EAAoB,sBAAAH,GAAIA,EAAII,IAAKJ,EAAIK,MAAM,WACxCP,E,KAGA,CACL,OACEK,EAAa,eAAAC,IAAKJ,EAAIM,SAAS,aAAY,eAAeR,GACxDK,EAAoB,sBAAAH,GAAIA,EAAII,IAAKJ,EAAIK,MAAM,WACxC,GAAAE,OAAGT,EAAKU,MAAM,EAAGN,GAAM,S,CAKlC,G,EAGMjD,EAAA6B,UAAA2B,qBAAA,eAAAtD,EAAAC,KACN,IAAKA,KAAKE,uBAAuBsC,OAAQ,CACvC,MAAO,E,CAGT,OAAOxC,KAAKE,uBAAuBuC,KAAI,SAACa,EAAQX,GAC9C,IAAMC,EAAKD,EAAME,WACjB,OACEE,EAAA,mBACEC,IAAKJ,EACLW,QAAQ,YACRhD,KAAM+C,EACNtC,KAAK,QACLwC,QAAS,SAACC,GAAO,OAAA1D,EAAKoB,mBAAmBmC,EAAQG,EAAhC,GAGvB,G,EAGF5D,EAAA6B,UAAAgC,OAAA,W,gBACE,IAAMC,EACJ3D,KAAKa,WAAa,MAAQb,KAAKI,UAAY,YAAcJ,KAAKI,UAAY,SAAWJ,KAAKI,UAAY,SACxG,IAAMwD,EAAe5D,KAAKI,UAAY,YAAcJ,KAAKI,UAAY,QACrE,IAAMyD,EAAY7D,KAAKK,YAAcL,KAAKM,gBAC1C,OACEyC,EAACe,EAAI,CAAAd,IAAA,4CACHD,EACE,OAAAC,IAAA,2CAAAQ,QAASxD,KAAKkB,QACd6C,SAAS,IACTC,OAAKC,EAAA,CACHC,UAAW,KACXrD,UAAW8C,EACXQ,cAAenE,KAAKe,cACpBkD,EAAC,aAAAd,OAAanD,KAAKgB,OAAS,K,GAEnB,YAAAhB,KAAKiB,UAEfjB,KAAKc,QAAUiC,EAAA,OAAAC,IAAA,2CAAKgB,MAAM,WAC1BJ,GACCb,EAAK,OAAAC,IAAA,2CAAAgB,MAAO,CAAEI,WAAY,OACvBpE,KAAKI,UAAY,SAAW2C,EAAA,aAAAC,IAAA,2CAAWxC,MAAOR,KAAKQ,MAAOL,QAASH,KAAKG,UACxEH,KAAKI,UAAY,YAChB2C,EAAc,gBAAAC,IAAA,2CAAAqB,MAAM,GAAGC,MAAM,GAAGC,KAAK,MAAMC,SAAU,MAAOrE,QAASH,KAAKG,WAI/E0D,EACCd,EAAA,cACEiB,MAAM,cACNO,KAAMvE,KAAKK,WACXoE,UAAWzE,KAAKM,gBAChBU,KAAK,gBAGPhB,KAAKO,MACHwC,EAAA,YACEiB,OAAKU,EAAA,GACHA,EAAC,aAAc,KACfA,EAAC,oBAAqB1E,KAAKc,O,GAE7BE,KAAK,SACLuD,KAAMvE,KAAKO,KACX0C,MAAM,UACN0B,MAAO3E,KAAKc,OAAS,QAAU,YAIrCiC,EAAK,OAAAC,IAAA,2CAAAgB,OAAKY,EAAA,GAAIA,EAAC,gBAAiB,KAAIA,IAClC7B,EAAA,QAAAC,IAAA,+CAEAhD,KAAKS,MAAQT,KAAKU,gBAClBqC,EACE,OAAAC,IAAA,2CAAAgB,OAAKa,EAAA,GACHA,EAAC,gBAAiB,KAClBA,EAAC,YAAa7E,KAAK4B,oBAAsB5B,KAAK+B,oBAAsB/B,KAAKC,cAAcuC,OAAS,E,IAGjGxC,KAAKS,MACJsC,EAAA,YAAAC,IAAA,2CAAUgB,MAAM,aAAaT,QAAQ,QAAQuB,IAAI,OAAOC,KAAM/E,KAAKc,OAAS,OAAS,WAClFd,KAAKS,MAGTT,KAAKU,eACJqC,EAAA,YAAAC,IAAA,2CAAUgB,MAAM,gBAAgBT,QAAQ,QAAO,cAAa,QAAQuB,IAAI,QACrE9E,KAAKU,gBAKdqC,EAAA,OAAAC,IAAA,2CAAKgB,OAAKgB,EAAA,GAAIA,EAAC,gBAAiB,KAAMA,EAAC,WAAY,KAAIA,IACpDhF,KAAKC,cAAcuC,OAAS,GAAKO,EAAA,OAAAC,IAAA,2CAAKgB,MAAM,kBAAkBhE,KAAKuC,eACpEQ,EAAA,QAAAC,IAAA,2CAAMuB,KAAK,oBAEVvE,KAAKI,UAAYJ,KAAKI,UAAY,YACnC2C,EAAA,OAAAC,IAAA,2CAAKgB,OAAKiB,EAAA,GAAIA,EAAC,eAAgB,KAAIA,IAChCjF,KAAKE,uBAAuBsC,OAAS,GACpCO,EAAA,OAAAC,IAAA,2CAAKgB,MAAM,4BAA4BhE,KAAKqD,wBAE9CN,EAAA,QAAAC,IAAA,2CAAMuB,KAAK,iBAGdvE,KAAKI,UAAY,UAAY2C,EAAY,cAAAC,IAAA,2CAAAqB,MAAM,GAAGE,KAAK,GAAGpE,QAASH,KAAKG,W,4TA/Q9D,I", "ignoreList": []}