import{r as t,c as i,h as e,a as o}from"./p-C3J6Z5OX.js";import{g as r,p as s}from"./p-BNEKIkjk.js";const n=':host{display:block}.element_input{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.element_input input{-webkit-box-shadow:inherit;box-shadow:inherit}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 8px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;gap:8px}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 7px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;padding:2px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;width:100%;line-height:22px;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-ghost, rgb(140, 140, 140));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.select{position:relative;outline:none}.select__icon{color:var(--color-content-ghost, rgb(140, 140, 140));display:-ms-flexbox;display:flex}.select__options{background:var(--color-surface-0, rgb(255, 255, 255));width:100%;max-height:200px;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:absolute;left:0;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));overflow-y:auto;z-index:2;pointer-events:none;opacity:0}.select__options::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.select__options::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.select__options--open{opacity:1;pointer-events:auto}.select__options--position-top{bottom:calc(100% + 4px)}.select__options--position-bottom{top:calc(100% + 4px)}.inside-input-left{display:-ms-inline-flexbox;display:inline-flex;gap:8px;-ms-flex-wrap:wrap;flex-wrap:wrap;max-height:200px;overflow-y:auto}.inside-input-left::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.inside-input-left::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input-chips__chip{margin:2px 4px 2px 0px}.input-chips__chips{-ms-flex:1;flex:1}';const a=class{constructor(e){t(this,e);this.bdsChange=i(this,"bdsChange");this.bdsCancel=i(this,"bdsCancel");this.bdsFocus=i(this,"bdsFocus");this.bdsBlur=i(this,"bdsBlur");this.intoView=null;this.isOpen=false;this.text="";this.validationDanger=false;this.isPressed=false;this.validationMesage="";this.danger=false;this.success=false;this.disabled=false;this.label="";this.icon="";this.placeholder="";this.helperMessage="";this.errorMessage="";this.successMessage="";this.optionsPosition="auto";this.dataTest=null;this.refNativeInput=t=>{this.nativeInput=t};this.refDropdown=t=>{this.dropElement=t};this.refIconDrop=t=>{this.iconDropElement=t};this.onClickWrapper=()=>{this.onFocus();this.isOpen=true;if(this.nativeInput){this.nativeInput.focus()}};this.onFocus=()=>{this.bdsFocus.emit();this.isPressed=true};this.onBlur=()=>{this.bdsBlur.emit();this.isPressed=false};this.toggle=()=>{if(!this.disabled){this.isOpen=!this.isOpen}};this.getText=t=>{var i;const e=this.childOptions.find((i=>i.value==t));if(this.internalOptions){const t=this.internalOptions.find((t=>t.value==(e===null||e===void 0?void 0:e.value)));if(t){return t.titleText?t.titleText:t.label}}return(e===null||e===void 0?void 0:e.titleText)?e===null||e===void 0?void 0:e.titleText:(i=e===null||e===void 0?void 0:e.innerText)!==null&&i!==void 0?i:""};this.handler=t=>{const{detail:{value:i}}=t;this.value=i;this.toggle()}}isOpenChanged(t){if(this.positionHeightDrop=="bottom"){this.iconDropElement.name=this.isOpen?"arrow-up":"arrow-down"}else{this.iconDropElement.name=this.isOpen?"arrow-down":"arrow-up"}if(t)if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}}valueChanged(){this.bdsChange.emit({value:this.value});for(const t of this.childOptions){t.selected=this.value===t.value}this.text=this.getText(this.value)}handleWindow(t){const i=t.composedPath();if(!i.find((t=>t==this.el))){this.isOpen=false}}componentWillLoad(){this.options&&this.optionsChanged();this.intoView=r(this.el)}componentWillRender(){this.options&&this.updateOptions();this.getValueSelected()}componentDidLoad(){this.getValueSelected();if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}}setDefaultPlacement(t){if(t=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}}validatePositionDrop(){const t=s({actionElement:this.el,changedElement:this.dropElement,intoView:this.intoView});this.positionHeightDrop=t.y;if(t.y=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}}optionsChanged(){this.updateOptions()}getValueSelected(){for(const t of this.childOptions){t.selected=this.value===t.value;t.addEventListener("optionSelected",this.handler)}this.text=this.getText(this.value)}updateOptions(){if(this.options){if(typeof this.options==="string"){this.internalOptions=JSON.parse(this.options)}else{this.internalOptions=this.options}}}get childOptions(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option")):Array.from(this.el.querySelectorAll("bds-select-option"))}get childOptionSelected(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option")).find((t=>t.selected)):Array.from(this.el.querySelectorAll("bds-select-option")).find((t=>t.selected))}keyPressWrapper(t){var i,e,o,r;switch(t.key){case"Enter":this.toggle();break;case"ArrowDown":if(!this.disabled){this.isOpen=true}if(this.childOptionSelected){this.value=(i=this.childOptionSelected.nextSibling)===null||i===void 0?void 0:i.value;return}this.value=(e=this.el.firstElementChild)===null||e===void 0?void 0:e.value;break;case"ArrowUp":if(this.childOptionSelected){this.value=(o=this.childOptionSelected.previousSibling)===null||o===void 0?void 0:o.value;return}this.value=(r=this.el.lastElementChild)===null||r===void 0?void 0:r.value;break}}renderIcon(){return this.icon&&e("div",{class:{input__icon:true,"input__icon--large":!!this.label}},e("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))}renderLabel(){return this.label&&e("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},e("bds-typo",{variant:"fs-12",bold:"bold"},this.label))}renderMessage(){const t=this.danger?"error":this.success?"checkball":"info";let i=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!i&&this.validationDanger)i=this.validationMesage;const o=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(i){return e("div",{class:o,part:"input__message"},e("div",{class:"input__message__icon"},e("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),e("bds-typo",{class:"input__message__text",variant:"fs-12"},i))}return undefined}render(){const t=this.isPressed&&!this.disabled;return e("div",{key:"5b257abab1684e682b2e5a4ece279838ded86426",class:"select"},e("div",{key:"076dac2ca0f3067b38c428e4a0bd5df9882fcbaa",class:{element_input:true},"aria-disabled":this.disabled?"true":null},e("div",{key:"bd64f6afe771e27f66200db9ed1f42f5e32ce87c",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":t},onClick:this.onClickWrapper,part:"input-container"},this.renderIcon(),e("div",{key:"9c940b2ac9b9713055d5b5c260fe5d21e19dfad8",class:"input__container"},this.renderLabel(),e("div",{key:"74ec281d8636c121e10e7fb7885d47888c2692fd",class:{input__container__wrapper:true}},e("input",{key:"f962d90b9e465c81848f460bcd40d6868643f3a0",ref:this.refNativeInput,class:{input__container__text:true},onFocus:this.onFocus,onBlur:this.onBlur,value:this.text,disabled:this.disabled,placeholder:this.placeholder,readonly:true,"data-test":this.dataTest,onKeyDown:this.keyPressWrapper.bind(this)}))),e("div",{key:"6c493aaf4bc617ea4694e15a4495ebb8d5a66031",class:"select__icon"},e("bds-icon",{key:"b22854217a0a2c790c1ed37adfdd2f0a2ab5e231",ref:t=>this.refIconDrop(t),size:"small",color:"inherit"})),this.success&&e("bds-icon",{key:"706bc4903f5c844cdf1b5a567e4b0c1651198c81",class:"icon-success",name:"check",theme:"outline",size:"xxx-small"})),this.renderMessage()),e("div",{key:"23e7d8e882712901a58228f82e6ccbfbc42893f5",ref:t=>this.refDropdown(t),class:{select__options:true,"select__options--open":this.isOpen},role:"application"},this.internalOptions?this.internalOptions.map(((t,i)=>t.icon||t.titleText?e("bds-select-option",{key:i,value:t.value,"title-text":t.titleText,"slot-align":t.slotAlign,bulkOption:t.bulkOption,status:t.status},t.icon&&e("bds-icon",{slot:"input-left",name:t.icon,size:"medium",color:t.iconColor}),t.label):e("bds-select-option",{key:i,value:t.value,bulkOption:t.bulkOption,status:t.status},t.label))):e("slot",null)))}get el(){return o(this)}static get watchers(){return{isOpen:["isOpenChanged"],value:["valueChanged"],options:["optionsChanged"]}}};a.style=n;export{a as bds_select};
//# sourceMappingURL=p-6fe5d3ce.entry.js.map