{"version": 3, "names": ["listCss", "ListItem", "constructor", "hostRef", "this", "internalChips", "internalActionsButtons", "checked", "typeList", "avatar<PERSON><PERSON>", "avatar<PERSON><PERSON><PERSON><PERSON>", "icon", "value", "text", "secondaryText", "chips", "actionsButtons", "clickable", "active", "borderRadius", "size", "dataTest", "handler", "clickActionButtons", "data", "event", "elementButton", "<PERSON><PERSON><PERSON>", "bdsClickActionButtom", "emit", "componentWillLoad", "hasActionAreaSlot", "hostElement", "querySelector", "hasContentAreaSlot", "chipsChanged", "actionsButtonsChanged", "checkedChanged", "isChecked", "bdsChecked", "JSON", "parse", "renderChips", "length", "map", "chip", "index", "id", "toString", "limit", "h", "key", "color", "position", "slice", "renderActionsButtons", "button", "variant", "onClick", "ev", "render", "hasInput", "hasLeftInput", "has<PERSON><PERSON><PERSON>", "Host", "tabindex", "class", "list_item", "border_radius", "input_list", "refer", "label", "name", "disabled", "thumbnail", "theme", "tag", "bold"], "sources": ["src/components/list/list.scss?tag=bds-list-item&encapsulation=shadow", "src/components/list/list-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { TypeList } from './list';\nexport type ItemSize = 'tall' | 'standard' | 'short';\n@Component({\n  tag: 'bds-list-item',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class ListItem {\n  private hasActionAreaSlot: boolean;\n  private hasContentAreaSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() internalChips: string[] = [];\n\n  @State() internalActionsButtons: string[] = [];\n\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n  /**\n   * Typelis. Used toselect type of item list.\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Value. Used to insert a value in list item.\n   */\n  @Prop() value: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text?: string = null;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * The actions buttons on the component\n   * Should be passed this way:\n   * actions-buttons='[\"copy\", \"settings-general\", \"more-options-horizontal\"]'\n   */\n  @Prop({ mutable: true }) actionsButtons: string | string[] = [];\n\n  /**\n   * Clickable. Used to define if the item is clickable or not.\n   */\n  @Prop() clickable?: boolean = false;\n\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop() active?: boolean = false;\n  /**\n   * Enable rounded border on item\n   */\n  @Prop() borderRadius?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: ItemSize = 'standard';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsChecked!: EventEmitter;\n\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionButtom!: EventEmitter;\n\n  componentWillLoad() {\n    this.hasActionAreaSlot = !!this.hostElement.querySelector('[slot=\"action-area\"]');\n    this.hasContentAreaSlot = !!this.hostElement.querySelector('[slot=\"content-area\"]');\n    this.chipsChanged();\n    this.actionsButtonsChanged();\n  }\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChecked.emit({\n      value: this.value,\n      text: this.text,\n      secondaryText: this.secondaryText,\n      typeList: this.typeList,\n      checked: isChecked,\n    });\n  }\n\n  @Watch('chips')\n  protected chipsChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        this.internalChips = JSON.parse(this.chips);\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('actionsButtons')\n  protected actionsButtonsChanged(): void {\n    if (this.actionsButtons) {\n      if (typeof this.actionsButtons === 'string') {\n        this.internalActionsButtons = JSON.parse(this.actionsButtons);\n      } else {\n        this.internalActionsButtons = this.actionsButtons;\n      }\n    } else {\n      this.internalActionsButtons = [];\n    }\n  }\n\n  private handler = (): void => {\n    this.typeList == 'radio' ? (this.checked = true) : (this.checked = !this.checked);\n  };\n\n  private clickActionButtons = (data, event): void => {\n    const elementButton = event.composedPath()[0];\n    this.bdsClickActionButtom.emit({\n      value: this.value,\n      icon: data,\n      elementButton: elementButton,\n    });\n  };\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable id={id} key={id} color=\"default\">\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable id={id} key={id} color=\"default\">\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderActionsButtons() {\n    if (!this.internalActionsButtons.length) {\n      return [];\n    }\n\n    return this.internalActionsButtons.map((button, index) => {\n      const id = index.toString();\n      return (\n        <bds-button-icon\n          key={id}\n          variant=\"secondary\"\n          icon={button}\n          size=\"short\"\n          onClick={(ev) => this.clickActionButtons(button, ev)}\n        ></bds-button-icon>\n      );\n    });\n  }\n\n  render() {\n    const hasInput =\n      this.clickable == true || this.typeList == 'checkbox' || this.typeList == 'radio' || this.typeList == 'switch';\n    const hasLeftInput = this.typeList == 'checkbox' || this.typeList == 'radio';\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <Host>\n        <div\n          onClick={this.handler}\n          tabindex=\"0\"\n          class={{\n            list_item: true,\n            clickable: hasInput,\n            border_radius: this.borderRadius,\n            [`list_item_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.active && <div class=\"active\"></div>}\n          {hasLeftInput && (\n            <div class={{ input_list: true }}>\n              {this.typeList == 'radio' && <bds-radio value={this.value} checked={this.checked}></bds-radio>}\n              {this.typeList == 'checkbox' && (\n                <bds-checkbox refer=\"\" label=\"\" name=\"cb1\" disabled={false} checked={this.checked}></bds-checkbox>\n              )}\n            </div>\n          )}\n          {hasAvatar ? (\n            <bds-avatar\n              class=\"avatar-item\"\n              name={this.avatarName}\n              thumbnail={this.avatarThumbnail}\n              size=\"extra-small\"\n            ></bds-avatar>\n          ) : (\n            this.icon && (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.active,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme={this.active ? 'solid' : 'outline'}\n              ></bds-icon>\n            )\n          )}\n          <div class={{ [`content-slot`]: true }}>\n            <slot></slot>\n          </div>\n          {(this.text || this.secondaryText) && (\n            <div\n              class={{\n                [`content-item`]: true,\n                [`grow-up`]: !this.hasActionAreaSlot && !this.hasContentAreaSlot && this.internalChips.length < 0,\n              }}\n            >\n              {this.text && (\n                <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\" bold={this.active ? 'bold' : 'regular'}>\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo class=\"subtitle-item\" variant=\"fs-12\" line-height=\"small\" tag=\"span\">\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n          )}\n          <div class={{ [`content-area`]: true, [`grow-up`]: true }}>\n            {this.internalChips.length > 0 && <div class=\"internal-chips\">{this.renderChips()}</div>}\n            <slot name=\"content-area\"></slot>\n          </div>\n          {(!this.typeList || this.typeList == 'default') && (\n            <div class={{ [`action-area`]: true }}>\n              {this.internalActionsButtons.length > 0 && (\n                <div class=\"internal-actions-buttons\">{this.renderActionsButtons()}</div>\n              )}\n              <slot name=\"action-area\"></slot>\n            </div>\n          )}\n          {this.typeList == 'switch' && <bds-switch refer=\"\" name=\"\" checked={this.checked}></bds-switch>}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "2DAAA,MAAMA,EAAU,g1E,MCQHC,EAAQ,MALrB,WAAAC,CAAAC,G,wGAWWC,KAAaC,cAAa,GAE1BD,KAAsBE,uBAAa,GAEJF,KAAOG,QAAa,MAIpDH,KAAQI,SAAc,KAItBJ,KAAUK,WAAY,KAItBL,KAAeM,gBAAY,KAI3BN,KAAIO,KAAY,KAIhBP,KAAKQ,MAAW,KAIhBR,KAAIS,KAAY,KAIhBT,KAAaU,cAAY,KAORV,KAAKW,MAAsB,GAO3BX,KAAcY,eAAsB,GAKrDZ,KAASa,UAAa,MAKtBb,KAAMc,OAAa,MAInBd,KAAYe,aAAa,MAMzBf,KAAIgB,KAAc,WAIlBhB,KAAQiB,SAAY,KAuDpBjB,KAAOkB,QAAG,KAChBlB,KAAKI,UAAY,QAAWJ,KAAKG,QAAU,KAASH,KAAKG,SAAWH,KAAKG,OAAQ,EAG3EH,KAAAmB,mBAAqB,CAACC,EAAMC,KAClC,MAAMC,EAAgBD,EAAME,eAAe,GAC3CvB,KAAKwB,qBAAqBC,KAAK,CAC7BjB,MAAOR,KAAKQ,MACZD,KAAMa,EACNE,cAAeA,GACf,CAuIL,CA7LC,iBAAAI,GACE1B,KAAK2B,oBAAsB3B,KAAK4B,YAAYC,cAAc,wBAC1D7B,KAAK8B,qBAAuB9B,KAAK4B,YAAYC,cAAc,yBAC3D7B,KAAK+B,eACL/B,KAAKgC,uB,CAIG,cAAAC,CAAeC,GACvBlC,KAAKmC,WAAWV,KAAK,CACnBjB,MAAOR,KAAKQ,MACZC,KAAMT,KAAKS,KACXC,cAAeV,KAAKU,cACpBN,SAAUJ,KAAKI,SACfD,QAAS+B,G,CAKH,YAAAH,GACR,GAAI/B,KAAKW,MAAO,CACd,UAAWX,KAAKW,QAAU,SAAU,CAClCX,KAAKC,cAAgBmC,KAAKC,MAAMrC,KAAKW,M,KAChC,CACLX,KAAKC,cAAgBD,KAAKW,K,MAEvB,CACLX,KAAKC,cAAgB,E,EAKf,qBAAA+B,GACR,GAAIhC,KAAKY,eAAgB,CACvB,UAAWZ,KAAKY,iBAAmB,SAAU,CAC3CZ,KAAKE,uBAAyBkC,KAAKC,MAAMrC,KAAKY,e,KACzC,CACLZ,KAAKE,uBAAyBF,KAAKY,c,MAEhC,CACLZ,KAAKE,uBAAyB,E,EAiB1B,WAAAoC,GACN,IAAKtC,KAAKC,cAAcsC,OAAQ,CAC9B,MAAO,E,CAGT,OAAOvC,KAAKC,cAAcuC,KAAI,CAACC,EAAMC,KACnC,MAAMC,EAAKD,EAAME,WACjB,MAAMC,EAAQ,GACd,GAAIJ,EAAKF,QAAUM,EAAO,CACxB,OACEC,EAAoB,sBAAAH,GAAIA,EAAII,IAAKJ,EAAIK,MAAM,WACxCP,E,KAGA,CACL,OACEK,EAAa,eAAAC,IAAKJ,EAAIM,SAAS,aAAY,eAAeR,GACxDK,EAAoB,sBAAAH,GAAIA,EAAII,IAAKJ,EAAIK,MAAM,WACxC,GAAGP,EAAKS,MAAM,EAAGL,U,KAQtB,oBAAAM,GACN,IAAKnD,KAAKE,uBAAuBqC,OAAQ,CACvC,MAAO,E,CAGT,OAAOvC,KAAKE,uBAAuBsC,KAAI,CAACY,EAAQV,KAC9C,MAAMC,EAAKD,EAAME,WACjB,OACEE,EAAA,mBACEC,IAAKJ,EACLU,QAAQ,YACR9C,KAAM6C,EACNpC,KAAK,QACLsC,QAAUC,GAAOvD,KAAKmB,mBAAmBiC,EAAQG,IAChC,G,CAKzB,MAAAC,GACE,MAAMC,EACJzD,KAAKa,WAAa,MAAQb,KAAKI,UAAY,YAAcJ,KAAKI,UAAY,SAAWJ,KAAKI,UAAY,SACxG,MAAMsD,EAAe1D,KAAKI,UAAY,YAAcJ,KAAKI,UAAY,QACrE,MAAMuD,EAAY3D,KAAKK,YAAcL,KAAKM,gBAC1C,OACEwC,EAACc,EAAI,CAAAb,IAAA,4CACHD,EACE,OAAAC,IAAA,2CAAAO,QAAStD,KAAKkB,QACd2C,SAAS,IACTC,MAAO,CACLC,UAAW,KACXlD,UAAW4C,EACXO,cAAehE,KAAKe,aACpB,CAAC,aAAaf,KAAKgB,QAAS,MAEnB,YAAAhB,KAAKiB,UAEfjB,KAAKc,QAAUgC,EAAA,OAAAC,IAAA,2CAAKe,MAAM,WAC1BJ,GACCZ,EAAK,OAAAC,IAAA,2CAAAe,MAAO,CAAEG,WAAY,OACvBjE,KAAKI,UAAY,SAAW0C,EAAA,aAAAC,IAAA,2CAAWvC,MAAOR,KAAKQ,MAAOL,QAASH,KAAKG,UACxEH,KAAKI,UAAY,YAChB0C,EAAc,gBAAAC,IAAA,2CAAAmB,MAAM,GAAGC,MAAM,GAAGC,KAAK,MAAMC,SAAU,MAAOlE,QAASH,KAAKG,WAI/EwD,EACCb,EAAA,cACEgB,MAAM,cACNM,KAAMpE,KAAKK,WACXiE,UAAWtE,KAAKM,gBAChBU,KAAK,gBAGPhB,KAAKO,MACHuC,EAAA,YACEgB,MAAO,CACL,CAAC,aAAc,KACf,CAAC,oBAAqB9D,KAAKc,QAE7BE,KAAK,SACLoD,KAAMpE,KAAKO,KACXyC,MAAM,UACNuB,MAAOvE,KAAKc,OAAS,QAAU,YAIrCgC,EAAK,OAAAC,IAAA,2CAAAe,MAAO,CAAE,CAAC,gBAAiB,OAC9BhB,EAAA,QAAAC,IAAA,+CAEA/C,KAAKS,MAAQT,KAAKU,gBAClBoC,EACE,OAAAC,IAAA,2CAAAe,MAAO,CACL,CAAC,gBAAiB,KAClB,CAAC,YAAa9D,KAAK2B,oBAAsB3B,KAAK8B,oBAAsB9B,KAAKC,cAAcsC,OAAS,IAGjGvC,KAAKS,MACJqC,EAAA,YAAAC,IAAA,2CAAUe,MAAM,aAAaT,QAAQ,QAAQmB,IAAI,OAAOC,KAAMzE,KAAKc,OAAS,OAAS,WAClFd,KAAKS,MAGTT,KAAKU,eACJoC,EAAA,YAAAC,IAAA,2CAAUe,MAAM,gBAAgBT,QAAQ,QAAO,cAAa,QAAQmB,IAAI,QACrExE,KAAKU,gBAKdoC,EAAA,OAAAC,IAAA,2CAAKe,MAAO,CAAE,CAAC,gBAAiB,KAAM,CAAC,WAAY,OAChD9D,KAAKC,cAAcsC,OAAS,GAAKO,EAAA,OAAAC,IAAA,2CAAKe,MAAM,kBAAkB9D,KAAKsC,eACpEQ,EAAA,QAAAC,IAAA,2CAAMqB,KAAK,oBAEVpE,KAAKI,UAAYJ,KAAKI,UAAY,YACnC0C,EAAA,OAAAC,IAAA,2CAAKe,MAAO,CAAE,CAAC,eAAgB,OAC5B9D,KAAKE,uBAAuBqC,OAAS,GACpCO,EAAA,OAAAC,IAAA,2CAAKe,MAAM,4BAA4B9D,KAAKmD,wBAE9CL,EAAA,QAAAC,IAAA,2CAAMqB,KAAK,iBAGdpE,KAAKI,UAAY,UAAY0C,EAAY,cAAAC,IAAA,2CAAAmB,MAAM,GAAGE,KAAK,GAAGjE,QAASH,KAAKG,W", "ignoreList": []}