{"version": 3, "names": ["listCss", "ListItemContent", "exports", "class_1", "hostRef", "this", "direction", "justifyContent", "flexWrap", "alignItems", "prototype", "render", "h", "Host", "key", "class", "list_item_content", "gap"], "sources": ["src/components/list/list.scss?tag=bds-list-item-content&encapsulation=scoped", "src/components/list/list-item-content.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Component, h, Host, Element, Prop } from '@stencil/core';\nimport { direction, justifyContent, flexWrap, alignItems, gap } from '../grid/grid-interface';\n\n@Component({\n  tag: 'bds-list-item-content',\n  styleUrl: 'list.scss',\n  scoped: true,\n})\nexport class ListItemContent {\n  @Element() hostElement: HTMLElement;\n\n  @Prop() direction?: direction = 'column';\n  @Prop() justifyContent?: justifyContent = 'flex-start';\n  @Prop() flexWrap?: flexWrap = 'wrap';\n  @Prop() alignItems?: alignItems = 'flex-start';\n  @Prop() gap?: gap;\n\n  render() {\n    return (\n      <Host\n        class={{\n          list_item_content: true,\n        }}\n      >\n        <bds-grid\n          direction={this.direction}\n          flexWrap={this.flexWrap}\n          justifyContent={this.justifyContent}\n          alignItems={this.alignItems}\n          gap={this.gap}\n        >\n          <slot />\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAU,urH,ICQHC,EAAeC,EAAA,mCAL5B,SAAAC,EAAAC,G,UAQUC,KAASC,UAAe,SACxBD,KAAcE,eAAoB,aAClCF,KAAQG,SAAc,OACtBH,KAAUI,WAAgB,YAsBnC,CAnBCN,EAAAO,UAAAC,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACLC,kBAAmB,OAGrBJ,EAAA,YAAAE,IAAA,2CACER,UAAWD,KAAKC,UAChBE,SAAUH,KAAKG,SACfD,eAAgBF,KAAKE,eACrBE,WAAYJ,KAAKI,WACjBQ,IAAKZ,KAAKY,KAEVL,EAAQ,QAAAE,IAAA,8C,gIAvBU,I", "ignoreList": []}