System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var r,n;return{setters:[function(t){r=t.r;n=t.h}],execute:function(){var e=":host{display:block}:host *{color:var(--color-content-default, rgb(40, 40, 40))}.button--icon{-webkit-transform:rotate(180deg);transform:rotate(180deg);color:var(--color-content-ghost, rgb(140, 140, 140))}.breadcrumb__button--0{padding-left:0}.breadcrumb__button--0 .button--icon{display:none}.breadcrumb__button--1{padding-left:8px}.breadcrumb__button--2{padding-left:16px}.breadcrumb__button--3{padding-left:24px}.breadcrumb__button--4{padding-left:32px}.breadcrumb__link--text{color:var(--color-content-disable, rgb(89, 89, 89))}.breadcrumb__link{text-decoration:none}";var i=t("bds_breadcrumb",function(){function t(t){r(this,t);this.items=[];this.parsedItems=[];this.isDropdownOpen=false}t.prototype.parseItems=function(t){if(typeof t==="string"){try{this.parsedItems=JSON.parse(t)}catch(t){this.parsedItems=[]}}else{this.parsedItems=t}};t.prototype.componentWillLoad=function(){this.parseItems(this.items)};t.prototype.toggleDropdown=function(){this.isDropdownOpen=!this.isDropdownOpen};t.prototype.render=function(){var t=this;if(!this.parsedItems||this.parsedItems.length===0){return n("p",null,"Sem itens para exibir no Breadcrumb.")}var r=this.parsedItems.length<=3?this.parsedItems:[this.parsedItems[0],{label:"...",href:null},this.parsedItems[this.parsedItems.length-1]];return n("nav",{"aria-label":"breadcrumb"},n("bds-grid",{direction:"row","align-items":"center"},r.map((function(e,i){return n("bds-grid",{class:{breadcrumb__item:true,"breadcrumb__item--active":i===r.length-1},"aria-current":i===r.length-1?"page":null},e.label==="..."?n("bds-dropdown",{"active-mode":"click",position:"auto"},n("bds-grid",{slot:"dropdown-content"},n("bds-grid",{direction:"column",padding:"1",gap:"half"},t.parsedItems.slice(1,-1).map((function(t,r){return n("bds-grid",{class:"breadcrumb__button--".concat(r)},t.href?n("a",{href:t.href,class:"breadcrumb__link breadcrumb__button--".concat(r)},n("bds-grid",{"align-items":"center",gap:"half"},n("bds-icon",{name:"reply",theme:"outline",class:"button--icon",size:"x-small"}),n("bds-button",{variant:"text",color:"content",size:"short"},t.label))):n("span",null,t.label))})))),n("bds-grid",{slot:"dropdown-activator","align-items":"center"},n("bds-button",{variant:"text",color:"content",size:"short",onClick:function(){return t.toggleDropdown()},"icon-left":"more-options-horizontal"}),n("bds-icon",{name:"arrow-right",size:"x-small"}))):e.href?n("bds-grid",{direction:"row"},n("bds-typo",{variant:"fs-12",margin:false,class:"breadcrumb__link--text"},n("a",{href:e.href,class:"breadcrumb__link"},e.label)),n("bds-icon",{name:"arrow-right",size:"x-small"})):n("bds-grid",{direction:"row"},n("bds-typo",{variant:"fs-12",bold:"semi-bold",margin:false},e.label)))}))))};Object.defineProperty(t,"watchers",{get:function(){return{items:["parseItems"]}},enumerable:false,configurable:true});return t}());i.style=e}}}));
//# sourceMappingURL=p-218fa4c4.system.entry.js.map