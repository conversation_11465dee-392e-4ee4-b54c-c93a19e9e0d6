{"version": 3, "names": ["selectCss", "Select", "constructor", "hostRef", "this", "intoView", "isOpen", "text", "validationDanger", "isPressed", "validationMesage", "danger", "success", "disabled", "label", "icon", "placeholder", "helperMessage", "errorMessage", "successMessage", "optionsPosition", "dataTest", "refNativeInput", "el", "nativeInput", "refDropdown", "dropElement", "refIconDrop", "iconDropElement", "onClickWrapper", "onFocus", "focus", "bdsFocus", "emit", "onBlur", "bdsBlur", "toggle", "getText", "value", "opt", "childOptions", "find", "option", "internalOptions", "internalOption", "titleText", "_a", "innerText", "handler", "event", "detail", "isOpenChanged", "positionHeightDrop", "name", "setDefaultPlacement", "validatePositionDrop", "valueChanged", "bdsChange", "selected", "handleWindow", "ev", "path", "<PERSON><PERSON><PERSON>", "element", "componentWillLoad", "options", "optionsChanged", "getScrollParent", "componentWillRender", "updateOptions", "getValueSelected", "componentDidLoad", "classList", "add", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "addEventListener", "JSON", "parse", "Array", "from", "shadowRoot", "querySelectorAll", "childOptionSelected", "keyPressWrapper", "key", "nextS<PERSON>ling", "_b", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "previousSibling", "_d", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIcon", "h", "class", "input__icon", "size", "color", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "undefined", "render", "element_input", "input", "onClick", "input__container__wrapper", "ref", "input__container__text", "readonly", "onKeyDown", "bind", "select__options", "role", "map", "idx", "slotAlign", "bulkOption", "status", "slot", "iconColor"], "sources": ["src/components/selects/select.scss?tag=bds-select&encapsulation=shadow", "src/components/selects/select/select.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Watch, Element, Listen } from '@stencil/core';\nimport { Option, SelectChangeEventDetail, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\n@Component({\n  tag: 'bds-select',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class Select {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalOptions: Option[];\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | Option[];\n\n  /**\n   * the value of the select.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  @Prop({ mutable: true }) value?: any | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEventDetail>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsChange.emit({ value: this.value });\n\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n\n    this.text = this.getText(this.value);\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true, capture: true })\n  handleWindow(ev: Event) {\n    const path = ev.composedPath();\n    if (!path.find((element: HTMLElement) => element == this.el)) {\n      this.isOpen = false;\n    }\n  }\n\n  componentWillLoad() {\n    this.options && this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  componentWillRender() {\n    this.options && this.updateOptions();\n    this.getValueSelected();\n  }\n\n  componentDidLoad() {\n    this.getValueSelected();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  @Watch('options')\n  optionsChanged() {\n    this.updateOptions();\n  }\n\n  private getValueSelected() {\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n      option.addEventListener('optionSelected', this.handler);\n    }\n    this.text = this.getText(this.value);\n  }\n\n  private updateOptions() {\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        this.internalOptions = JSON.parse(this.options);\n      } else {\n        this.internalOptions = this.options;\n      }\n    }\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private refNativeInput = (el: any): void => {\n    this.nativeInput = el;\n  };\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.isOpen = true;\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getText = (value): string => {\n    const opt = this.childOptions.find((option) => option.value == value);\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.titleText ? internalOption.titleText : internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt?.titleText : (opt?.innerText ?? '');\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n\n    return (\n      <div class=\"select\">\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n            part=\"input-container\"\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                <input\n                  ref={this.refNativeInput}\n                  class={{ input__container__text: true }}\n                  onFocus={this.onFocus}\n                  onBlur={this.onBlur}\n                  value={this.text}\n                  disabled={this.disabled}\n                  placeholder={this.placeholder}\n                  readonly\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper.bind(this)}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n          role=\"application\"\n        >\n          {this.internalOptions ? (\n            this.internalOptions.map((option, idx) =>\n              option.icon || option.titleText ? (\n                <bds-select-option\n                  key={idx}\n                  value={option.value}\n                  title-text={option.titleText}\n                  slot-align={option.slotAlign}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                >\n                  {option.icon && (\n                    <bds-icon slot=\"input-left\" name={option.icon} size=\"medium\" color={option.iconColor}></bds-icon>\n                  )}\n                  {option.label}\n                </bds-select-option>\n              ) : (\n                <bds-select-option key={idx} value={option.value} bulkOption={option.bulkOption} status={option.status}>\n                  {option.label}\n                </bds-select-option>\n              ),\n            )\n          ) : (\n            <slot />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "oGAAA,MAAMA,EAAY,qmU,MCQLC,EAAM,MALnB,WAAAC,CAAAC,G,gJAaWC,KAAQC,SAAiB,KAEzBD,KAAME,OAAI,MAEVF,KAAIG,KAAI,GAKRH,KAAgBI,iBAAa,MAI7BJ,KAASK,UAAI,MAKbL,KAAgBM,iBAAI,GAqBJN,KAAMO,OAAI,MAIKP,KAAOQ,QAAa,MAInCR,KAAQS,SAAI,MAyB7BT,KAAKU,MAAI,GAKQV,KAAIW,KAAY,GAKjCX,KAAWY,YAAY,GAKvBZ,KAAaa,cAAY,GAKzBb,KAAYc,aAAY,GAIPd,KAAce,eAAY,GAIXf,KAAegB,gBAA+B,OAK9EhB,KAAQiB,SAAY,KAqHpBjB,KAAAkB,eAAkBC,IACxBnB,KAAKoB,YAAcD,CAAE,EAGfnB,KAAAqB,YAAeF,IACrBnB,KAAKsB,YAAcH,CAAE,EAGfnB,KAAAuB,YAAeJ,IACrBnB,KAAKwB,gBAAkBL,CAAE,EAGnBnB,KAAcyB,eAAG,KACvBzB,KAAK0B,UACL1B,KAAKE,OAAS,KACd,GAAIF,KAAKoB,YAAa,CACpBpB,KAAKoB,YAAYO,O,GAIb3B,KAAO0B,QAAG,KAChB1B,KAAK4B,SAASC,OACd7B,KAAKK,UAAY,IAAI,EAGfL,KAAM8B,OAAG,KACf9B,KAAK+B,QAAQF,OACb7B,KAAKK,UAAY,KAAK,EAGhBL,KAAMgC,OAAG,KACf,IAAKhC,KAAKS,SAAU,CAClBT,KAAKE,QAAUF,KAAKE,M,GAIhBF,KAAAiC,QAAWC,I,MACjB,MAAMC,EAAMnC,KAAKoC,aAAaC,MAAMC,GAAWA,EAAOJ,OAASA,IAC/D,GAAIlC,KAAKuC,gBAAiB,CACxB,MAAMC,EAAiBxC,KAAKuC,gBAAgBF,MAAMC,GAAWA,EAAOJ,QAASC,IAAA,MAAAA,SAAA,SAAAA,EAAKD,SAClF,GAAIM,EAAgB,CAClB,OAAOA,EAAeC,UAAYD,EAAeC,UAAYD,EAAe9B,K,EAGhF,OAAOyB,IAAA,MAAAA,SAAG,SAAHA,EAAKM,WAAYN,IAAG,MAAHA,SAAA,SAAAA,EAAKM,WAAaC,EAAAP,IAAG,MAAHA,SAAG,SAAHA,EAAKQ,aAAa,MAAAD,SAAA,EAAAA,EAAA,EAAG,EAGzD1C,KAAA4C,QAAWC,IACjB,MACEC,QAAQZ,MAAEA,IACRW,EACJ7C,KAAKkC,MAAQA,EACblC,KAAKgC,QAAQ,CA0KhB,CAhVW,aAAAe,CAAc7C,GACtB,GAAIF,KAAKgD,oBAAsB,SAAU,CACvChD,KAAKwB,gBAAgByB,KAAOjD,KAAKE,OAAS,WAAa,Y,KAClD,CACLF,KAAKwB,gBAAgByB,KAAOjD,KAAKE,OAAS,aAAe,U,CAE3D,GAAIA,EACF,GAAIF,KAAKgB,iBAAmB,OAAQ,CAClChB,KAAKkD,oBAAoBlD,KAAKgB,gB,KACzB,CACLhB,KAAKmD,sB,EAKX,YAAAC,GACEpD,KAAKqD,UAAUxB,KAAK,CAAEK,MAAOlC,KAAKkC,QAElC,IAAK,MAAMI,KAAUtC,KAAKoC,aAAc,CACtCE,EAAOgB,SAAWtD,KAAKkC,QAAUI,EAAOJ,K,CAG1ClC,KAAKG,KAAOH,KAAKiC,QAAQjC,KAAKkC,M,CAIhC,YAAAqB,CAAaC,GACX,MAAMC,EAAOD,EAAGE,eAChB,IAAKD,EAAKpB,MAAMsB,GAAyBA,GAAW3D,KAAKmB,KAAK,CAC5DnB,KAAKE,OAAS,K,EAIlB,iBAAA0D,GACE5D,KAAK6D,SAAW7D,KAAK8D,iBACrB9D,KAAKC,SAAW8D,EAAgB/D,KAAKmB,G,CAGvC,mBAAA6C,GACEhE,KAAK6D,SAAW7D,KAAKiE,gBACrBjE,KAAKkE,kB,CAGP,gBAAAC,GACEnE,KAAKkE,mBACL,GAAIlE,KAAKgB,iBAAmB,OAAQ,CAClChB,KAAKkD,oBAAoBlD,KAAKgB,gB,KACzB,CACLhB,KAAKmD,sB,EAID,mBAAAD,CAAoBhB,GAC1B,GAAIA,GAAS,SAAU,CACrBlC,KAAKsB,YAAY8C,UAAUC,IAAI,oCAC/BrE,KAAKwB,gBAAgByB,KAAO,Y,KACvB,CACLjD,KAAKsB,YAAY8C,UAAUC,IAAI,iCAC/BrE,KAAKwB,gBAAgByB,KAAO,U,EAIxB,oBAAAE,GACN,MAAMmB,EAAgBC,EAAwB,CAC5CC,cAAexE,KAAKmB,GACpBsD,eAAgBzE,KAAKsB,YACrBrB,SAAUD,KAAKC,WAEjBD,KAAKgD,mBAAqBsB,EAAcI,EACxC,GAAIJ,EAAcI,GAAK,SAAU,CAC/B1E,KAAKsB,YAAY8C,UAAUC,IAAI,oCAC/BrE,KAAKwB,gBAAgByB,KAAO,Y,KACvB,CACLjD,KAAKsB,YAAY8C,UAAUC,IAAI,iCAC/BrE,KAAKwB,gBAAgByB,KAAO,U,EAKhC,cAAAa,GACE9D,KAAKiE,e,CAGC,gBAAAC,GACN,IAAK,MAAM5B,KAAUtC,KAAKoC,aAAc,CACtCE,EAAOgB,SAAWtD,KAAKkC,QAAUI,EAAOJ,MACxCI,EAAOqC,iBAAiB,iBAAkB3E,KAAK4C,Q,CAEjD5C,KAAKG,KAAOH,KAAKiC,QAAQjC,KAAKkC,M,CAGxB,aAAA+B,GACN,GAAIjE,KAAK6D,QAAS,CAChB,UAAW7D,KAAK6D,UAAY,SAAU,CACpC7D,KAAKuC,gBAAkBqC,KAAKC,MAAM7E,KAAK6D,Q,KAClC,CACL7D,KAAKuC,gBAAkBvC,KAAK6D,O,GAKlC,gBAAYzB,GACV,OAAOpC,KAAK6D,QACRiB,MAAMC,KAAK/E,KAAKmB,GAAG6D,WAAWC,iBAAiB,sBAC/CH,MAAMC,KAAK/E,KAAKmB,GAAG8D,iBAAiB,qB,CAG1C,uBAAYC,GACV,OAAOlF,KAAK6D,QACRiB,MAAMC,KAAK/E,KAAKmB,GAAG6D,WAAWC,iBAAiB,sBAAsB5C,MAAMC,GAAWA,EAAOgB,WAC7FwB,MAAMC,KAAK/E,KAAKmB,GAAG8D,iBAAiB,sBAAsB5C,MAAMC,GAAWA,EAAOgB,U,CA2DhF,eAAA6B,CAAgBtC,G,YACtB,OAAQA,EAAMuC,KACZ,IAAK,QACHpF,KAAKgC,SACL,MACF,IAAK,YACH,IAAKhC,KAAKS,SAAU,CAClBT,KAAKE,OAAS,I,CAEhB,GAAIF,KAAKkF,oBAAqB,CAC5BlF,KAAKkC,OAASQ,EAAA1C,KAAKkF,oBAAoBG,eAA4C,MAAA3C,SAAA,SAAAA,EAAAR,MACnF,M,CAEFlC,KAAKkC,OAASoD,EAAAtF,KAAKmB,GAAGoE,qBAAkD,MAAAD,SAAA,SAAAA,EAAApD,MACxE,MACF,IAAK,UACH,GAAIlC,KAAKkF,oBAAqB,CAC5BlF,KAAKkC,OAASsD,EAAAxF,KAAKkF,oBAAoBO,mBAAgD,MAAAD,SAAA,SAAAA,EAAAtD,MACvF,M,CAEFlC,KAAKkC,OAASwD,EAAA1F,KAAKmB,GAAGwE,oBAAiD,MAAAD,SAAA,SAAAA,EAAAxD,MACvE,M,CAIE,UAAA0D,GACN,OACE5F,KAAKW,MACHkF,EAAA,OACEC,MAAO,CACLC,YAAa,KACb,uBAAwB/F,KAAKU,QAG/BmF,EAAU,YAAAG,KAAMhG,KAAKU,MAAQ,SAAW,QAASuC,KAAMjD,KAAKW,KAAMsF,MAAM,Y,CAMxE,WAAAC,GACN,OACElG,KAAKU,OACHmF,EAAA,SACEC,MAAO,CACLK,wBAAyB,KACzB,mCAAoCnG,KAAKK,YAAcL,KAAKS,WAG9DoF,EAAA,YAAUO,QAAQ,QAAQC,KAAK,QAC5BrG,KAAKU,O,CAOR,aAAA4F,GACN,MAAM3F,EAAOX,KAAKO,OAAS,QAAUP,KAAKQ,QAAU,YAAc,OAClE,IAAI+F,EAAUvG,KAAKO,OAASP,KAAKc,aAAed,KAAKQ,QAAUR,KAAKe,eAAiBf,KAAKa,cAE1F,IAAK0F,GAAWvG,KAAKI,iBAAkBmG,EAAUvG,KAAKM,iBAEtD,MAAMkG,EACJxG,KAAKO,QAAUP,KAAKI,iBAChB,wCACAJ,KAAKQ,QACH,yCACA,iBAER,GAAI+F,EAAS,CACX,OACEV,EAAA,OAAKC,MAAOU,EAAQC,KAAK,kBACvBZ,EAAK,OAAAC,MAAM,wBACTD,EAAA,YAAUG,KAAK,UAAU/C,KAAMtC,EAAM+F,MAAM,UAAUT,MAAM,aAE7DJ,EAAA,YAAUC,MAAM,uBAAuBM,QAAQ,SAC5CG,G,CAMT,OAAOI,S,CAGT,MAAAC,GACE,MAAMvG,EAAYL,KAAKK,YAAcL,KAAKS,SAE1C,OACEoF,EAAA,OAAAT,IAAA,2CAAKU,MAAM,UACTD,EAAA,OAAAT,IAAA,2CAAKU,MAAO,CAAEe,cAAe,MAAuB,gBAAA7G,KAAKS,SAAW,OAAS,MAC3EoF,EAAA,OAAAT,IAAA,2CACEU,MAAO,CACLgB,MAAO,KACP,wBAAyB9G,KAAKO,SAAWP,KAAKI,iBAC9C,sBAAuBJ,KAAKO,QAAUP,KAAKI,iBAC3C,uBAAwBJ,KAAKQ,QAC7B,wBAAyBR,KAAKS,SAC9B,iBAAkBT,KAAKU,MACvB,iBAAkBL,GAEpB0G,QAAS/G,KAAKyB,eACdgF,KAAK,mBAEJzG,KAAK4F,aACNC,EAAK,OAAAT,IAAA,2CAAAU,MAAM,oBACR9F,KAAKkG,cACNL,EAAA,OAAAT,IAAA,2CAAKU,MAAO,CAAEkB,0BAA2B,OACvCnB,EAAA,SAAAT,IAAA,2CACE6B,IAAKjH,KAAKkB,eACV4E,MAAO,CAAEoB,uBAAwB,MACjCxF,QAAS1B,KAAK0B,QACdI,OAAQ9B,KAAK8B,OACbI,MAAOlC,KAAKG,KACZM,SAAUT,KAAKS,SACfG,YAAaZ,KAAKY,YAClBuG,SACW,iBAAAnH,KAAKiB,SAChBmG,UAAWpH,KAAKmF,gBAAgBkC,KAAKrH,UAI3C6F,EAAK,OAAAT,IAAA,2CAAAU,MAAM,gBACTD,EAAU,YAAAT,IAAA,2CAAA6B,IAAM9F,GAAOnB,KAAKuB,YAAYJ,GAAK6E,KAAK,QAAQC,MAAM,aAEjEjG,KAAKQ,SAAWqF,EAAA,YAAAT,IAAA,2CAAUU,MAAM,eAAe7C,KAAK,QAAQyD,MAAM,UAAUV,KAAK,eAEnFhG,KAAKsG,iBAERT,EAAA,OAAAT,IAAA,2CACE6B,IAAM9F,GAAOnB,KAAKqB,YAAYF,GAC9B2E,MAAO,CACLwB,gBAAiB,KACjB,wBAAyBtH,KAAKE,QAEhCqH,KAAK,eAEJvH,KAAKuC,gBACJvC,KAAKuC,gBAAgBiF,KAAI,CAAClF,EAAQmF,IAChCnF,EAAO3B,MAAQ2B,EAAOG,UACpBoD,EAAA,qBACET,IAAKqC,EACLvF,MAAOI,EAAOJ,MAAK,aACPI,EAAOG,UACP,aAAAH,EAAOoF,UACnBC,WAAYrF,EAAOqF,WACnBC,OAAQtF,EAAOsF,QAEdtF,EAAO3B,MACNkF,EAAA,YAAUgC,KAAK,aAAa5E,KAAMX,EAAO3B,KAAMqF,KAAK,SAASC,MAAO3D,EAAOwF,YAE5ExF,EAAO5B,OAGVmF,EAAmB,qBAAAT,IAAKqC,EAAKvF,MAAOI,EAAOJ,MAAOyF,WAAYrF,EAAOqF,WAAYC,OAAQtF,EAAOsF,QAC7FtF,EAAO5B,SAKdmF,EAAA,c", "ignoreList": []}