System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,i,o,r,s;return{setters:[function(e){t=e.r;i=e.c;o=e.h;r=e.H;s=e.a}],execute:function(){var a=':host{display:-ms-flexbox;display:flex;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;border-radius:4px;-webkit-box-sizing:border-box;box-sizing:border-box;max-width:100%}:host .chip{display:-ms-flexbox;display:flex;min-width:32px;width:-webkit-max-content;width:-moz-max-content;width:max-content;height:32px;border-radius:16px;padding:2px 4px;-ms-flex-align:center;align-items:center;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-pack:center;justify-content:center;position:relative;z-index:0}:host .chip .chip_focus:focus{position:absolute;width:100%;height:100%;padding:2px;border-radius:4px;outline:var(--color-focus, rgb(194, 38, 251)) solid 2px}:host .chip .chip_darker{position:absolute;width:100%;height:100%;border-radius:inherit;z-index:1;-webkit-backdrop-filter:brightness(1);backdrop-filter:brightness(1);-webkit-box-sizing:border-box;box-sizing:border-box}:host .chip--icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding-left:4px;height:20px;z-index:2}:host .chip--text{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:20px;z-index:2;margin:0 8px;-ms-flex-wrap:nowrap;flex-wrap:nowrap;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif}:host .chip--tall{height:40px;border-radius:24px}:host .chip--default{background-color:var(--color-system, rgb(178, 223, 253));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip--info{background-color:var(--color-info, rgb(128, 227, 235));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip--success{background-color:var(--color-success, rgb(132, 235, 188));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip--warning{background-color:var(--color-warning, rgb(253, 233, 155));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip--danger{background-color:var(--color-error, rgb(250, 190, 190));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip--outline{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));color:var(--color-content-default, rgb(40, 40, 40));padding:2px 3px}:host .chip--outline .chip_darker{height:calc(100% + 2px)}:host .chip:hover{cursor:pointer}:host .chip:hover .chip_darker{-webkit-backdrop-filter:brightness(0.9);backdrop-filter:brightness(0.9)}:host .chip:active{cursor:pointer}:host .chip:active .chip_darker{-webkit-backdrop-filter:brightness(0.8);backdrop-filter:brightness(0.8)}:host .chip:focus-visible{outline:none}:host .chip_selected{display:-ms-flexbox;display:flex;min-width:32px;width:-webkit-max-content;width:-moz-max-content;width:max-content;height:32px;border-radius:16px;padding:2px;-ms-flex-align:center;align-items:center;-webkit-box-sizing:border-box;box-sizing:border-box;background-color:var(--color-surface-1, rgb(246, 246, 246));border:2px solid var(--color-content-default, rgb(40, 40, 40))}:host .chip_selected--container-text--full{width:100%}:host .chip_selected--container-text--half{width:calc(100% - 20px)}:host .chip_selected--icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:20px;padding-left:4px;color:var(--color-content-default, rgb(40, 40, 40))}:host .chip_selected--text{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:20px;margin:0 8px;-ms-flex-wrap:nowrap;flex-wrap:nowrap;color:var(--color-content-default, rgb(40, 40, 40));font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif}:host .chip_selected--tall{height:40px;border-radius:24px}:host .chip_selected:hover{opacity:38%;cursor:pointer}:host .chip_selected:active{opacity:38%}:host .chip_disabled{display:-ms-flexbox;display:flex;min-width:32px;width:-webkit-max-content;width:-moz-max-content;width:max-content;height:32px;border-radius:16px;padding:2px 4px;-ms-flex-align:center;align-items:center;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-pack:center;justify-content:center;position:relative;z-index:0;background-color:var(--color-surface-3, rgb(227, 227, 227))}:host .chip_disabled--icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding-left:4px;width:16px;height:20px;color:var(--color-content-default, rgb(40, 40, 40));z-index:2}:host .chip_disabled--text{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:20px;z-index:2;margin:0 8px;-ms-flex-wrap:nowrap;flex-wrap:nowrap;color:var(--color-content-default, rgb(40, 40, 40));font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif}:host .chip_disabled--tall{height:40px;border-radius:24px}:host .chip_disabled:hover{cursor:default}';var c=e("bds_chip_selected",function(){function e(e){t(this,e);this.chipClick=i(this,"chipClick");this.isSelected=false;this.color="default";this.size="standard";this.selected=false;this.disabled=false;this.dataTest=null}e.prototype.handleKeyDown=function(e){if((e.key==="Enter"||e.key===" ")&&!this.disabled){e.preventDefault();if(this.isSelected){this.isSelected=false}else{this.isSelected=true}this.chipClick.emit({selected:this.isSelected})}};e.prototype.handleClick=function(e){if(!this.disabled){e.preventDefault();if(this.isSelected){this.isSelected=false}else{this.isSelected=true}this.chipClick.emit({selected:this.isSelected})}};e.prototype.componentWillLoad=function(){this.el.focus();this.isSelected=this.selected};e.prototype.getDisabledChip=function(){var e;return this.disabled?(e={chip_disabled:true},e["chip_disabled--".concat(this.size)]=true,e):{}};e.prototype.getStyleChip=function(){var e,t;return this.isSelected?(e={chip_selected:true},e["chip_selected--".concat(this.size)]=true,e):(t={},t["chip--".concat(this.color)]=true,t["chip--".concat(this.size)]=true,t)};e.prototype.getStyleText=function(){if(this.isSelected){var e={"chip_selected--text":true};return e}};e.prototype.getSizeIconChip=function(){if(this.size==="tall"){return"medium"}else return"x-small"};e.prototype.render=function(){var e=this;return o(r,{key:"91d33c460176f9b163760db431bb6e40f2315a0d"},o("div",{key:"fa32872ea7e2d99db15ed3f3915320730eb259f2",class:Object.assign(Object.assign({chip:true},this.getStyleChip()),this.getDisabledChip()),onClick:function(t){return e.handleClick(t)},"data-test":this.dataTest},!this.disabled&&o("div",{key:"afac917fb643afe0f74dbf0d4a342a8abf1bf51a",class:"chip_focus",onKeyDown:this.handleKeyDown.bind(this),tabindex:"0"}),!this.isSelected&&!this.disabled&&o("div",{key:"1ff4dc3b67a4122420d8beb1108d975216f59e66",class:"chip_darker"}),this.icon&&!this.isSelected&&o("div",{key:"16d5a1055d1f140ebe8a41715373e5a938be1843",class:"chip--icon"},o("bds-icon",{key:"756221618403928021c4e6a6a503f6bf7972da73",size:this.getSizeIconChip(),name:this.icon})),this.isSelected&&o("div",{key:"e9d7eb49b3a7ff338d98bf42ed72ad11ba8f5b3a",class:"chip_selected--icon"},o("bds-icon",{key:"0e0175d66d0d1a3e13459ecdc77b6db992e347e7",size:this.getSizeIconChip(),name:"checkball"})),o("div",{key:"4cba6caffec90399696995c61bd169de5c0fad8d",class:this.isSelected?"chip_selected--container-text--half":"chip_selected--container-text--full"},o("bds-typo",{key:"f60732ceba2a6ebacf856f7120e4ea7e7b1f693a",class:Object.assign({"chip--text":true},this.getStyleText()),variant:"fs-12","no-wrap":true,bold:"bold"},o("slot",{key:"7eb8412b2ea1ec66d430eb8313dd66e58b8d1477"})))))};Object.defineProperty(e.prototype,"el",{get:function(){return s(this)},enumerable:false,configurable:true});return e}());c.style=a}}}));
//# sourceMappingURL=p-a52f705e.system.entry.js.map