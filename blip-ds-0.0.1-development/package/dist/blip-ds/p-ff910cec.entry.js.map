{"version": 3, "names": ["CardBody", "render", "h", "key"], "sources": ["src/components/card/card-body/card-body.tsx"], "sourcesContent": ["import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-body',\n  shadow: true,\n})\nexport class CardBody implements ComponentInterface {\n  render() {\n    return (\n      <bds-grid>\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "mappings": "4CAMaA,EAAQ,M,yBACnB,MAAAC,GACE,OACEC,EAAA,YAAAC,IAAA,4CACED,EAAQ,QAAAC,IAAA,6C", "ignoreList": []}