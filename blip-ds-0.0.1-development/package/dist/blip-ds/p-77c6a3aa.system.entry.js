System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,s,a,o;return{setters:[function(t){e=t.r;i=t.c;s=t.h;a=t.H;o=t.a}],execute:function(){var l=':host{display:block;width:100%}:host(.list_item_content){display:-ms-flexbox;display:flex;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.list_item{display:-ms-flexbox;display:flex;gap:16px;-ms-flex-align:center;align-items:center}.list_item_tall{padding:16px}.list_item_standard{padding:8px 16px}.list_item_short{padding:8px}.list_item .input_list{position:relative}.list_item .avatar-item{position:relative;display:block}.list_item .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.list_item .grow-up{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-slot{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;gap:8px}.list_item .content-item{position:relative;display:-ms-flexbox;display:flex;gap:2px;-ms-flex-direction:column;flex-direction:column}.list_item .content-item .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-item .subtitle-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-area{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-area .internal-chips,.list_item .content-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;gap:8px}.list_item .action-area{position:relative}.list_item .action-area .internal-actions-buttons,.list_item .action-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-arrow{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.list_item .icon-arrow-active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.border_radius{border-radius:8px}.border_radius:before,.border_radius:after,.border_radius .active{border-radius:8px}.active{position:absolute;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08;inset:0}.clickable{position:relative;cursor:pointer;gap:8px}.clickable:before{content:"";position:absolute;inset:0}.clickable:hover:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.clickable:active:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}';var n=t("bds_list_item",function(){function t(t){var s=this;e(this,t);this.bdsChecked=i(this,"bdsChecked");this.bdsClickActionButtom=i(this,"bdsClickActionButtom");this.internalChips=[];this.internalActionsButtons=[];this.checked=false;this.typeList=null;this.avatarName=null;this.avatarThumbnail=null;this.icon=null;this.value=null;this.text=null;this.secondaryText=null;this.chips=[];this.actionsButtons=[];this.clickable=false;this.active=false;this.borderRadius=false;this.size="standard";this.dataTest=null;this.handler=function(){s.typeList=="radio"?s.checked=true:s.checked=!s.checked};this.clickActionButtons=function(t,e){var i=e.composedPath()[0];s.bdsClickActionButtom.emit({value:s.value,icon:t,elementButton:i})}}t.prototype.componentWillLoad=function(){this.hasActionAreaSlot=!!this.hostElement.querySelector('[slot="action-area"]');this.hasContentAreaSlot=!!this.hostElement.querySelector('[slot="content-area"]');this.chipsChanged();this.actionsButtonsChanged()};t.prototype.checkedChanged=function(t){this.bdsChecked.emit({value:this.value,text:this.text,secondaryText:this.secondaryText,typeList:this.typeList,checked:t})};t.prototype.chipsChanged=function(){if(this.chips){if(typeof this.chips==="string"){this.internalChips=JSON.parse(this.chips)}else{this.internalChips=this.chips}}else{this.internalChips=[]}};t.prototype.actionsButtonsChanged=function(){if(this.actionsButtons){if(typeof this.actionsButtons==="string"){this.internalActionsButtons=JSON.parse(this.actionsButtons)}else{this.internalActionsButtons=this.actionsButtons}}else{this.internalActionsButtons=[]}};t.prototype.renderChips=function(){if(!this.internalChips.length){return[]}return this.internalChips.map((function(t,e){var i=e.toString();var a=30;if(t.length<=a){return s("bds-chip-clickable",{id:i,key:i,color:"default"},t)}else{return s("bds-tooltip",{key:i,position:"top-center","tooltip-text":t},s("bds-chip-clickable",{id:i,key:i,color:"default"},"".concat(t.slice(0,a)," ...")))}}))};t.prototype.renderActionsButtons=function(){var t=this;if(!this.internalActionsButtons.length){return[]}return this.internalActionsButtons.map((function(e,i){var a=i.toString();return s("bds-button-icon",{key:a,variant:"secondary",icon:e,size:"short",onClick:function(i){return t.clickActionButtons(e,i)}})}))};t.prototype.render=function(){var t,e,i,o,l,n;var r=this.clickable==true||this.typeList=="checkbox"||this.typeList=="radio"||this.typeList=="switch";var c=this.typeList=="checkbox"||this.typeList=="radio";var d=this.avatarName||this.avatarThumbnail;return s(a,{key:"c6f535bacd09a7380a8bfed5aa43cafd6e77480e"},s("div",{key:"6069a62d8aaf8da2a44087c543e7bf03945d94b3",onClick:this.handler,tabindex:"0",class:(t={list_item:true,clickable:r,border_radius:this.borderRadius},t["list_item_".concat(this.size)]=true,t),"data-test":this.dataTest},this.active&&s("div",{key:"1c9877baeb95d6d0b442ffef23a3036b0aaf6f05",class:"active"}),c&&s("div",{key:"cb8d6e06b0461667d8d833de543a354a2695790b",class:{input_list:true}},this.typeList=="radio"&&s("bds-radio",{key:"d2bb56ce9d7368ebf2042bd120a46897032d918d",value:this.value,checked:this.checked}),this.typeList=="checkbox"&&s("bds-checkbox",{key:"d2b1192fe200be921f27b13ff404b77636c248b7",refer:"",label:"",name:"cb1",disabled:false,checked:this.checked})),d?s("bds-avatar",{class:"avatar-item",name:this.avatarName,thumbnail:this.avatarThumbnail,size:"extra-small"}):this.icon&&s("bds-icon",{class:(e={},e["icon-item"]=true,e["icon-item-active"]=this.active,e),size:"medium",name:this.icon,color:"inherit",theme:this.active?"solid":"outline"}),s("div",{key:"552b5e8b789d876a36152ffe08d3345a00432964",class:(i={},i["content-slot"]=true,i)},s("slot",{key:"9d29123adda77805387e2cec7c48a8f8420f629d"})),(this.text||this.secondaryText)&&s("div",{key:"13e2dcacf1d732f6fe7ceec0dd35b573e1d516e6",class:(o={},o["content-item"]=true,o["grow-up"]=!this.hasActionAreaSlot&&!this.hasContentAreaSlot&&this.internalChips.length<0,o)},this.text&&s("bds-typo",{key:"d9a70e2a6f86587228999282950c22ffd783d8dd",class:"title-item",variant:"fs-16",tag:"span",bold:this.active?"bold":"regular"},this.text),this.secondaryText&&s("bds-typo",{key:"64f9a2d8603a984f2e21f6e5fc2bb4cd0dac5bdd",class:"subtitle-item",variant:"fs-12","line-height":"small",tag:"span"},this.secondaryText)),s("div",{key:"8fd955b1692070969a655b9b915d603037fc7b76",class:(l={},l["content-area"]=true,l["grow-up"]=true,l)},this.internalChips.length>0&&s("div",{key:"f4d5a1b4f3853bca8be36ada97194c9c5cb0d5ff",class:"internal-chips"},this.renderChips()),s("slot",{key:"f354984737785227a7a6616ccb14a36cdb019573",name:"content-area"})),(!this.typeList||this.typeList=="default")&&s("div",{key:"7ad2aab79dc28f5bb3be6a2bc191f6d23363069c",class:(n={},n["action-area"]=true,n)},this.internalActionsButtons.length>0&&s("div",{key:"fb7e71327931ebfd91f089d2f75d50b468cd0d05",class:"internal-actions-buttons"},this.renderActionsButtons()),s("slot",{key:"dee52851703625af2e149d5e40a5eecfe1a2194c",name:"action-area"})),this.typeList=="switch"&&s("bds-switch",{key:"c4aee037502773767d1ccad20a9331a18232860f",refer:"",name:"",checked:this.checked})))};Object.defineProperty(t.prototype,"hostElement",{get:function(){return o(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{checked:["checkedChanged"],chips:["chipsChanged"],actionsButtons:["actionsButtonsChanged"]}},enumerable:false,configurable:true});return t}());n.style=l}}}));
//# sourceMappingURL=p-77c6a3aa.system.entry.js.map