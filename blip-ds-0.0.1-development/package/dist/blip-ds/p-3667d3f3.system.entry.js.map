{"version": 3, "names": ["illustrationCss", "BdsIllustration", "exports", "class_1", "hostRef", "_this", "this", "type", "dataTest", "setIllustrationContent", "tokensVersion", "packageJson", "dependencies", "replace", "apiUrl", "concat", "name", "fetch", "then", "response", "json", "data", "IllustrationContent", "prototype", "componentWillLoad", "render", "h", "Host", "key", "role", "class", "draggable", "src", "alt", "skeletonCss", "Skeleton", "class_2", "shape", "height", "width", "style", "display", "position", "overflow", "borderRadius", "xxs", "_a", "skeleton"], "sources": ["src/components/illustration/illustration.scss?tag=bds-illustration&encapsulation=shadow", "src/components/illustration/illustration.tsx", "src/components/skeleton/skeleton.scss?tag=bds-skeleton&encapsulation=shadow", "src/components/skeleton/skeleton.tsx"], "sourcesContent": [":host {\n  .illustration {\n    display: flex;\n    height: 100%;\n    width: auto;\n  }\n  \n}\n\n:host(.bds-illustration) {\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n", "import { Component, h, Host, Prop, State } from '@stencil/core';\nimport { IllustrationType } from './illustration-interface';\nimport packageJson from '../../../package.json';\n\n@Component({\n  tag: 'bds-illustration',\n  assetsDirs: ['svg'],\n  styleUrl: 'illustration.scss',\n  shadow: true,\n})\nexport class BdsIllustration {\n  @State() private IllustrationContent?: string;\n\n  /**\n   * Specifies the type to use. Can be: 'default'.\n   */\n  @Prop() type: IllustrationType = 'default';\n  /**\n   * Specifies the name of illustration. Verify the names on illustration tokens.\n   */\n  @Prop() name: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setIllustrationContent();\n  }\n\n  /**Function to map the svg and call the \"formatSvg\" function */\n  setIllustrationContent = () => {\n    const tokensVersion = packageJson.dependencies['blip-tokens'].replace('^', '');\n    const apiUrl = `https://cdn.jsdelivr.net/npm/blip-tokens@${tokensVersion}/build/json/illustrations/${this.type}/${this.name}.json`;\n    fetch(apiUrl).then((response) =>\n      response.json().then((data) => {\n        this.IllustrationContent = data[`asset-${this.type}-${this.name}-svg`];\n      }),\n    );\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-illustration': true,\n        }}\n      >\n        {this.IllustrationContent ? (\n          <img\n            draggable={false}\n            src={`data:image/svg+xml;base64,${this.IllustrationContent}`}\n            alt={this.alt}\n            data-test={this.dataTest}\n          />\n        ) : (\n          <div class=\"default\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n.skeleton {\n  min-width: 8px;\n  min-height: 8px;\n  background-color: $color-content-default;\n  opacity: 0.16;\n  overflow: hidden;\n\n  &_shape {\n    &--circle {\n      border-radius: 50%;\n    }\n    &--square {\n      border-radius: 8px;\n    }\n  }\n}\n\n.animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    rgba(246, 246, 246, 0) 0%,\n    rgba(246, 246, 246, 0.56) 50%,\n    rgba(246, 246, 246, 0) 100%\n  );\n  mix-blend-mode: overlay;\n\n  animation: 2.5s ease-out infinite shine;\n}\n\n@keyframes shine {\n  0% {\n    transform: translateX(-100%);\n  }\n\n  20% {\n    transform: translateX(100%);\n  }\n\n  100% {\n    transform: translateX(100%);\n  }\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\n\nexport type Shape = 'circle' | 'square';\n\n@Component({\n  tag: 'bds-skeleton',\n  styleUrl: 'skeleton.scss',\n  shadow: true,\n})\nexport class Skeleton {\n  @Prop() shape?: Shape = 'square';\n  @Prop() height?: string = '50px';\n  @Prop() width?: string = '100%';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host\n        style={{\n          display: 'flex',\n          position: 'relative',\n          overflow: 'hidden',\n          width: this.width,\n          height: this.height,\n          borderRadius: this.shape === 'circle' ? '50%' : '8px',\n        }}\n      >\n        <bds-grid xxs=\"12\" class={{ skeleton: true, [`skeleton_shape--${this.shape}`]: true }}></bds-grid>\n        <div\n          style={{\n            display: 'flex',\n            width: '100%',\n            height: '100%',\n            position: 'absolute',\n            borderRadius: this.shape === 'circle' ? '50%' : '8px',\n            overflow: 'hidden',\n          }}\n          data-test={this.dataTest}\n        >\n          <div class=\"animation\"></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "yMAAA,IAAMA,EAAkB,mI,ICUXC,EAAeC,EAAA,8BAN5B,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,UAYUA,KAAIC,KAAqB,UAczBD,KAAQE,SAAY,KAO5BF,KAAsBG,uBAAG,WACvB,IAAMC,EAAgBC,EAAYC,aAAa,eAAeC,QAAQ,IAAK,IAC3E,IAAMC,EAAS,4CAAAC,OAA4CL,EAAa,8BAAAK,OAA6BV,EAAKE,KAAI,KAAAQ,OAAIV,EAAKW,KAAI,SAC3HC,MAAMH,GAAQI,MAAK,SAACC,GAClB,OAAAA,EAASC,OAAOF,MAAK,SAACG,GACpBhB,EAAKiB,oBAAsBD,EAAK,SAAAN,OAASV,EAAKE,KAAI,KAAAQ,OAAIV,EAAKW,KAAI,Q,GADjE,GAIJ,CAuBD,CApCCb,EAAAoB,UAAAC,kBAAA,WACElB,KAAKG,wB,EAcPN,EAAAoB,UAAAE,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,KAAK,MACLC,MAAO,CACL,mBAAoB,OAGrBxB,KAAKgB,oBACJI,EAAA,OACEK,UAAW,MACXC,IAAK,6BAAAjB,OAA6BT,KAAKgB,qBACvCW,IAAK3B,KAAK2B,IAAG,YACF3B,KAAKE,WAGlBkB,EAAA,OAAKI,MAAM,UAAS,YAAYxB,KAAKE,W,oHArDnB,I,UCV5B,IAAM0B,EAAc,+kC,ICSPC,EAAQjC,EAAA,0BALrB,SAAAkC,EAAAhC,G,UAMUE,KAAK+B,MAAW,SAChB/B,KAAMgC,OAAY,OAClBhC,KAAKiC,MAAY,OAKjBjC,KAAQE,SAAY,IA+B7B,CA7BC4B,EAAAb,UAAAE,OAAA,W,MACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHY,MAAO,CACLC,QAAS,OACTC,SAAU,WACVC,SAAU,SACVJ,MAAOjC,KAAKiC,MACZD,OAAQhC,KAAKgC,OACbM,aAActC,KAAK+B,QAAU,SAAW,MAAQ,QAGlDX,EAAU,YAAAE,IAAA,2CAAAiB,IAAI,KAAKf,OAAKgB,EAAA,CAAIC,SAAU,MAAMD,EAAC,mBAAA/B,OAAmBT,KAAK+B,QAAU,KAAIS,KACnFpB,EAAA,OAAAE,IAAA,2CACEY,MAAO,CACLC,QAAS,OACTF,MAAO,OACPD,OAAQ,OACRI,SAAU,WACVE,aAActC,KAAK+B,QAAU,SAAW,MAAQ,MAChDM,SAAU,UAED,YAAArC,KAAKE,UAEhBkB,EAAA,OAAAE,IAAA,2CAAKE,MAAM,e,WAlCA,I", "ignoreList": []}