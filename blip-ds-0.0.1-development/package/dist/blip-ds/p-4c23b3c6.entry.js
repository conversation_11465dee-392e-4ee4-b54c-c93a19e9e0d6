import{r as e,h as a}from"./p-C3J6Z5OX.js";const i=".menuseparation{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:0 16px}.menuseparation__small{margin:8px 0}.menuseparation__default{margin:12px 0}.menuseparation__large{margin:16px 0}.menuseparation .dividor-item{height:1px;width:100%;background-color:#d4d4d4}.menuseparation .title-item{margin-right:8px;margin-top:-4px;color:#6e7b91}";const t=class{constructor(a){e(this,a);this.value=null;this.size=null}render(){return a("div",{key:"3aabfb5b93571fc6cffded9393e2ed6f0088e6f5",class:{menuseparation:true,[`menuseparation__${this.size}`]:true}},this.value&&a("bds-typo",{key:"3b418384cac21264f22c04a53eef008a55c13f74",class:"title-item",variant:"fs-10",tag:"span"},this.value),a("div",{key:"746b0b0b51a9ffad2ef749b33e61c21b43a711be",class:"dividor-item"}))}};t.style=i;export{t as bds_menu_separation};
//# sourceMappingURL=p-4c23b3c6.entry.js.map