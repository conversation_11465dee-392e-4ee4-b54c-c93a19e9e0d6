var __awaiter=this&&this.__awaiter||function(t,i,n,e){function o(t){return t instanceof n?t:new n((function(i){i(t)}))}return new(n||(n=Promise))((function(n,r){function c(t){try{a(e.next(t))}catch(t){r(t)}}function s(t){try{a(e["throw"](t))}catch(t){r(t)}}function a(t){t.done?n(t.value):o(t.value).then(c,s)}a((e=e.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var n={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},e,o,r,c;return c={next:s(0),throw:s(1),return:s(2)},typeof Symbol==="function"&&(c[Symbol.iterator]=function(){return this}),c;function s(t){return function(i){return a([t,i])}}function a(s){if(e)throw new TypeError("Generator is already executing.");while(c&&(c=0,s[0]&&(n=0)),n)try{if(e=1,o&&(r=s[0]&2?o["return"]:s[0]?o["throw"]||((r=o["return"])&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;if(o=0,r)s=[s[0]&2,r.value];switch(s[0]){case 0:case 1:r=s;break;case 4:n.label++;return{value:s[1],done:false};case 5:n.label++;o=s[1];s=[0];continue;case 7:s=n.ops.pop();n.trys.pop();continue;default:if(!(r=n.trys,r=r.length>0&&r[r.length-1])&&(s[0]===6||s[0]===2)){n=0;continue}if(s[0]===3&&(!r||s[1]>r[0]&&s[1]<r[3])){n.label=s[1];break}if(s[0]===6&&n.label<r[1]){n.label=r[1];r=s;break}if(r&&n.label<r[2]){n.label=r[2];n.ops.push(s);break}if(r[2])n.ops.pop();n.trys.pop();continue}s=i.call(t,n)}catch(t){s=[6,t];o=0}finally{e=r=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js","./p-KsAJij7V.system.js"],(function(t){"use strict";var i,n,e,o,r,c,s;return{setters:[function(t){i=t.r;n=t.c;e=t.h;o=t.H;r=t.a},function(t){c=t.g;s=t.p}],execute:function(){var a=':host{position:relative;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}:host(.is_child_drop){display:block;width:100%}.dropdown{position:absolute;pointer-events:none;padding:2px;background-color:var(--color-surface-0, rgb(255, 255, 255));border-radius:8px;-webkit-box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));min-width:240px;width:-webkit-max-content;width:-moz-max-content;width:max-content;opacity:0;-webkit-transition:opacity 0.5s;-moz-transition:opacity 0.5s;transition:opacity 0.5s;z-index:90000}.dropdown__open{pointer-events:auto;opacity:1}.dropdown__basic__top-center{bottom:calc(100% + 16px);left:calc(50% - 122px)}.dropdown__basic__top-left{bottom:calc(100% + 16px);left:0}.dropdown__basic__top-right{bottom:calc(100% + 16px);right:0}.dropdown__basic__bottom-center{top:calc(100% + 16px);left:calc(50% - 122px)}.dropdown__basic__bottom-right{top:calc(100% + 16px);right:0}.dropdown__basic__bottom-left{top:calc(100% + 16px);left:0}.dropdown__basic__right-center{right:calc(100% + 8px)}.dropdown__basic__right-top{right:calc(100% + 8px);top:0}.dropdown__basic__right-bottom{right:calc(100% + 8px);bottom:0}.dropdown__basic__left-center{left:calc(100% + 8px)}.dropdown__basic__left-top{left:calc(100% + 8px);top:0}.dropdown__basic__left-bottom{left:calc(100% + 8px);bottom:0}.dropdown:after{content:"";position:absolute;inset:0;border-radius:8px;-webkit-box-shadow:var(--color-surface-0, rgb(255, 255, 255)) 0px 0px 0px 2px inset;box-shadow:var(--color-surface-0, rgb(255, 255, 255)) 0px 0px 0px 2px inset;pointer-events:none}.outzone{position:fixed;top:0;left:0;width:100%;height:100vh;z-index:80000}';var u=t("bds_dropdown",function(){function t(t){var e=this;i(this,t);this.bdsToggle=n(this,"bdsToggle");this.intoView=null;this.stateOpenSubMenu=false;this.stateSubMenu="close";this.zIndex=0;this.delay=null;this.activeMode="click";this.open=false;this.position="auto";this.dataTest=null;this.onCloseSubMenu=function(){e.stateSubMenu="close"};this.refDropElement=function(t){e.dropElement=t};this.onClickCloseButtom=function(){e.open=false};this.onMouseOver=function(){if(e.activeMode==="hover"){e.zIndex=1}e.stateOpenSubMenu=true};this.onMouseOut=function(){if(e.activeMode==="hover"){e.zIndex=0;e.stateOpenSubMenu=false}};this.handleClickOutside=function(t){if(e.open&&!e.hostElement.contains(t.target)){e.setClose()}};this.centerDropElement=function(t){var i=t.split("-");if((i[0]=="left"||i[0]=="right")&&i[1]=="center"){e.dropElement.style.top="calc(50% - ".concat(e.dropElement.offsetHeight/2,"px)")}}}t.prototype.componentWillLoad=function(){var t=this;this.activatorElement=this.hostElement.querySelector('[slot="dropdown-activator"]').children[0];this.intoView=c(this.hostElement);if(this.activeMode=="hover"){this.activatorElement.addEventListener("mouseover",(function(){return t.onMouseOver()}));this.activatorElement.addEventListener("click",(function(){return t.onMouseOver()}));this.activatorElement.addEventListener("mouseout",(function(){return t.onMouseOut()}))}else{this.activatorElement.addEventListener("click",(function(){return t.toggle()}))}};t.prototype.componentDidLoad=function(){if(this.position!="auto"){this.centerDropElement(this.position);this.setDefaultPlacement(this.position)}else{this.validatePositionDrop()}document.addEventListener("click",this.handleClickOutside)};t.prototype.disconnectedCallback=function(){document.removeEventListener("click",this.handleClickOutside)};t.prototype.setDefaultPlacement=function(t){this.dropElement.classList.add("dropdown__basic__".concat(t))};t.prototype.validatePositionDrop=function(){var t=s({actionElement:this.hostElement,changedElement:this.dropElement,intoView:this.intoView});this.dropElement.classList.add("dropdown__basic__".concat(t.y,"-").concat(t.x))};t.prototype.isOpenChanged=function(t){this.bdsToggle.emit({value:t});if(t)if(this.position!="auto"){this.setDefaultPlacement(this.position)}else{this.validatePositionDrop()}};t.prototype.isPositionChanged=function(){this.setDefaultPlacement(this.position)};t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.open=!this.open;return[2]}))}))};t.prototype.setOpen=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.open=true;return[2]}))}))};t.prototype.setClose=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.stateOpenSubMenu=false;clearTimeout(this.delay);this.open=false;return[2]}))}))};t.prototype.openSubMenuChanged=function(t){if(t==false){this.stateSubMenu="pending";this.delay=setTimeout(this.onCloseSubMenu,1e3)}if(t==true){clearTimeout(this.delay);this.delay=null;this.stateSubMenu="open"}return};t.prototype.stateSubMenuChanged=function(t){switch(t){case"open":this.open=true;break;case"pending":this.open=true;break;case"close":this.open=false;break}};t.prototype.render=function(){var t=this;var i={zIndex:"".concat(this.zIndex)};return e(o,{key:"939a25fc739bb7820e92fd85b8875ecd19482e9a"},e("slot",{key:"8ca0ab91226ca341c5d73a44d402dcc601ce9085",name:"dropdown-activator"}),e("div",{key:"1b0ec61d5bdba6ebbfc4b52d6135f370e643a0cd",ref:function(i){return t.refDropElement(i)},class:{dropdown:true,dropdown__open:this.open},"data-test":this.dataTest,onMouseOver:function(){return t.onMouseOver()},onMouseOut:function(){return t.onMouseOut()}},e("div",{key:"a9bfdcc1db256676b7ae4c39d0aca6abe2209c92",class:"content",style:i},e("slot",{key:"a10054f1c922dc1f10ae9e668443942fb02d77a8",name:"dropdown-content"}))),this.activeMode!=="hover"&&this.open&&e("div",{key:"d7a20da55b47fd6c3714a7908f1cd931755fd49a",class:{outzone:true},onClick:function(){return t.onClickCloseButtom()}}))};Object.defineProperty(t.prototype,"hostElement",{get:function(){return r(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{open:["isOpenChanged"],position:["isPositionChanged"],stateOpenSubMenu:["openSubMenuChanged"],stateSubMenu:["stateSubMenuChanged"]}},enumerable:false,configurable:true});return t}());u.style=a}}}));
//# sourceMappingURL=p-571ee157.system.entry.js.map