var __awaiter=this&&this.__awaiter||function(t,i,e,s){function n(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,r){function o(t){try{l(s.next(t))}catch(t){r(t)}}function a(t){try{l(s["throw"](t))}catch(t){r(t)}}function l(t){t.done?e(t.value):n(t.value).then(o,a)}l((s=s.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},s,n,r,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(i){return l([t,i])}}function l(a){if(s)throw new TypeError("Generator is already executing.");while(o&&(o=0,a[0]&&(e=0)),e)try{if(s=1,n&&(r=a[0]&2?n["return"]:a[0]?n["throw"]||((r=n["return"])&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;if(n=0,r)a=[a[0]&2,r.value];switch(a[0]){case 0:case 1:r=a;break;case 4:e.label++;return{value:a[1],done:false};case 5:e.label++;n=a[1];a=[0];continue;case 7:a=e.ops.pop();e.trys.pop();continue;default:if(!(r=e.trys,r=r.length>0&&r[r.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!r||a[1]>r[0]&&a[1]<r[3])){e.label=a[1];break}if(a[0]===6&&e.label<r[1]){e.label=r[1];r=a;break}if(r&&e.label<r[2]){e.label=r[2];e.ops.push(a);break}if(r[2])e.ops.pop();e.trys.pop();continue}a=i.call(t,e)}catch(t){a=[6,t];n=0}finally{s=r=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(t,i,e){if(e||arguments.length===2)for(var s=0,n=i.length,r;s<n;s++){if(r||!(s in i)){if(!r)r=Array.prototype.slice.call(i,0,s);r[s]=i[s]}}return t.concat(r||Array.prototype.slice.call(i))};System.register(["./p-B47mPBRA.system.js","./p-KsAJij7V.system.js"],(function(t){"use strict";var i,e,s,n,r,o,a;return{setters:[function(t){i=t.r;e=t.c;s=t.h;n=t.a},function(t){r=t.a;o=t.b;a=t.c}],execute:function(){var l=':host{display:block;-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;position:relative}.carousel{display:block;-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;max-width:1920px;position:relative}.carousel_slide{width:100%;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;padding:0 48px}.carousel_slide::after{content:"";position:absolute;inset:-8px;border:2px solid transparent;border-radius:4px;pointer-events:none}.carousel_slide:focus-visible{outline:none}.carousel_slide:focus-visible::after{border-color:var(--color-focus, rgb(194, 38, 251))}.carousel_slide_fullwidth{padding:0}.carousel_slide_frame{width:100%;display:-ms-flexbox;display:flex;overflow:hidden;-webkit-transition:height ease-in-out 0.5s;-moz-transition:height ease-in-out 0.5s;transition:height ease-in-out 0.5s}.carousel_slide_frame_loading{opacity:0;pointer-events:none}.carousel_slide_frame *{-webkit-user-select:none;-ms-user-select:none;-moz-user-select:none;user-select:none;-webkit-user-drag:none;-khtml-user-drag:none;-moz-user-drag:none;-o-user-drag:none}.carousel_slide_frame *[slot=loop]{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:relative;right:0;-webkit-transition:right ease-in-out 0.75s;-moz-transition:right ease-in-out 0.75s;transition:right ease-in-out 0.75s}.carousel_slide_frame_repeater{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:relative;right:0;-webkit-transition:right ease-in-out 0.75s;-moz-transition:right ease-in-out 0.75s;transition:right ease-in-out 0.75s}.carousel_slide_loading{opacity:0;pointer-events:none;position:absolute;inset:0}.carousel_slide_loading_visible{opacity:1;pointer-events:all}.carousel_loading_bar{-webkit-box-sizing:border-box;box-sizing:border-box;padding:0 60px;margin-top:8px}.carousel_loading_bar_fullwidth{padding:0 4px}.carousel_buttons{position:absolute;width:100%;height:0px;top:calc(50% - 20px);left:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel_buttons_fullwidth{padding:0 8px}.carousel_bullets{position:relative;margin-top:8px}.carousel_bullets_inside{position:absolute;bottom:0px;width:100%;margin:0;padding:0px 16px;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel_bullets_card{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;display:-ms-inline-flexbox;display:inline-flex;gap:8px}.carousel_bullets_card_inside{border-top-left-radius:8px;border-top-right-radius:8px;padding:8px;background-color:var(--color-surface-0, rgb(255, 255, 255))}.carousel_bullets_item{width:16px;height:16px;border:2px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:50%;position:relative;-webkit-transform:rotate(45deg);transform:rotate(45deg);cursor:pointer}.carousel_bullets_item::before{content:"";position:absolute;inset:4px;border-radius:50%}.carousel_bullets_item::after{content:"";position:absolute;inset:-8px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);border:2px solid transparent;border-radius:4px}.carousel_bullets_item:focus-visible{outline:none}.carousel_bullets_item:focus-visible::after{border-color:var(--color-focus, rgb(194, 38, 251))}.carousel_bullets_item_active::before{background-color:var(--color-primary, rgb(30, 107, 241))}.carousel_bullets_item_conclude{position:absolute;inset:-2px;border-radius:50%;border:2px solid var(--color-content-disable, rgb(89, 89, 89))}.carousel_bullets_item_loader{position:absolute;inset:-2px;border-radius:50%;border:2px solid var(--color-primary, rgb(30, 107, 241));-webkit-animation:l18 linear;animation:l18 linear}@-webkit-keyframes l18{0%{-webkit-clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)}25%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)}50%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)}75%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)}100%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)}}@keyframes l18{0%{-webkit-clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);clip-path:polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0)}25%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0)}50%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%)}75%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%)}100%{-webkit-clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);clip-path:polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0)}}';var u=t("bds_carousel",function(){function t(t){var s=this;i(this,t);this.bdsChangeCarousel=e(this,"bdsChangeCarousel");this.itemsElement=null;this.bulletElement=null;this.bulletElements=[];this.itemActivated=1;this.seconds=0;this.isWhole=0;this.heightCarousel=240;this.framePressed=false;this.autoplayState="running";this.autoplay=false;this.autoplayTimeout=5e3;this.autoplayHoverPause=false;this.autoHeight=false;this.bullets="outside";this.bulletsPosition="center";this.infiniteLoop=false;this.arrows="outside";this.slidePerPage=1;this.gap="none";this.grab=true;this.loading=false;this.dtSlideContent=null;this.dtButtonPrev=null;this.dtButtonNext=null;this.secondsLimit=this.autoplayTimeout/1e3;this.setInternalItens=function(t){var i=Math.floor(t.length/s.slidePerPage);var e=t.length/s.slidePerPage;var n=r(e);s.internalItens=n;s.isWhole=t.length-s.slidePerPage*i};this.startCountSeconds=function(){if(s.autoplay){s.incrementSeconds=setInterval((function(){s.seconds+=.1}),100)}};this.updateHeight=function(t){var i=t[s.itemActivated*s.slidePerPage-s.slidePerPage];var e=240;if(s.slidePerPage>1){var n=s.isWhole>0&&s.itemActivated==s.internalItens.length?t.slice(s.internalItens.length-s.internalItens.length-s.slidePerPage,s.itemActivated*s.slidePerPage):t.slice(s.itemActivated*s.slidePerPage-s.slidePerPage,s.itemActivated*s.slidePerPage);e=o(n)[0]}else{e=i.offsetHeight}s.frame.style.height="".concat(e,"px")};this.refFrame=function(t){s.frame=t};this.refThemeProviderArrows=function(t){s.themeProviderArrows=t};this.refFrameRepeater=function(t){s.frameRepeater=t};this.refBulletElement=function(t){if(t){s.bulletElement=t;s.bulletElements.push(t)}};this.onMouseOver=function(){if(s.autoplayHoverPause){s.pauseAutoplay()}};this.onMouseOut=function(){if(s.autoplayHoverPause){s.runAutoplay()}};this.onMouseDown=function(t){if(s.grab){s.framePressed=true;var i=s.frame.offsetLeft+s.element.offsetLeft;s.startX=t.pageX-i;s.endX=t.pageX-i;s.frame.style.cursor="grabbing"}};this.onMouseEnter=function(){if(s.grab){s.frame.style.cursor="grab"}};this.onMouseUp=function(){if(s.grab){s.framePressed=false;s.frame.style.cursor="grab";s.boundItems();if(s.autoplayHoverPause){s.pauseAutoplay()}}};this.onMouseMove=function(t){if(s.grab){if(!s.framePressed)return;t.preventDefault();var i=s.frame.offsetLeft+s.element.offsetLeft;s.endX=t.pageX-i}};this.boundItems=function(){if(s.endX<s.startX){s.nextSlide();s.seconds=0}else if(s.endX>s.startX){s.prevSlide();s.seconds=0}};this.setKeydownNavigation=function(t){if(t.key==="Tab"){if(s.bulletElements.length>0){s.bulletElements[0].focus()}else if(s.bulletElement){s.bulletElement.focus()}}if(t.key==="ArrowRight"){s.nextSlide()}if(t.key==="ArrowLeft"){s.prevSlide()}}}t.prototype.componentWillLoad=function(){this.itemsElement=this.element.getElementsByTagName("bds-carousel-item");this.setInternalItens(Array.from(this.itemsElement));if(this.bullets==true){this.bullets="outside"}if(this.bullets==false){this.bullets="none"}};t.prototype.componentDidRender=function(){if(!this.loading){if(this.gap!="none"){this.frame.style.width="calc(100% + ".concat(a(this.gap),"px)");this.frame.style.marginLeft="-".concat(a(this.gap)/2,"px")}for(var t=0;t<this.itemsElement.length;t++){var i=this.frame.offsetWidth>=1920?1920:this.frame.offsetWidth;this.itemsElement[t].style.width="".concat(i/this.slidePerPage,"px");this.itemsElement[t].style.padding="0 ".concat(a(this.gap)/2,"px")}if(this.autoHeight)this.updateHeight(Array.from(this.itemsElement))}if(this.arrows=="inside"){var e=(this.itemActivated-1)*(this.itemsElement.length/this.internalItens.length)+1;this.themeProviderArrows.theme=this.slidePerPage<=1?this.itemsElement[this.itemActivated-1].theme:this.itemsElement[Math.round(e)].theme}};t.prototype.componentDidLoad=function(){this.startCountSeconds()};t.prototype.itemActivatedChanged=function(){var t=this;var i=this.internalItens.find((function(i){return i.id===t.itemActivated}));var e=!this.frame?0:this.frame.offsetWidth*(this.itemActivated-1);if(this.frameRepeater){if(i.isWhole){var s=this.itemsElement[1].offsetWidth*(this.slidePerPage-this.isWhole);this.frameRepeater.style.right="".concat(e-s,"px")}else{this.frameRepeater.style.right="".concat(e,"px")}}this.bdsChangeCarousel.emit({value:i})};t.prototype.autoplayTimeoutChanged=function(){this.secondsLimit=this.autoplayTimeout/1e3};t.prototype.secondsChanged=function(){if(this.seconds>=this.secondsLimit){this.nextSlide();this.seconds=0}};t.prototype.isWholeChanged=function(){var t,i;if(this.internalItens!=undefined){if(this.isWhole>0){var e={id:((t=this.internalItens)===null||t===void 0?void 0:t.length)+1,label:"Frame - ".concat(((i=this.internalItens)===null||i===void 0?void 0:i.length)+1),isWhole:true};this.internalItens=__spreadArray(__spreadArray([],this.internalItens,true),[e],false)}}};t.prototype.buildCarousel=function(){return __awaiter(this,void 0,void 0,(function(){var t=this;return __generator(this,(function(i){this.itemsElement=this.element.getElementsByTagName("bds-carousel-item");this.loading=true;setTimeout((function(){return t.setInternalItens(Array.from(t.itemsElement)),t.loading=false,t.setActivated(1)}),1e3);return[2]}))}))};t.prototype.nextSlide=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){if(this.itemActivated==this.internalItens.length){if(this.infiniteLoop||this.autoplay){this.itemActivated=1}else{this.itemActivated=this.itemActivated}}else{this.itemActivated=this.itemActivated+1}clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds();return[2]}))}))};t.prototype.prevSlide=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){if(this.itemActivated==1){if(this.infiniteLoop||this.autoplay){this.itemActivated=this.internalItens.length}else{this.itemActivated=this.itemActivated}}else{this.itemActivated=this.itemActivated-1}clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds();return[2]}))}))};t.prototype.setActivated=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){this.itemActivated=t;clearInterval(this.incrementSeconds);this.seconds=0;this.startCountSeconds();this.autoplayState="running";return[2]}))}))};t.prototype.pauseAutoplay=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){clearInterval(this.incrementSeconds);this.autoplayState="paused";return[2]}))}))};t.prototype.runAutoplay=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.startCountSeconds();this.autoplayState="running";return[2]}))}))};t.prototype.render=function(){var t;var i=this;this.bulletElements=[];var e=this.arrows=="inside"?"bds-theme-provider":"div";var n=this.bulletsPosition=="center"?"center":this.bulletsPosition=="right"?"flex-end":this.bulletsPosition=="left"&&"flex-start";return s("div",{key:"aa7aebd1a8d211ab7c482337b1363dafe5e23d01",class:{carousel:true}},s("div",{key:"31a7d3f060d37f7241ff378dfc682d2c4d891ee5",class:(t={carousel_slide:true,carousel_slide_fullwidth:this.arrows!="outside"},t["carousel_slide_state_".concat(this.autoplayState)]=this.autoplay,t),tabindex:"0",onKeyDown:function(t){return i.setKeydownNavigation(t)},"data-test":this.dtSlideContent},s("div",{key:"5a590d1d40035790ad135954974a8977cdad30b1",ref:function(t){return i.refFrame(t)},class:{carousel_slide_frame:true,carousel_slide_frame_loading:this.loading},onMouseOver:function(){return i.onMouseOver()},onMouseOut:function(){return i.onMouseOut()},onMouseDown:function(t){return i.onMouseDown(t)},onMouseEnter:function(){return i.onMouseEnter()},onMouseUp:function(){return i.onMouseUp()},onMouseMove:function(t){return i.onMouseMove(t)}},s("div",{key:"1478791fb1cdafab371e74d6fc7a8294260955ed",ref:function(t){return i.refFrameRepeater(t)},class:{carousel_slide_frame_repeater:true}},s("slot",{key:"678c0af549a9171cb7c17db113026dee11c18093"}))),s("bds-grid",{key:"2fc13711f365a81f963958f3b5e16a9429967e13",class:{carousel_slide_loading:true,carousel_slide_loading_visible:this.loading}},s("bds-skeleton",{key:"bb484c25c5a651edd9cb9a41771c46837c13bdc8",height:"100%",shape:"square",width:"100%"})),this.arrows!="none"&&!this.loading&&s(e,{key:"163ea8a0fd5115c988f66532a01cedcf954e6803",ref:function(t){return i.refThemeProviderArrows(t)},class:{carousel_buttons:true,carousel_buttons_fullwidth:this.arrows!="outside"}},s("bds-button",{key:"0ec0b57300c808f40e3f378d80474ec38bead9fd",variant:"text",iconLeft:"arrow-left",color:"content",onBdsClick:function(){return i.prevSlide()},disabled:!this.infiniteLoop&&this.itemActivated<=1,dataTest:this.dtButtonPrev}),s("bds-button",{key:"653394988180d1c0a1b8ea2b8d28abdac0fa2676",variant:"text",iconLeft:"arrow-right",color:"content",onBdsClick:function(){return i.nextSlide()},disabled:!this.infiniteLoop&&this.itemActivated>=this.internalItens.length,dataTest:this.dtButtonNext}))),this.internalItens.length>1&&this.bullets!="none"&&s("div",{key:"ead3dbd4e1c431bb7259dce22f5acbb224603b55",class:{carousel_bullets:true,carousel_bullets_inside:this.bullets=="inside"}},this.loading&&this.bullets!="inside"?s("bds-grid",{xxs:"12",gap:"1","justify-content":n,padding:this.arrows==="outside"?"x-7":"none"},s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"}),s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"}),s("bds-skeleton",{height:"16px",width:"16px",shape:"circle"})):this.internalItens&&s("bds-grid",{xxs:"12","justify-content":n,padding:this.arrows==="outside"?"x-7":"none"},s("div",{class:{carousel_bullets_card:true,carousel_bullets_card_inside:this.bullets=="inside"}},this.internalItens.map((function(t,e){return s("div",{key:e,ref:function(t){return i.refBulletElement(t)},class:{carousel_bullets_item:true,carousel_bullets_item_active:t.id==i.itemActivated},tabindex:"0",onClick:function(){return i.setActivated(t.id)}},t.id<i.itemActivated&&i.autoplay&&s("div",{class:{carousel_bullets_item_conclude:true}}),t.id==i.itemActivated&&i.autoplay&&s("div",{class:{carousel_bullets_item_loader:true},style:{animationDuration:"".concat(i.autoplayTimeout/1e3-.1,"s"),animationPlayState:i.autoplayState}}))}))))),s("slot",{key:"53f9b8dc67817f99e57bfb72a5d210b93668df24",name:"after"}))};Object.defineProperty(t.prototype,"element",{get:function(){return n(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{itemActivated:["itemActivatedChanged"],autoplayTimeout:["autoplayTimeoutChanged"],seconds:["secondsChanged"],isWhole:["isWholeChanged"]}},enumerable:false,configurable:true});return t}());u.style=l}}}));
//# sourceMappingURL=p-8e23f594.system.entry.js.map