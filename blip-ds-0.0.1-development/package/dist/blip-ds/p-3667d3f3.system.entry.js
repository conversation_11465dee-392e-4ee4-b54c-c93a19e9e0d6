System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,a,s;return{setters:[function(t){e=t.r;a=t.h;s=t.H}],execute:function(){var i={"blip-tokens":"^1.93.0"};var r={dependencies:i};var n=":host .illustration{display:-ms-flexbox;display:flex;height:100%;width:auto}:host(.bds-illustration) img{width:100%;height:100%}";var o=t("bds_illustration",function(){function t(t){var a=this;e(this,t);this.type="default";this.dataTest=null;this.setIllustrationContent=function(){var t=r.dependencies["blip-tokens"].replace("^","");var e="https://cdn.jsdelivr.net/npm/blip-tokens@".concat(t,"/build/json/illustrations/").concat(a.type,"/").concat(a.name,".json");fetch(e).then((function(t){return t.json().then((function(t){a.IllustrationContent=t["asset-".concat(a.type,"-").concat(a.name,"-svg")]}))}))}}t.prototype.componentWillLoad=function(){this.setIllustrationContent()};t.prototype.render=function(){return a(s,{key:"068f8c950e40c190bc704ef26afba5a5a5ca0043",role:"img",class:{"bds-illustration":true}},this.IllustrationContent?a("img",{draggable:false,src:"data:image/svg+xml;base64,".concat(this.IllustrationContent),alt:this.alt,"data-test":this.dataTest}):a("div",{class:"default","data-test":this.dataTest}))};Object.defineProperty(t,"assetsDirs",{get:function(){return["svg"]},enumerable:false,configurable:true});return t}());o.style=n;var l=".skeleton{min-width:8px;min-height:8px;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16;overflow:hidden}.skeleton_shape--circle{border-radius:50%}.skeleton_shape--square{border-radius:8px}.animation{position:absolute;width:100%;height:100%;background:-webkit-gradient(linear, left top, right top, from(rgba(246, 246, 246, 0)), color-stop(50%, rgba(246, 246, 246, 0.56)), to(rgba(246, 246, 246, 0)));background:linear-gradient(90deg, rgba(246, 246, 246, 0) 0%, rgba(246, 246, 246, 0.56) 50%, rgba(246, 246, 246, 0) 100%);mix-blend-mode:overlay;-webkit-animation:2.5s ease-out infinite shine;animation:2.5s ease-out infinite shine}@-webkit-keyframes shine{0%{-webkit-transform:translateX(-100%);transform:translateX(-100%)}20%{-webkit-transform:translateX(100%);transform:translateX(100%)}100%{-webkit-transform:translateX(100%);transform:translateX(100%)}}@keyframes shine{0%{-webkit-transform:translateX(-100%);transform:translateX(-100%)}20%{-webkit-transform:translateX(100%);transform:translateX(100%)}100%{-webkit-transform:translateX(100%);transform:translateX(100%)}}";var d=t("bds_skeleton",function(){function t(t){e(this,t);this.shape="square";this.height="50px";this.width="100%";this.dataTest=null}t.prototype.render=function(){var t;return a(s,{key:"096bd4a0d5488514a4e4175365b2cdc72b2f95be",style:{display:"flex",position:"relative",overflow:"hidden",width:this.width,height:this.height,borderRadius:this.shape==="circle"?"50%":"8px"}},a("bds-grid",{key:"****************************************",xxs:"12",class:(t={skeleton:true},t["skeleton_shape--".concat(this.shape)]=true,t)}),a("div",{key:"65afb39b74e9fd0a44e3b11c5b26d5726136eee2",style:{display:"flex",width:"100%",height:"100%",position:"absolute",borderRadius:this.shape==="circle"?"50%":"8px",overflow:"hidden"},"data-test":this.dataTest},a("div",{key:"6d1069c509fff8283cb7456a96cdd6d43fb83845",class:"animation"})))};return t}());d.style=l}}}));
//# sourceMappingURL=p-3667d3f3.system.entry.js.map