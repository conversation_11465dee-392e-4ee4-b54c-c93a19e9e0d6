System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,n,r,i,u;return{setters:[function(t){e=t.r;n=t.c;r=t.h;i=t.H;u=t.a}],execute:function(){var s=t("bds_radio_group",function(){function t(t){var r=this;e(this,t);this.bdsRadioGroupChange=n(this,"bdsRadioGroupChange");this.radioGroupElement=null;this.chagedOptions=function(t,e){if(e.detail.checked==true){r.value=t}}}t.prototype.valueChanged=function(t){this.setSelectedRadio(t);this.bdsRadioGroupChange.emit({value:t})};t.prototype.componentWillRender=function(){var t=this;this.radioGroupElement=this.element.getElementsByTagName("bds-radio");var e=function(e){n.radioGroupElement[e].addEventListener("bdsChange",(function(n){return t.chagedOptions(t.radioGroupElement[e].value,n)}))};var n=this;for(var r=0;r<this.radioGroupElement.length;r++){e(r)}};t.prototype.setSelectedRadio=function(t){var e=this.radioGroupElement;for(var n=0;n<e.length;n++){var r=e[n].value;e[n].checked=false;if(e[n].checked==false&&t==r){e[n].checked=true}}};t.prototype.render=function(){return r(i,{key:"e3125c6fb59d1ae2d14b403c0987a1c38d3ed4e9"},r("slot",{key:"760a98c72db9700f888e85436e4f07a89bc7209b"}))};Object.defineProperty(t.prototype,"element",{get:function(){return u(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{value:["valueChanged"]}},enumerable:false,configurable:true});return t}())}}}));
//# sourceMappingURL=p-c6d97439.system.entry.js.map