{"version": 3, "file": "bds-checkbox.bds-select-option.entry.esm.js", "sources": ["src/components/checkbox/checkbox.scss?tag=bds-checkbox&encapsulation=shadow", "src/components/checkbox/checkbox.tsx", "src/components/select-option/select-option.scss?tag=bds-select-option&encapsulation=shadow", "src/components/select-option/select-option.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$checkbox-size: 18px;\n$checkbox-icon-radius: 4px;\n$checkbox-spacing-text: 8px;\n\n.checkbox {\n  display: inline;\n\n  input[type='checkbox'] {\n    display: none;\n    -webkit-appearance: none;\n    -moz-appearance: none;\n    appearance: none;\n    -webkit-tap-highlight-color: transparent;\n    cursor: pointer;\n    margin: 0;\n    &:focus {\n      outline: 0;\n    }\n  }\n\n  &__icon {\n    position: relative;\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      border-color: $color-brand;\n    }\n  }\n\n  &--selected {\n    .checkbox__icon {\n      background-color: $color-surface-primary;\n      border-color: $color-surface-primary;\n\n      &__svg {\n        color: $color-content-bright;\n      }\n\n      &:hover {\n        background-color: $color-brand;\n      }\n    }\n  }\n\n  &--selected-disabled {\n    .checkbox__label {\n      cursor: not-allowed;\n    }\n\n    .checkbox__icon {\n      color: $color-content-default;\n      border-color: $color-content-default;\n      background-color: $color-surface-3;\n      opacity: 50%;\n    }\n    .checkbox__text {\n      opacity: 50%;\n    }\n  }\n\n  &--deselected {\n    .checkbox__icon__svg {\n      display: none;\n    }\n  }\n\n  &--deselected-disabled {\n    .checkbox__label {\n      cursor: not-allowed;\n    }\n\n    .checkbox__icon {\n      opacity: 50%;\n      background-color: $color-surface-1;\n      border: 1px solid $color-brand;\n    }\n\n    .checkbox__icon__svg {\n      display: none;\n    }\n  }\n\n  &__label {\n    @include no-select();\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    max-width: fit-content;\n  }\n\n  &__icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: $checkbox-size;\n    width: $checkbox-size;\n    min-width: 18px;\n    border-radius: $checkbox-icon-radius;\n    color: $color-surface-1;\n    border: 1px solid $color-content-default;\n    box-sizing: border-box;\n    border-radius: 4px;\n    @include animation();\n  }\n\n  &__text {\n    margin-left: $checkbox-spacing-text;\n    color: $color-content-default;\n  }\n}\n", "import { Component, h, Prop, State, Method, Event, EventEmitter } from '@stencil/core';\n\nlet checkBoxIds = 0;\n@Component({\n  tag: 'bds-checkbox',\n  styleUrl: 'checkbox.scss',\n  shadow: true,\n})\nexport class Checkbox {\n  private nativeInput?: HTMLInputElement;\n\n  @State() checkBoxId?: string;\n\n  @Prop() refer!: string;\n\n  @Prop() label!: string;\n\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name!: string;\n\n  /**\n   * If `true`, the checkbox is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked = false;\n\n  /**\n   * If `true`, the user cannot interact with the checkbox.\n   */\n  @Prop() disabled = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    this.checkBoxId = this.refer || `bds-checkbox-${checkBoxIds++}`;\n  }\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<KeyboardEvent>;\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  @Method()\n  async toggle() {\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  }\n\n  private onClick = (ev: Event): void => {\n    ev.stopPropagation();\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  private getStyleState = (): string => {\n    if (this.checked && !this.disabled) {\n      return 'checkbox--selected';\n    }\n\n    if (!this.checked && !this.disabled) {\n      return 'checkbox--deselected';\n    }\n\n    if (this.checked && this.disabled) {\n      return 'checkbox--selected-disabled';\n    }\n\n    if (!this.checked && this.disabled) {\n      return 'checkbox--deselected-disabled';\n    }\n\n    return '';\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.checked = !this.checked;\n      this.bdsChange.emit({\n        checked: this.checked,\n      });\n    }\n  }\n\n  render(): HTMLElement {\n    const styleState = this.getStyleState();\n\n    return (\n      <div\n        class={{\n          checkbox: true,\n          [styleState]: true,\n        }}\n      >\n        <input\n          type=\"checkbox\"\n          ref={this.refNativeInput}\n          id={this.checkBoxId}\n          name={this.name}\n          onClick={(ev) => this.onClick(ev)}\n          checked={this.checked}\n          disabled={this.disabled}\n          data-test={this.dataTest}\n        ></input>\n        <label class=\"checkbox__label\" htmlFor={this.checkBoxId}>\n          <div class=\"checkbox__icon\" tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)}>\n            <bds-icon class=\"checkbox__icon__svg\" size=\"x-small\" name=\"true\" color=\"inherit\"></bds-icon>\n          </div>\n          {this.label && (\n            <bds-typo class=\"checkbox__text\" variant=\"fs-14\" tag=\"span\">\n              {this.label}\n            </bds-typo>\n          )}\n        </label>\n      </div>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n$select-option-padding: 8px;\n$select-option-left: 12px;\n\n:host(.option-checked) {\n  order: -1;\n}\n\n.load-spinner {\n  background-color: $color-surface-0;\n  height: 200px;\n}\n\n.select-option {\n  display: grid;\n  width: 100%;\n  @include no-select();\n  cursor: pointer;\n  background-color: $color-surface-0;\n  padding: $select-option-padding;\n  padding-left: $select-option-left;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  outline: none;\n  order: 1;\n\n  &--selected {\n    .select-option__container--value {\n      color: $color-primary;\n    }\n  }\n\n  &--disabled {\n    .select-option__container--value,\n    .select-option__container--bulk {\n      &:hover {\n        background-color: $color-surface-1;\n      }\n\n      cursor: not-allowed;\n      color: $color-content-disable;\n    }\n  }\n\n  ::slotted(bds-icon) {\n    margin-right: 10px;\n  }\n\n  &__container {\n    color: $color-content-default;\n    display: flex;\n    flex-direction: column;\n\n    &__checkbox {\n      cursor: pointer;\n      padding: $select-option-padding;\n      padding-left: $select-option-left;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n\n      & bds-checkbox {\n        pointer-events: none;\n      }\n    }\n\n    &__fill_space {\n      width: 100%;\n    }\n\n    &--bulk,\n    &--status {\n      color: $color-content-ghost;\n    }\n    &--status {\n      margin-left: 4px;\n    }\n\n    &__overflow {\n      overflow: hidden;\n      padding-right: 16px;\n    }\n\n    &:hover > &--value,\n    &:hover > &--bulk,\n    &:hover > &--status {\n      color: $color-primary;\n    }\n\n    &:active > &--value,\n    &:active > &--bulk,\n    &:active > &--status {\n      color: $color-primary;\n    }\n  }\n\n  &:hover {\n    background-color: $color-surface-1;\n  }\n\n  &:focus {\n    background-color: $color-surface-1;\n    color: $color-primary-main;\n  }\n\n  &--selected {\n    background-color: $color-surface-1;\n  }\n\n  &--invisible {\n    display: none;\n  }\n}\n", "import { Component, h, Element, Prop, EventEmitter, Event, Method, Watch } from '@stencil/core';\r\nimport { Keyboard } from '../../utils/enums';\r\n\r\nexport type TypeOption = 'checkbox' | 'default';\r\n\r\n@Component({\r\n  tag: 'bds-select-option',\r\n  styleUrl: 'select-option.scss',\r\n  shadow: true,\r\n})\r\nexport class SelectOption {\r\n  private nativeInput?: HTMLBdsCheckboxElement;\r\n\r\n  @Element() private element: HTMLElement;\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  @Prop() value!: any;\r\n\r\n  /**\r\n   * The text value of the option.\r\n   */\r\n  @Prop() selected? = false;\r\n\r\n  /**\r\n   * If `true`, the user cannot interact with the select option.\r\n   */\r\n  @Prop() disabled? = false;\r\n\r\n  /**\r\n   * Add state danger on input, use for use feedback.\r\n   */\r\n  @Prop({ reflect: true, mutable: true }) invisible? = false;\r\n\r\n  /**\r\n   * Add state danger on input, use for use feedback.\r\n   */\r\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\r\n\r\n  /**\r\n   *  Quantity Description on option value, this item is locate to rigth in component.\r\n   */\r\n  @Prop() bulkOption? = '';\r\n\r\n  /**\r\n   *  Alignment of input-left slot. The value need to be one of the values used on flexbox align-self property.\r\n   */\r\n  @Prop() slotAlign? = 'center';\r\n\r\n  /**\r\n   *  If set, a title will be shown under the text\r\n   */\r\n  @Prop() titleText: string;\r\n\r\n  /**\r\n   *  If set, a text will be displayed on the right side of the option label\r\n   */\r\n  @Prop() status?: string;\r\n\r\n  /**\r\n   * Type Option. Used toselect type of item list.\r\n   */\r\n  @Prop({ mutable: true, reflect: true }) typeOption?: TypeOption = 'default';\r\n\r\n  /**\r\n   * If `true`, the checkbox is selected.\r\n   */\r\n  @Prop({ mutable: true, reflect: true }) checked = false;\r\n\r\n  /**\r\n   * Data test is the prop to specifically test the component action object.\r\n   */\r\n  @Prop() dataTest?: string = null;\r\n\r\n  @Event() optionSelected: EventEmitter;\r\n\r\n  @Event() optionChecked: EventEmitter;\r\n\r\n  @Watch('typeOption')\r\n  protected changeSelectionType() {\r\n    this.typeOption = this.typeOption;\r\n  }\r\n\r\n  @Method()\r\n  async toggle() {\r\n    this.checked = !this.checked;\r\n  }\r\n\r\n  @Method()\r\n  async toMark() {\r\n    this.checked = true;\r\n  }\r\n\r\n  @Method()\r\n  async markOff() {\r\n    this.checked = false;\r\n  }\r\n\r\n  private refNativeInput = (input: HTMLBdsCheckboxElement): void => {\r\n    this.nativeInput = input;\r\n  };\r\n\r\n  private checkedCurrent = () => {\r\n    if (this.typeOption !== 'checkbox') return;\r\n    this.nativeInput.toggle();\r\n  };\r\n\r\n  private onClickSelectOption = (): void => {\r\n    if (this.typeOption == 'checkbox') return;\r\n    if (!this.disabled) {\r\n      this.optionSelected.emit({ value: this.value, label: this.element.innerHTML });\r\n    }\r\n  };\r\n\r\n  private optionHandle = (ev: CustomEvent): void => {\r\n    const elementChecked = ev.target as HTMLBdsCheckboxElement;\r\n    const data = { value: elementChecked.name, label: this.element.innerHTML, checked: elementChecked.checked };\r\n    this.checked = !this.checked;\r\n    this.optionChecked.emit(data);\r\n  };\r\n\r\n  private attachOptionKeyboardListeners = (event: KeyboardEvent): void => {\r\n    const element = event.target as HTMLElement;\r\n\r\n    switch (event.key) {\r\n      case Keyboard.ENTER:\r\n        this.onClickSelectOption();\r\n        break;\r\n      case Keyboard.ARROW_DOWN:\r\n        if (\r\n          element.parentElement.nextElementSibling &&\r\n          !element.parentElement.nextElementSibling.hasAttribute('invisible')\r\n        ) {\r\n          event.preventDefault();\r\n          event.stopPropagation();\r\n          (element.parentElement.nextElementSibling.firstElementChild as HTMLInputElement).focus();\r\n        }\r\n        break;\r\n      case Keyboard.ARROW_UP:\r\n        if (\r\n          element.parentElement.previousElementSibling &&\r\n          !element.parentElement.previousElementSibling.hasAttribute('invisible')\r\n        ) {\r\n          event.preventDefault();\r\n          event.stopPropagation();\r\n          (element.parentElement.previousElementSibling.firstElementChild as HTMLInputElement).focus();\r\n        }\r\n    }\r\n  };\r\n\r\n  render(): HTMLElement {\r\n    return (\r\n      <div\r\n        id={`bds-select-option-${this.value}`}\r\n        data-event=\"click\"\r\n        role=\"button\"\r\n        onKeyDown={this.attachOptionKeyboardListeners}\r\n        onClick={this.onClickSelectOption}\r\n        data-value={this.value}\r\n        data-test={this.dataTest}\r\n        class={{\r\n          'select-option': this.typeOption != 'checkbox',\r\n          'select-option--selected': this.selected,\r\n          'select-option--disabled': this.disabled,\r\n          'select-option--invisible': this.invisible,\r\n        }}\r\n      >\r\n        <div style={{ alignSelf: this.slotAlign }}>\r\n          <slot name=\"input-left\"></slot>\r\n        </div>\r\n\r\n        <div\r\n          class={{\r\n            'select-option__container': true,\r\n            'select-option__container__fill_space': !!this.status,\r\n            'select-option__container__checkbox': this.typeOption == 'checkbox',\r\n          }}\r\n          onClick={() => this.checkedCurrent()}\r\n        >\r\n          {this.titleText && (\r\n            <bds-typo class=\"select-option__container--value\" variant=\"fs-16\" bold=\"semi-bold\">\r\n              {this.titleText}\r\n            </bds-typo>\r\n          )}\r\n\r\n          {this.typeOption === 'checkbox' ? (\r\n            <bds-checkbox\r\n              ref={this.refNativeInput}\r\n              refer={`html-for-${this.value}`}\r\n              label={this.element.innerHTML}\r\n              name={this.value}\r\n              checked={this.checked}\r\n              onBdsChange={(ev) => this.optionHandle(ev)}\r\n            ></bds-checkbox>\r\n          ) : (\r\n            <bds-typo\r\n              class={{\r\n                'select-option__container--value': true,\r\n                'select-option__container__overflow': !!this.status,\r\n              }}\r\n              noWrap={!!this.status}\r\n              variant=\"fs-14\"\r\n            >\r\n              <slot />\r\n            </bds-typo>\r\n          )}\r\n        </div>\r\n        {this.bulkOption && (\r\n          <bds-typo class=\"select-option__container--bulk\" variant=\"fs-10\">\r\n            {this.bulkOption}\r\n          </bds-typo>\r\n        )}\r\n        {this.status && (\r\n          <bds-typo class=\"select-option__container--status\" noWrap={true} variant=\"fs-10\">\r\n            {this.status}\r\n          </bds-typo>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;AAAA,MAAM,WAAW,GAAG,spFAAspF;;ACE1qF,IAAI,WAAW,GAAG,CAAC;MAMN,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AAmBE;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;AAEvD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAExB;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAkCxB,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAS,KAAU;YACpC,EAAE,CAAC,eAAe,EAAE;AACpB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC;AACJ,SAAC;AAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAuB,KAAU;AACzD,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AAC1B,SAAC;AAEO,QAAA,IAAa,CAAA,aAAA,GAAG,MAAa;YACnC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,gBAAA,OAAO,oBAAoB;;YAG7B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnC,gBAAA,OAAO,sBAAsB;;YAG/B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,gBAAA,OAAO,6BAA6B;;YAGtC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,gBAAA,OAAO,+BAA+B;;AAGxC,YAAA,OAAO,EAAE;AACX,SAAC;AA4CF;IA1GC,iBAAiB,GAAA;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,CAAgB,aAAA,EAAA,WAAW,EAAE,CAAA,CAAE;;IAcjE,eAAe,GAAA;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;;IAI1C,QAAQ,GAAA;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;AAIlD,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClB,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,SAAA,CAAC;;AAmCI,IAAA,aAAa,CAAC,KAAK,EAAA;AACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC;;;IAIN,MAAM,GAAA;AACJ,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;QAEvC,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,QAAQ,EAAE,IAAI;gBACd,CAAC,UAAU,GAAG,IAAI;AACnB,aAAA,EAAA,EAED,CACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,UAAU,EACf,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,UAAU,EACnB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EACjC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACZ,WAAA,EAAA,IAAI,CAAC,QAAQ,EACjB,CAAA,EACT,CAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAA,EACrD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAC,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAC/E,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,qBAAqB,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,KAAK,EAAC,SAAS,GAAY,CACxF,EACL,IAAI,CAAC,KAAK,KACT,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAA,EACxD,IAAI,CAAC,KAAK,CACF,CACZ,CACK,CACJ;;;;;AC5IZ,MAAM,eAAe,GAAG,snFAAsnF;;MCUjoF,YAAY,GAAA,MAAA;AALzB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AAaE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAEzB;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAEzB;;AAEG;AACqC,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE1D;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAEhE;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAI,EAAE;AAExB;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAI,QAAQ;AAY7B;;AAEG;AACqC,QAAA,IAAU,CAAA,UAAA,GAAgB,SAAS;AAE3E;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;AAEvD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA0BxB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAA6B,KAAU;AAC/D,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AAC1B,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAK;AAC5B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;gBAAE;AACpC,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AAC3B,SAAC;AAEO,QAAA,IAAmB,CAAA,mBAAA,GAAG,MAAW;AACvC,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,UAAU;gBAAE;AACnC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;;AAElF,SAAC;AAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,EAAe,KAAU;AAC/C,YAAA,MAAM,cAAc,GAAG,EAAE,CAAC,MAAgC;YAC1D,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE;AAC3G,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC5B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,SAAC;AAEO,QAAA,IAAA,CAAA,6BAA6B,GAAG,CAAC,KAAoB,KAAU;AACrE,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,MAAqB;AAE3C,YAAA,QAAQ,KAAK,CAAC,GAAG;gBACf,KAAA,OAAA;oBACE,IAAI,CAAC,mBAAmB,EAAE;oBAC1B;gBACF,KAAA,WAAA;AACE,oBAAA,IACE,OAAO,CAAC,aAAa,CAAC,kBAAkB;wBACxC,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACnE;wBACA,KAAK,CAAC,cAAc,EAAE;wBACtB,KAAK,CAAC,eAAe,EAAE;wBACtB,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,iBAAsC,CAAC,KAAK,EAAE;;oBAE1F;gBACF,KAAA,SAAA;AACE,oBAAA,IACE,OAAO,CAAC,aAAa,CAAC,sBAAsB;wBAC5C,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,EACvE;wBACA,KAAK,CAAC,cAAc,EAAE;wBACtB,KAAK,CAAC,eAAe,EAAE;wBACtB,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,iBAAsC,CAAC,KAAK,EAAE;;;AAGpG,SAAC;AAwEF;IA7IW,mBAAmB,GAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;;AAInC,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;;AAI9B,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;AAIrB,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;IAuDtB,MAAM,GAAA;QACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAE,EAAE,CAAqB,kBAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,EAAA,YAAA,EAC1B,OAAO,EAClB,IAAI,EAAC,QAAQ,EACb,SAAS,EAAE,IAAI,CAAC,6BAA6B,EAC7C,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAA,YAAA,EACrB,IAAI,CAAC,KAAK,EACX,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU;gBAC9C,yBAAyB,EAAE,IAAI,CAAC,QAAQ;gBACxC,yBAAyB,EAAE,IAAI,CAAC,QAAQ;gBACxC,0BAA0B,EAAE,IAAI,CAAC,SAAS;aAC3C,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAA,EACvC,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,YAAY,EAAA,CAAQ,CAC3B,EAEN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,0BAA0B,EAAE,IAAI;AAChC,gBAAA,sCAAsC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM;AACrD,gBAAA,oCAAoC,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU;aACpE,EACD,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE,EAAA,EAEnC,IAAI,CAAC,SAAS,KACb,iEAAU,KAAK,EAAC,iCAAiC,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,IAC/E,IAAI,CAAC,SAAS,CACN,CACZ,EAEA,IAAI,CAAC,UAAU,KAAK,UAAU,IAC7B,CACE,CAAA,cAAA,EAAA,EAAA,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,CAAY,SAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,EAC/B,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAC7B,IAAI,EAAE,IAAI,CAAC,KAAK,EAChB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA,CAC5B,KAEhB,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,iCAAiC,EAAE,IAAI;AACvC,gBAAA,oCAAoC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM;AACpD,aAAA,EACD,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EACrB,OAAO,EAAC,OAAO,EAAA,EAEf,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,CACC,CACZ,CACG,EACL,IAAI,CAAC,UAAU,KACd,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,gCAAgC,EAAC,OAAO,EAAC,OAAO,EAAA,EAC7D,IAAI,CAAC,UAAU,CACP,CACZ,EACA,IAAI,CAAC,MAAM,KACV,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,kCAAkC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAC,OAAO,EAAA,EAC7E,IAAI,CAAC,MAAM,CACH,CACZ,CACG;;;;;;;;;;;"}