const e=n=>{if(n===null){return null}if(n.classList.contains("element_scrolled")||(n===null||n===void 0?void 0:n.tagName)==="BODY"){return n}else{return e(n.offsetParent)}};function n(e,n){const o=[e];while(e&&!e.matches(n)){e=e.parentElement;o.push(e)}return o}function o({actionElement:e,changedElement:n,intoView:o}){const t=o?o:document.body;const c=t.offsetParent;const s=!!t.classList.contains("element_scrolled");const a=s?e.offsetTop-t.scrollTop+c.offsetTop:e.offsetTop-window.scrollY;const l=s?e.offsetLeft+c.offsetLeft:e.offsetLeft;const i=(n===null||n===void 0?void 0:n.offsetHeight)>window.innerHeight-a?a-(n===null||n===void 0?void 0:n.offsetHeight)-16:a+(e===null||e===void 0?void 0:e.offsetHeight)+16;const r=(n===null||n===void 0?void 0:n.offsetWidth)>window.innerWidth-l?l+(e===null||e===void 0?void 0:e.offsetWidth)-(n===null||n===void 0?void 0:n.offsetWidth):l;const d=window.innerHeight-(n===null||n===void 0?void 0:n.offsetHeight);const u=window.innerWidth-(n===null||n===void 0?void 0:n.offsetWidth);const v={top:i<8?8:i>d?d-8:i,left:r<0?0:r>u?u:r};return v}function t({actionElement:e,changedElement:n,intoView:o}){const t=o?o:document.body;const c=t.offsetHeight<n.offsetHeight?window.screen.height:t.offsetHeight;const s=t.offsetWidth<n.offsetWidth?window.screen.width:t.offsetWidth;const a=c-e.offsetTop;const l=s-e.offsetLeft;const i={y:a<n.offsetHeight+e.offsetHeight?"top":"bottom",x:l<n.offsetWidth?"right":"left"};return i}const c=e=>{const n=[];let o=1;while(o<=e){const e={id:o,label:`Frame - ${o}`};n.push(e);o++}return n};const s=e=>{var n=Math.max.apply(null,e.map((e=>e.offsetHeight)));var o=e.filter((e=>e.offsetHeight==n)).map((e=>e.offsetHeight));return o};const a=e=>{let n;switch(e){case"none":n=0;break;case"half":n=4;break;case"1":n=8;break;case"2":n=16;break;case"3":n=24;break;case"4":n=32;break;case"5":n=40;break;case"6":n=48;break;case"7":n=56;break;case"8":n=64;break;case"9":n=72;break;case"10":n=80;break;case"11":n=88;break;case"12":n=96;break;default:n=0}return n};export{c as a,s as b,a as c,o as d,n as e,e as g,t as p};
//# sourceMappingURL=p-BNEKIkjk.js.map