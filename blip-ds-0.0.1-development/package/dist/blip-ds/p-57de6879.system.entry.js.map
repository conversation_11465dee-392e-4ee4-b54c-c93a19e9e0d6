{"version": 3, "names": ["CardSubtitle", "exports", "class_1", "prototype", "render", "h", "key", "variant", "tag", "bold", "margin", "this", "text"], "sources": ["src/components/card/card-subtitle/card-subtitle.tsx"], "sourcesContent": ["import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-subtitle',\n  shadow: true,\n})\nexport class CardSubtitle {\n  /**\n   *Set the card subtitle.\n   */\n  @Prop() text?: string;\n  render() {\n    return (\n      <bds-typo variant=\"fs-12\" tag=\"p\" bold=\"regular\" margin={false}>\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "mappings": "8IAMaA,EAAYC,EAAA,+B,wBAKvBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAAA,YAAAC,IAAA,2CAAUC,QAAQ,QAAQC,IAAI,IAAIC,KAAK,UAAUC,OAAQ,OACtDC,KAAKC,K,WARW,G", "ignoreList": []}