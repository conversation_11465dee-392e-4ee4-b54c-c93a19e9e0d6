{"version": 3, "file": "bds-menu-action.entry.esm.js", "sources": ["src/components/menu/menu-action/menu-action.scss?tag=bds-menu-action&encapsulation=shadow", "src/components/menu/menu-action/menu-action.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuaction {\n  position: relative;\n\n  &__button {\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    align-items: center;\n    background-color: $color-surface-1;\n    border: 0;\n    border-radius: 8px;\n    padding: 16px;\n    width: 100%;\n    text-align: left;\n    cursor: pointer;\n\n    &__activeicleft {\n      grid-template-columns: auto 1fr;\n    }\n\n    &__activeicright {\n      grid-template-columns: 1fr auto;\n    }\n\n    &__activeicleftright {\n      grid-template-columns: auto 1fr auto;\n    }\n\n    & .icon-item {\n      color: $color-content-default;\n    }\n    & .content-item {\n      width: 100%;\n      color: $color-content-default;\n      display: flex;\n      flex-direction: column;\n    }\n\n    & .arrow {\n      color: $color-content-default;\n    }\n\n    &__lipstick {\n      & .icon-item {\n        color: $color-extend-reds-lipstick;\n      }\n      & .content-item {\n        color: $color-extend-reds-lipstick;\n      }\n      & .arrow {\n        color: $color-extend-reds-lipstick;\n      }\n    }\n\n    &__disabled {\n      opacity: 0.5;\n      cursor: no-drop;\n    }\n\n    &:hover {\n      background-color: $color-surface-2;\n    }\n  }\n\n  &__submenu {\n    position: absolute;\n    pointer-events: none;\n    display: block;\n    padding: 2px;\n    background-color: $color-surface-1;\n    border-radius: 8px;\n    box-shadow: 0px 4px 16px rgba(7, 71, 166, 0.12);\n    min-width: 196px;\n    opacity: 0;\n    -webkit-transition: opacity 0.5s;\n    -moz-transition: opacity 0.5s;\n    transition: opacity 0.5s;\n\n    &__open {\n      pointer-events: auto;\n      opacity: 1;\n    }\n  }\n\n  &.position-right {\n    & .menuaction__button {\n      & .icon-item {\n        order: 0;\n      }\n      & .content-item {\n        order: 1;\n      }\n      & .arrow {\n        order: 2;\n      }\n    }\n    & .menuaction__submenu {\n      top: -2px;\n      left: 100%;\n    }\n  }\n\n  &.position-left {\n    & .menuaction__button {\n      & .icon-item {\n        order: 1;\n      }\n      & .content-item {\n        order: 2;\n      }\n      & .arrow {\n        order: 0;\n      }\n    }\n    & .menuaction__submenu {\n      top: -2px;\n      right: 100%;\n    }\n  }\n}\n", "import { Component, h, Element, State, Prop, Watch } from '@stencil/core';\n\nexport type closeSubMenuState = 'close' | 'pending' | 'open';\nexport type positionSubMenuState = 'right' | 'left';\n\n@Component({\n  tag: 'bds-menu-action',\n  styleUrl: 'menu-action.scss',\n  shadow: true,\n})\nexport class BdsMenuAction {\n  private menuElement?: HTMLBdsMenuElement;\n\n  @Element() private element: HTMLElement;\n\n  @State() openParentMenu?: boolean = false;\n  @State() openSubMenu?: boolean = false;\n  @State() positionSubMenu?: positionSubMenuState = 'right';\n  @State() stateSubMenu?: closeSubMenuState = 'close';\n  @State() delaySubMenu?: boolean = false;\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n  /**\n   * ButtonText. Used to enter the display text for the item.\n   */\n  @Prop() buttonText?: string = '';\n  /**\n   * SubMenu. Used to declare that the button will have a submenu.\n   */\n  @Prop() subMenu?: boolean = false;\n  /**\n   * Iconleft. Used to insert the string icon and make the icon available to the left of the item.\n   */\n  @Prop() iconLeft?: string = null;\n  /**\n   * Subtitle. Used to insert a subtitle in the display item.\n   */\n  @Prop() subtitle?: string = null;\n  /**\n   * Description. Used to insert a subtitle in the display item.\n   */\n  @Prop() description?: string = null;\n  /**\n   * Lipstick. Used to declare that the item will be a negative/error action.\n   */\n  @Prop() lipstick?: boolean = false;\n  /**\n   * Disabled. Used to declare that the item will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  componentWillLoad() {\n    if (this.subMenu) {\n      this.menuElement = this.element.parentElement as HTMLBdsMenuElement;\n      this.menuElement.addEventListener('bdsOpenMenu', (event) => {\n        this.onChangeOpenParent(event);\n      });\n    }\n  }\n\n  @Watch('openParentMenu')\n  protected openParentMenuChanged(active: boolean): void {\n    if (active) {\n      const divMenu = this.menuElement.shadowRoot.querySelectorAll('div')[0];\n      this.positionSubMenu = divMenu.offsetLeft + divMenu.offsetWidth + 196 >= window.innerWidth ? 'left' : 'right';\n    }\n  }\n\n  @Watch('openSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: closeSubMenuState): void {\n    switch (state) {\n      case 'open':\n        this.delaySubMenu = true;\n        break;\n      case 'pending':\n        this.delaySubMenu = true;\n        break;\n      case 'close':\n        this.delaySubMenu = false;\n        break;\n    }\n  }\n\n  private onChangeOpenParent = (event) => {\n    this.openParentMenu = event.detail.value;\n  };\n\n  render() {\n    const actLeft = this.iconLeft && !this.subMenu;\n    const actRight = this.subMenu && !this.iconLeft;\n    const actLeftright = this.iconLeft && this.subMenu;\n\n    const openSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 1;\n        this.openSubMenu = true;\n      }\n    };\n\n    const closeSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 0;\n        this.openSubMenu = false;\n      }\n    };\n\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n\n    return (\n      <div\n        class={{\n          menuaction: true,\n          [`position-${this.positionSubMenu}`]: true,\n        }}\n        onMouseOver={openSubmenu}\n        onMouseOut={closeSubmenu}\n      >\n        <button\n          class={{\n            menuaction__button: true,\n            [`menuaction__button__activeicleft`]: actLeft,\n            [`menuaction__button__activeicright`]: actRight,\n            [`menuaction__button__activeicleftright`]: actLeftright,\n            [`menuaction__button__lipstick`]: this.lipstick,\n            [`menuaction__button__disabled`]: this.disabled,\n          }}\n        >\n          {this.iconLeft && <bds-icon class=\"icon-item\" name={this.iconLeft} theme=\"outline\" size=\"small\"></bds-icon>}\n          <div class=\"content-item\">\n            {this.buttonText && (\n              <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\">\n                {this.buttonText}\n              </bds-typo>\n            )}\n            {this.subtitle && (\n              <bds-typo class=\"subtitle-item\" variant=\"fs-10\" tag=\"span\">\n                {this.subtitle}\n              </bds-typo>\n            )}\n            {this.description && (\n              <bds-typo class=\"description-item\" variant=\"fs-10\" tag=\"span\">\n                {this.description}\n              </bds-typo>\n            )}\n          </div>\n          {this.subMenu && (\n            <bds-icon\n              class={{ arrow: true }}\n              name={`arrow-${this.positionSubMenu}`}\n              theme=\"outline\"\n              size=\"small\"\n            ></bds-icon>\n          )}\n        </button>\n        {this.subMenu && (\n          <div\n            class={{\n              menuaction__submenu: true,\n              menuaction__submenu__open: this.delaySubMenu,\n            }}\n            style={zIndexSubmenu}\n          >\n            <slot />\n          </div>\n        )}\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,aAAa,GAAG,urEAAurE;;MCUhsE,aAAa,GAAA,MAAA;AAL1B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAUW,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AAChC,QAAA,IAAW,CAAA,WAAA,GAAa,KAAK;AAC7B,QAAA,IAAe,CAAA,eAAA,GAA0B,OAAO;AAChD,QAAA,IAAY,CAAA,YAAA,GAAuB,OAAO;AAC1C,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;AAC9B,QAAA,IAAM,CAAA,MAAA,GAAY,CAAC;AACnB,QAAA,IAAK,CAAA,KAAA,GAAG,IAAI;AACrB;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAY,EAAE;AAChC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;AACnC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAClC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAE1B,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;AAClC,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO;AAC7B,SAAC;AA+CO,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,KAAK,KAAI;YACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK;AAC1C,SAAC;AAqFF;IApIC,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAmC;YACnE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,KAAK,KAAI;AACzD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;AAChC,aAAC,CAAC;;;AAKI,IAAA,qBAAqB,CAAC,MAAe,EAAA;QAC7C,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,GAAG,OAAO;;;AAKvG,IAAA,kBAAkB,CAAC,MAAe,EAAA;AAC1C,QAAA,IAAI,MAAM,IAAI,KAAK,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,GAAG,SAAS;YAC7B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;;AAEpD,QAAA,IAAI,MAAM,IAAI,IAAI,EAAE;AAClB,YAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;;;AAKpB,IAAA,mBAAmB,CAAC,KAAwB,EAAA;QACpD,QAAQ,KAAK;AACX,YAAA,KAAK,MAAM;AACT,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;gBACxB;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;gBACxB;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK;gBACzB;;;IAQN,MAAM,GAAA;QACJ,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO;QAElD,MAAM,WAAW,GAAG,MAAK;AACvB,YAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC;AACf,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;AAE3B,SAAC;QAED,MAAM,YAAY,GAAG,MAAK;AACxB,YAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC;AACf,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK;;AAE5B,SAAC;AAED,QAAA,MAAM,aAAa,GAAG;AACpB,YAAA,MAAM,EAAE,CAAA,EAAG,IAAI,CAAC,MAAM,CAAE,CAAA;SACzB;QAED,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,CAAC,YAAY,IAAI,CAAC,eAAe,CAAE,CAAA,GAAG,IAAI;AAC3C,aAAA,EACD,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,YAAY,EAAA,EAExB,CAAA,CAAA,QAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,kBAAkB,EAAE,IAAI;gBACxB,CAAC,CAAA,gCAAA,CAAkC,GAAG,OAAO;gBAC7C,CAAC,CAAA,iCAAA,CAAmC,GAAG,QAAQ;gBAC/C,CAAC,CAAA,qCAAA,CAAuC,GAAG,YAAY;AACvD,gBAAA,CAAC,CAA8B,4BAAA,CAAA,GAAG,IAAI,CAAC,QAAQ;AAC/C,gBAAA,CAAC,CAA8B,4BAAA,CAAA,GAAG,IAAI,CAAC,QAAQ;aAChD,EAAA,EAEA,IAAI,CAAC,QAAQ,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,WAAW,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,OAAO,EAAY,CAAA,EAC3G,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACtB,IAAI,CAAC,UAAU,KACd,iEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACpD,IAAI,CAAC,UAAU,CACP,CACZ,EACA,IAAI,CAAC,QAAQ,KACZ,iEAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACvD,IAAI,CAAC,QAAQ,CACL,CACZ,EACA,IAAI,CAAC,WAAW,KACf,iEAAU,KAAK,EAAC,kBAAkB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAA,EAC1D,IAAI,CAAC,WAAW,CACR,CACZ,CACG,EACL,IAAI,CAAC,OAAO,KACX,CACE,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EACtB,IAAI,EAAE,CAAA,MAAA,EAAS,IAAI,CAAC,eAAe,CAAE,CAAA,EACrC,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EAAA,CACF,CACb,CACM,EACR,IAAI,CAAC,OAAO,KACX,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,mBAAmB,EAAE,IAAI;gBACzB,yBAAyB,EAAE,IAAI,CAAC,YAAY;AAC7C,aAAA,EACD,KAAK,EAAE,aAAa,EAAA,EAEpB,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,CACP,CACG;;;;;;;;;;;;;"}