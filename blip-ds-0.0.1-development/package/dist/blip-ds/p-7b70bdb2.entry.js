import{r as e,h as s,H as t}from"./p-C3J6Z5OX.js";const i=":host{display:block;-webkit-box-sizing:border-box;box-sizing:border-box;width:auto}.carrosel-item-frame{display:block;width:100%;height:100%;border-radius:8px;position:relative;overflow:hidden}.carrosel-item-frame ::slotted(*){position:relative}.image-bg{position:absolute;width:100%;height:100%}";const o=class{constructor(s){e(this,s);this.theme="light";this.bgImageBrightness=1}render(){return s(t,{key:"c14d96bc55b5e5008d3fcb707876050b39c509c6",class:"carrosel-item"},s("bds-theme-provider",{key:"b32b18a61e3648d8c0972f700d1b231ded71b620",theme:this.theme,class:"carrosel-item-frame",style:{background:this.bgColor}},this.bgImage&&s("bds-image",{key:"89a3347329bee52dd4fbd8646d9a415afa73ec84",class:"image-bg",alt:"Example of a image",width:"100%",height:"100%",brightness:this.bgImageBrightness,"object-fit":"cover",src:this.bgImage}),s("slot",{key:"bcd4d4d74eb34c667a76e114447e37927a389f71"})))}};o.style=i;export{o as bds_carousel_item};
//# sourceMappingURL=p-7b70bdb2.entry.js.map