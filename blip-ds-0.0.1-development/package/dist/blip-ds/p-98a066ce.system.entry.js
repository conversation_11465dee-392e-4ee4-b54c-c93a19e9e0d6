System.register(["./p-B47mPBRA.system.js"],(function(o){"use strict";var r,c;return{setters:[function(o){r=o.r;c=o.h}],execute:function(){var e=".card{cursor:pointer}.card-color{width:239px;height:136px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;border-radius:8px;-webkit-box-shadow:0px 2px 8px -2px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 2px 8px -2px var(--color-shadow-1, rgba(0, 0, 0, 0.16));margin-left:8px;margin-top:8px}.card-color--color{-ms-flex:2;flex:2;border-top-left-radius:8px;border-top-right-radius:8px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.card-color--color-brand{background-color:var(--color-brand, rgb(0, 150, 250))}.card-color--color-primary{background-color:var(--color-primary, rgb(30, 107, 241))}.card-color--color-secondary{background-color:var(--color-secondary, rgb(41, 41, 41))}.card-color--color-surface-1{background-color:var(--color-surface-1, rgb(246, 246, 246))}.card-color--color-surface-2{background-color:var(--color-surface-2, rgb(237, 237, 237))}.card-color--color-surface-3{background-color:var(--color-surface-3, rgb(227, 227, 227))}.card-color--color-surface-4{background-color:var(--color-surface-4, rgb(20, 20, 20))}.card-color--color-content-default{background-color:var(--color-content-default, rgb(40, 40, 40))}.card-color--color-content-disable{background-color:var(--color-content-disable, rgb(89, 89, 89))}.card-color--color-content-ghost{background-color:var(--color-content-ghost, rgb(140, 140, 140))}.card-color--color-content-bright{background-color:var(--color-content-bright, rgb(255, 255, 255))}.card-color--color-content-din{background-color:var(--color-content-din, rgb(0, 0, 0))}.card-color--color-border-1{background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.card-color--color-border-2{background-color:var(--color-border-2, rgba(0, 0, 0, 0.16))}.card-color--color-border-3{background-color:var(--color-border-3, rgba(0, 0, 0, 0.06))}.card-color--color-info{background-color:var(--color-info, rgb(128, 227, 235))}.card-color--color-system{background-color:var(--color-system, rgb(178, 223, 253))}.card-color--color-focus{background-color:var(--color-focus, rgb(194, 38, 251))}.card-color--color-success{background-color:var(--color-success, rgb(132, 235, 188))}.card-color--color-warning{background-color:var(--color-warning, rgb(253, 233, 155))}.card-color--color-error{background-color:var(--color-error, rgb(250, 190, 190))}.card-color--color-delete{background-color:var(--color-delete, rgb(230, 15, 15))}.card-color--color-extended-blue{background-color:var(--color-extended-blue, rgb(25, 104, 240))}.card-color--color-extended-ocean{background-color:var(--color-extended-ocean, rgb(0, 211, 228))}.card-color--color-extended-green{background-color:var(--color-extended-green, rgb(53, 222, 144))}.card-color--color-extended-yellow{background-color:var(--color-extended-yellow, rgb(251, 207, 35))}.card-color--color-extended-orange{background-color:var(--color-extended-orange, rgb(240, 99, 5))}.card-color--color-extended-red{background-color:var(--color-extended-red, rgb(230, 15, 15))}.card-color--color-extended-pink{background-color:var(--color-extended-pink, rgb(251, 75, 193))}.card-color--color-extended-gray{background-color:var(--color-extended-gray, rgb(102, 102, 102))}.card-text{-webkit-animation:invert-motion 1s;animation:invert-motion 1s}.card-text-copie{-webkit-animation:motion 3s 1;animation:motion 3s 1}@-webkit-keyframes motion{0%{height:0}30%{height:50%}85%{letter-spacing:0}}@keyframes motion{0%{height:0}30%{height:50%}85%{letter-spacing:0}}@-webkit-keyframes invert-motion{0%{height:100%}30%{height:50%}85%{letter-spacing:0}}@keyframes invert-motion{0%{height:100%}30%{height:50%}85%{letter-spacing:0}}";var a=o("bds_card_color",function(){function o(o){var c=this;r(this,o);this.showMessage=false;this.gradient=false;this.lightText=false;this.handleCopyVariable=function(o){var r="$".concat(o);navigator.clipboard.writeText(r);c.showMessage=true;setTimeout((function(){c.showMessage=false}),3e3)}}o.prototype.render=function(){var o;var r=this;return c("bds-paper",{key:"a899922aee333016222e09ffdd6fcb8ded2d7f8b",class:"card",width:"240px",height:"140px",onClick:function(){return r.handleCopyVariable(r.variable)}},c("bds-grid",{key:"312992180a07b30d6911aba4bce71a2a8adeaa7e",direction:"column",height:"100%"},c("bds-grid",{key:"6c7778937650370b14fd367492f0fd06901dd9a5",height:"70%",xxs:"12",class:(o={"card-color--color":true},o["card-color--".concat(this.variable)]=true,o)}),c("bds-grid",{key:"8fe88497bab0f9849c9246f1f87f044cc752ed21","justify-content":"center","align-items":"center",height:"30%"},!this.showMessage?c("bds-typo",{class:"card-text",variant:"fs-14",bold:"bold"},"$",this.variable):c("bds-typo",{class:"card-text-copie",variant:"fs-14",bold:"bold"},"Cor copiada!"))))};return o}());a.style=e}}}));
//# sourceMappingURL=p-98a066ce.system.entry.js.map