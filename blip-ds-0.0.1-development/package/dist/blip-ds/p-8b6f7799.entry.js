import{r as o,h as e,a as r}from"./p-C3J6Z5OX.js";const t='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';const i=class{constructor(e){o(this,e);this.accordionElement=null;this.isOpen=false;this.btToggleIsfocus=false;this.numberElement=null;this.accordionTitle=null;this.icon=null;this.avatarName=null;this.avatarThumb=null;this.dataTest=null;this.toggleHeader=()=>{var o,e;if(this.isOpen){(o=this.accordionElement)===null||o===void 0?void 0:o.close()}else{(e=this.accordionElement)===null||e===void 0?void 0:e.open()}}}async toggle(){this.isOpen=!this.isOpen}async open(){this.isOpen=true}async close(){this.isOpen=false}componentWillRender(){this.accordionElement=this.element.parentElement}handleKeyDown(o){var e,r;if(o.key=="Enter"){if(this.isOpen){(e=this.accordionElement)===null||e===void 0?void 0:e.close()}else{(r=this.accordionElement)===null||r===void 0?void 0:r.open()}}}render(){return e("div",{key:"187061591a0438fad6432f12708fb0b1d6ae2f2a",onClick:this.toggleHeader,class:{accordion_header:true},"data-test":this.dataTest},this.avatarName||this.avatarThumb?e("bds-avatar",{name:this.avatarName,thumbnail:this.avatarThumb,size:"extra-small"}):this.icon&&e("bds-icon",{size:"x-large",name:this.icon,color:"inherit"}),this.accordionTitle&&e("bds-typo",{key:"7bdadadb86dc1cc7e5aa85a28e9e6b70b39eed24",bold:"bold",variant:"fs-16","line-height":"double"},this.accordionTitle),e("slot",{key:"94a857b65dbdc8ebe1621e4436d8c0414444fd95"}),e("bds-icon",{key:"9733311a10ebe3849f53cb13614d52dc56b2b7ab",class:{accButton:true,accButton__isopen:this.isOpen,accButton__isfocus:this.btToggleIsfocus},size:"x-large",name:"arrow-down",color:"inherit",tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)}))}get element(){return r(this)}};i.style=t;export{i as bds_accordion_header};
//# sourceMappingURL=p-8b6f7799.entry.js.map