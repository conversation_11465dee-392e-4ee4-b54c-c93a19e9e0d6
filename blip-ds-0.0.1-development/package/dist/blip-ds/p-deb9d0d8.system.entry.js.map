{"version": 3, "names": ["cardHeaderCss", "<PERSON><PERSON><PERSON><PERSON>", "exports", "class_1", "hostRef", "this", "align", "prototype", "render", "h", "key", "xxs", "direction", "gap", "justifyContent", "alignItems"], "sources": ["src/components/card/card-header/card-header.scss?tag=bds-card-header&encapsulation=shadow", "src/components/card/card-header/card-header.tsx"], "sourcesContent": [":host {\n    width: 100%;\n}", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n\n@Component({\n  tag: 'bds-card-header',\n  styleUrl: 'card-header.scss',\n  shadow: true,\n})\nexport class CardHeader implements ComponentInterface {\n  /**\n   * Prop for internal elements alignment. Will follow the same values of css.\n   */\n  @Prop() align?: justifyContent = 'space-between';\n\n  render() {\n    return (\n      <bds-grid xxs=\"12\" direction=\"row\" gap=\"1\" justifyContent={this.align} alignItems=\"center\">\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAgB,oB,ICSTC,EAAUC,EAAA,6BALvB,SAAAC,EAAAC,G,UASUC,KAAKC,MAAoB,eASlC,CAPCH,EAAAI,UAAAC,OAAA,WACE,OACEC,EAAA,YAAAC,IAAA,2CAAUC,IAAI,KAAKC,UAAU,MAAMC,IAAI,IAAIC,eAAgBT,KAAKC,MAAOS,WAAW,UAChFN,EAAQ,QAAAC,IAAA,6C,WATO,I", "ignoreList": []}