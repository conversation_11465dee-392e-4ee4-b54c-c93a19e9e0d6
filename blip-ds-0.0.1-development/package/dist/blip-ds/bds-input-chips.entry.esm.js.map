{"version": 3, "file": "bds-input-chips.entry.esm.js", "sources": ["src/components/input-chips/input-chips.scss?tag=bds-input-chips&encapsulation=shadow", "src/components/input-chips/input-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n\n:host {\n  display: display;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      gap: 6px;\n      min-height: 24px;\n      max-width: 100%;\n      overflow-y: auto;\n      overflow-x: hidden; /* Force no horizontal scroll */\n      word-break: break-word; /* Break long text in chips */\n      @include custom-scroll;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      min-width: 80px; /* Reduced from 120px */\n      max-width: 100%;\n      flex: 1;\n      border: none;\n      outline: none;\n      background: transparent;\n      resize: none;\n      cursor: inherit;\n      overflow: hidden; /* Prevent text overflow */\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n", "import { Component, Host, h, Prop, Method, Event, EventEmitter, Watch, State } from '@stencil/core';\nimport { emailValidation, whitespaceValidation } from '../../utils/validations';\nimport { InputChipsTypes } from './input-chips-interface';\n\n@Component({\n  tag: 'bds-input-chips',\n  styleUrl: 'input-chips.scss',\n  shadow: true,\n})\nexport class InputChips {\n  private nativeInput?: HTMLInputElement;\n\n  @State() InputSize?: number = null;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Used to enable or disable input\n   */\n  @State() inputAvalible?: boolean = true;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string[] | string = [];\n\n  /**\n   * When true, the press enter will be simulated on blur event.\n   */\n  @Prop() blurCreation = false;\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   *  Set maximum length value for the chip content\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   *  Set maximum length value for chips\n   */\n  @Prop() maxChipsLength?: number;\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n\n  /**\n   * Disabled input\n   */\n  @Prop({ reflect: true }) disabled?: boolean = false;\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Passing true to display a counter of available size, it is necessary to\n   * pass another maxlength property.\n   */\n  @Prop() counterLength? = false;\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsFocus!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsBlur!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsInputChipsInput!: EventEmitter;\n\n  /**\n   * Emitted when a maximum value defined by the \"max-chips-length\" prop is entered\n   */\n  @Event() bdsExtendedQuantityInput!: EventEmitter;\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.minMaxValidation();\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async get(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentDidLoad() {\n    this.minMaxValidation();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n  }\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsInputChipsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit(this.internalChips);\n    if (this.internalChips.length > 0) {\n      this.bdsSubmit.emit({ value: this.internalChips });\n    }\n    this.handleDelimiters();\n    this.isPressed = false;\n    if (this.blurCreation) {\n      this.setChip(this.value);\n      this.value = '';\n    }\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInputChipsInput.emit(ev);\n  };\n\n  private minMaxValidation() {\n    if (!this.maxChipsLength == undefined) {\n      this.inputAvalible = true;\n    } else if (this.internalChips.length >= this.maxChipsLength) {\n      this.inputAvalible = false;\n      this.bdsExtendedQuantityInput.emit({ value: !this.inputAvalible });\n    } else {\n      this.inputAvalible = true;\n    }\n  }\n\n  private getLastChip(): string {\n    return this.internalChips[this.internalChips.length - 1];\n  }\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.handleDelimiters();\n        this.setChip(this.value);\n        this.value = '';\n        this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n        this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n        }\n        break;\n    }\n  };\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    const {\n      detail: { value },\n    } = event;\n\n    // console.log('TRACE [input-chips] handleChange 1:', { value });\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChange.emit({ data: this.internalChips, value: this.getLastChip() });\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.getLastChip() });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      // Reduce the limit to prevent chips from being too wide and causing scroll issues\n      const limit = 20;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n            dtButtonClose={this.dtButtonClose}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n              dtButtonClose={this.dtButtonClose}\n            >\n              {`${chip.slice(0, limit)}...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n    // Set default maxHeight if not provided to prevent UI breaking\n    const defaultMaxHeight = this.maxHeight || '80px';\n    \n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div \n              class=\"input__container__wrapper\"\n              style={{ maxHeight: defaultMaxHeight }}\n            >\n              {/* Chips and input are now siblings in the same flex container */}\n              {this.internalChips.length > 0 && this.renderChips()}\n              {this.inputAvalible && (\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class=\"input__container__text\"\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                />\n              )}\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text length={this.internalChips.length} max={this.maxChipsLength} active={isPressed} />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"checkb\" theme=\"outline\" size=\"xxx-small\" />}\n          <slot name=\"input-right\"></slot>\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,GAAG,g7QAAg7Q;;MCSz7Q,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;AAQW,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AAElC;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAC3C;;AAEG;AACM,QAAA,IAAa,CAAA,aAAA,GAAa,IAAI;AACvC;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAEtB,QAAA,IAAa,CAAA,aAAA,GAAa,EAAE;AAErC;;;;AAIG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAsB,EAAE;AAEtD;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAG,KAAK;AAE5B;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAoB,MAAM;AAEtC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;AAWnB;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAI,KAAK;AAE3B;;AAEG;AACsB,QAAA,IAAY,CAAA,YAAA,GAAI,EAAE;AAE3C;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AACvD;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACqC,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;AAElE;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;AAEpC;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAG,KAAK;AAE7B;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAEnD;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AACnC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAE/B;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAI,KAAK;AAS9B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AA0H7B,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC;AAeO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;AACzC,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;AAEhC,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;AAClC,SAAC;AAiBO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;AACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;AACf,gBAAA,KAAK,OAAO;oBACV,IAAI,CAAC,gBAAgB,EAAE;AACvB,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,oBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;oBACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBAC5E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACjF;AACF,gBAAA,KAAK,WAAW;AAChB,gBAAA,KAAK,QAAQ;oBACX,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;wBAChF,IAAI,CAAC,cAAc,EAAE;wBACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBAC5E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;;oBAEnF;;AAEN,SAAC;AA6QF;AAtaC;;AAEG;IAEO,YAAY,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAClC,gBAAA,IAAI;oBACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;gBAC3C,OAAA,EAAA,EAAM;AACN,oBAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;iBAEpB;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;;;aAE5B;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;IAKjB,oBAAoB,GAAA;QAC5B,IAAI,CAAC,gBAAgB,EAAE;;AAGzB;;AAEG;AAEH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;AAG7B;;AAEG;AAEH,IAAA,MAAM,GAAG,GAAA;QACP,OAAO,IAAI,CAAC,aAAa;;AAG3B;;AAEG;AAEH,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;IAIjB,MAAM,GAAG,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;aACd;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE1B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;AAIjB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAI1B,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;IAGzB,gBAAgB,GAAA;QACd,IAAI,CAAC,gBAAgB,EAAE;;IAGzB,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,EAAE;;IAGb,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;aAC9D;AACL,YAAA,OAAO,IAAI;;;IAgBP,YAAY,GAAA;QAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;QACrC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;;QAEpD,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;;IAYX,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,SAAS,EAAE;AACrC,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;aACpB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;AAC3D,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,YAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;;aAC7D;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;IAIrB,WAAW,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;;IAuBlD,gBAAgB,GAAA;AACtB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;AACpC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;AAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE;QAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS;YAAE;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE;YACvB;;QAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACpB,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE;;AAGjB,IAAA,6BAA6B,CAAC,KAAa,EAAA;AACjD,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzD,YAAA,OAAO,EAAE;;AAGX,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;AAE/D,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACtC,YAAA,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;AAGlC,QAAA,OAAO,QAAQ;;IAGT,MAAM,YAAY,CAAC,KAAqC,EAAA;QAC9D,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;;AAIT,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;AAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE;QAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS;YAAE;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE;YACvB;;QAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AAChC,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;;IAG3E,gBAAgB,CAAC,KAAK,GAAG,EAAE,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK;AAC9B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;AAGZ,IAAA,OAAO,CAAC,IAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3F,YAAA,IAAI,MAAM;gBAAE;;AAGd,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;YAC/B;;QAGF,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC;;AAG5C,IAAA,YAAY,CAAC,IAAY,EAAA;AAC/B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE;AACzD,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,IAAI;;IAGL,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGzE,IAAA,UAAU,CAAC,KAAkC,EAAA;QACnD,MAAM,EACJ,MAAM,EAAE,EAAE,EAAE,EAAE,GACf,GAAG,KAAK;QAET,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;QACzF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;;IAG3E,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AAC9B,YAAA,OAAO,EAAE;;QAGX,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC5C,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE;;YAE3B,MAAM,KAAK,GAAG,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;gBACxB,QACE,0BACE,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACvD,aAAa,EAAE,IAAI,CAAC,aAAa,IAEhC,IAAI,CACc;;iBAElB;AACL,gBAAA,QACE,CAAa,CAAA,aAAA,EAAA,EAAA,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,cAAA,EAAe,IAAI,EAAA,EAC5D,CACE,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACvD,aAAa,EAAE,IAAI,CAAC,aAAa,EAEhC,EAAA,CAAA,EAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CACV,CACT;;AAGpB,SAAC,CAAC;;IAGI,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;IAGlB,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;;AAElD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM;AAEjD,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;AAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnB,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,2BAA2B,EACjC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,EAAA,EAGrC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,EACnD,IAAI,CAAC,aAAa,KACjB,CACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,KAAK,EAAC,wBAAwB,EAC9B,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,EACjC,QAAQ,EAAE,MAAM,IAAI,CAAC,YAAY,EACjC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,EACxB,CAAA,CACH,CACG,CACF,EACL,IAAI,CAAC,aAAa,KACjB,yEAAkB,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,SAAS,EAAA,CAAI,CACrG,EACA,IAAI,CAAC,OAAO,IAAI,iEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,EAAG,CAAA,EACjG,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,aAAa,EAAA,CAAQ,CAC5B,EACL,IAAI,CAAC,aAAa,EAAE,CAChB;;;;;;;;;;;"}