{"version": 3, "file": "bds-button-group.entry.esm.js", "sources": ["src/components/button/button-group.scss?tag=bds-button-group&encapsulation=shadow", "src/components/button/button-group.tsx"], "sourcesContent": [":host {\n    width: fit-content;\n}", "import { Component, h, Element, State, Event, EventEmitter, Prop, Host, Watch, Method } from '@stencil/core';\nimport { direction } from '../grid/grid-interface';\nimport { ButtonSize } from './button';\n\ninterface HTMLBdsButtonElement extends HTMLElement {\n  setVariant(variant: string): void;\n  setColor(color: string): void;\n  setSize(size: string): void;\n  setDirection(direction: string): void;\n  isActive(active: boolean): void;\n  setPosition(position: string): void;\n}\n\n@Component({\n  tag: 'bds-button-group',\n  styleUrl: 'button-group.scss',\n  shadow: true,\n})\nexport class ButtonGroup {\n  @Element() el!: HTMLElement;\n\n  @State() activeIndexes: Set<number> = new Set();\n\n  /**\n   * Size of the buttons. Can be one of:\n   * 'medium', 'large'.\n   */\n  @Prop({ mutable: true }) size?: ButtonSize = 'medium';\n\n  /**\n   * Direction of the button group layout. Can be one of:\n   * 'row', 'column'.\n   */\n  @Prop({ mutable: true }) direction?: direction = 'row';\n\n  /**\n   * Color scheme for the buttons. Default is 'primary'.\n   */\n  @Prop({ mutable: true }) color?: string = 'primary';\n\n  /**\n   * Allows multiple buttons to be selected simultaneously if true.\n   */\n  @Prop({ mutable: true }) multiple? = false;\n\n  @Event() buttonSelected: EventEmitter;\n\n  private buttons: HTMLCollectionOf<HTMLBdsButtonElement>;\n\n  componentDidLoad() {\n    this.buttons = this.el.getElementsByTagName('bds-button') as HTMLCollectionOf<HTMLBdsButtonElement>;\n    this.setupButtons();\n  }\n\n  componentDidUpdate() {\n    this.setupButtons();\n  }\n\n  @Watch('size')\n  @Watch('direction')\n  @Watch('color')\n  @Watch('multiple')\n  handlePropChanges() {\n    // Re-setup buttons when props change\n    this.setupButtons();\n  }\n\n  setupButtons() {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      button.setAttribute('data-index', i.toString());\n      button.addEventListener('click', () => this.selectButton(i));\n      button.setVariant('outline');\n      this.updateButtonPosition(i);\n      this.updateButtonDirection(i);\n      this.updateButtonSize(i);\n      this.updateButtonColor(i);\n    }\n  }\n\n  @Method()\n  async activateButton(index: number) {\n    if (index >= 0 && index < this.buttons.length) {\n      this.selectButton(index);\n    }\n  }\n\n  selectButton(index: number) {\n    if (this.multiple) {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.delete(index);\n      } else {\n        this.activeIndexes.add(index);\n      }\n    } else {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.clear();\n      } else {\n        this.activeIndexes.clear();\n        this.activeIndexes.add(index);\n      }\n    }\n    this.updateButtonStates(index);\n  }\n\n  updateButtonStates(clickedIndex: number) {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      if (this.activeIndexes.has(i)) {\n        button.isActive(true);\n        button.setVariant('solid');\n        button.classList.add('active');\n      } else {\n        button.isActive(false);\n        button.setVariant('outline');\n        button.classList.remove('active');\n      }\n      if (i === clickedIndex) {\n        this.buttonSelected.emit(button.id);\n      }\n    }\n  }\n\n  updateButtonPosition(index: number) {\n    const button = this.buttons[index];\n    if (index === 0) {\n      button.setPosition('first');\n    } else if (index === this.buttons.length - 1) {\n      button.setPosition('last');\n    } else {\n      button.setPosition('middle');\n    }\n  }\n\n  updateButtonDirection(index: number) {\n    const button = this.buttons[index];\n    this.direction === 'row' ? button.setDirection('row') : button.setDirection('column');\n  }\n\n  updateButtonSize(index: number) {\n    const button = this.buttons[index];\n    this.size === 'medium' ? button.setSize('medium') : button.setSize('large');\n  }\n\n  updateButtonColor(index: number) {\n    const button = this.buttons[index];\n    button.setColor(this.color);\n  }\n\n  render() {\n    return (\n      <Host class=\"button_group\">\n        <bds-grid direction={this.direction}>\n          <slot></slot>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAG,2EAA2E;;MCkBrF,WAAW,GAAA,MAAA;AALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAQW,QAAA,IAAA,CAAA,aAAa,GAAgB,IAAI,GAAG,EAAE;AAE/C;;;AAGG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAgB,QAAQ;AAErD;;;AAGG;AACsB,QAAA,IAAS,CAAA,SAAA,GAAe,KAAK;AAEtD;;AAEG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAY,SAAS;AAEnD;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAmH3C;IA7GC,gBAAgB,GAAA;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,oBAAoB,CAAC,YAAY,CAA2C;QACnG,IAAI,CAAC,YAAY,EAAE;;IAGrB,kBAAkB,GAAA;QAChB,IAAI,CAAC,YAAY,EAAE;;IAOrB,iBAAiB,GAAA;;QAEf,IAAI,CAAC,YAAY,EAAE;;IAGrB,YAAY,GAAA;AACV,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC/C,YAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC5D,YAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAC5B,YAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;IAK7B,MAAM,cAAc,CAAC,KAAa,EAAA;AAChC,QAAA,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;AAI5B,IAAA,YAAY,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACjC,gBAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC;;iBAC3B;AACL,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;;;aAE1B;YACL,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACjC,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;iBACrB;AACL,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;AAC1B,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;;;AAGjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;;AAGhC,IAAA,kBAAkB,CAAC,YAAoB,EAAA;AACrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC7B,gBAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACrB,gBAAA,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AAC1B,gBAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;iBACzB;AACL,gBAAA,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtB,gBAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;AAC5B,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAEnC,YAAA,IAAI,CAAC,KAAK,YAAY,EAAE;gBACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;;;;AAKzC,IAAA,oBAAoB,CAAC,KAAa,EAAA;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAClC,QAAA,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,YAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC;;aACtB,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,YAAA,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;;aACrB;AACL,YAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;;;AAIhC,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;;AAGvF,IAAA,gBAAgB,CAAC,KAAa,EAAA;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;;AAG7E,IAAA,iBAAiB,CAAC,KAAa,EAAA;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAClC,QAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;IAG7B,MAAM,GAAA;QACJ,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACxB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,SAAS,EAAE,IAAI,CAAC,SAAS,EAAA,EACjC,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACJ,CACN;;;;;;;;;;;;;;"}