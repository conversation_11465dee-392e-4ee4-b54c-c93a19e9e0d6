var __awaiter=this&&this.__awaiter||function(t,i,e,n){function r(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,s){function o(t){try{c(n.next(t))}catch(t){s(t)}}function u(t){try{c(n["throw"](t))}catch(t){s(t)}}function c(t){t.done?e(t.value):r(t.value).then(o,u)}c((n=n.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,r,s,o;return o={next:u(0),throw:u(1),return:u(2)},typeof Symbol==="function"&&(o[Symbol.iterator]=function(){return this}),o;function u(t){return function(i){return c([t,i])}}function c(u){if(n)throw new TypeError("Generator is already executing.");while(o&&(o=0,u[0]&&(e=0)),e)try{if(n=1,r&&(s=u[0]&2?r["return"]:u[0]?r["throw"]||((s=r["return"])&&s.call(r),0):r.next)&&!(s=s.call(r,u[1])).done)return s;if(r=0,s)u=[u[0]&2,s.value];switch(u[0]){case 0:case 1:s=u;break;case 4:e.label++;return{value:u[1],done:false};case 5:e.label++;r=u[1];u=[0];continue;case 7:u=e.ops.pop();e.trys.pop();continue;default:if(!(s=e.trys,s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){e=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){e.label=u[1];break}if(u[0]===6&&e.label<s[1]){e.label=s[1];s=u;break}if(s&&e.label<s[2]){e.label=s[2];e.ops.push(u);break}if(s[2])e.ops.pop();e.trys.pop();continue}u=i.call(t,e)}catch(t){u=[6,t];r=0}finally{n=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var i,e,n,r,s;return{setters:[function(t){i=t.r;e=t.c;n=t.h;r=t.H;s=t.a}],execute:function(){var o=":host{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}";var u=t("bds_button_group",function(){function t(t){i(this,t);this.buttonSelected=e(this,"buttonSelected");this.activeIndexes=new Set;this.size="medium";this.direction="row";this.color="primary";this.multiple=false}t.prototype.componentDidLoad=function(){this.buttons=this.el.getElementsByTagName("bds-button");this.setupButtons()};t.prototype.componentDidUpdate=function(){this.setupButtons()};t.prototype.handlePropChanges=function(){this.setupButtons()};t.prototype.setupButtons=function(){var t=this;var i=function(i){var n=e.buttons[i];n.setAttribute("data-index",i.toString());n.addEventListener("click",(function(){return t.selectButton(i)}));n.setVariant("outline");e.updateButtonPosition(i);e.updateButtonDirection(i);e.updateButtonSize(i);e.updateButtonColor(i)};var e=this;for(var n=0;n<this.buttons.length;n++){i(n)}};t.prototype.activateButton=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){if(t>=0&&t<this.buttons.length){this.selectButton(t)}return[2]}))}))};t.prototype.selectButton=function(t){if(this.multiple){if(this.activeIndexes.has(t)){this.activeIndexes.delete(t)}else{this.activeIndexes.add(t)}}else{if(this.activeIndexes.has(t)){this.activeIndexes.clear()}else{this.activeIndexes.clear();this.activeIndexes.add(t)}}this.updateButtonStates(t)};t.prototype.updateButtonStates=function(t){for(var i=0;i<this.buttons.length;i++){var e=this.buttons[i];if(this.activeIndexes.has(i)){e.isActive(true);e.setVariant("solid");e.classList.add("active")}else{e.isActive(false);e.setVariant("outline");e.classList.remove("active")}if(i===t){this.buttonSelected.emit(e.id)}}};t.prototype.updateButtonPosition=function(t){var i=this.buttons[t];if(t===0){i.setPosition("first")}else if(t===this.buttons.length-1){i.setPosition("last")}else{i.setPosition("middle")}};t.prototype.updateButtonDirection=function(t){var i=this.buttons[t];this.direction==="row"?i.setDirection("row"):i.setDirection("column")};t.prototype.updateButtonSize=function(t){var i=this.buttons[t];this.size==="medium"?i.setSize("medium"):i.setSize("large")};t.prototype.updateButtonColor=function(t){var i=this.buttons[t];i.setColor(this.color)};t.prototype.render=function(){return n(r,{key:"dc12bbfd11ecdfc7a39bb3474bbef396d8182d3f",class:"button_group"},n("bds-grid",{key:"b2a0f033711ac0dd8c0bf6c773fba095d8d91be8",direction:this.direction},n("slot",{key:"877672a01e95c7fb6dfbeecafabd7ce93ebc0dc0"})))};Object.defineProperty(t.prototype,"el",{get:function(){return s(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{size:["handlePropChanges"],direction:["handlePropChanges"],color:["handlePropChanges"],multiple:["handlePropChanges"]}},enumerable:false,configurable:true});return t}());u.style=o}}}));
//# sourceMappingURL=p-709fef30.system.entry.js.map