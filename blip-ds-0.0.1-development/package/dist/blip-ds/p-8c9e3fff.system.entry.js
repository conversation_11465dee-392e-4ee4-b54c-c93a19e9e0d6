var __awaiter=this&&this.__awaiter||function(e,i,t,r){function o(e){return e instanceof t?e:new t((function(i){i(e)}))}return new(t||(t=Promise))((function(t,a){function s(e){try{d(r.next(e))}catch(e){a(e)}}function n(e){try{d(r["throw"](e))}catch(e){a(e)}}function d(e){e.done?t(e.value):o(e.value).then(s,n)}d((r=r.apply(e,i||[])).next())}))};var __generator=this&&this.__generator||function(e,i){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,o,a,s;return s={next:n(0),throw:n(1),return:n(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function n(e){return function(i){return d([e,i])}}function d(n){if(r)throw new TypeError("Generator is already executing.");while(s&&(s=0,n[0]&&(t=0)),t)try{if(r=1,o&&(a=n[0]&2?o["return"]:n[0]?o["throw"]||((a=o["return"])&&a.call(o),0):o.next)&&!(a=a.call(o,n[1])).done)return a;if(o=0,a)n=[n[0]&2,a.value];switch(n[0]){case 0:case 1:a=n;break;case 4:t.label++;return{value:n[1],done:false};case 5:t.label++;o=n[1];n=[0];continue;case 7:n=t.ops.pop();t.trys.pop();continue;default:if(!(a=t.trys,a=a.length>0&&a[a.length-1])&&(n[0]===6||n[0]===2)){t=0;continue}if(n[0]===3&&(!a||n[1]>a[0]&&n[1]<a[3])){t.label=n[1];break}if(n[0]===6&&t.label<a[1]){t.label=a[1];a=n;break}if(a&&t.label<a[2]){t.label=a[2];t.ops.push(n);break}if(a[2])t.ops.pop();t.trys.pop();continue}n=i.call(e,t)}catch(e){n=[6,e];o=0}finally{r=a=0}if(n[0]&5)throw n[1];return{value:n[0]?n[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var i,t,r,o;return{setters:[function(e){i=e.r;t=e.c;r=e.h;o=e.a}],execute:function(){var a=".sidebar_dialog{width:100%;height:100vh;-webkit-box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));background-color:rgba(0, 0, 0, 0.7);opacity:0;visibility:hidden;-webkit-transition:opacity 0.3s ease-in-out;transition:opacity 0.3s ease-in-out;display:none}.sidebar_dialog.type_over{position:fixed;top:0;left:0;z-index:80000}.sidebar_dialog.type_over .sidebar{z-index:90000}.sidebar_dialog.type_fixed{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;position:relative;height:100%;-webkit-box-shadow:none;box-shadow:none}.sidebar_dialog.is_open{display:-ms-flexbox;display:flex;opacity:1;visibility:visible}.sidebar_dialog .outzone{-ms-flex-order:2;order:2;width:100%;height:100vh}.sidebar_dialog .sidebar{width:360px;-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;background-color:var(--color-surface-2, rgb(237, 237, 237));-ms-flex-negative:0;flex-shrink:0}.sidebar_dialog .sidebar.position_left{-ms-flex-order:1;order:1}.sidebar_dialog .sidebar.position_right{-ms-flex-order:3;order:3}.sidebar_dialog .sidebar.background_surface-1{background-color:var(--color-surface-1, rgb(246, 246, 246))}.sidebar_dialog .sidebar.background_surface-2{background-color:var(--color-surface-2, rgb(237, 237, 237))}.sidebar_dialog .sidebar.background_surface-3{background-color:var(--color-surface-3, rgb(227, 227, 227))}.sidebar_dialog .sidebar.background_surface-4{background-color:var(--color-surface-4, rgb(20, 20, 20))}.sidebar_dialog .sidebar.type_fixed{width:288px}.sidebar_dialog .sidebar .header{display:-ms-flexbox;display:flex;-ms-flex-line-pack:center;align-content:center;-ms-flex-pack:justify;justify-content:space-between;padding:24px}.sidebar_dialog .sidebar .header .content{display:-ms-flexbox;display:flex;width:100%;-ms-flex-align:center;align-items:center;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.sidebar_dialog .sidebar .header .content ::slotted(*){width:100%}.sidebar_dialog .sidebar .header .closeButton{border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1;cursor:pointer;color:var(--color-content-default, rgb(40, 40, 40))}.sidebar_dialog .sidebar .body{position:relative;-ms-flex:1 1 auto;flex:1 1 auto}.sidebar_dialog .sidebar .body .content{position:absolute;inset:0;z-index:999999;overflow-y:overlay;overflow-x:clip}.sidebar_dialog .sidebar .body .content::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.sidebar_dialog .sidebar .body .content::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.sidebar_dialog .sidebar .body .margin{padding:8px 24px}.sidebar_dialog .sidebar .footer .content{padding:24px}.sidebar_dialog .sidebar .footer .content ::slotted(*){height:40px;overflow:hidden}.sidebar_dialog .sidebar.is_open.position_left{right:calc(100% - 360px)}.sidebar_dialog .sidebar.is_open.position_right{left:calc(100% - 360px)}";var s=e("bds_sidebar",function(){function e(e){var r=this;i(this,e);this.bdsToggle=t(this,"bdsToggle");this.InnerSpacing=0;this.isOpen=this.type==="fixed"?true:false;this.sidebarPosition="left";this.type="over";this.margin=true;this.width=360;this.dtOutzone=null;this.dtButtonClose=null;this.background="surface-2";this.listiner=function(e){if(e.key=="Escape"&&r.type!=="fixed"){r.isOpen=false}};this.onClickCloseButtom=function(){r.isOpen=false}}e.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.isOpen=!this.isOpen;return[2]}))}))};e.prototype.isOpenChanged=function(e){this.bdsToggle.emit({value:e});if(e===true){document.addEventListener("keyup",this.listiner,false)}else{document.removeEventListener("keyup",this.listiner,false)}};e.prototype.componentWillLoad=function(){this.hasFooterSlot=!!this.hostElement.querySelector('[slot="footer"]');this.hasHeaderSlot=!!this.hostElement.querySelector('[slot="header"]')};e.prototype.render=function(){var e,i;var t=this;return r("div",{key:"13ba704ac3fd10d7c77d4ded636c5b17c5560c87",class:(e={sidebar_dialog:true,is_open:this.isOpen},e["type_".concat(this.type)]=true,e)},this.type==="over"?r("div",{class:{outzone:true},onClick:function(){return t.onClickCloseButtom()},"data-test":this.dtOutzone}):"",r("div",{key:"498dbf3de26d0dd07e5cfbe68a484f0771225df3",class:(i={sidebar:true,is_open:this.isOpen},i["type_".concat(this.type)]=true,i["position_".concat(this.sidebarPosition)]=true,i["background_".concat(this.background)]=true,i),style:{width:"".concat(this.width<144?144:this.width,"px")}},this.hasHeaderSlot&&r("div",{key:"5ff63216fe15dcb73f5c1bac2d40cb450a12efe4",class:{header:true}},r("div",{key:"513387ec6653504e8b4f960f6c204fa29f79b7ef",class:{content:true}},r("slot",{key:"2f66f02119c41d41c0b54ec310971845920ffd75",name:"header"})),this.type==="fixed"?"":r("bds-button-icon",{class:{closeButton:true},icon:"close",size:"short",variant:"secondary",onClick:function(){return t.onClickCloseButtom()},dataTest:this.dtButtonClose})),r("div",{key:"1fe85b9e8df3ff755f73decb42a013c917240eae",class:{body:true}},r("div",{key:"6da11a2c668372b9dc612aa99a598c9929796bce",class:{content:true,element_scrolled:true,margin:this.margin}},r("slot",{key:"ccd7205ca9a9a2c1c821b84a0f166f1c9678e288",name:"body"}))),this.hasFooterSlot&&r("div",{key:"60401dd3bf62dd56903e48835f1bf1a4170ae370",class:{footer:true}},r("div",{key:"d316cc85e985252145748929d4a585b05a8d96ed",class:{content:true}},r("slot",{key:"0b917bd07043a083f15ae67dea83d15ee418596d",name:"footer"})))))};Object.defineProperty(e.prototype,"hostElement",{get:function(){return o(this)},enumerable:false,configurable:true});Object.defineProperty(e,"watchers",{get:function(){return{isOpen:["isOpenChanged"]}},enumerable:false,configurable:true});return e}());s.style=a}}}));
//# sourceMappingURL=p-8c9e3fff.system.entry.js.map