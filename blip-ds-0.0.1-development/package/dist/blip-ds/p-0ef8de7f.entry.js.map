{"version": 3, "names": ["stepper<PERSON>s", "BdsStepper", "connectedCallback", "this", "childOptions", "for<PERSON>ach", "option", "index", "length", "last", "componentDidLoad", "renderLine", "setActiveStep", "resetActiveSteps", "active", "setCompletedStep", "completed", "getActiveStep", "find", "step", "resetCompletedSteps", "Array", "from", "el", "querySelectorAll", "line", "document", "createElement", "classList", "add", "item", "idx", "insertAdjacentHTML", "outerHTML", "render", "h", "Host", "key", "class"], "sources": ["src/components/stepper/stepper.scss?tag=bds-stepper&encapsulation=shadow", "src/components/stepper/stepper.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  width: 100%;\n  border-radius: 8px;\n  box-sizing: border-box;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n\n  ::slotted(bds-step:last-child) {\n    flex: inherit;\n  }\n}\n\n::slotted(.stepper__container__divisor) {\n  flex: 1 1 auto;\n  align-self: center;\n  height: 1.5px;\n  background: $color-content-disable;\n  margin: 0px 8px;\n  min-width: 24px;\n}\n\n::slotted(.stepper__container__divisor--completed) {\n  border-top: 2px solid $color-primary;\n}\n", "import { Component, ComponentInterface, h, Element, Method, Host } from '@stencil/core';\n@Component({\n  tag: 'bds-stepper',\n  styleUrl: 'stepper.scss',\n  shadow: true,\n})\nexport class BdsStepper implements ComponentInterface {\n  @Element() el: HTMLBdsStepperElement;\n\n  connectedCallback() {\n    this.childOptions.forEach((option, index) => {\n      option.index = index;\n      if (index === this.childOptions.length - 1) {\n        option.last = true;\n      }\n    });\n  }\n\n  componentDidLoad() {\n    this.renderLine();\n  }\n\n  /**\n   * Set the active step\n   *\n   * @param index The index of the step to be set as active\n   * @returns void\n   */\n  @Method()\n  public async setActiveStep(index: number): Promise<void> {\n    this.resetActiveSteps();\n    this.childOptions[index].active = true;\n  }\n\n  /**\n   * Set the completed step\n   *\n   * @param index The index of the step to be set as completed\n   * @returns void\n   */\n  @Method()\n  public async setCompletedStep(index: number): Promise<void> {\n    this.childOptions[index].completed = true;\n  }\n\n  /**\n   * Returns the active step\n   *\n   * @returns HTMLBdsStepElement\n   */\n  @Method()\n  public async getActiveStep(): Promise<number> {\n    return this.childOptions.find((step) => step.active === true).index;\n  }\n\n  /**\n   * Reset all active steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetActiveSteps() {\n    for (const step of this.childOptions) {\n      step.active = false;\n    }\n  }\n\n  /**\n   * Reset all completed steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetCompletedSteps() {\n    for (const step of this.childOptions) {\n      step.completed = false;\n    }\n  }\n\n  private get childOptions(): HTMLBdsStepElement[] {\n    return Array.from(this.el.querySelectorAll('bds-step'));\n  }\n\n  private renderLine() {\n    const line = document.createElement('div');\n    line.classList.add('stepper__container__divisor');\n\n    Array.from(this.childOptions).forEach((item, idx) => {\n      if (this.childOptions.length - 1 != idx) {\n        item.insertAdjacentHTML('afterend', line.outerHTML);\n      }\n    });\n  }\n\n  render() {\n    return (\n      <Host class=\"stepper__container\">\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "yDAAA,MAAMA,EAAa,wkB,MCMNC,EAAU,M,yBAGrB,iBAAAC,GACEC,KAAKC,aAAaC,SAAQ,CAACC,EAAQC,KACjCD,EAAOC,MAAQA,EACf,GAAIA,IAAUJ,KAAKC,aAAaI,OAAS,EAAG,CAC1CF,EAAOG,KAAO,I,KAKpB,gBAAAC,GACEP,KAAKQ,Y,CAUA,mBAAMC,CAAcL,GACzBJ,KAAKU,mBACLV,KAAKC,aAAaG,GAAOO,OAAS,I,CAU7B,sBAAMC,CAAiBR,GAC5BJ,KAAKC,aAAaG,GAAOS,UAAY,I,CAShC,mBAAMC,GACX,OAAOd,KAAKC,aAAac,MAAMC,GAASA,EAAKL,SAAW,OAAMP,K,CASzD,sBAAMM,GACX,IAAK,MAAMM,KAAQhB,KAAKC,aAAc,CACpCe,EAAKL,OAAS,K,EAUX,yBAAMM,GACX,IAAK,MAAMD,KAAQhB,KAAKC,aAAc,CACpCe,EAAKH,UAAY,K,EAIrB,gBAAYZ,GACV,OAAOiB,MAAMC,KAAKnB,KAAKoB,GAAGC,iBAAiB,Y,CAGrC,UAAAb,GACN,MAAMc,EAAOC,SAASC,cAAc,OACpCF,EAAKG,UAAUC,IAAI,+BAEnBR,MAAMC,KAAKnB,KAAKC,cAAcC,SAAQ,CAACyB,EAAMC,KAC3C,GAAI5B,KAAKC,aAAaI,OAAS,GAAKuB,EAAK,CACvCD,EAAKE,mBAAmB,WAAYP,EAAKQ,U,KAK/C,MAAAC,GACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAM,sBACVH,EAAQ,QAAAE,IAAA,6C", "ignoreList": []}