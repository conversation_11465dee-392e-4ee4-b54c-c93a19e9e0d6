{"version": 3, "file": "p-DUxyf5jA.system.js", "sources": ["src/components/checkbox/checkbox.scss?tag=bds-checkbox&encapsulation=shadow", "src/components/checkbox/checkbox.tsx", "src/components/select-option/select-option.scss?tag=bds-select-option&encapsulation=shadow", "src/components/select-option/select-option.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$checkbox-size: 18px;\n$checkbox-icon-radius: 4px;\n$checkbox-spacing-text: 8px;\n\n.checkbox {\n  display: inline;\n\n  input[type='checkbox'] {\n    display: none;\n    -webkit-appearance: none;\n    -moz-appearance: none;\n    appearance: none;\n    -webkit-tap-highlight-color: transparent;\n    cursor: pointer;\n    margin: 0;\n    &:focus {\n      outline: 0;\n    }\n  }\n\n  &__icon {\n    position: relative;\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      border-color: $color-brand;\n    }\n  }\n\n  &--selected {\n    .checkbox__icon {\n      background-color: $color-surface-primary;\n      border-color: $color-surface-primary;\n\n      &__svg {\n        color: $color-content-bright;\n      }\n\n      &:hover {\n        background-color: $color-brand;\n      }\n    }\n  }\n\n  &--selected-disabled {\n    .checkbox__label {\n      cursor: not-allowed;\n    }\n\n    .checkbox__icon {\n      color: $color-content-default;\n      border-color: $color-content-default;\n      background-color: $color-surface-3;\n      opacity: 50%;\n    }\n    .checkbox__text {\n      opacity: 50%;\n    }\n  }\n\n  &--deselected {\n    .checkbox__icon__svg {\n      display: none;\n    }\n  }\n\n  &--deselected-disabled {\n    .checkbox__label {\n      cursor: not-allowed;\n    }\n\n    .checkbox__icon {\n      opacity: 50%;\n      background-color: $color-surface-1;\n      border: 1px solid $color-brand;\n    }\n\n    .checkbox__icon__svg {\n      display: none;\n    }\n  }\n\n  &__label {\n    @include no-select();\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    max-width: fit-content;\n  }\n\n  &__icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: $checkbox-size;\n    width: $checkbox-size;\n    min-width: 18px;\n    border-radius: $checkbox-icon-radius;\n    color: $color-surface-1;\n    border: 1px solid $color-content-default;\n    box-sizing: border-box;\n    border-radius: 4px;\n    @include animation();\n  }\n\n  &__text {\n    margin-left: $checkbox-spacing-text;\n    color: $color-content-default;\n  }\n}\n", "import { Component, h, Prop, State, Method, Event, EventEmitter } from '@stencil/core';\n\nlet checkBoxIds = 0;\n@Component({\n  tag: 'bds-checkbox',\n  styleUrl: 'checkbox.scss',\n  shadow: true,\n})\nexport class Checkbox {\n  private nativeInput?: HTMLInputElement;\n\n  @State() checkBoxId?: string;\n\n  @Prop() refer!: string;\n\n  @Prop() label!: string;\n\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name!: string;\n\n  /**\n   * If `true`, the checkbox is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked = false;\n\n  /**\n   * If `true`, the user cannot interact with the checkbox.\n   */\n  @Prop() disabled = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    this.checkBoxId = this.refer || `bds-checkbox-${checkBoxIds++}`;\n  }\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<KeyboardEvent>;\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  @Method()\n  async toggle() {\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  }\n\n  private onClick = (ev: Event): void => {\n    ev.stopPropagation();\n    this.checked = !this.checked;\n    this.bdsChange.emit({\n      checked: this.checked,\n    });\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  private getStyleState = (): string => {\n    if (this.checked && !this.disabled) {\n      return 'checkbox--selected';\n    }\n\n    if (!this.checked && !this.disabled) {\n      return 'checkbox--deselected';\n    }\n\n    if (this.checked && this.disabled) {\n      return 'checkbox--selected-disabled';\n    }\n\n    if (!this.checked && this.disabled) {\n      return 'checkbox--deselected-disabled';\n    }\n\n    return '';\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.checked = !this.checked;\n      this.bdsChange.emit({\n        checked: this.checked,\n      });\n    }\n  }\n\n  render(): HTMLElement {\n    const styleState = this.getStyleState();\n\n    return (\n      <div\n        class={{\n          checkbox: true,\n          [styleState]: true,\n        }}\n      >\n        <input\n          type=\"checkbox\"\n          ref={this.refNativeInput}\n          id={this.checkBoxId}\n          name={this.name}\n          onClick={(ev) => this.onClick(ev)}\n          checked={this.checked}\n          disabled={this.disabled}\n          data-test={this.dataTest}\n        ></input>\n        <label class=\"checkbox__label\" htmlFor={this.checkBoxId}>\n          <div class=\"checkbox__icon\" tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)}>\n            <bds-icon class=\"checkbox__icon__svg\" size=\"x-small\" name=\"true\" color=\"inherit\"></bds-icon>\n          </div>\n          {this.label && (\n            <bds-typo class=\"checkbox__text\" variant=\"fs-14\" tag=\"span\">\n              {this.label}\n            </bds-typo>\n          )}\n        </label>\n      </div>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n$select-option-padding: 8px;\n$select-option-left: 12px;\n\n:host(.option-checked) {\n  order: -1;\n}\n\n.load-spinner {\n  background-color: $color-surface-0;\n  height: 200px;\n}\n\n.select-option {\n  display: grid;\n  width: 100%;\n  @include no-select();\n  cursor: pointer;\n  background-color: $color-surface-0;\n  padding: $select-option-padding;\n  padding-left: $select-option-left;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  outline: none;\n  order: 1;\n\n  &--selected {\n    .select-option__container--value {\n      color: $color-primary;\n    }\n  }\n\n  &--disabled {\n    .select-option__container--value,\n    .select-option__container--bulk {\n      &:hover {\n        background-color: $color-surface-1;\n      }\n\n      cursor: not-allowed;\n      color: $color-content-disable;\n    }\n  }\n\n  ::slotted(bds-icon) {\n    margin-right: 10px;\n  }\n\n  &__container {\n    color: $color-content-default;\n    display: flex;\n    flex-direction: column;\n\n    &__checkbox {\n      cursor: pointer;\n      padding: $select-option-padding;\n      padding-left: $select-option-left;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n\n      & bds-checkbox {\n        pointer-events: none;\n      }\n    }\n\n    &__fill_space {\n      width: 100%;\n    }\n\n    &--bulk,\n    &--status {\n      color: $color-content-ghost;\n    }\n    &--status {\n      margin-left: 4px;\n    }\n\n    &__overflow {\n      overflow: hidden;\n      padding-right: 16px;\n    }\n\n    &:hover > &--value,\n    &:hover > &--bulk,\n    &:hover > &--status {\n      color: $color-primary;\n    }\n\n    &:active > &--value,\n    &:active > &--bulk,\n    &:active > &--status {\n      color: $color-primary;\n    }\n  }\n\n  &:hover {\n    background-color: $color-surface-1;\n  }\n\n  &:focus {\n    background-color: $color-surface-1;\n    color: $color-primary-main;\n  }\n\n  &--selected {\n    background-color: $color-surface-1;\n  }\n\n  &--invisible {\n    display: none;\n  }\n}\n", "import { Component, h, Element, Prop, EventEmitter, Event, Method, Watch } from '@stencil/core';\r\nimport { Keyboard } from '../../utils/enums';\r\n\r\nexport type TypeOption = 'checkbox' | 'default';\r\n\r\n@Component({\r\n  tag: 'bds-select-option',\r\n  styleUrl: 'select-option.scss',\r\n  shadow: true,\r\n})\r\nexport class SelectOption {\r\n  private nativeInput?: HTMLBdsCheckboxElement;\r\n\r\n  @Element() private element: HTMLElement;\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  @Prop() value!: any;\r\n\r\n  /**\r\n   * The text value of the option.\r\n   */\r\n  @Prop() selected? = false;\r\n\r\n  /**\r\n   * If `true`, the user cannot interact with the select option.\r\n   */\r\n  @Prop() disabled? = false;\r\n\r\n  /**\r\n   * Add state danger on input, use for use feedback.\r\n   */\r\n  @Prop({ reflect: true, mutable: true }) invisible? = false;\r\n\r\n  /**\r\n   * Add state danger on input, use for use feedback.\r\n   */\r\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\r\n\r\n  /**\r\n   *  Quantity Description on option value, this item is locate to rigth in component.\r\n   */\r\n  @Prop() bulkOption? = '';\r\n\r\n  /**\r\n   *  Alignment of input-left slot. The value need to be one of the values used on flexbox align-self property.\r\n   */\r\n  @Prop() slotAlign? = 'center';\r\n\r\n  /**\r\n   *  If set, a title will be shown under the text\r\n   */\r\n  @Prop() titleText: string;\r\n\r\n  /**\r\n   *  If set, a text will be displayed on the right side of the option label\r\n   */\r\n  @Prop() status?: string;\r\n\r\n  /**\r\n   * Type Option. Used toselect type of item list.\r\n   */\r\n  @Prop({ mutable: true, reflect: true }) typeOption?: TypeOption = 'default';\r\n\r\n  /**\r\n   * If `true`, the checkbox is selected.\r\n   */\r\n  @Prop({ mutable: true, reflect: true }) checked = false;\r\n\r\n  /**\r\n   * Data test is the prop to specifically test the component action object.\r\n   */\r\n  @Prop() dataTest?: string = null;\r\n\r\n  @Event() optionSelected: EventEmitter;\r\n\r\n  @Event() optionChecked: EventEmitter;\r\n\r\n  @Watch('typeOption')\r\n  protected changeSelectionType() {\r\n    this.typeOption = this.typeOption;\r\n  }\r\n\r\n  @Method()\r\n  async toggle() {\r\n    this.checked = !this.checked;\r\n  }\r\n\r\n  @Method()\r\n  async toMark() {\r\n    this.checked = true;\r\n  }\r\n\r\n  @Method()\r\n  async markOff() {\r\n    this.checked = false;\r\n  }\r\n\r\n  private refNativeInput = (input: HTMLBdsCheckboxElement): void => {\r\n    this.nativeInput = input;\r\n  };\r\n\r\n  private checkedCurrent = () => {\r\n    if (this.typeOption !== 'checkbox') return;\r\n    this.nativeInput.toggle();\r\n  };\r\n\r\n  private onClickSelectOption = (): void => {\r\n    if (this.typeOption == 'checkbox') return;\r\n    if (!this.disabled) {\r\n      this.optionSelected.emit({ value: this.value, label: this.element.innerHTML });\r\n    }\r\n  };\r\n\r\n  private optionHandle = (ev: CustomEvent): void => {\r\n    const elementChecked = ev.target as HTMLBdsCheckboxElement;\r\n    const data = { value: elementChecked.name, label: this.element.innerHTML, checked: elementChecked.checked };\r\n    this.checked = !this.checked;\r\n    this.optionChecked.emit(data);\r\n  };\r\n\r\n  private attachOptionKeyboardListeners = (event: KeyboardEvent): void => {\r\n    const element = event.target as HTMLElement;\r\n\r\n    switch (event.key) {\r\n      case Keyboard.ENTER:\r\n        this.onClickSelectOption();\r\n        break;\r\n      case Keyboard.ARROW_DOWN:\r\n        if (\r\n          element.parentElement.nextElementSibling &&\r\n          !element.parentElement.nextElementSibling.hasAttribute('invisible')\r\n        ) {\r\n          event.preventDefault();\r\n          event.stopPropagation();\r\n          (element.parentElement.nextElementSibling.firstElementChild as HTMLInputElement).focus();\r\n        }\r\n        break;\r\n      case Keyboard.ARROW_UP:\r\n        if (\r\n          element.parentElement.previousElementSibling &&\r\n          !element.parentElement.previousElementSibling.hasAttribute('invisible')\r\n        ) {\r\n          event.preventDefault();\r\n          event.stopPropagation();\r\n          (element.parentElement.previousElementSibling.firstElementChild as HTMLInputElement).focus();\r\n        }\r\n    }\r\n  };\r\n\r\n  render(): HTMLElement {\r\n    return (\r\n      <div\r\n        id={`bds-select-option-${this.value}`}\r\n        data-event=\"click\"\r\n        role=\"button\"\r\n        onKeyDown={this.attachOptionKeyboardListeners}\r\n        onClick={this.onClickSelectOption}\r\n        data-value={this.value}\r\n        data-test={this.dataTest}\r\n        class={{\r\n          'select-option': this.typeOption != 'checkbox',\r\n          'select-option--selected': this.selected,\r\n          'select-option--disabled': this.disabled,\r\n          'select-option--invisible': this.invisible,\r\n        }}\r\n      >\r\n        <div style={{ alignSelf: this.slotAlign }}>\r\n          <slot name=\"input-left\"></slot>\r\n        </div>\r\n\r\n        <div\r\n          class={{\r\n            'select-option__container': true,\r\n            'select-option__container__fill_space': !!this.status,\r\n            'select-option__container__checkbox': this.typeOption == 'checkbox',\r\n          }}\r\n          onClick={() => this.checkedCurrent()}\r\n        >\r\n          {this.titleText && (\r\n            <bds-typo class=\"select-option__container--value\" variant=\"fs-16\" bold=\"semi-bold\">\r\n              {this.titleText}\r\n            </bds-typo>\r\n          )}\r\n\r\n          {this.typeOption === 'checkbox' ? (\r\n            <bds-checkbox\r\n              ref={this.refNativeInput}\r\n              refer={`html-for-${this.value}`}\r\n              label={this.element.innerHTML}\r\n              name={this.value}\r\n              checked={this.checked}\r\n              onBdsChange={(ev) => this.optionHandle(ev)}\r\n            ></bds-checkbox>\r\n          ) : (\r\n            <bds-typo\r\n              class={{\r\n                'select-option__container--value': true,\r\n                'select-option__container__overflow': !!this.status,\r\n              }}\r\n              noWrap={!!this.status}\r\n              variant=\"fs-14\"\r\n            >\r\n              <slot />\r\n            </bds-typo>\r\n          )}\r\n        </div>\r\n        {this.bulkOption && (\r\n          <bds-typo class=\"select-option__container--bulk\" variant=\"fs-10\">\r\n            {this.bulkOption}\r\n          </bds-typo>\r\n        )}\r\n        {this.status && (\r\n          <bds-typo class=\"select-option__container--status\" noWrap={true} variant=\"fs-10\">\r\n            {this.status}\r\n          </bds-typo>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,WAAW,GAAG,spFAAspF;;MCE1qF,IAAI,WAAW,GAAG,CAAC;YAMN,QAAQ,2BAAA,MAAA;MALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MAmBE;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;MAEvD;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAExB;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAkCxB,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAS,KAAU;kBACpC,EAAE,CAAC,eAAe,EAAE;MACpB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;MAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;sBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;MACtB,aAAA,CAAC;MACJ,SAAC;MAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAuB,KAAU;MACzD,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;MAC1B,SAAC;MAEO,QAAA,IAAa,CAAA,aAAA,GAAG,MAAa;kBACnC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClC,gBAAA,OAAO,oBAAoB;;kBAG7B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MACnC,gBAAA,OAAO,sBAAsB;;kBAG/B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;MACjC,gBAAA,OAAO,6BAA6B;;kBAGtC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;MAClC,gBAAA,OAAO,+BAA+B;;MAGxC,YAAA,OAAO,EAAE;MACX,SAAC;MA4CF;UA1GC,iBAAiB,GAAA;cACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,CAAgB,aAAA,EAAA,WAAW,EAAE,CAAA,CAAE;;UAcjE,eAAe,GAAA;cACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;;UAI1C,QAAQ,GAAA;cACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;MAIlD,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;MAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;kBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;MACtB,SAAA,CAAC;;MAmCI,IAAA,aAAa,CAAC,KAAK,EAAA;MACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;MACxB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;MAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;sBAClB,OAAO,EAAE,IAAI,CAAC,OAAO;MACtB,aAAA,CAAC;;;UAIN,MAAM,GAAA;MACJ,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;cAEvC,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;MACL,gBAAA,QAAQ,EAAE,IAAI;sBACd,CAAC,UAAU,GAAG,IAAI;MACnB,aAAA,EAAA,EAED,CACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,UAAU,EACf,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,UAAU,EACnB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EACjC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACZ,WAAA,EAAA,IAAI,CAAC,QAAQ,EACjB,CAAA,EACT,CAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAA,EACrD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAC,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAC/E,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,qBAAqB,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,KAAK,EAAC,SAAS,GAAY,CACxF,EACL,IAAI,CAAC,KAAK,KACT,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAA,EACxD,IAAI,CAAC,KAAK,CACF,CACZ,CACK,CACJ;;;;;MC5IZ,MAAM,eAAe,GAAG,snFAAsnF;;YCUjoF,YAAY,gCAAA,MAAA;MALzB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MAaE;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAEzB;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAEzB;;MAEG;MACqC,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;MAE1D;;MAEG;MACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;MAEhE;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAI,EAAE;MAExB;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAI,QAAQ;MAY7B;;MAEG;MACqC,QAAA,IAAU,CAAA,UAAA,GAAgB,SAAS;MAE3E;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;MAEvD;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MA0BxB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAA6B,KAAU;MAC/D,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;MAC1B,SAAC;MAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAK;MAC5B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU;sBAAE;MACpC,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;MAC3B,SAAC;MAEO,QAAA,IAAmB,CAAA,mBAAA,GAAG,MAAW;MACvC,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,UAAU;sBAAE;MACnC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;sBAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;;MAElF,SAAC;MAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,EAAe,KAAU;MAC/C,YAAA,MAAM,cAAc,GAAG,EAAE,CAAC,MAAgC;kBAC1D,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,OAAO,EAAE;MAC3G,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;MAC5B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;MAC/B,SAAC;MAEO,QAAA,IAAA,CAAA,6BAA6B,GAAG,CAAC,KAAoB,KAAU;MACrE,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,MAAqB;MAE3C,YAAA,QAAQ,KAAK,CAAC,GAAG;sBACf,KAAA,OAAA;0BACE,IAAI,CAAC,mBAAmB,EAAE;0BAC1B;sBACF,KAAA,WAAA;MACE,oBAAA,IACE,OAAO,CAAC,aAAa,CAAC,kBAAkB;8BACxC,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,WAAW,CAAC,EACnE;8BACA,KAAK,CAAC,cAAc,EAAE;8BACtB,KAAK,CAAC,eAAe,EAAE;8BACtB,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,iBAAsC,CAAC,KAAK,EAAE;;0BAE1F;sBACF,KAAA,SAAA;MACE,oBAAA,IACE,OAAO,CAAC,aAAa,CAAC,sBAAsB;8BAC5C,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,EACvE;8BACA,KAAK,CAAC,cAAc,EAAE;8BACtB,KAAK,CAAC,eAAe,EAAE;8BACtB,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,iBAAsC,CAAC,KAAK,EAAE;;;MAGpG,SAAC;MAwEF;UA7IW,mBAAmB,GAAA;MAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;;MAInC,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;;MAI9B,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;MAIrB,IAAA,MAAM,OAAO,GAAA;MACX,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;UAuDtB,MAAM,GAAA;cACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EAAE,EAAE,CAAqB,kBAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,EAAA,YAAA,EAC1B,OAAO,EAClB,IAAI,EAAC,QAAQ,EACb,SAAS,EAAE,IAAI,CAAC,6BAA6B,EAC7C,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAA,YAAA,EACrB,IAAI,CAAC,KAAK,EACX,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU;sBAC9C,yBAAyB,EAAE,IAAI,CAAC,QAAQ;sBACxC,yBAAyB,EAAE,IAAI,CAAC,QAAQ;sBACxC,0BAA0B,EAAE,IAAI,CAAC,SAAS;mBAC3C,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAA,EACvC,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,YAAY,EAAA,CAAQ,CAC3B,EAEN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,0BAA0B,EAAE,IAAI;MAChC,gBAAA,sCAAsC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM;MACrD,gBAAA,oCAAoC,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU;mBACpE,EACD,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE,EAAA,EAEnC,IAAI,CAAC,SAAS,KACb,iEAAU,KAAK,EAAC,iCAAiC,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,IAC/E,IAAI,CAAC,SAAS,CACN,CACZ,EAEA,IAAI,CAAC,UAAU,KAAK,UAAU,IAC7B,CACE,CAAA,cAAA,EAAA,EAAA,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,CAAY,SAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,EAC/B,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAC7B,IAAI,EAAE,IAAI,CAAC,KAAK,EAChB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA,CAC5B,KAEhB,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,iCAAiC,EAAE,IAAI;MACvC,gBAAA,oCAAoC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM;MACpD,aAAA,EACD,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,EACrB,OAAO,EAAC,OAAO,EAAA,EAEf,CAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,CACC,CACZ,CACG,EACL,IAAI,CAAC,UAAU,KACd,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,gCAAgC,EAAC,OAAO,EAAC,OAAO,EAAA,EAC7D,IAAI,CAAC,UAAU,CACP,CACZ,EACA,IAAI,CAAC,MAAM,KACV,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,kCAAkC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAC,OAAO,EAAA,EAC7E,IAAI,CAAC,MAAM,CACH,CACZ,CACG;;;;;;;;;;;;;;;"}