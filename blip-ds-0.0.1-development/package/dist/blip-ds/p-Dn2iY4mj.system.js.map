{"version": 3, "file": "p-Dn2iY4mj.system.js", "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion&encapsulation=shadow", "src/components/accordion/accordion.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, Element, Event, EventEmitter, h, Method, Prop, State, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class Accordion {\n  private accGroup?: HTMLBdsAccordionGroupElement = null;\n  private accheaders?: HTMLBdsAccordionHeaderElement = null;\n  private accBodies?: HTMLBdsAccordionBodyElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n  @State() numberElement?: number = null;\n  @State() condition?: boolean = false;\n\n  @Event() bdsToggle?: EventEmitter;\n  @Event() bdsAccordionOpen?: EventEmitter;\n  @Event() bdsAccordionClose?: EventEmitter;\n\n  @Prop({ reflect: true }) startOpen?: boolean = false;\n  @Prop({ reflect: true }) divisor?: boolean = true;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n    this.bdsToggle.emit({ value: this.isOpen });\n  }\n\n  @Method()\n  async open() {\n    this.accheaders?.open();\n    this.accBodies?.open();\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.accheaders?.close();\n    this.accBodies?.close();\n    this.isOpen = false;\n  }\n\n  @Method()\n  async notStart() {\n    this.startOpen = false;\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(value): void {\n    if (value) {\n      if (this.accGroup.collapse == 'single' && this.condition === false) {\n        this.accGroup?.closeAll(this.numberElement);\n      }\n      this.accheaders?.open();\n      this.accBodies?.open();\n      this.bdsAccordionOpen.emit();\n    } else {\n      this.accheaders?.close();\n      this.accBodies?.close();\n      this.bdsAccordionClose.emit();\n    }\n    this.condition = false;\n  }\n\n  @Watch('divisor')\n  divisorChanged(newValue: boolean): void {\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(newValue);\n    }\n  }\n\n  componentWillLoad() {\n    this.accGroup =\n      this.element.parentElement.tagName == 'BDS-ACCORDION-GROUP' &&\n      (this.element.parentElement as HTMLBdsAccordionGroupElement);\n    this.accheaders = this.element.querySelector('bds-accordion-header') as HTMLBdsAccordionHeaderElement;\n    this.accBodies = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n\n    // Passar a prop divisor para o AccordionBody\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(this.divisor);\n    }\n\n    if (this.startOpen === true) {\n      this.condition = true;\n      this.isOpen = true;\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"accordion_group\">\n        <slot></slot>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,YAAY,GAAG,4lEAA4lE;;YCOpmE,SAAS,4BAAA,MAAA;MALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;MAMU,QAAA,IAAQ,CAAA,QAAA,GAAkC,IAAI;MAC9C,QAAA,IAAU,CAAA,UAAA,GAAmC,IAAI;MACjD,QAAA,IAAS,CAAA,SAAA,GAAiC,IAAI;MAI7C,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;MACxB,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;MAC7B,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;MAMX,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;MAC3B,QAAA,IAAO,CAAA,OAAA,GAAa,IAAI;MAmFlD;MAhFC,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;MAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;;MAI7C,IAAA,MAAM,IAAI,GAAA;;cACR,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;cACvB,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;MACtB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;MAIpB,IAAA,MAAM,KAAK,GAAA;;cACT,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;cACxB,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;MACvB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;MAIrB,IAAA,MAAM,QAAQ,GAAA;MACZ,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;UAIxB,MAAM,YAAY,CAAC,MAAM,EAAA;MACvB,QAAA,IAAI,CAAC,aAAa,GAAG,MAAM;;MAI7B,IAAA,aAAa,CAAC,KAAK,EAAA;;cACjB,IAAI,KAAK,EAAE;MACT,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;MAClE,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;;kBAE7C,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;kBACvB,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;MACtB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;;mBACvB;kBACL,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;kBACxB,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;MACvB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;;MAE/B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;MAIxB,IAAA,cAAc,CAAC,QAAiB,EAAA;cAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC;cACrG,IAAI,aAAa,EAAE;MAChB,YAAA,aAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC;;;UAI5C,iBAAiB,GAAA;MACf,QAAA,IAAI,CAAC,QAAQ;MACX,YAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,qBAAqB;MAC1D,gBAAA,IAAI,CAAC,OAAO,CAAC,aAA8C;cAC9D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAkC;cACrG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC;;cAGhG,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,oBAAoB,CAAgC;cACrG,IAAI,aAAa,EAAE;MAChB,YAAA,aAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;MAG9C,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;MAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACrB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;;UAItB,MAAM,GAAA;cACJ,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,iBAAiB,EAAA,EAC1B,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACT;;;;;;;;;;;;;;;;"}