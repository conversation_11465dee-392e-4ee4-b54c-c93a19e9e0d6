var __awaiter=this&&this.__awaiter||function(a,e,o,i){function n(a){return a instanceof o?a:new o((function(e){e(a)}))}return new(o||(o=Promise))((function(o,d){function s(a){try{c(i.next(a))}catch(a){d(a)}}function l(a){try{c(i["throw"](a))}catch(a){d(a)}}function c(a){a.done?o(a.value):n(a.value).then(s,l)}c((i=i.apply(a,e||[])).next())}))};var __generator=this&&this.__generator||function(a,e){var o={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},i,n,d,s;return s={next:l(0),throw:l(1),return:l(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function l(a){return function(e){return c([a,e])}}function c(l){if(i)throw new TypeError("Generator is already executing.");while(s&&(s=0,l[0]&&(o=0)),o)try{if(i=1,n&&(d=l[0]&2?n["return"]:l[0]?n["throw"]||((d=n["return"])&&d.call(n),0):n.next)&&!(d=d.call(n,l[1])).done)return d;if(n=0,d)l=[l[0]&2,d.value];switch(l[0]){case 0:case 1:d=l;break;case 4:o.label++;return{value:l[1],done:false};case 5:o.label++;n=l[1];l=[0];continue;case 7:l=o.ops.pop();o.trys.pop();continue;default:if(!(d=o.trys,d=d.length>0&&d[d.length-1])&&(l[0]===6||l[0]===2)){o=0;continue}if(l[0]===3&&(!d||l[1]>d[0]&&l[1]<d[3])){o.label=l[1];break}if(l[0]===6&&o.label<d[1]){o.label=d[1];d=l;break}if(d&&o.label<d[2]){o.label=d[2];o.ops.push(l);break}if(d[2])o.ops.pop();o.trys.pop();continue}l=e.call(a,o)}catch(a){l=[6,a];n=0}finally{i=d=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js","./p-DLraUrU1.system.js"],(function(a){"use strict";var e,o,i,n,d,s;return{setters:[function(a){e=a.r;o=a.c;i=a.h;n=a.H;d=a.a},function(a){s=a.n}],execute:function(){var l={"brazil-flag":{code:"+55",name:"Brasil",isoCode:"BR / BRA"},"united-states-flag":{code:"+1",name:"Estados Unidos",isoCode:"US / USA"},"afghanistan-flag":{code:"+93",name:"Afeganistão",isoCode:"AF / AFG"},"south-africa-flag":{code:"+27",name:"África do Sul",isoCode:"ZA / ZAF"},"albania-flag":{code:"+355",name:"Albânia",isoCode:"AL / ALB"},"germany-flag":{code:"+49",name:"Alemanha",isoCode:"DE / DEU"},"andorra-flag":{code:"+376",name:"Andorra",isoCode:"AD / AND"},"angola-flag":{code:"+244",name:"Angola",isoCode:"AO / AGO"},"anguilla-flag":{code:"+1-264",name:"Anguila",isoCode:"AI / AIA"},"antigua-and-barbuda-flag":{code:"+1-268",name:"Antígua e Barbuda",isoCode:"AG / ATG"},"netherlands-antilles-flag":{code:"+599",name:"Antilhas Holandesas",isoCode:"AN / ANT"},"saudi-arabia-flag":{code:"+966",name:"Arábia Saudita",isoCode:"SA / SAU"},"algeria-flag":{code:"+213",name:"Argélia",isoCode:"DZ / DZA"},"argentina-flag":{code:"+54",name:"Argentina",isoCode:"AR / ARG"},"armenia-flag":{code:"+374",name:"Armênia",isoCode:"AM / ARM"},"aruba-flag":{code:"+297",name:"Aruba",isoCode:"AW / ABW"},"australia-flag":{code:"+61",name:"Austrália",isoCode:"AU / AUS"},"austria-flag":{code:"+43",name:"Áustria",isoCode:"AT / AUT"},"azerbaijan-flag":{code:"+994",name:"Azerbaijão",isoCode:"AZ / AZE"},"bahamas-flag":{code:"+1-242",name:"Bahamas",isoCode:"BS / BHS"},"bahrain-flag":{code:"+973",name:"Bahrein",isoCode:"BH / BHR"},"bangladesh-flag":{code:"+880",name:"Bangladesh",isoCode:"BD / BGD"},"barbados-flag":{code:"+1-246",name:"Barbados",isoCode:"BB / BRB"},"belgium-flag":{code:"+32",name:"Bélgica",isoCode:"BE / BEL"},"belize-flag":{code:"+501",name:"Belize",isoCode:"BZ / BLZ"},"benin-flag":{code:"+229",name:"Benim",isoCode:"BJ / BEN"},"bermuda-flag":{code:"+1-441",name:"Bermuda",isoCode:"BM / BMU"},"belarus-flag":{code:"+375",name:"Bielorrússia",isoCode:"BY / BLR"},"bolivia-flag":{code:"+591",name:"Bolívia",isoCode:"BO / BOL"},"bosnia-and-herzegovina-flag":{code:"+387",name:"Bósnia e Herzegovina",isoCode:"BA / BIH"},"botswana-flag":{code:"+267",name:"Botsuana",isoCode:"BW / BWA"},"british-indian-ocean-territory-flag":{code:"+246",name:"Território Britânico do Oceano Índico",isoCode:"IO / IOT"},"british-virgin-islands-flag":{code:"+1-284",name:"Ilhas Virgens Britânicas",isoCode:"VG / VGB"},"brunei-flag":{code:"+673",name:"Brunei",isoCode:"BN / BRN"},"bulgaria-flag":{code:"+359",name:"Bulgária",isoCode:"BG / BGR"},"burkina-faso-flag":{code:"+226",name:"Burkina Faso",isoCode:"BF / BFA"},"burundi-flag":{code:"+257",name:"Burundi",isoCode:"BI / BDI"},"bhutan-flag":{code:"+975",name:"Butão",isoCode:"BT / BTN"},"cape-verde-flag":{code:"+238",name:"Cabo Verde",isoCode:"CV / CPV"},"cameroon-flag":{code:"+237",name:"Camarões",isoCode:"CM / CMR"},"cambodia-flag":{code:"+855",name:"Camboja",isoCode:"KH / KHM"},"canada-flag":{code:"+1",name:"Canadá",isoCode:"CA / CAN"},"qatar-flag":{code:"+974",name:"Catar",isoCode:"QA / QAT"},"kazakhstan-flag":{code:"+7",name:"Cazaquistão",isoCode:"KZ / KAZ"},"chad-flag":{code:"+235",name:"Chade",isoCode:"TD / TCD"},"china-flag":{code:"+86",name:"China",isoCode:"CN / CHN"},"chile-flag":{code:"+56",name:"Chile",isoCode:"CL / CHL"},"chipre-flag":{code:"+357",name:"Chipre",isoCode:"CY / CYP"},"singapore-flag":{code:"+65",name:"Cingapura",isoCode:"SG / SGP"},"colombia-flag":{code:"+57",name:"Colômbia",isoCode:"CO / COL"},"comoros-flag":{code:"+269",name:"Comores",isoCode:"KM / COM"},"north-korea-flag":{code:"+850",name:"Coréia do Norte",isoCode:"KP / PRK"},"ivory-coast-flag":{code:"+225",name:"Costa do Marfim",isoCode:"CI / CIV"},"costa-rica-flag":{code:"+506",name:"Costa Rica",isoCode:"CR / CRI"},"croatia-flag":{code:"+385",name:"Croácia",isoCode:"HR / HRV"},"cuba-flag":{code:"+53",name:"Cuba",isoCode:"CU / CUB"},"curacao-flag":{code:"+599",name:"Curaçao",isoCode:"CW / CUW"},"denmark-flag":{code:"+45",name:"Dinamarca",isoCode:"DK / DNK"},"djibouti-flag":{code:"+253",name:"Djibuti",isoCode:"DJ / DJI"},"dominica-flag":{code:"+1-767",name:"Dominica",isoCode:"DM / DMA"},"ecuador-flag":{code:"+593",name:"Equador",isoCode:"EC / ECU"},"egypt-flag":{code:"+20",name:"Egito",isoCode:"EG / EGY"},"el-salvador-flag":{code:"+503",name:"El Salvador",isoCode:"SV / SLV"},"united-arab-emirates-flag":{code:"+971",name:"Emirados Árabes Unidos",isoCode:"AE / ARE"},"eritrea-flag":{code:"+291",name:"Eritreia",isoCode:"ER / ERI"},"spain-flag":{code:"+34",name:"Espanha",isoCode:"ES / ESP"},"slovakia-flag":{code:"+421",name:"Eslováquia",isoCode:"SK / SVK"},"slovenia-flag":{code:"+386",name:"Eslovênia",isoCode:"SI / SVN"},"estonia-flag":{code:"+372",name:"Estônia",isoCode:"EE / EST"},"ethiopia-flag":{code:"+251",name:"Etiópia",isoCode:"ET / ETH"},"fiji-flag":{code:"+679",name:"Fiji",isoCode:"FJ / FJI"},"philippines-flag":{code:"+63",name:"Filipinas",isoCode:"PH / PHL"},"finland-flag":{code:"+358",name:"Finlândia",isoCode:"FI / FIN"},"france-flag":{code:"+33",name:"França",isoCode:"FR / FRA"},"gabon-flag":{code:"+241",name:"Gabão",isoCode:"GA / GAB"},"gambia-flag":{code:"+220",name:"Gâmbia",isoCode:"GM / GMB"},"georgia-flag":{code:"+995",name:"Geórgia",isoCode:"GE / GEO"},"ghana-flag":{code:"+233",name:"Gana",isoCode:"GH / GHA"},"gibraltar-flag":{code:"+350",name:"Gibraltar",isoCode:"GI / GIB"},"grenada-flag":{code:"+1-473",name:"Granada",isoCode:"GD / GRD"},"greece-flag":{code:"+30",name:"Grécia",isoCode:"GR / GRC"},"greenland-flag":{code:"+299",name:"Groenlândia",isoCode:"GL / GRL"},"guam-flag":{code:"+1-671",name:"Guam",isoCode:"GU / GUM"},"guatemala-flag":{code:"+502",name:"Guatemala",isoCode:"GT / GTM"},"guernsey-flag":{code:"+44-1481",name:"Guernsey",isoCode:"GG / GGY"},"guinea-flag":{code:"+224",name:"Guiné",isoCode:"GN / GIN"},"guinea-bissau-flag":{code:"+245",name:"Guiné Bissau",isoCode:"GW / GNB"},"equatorial-guinea-flag":{code:"+240",name:"Guiné Equatorial",isoCode:"GQ / GNQ"},"guyana-flag":{code:"+592",name:"Guiana",isoCode:"GY / GUY"},"french-guyana-flag":{code:"+594",name:"Guiana Francesa",isoCode:"GQ / GNQ"},"haiti-flag":{code:"+509",name:"Haiti",isoCode:"HT / HTI"},"netherlands-flag":{code:"+31",name:"Holanda",isoCode:"NL / NLD"},"honduras-flag":{code:"+504",name:"Honduras",isoCode:"HN / HND"},"hong-kong-flag":{code:"+852",name:"Hong Kong",isoCode:"HK / HKG"},"hungary-flag":{code:"+36",name:"Hungria",isoCode:"HU / HUN"},"iceland-flag":{code:"+354",name:"Islândia",isoCode:"IS / ISL"},"yemen-flag":{code:"+967",name:"Iémen",isoCode:"YE / YEM"},"cayman-islands-flag":{code:"+1-345",name:"Ilhas Cayman",isoCode:"KY / CYM"},"isle-of-man-flag":{code:"+44-1624",name:"Ilha de Man",isoCode:"IM / IMN"},"christmas-island-flag":{code:"+61",name:"Ilha do Natal",isoCode:"CX / CXR"},"cook-islands-flag":{code:"+682",name:"Ilhas Cook",isoCode:"CK / COK"},"falkland-islands-flag":{code:"+500",name:"Ilhas Malvinas",isoCode:"FK / FLK"},"faroe-islands-flag":{code:"+298",name:"ilhas Faroe",isoCode:"FO / FRO"},"marshall-islands-flag":{code:"+692",name:"Ilhas Marshall",isoCode:"MH / MHL"},"northern-mariana-islands-flag":{code:"+1-670",name:"Ilhas Marianas do Norte",isoCode:"MP / MNP"},"solomon-islands-flag":{code:"+677",name:"Ilhas Salomão",isoCode:"SB / SLB"},"turks-and-caicos-islands-flag":{code:"+1-649",name:"Ilhas Turcas e Caicos",isoCode:"TC / TCA"},"u.s.-virgin-islands-flag":{code:"+1-340",name:"Ilhas Virgens Americanas",isoCode:"VI / VIR"},"india-flag":{code:"+91",name:"Índia",isoCode:"IN / IND"},"indonesia-flag":{code:"+62",name:"Indonésia",isoCode:"ID / IDN"},"iran-flag":{code:"+98",name:"Irã",isoCode:"IR / IRN"},"iraq-flag":{code:"+964",name:"Iraque",isoCode:"IQ / IRQ"},"ireland-flag":{code:"+353",name:"Irlanda",isoCode:"IE / IRL"},"israel-flag":{code:"+972",name:"Israel",isoCode:"IL / ISR"},"italy-flag":{code:"+39",name:"Itália",isoCode:"IT / ITA"},"jamaica-flag":{code:"+1-876",name:"Jamaica",isoCode:"JM / JAM"},"japan-flag":{code:"+81",name:"Japão",isoCode:"JP / JPN"},"jersey-flag":{code:"+44-1534",name:"Camisa",isoCode:"JE / JEY"},"jordan-flag":{code:"+962",name:"Jordânia",isoCode:"JO / JOR"},"kiribati-flag":{code:"+686",name:"Kiribati",isoCode:"KI / KIR"},"kosovo-flag":{code:"+383",name:"Kosovo",isoCode:"XK / XKX"},"kuwait-flag":{code:"+965",name:"Kuwait",isoCode:"KW / KWT"},"laos-flag":{code:"+856",name:"Laos",isoCode:"LA / LAO"},"latvia-flag":{code:"+371",name:"Letônia",isoCode:"LV / LVA"},"lebanon-flag":{code:"+961",name:"Líbano",isoCode:"LB / LBN"},"lesotho-flag":{code:"+266",name:"Lesoto",isoCode:"LS / LSO"},"liberia-flag":{code:"+231",name:"Libéria",isoCode:"LR / LBR"},"libya-flag":{code:"+218",name:"Líbia",isoCode:"LY / LBY"},"liechtenstein-flag":{code:"+423",name:"Liechtenstein",isoCode:"LI / LIE"},"lithuania-flag":{code:"+370",name:"Lituânia",isoCode:"LT / LTU"},"luxembourg-flag":{code:"+352",name:"Luxemburgo",isoCode:"LU / LUX"},"macau-flag":{code:"+853",name:"Macau",isoCode:"MO / MAC"},"macedonia-flag":{code:"+389",name:"Macedônia",isoCode:"MK / MKD"},"madagascar-flag":{code:"+261",name:"Madagáscar",isoCode:"MG / MDG"},"malawi-flag":{code:"+265",name:"Malawi",isoCode:"MW / MWI"},"malasya-flag":{code:"+60",name:"Malásia",isoCode:"MY / MYS"},"maldives-flag":{code:"+960",name:"Maldivas",isoCode:"MV / MDV"},"mali-flag":{code:"+223",name:"Mali",isoCode:"ML / MLI"},"malta-flag":{code:"+356",name:"Malta",isoCode:"MT / MLT"},"martinique-flag":{code:"+596",name:"Martinica",isoCode:"MQ / MTQ"},"mauritania-flag":{code:"+222",name:"Mauritânia",isoCode:"MR / MRT"},"mauritius-flag":{code:"+230",name:"Maurício",isoCode:"MU / MUS"},"mayotte-flag":{code:"+262",name:"Maiote",isoCode:"YT / MYT"},"mexico-flag":{code:"+52",name:"México",isoCode:"MX / MEX"},"micronesia-flag":{code:"+691",name:"Micronésia",isoCode:"FM / FSM"},"moldova-flag":{code:"+373",name:"Moldávia",isoCode:"MD / MDA"},"monaco-flag":{code:"+377",name:"Mônaco",isoCode:"MC / MCO"},"mongolia-flag":{code:"+976",name:"Mongólia",isoCode:"MN / MNG"},"montenegro-flag":{code:"+382",name:"Montenegro",isoCode:"ME / MNE"},"montserrat-flag":{code:"+1-664",name:"Montserrat",isoCode:"MS / MSR"},"morocco-flag":{code:"+212",name:"Marrocos",isoCode:"MA / MAR"},"mozambique-flag":{code:"+258",name:"Moçambique",isoCode:"MZ / MOZ"},"myanmar-flag":{code:"+95",name:"Mianmar",isoCode:"MM / MMR"},"namibia-flag":{code:"+264",name:"Namíbia",isoCode:"NA / NAM"},"nauru-flag":{code:"+674",name:"Nauru",isoCode:"NR / NRU"},"nepal-flag":{code:"+977",name:"Nepal",isoCode:"NP / NPL"},"nicaragua-flag":{code:"+505",name:"Nicarágua",isoCode:"NI / NIC"},"niger-flag":{code:"+227",name:"Níger",isoCode:"NE / NER"},"nigeria-flag":{code:"+234",name:"Nigéria",isoCode:"NG / NGA"},"niue-flag":{code:"+683",name:"Niue",isoCode:"NU / NIU"},"norway-flag":{code:"+47",name:"Noruega",isoCode:"NO / NOR"},"new-caledonia-flag":{code:"+687",name:"Nova Caledônia",isoCode:"NC / NCL"},"new-zealand-flag":{code:"+64",name:"Nova Zelândia",isoCode:"NZ / NZL"},"oman-flag":{code:"+968",name:"Omã",isoCode:"OM / OMN"},"pakistan-flag":{code:"+92",name:"Paquistão",isoCode:"PK / PAK"},"palau-flag":{code:"+680",name:"Palau",isoCode:"PW / PLW"},"palestine-flag":{code:"+970",name:"Palestina",isoCode:"PS / PSE"},"panama-flag":{code:"+507",name:"Panamá",isoCode:"PA / PAN"},"papua-new-guinea-flag":{code:"+675",name:"Papua Nova Guiné",isoCode:"PG / PNG"},"paraguay-flag":{code:"+595",name:"Paraguai",isoCode:"PY / PRY"},"peru-flag":{code:"+51",name:"Peru",isoCode:"PE / PER"},"pitcairn-flag":{code:"+64",name:"Pitcairn",isoCode:"PN / PCN"},"poland-flag":{code:"+48",name:"Polônia",isoCode:"PL / POL"},"french-polynesia-flag":{code:"+689",name:"Polinésia Francesa",isoCode:"PF / PYF"},"portugal-flag":{code:"+351",name:"Portugal",isoCode:"PT / PRT"},"puerto-rico-flag":{code:"+1-787",name:"Porto Rico",isoCode:"PR / PRI"},"kenya-flag":{code:"+254",name:"Quênia",isoCode:"KE / KEN"},"kyrgyzstan-flag":{code:"+996",name:"Quirguistão",isoCode:"KG / KGZ"},"central-african-republic-flag":{code:"+236",name:"República Centro-Africana",isoCode:"CF / CAF"},"czech-republic-flag":{code:"+420",name:"República Checa",isoCode:"CZ / CZE"},"dominican-republic-flag":{code:"+1-809",name:"República Dominicana",isoCode:"DO / DOM"},"united-kingdom-flag":{code:"+44",name:"Reino Unido",isoCode:"GB / GBR"},"republic-of-the-congo-flag":{code:"+242",name:"República do congo",isoCode:"CG / COG"},"democratic-republic-of-congo-flag":{code:"+243",name:"República Democratica do congo",isoCode:"CD / COD"},"reunion-flag":{code:"+262",name:"Reunião",isoCode:"RE / REU"},"romania-flag":{code:"+40",name:"Romênia",isoCode:"RO / ROU"},"russia-flag":{code:"+7",name:"Rússia",isoCode:"RU / RUS"},"rwanda-flag":{code:"+250",name:"Ruanda",isoCode:"RW / RWA"},"western-sahara-flag":{code:"+212",name:"Saara Ocidental",isoCode:"EH / ESH"},"saint-barthelemy-flag":{code:"+590",name:"São Bartolomeu",isoCode:"BL / BLM"},"saint-helena-flag":{code:"+290",name:"Santa Helena",isoCode:"SH / SHN"},"saint-kitts-and-nevis-flag":{code:"+1-869",name:"São Cristóvão e Nevis",isoCode:"KN / KNA"},"saint-lucia-flag":{code:"+1-758",name:"Santa Lúcia",isoCode:"LC / LCA"},"saint-martin-flag":{code:"+590",name:"são Martinho",isoCode:"MF / MAF"},"saint-pierre-and-miquelon-flag":{code:"+508",name:"São Pedro e Miquelon",isoCode:"PM / SPM"},"saint-vincent-and-the-grenadines-flag":{code:"+1-784",name:"São Vicente e Granadinas",isoCode:"VC / VCT"},"samoa-flag":{code:"+685",name:"Samoa",isoCode:"WS / WSM"},"american-samoa-flag":{code:"+1-684",name:"Samoa Americana",isoCode:"AS / ASM"},"san-marino-flag":{code:"+378",name:"San Marino",isoCode:"SM / SMR"},"sao-tome-and-principe-flag":{code:"+239",name:"São Tomé e Príncipe",isoCode:"ST / STP"},"senegal-flag":{code:"+221",name:"Senegal",isoCode:"SN / SEN"},"serbia-flag":{code:"+381",name:"Sérvia",isoCode:"RS / SRB"},"seychelles-flag":{code:"+248",name:"Seychelles",isoCode:"SC / SYC"},"sierra-leone-flag":{code:"+232",name:"Serra Leoa",isoCode:"SL / SLE"},"sint-maarten-flag":{code:"+1-721",name:"Sint Maarten",isoCode:"SX / SXM"},"somalia-flag":{code:"+252",name:"Somália",isoCode:"SO / SOM"},"south-korea-flag":{code:"+82",name:"Coreia do Sul",isoCode:"KR / KOR"},"south-sudan-flag":{code:"+211",name:"Sudão do Sul",isoCode:"SS / SSD"},"sri-lanka-flag":{code:"+94",name:"Sri Lanka",isoCode:"LK / LKA"},"sudan-flag":{code:"+249",name:"Sudão",isoCode:"SD / SDN"},"suriname-flag":{code:"+597",name:"Suriname",isoCode:"SR / SUR"},"svalbard-and-jan-mayen-flag":{code:"+47",name:"Svalbard e Jan Mayen",isoCode:"SJ / SJM"},"swaziland-flag":{code:"+268",name:"Suazilândia",isoCode:"SZ / SWZ"},"sweden-flag":{code:"+46",name:"Suécia",isoCode:"SE / SWE"},"switzerland-flag":{code:"+41",name:"Suíça",isoCode:"CH / CHE"},"syria-flag":{code:"+963",name:"Síria",isoCode:"SY / SYR"},"taiwan-flag":{code:"+886",name:"Taiwan",isoCode:"TW / TWN"},"tajikistan-flag":{code:"+992",name:"Tadjiquistão",isoCode:"TJ / TJK"},"tanzania-flag":{code:"+255",name:"Tanzânia",isoCode:"TZ / TZA"},"thailand-flag":{code:"+66",name:"Tailândia",isoCode:"TH / THA"},"east-timor-flag":{code:"+670",name:"Timor Leste",isoCode:"TL / TLS"},"togo-flag":{code:"+228",name:"Togo",isoCode:"TG / TGO"},"tokelau-flag":{code:"+690",name:"Toquelau",isoCode:"TK / TKL"},"tonga-flag":{code:"+676",name:"Tonga",isoCode:"TO / TON"},"trinidad-and-tobago-flag":{code:"+1-868",name:"Trindade e Tobago",isoCode:"TT / TTO"},"tunisia-flag":{code:"+216",name:"Tunísia",isoCode:"TN / TUN"},"turkey-flag":{code:"+90",name:"Turquia",isoCode:"TR / TUR"},"turkmenistan-flag":{code:"+993",name:"Turquemenistão",isoCode:"TM / TKM"},"tuvalu-flag":{code:"+688",name:"Tuvalu",isoCode:"TV / TUV"},"uganda-flag":{code:"+256",name:"Uganda",isoCode:"UG / UGA"},"ukraine-flag":{code:"+380",name:"Ucrânia",isoCode:"UA / UKR"},"uruguay-flag":{code:"+598",name:"Uruguai",isoCode:"UY / URY"},"uzbekistan-flag":{code:"+998",name:"Uzbequistão",isoCode:"UZ / UZB"},"vanuatu-flag":{code:"+678",name:"Vanuatu",isoCode:"VU / VUT"},"vatican-flag":{code:"+379",name:"Vaticano",isoCode:"VA / VAT"},"venezuela-flag":{code:"+58",name:"Venezuela",isoCode:"VE / VEN"},"vietnam-flag":{code:"+84",name:"Vietnã",isoCode:"VN / VNM"},"wallis-and-futuna-flag":{code:"+681",name:"Wallis e Futuna",isoCode:"WF / WLF"},"zambia-flag":{code:"+260",name:"Zâmbia",isoCode:"ZM / ZMB"},"zimbabwe-flag":{code:"+263",name:"Zimbábue",isoCode:"ZW / ZWE"}};var c={"brazil-flag":{code:"+55",name:"Brasil",isoCode:"BR / BRA"},"united-states-flag":{code:"+1",name:"Estados Unidos",isoCode:"US / USA"},"afghanistan-flag":{code:"+93",name:"Afeganistão",isoCode:"AF / AFG"},"south-africa-flag":{code:"+27",name:"África do Sul",isoCode:"ZA / ZAF"},"albania-flag":{code:"+355",name:"Albânia",isoCode:"AL / ALB"},"germany-flag":{code:"+49",name:"Alemanha",isoCode:"DE / DEU"},"andorra-flag":{code:"+376",name:"Andorra",isoCode:"AD / AND"},"angola-flag":{code:"+244",name:"Angola",isoCode:"AO / AGO"},"anguilla-flag":{code:"+1-264",name:"Anguila",isoCode:"AI / AIA"},"antigua-and-barbuda-flag":{code:"+1-268",name:"Antígua e Barbuda",isoCode:"AG / ATG"},"netherlands-antilles-flag":{code:"+599",name:"Antilhas Holandesas",isoCode:"AN / ANT"},"saudi-arabia-flag":{code:"+966",name:"Arábia Saudita",isoCode:"SA / SAU"},"algeria-flag":{code:"+213",name:"Argélia",isoCode:"DZ / DZA"},"argentina-flag":{code:"+54",name:"Argentina",isoCode:"AR / ARG"},"armenia-flag":{code:"+374",name:"Armênia",isoCode:"AM / ARM"},"aruba-flag":{code:"+297",name:"Aruba",isoCode:"AW / ABW"},"australia-flag":{code:"+61",name:"Austrália",isoCode:"AU / AUS"},"austria-flag":{code:"+43",name:"Áustria",isoCode:"AT / AUT"},"azerbaijan-flag":{code:"+994",name:"Azerbaijão",isoCode:"AZ / AZE"},"bahamas-flag":{code:"+1-242",name:"Bahamas",isoCode:"BS / BHS"},"bahrain-flag":{code:"+973",name:"Bahrein",isoCode:"BH / BHR"},"bangladesh-flag":{code:"+880",name:"Bangladesh",isoCode:"BD / BGD"},"barbados-flag":{code:"+1-246",name:"Barbados",isoCode:"BB / BRB"},"belgium-flag":{code:"+32",name:"Bélgica",isoCode:"BE / BEL"},"belize-flag":{code:"+501",name:"Belize",isoCode:"BZ / BLZ"},"benin-flag":{code:"+229",name:"Benim",isoCode:"BJ / BEN"},"bermuda-flag":{code:"+1-441",name:"Bermuda",isoCode:"BM / BMU"},"belarus-flag":{code:"+375",name:"Bielorrússia",isoCode:"BY / BLR"},"bolivia-flag":{code:"+591",name:"Bolívia",isoCode:"BO / BOL"},"bosnia-and-herzegovina-flag":{code:"+387",name:"Bósnia e Herzegovina",isoCode:"BA / BIH"},"botswana-flag":{code:"+267",name:"Botsuana",isoCode:"BW / BWA"},"british-indian-ocean-territory-flag":{code:"+246",name:"Território Britânico do Oceano Índico",isoCode:"IO / IOT"},"british-virgin-islands-flag":{code:"+1-284",name:"Ilhas Virgens Britânicas",isoCode:"VG / VGB"},"brunei-flag":{code:"+673",name:"Brunei",isoCode:"BN / BRN"},"bulgaria-flag":{code:"+359",name:"Bulgária",isoCode:"BG / BGR"},"burkina-faso-flag":{code:"+226",name:"Burkina Faso",isoCode:"BF / BFA"},"burundi-flag":{code:"+257",name:"Burundi",isoCode:"BI / BDI"},"bhutan-flag":{code:"+975",name:"Butão",isoCode:"BT / BTN"},"cape-verde-flag":{code:"+238",name:"Cabo Verde",isoCode:"CV / CPV"},"cameroon-flag":{code:"+237",name:"Camarões",isoCode:"CM / CMR"},"cambodia-flag":{code:"+855",name:"Camboja",isoCode:"KH / KHM"},"canada-flag":{code:"+1",name:"Canadá",isoCode:"CA / CAN"},"qatar-flag":{code:"+974",name:"Catar",isoCode:"QA / QAT"},"kazakhstan-flag":{code:"+7",name:"Cazaquistão",isoCode:"KZ / KAZ"},"chad-flag":{code:"+235",name:"Chade",isoCode:"TD / TCD"},"china-flag":{code:"+86",name:"China",isoCode:"CN / CHN"},"chile-flag":{code:"+56",name:"Chile",isoCode:"CL / CHL"},"chipre-flag":{code:"+357",name:"Chipre",isoCode:"CY / CYP"},"singapore-flag":{code:"+65",name:"Cingapura",isoCode:"SG / SGP"},"colombia-flag":{code:"+57",name:"Colômbia",isoCode:"CO / COL"},"comoros-flag":{code:"+269",name:"Comores",isoCode:"KM / COM"},"north-korea-flag":{code:"+850",name:"Coréia do Norte",isoCode:"KP / PRK"},"ivory-coast-flag":{code:"+225",name:"Costa do Marfim",isoCode:"CI / CIV"},"costa-rica-flag":{code:"+506",name:"Costa Rica",isoCode:"CR / CRI"},"croatia-flag":{code:"+385",name:"Croácia",isoCode:"HR / HRV"},"cuba-flag":{code:"+53",name:"Cuba",isoCode:"CU / CUB"},"curacao-flag":{code:"+599",name:"Curaçao",isoCode:"CW / CUW"},"denmark-flag":{code:"+45",name:"Dinamarca",isoCode:"DK / DNK"},"djibouti-flag":{code:"+253",name:"Djibuti",isoCode:"DJ / DJI"},"dominica-flag":{code:"+1-767",name:"Dominica",isoCode:"DM / DMA"},"ecuador-flag":{code:"+593",name:"Equador",isoCode:"EC / ECU"},"egypt-flag":{code:"+20",name:"Egito",isoCode:"EG / EGY"},"el-salvador-flag":{code:"+503",name:"El Salvador",isoCode:"SV / SLV"},"united-arab-emirates-flag":{code:"+971",name:"Emirados Árabes Unidos",isoCode:"AE / ARE"},"eritrea-flag":{code:"+291",name:"Eritreia",isoCode:"ER / ERI"},"spain-flag":{code:"+34",name:"Espanha",isoCode:"ES / ESP"},"slovakia-flag":{code:"+421",name:"Eslováquia",isoCode:"SK / SVK"},"slovenia-flag":{code:"+386",name:"Eslovênia",isoCode:"SI / SVN"},"estonia-flag":{code:"+372",name:"Estônia",isoCode:"EE / EST"},"ethiopia-flag":{code:"+251",name:"Etiópia",isoCode:"ET / ETH"},"fiji-flag":{code:"+679",name:"Fiji",isoCode:"FJ / FJI"},"philippines-flag":{code:"+63",name:"Filipinas",isoCode:"PH / PHL"},"finland-flag":{code:"+358",name:"Finlândia",isoCode:"FI / FIN"},"france-flag":{code:"+33",name:"França",isoCode:"FR / FRA"},"gabon-flag":{code:"+241",name:"Gabão",isoCode:"GA / GAB"},"gambia-flag":{code:"+220",name:"Gâmbia",isoCode:"GM / GMB"},"georgia-flag":{code:"+995",name:"Geórgia",isoCode:"GE / GEO"},"ghana-flag":{code:"+233",name:"Gana",isoCode:"GH / GHA"},"gibraltar-flag":{code:"+350",name:"Gibraltar",isoCode:"GI / GIB"},"grenada-flag":{code:"+1-473",name:"Granada",isoCode:"GD / GRD"},"greece-flag":{code:"+30",name:"Grécia",isoCode:"GR / GRC"},"greenland-flag":{code:"+299",name:"Groenlândia",isoCode:"GL / GRL"},"guam-flag":{code:"+1-671",name:"Guam",isoCode:"GU / GUM"},"guatemala-flag":{code:"+502",name:"Guatemala",isoCode:"GT / GTM"},"guernsey-flag":{code:"+44-1481",name:"Guernsey",isoCode:"GG / GGY"},"guinea-flag":{code:"+224",name:"Guiné",isoCode:"GN / GIN"},"guinea-bissau-flag":{code:"+245",name:"Guiné Bissau",isoCode:"GW / GNB"},"equatorial-guinea-flag":{code:"+240",name:"Guiné Equatorial",isoCode:"GQ / GNQ"},"guyana-flag":{code:"+592",name:"Guiana",isoCode:"GY / GUY"},"french-guyana-flag":{code:"+594",name:"Guiana Francesa",isoCode:"GQ / GNQ"},"haiti-flag":{code:"+509",name:"Haiti",isoCode:"HT / HTI"},"netherlands-flag":{code:"+31",name:"Holanda",isoCode:"NL / NLD"},"honduras-flag":{code:"+504",name:"Honduras",isoCode:"HN / HND"},"hong-kong-flag":{code:"+852",name:"Hong Kong",isoCode:"HK / HKG"},"hungary-flag":{code:"+36",name:"Hungria",isoCode:"HU / HUN"},"iceland-flag":{code:"+354",name:"Islândia",isoCode:"IS / ISL"},"yemen-flag":{code:"+967",name:"Iémen",isoCode:"YE / YEM"},"cayman-islands-flag":{code:"+1-345",name:"Ilhas Cayman",isoCode:"KY / CYM"},"isle-of-man-flag":{code:"+44-1624",name:"Ilha de Man",isoCode:"IM / IMN"},"christmas-island-flag":{code:"+61",name:"Ilha do Natal",isoCode:"CX / CXR"},"cook-islands-flag":{code:"+682",name:"Ilhas Cook",isoCode:"CK / COK"},"falkland-islands-flag":{code:"+500",name:"Ilhas Malvinas",isoCode:"FK / FLK"},"faroe-islands-flag":{code:"+298",name:"ilhas Faroe",isoCode:"FO / FRO"},"marshall-islands-flag":{code:"+692",name:"Ilhas Marshall",isoCode:"MH / MHL"},"northern-mariana-islands-flag":{code:"+1-670",name:"Ilhas Marianas do Norte",isoCode:"MP / MNP"},"solomon-islands-flag":{code:"+677",name:"Ilhas Salomão",isoCode:"SB / SLB"},"turks-and-caicos-islands-flag":{code:"+1-649",name:"Ilhas Turcas e Caicos",isoCode:"TC / TCA"},"u.s.-virgin-islands-flag":{code:"+1-340",name:"Ilhas Virgens Americanas",isoCode:"VI / VIR"},"india-flag":{code:"+91",name:"Índia",isoCode:"IN / IND"},"indonesia-flag":{code:"+62",name:"Indonésia",isoCode:"ID / IDN"},"iran-flag":{code:"+98",name:"Irã",isoCode:"IR / IRN"},"iraq-flag":{code:"+964",name:"Iraque",isoCode:"IQ / IRQ"},"ireland-flag":{code:"+353",name:"Irlanda",isoCode:"IE / IRL"},"israel-flag":{code:"+972",name:"Israel",isoCode:"IL / ISR"},"italy-flag":{code:"+39",name:"Itália",isoCode:"IT / ITA"},"jamaica-flag":{code:"+1-876",name:"Jamaica",isoCode:"JM / JAM"},"japan-flag":{code:"+81",name:"Japão",isoCode:"JP / JPN"},"jersey-flag":{code:"+44-1534",name:"Camisa",isoCode:"JE / JEY"},"jordan-flag":{code:"+962",name:"Jordânia",isoCode:"JO / JOR"},"kiribati-flag":{code:"+686",name:"Kiribati",isoCode:"KI / KIR"},"kosovo-flag":{code:"+383",name:"Kosovo",isoCode:"XK / XKX"},"kuwait-flag":{code:"+965",name:"Kuwait",isoCode:"KW / KWT"},"laos-flag":{code:"+856",name:"Laos",isoCode:"LA / LAO"},"latvia-flag":{code:"+371",name:"Letônia",isoCode:"LV / LVA"},"lebanon-flag":{code:"+961",name:"Líbano",isoCode:"LB / LBN"},"lesotho-flag":{code:"+266",name:"Lesoto",isoCode:"LS / LSO"},"liberia-flag":{code:"+231",name:"Libéria",isoCode:"LR / LBR"},"libya-flag":{code:"+218",name:"Líbia",isoCode:"LY / LBY"},"liechtenstein-flag":{code:"+423",name:"Liechtenstein",isoCode:"LI / LIE"},"lithuania-flag":{code:"+370",name:"Lituânia",isoCode:"LT / LTU"},"luxembourg-flag":{code:"+352",name:"Luxemburgo",isoCode:"LU / LUX"},"macau-flag":{code:"+853",name:"Macau",isoCode:"MO / MAC"},"macedonia-flag":{code:"+389",name:"Macedônia",isoCode:"MK / MKD"},"madagascar-flag":{code:"+261",name:"Madagáscar",isoCode:"MG / MDG"},"malawi-flag":{code:"+265",name:"Malawi",isoCode:"MW / MWI"},"malaysia-flag":{code:"+60",name:"Malásia",isoCode:"MY / MYS"},"maldives-flag":{code:"+960",name:"Maldivas",isoCode:"MV / MDV"},"mali-flag":{code:"+223",name:"Mali",isoCode:"ML / MLI"},"malta-flag":{code:"+356",name:"Malta",isoCode:"MT / MLT"},"martinique-flag":{code:"+596",name:"Martinica",isoCode:"MQ / MTQ"},"mauritania-flag":{code:"+222",name:"Mauritânia",isoCode:"MR / MRT"},"mauritius-flag":{code:"+230",name:"Maurício",isoCode:"MU / MUS"},"mayotte-flag":{code:"+262",name:"Maiote",isoCode:"YT / MYT"},"mexico-flag":{code:"+52",name:"México",isoCode:"MX / MEX"},"micronesia-flag":{code:"+691",name:"Micronésia",isoCode:"FM / FSM"},"moldova-flag":{code:"+373",name:"Moldávia",isoCode:"MD / MDA"},"monaco-flag":{code:"+377",name:"Mônaco",isoCode:"MC / MCO"},"mongolia-flag":{code:"+976",name:"Mongólia",isoCode:"MN / MNG"},"montenegro-flag":{code:"+382",name:"Montenegro",isoCode:"ME / MNE"},"montserrat-flag":{code:"+1-664",name:"Montserrat",isoCode:"MS / MSR"},"morocco-flag":{code:"+212",name:"Marrocos",isoCode:"MA / MAR"},"mozambique-flag":{code:"+258",name:"Moçambique",isoCode:"MZ / MOZ"},"myanmar-flag":{code:"+95",name:"Mianmar",isoCode:"MM / MMR"},"namibia-flag":{code:"+264",name:"Namíbia",isoCode:"NA / NAM"},"nauru-flag":{code:"+674",name:"Nauru",isoCode:"NR / NRU"},"nepal-flag":{code:"+977",name:"Nepal",isoCode:"NP / NPL"},"nicaragua-flag":{code:"+505",name:"Nicarágua",isoCode:"NI / NIC"},"niger-flag":{code:"+227",name:"Níger",isoCode:"NE / NER"},"nigeria-flag":{code:"+234",name:"Nigéria",isoCode:"NG / NGA"},"niue-flag":{code:"+683",name:"Niue",isoCode:"NU / NIU"},"norway-flag":{code:"+47",name:"Noruega",isoCode:"NO / NOR"},"new-caledonia-flag":{code:"+687",name:"Nova Caledônia",isoCode:"NC / NCL"},"new-zealand-flag":{code:"+64",name:"Nova Zelândia",isoCode:"NZ / NZL"},"oman-flag":{code:"+968",name:"Omã",isoCode:"OM / OMN"},"pakistan-flag":{code:"+92",name:"Paquistão",isoCode:"PK / PAK"},"palau-flag":{code:"+680",name:"Palau",isoCode:"PW / PLW"},"palestine-flag":{code:"+970",name:"Palestina",isoCode:"PS / PSE"},"panama-flag":{code:"+507",name:"Panamá",isoCode:"PA / PAN"},"papua-new-guinea-flag":{code:"+675",name:"Papua Nova Guiné",isoCode:"PG / PNG"},"paraguay-flag":{code:"+595",name:"Paraguai",isoCode:"PY / PRY"},"peru-flag":{code:"+51",name:"Peru",isoCode:"PE / PER"},"pitcairn-flag":{code:"+64",name:"Pitcairn",isoCode:"PN / PCN"},"poland-flag":{code:"+48",name:"Polônia",isoCode:"PL / POL"},"french-polynesia-flag":{code:"+689",name:"Polinésia Francesa",isoCode:"PF / PYF"},"portugal-flag":{code:"+351",name:"Portugal",isoCode:"PT / PRT"},"puerto-rico-flag":{code:"+1-787",name:"Porto Rico",isoCode:"PR / PRI"},"kenya-flag":{code:"+254",name:"Quênia",isoCode:"KE / KEN"},"kyrgyzstan-flag":{code:"+996",name:"Quirguistão",isoCode:"KG / KGZ"},"central-african-republic-flag":{code:"+236",name:"República Centro-Africana",isoCode:"CF / CAF"},"czech-republic-flag":{code:"+420",name:"República Checa",isoCode:"CZ / CZE"},"dominican-republic-flag":{code:"+1-809",name:"República Dominicana",isoCode:"DO / DOM"},"united-kingdom-flag":{code:"+44",name:"Reino Unido",isoCode:"GB / GBR"},"republic-of-the-congo-flag":{code:"+242",name:"República do congo",isoCode:"CG / COG"},"democratic-republic-of-congo-flag":{code:"+243",name:"República Democratica do congo",isoCode:"CD / COD"},"reunion-flag":{code:"+262",name:"Reunião",isoCode:"RE / REU"},"romania-flag":{code:"+40",name:"Romênia",isoCode:"RO / ROU"},"russia-flag":{code:"+7",name:"Rússia",isoCode:"RU / RUS"},"rwanda-flag":{code:"+250",name:"Ruanda",isoCode:"RW / RWA"},"western-sahara-flag":{code:"+212",name:"Saara Ocidental",isoCode:"EH / ESH"},"saint-barthelemy-flag":{code:"+590",name:"São Bartolomeu",isoCode:"BL / BLM"},"saint-helena-flag":{code:"+290",name:"Santa Helena",isoCode:"SH / SHN"},"saint-kitts-and-nevis-flag":{code:"+1-869",name:"São Cristóvão e Nevis",isoCode:"KN / KNA"},"saint-lucia-flag":{code:"+1-758",name:"Santa Lúcia",isoCode:"LC / LCA"},"saint-martin-flag":{code:"+590",name:"são Martinho",isoCode:"MF / MAF"},"saint-pierre-and-miquelon-flag":{code:"+508",name:"São Pedro e Miquelon",isoCode:"PM / SPM"},"saint-vincent-and-the-grenadines-flag":{code:"+1-784",name:"São Vicente e Granadinas",isoCode:"VC / VCT"},"samoa-flag":{code:"+685",name:"Samoa",isoCode:"WS / WSM"},"american-samoa-flag":{code:"+1-684",name:"Samoa Americana",isoCode:"AS / ASM"},"san-marino-flag":{code:"+378",name:"San Marino",isoCode:"SM / SMR"},"sao-tome-and-principe-flag":{code:"+239",name:"São Tomé e Príncipe",isoCode:"ST / STP"},"senegal-flag":{code:"+221",name:"Senegal",isoCode:"SN / SEN"},"serbia-flag":{code:"+381",name:"Sérvia",isoCode:"RS / SRB"},"seychelles-flag":{code:"+248",name:"Seychelles",isoCode:"SC / SYC"},"sierra-leone-flag":{code:"+232",name:"Serra Leoa",isoCode:"SL / SLE"},"sint-maarten-flag":{code:"+1-721",name:"Sint Maarten",isoCode:"SX / SXM"},"somalia-flag":{code:"+252",name:"Somália",isoCode:"SO / SOM"},"south-korea-flag":{code:"+82",name:"Coreia do Sul",isoCode:"KR / KOR"},"south-sudan-flag":{code:"+211",name:"Sudão do Sul",isoCode:"SS / SSD"},"sri-lanka-flag":{code:"+94",name:"Sri Lanka",isoCode:"LK / LKA"},"sudan-flag":{code:"+249",name:"Sudão",isoCode:"SD / SDN"},"suriname-flag":{code:"+597",name:"Suriname",isoCode:"SR / SUR"},"svalbard-and-jan-mayen-flag":{code:"+47",name:"Svalbard e Jan Mayen",isoCode:"SJ / SJM"},"swaziland-flag":{code:"+268",name:"Suazilândia",isoCode:"SZ / SWZ"},"sweden-flag":{code:"+46",name:"Suécia",isoCode:"SE / SWE"},"switzerland-flag":{code:"+41",name:"Suíça",isoCode:"CH / CHE"},"syria-flag":{code:"+963",name:"Síria",isoCode:"SY / SYR"},"taiwan-flag":{code:"+886",name:"Taiwan",isoCode:"TW / TWN"},"tajikistan-flag":{code:"+992",name:"Tadjiquistão",isoCode:"TJ / TJK"},"tanzania-flag":{code:"+255",name:"Tanzânia",isoCode:"TZ / TZA"},"thailand-flag":{code:"+66",name:"Tailândia",isoCode:"TH / THA"},"east-timor-flag":{code:"+670",name:"Timor Leste",isoCode:"TL / TLS"},"togo-flag":{code:"+228",name:"Togo",isoCode:"TG / TGO"},"tokelau-flag":{code:"+690",name:"Toquelau",isoCode:"TK / TKL"},"tonga-flag":{code:"+676",name:"Tonga",isoCode:"TO / TON"},"trinidad-and-tobago-flag":{code:"+1-868",name:"Trindade e Tobago",isoCode:"TT / TTO"},"tunisia-flag":{code:"+216",name:"Tunísia",isoCode:"TN / TUN"},"turkey-flag":{code:"+90",name:"Turquia",isoCode:"TR / TUR"},"turkmenistan-flag":{code:"+993",name:"Turquemenistão",isoCode:"TM / TKM"},"tuvalu-flag":{code:"+688",name:"Tuvalu",isoCode:"TV / TUV"},"uganda-flag":{code:"+256",name:"Uganda",isoCode:"UG / UGA"},"ukraine-flag":{code:"+380",name:"Ucrânia",isoCode:"UA / UKR"},"uruguay-flag":{code:"+598",name:"Uruguai",isoCode:"UY / URY"},"uzbekistan-flag":{code:"+998",name:"Uzbequistão",isoCode:"UZ / UZB"},"vanuatu-flag":{code:"+678",name:"Vanuatu",isoCode:"VU / VUT"},"vatican-flag":{code:"+379",name:"Vaticano",isoCode:"VA / VAT"},"venezuela-flag":{code:"+58",name:"Venezuela",isoCode:"VE / VEN"},"vietnam-flag":{code:"+84",name:"Vietnã",isoCode:"VN / VNM"},"wallis-and-futuna-flag":{code:"+681",name:"Wallis e Futuna",isoCode:"WF / WLF"},"zambia-flag":{code:"+260",name:"Zâmbia",isoCode:"ZM / ZMB"},"zimbabwe-flag":{code:"+263",name:"Zimbábue",isoCode:"ZW / ZWE"}};var r={"brazil-flag":{code:"+55",name:"Brazil",isoCode:"BR / BRA"},"united-states-flag":{code:"+1",name:"United States",isoCode:"US / USA"},"afghanistan-flag":{code:"+93",name:"Afghanistan",isoCode:"AF / AFG"},"south-africa-flag":{code:"+27",name:"South Africa",isoCode:"ZA / ZAF"},"albania-flag":{code:"+355",name:"Albania",isoCode:"AL / ALB"},"germany-flag":{code:"+49",name:"Germany",isoCode:"DE / DEU"},"andorra-flag":{code:"+376",name:"Andorra",isoCode:"AD / AND"},"angola-flag":{code:"+244",name:"Angola",isoCode:"AO / AGO"},"anguilla-flag":{code:"+1-264",name:"Anguilla",isoCode:"AI / AIA"},"antigua-and-barbuda-flag":{code:"+1-268",name:"Antigua and Barbuda",isoCode:"AG / ATG"},"netherlands-antilles-flag":{code:"+599",name:"Netherlands Antilles",isoCode:"AN / ANT"},"saudi-arabia-flag":{code:"+966",name:"Saudi Arabia",isoCode:"SA / SAU"},"algeria-flag":{code:"+213",name:"Algeria",isoCode:"DZ / DZA"},"argentina-flag":{code:"+54",name:"Argentina",isoCode:"AR / ARG"},"armenia-flag":{code:"+374",name:"Armenia",isoCode:"AM / ARM"},"aruba-flag":{code:"+297",name:"Aruba",isoCode:"AW / ABW"},"australia-flag":{code:"+61",name:"Australia",isoCode:"AU / AUS"},"austria-flag":{code:"+43",name:"Austria",isoCode:"AT / AUT"},"azerbaijan-flag":{code:"+994",name:"Azerbaijan",isoCode:"AZ / AZE"},"bahamas-flag":{code:"+1-242",name:"Bahamas",isoCode:"BS / BHS"},"bahrain-flag":{code:"+973",name:"Bahrain",isoCode:"BH / BHR"},"bangladesh-flag":{code:"+880",name:"Bangladesh",isoCode:"BD / BGD"},"barbados-flag":{code:"+1-246",name:"Barbados",isoCode:"BB / BRB"},"belgium-flag":{code:"+32",name:"Belgium",isoCode:"BE / BEL"},"belize-flag":{code:"+501",name:"Belize",isoCode:"BZ / BLZ"},"benin-flag":{code:"+229",name:"Benin",isoCode:"BJ / BEN"},"bermuda-flag":{code:"+1-441",name:"Bermuda",isoCode:"BM / BMU"},"belarus-flag":{code:"+375",name:"Belarus",isoCode:"BY / BLR"},"bolivia-flag":{code:"+591",name:"Bolivia",isoCode:"BO / BOL"},"bosnia-and-herzegovina-flag":{code:"+387",name:"Bosnia and Herzegovina",isoCode:"BA / BIH"},"botswana-flag":{code:"+267",name:"Botswana",isoCode:"BW / BWA"},"british-indian-ocean-territory-flag":{code:"+246",name:"British Indian Ocean Territory",isoCode:"IO / IOT"},"british-virgin-islands-flag":{code:"+1-284",name:"British Virgin Islands",isoCode:"VG / VGB"},"brunei-flag":{code:"+673",name:"Brunei",isoCode:"BN / BRN"},"bulgaria-flag":{code:"+359",name:"Bulgaria",isoCode:"BG / BGR"},"burkina-faso-flag":{code:"+226",name:"Burkina Faso",isoCode:"BF / BFA"},"burundi-flag":{code:"+257",name:"Burundi",isoCode:"BI / BDI"},"bhutan-flag":{code:"+975",name:"Bhutan",isoCode:"BT / BTN"},"cape-verde-flag":{code:"+238",name:"Cape Verde",isoCode:"CV / CPV"},"cameroon-flag":{code:"+237",name:"Cameroon",isoCode:"CM / CMR"},"cambodia-flag":{code:"+855",name:"Cambodia",isoCode:"KH / KHM"},"canada-flag":{code:"+1",name:"Canada",isoCode:"CA / CAN"},"qatar-flag":{code:"+974",name:"Qatar",isoCode:"QA / QAT"},"kazakhstan-flag":{code:"+7",name:"Kazakhstan",isoCode:"KZ / KAZ"},"chad-flag":{code:"+235",name:"Chad",isoCode:"TD / TCD"},"china-flag":{code:"+86",name:"China",isoCode:"CN / CHN"},"chile-flag":{code:"+56",name:"Chile",isoCode:"CL / CHL"},"chipre-flag":{code:"+357",name:"Cyprus",isoCode:"CY / CYP"},"singapore-flag":{code:"+65",name:"Singapore",isoCode:"SG / SGP"},"colombia-flag":{code:"+57",name:"Colombia",isoCode:"CO / COL"},"comoros-flag":{code:"+269",name:"Comoros",isoCode:"KM / COM"},"north-korea-flag":{code:"+850",name:"North Korea",isoCode:"KP / PRK"},"ivory-coast-flag":{code:"+225",name:"Ivory Coast",isoCode:"CI / CIV"},"costa-rica-flag":{code:"+506",name:"Costa Rica",isoCode:"CR / CRI"},"croatia-flag":{code:"+385",name:"Croatia",isoCode:"HR / HRV"},"cuba-flag":{code:"+53",name:"Cuba",isoCode:"CU / CUB"},"curacao-flag":{code:"+599",name:"Curaçao",isoCode:"CW / CUW"},"denmark-flag":{code:"+45",name:"Denmark",isoCode:"DK / DNK"},"djibouti-flag":{code:"+253",name:"Djibouti",isoCode:"DJ / DJI"},"dominica-flag":{code:"+1-767",name:"Dominica",isoCode:"DM / DMA"},"ecuador-flag":{code:"+593",name:"Ecuador",isoCode:"EC / ECU"},"egypt-flag":{code:"+20",name:"Egypt",isoCode:"EG / EGY"},"el-salvador-flag":{code:"+503",name:"El Salvador",isoCode:"SV / SLV"},"united-arab-emirates-flag":{code:"+971",name:"United Arab Emirates",isoCode:"AE / ARE"},"eritrea-flag":{code:"+291",name:"Eritrea",isoCode:"ER / ERI"},"spain-flag":{code:"+34",name:"Spain",isoCode:"ES / ESP"},"slovakia-flag":{code:"+421",name:"Slovakia",isoCode:"SK / SVK"},"slovenia-flag":{code:"+386",name:"Slovenia",isoCode:"SI / SVN"},"estonia-flag":{code:"+372",name:"Estonia",isoCode:"EE / EST"},"ethiopia-flag":{code:"+251",name:"Ethiopia",isoCode:"ET / ETH"},"fiji-flag":{code:"+679",name:"Fiji",isoCode:"FJ / FJI"},"philippines-flag":{code:"+63",name:"Philippines",isoCode:"PH / PHL"},"finland-flag":{code:"+358",name:"Finland",isoCode:"FI / FIN"},"france-flag":{code:"+33",name:"France",isoCode:"FR / FRA"},"gabon-flag":{code:"+241",name:"Gabon",isoCode:"GA / GAB"},"gambia-flag":{code:"+220",name:"Gambia",isoCode:"GM / GMB"},"georgia-flag":{code:"+995",name:"Georgia",isoCode:"GE / GEO"},"ghana-flag":{code:"+233",name:"Ghana",isoCode:"GH / GHA"},"gibraltar-flag":{code:"+350",name:"Gibraltar",isoCode:"GI / GIB"},"grenada-flag":{code:"+1-473",name:"Grenada",isoCode:"GD / GRD"},"greece-flag":{code:"+30",name:"Greece",isoCode:"GR / GRC"},"greenland-flag":{code:"+299",name:"Greenland",isoCode:"GL / GRL"},"guam-flag":{code:"+1-671",name:"Guam",isoCode:"GU / GUM"},"guatemala-flag":{code:"+502",name:"Guatemala",isoCode:"GT / GTM"},"guernsey-flag":{code:"+44-1481",name:"Guernsey",isoCode:"GG / GGY"},"guinea-flag":{code:"+224",name:"Guinea",isoCode:"GN / GIN"},"guinea-bissau-flag":{code:"+245",name:"Guinea-Bissau",isoCode:"GW / GNB"},"equatorial-guinea-flag":{code:"+240",name:"Equatorial Guinea",isoCode:"GQ / GNQ"},"guyana-flag":{code:"+592",name:"Guyana",isoCode:"GY / GUY"},"french-guyana-flag":{code:"+594",name:"French Guiana",isoCode:"GQ / GNQ"},"haiti-flag":{code:"+509",name:"Haiti",isoCode:"HT / HTI"},"netherlands-flag":{code:"+31",name:"Netherlands",isoCode:"NL / NLD"},"honduras-flag":{code:"+504",name:"Honduras",isoCode:"HN / HND"},"hong-kong-flag":{code:"+852",name:"Hong Kong",isoCode:"HK / HKG"},"hungary-flag":{code:"+36",name:"Hungary",isoCode:"HU / HUN"},"iceland-flag":{code:"+354",name:"Iceland",isoCode:"IS / ISL"},"yemen-flag":{code:"+967",name:"Yemen",isoCode:"YE / YEM"},"cayman-islands-flag":{code:"+1-345",name:"Cayman Islands",isoCode:"KY / CYM"},"isle-of-man-flag":{code:"+44-1624",name:"Isle of Man",isoCode:"IM / IMN"},"christmas-island-flag":{code:"+61",name:"Christmas Island",isoCode:"CX / CXR"},"cook-islands-flag":{code:"+682",name:"Cook Islands",isoCode:"CK / COK"},"falkland-islands-flag":{code:"+500",name:"Falkland Islands",isoCode:"FK / FLK"},"faroe-islands-flag":{code:"+298",name:"Faroe Islands",isoCode:"FO / FRO"},"marshall-islands-flag":{code:"+692",name:"Marshall Islands",isoCode:"MH / MHL"},"northern-mariana-islands-flag":{code:"+1-670",name:"Northern Mariana Islands",isoCode:"MP / MNP"},"solomon-islands-flag":{code:"+677",name:"Solomon Islands",isoCode:"SB / SLB"},"turks-and-caicos-islands-flag":{code:"+1-649",name:"Turks and Caicos Islands",isoCode:"TC / TCA"},"u.s.-virgin-islands-flag":{code:"+1-340",name:"U.S. Virgin Islands",isoCode:"VI / VIR"},"india-flag":{code:"+91",name:"India",isoCode:"IN / IND"},"indonesia-flag":{code:"+62",name:"Indonesia",isoCode:"ID / IDN"},"iran-flag":{code:"+98",name:"Iran",isoCode:"IR / IRN"},"iraq-flag":{code:"+964",name:"Iraq",isoCode:"IQ / IRQ"},"ireland-flag":{code:"+353",name:"Ireland",isoCode:"IE / IRL"},"israel-flag":{code:"+972",name:"Israel",isoCode:"IL / ISR"},"italy-flag":{code:"+39",name:"Italy",isoCode:"IT / ITA"},"jamaica-flag":{code:"+1-876",name:"Jamaica",isoCode:"JM / JAM"},"japan-flag":{code:"+81",name:"Japan",isoCode:"JP / JPN"},"jersey-flag":{code:"+44-1534",name:"Jersey",isoCode:"JE / JEY"},"jordan-flag":{code:"+962",name:"Jordan",isoCode:"JO / JOR"},"kiribati-flag":{code:"+686",name:"Kiribati",isoCode:"KI / KIR"},"kosovo-flag":{code:"+383",name:"Kosovo",isoCode:"XK / XKX"},"kuwait-flag":{code:"+965",name:"Kuwait",isoCode:"KW / KWT"},"laos-flag":{code:"+856",name:"Laos",isoCode:"LA / LAO"},"latvia-flag":{code:"+371",name:"Latvia",isoCode:"LV / LVA"},"lebanon-flag":{code:"+961",name:"Lebanon",isoCode:"LB / LBN"},"lesotho-flag":{code:"+266",name:"Lesotho",isoCode:"LS / LSO"},"liberia-flag":{code:"+231",name:"Liberia",isoCode:"LR / LBR"},"libya-flag":{code:"+218",name:"Libya",isoCode:"LY / LBY"},"liechtenstein-flag":{code:"+423",name:"Liechtenstein",isoCode:"LI / LIE"},"lithuania-flag":{code:"+370",name:"Lithuania",isoCode:"LT / LTU"},"luxembourg-flag":{code:"+352",name:"Luxembourg",isoCode:"LU / LUX"},"macau-flag":{code:"+853",name:"Macau",isoCode:"MO / MAC"},"macedonia-flag":{code:"+389",name:"Macedonia",isoCode:"MK / MKD"},"madagascar-flag":{code:"+261",name:"Madagascar",isoCode:"MG / MDG"},"malawi-flag":{code:"+265",name:"Malawi",isoCode:"MW / MWI"},"malaysia-flag":{code:"+60",name:"Malaysia",isoCode:"MY / MYS"},"maldives-flag":{code:"+960",name:"Maldives",isoCode:"MV / MDV"},"mali-flag":{code:"+223",name:"Mali",isoCode:"ML / MLI"},"malta-flag":{code:"+356",name:"Malta",isoCode:"MT / MLT"},"martinique-flag":{code:"+596",name:"Martinique",isoCode:"MQ / MTQ"},"mauritania-flag":{code:"+222",name:"Mauritania",isoCode:"MR / MRT"},"mauritius-flag":{code:"+230",name:"Mauritius",isoCode:"MU / MUS"},"mayotte-flag":{code:"+262",name:"Mayotte",isoCode:"YT / MYT"},"mexico-flag":{code:"+52",name:"Mexico",isoCode:"MX / MEX"},"micronesia-flag":{code:"+691",name:"Micronesia",isoCode:"FM / FSM"},"moldova-flag":{code:"+373",name:"Moldova",isoCode:"MD / MDA"},"monaco-flag":{code:"+377",name:"Monaco",isoCode:"MC / MCO"},"mongolia-flag":{code:"+976",name:"Mongolia",isoCode:"MN / MNG"},"montenegro-flag":{code:"+382",name:"Montenegro",isoCode:"ME / MNE"},"montserrat-flag":{code:"+1-664",name:"Montserrat",isoCode:"MS / MSR"},"morocco-flag":{code:"+212",name:"Morocco",isoCode:"MA / MAR"},"mozambique-flag":{code:"+258",name:"Mozambique",isoCode:"MZ / MOZ"},"myanmar-flag":{code:"+95",name:"Myanmar",isoCode:"MM / MMR"},"namibia-flag":{code:"+264",name:"Namibia",isoCode:"NA / NAM"},"nauru-flag":{code:"+674",name:"Nauru",isoCode:"NR / NRU"},"nepal-flag":{code:"+977",name:"Nepal",isoCode:"NP / NPL"},"nicaragua-flag":{code:"+505",name:"Nicaragua",isoCode:"NI / NIC"},"niger-flag":{code:"+227",name:"Niger",isoCode:"NE / NER"},"nigeria-flag":{code:"+234",name:"Nigeria",isoCode:"NG / NGA"},"niue-flag":{code:"+683",name:"Niue",isoCode:"NU / NIU"},"norway-flag":{code:"+47",name:"Norway",isoCode:"NO / NOR"},"new-caledonia-flag":{code:"+687",name:"New Caledonia",isoCode:"NC / NCL"},"new-zealand-flag":{code:"+64",name:"New Zealand",isoCode:"NZ / NZL"},"oman-flag":{code:"+968",name:"Oman",isoCode:"OM / OMN"},"pakistan-flag":{code:"+92",name:"Pakistan",isoCode:"PK / PAK"},"palau-flag":{code:"+680",name:"Palau",isoCode:"PW / PLW"},"palestine-flag":{code:"+970",name:"Palestine",isoCode:"PS / PSE"},"panama-flag":{code:"+507",name:"Panama",isoCode:"PA / PAN"},"papua-new-guinea-flag":{code:"+675",name:"Papua New Guinea",isoCode:"PG / PNG"},"paraguay-flag":{code:"+595",name:"Paraguay",isoCode:"PY / PRY"},"peru-flag":{code:"+51",name:"Peru",isoCode:"PE / PER"},"pitcairn-flag":{code:"+64",name:"Pitcairn",isoCode:"PN / PCN"},"poland-flag":{code:"+48",name:"Poland",isoCode:"PL / POL"},"french-polynesia-flag":{code:"+689",name:"French Polynesia",isoCode:"PF / PYF"},"portugal-flag":{code:"+351",name:"Portugal",isoCode:"PT / PRT"},"puerto-rico-flag":{code:"+1-787",name:"Puerto Rico",isoCode:"PR / PRI"},"kenya-flag":{code:"+254",name:"Kenya",isoCode:"KE / KEN"},"kyrgyzstan-flag":{code:"+996",name:"Kyrgyzstan",isoCode:"KG / KGZ"},"central-african-republic-flag":{code:"+236",name:"Central African Republic",isoCode:"CF / CAF"},"czech-republic-flag":{code:"+420",name:"Czech Republic",isoCode:"CZ / CZE"},"dominican-republic-flag":{code:"+1-809",name:"Dominican Republic",isoCode:"DO / DOM"},"united-kingdom-flag":{code:"+44",name:"United Kingdom",isoCode:"GB / GBR"},"republic-of-the-congo-flag":{code:"+242",name:"Republic of the Congo",isoCode:"CG / COG"},"democratic-republic-of-congo-flag":{code:"+243",name:"Democratic Republic of the Congo",isoCode:"CD / COD"},"reunion-flag":{code:"+262",name:"Réunion",isoCode:"RE / REU"},"romania-flag":{code:"+40",name:"Romania",isoCode:"RO / ROU"},"russia-flag":{code:"+7",name:"Russia",isoCode:"RU / RUS"},"rwanda-flag":{code:"+250",name:"Rwanda",isoCode:"RW / RWA"},"western-sahara-flag":{code:"+212",name:"Western Sahara",isoCode:"EH / ESH"},"saint-barthelemy-flag":{code:"+590",name:"Saint Barthélemy",isoCode:"BL / BLM"},"saint-helena-flag":{code:"+290",name:"Saint Helena",isoCode:"SH / SHN"},"saint-kitts-and-nevis-flag":{code:"+1-869",name:"Saint Kitts and Nevis",isoCode:"KN / KNA"},"saint-lucia-flag":{code:"+1-758",name:"Saint Lucia",isoCode:"LC / LCA"},"saint-martin-flag":{code:"+590",name:"Saint Martin",isoCode:"MF / MAF"},"saint-pierre-and-miquelon-flag":{code:"+508",name:"Saint Pierre and Miquelon",isoCode:"PM / SPM"},"saint-vincent-and-the-grenadines-flag":{code:"+1-784",name:"Saint Vincent and the Grenadines",isoCode:"VC / VCT"},"samoa-flag":{code:"+685",name:"Samoa",isoCode:"WS / WSM"},"american-samoa-flag":{code:"+1-684",name:"American Samoa",isoCode:"AS / ASM"},"san-marino-flag":{code:"+378",name:"San Marino",isoCode:"SM / SMR"},"sao-tome-and-principe-flag":{code:"+239",name:"Sao Tome and Principe",isoCode:"ST / STP"},"senegal-flag":{code:"+221",name:"Senegal",isoCode:"SN / SEN"},"serbia-flag":{code:"+381",name:"Serbia",isoCode:"RS / SRB"},"seychelles-flag":{code:"+248",name:"Seychelles",isoCode:"SC / SYC"},"sierra-leone-flag":{code:"+232",name:"Sierra Leone",isoCode:"SL / SLE"},"sint-maarten-flag":{code:"+1-721",name:"Sint Maarten",isoCode:"SX / SXM"},"somalia-flag":{code:"+252",name:"Somalia",isoCode:"SO / SOM"},"south-korea-flag":{code:"+82",name:"South Korea",isoCode:"KR / KOR"},"south-sudan-flag":{code:"+211",name:"South Sudan",isoCode:"SS / SSD"},"sri-lanka-flag":{code:"+94",name:"Sri Lanka",isoCode:"LK / LKA"},"sudan-flag":{code:"+249",name:"Sudan",isoCode:"SD / SDN"},"suriname-flag":{code:"+597",name:"Suriname",isoCode:"SR / SUR"},"svalbard-and-jan-mayen-flag":{code:"+47",name:"Svalbard and Jan Mayen",isoCode:"SJ / SJM"},"swaziland-flag":{code:"+268",name:"Swaziland",isoCode:"SZ / SWZ"},"sweden-flag":{code:"+46",name:"Sweden",isoCode:"SE / SWE"},"switzerland-flag":{code:"+41",name:"Switzerland",isoCode:"CH / CHE"},"syria-flag":{code:"+963",name:"Syria",isoCode:"SY / SYR"},"taiwan-flag":{code:"+886",name:"Taiwan",isoCode:"TW / TWN"},"tajikistan-flag":{code:"+992",name:"Tajikistan",isoCode:"TJ / TJK"},"tanzania-flag":{code:"+255",name:"Tanzania",isoCode:"TZ / TZA"},"thailand-flag":{code:"+66",name:"Thailand",isoCode:"TH / THA"},"east-timor-flag":{code:"+670",name:"East Timor",isoCode:"TL / TLS"},"togo-flag":{code:"+228",name:"Togo",isoCode:"TG / TGO"},"tokelau-flag":{code:"+690",name:"Tokelau",isoCode:"TK / TKL"},"tonga-flag":{code:"+676",name:"Tonga",isoCode:"TO / TON"},"trinidad-and-tobago-flag":{code:"+1-868",name:"Trinidad and Tobago",isoCode:"TT / TTO"},"tunisia-flag":{code:"+216",name:"Tunisia",isoCode:"TN / TUN"},"turkey-flag":{code:"+90",name:"Turkey",isoCode:"TR / TUR"},"turkmenistan-flag":{code:"+993",name:"Turkmenistan",isoCode:"TM / TKM"},"tuvalu-flag":{code:"+688",name:"Tuvalu",isoCode:"TV / TUV"},"uganda-flag":{code:"+256",name:"Uganda",isoCode:"UG / UGA"},"ukraine-flag":{code:"+380",name:"Ukraine",isoCode:"UA / UKR"},"uruguay-flag":{code:"+598",name:"Uruguay",isoCode:"UY / URY"},"uzbekistan-flag":{code:"+998",name:"Uzbekistan",isoCode:"UZ / UZB"},"vanuatu-flag":{code:"+678",name:"Vanuatu",isoCode:"VU / VUT"},"vatican-flag":{code:"+379",name:"Vatican City",isoCode:"VA / VAT"},"venezuela-flag":{code:"+58",name:"Venezuela",isoCode:"VE / VEN"},"vietnam-flag":{code:"+84",name:"Vietnam",isoCode:"VN / VNM"},"wallis-and-futuna-flag":{code:"+681",name:"Wallis and Futuna",isoCode:"WF / WLF"},"zambia-flag":{code:"+260",name:"Zambia",isoCode:"ZM / ZMB"},"zimbabwe-flag":{code:"+263",name:"Zimbabwe",isoCode:"ZW / ZWE"}};var t={"brazil-flag":{code:"+55",name:"Brasil",isoCode:"BR / BRA"},"united-states-flag":{code:"+1",name:"Estados Unidos",isoCode:"US / USA"},"afghanistan-flag":{code:"+93",name:"Afganistán",isoCode:"AF / AFG"},"south-africa-flag":{code:"+27",name:"Sudáfrica",isoCode:"ZA / ZAF"},"albania-flag":{code:"+355",name:"Albania",isoCode:"AL / ALB"},"germany-flag":{code:"+49",name:"Alemania",isoCode:"DE / DEU"},"andorra-flag":{code:"+376",name:"Andorra",isoCode:"AD / AND"},"angola-flag":{code:"+244",name:"Angola",isoCode:"AO / AGO"},"anguilla-flag":{code:"+1-264",name:"Anguilla",isoCode:"AI / AIA"},"antigua-and-barbuda-flag":{code:"+1-268",name:"Antigua y Barbuda",isoCode:"AG / ATG"},"netherlands-antilles-flag":{code:"+599",name:"Antillas Holandesas",isoCode:"AN / ANT"},"saudi-arabia-flag":{code:"+966",name:"Arabia Saudita",isoCode:"SA / SAU"},"algeria-flag":{code:"+213",name:"Argelia",isoCode:"DZ / DZA"},"argentina-flag":{code:"+54",name:"Argentina",isoCode:"AR / ARG"},"armenia-flag":{code:"+374",name:"Armenia",isoCode:"AM / ARM"},"aruba-flag":{code:"+297",name:"Aruba",isoCode:"AW / ABW"},"australia-flag":{code:"+61",name:"Australia",isoCode:"AU / AUS"},"austria-flag":{code:"+43",name:"Austria",isoCode:"AT / AUT"},"azerbaijan-flag":{code:"+994",name:"Azerbaiyán",isoCode:"AZ / AZE"},"bahamas-flag":{code:"+1-242",name:"Bahamas",isoCode:"BS / BHS"},"bahrain-flag":{code:"+973",name:"Baréin",isoCode:"BH / BHR"},"bangladesh-flag":{code:"+880",name:"Bangladesh",isoCode:"BD / BGD"},"barbados-flag":{code:"+1-246",name:"Barbados",isoCode:"BB / BRB"},"belgium-flag":{code:"+32",name:"Bélgica",isoCode:"BE / BEL"},"belize-flag":{code:"+501",name:"Belice",isoCode:"BZ / BLZ"},"benin-flag":{code:"+229",name:"Benín",isoCode:"BJ / BEN"},"bermuda-flag":{code:"+1-441",name:"Bermudas",isoCode:"BM / BMU"},"belarus-flag":{code:"+375",name:"Bielorrusia",isoCode:"BY / BLR"},"bolivia-flag":{code:"+591",name:"Bolivia",isoCode:"BO / BOL"},"bosnia-and-herzegovina-flag":{code:"+387",name:"Bosnia y Herzegovina",isoCode:"BA / BIH"},"botswana-flag":{code:"+267",name:"Botsuana",isoCode:"BW / BWA"},"british-indian-ocean-territory-flag":{code:"+246",name:"Territorio Británico del Océano Índico",isoCode:"IO / IOT"},"british-virgin-islands-flag":{code:"+1-284",name:"Islas Vírgenes Británicas",isoCode:"VG / VGB"},"brunei-flag":{code:"+673",name:"Brunéi",isoCode:"BN / BRN"},"bulgaria-flag":{code:"+359",name:"Bulgaria",isoCode:"BG / BGR"},"burkina-faso-flag":{code:"+226",name:"Burkina Faso",isoCode:"BF / BFA"},"burundi-flag":{code:"+257",name:"Burundi",isoCode:"BI / BDI"},"bhutan-flag":{code:"+975",name:"Bután",isoCode:"BT / BTN"},"cape-verde-flag":{code:"+238",name:"Cabo Verde",isoCode:"CV / CPV"},"cameroon-flag":{code:"+237",name:"Camerún",isoCode:"CM / CMR"},"cambodia-flag":{code:"+855",name:"Camboya",isoCode:"KH / KHM"},"canada-flag":{code:"+1",name:"Canadá",isoCode:"CA / CAN"},"qatar-flag":{code:"+974",name:"Catar",isoCode:"QA / QAT"},"kazakhstan-flag":{code:"+7",name:"Kazajistán",isoCode:"KZ / KAZ"},"chad-flag":{code:"+235",name:"Chad",isoCode:"TD / TCD"},"china-flag":{code:"+86",name:"China",isoCode:"CN / CHN"},"chile-flag":{code:"+56",name:"Chile",isoCode:"CL / CHL"},"chipre-flag":{code:"+357",name:"Chipre",isoCode:"CY / CYP"},"singapore-flag":{code:"+65",name:"Singapur",isoCode:"SG / SGP"},"colombia-flag":{code:"+57",name:"Colombia",isoCode:"CO / COL"},"comoros-flag":{code:"+269",name:"Comoras",isoCode:"KM / COM"},"north-korea-flag":{code:"+850",name:"Corea del Norte",isoCode:"KP / PRK"},"ivory-coast-flag":{code:"+225",name:"Costa de Marfil",isoCode:"CI / CIV"},"costa-rica-flag":{code:"+506",name:"Costa Rica",isoCode:"CR / CRI"},"croatia-flag":{code:"+385",name:"Croacia",isoCode:"HR / HRV"},"cuba-flag":{code:"+53",name:"Cuba",isoCode:"CU / CUB"},"curacao-flag":{code:"+599",name:"Curazao",isoCode:"CW / CUW"},"denmark-flag":{code:"+45",name:"Dinamarca",isoCode:"DK / DNK"},"djibouti-flag":{code:"+253",name:"Yibuti",isoCode:"DJ / DJI"},"dominica-flag":{code:"+1-767",name:"Dominica",isoCode:"DM / DMA"},"ecuador-flag":{code:"+593",name:"Ecuador",isoCode:"EC / ECU"},"egypt-flag":{code:"+20",name:"Egipto",isoCode:"EG / EGY"},"el-salvador-flag":{code:"+503",name:"El Salvador",isoCode:"SV / SLV"},"united-arab-emirates-flag":{code:"+971",name:"Emiratos Árabes Unidos",isoCode:"AE / ARE"},"eritrea-flag":{code:"+291",name:"Eritrea",isoCode:"ER / ERI"},"spain-flag":{code:"+34",name:"España",isoCode:"ES / ESP"},"slovakia-flag":{code:"+421",name:"Eslovaquia",isoCode:"SK / SVK"},"slovenia-flag":{code:"+386",name:"Eslovenia",isoCode:"SI / SVN"},"estonia-flag":{code:"+372",name:"Estonia",isoCode:"EE / EST"},"ethiopia-flag":{code:"+251",name:"Etiopía",isoCode:"ET / ETH"},"fiji-flag":{code:"+679",name:"Fiyi",isoCode:"FJ / FJI"},"philippines-flag":{code:"+63",name:"Filipinas",isoCode:"PH / PHL"},"finland-flag":{code:"+358",name:"Finlandia",isoCode:"FI / FIN"},"france-flag":{code:"+33",name:"Francia",isoCode:"FR / FRA"},"gabon-flag":{code:"+241",name:"Gabón",isoCode:"GA / GAB"},"gambia-flag":{code:"+220",name:"Gambia",isoCode:"GM / GMB"},"georgia-flag":{code:"+995",name:"Georgia",isoCode:"GE / GEO"},"ghana-flag":{code:"+233",name:"Ghana",isoCode:"GH / GHA"},"gibraltar-flag":{code:"+350",name:"Gibraltar",isoCode:"GI / GIB"},"grenada-flag":{code:"+1-473",name:"Granada",isoCode:"GD / GRD"},"greece-flag":{code:"+30",name:"Grecia",isoCode:"GR / GRC"},"greenland-flag":{code:"+299",name:"Groenlandia",isoCode:"GL / GRL"},"guam-flag":{code:"+1-671",name:"Guam",isoCode:"GU / GUM"},"guatemala-flag":{code:"+502",name:"Guatemala",isoCode:"GT / GTM"},"guernsey-flag":{code:"+44-1481",name:"Guernesey",isoCode:"GG / GGY"},"guinea-flag":{code:"+224",name:"Guinea",isoCode:"GN / GIN"},"guinea-bissau-flag":{code:"+245",name:"Guinea-Bisáu",isoCode:"GW / GNB"},"equatorial-guinea-flag":{code:"+240",name:"Guinea Ecuatorial",isoCode:"GQ / GNQ"},"guyana-flag":{code:"+592",name:"Guyana",isoCode:"GY / GUY"},"french-guyana-flag":{code:"+594",name:"Guayana Francesa",isoCode:"GQ / GNQ"},"haiti-flag":{code:"+509",name:"Haití",isoCode:"HT / HTI"},"netherlands-flag":{code:"+31",name:"Países Bajos",isoCode:"NL / NLD"},"honduras-flag":{code:"+504",name:"Honduras",isoCode:"HN / HND"},"hong-kong-flag":{code:"+852",name:"Hong Kong",isoCode:"HK / HKG"},"hungary-flag":{code:"+36",name:"Hungría",isoCode:"HU / HUN"},"iceland-flag":{code:"+354",name:"Islandia",isoCode:"IS / ISL"},"yemen-flag":{code:"+967",name:"Yemen",isoCode:"YE / YEM"},"cayman-islands-flag":{code:"+1-345",name:"Islas Caimán",isoCode:"KY / CYM"},"isle-of-man-flag":{code:"+44-1624",name:"Isla de Man",isoCode:"IM / IMN"},"christmas-island-flag":{code:"+61",name:"Isla de Navidad",isoCode:"CX / CXR"},"cook-islands-flag":{code:"+682",name:"Islas Cook",isoCode:"CK / COK"},"falkland-islands-flag":{code:"+500",name:"Islas Malvinas",isoCode:"FK / FLK"},"faroe-islands-flag":{code:"+298",name:"Islas Feroe",isoCode:"FO / FRO"},"marshall-islands-flag":{code:"+692",name:"Islas Marshall",isoCode:"MH / MHL"},"northern-mariana-islands-flag":{code:"+1-670",name:"Islas Marianas del Norte",isoCode:"MP / MNP"},"solomon-islands-flag":{code:"+677",name:"Islas Salomón",isoCode:"SB / SLB"},"turks-and-caicos-islands-flag":{code:"+1-649",name:"Islas Turcas y Caicos",isoCode:"TC / TCA"},"u.s.-virgin-islands-flag":{code:"+1-340",name:"Islas Vírgenes de EE. UU.",isoCode:"VI / VIR"},"india-flag":{code:"+91",name:"India",isoCode:"IN / IND"},"indonesia-flag":{code:"+62",name:"Indonesia",isoCode:"ID / IDN"},"iran-flag":{code:"+98",name:"Irán",isoCode:"IR / IRN"},"iraq-flag":{code:"+964",name:"Irak",isoCode:"IQ / IRQ"},"ireland-flag":{code:"+353",name:"Irlanda",isoCode:"IE / IRL"},"israel-flag":{code:"+972",name:"Israel",isoCode:"IL / ISR"},"italy-flag":{code:"+39",name:"Italia",isoCode:"IT / ITA"},"jamaica-flag":{code:"+1-876",name:"Jamaica",isoCode:"JM / JAM"},"japan-flag":{code:"+81",name:"Japón",isoCode:"JP / JPN"},"jersey-flag":{code:"+44-1534",name:"Jersey",isoCode:"JE / JEY"},"jordan-flag":{code:"+962",name:"Jordania",isoCode:"JO / JOR"},"kiribati-flag":{code:"+686",name:"Kiribati",isoCode:"KI / KIR"},"kosovo-flag":{code:"+383",name:"Kosovo",isoCode:"XK / XKX"},"kuwait-flag":{code:"+965",name:"Kuwait",isoCode:"KW / KWT"},"laos-flag":{code:"+856",name:"Laos",isoCode:"LA / LAO"},"latvia-flag":{code:"+371",name:"Letonia",isoCode:"LV / LVA"},"lebanon-flag":{code:"+961",name:"Líbano",isoCode:"LB / LBN"},"lesotho-flag":{code:"+266",name:"Lesoto",isoCode:"LS / LSO"},"liberia-flag":{code:"+231",name:"Liberia",isoCode:"LR / LBR"},"libya-flag":{code:"+218",name:"Libia",isoCode:"LY / LBY"},"liechtenstein-flag":{code:"+423",name:"Liechtenstein",isoCode:"LI / LIE"},"lithuania-flag":{code:"+370",name:"Lituania",isoCode:"LT / LTU"},"luxembourg-flag":{code:"+352",name:"Luxemburgo",isoCode:"LU / LUX"},"macau-flag":{code:"+853",name:"Macao",isoCode:"MO / MAC"},"macedonia-flag":{code:"+389",name:"Macedonia",isoCode:"MK / MKD"},"madagascar-flag":{code:"+261",name:"Madagascar",isoCode:"MG / MDG"},"malawi-flag":{code:"+265",name:"Malaui",isoCode:"MW / MWI"},"malaysia-flag":{code:"+60",name:"Malasia",isoCode:"MY / MYS"},"maldives-flag":{code:"+960",name:"Maldivas",isoCode:"MV / MDV"},"mali-flag":{code:"+223",name:"Mali",isoCode:"ML / MLI"},"malta-flag":{code:"+356",name:"Malta",isoCode:"MT / MLT"},"martinique-flag":{code:"+596",name:"Martinica",isoCode:"MQ / MTQ"},"mauritania-flag":{code:"+222",name:"Mauritania",isoCode:"MR / MRT"},"mauritius-flag":{code:"+230",name:"Mauricio",isoCode:"MU / MUS"},"mayotte-flag":{code:"+262",name:"Mayotte",isoCode:"YT / MYT"},"mexico-flag":{code:"+52",name:"México",isoCode:"MX / MEX"},"micronesia-flag":{code:"+691",name:"Micronesia",isoCode:"FM / FSM"},"moldova-flag":{code:"+373",name:"Moldavia",isoCode:"MD / MDA"},"monaco-flag":{code:"+377",name:"Mónaco",isoCode:"MC / MCO"},"mongolia-flag":{code:"+976",name:"Mongolia",isoCode:"MN / MNG"},"montenegro-flag":{code:"+382",name:"Montenegro",isoCode:"ME / MNE"},"montserrat-flag":{code:"+1-664",name:"Montserrat",isoCode:"MS / MSR"},"morocco-flag":{code:"+212",name:"Marruecos",isoCode:"MA / MAR"},"mozambique-flag":{code:"+258",name:"Mozambique",isoCode:"MZ / MOZ"},"myanmar-flag":{code:"+95",name:"Myanmar",isoCode:"MM / MMR"},"namibia-flag":{code:"+264",name:"Namibia",isoCode:"NA / NAM"},"nauru-flag":{code:"+674",name:"Nauru",isoCode:"NR / NRU"},"nepal-flag":{code:"+977",name:"Nepal",isoCode:"NP / NPL"},"nicaragua-flag":{code:"+505",name:"Nicaragua",isoCode:"NI / NIC"},"niger-flag":{code:"+227",name:"Níger",isoCode:"NE / NER"},"nigeria-flag":{code:"+234",name:"Nigeria",isoCode:"NG / NGA"},"niue-flag":{code:"+683",name:"Niue",isoCode:"NU / NIU"},"norway-flag":{code:"+47",name:"Noruega",isoCode:"NO / NOR"},"new-caledonia-flag":{code:"+687",name:"Nueva Caledonia",isoCode:"NC / NCL"},"new-zealand-flag":{code:"+64",name:"Nueva Zelanda",isoCode:"NZ / NZL"},"oman-flag":{code:"+968",name:"Omán",isoCode:"OM / OMN"},"pakistan-flag":{code:"+92",name:"Pakistán",isoCode:"PK / PAK"},"palau-flag":{code:"+680",name:"Palau",isoCode:"PW / PLW"},"palestine-flag":{code:"+970",name:"Palestina",isoCode:"PS / PSE"},"panama-flag":{code:"+507",name:"Panamá",isoCode:"PA / PAN"},"papua-new-guinea-flag":{code:"+675",name:"Papúa Nueva Guinea",isoCode:"PG / PNG"},"paraguay-flag":{code:"+595",name:"Paraguay",isoCode:"PY / PRY"},"peru-flag":{code:"+51",name:"Perú",isoCode:"PE / PER"},"pitcairn-flag":{code:"+64",name:"Pitcairn",isoCode:"PN / PCN"},"poland-flag":{code:"+48",name:"Polonia",isoCode:"PL / POL"},"french-polynesia-flag":{code:"+689",name:"Polinesia Francesa",isoCode:"PF / PYF"},"portugal-flag":{code:"+351",name:"Portugal",isoCode:"PT / PRT"},"puerto-rico-flag":{code:"+1-787",name:"Puerto Rico",isoCode:"PR / PRI"},"kenya-flag":{code:"+254",name:"Kenia",isoCode:"KE / KEN"},"kyrgyzstan-flag":{code:"+996",name:"Kirguistán",isoCode:"KG / KGZ"},"central-african-republic-flag":{code:"+236",name:"República Centroafricana",isoCode:"CF / CAF"},"czech-republic-flag":{code:"+420",name:"República Checa",isoCode:"CZ / CZE"},"dominican-republic-flag":{code:"+1-809",name:"República Dominicana",isoCode:"DO / DOM"},"united-kingdom-flag":{code:"+44",name:"Reino Unido",isoCode:"GB / GBR"},"republic-of-the-congo-flag":{code:"+242",name:"República del Congo",isoCode:"CG / COG"},"democratic-republic-of-congo-flag":{code:"+243",name:"República Democrática del Congo",isoCode:"CD / COD"},"reunion-flag":{code:"+262",name:"Reunión",isoCode:"RE / REU"},"romania-flag":{code:"+40",name:"Rumania",isoCode:"RO / ROU"},"russia-flag":{code:"+7",name:"Rusia",isoCode:"RU / RUS"},"rwanda-flag":{code:"+250",name:"Ruanda",isoCode:"RW / RWA"},"western-sahara-flag":{code:"+212",name:"Sahara Occidental",isoCode:"EH / ESH"},"saint-barthelemy-flag":{code:"+590",name:"San Bartolomé",isoCode:"BL / BLM"},"saint-helena-flag":{code:"+290",name:"Santa Helena",isoCode:"SH / SHN"},"saint-kitts-and-nevis-flag":{code:"+1-869",name:"San Cristóbal y Nieves",isoCode:"KN / KNA"},"saint-lucia-flag":{code:"+1-758",name:"Santa Lucía",isoCode:"LC / LCA"},"saint-martin-flag":{code:"+590",name:"San Martín",isoCode:"MF / MAF"},"saint-pierre-and-miquelon-flag":{code:"+508",name:"San Pedro y Miquelón",isoCode:"PM / SPM"},"saint-vincent-and-the-grenadines-flag":{code:"+1-784",name:"San Vicente y las Granadinas",isoCode:"VC / VCT"},"samoa-flag":{code:"+685",name:"Samoa",isoCode:"WS / WSM"},"american-samoa-flag":{code:"+1-684",name:"Samoa Americana",isoCode:"AS / ASM"},"san-marino-flag":{code:"+378",name:"San Marino",isoCode:"SM / SMR"},"sao-tome-and-principe-flag":{code:"+239",name:"Santo Tomé y Príncipe",isoCode:"ST / STP"},"senegal-flag":{code:"+221",name:"Senegal",isoCode:"SN / SEN"},"serbia-flag":{code:"+381",name:"Serbia",isoCode:"RS / SRB"},"seychelles-flag":{code:"+248",name:"Seychelles",isoCode:"SC / SYC"},"sierra-leone-flag":{code:"+232",name:"Sierra Leona",isoCode:"SL / SLE"},"sint-maarten-flag":{code:"+1-721",name:"Sint Maarten",isoCode:"SX / SXM"},"somalia-flag":{code:"+252",name:"Somalia",isoCode:"SO / SOM"},"south-korea-flag":{code:"+82",name:"Corea del Sur",isoCode:"KR / KOR"},"south-sudan-flag":{code:"+211",name:"Sudán del Sur",isoCode:"SS / SSD"},"sri-lanka-flag":{code:"+94",name:"Sri Lanka",isoCode:"LK / LKA"},"sudan-flag":{code:"+249",name:"Sudán",isoCode:"SD / SDN"},"suriname-flag":{code:"+597",name:"Surinam",isoCode:"SR / SUR"},"svalbard-and-jan-mayen-flag":{code:"+47",name:"Svalbard y Jan Mayen",isoCode:"SJ / SJM"},"swaziland-flag":{code:"+268",name:"Suazilandia",isoCode:"SZ / SWZ"},"sweden-flag":{code:"+46",name:"Suecia",isoCode:"SE / SWE"},"switzerland-flag":{code:"+41",name:"Suiza",isoCode:"CH / CHE"},"syria-flag":{code:"+963",name:"Siria",isoCode:"SY / SYR"},"taiwan-flag":{code:"+886",name:"Taiwán",isoCode:"TW / TWN"},"tajikistan-flag":{code:"+992",name:"Tayikistán",isoCode:"TJ / TJK"},"tanzania-flag":{code:"+255",name:"Tanzania",isoCode:"TZ / TZA"},"thailand-flag":{code:"+66",name:"Tailandia",isoCode:"TH / THA"},"east-timor-flag":{code:"+670",name:"Timor Oriental",isoCode:"TL / TLS"},"togo-flag":{code:"+228",name:"Togo",isoCode:"TG / TGO"},"tokelau-flag":{code:"+690",name:"Tokelau",isoCode:"TK / TKL"},"tonga-flag":{code:"+676",name:"Tonga",isoCode:"TO / TON"},"trinidad-and-tobago-flag":{code:"+1-868",name:"Trinidad y Tobago",isoCode:"TT / TTO"},"tunisia-flag":{code:"+216",name:"Túnez",isoCode:"TN / TUN"},"turkey-flag":{code:"+90",name:"Turquía",isoCode:"TR / TUR"},"turkmenistan-flag":{code:"+993",name:"Turkmenistán",isoCode:"TM / TKM"},"tuvalu-flag":{code:"+688",name:"Tuvalu",isoCode:"TV / TUV"},"uganda-flag":{code:"+256",name:"Uganda",isoCode:"UG / UGA"},"ukraine-flag":{code:"+380",name:"Ucrania",isoCode:"UA / UKR"},"uruguay-flag":{code:"+598",name:"Uruguay",isoCode:"UY / URY"},"uzbekistan-flag":{code:"+998",name:"Uzbekistán",isoCode:"UZ / UZB"},"vanuatu-flag":{code:"+678",name:"Vanuatu",isoCode:"VU / VUT"},"vatican-flag":{code:"+379",name:"Vaticano",isoCode:"VA / VAT"},"venezuela-flag":{code:"+58",name:"Venezuela",isoCode:"VE / VEN"},"vietnam-flag":{code:"+84",name:"Vietnam",isoCode:"VN / VNM"},"wallis-and-futuna-flag":{code:"+681",name:"Wallis y Futuna",isoCode:"WF / WLF"},"zambia-flag":{code:"+260",name:"Zambia",isoCode:"ZM / ZMB"},"zimbabwe-flag":{code:"+263",name:"Zimbabue",isoCode:"ZW / ZWE"}};var m=':host{display:-ms-flexbox;display:flex;border-radius:8px;position:relative;outline:none;width:100%;min-width:200px}.element_input{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.element_input input{-webkit-box-shadow:inherit;box-shadow:inherit}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary .input__container{padding:4px 8px 9px}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40));-ms-flex-positive:1;flex-grow:1}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger .input__container{padding:4px 8px 9px}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40));-ms-flex-positive:1;flex-grow:1}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success .input__container{padding:4px 8px 9px}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40));-ms-flex-positive:1;flex-grow:1}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:0}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-disable, rgb(89, 89, 89));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.input__icon{position:relative;height:100%;color:var(--color-content-disable, rgb(89, 89, 89));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:space-evenly;justify-content:space-evenly;padding-right:16px;padding-left:12px;cursor:pointer}.input__icon bds-icon{position:relative}.input__icon bds-icon:first-child{margin-right:8px}.input__icon::before{content:"";background:transparent;height:calc(100% - 2px);max-height:54px;width:70px;position:absolute;left:1px;top:1px;border-radius:8px 0px 0px 8px}.input__icon::after{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px;pointer-events:none}.input__icon:focus-visible{outline:none}.input__icon:focus-visible::after{border-color:var(--color-focus, rgb(194, 38, 251))}.input__country-code{color:var(--color-content-disable, rgb(89, 89, 89));padding-right:5px}.input:hover .input__icon::before,.input--pressed .input__icon::before{background:var(--color-surface-2, rgb(237, 237, 237))}.select-phone-number__options{background:var(--color-surface-2, rgb(237, 237, 237));width:100%;max-height:200px;position:absolute;top:99%;left:0;border-radius:8px;-webkit-box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));overflow-y:auto;overflow-x:hidden;z-index:2;margin-top:4px;-webkit-transition:opacity 0.75s, visibility 0.75s, -webkit-transform 0.25s;transition:opacity 0.75s, visibility 0.75s, -webkit-transform 0.25s;transition:transform 0.25s, opacity 0.75s, visibility 0.75s;transition:transform 0.25s, opacity 0.75s, visibility 0.75s, -webkit-transform 0.25s;-webkit-transform-origin:top left;transform-origin:top left;-webkit-transform:scaleY(0);transform:scaleY(0);opacity:0}.select-phone-number__options::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.select-phone-number__options::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.select-phone-number__options--open{visibility:visible;-webkit-transform:scale(1);transform:scale(1);opacity:1}';var g=a("bds_input_phone_number",function(){function a(a){var i=this;e(this,a);this.bdsPhoneNumberChange=o(this,"bdsPhoneNumberChange");this.bdsInput=o(this,"bdsInput");this.bdsCancel=o(this,"bdsCancel");this.bdsFocus=o(this,"bdsFocus");this.bdsBlur=o(this,"bdsBlur");this.isOpen=false;this.validationDanger=false;this.validationMesage="";this.isPressed=false;this.options=[];this.text="";this.value="+55";this.danger=false;this.success=false;this.disabled=false;this.helperMessage="";this.errorMessage="";this.successMessage="";this.dataTest=null;this.dtSelectFlag=null;this.label="Phone number";this.icon="";this.language="pt_BR";this.countries={};this.refNativeInput=function(a){i.nativeInput=a};this.onClickWrapper=function(){i.onFocus();if(i.nativeInput){i.nativeInput.focus()}};this.onFocus=function(){i.bdsFocus.emit();i.isPressed=true};this.onBlur=function(){i.bdsBlur.emit();i.isPressed=false};this.changedInputValue=function(a){return __awaiter(i,void 0,void 0,(function(){var e;return __generator(this,(function(o){e=a.target;this.checkValidity();if(e){this.text=e.value||"";this.numberValidation()}this.bdsInput.emit(a);return[2]}))}))};this.toggle=function(){if(!i.disabled){i.isOpen=!i.isOpen}};this.handler=function(a){var e=a.detail.value;i.value=e.code;i.selectedCountry=e.flag;i.isoCode=e.isoCode;i.bdsPhoneNumberChange.emit({value:i.text,code:i.value,isoCode:i.isoCode,country:i.selectedCountry});i.toggle()};this.keyPressWrapper=function(a){var e=a.target.localName==="bds-select";var o=a.target.localName==="input";if(a.key==="Enter"&&!i.isOpen&&(e||o)){i.toggle()}}}a.prototype.removeFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(a){this.onBlur();return[2]}))}))};a.prototype.valueChanged=function(){for(var a=0,e=this.childOptions;a<e.length;a++){var o=e[a];o.selected=this.value===o.value}};a.prototype.handleWindow=function(a){if(!this.el.contains(a.target)){this.isOpen=false}};a.prototype.languageChanged=function(){this.updateCountries()};a.prototype.updateCountries=function(){var a=this;switch(this.language){case"pt_BR":this.countries=c;break;case"en_US":this.countries=r;break;case"es_ES":this.countries=t;break;default:this.countries=l;break}var e=Object.keys(this.countries);var o=Object.values(this.countries).findIndex((function(e){return e.code===a.value}));if(o!==-1){this.selectedCountry=e[o]}else{this.selectedCountry=this.selectedCountry||e[0]}this.isoCode=this.isoCode||e[0]};a.prototype.componentWillRender=function(){this.updateCountries()};Object.defineProperty(a.prototype,"childOptions",{get:function(){return Array.from(this.el.querySelectorAll("bds-select-option"))},enumerable:false,configurable:true});a.prototype.handleInputChange=function(){this.bdsPhoneNumberChange.emit({value:this.text,code:this.value,isoCode:this.isoCode,country:this.selectedCountry})};a.prototype.numberValidation=function(){if(s(this.nativeInput.value)){this.validationMesage=this.numberErrorMessage;this.validationDanger=true}else{this.validationDanger=false}};a.prototype.handleKeyDown=function(a){if(a.key=="Enter"){this.toggle()}};a.prototype.changeCountry=function(a,e,o){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){this.value=a;this.selectedCountry=o;this.isoCode=e;this.bdsPhoneNumberChange.emit({value:this.text,code:this.value,isoCode:this.isoCode,country:this.selectedCountry});return[2]}))}))};a.prototype.checkValidity=function(){if(this.nativeInput.validity.valid){this.validationDanger=false}};a.prototype.renderIcon=function(){return this.icon&&i("div",{class:{input__icon:true,"input__icon--large":!!this.label}},i("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))};a.prototype.renderLabel=function(){return this.label&&i("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},i("bds-typo",{variant:"fs-12",bold:"bold"},this.label))};a.prototype.renderMessage=function(){var a=this.danger?"error":this.success?"checkball":"info";var e=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!e&&this.validationDanger)e=this.validationMesage;var o=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";return e?i("div",{class:o,part:"input__message"},i("div",{class:"input__message__icon"},i("bds-icon",{size:"x-small",name:a,theme:"outline",color:"inherit"})),i("bds-typo",{class:"input__message__text",variant:"fs-12"},e)):null};a.prototype.render=function(){var a=this;var e=this.isPressed&&!this.disabled;var o=this.isOpen?"arrow-up":"arrow-down";var d=Object.keys(this.countries);return i(n,{key:"544475022847584bfd0251606b7055c3c37ed4bf","aria-disabled":this.disabled?"true":null},i("div",{key:"5c9890fdc79f2300efc90e679a652b7db93d0f46",class:{element_input:true},"aria-disabled":this.disabled?"true":null},i("div",{key:"95c40d8489f5a17058a6ec0d623e6ce9b70097ed",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":e},onClick:this.onClickWrapper,onKeyDown:this.keyPressWrapper,part:"input-container"},this.renderIcon(),i("div",{key:"c6b26a34caa587bf12290703ce9c4f1c2e2c8902",onClick:this.toggle,onKeyDown:this.handleKeyDown.bind(this),"data-test":this.dtSelectFlag,class:"input__icon",tabindex:"0"},i("bds-icon",{key:"f7d0db71ea096d6e2fb0bc3cae44d688775f4ea4",size:"medium",theme:"solid",name:this.selectedCountry,color:"primary"}),i("bds-icon",{key:"717fb5a6c9ddf56e7fb9fe2f3a83c4354e8263ea",size:"x-small",name:o})),i("div",{key:"91211b1e0bb13f43886ebc2d5d0d50033015bdf4",class:"input__container"},this.renderLabel(),i("div",{key:"6106bf6fe58c2793694f7f39ae9bb097c9d75277",class:{input__container__wrapper:true}},i("div",{key:"89b22af55d604c26d36a4342e198217914f8bdd5",class:"input__container__country-code"},i("bds-typo",{key:"a6966011b48fa9799388ee6c8a96192edb111de5","no-wrap":"true",variant:"fs-14"},this.value)),i("input",{key:"cf76d226e790edd48b51ae86ce3a91c3a616eb35",class:{input__container__text:true},type:"phonenumber",required:this.required,pattern:"/^(\\(?\\+?[0-9]*\\)?)?[0-9_\\- \\(\\)]*$/",ref:this.refNativeInput,onInput:this.changedInputValue,onFocus:this.onFocus,onBlur:this.onBlur,value:this.text,disabled:this.disabled,"data-test":this.dataTest,maxlength:this.value==="+55"?25:null}))),this.success&&i("bds-icon",{key:"8383ae9fdb2cca9e19ef2f969dc177d2eb8536e2",class:"icon-success",name:"check",theme:"outline",size:"xxx-small"}),i("slot",{key:"aca9d5f821c73d57921952093608d00ea89652f5",name:"input-right"})),this.renderMessage()),i("div",{key:"d4f007c25ccfad5242b530504ca291b23dee5ae5",class:{"select-phone-number__options":true,"select-phone-number__options--open":this.isOpen}},this.isOpen&&d.map((function(e){return i("bds-select-option",{key:e,onOptionSelected:a.handler,selected:e===a.selectedCountry,value:{code:a.countries[e].code,isoCode:a.countries[e].isoCode,flag:e},status:a.countries[e].isoCode},a.countries[e].name," ",a.countries[e].code)}))))};Object.defineProperty(a.prototype,"el",{get:function(){return d(this)},enumerable:false,configurable:true});Object.defineProperty(a,"watchers",{get:function(){return{value:["valueChanged"],language:["languageChanged"],text:["handleInputChange"]}},enumerable:false,configurable:true});return a}());g.style=m}}}));
//# sourceMappingURL=p-84545e05.system.entry.js.map