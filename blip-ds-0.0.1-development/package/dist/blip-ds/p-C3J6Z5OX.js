const e="blip-ds";const t={hydratedSelectorName:"hydrated",lazyLoad:true,slotRelocation:true,updatable:true};const n=()=>{};const r="";var i=Object.defineProperty;var o=(e,t)=>{for(var n in t)i(e,n,{get:t[n],enumerable:true})};var s=e=>{if(e.__stencil__getHostRef){return e.__stencil__getHostRef()}return void 0};var l=(e,t)=>{e.__stencil__getHostRef=()=>t;t.t=e};var f=(e,t)=>{const n={i:0,$hostElement$:e,o:t,l:new Map};{n.u=new Promise((e=>n.v=e))}{n.h=new Promise((e=>n.p=e));e["s-p"]=[];e["s-rc"]=[]}const r=n;e.__stencil__getHostRef=()=>r;return r};var a=(e,t)=>t in e;var c=(e,t)=>(0,console.error)(e,t);var u=new Map;var v=(e,t,n)=>{const r=e.m.replace(/-/g,"_");const i=e.$;if(!i){return void 0}const o=u.get(i);if(o){return o[r]}
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/return import(`./${i}.entry.js${""}`).then((e=>{{u.set(i,e)}return e[r]}),(e=>{c(e,t.$hostElement$)}))};var d=new Map;var h="{visibility:hidden}.hydrated{visibility:inherit}";var p="slot-fb{display:contents}slot-fb[hidden]{display:none}";var m="http://www.w3.org/1999/xlink";var y=typeof window!=="undefined"?window:{};var b={i:0,S:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,r)=>e.addEventListener(t,n,r),rel:(e,t,n,r)=>e.removeEventListener(t,n,r),ce:(e,t)=>new CustomEvent(e,t)};var w=(()=>{var e;let t=false;try{(e=y.document)==null?void 0:e.addEventListener("e",null,Object.defineProperty({},"passive",{get(){t=true}}))}catch(e){}return t})();var g=e=>Promise.resolve(e);var $=(()=>{try{new CSSStyleSheet;return typeof(new CSSStyleSheet).replaceSync==="function"}catch(e){}return false})();var S=false;var j=[];var O=[];var N=(e,t)=>n=>{e.push(n);if(!S){S=true;if(t&&b.i&4){E(k)}else{b.raf(k)}}};var C=e=>{for(let t=0;t<e.length;t++){try{e[t](performance.now())}catch(e){c(e)}}e.length=0};var k=()=>{C(j);{C(O);if(S=j.length>0){b.raf(k)}}};var E=e=>g().then(e);var x=N(O,true);var M=e=>e!=null&&e!==void 0;var R=e=>{e=typeof e;return e==="object"||e==="function"};function P(e){var t,n,r;return(r=(n=(t=e.head)==null?void 0:t.querySelector('meta[name="csp-nonce"]'))==null?void 0:n.getAttribute("content"))!=null?r:void 0}var T=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");var L={};o(L,{err:()=>A,map:()=>F,ok:()=>U,unwrap:()=>_,unwrapErr:()=>D});var U=e=>({isOk:true,isErr:false,value:e});var A=e=>({isOk:false,isErr:true,value:e});function F(e,t){if(e.isOk){const n=t(e.value);if(n instanceof Promise){return n.then((e=>U(e)))}else{return U(n)}}if(e.isErr){const t=e.value;return A(t)}throw"should never get here"}var _=e=>{if(e.isOk){return e.value}else{throw e.value}};var D=e=>{if(e.isErr){return e.value}else{throw e.value}};function W(e){const t=this.attachShadow({mode:"open"});if($){const e=new CSSStyleSheet;e.replaceSync(r);t.adoptedStyleSheets.push(e)}}var B=e=>{const t=ne(e,"childNodes");if(e.tagName&&e.tagName.includes("-")&&e["s-cr"]&&e.tagName!=="SLOT-FB"){z(t,e.tagName).forEach((e=>{if(e.nodeType===1&&e.tagName==="SLOT-FB"){if(I(e,q(e),false).length){e.hidden=true}else{e.hidden=false}}}))}let n=0;for(n=0;n<t.length;n++){const e=t[n];if(e.nodeType===1&&ne(e,"childNodes").length){B(e)}}};var H=e=>{const t=[];for(let n=0;n<e.length;n++){const r=e[n]["s-nr"]||void 0;if(r&&r.isConnected){t.push(r)}}return t};function z(e,t,n){let r=0;let i=[];let o;for(;r<e.length;r++){o=e[r];if(o["s-sr"]&&(!t||o["s-hn"]===t)&&(n===void 0||q(o)===n)){i.push(o);if(typeof n!=="undefined")return i}i=[...i,...z(o.childNodes,t,n)]}return i}var I=(e,t,n=true)=>{const r=[];if(n&&e["s-sr"]||!e["s-sr"])r.push(e);let i=e;while(i=i.nextSibling){if(q(i)===t&&(n||!i["s-sr"]))r.push(i)}return r};var V=(e,t)=>{if(e.nodeType===1){if(e.getAttribute("slot")===null&&t===""){return true}if(e.getAttribute("slot")===t){return true}return false}if(e["s-sn"]===t){return true}return t===""};var Y=(e,t,n,r)=>{if(e["s-ol"]&&e["s-ol"].isConnected){return}const i=document.createTextNode("");i["s-nr"]=e;if(!t["s-cr"]||!t["s-cr"].parentNode)return;const o=t["s-cr"].parentNode;const s=ne(o,"appendChild");{s.call(o,i)}e["s-ol"]=i;e["s-sh"]=t["s-hn"]};var q=e=>typeof e["s-sn"]==="string"?e["s-sn"]:e.nodeType===1&&e.getAttribute("slot")||void 0;function G(e){if(e.assignedElements||e.assignedNodes||!e["s-sr"])return;const t=t=>function(e){const n=[];const r=this["s-sn"];if(e==null?void 0:e.flatten){console.error(`\n          Flattening is not supported for Stencil non-shadow slots.\n          You can use \`.childNodes\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `)}const i=this["s-cr"].parentElement;const o=i.__childNodes?i.childNodes:H(i.childNodes);o.forEach((e=>{if(r===q(e)){n.push(e)}}));if(t){return n.filter((e=>e.nodeType===1))}return n}.bind(e);e.assignedElements=t(true);e.assignedNodes=t(false)}function J(e){e.dispatchEvent(new CustomEvent("slotchange",{bubbles:false,cancelable:false,composed:false}))}function K(e,t){var n;t=t||((n=e["s-ol"])==null?void 0:n.parentElement);if(!t)return{slotNode:null,slotName:""};const r=e["s-sn"]=q(e)||"";const i=ne(t,"childNodes");const o=z(i,t.tagName,r)[0];return{slotNode:o,slotName:r}}var Q=e=>{e.__appendChild=e.appendChild;e.appendChild=function(e){const{slotName:t,slotNode:n}=K(e,this);if(n){Y(e,n);const r=I(n,t);const i=r[r.length-1];const o=ne(i,"parentNode");const s=ne(o,"insertBefore")(e,i.nextSibling);J(n);B(this);return s}return this.__appendChild(e)}};var X=e=>{class t extends Array{item(e){return this[e]}}te("children",e);Object.defineProperty(e,"children",{get(){return this.childNodes.filter((e=>e.nodeType===1))}});Object.defineProperty(e,"childElementCount",{get(){return this.children.length}});te("firstChild",e);Object.defineProperty(e,"firstChild",{get(){return this.childNodes[0]}});te("lastChild",e);Object.defineProperty(e,"lastChild",{get(){return this.childNodes[this.childNodes.length-1]}});te("childNodes",e);Object.defineProperty(e,"childNodes",{get(){const e=new t;e.push(...H(this.__childNodes));return e}})};var Z=["children","nextElementSibling","previousElementSibling"];var ee=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function te(e,t){let n;if(Z.includes(e)){n=Object.getOwnPropertyDescriptor(Element.prototype,e)}else if(ee.includes(e)){n=Object.getOwnPropertyDescriptor(Node.prototype,e)}if(!n){n=Object.getOwnPropertyDescriptor(t,e)}if(n)Object.defineProperty(t,"__"+e,n)}function ne(e,t){if("__"+t in e){const n=e["__"+t];if(typeof n!=="function")return n;return n.bind(e)}else{if(typeof e[t]!=="function")return e[t];return e[t].bind(e)}}var re=(e,t="")=>{{return()=>{}}};var ie=(e,t)=>{{return()=>{}}};var oe=(e,t,...n)=>{let r=null;let i=null;let o=null;let s=false;let l=false;const f=[];const a=t=>{for(let n=0;n<t.length;n++){r=t[n];if(Array.isArray(r)){a(r)}else if(r!=null&&typeof r!=="boolean"){if(s=typeof e!=="function"&&!R(r)){r=String(r)}if(s&&l){f[f.length-1].j+=r}else{f.push(s?se(null,r):r)}l=s}}};a(n);if(t){if(t.key){i=t.key}if(t.name){o=t.name}{const e=t.className||t.class;if(e){t.class=typeof e!=="object"?e:Object.keys(e).filter((t=>e[t])).join(" ")}}}if(typeof e==="function"){return e(t===null?{}:t,f,ae)}const c=se(e,null);c.O=t;if(f.length>0){c.N=f}{c.C=i}{c.k=o}return c};var se=(e,t)=>{const n={i:0,M:e,j:t,R:null,N:null};{n.O=null}{n.C=null}{n.k=null}return n};var le={};var fe=e=>e&&e.M===le;var ae={forEach:(e,t)=>e.map(ce).forEach(t),map:(e,t)=>e.map(ce).map(t).map(ue)};var ce=e=>({vattrs:e.O,vchildren:e.N,vkey:e.C,vname:e.k,vtag:e.M,vtext:e.j});var ue=e=>{if(typeof e.vtag==="function"){const t={...e.vattrs};if(e.vkey){t.key=e.vkey}if(e.vname){t.name=e.vname}return oe(e.vtag,t,...e.vchildren||[])}const t=se(e.vtag,e.vtext);t.O=e.vattrs;t.N=e.vchildren;t.C=e.vkey;t.k=e.vname;return t};var ve=e=>{const t=T(e);return new RegExp(`(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?${t}))(${t}\\b)`,"g")};ve("::slotted");ve(":host");ve(":host-context");var de=(e,t,n)=>{if(e!=null&&!R(e)){if(t&4){{return e==="false"?false:e===""||!!e}}if(t&2){return typeof e==="string"?parseFloat(e):typeof e==="number"?e:NaN}if(t&1){return String(e)}return e}return e};var he=e=>s(e).$hostElement$;var pe=(e,t,n)=>{const r=he(e);return{emit:e=>me(r,t,{bubbles:true,composed:true,cancelable:true,detail:e})}};var me=(e,t,n)=>{const r=b.ce(t,n);e.dispatchEvent(r);return r};var ye=new WeakMap;var be=(e,t,n)=>{let r=d.get(e);if($&&n){r=r||new CSSStyleSheet;if(typeof r==="string"){r=t}else{r.replaceSync(t)}}else{r=t}d.set(e,r)};var we=(e,t,n)=>{var r;const i=$e(t);const o=d.get(i);if(!y.document){return i}e=e.nodeType===11?e:y.document;if(o){if(typeof o==="string"){e=e.head||e;let n=ye.get(e);let s;if(!n){ye.set(e,n=new Set)}if(!n.has(i)){{s=y.document.createElement("style");s.innerHTML=o;const n=(r=b.P)!=null?r:P(y.document);if(n!=null){s.setAttribute("nonce",n)}if(!(t.i&1)){if(e.nodeName==="HEAD"){const t=e.querySelectorAll("link[rel=preconnect]");const n=t.length>0?t[t.length-1].nextSibling:e.querySelector("style");e.insertBefore(s,(n==null?void 0:n.parentNode)===e?n:null)}else if("host"in e){if($){const t=new CSSStyleSheet;t.replaceSync(o);e.adoptedStyleSheets=[t,...e.adoptedStyleSheets]}else{const t=e.querySelector("style");if(t){t.innerHTML=o+t.innerHTML}else{e.prepend(s)}}}else{e.append(s)}}if(t.i&1){e.insertBefore(s,null)}}if(t.i&4){s.innerHTML+=p}if(n){n.add(i)}}}else if(!e.adoptedStyleSheets.includes(o)){e.adoptedStyleSheets=[...e.adoptedStyleSheets,o]}}return i};var ge=e=>{const t=e.o;const n=e.$hostElement$;const r=t.i;const i=re("attachStyles",t.m);const o=we(n.shadowRoot?n.shadowRoot:n.getRootNode(),t);if(r&10){n["s-sc"]=o;n.classList.add(o+"-h")}i()};var $e=(e,t)=>"sc-"+e.m;var Se=(e,t,n,r,i,o,s)=>{if(n===r){return}let l=a(e,t);let f=t.toLowerCase();if(t==="class"){const t=e.classList;const i=Oe(n);let o=Oe(r);{t.remove(...i.filter((e=>e&&!o.includes(e))));t.add(...o.filter((e=>e&&!i.includes(e))))}}else if(t==="style"){{for(const t in n){if(!r||r[t]==null){if(t.includes("-")){e.style.removeProperty(t)}else{e.style[t]=""}}}}for(const t in r){if(!n||r[t]!==n[t]){if(t.includes("-")){e.style.setProperty(t,r[t])}else{e.style[t]=r[t]}}}}else if(t==="key");else if(t==="ref"){if(r){r(e)}}else if(!l&&t[0]==="o"&&t[1]==="n"){if(t[2]==="-"){t=t.slice(3)}else if(a(y,f)){t=f.slice(2)}else{t=f[2]+t.slice(3)}if(n||r){const i=t.endsWith(Ne);t=t.replace(Ce,"");if(n){b.rel(e,t,n,i)}if(r){b.ael(e,t,r,i)}}}else{const s=R(r);if((l||s&&r!==null)&&true){try{if(!e.tagName.includes("-")){const i=r==null?"":r;if(t==="list"){l=false}else if(n==null||e[t]!=i){if(typeof e.__lookupSetter__(t)==="function"){e[t]=i}else{e.setAttribute(t,i)}}}else if(e[t]!==r){e[t]=r}}catch(e){}}let a=false;{if(f!==(f=f.replace(/^xlink\:?/,""))){t=f;a=true}}if(r==null||r===false){if(r!==false||e.getAttribute(t)===""){if(a){e.removeAttributeNS(m,t)}else{e.removeAttribute(t)}}}else if((!l||o&4||i)&&!s&&e.nodeType===1){r=r===true?"":r;if(a){e.setAttributeNS(m,t,r)}else{e.setAttribute(t,r)}}}};var je=/\s/;var Oe=e=>{if(typeof e==="object"&&e&&"baseVal"in e){e=e.baseVal}if(!e||typeof e!=="string"){return[]}return e.split(je)};var Ne="Capture";var Ce=new RegExp(Ne+"$");var ke=(e,t,n,r)=>{const i=t.R.nodeType===11&&t.R.host?t.R.host:t.R;const o=e&&e.O||{};const s=t.O||{};{for(const e of Ee(Object.keys(o))){if(!(e in s)){Se(i,e,o[e],void 0,n,t.i)}}}for(const e of Ee(Object.keys(s))){Se(i,e,o[e],s[e],n,t.i)}};function Ee(e){return e.includes("ref")?[...e.filter((e=>e!=="ref")),"ref"]:e}var xe;var Me;var Re;var Pe=false;var Te=false;var Le=false;var Ue=false;var Ae=(e,n,r)=>{var i;const o=n.N[r];let s=0;let l;let f;let a;if(!Pe){Le=true;if(o.M==="slot"){o.i|=o.N?2:1}}if(o.j!==null){l=o.R=y.document.createTextNode(o.j)}else if(o.i&1){l=o.R=y.document.createTextNode("");{ke(null,o,Ue)}}else{if(!y.document){throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.")}l=o.R=y.document.createElement(!Pe&&t.slotRelocation&&o.i&2?"slot-fb":o.M);{ke(null,o,Ue)}if(M(xe)&&l["s-si"]!==xe){l.classList.add(l["s-si"]=xe)}if(o.N){for(s=0;s<o.N.length;++s){f=Ae(e,o,s);if(f){l.appendChild(f)}}}}l["s-hn"]=Re;{if(o.i&(2|1)){l["s-sr"]=true;l["s-cr"]=Me;l["s-sn"]=o.k||"";l["s-rf"]=(i=o.O)==null?void 0:i.ref;G(l);a=e&&e.N&&e.N[r];if(a&&a.M===o.M&&e.R){{Fe(e.R,false)}}{Ge(Me,l,n.R,e==null?void 0:e.R)}}}return l};var Fe=(e,t)=>{b.i|=1;const n=Array.from(e.__childNodes||e.childNodes);for(let e=n.length-1;e>=0;e--){const r=n[e];if(r["s-hn"]!==Re&&r["s-ol"]){qe(He(r).parentNode,r,He(r));r["s-ol"].remove();r["s-ol"]=void 0;r["s-sh"]=void 0;Le=true}if(t){Fe(r,t)}}b.i&=-2};var _e=(e,t,n,r,i,o)=>{let s=e["s-cr"]&&e["s-cr"].parentNode||e;let l;if(s.shadowRoot&&s.tagName===Re){s=s.shadowRoot}for(;i<=o;++i){if(r[i]){l=Ae(null,n,i);if(l){r[i].R=l;qe(s,l,He(t))}}}};var De=(e,t,n)=>{for(let r=t;r<=n;++r){const t=e[r];if(t){const e=t.R;Ye(t);if(e){{Te=true;if(e["s-ol"]){e["s-ol"].remove()}else{Fe(e,true)}}e.remove()}}}};var We=(e,t,n,r,i=false)=>{let o=0;let s=0;let l=0;let f=0;let a=t.length-1;let c=t[0];let u=t[a];let v=r.length-1;let d=r[0];let h=r[v];let p;let m;while(o<=a&&s<=v){if(c==null){c=t[++o]}else if(u==null){u=t[--a]}else if(d==null){d=r[++s]}else if(h==null){h=r[--v]}else if(Be(c,d,i)){ze(c,d,i);c=t[++o];d=r[++s]}else if(Be(u,h,i)){ze(u,h,i);u=t[--a];h=r[--v]}else if(Be(c,h,i)){if(c.M==="slot"||h.M==="slot"){Fe(c.R.parentNode,false)}ze(c,h,i);qe(e,c.R,u.R.nextSibling);c=t[++o];h=r[--v]}else if(Be(u,d,i)){if(c.M==="slot"||h.M==="slot"){Fe(u.R.parentNode,false)}ze(u,d,i);qe(e,u.R,c.R);u=t[--a];d=r[++s]}else{l=-1;{for(f=o;f<=a;++f){if(t[f]&&t[f].C!==null&&t[f].C===d.C){l=f;break}}}if(l>=0){m=t[l];if(m.M!==d.M){p=Ae(t&&t[s],n,l)}else{ze(m,d,i);t[l]=void 0;p=m.R}d=r[++s]}else{p=Ae(t&&t[s],n,s);d=r[++s]}if(p){{qe(He(c.R).parentNode,p,He(c.R))}}}}if(o>a){_e(e,r[v+1]==null?null:r[v+1].R,n,r,s,v)}else if(s>v){De(t,o,a)}};var Be=(e,t,n=false)=>{if(e.M===t.M){if(e.M==="slot"){return e.k===t.k}if(!n){return e.C===t.C}if(n&&!e.C&&t.C){e.C=t.C}return true}return false};var He=e=>e&&e["s-ol"]||e;var ze=(e,n,r=false)=>{const i=n.R=e.R;const o=e.N;const s=n.N;const l=n.j;let f;if(l===null){{ke(e,n,Ue)}if(o!==null&&s!==null){We(i,o,n,s,r)}else if(s!==null){if(e.j!==null){i.textContent=""}_e(i,null,n,s,0,s.length-1)}else if(!r&&t.updatable&&o!==null){De(o,0,o.length-1)}}else if(f=i["s-cr"]){f.parentNode.textContent=l}else if(e.j!==l){i.data=l}};var Ie=[];var Ve=e=>{let t;let n;let r;const i=e.__childNodes||e.childNodes;for(const e of i){if(e["s-sr"]&&(t=e["s-cr"])&&t.parentNode){n=t.parentNode.__childNodes||t.parentNode.childNodes;const i=e["s-sn"];for(r=n.length-1;r>=0;r--){t=n[r];if(!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==e["s-hn"]&&true){if(V(t,i)){let n=Ie.find((e=>e.T===t));Te=true;t["s-sn"]=t["s-sn"]||i;if(n){n.T["s-sh"]=e["s-hn"];n.L=e}else{t["s-sh"]=e["s-hn"];Ie.push({L:e,T:t})}if(t["s-sr"]){Ie.map((e=>{if(V(e.T,t["s-sn"])){n=Ie.find((e=>e.T===t));if(n&&!e.L){e.L=n.L}}}))}}else if(!Ie.some((e=>e.T===t))){Ie.push({T:t})}}}}if(e.nodeType===1){Ve(e)}}};var Ye=e=>{{e.O&&e.O.ref&&e.O.ref(null);e.N&&e.N.map(Ye)}};var qe=(e,t,n)=>{if(typeof t["s-sn"]==="string"&&!!t["s-sr"]&&!!t["s-cr"]){Ge(t["s-cr"],t,e,t.parentElement)}{return e==null?void 0:e.insertBefore(t,n)}};function Ge(e,t,n,r){var i,o;let s;if(e&&typeof t["s-sn"]==="string"&&!!t["s-sr"]&&e.parentNode&&e.parentNode["s-sc"]&&(s=t["s-si"]||e.parentNode["s-sc"])){const e=t["s-sn"];const l=t["s-hn"];(i=n.classList)==null?void 0:i.add(s+"-s");if(r&&((o=r.classList)==null?void 0:o.contains(s+"-s"))){let t=(r.__childNodes||r.childNodes)[0];let n=false;while(t){if(t["s-sn"]!==e&&t["s-hn"]===l&&!!t["s-sr"]){n=true;break}t=t.nextSibling}if(!n)r.classList.remove(s+"-s")}}}var Je=(e,t,n=false)=>{var r,i,o,s;const l=e.$hostElement$;const f=e.o;const a=e.U||se(null,null);const c=fe(t);const u=c?t:oe(null,null,t);Re=l.tagName;if(f.A){u.O=u.O||{};f.A.map((([e,t])=>u.O[t]=l[e]))}if(n&&u.O){for(const e of Object.keys(u.O)){if(l.hasAttribute(e)&&!["key","ref","style","class"].includes(e)){u.O[e]=l[e]}}}u.M=null;u.i|=4;e.U=u;u.R=a.R=l.shadowRoot||l;{xe=l["s-sc"]}Pe=!!(f.i&1)&&!(f.i&128);{Me=l["s-cr"];Te=false}ze(a,u,n);{b.i|=1;if(Le){Ve(u.R);for(const e of Ie){const t=e.T;if(!t["s-ol"]&&y.document){const e=y.document.createTextNode("");e["s-nr"]=t;qe(t.parentNode,t["s-ol"]=e,t)}}for(const e of Ie){const t=e.T;const l=e.L;if(l){const e=l.parentNode;let n=l.nextSibling;{let o=(r=t["s-ol"])==null?void 0:r.previousSibling;while(o){let r=(i=o["s-nr"])!=null?i:null;if(r&&r["s-sn"]===t["s-sn"]&&e===(r.__parentNode||r.parentNode)){r=r.nextSibling;while(r===t||(r==null?void 0:r["s-sr"])){r=r==null?void 0:r.nextSibling}if(!r||!r["s-nr"]){n=r;break}}o=o.previousSibling}}const s=t.__parentNode||t.parentNode;const f=t.__nextSibling||t.nextSibling;if(!n&&e!==s||f!==n){if(t!==n){if(!t["s-hn"]&&t["s-ol"]){t["s-hn"]=t["s-ol"].parentNode.nodeName}qe(e,t,n);if(t.nodeType===1&&t.tagName!=="SLOT-FB"){t.hidden=(o=t["s-ih"])!=null?o:false}}}t&&typeof l["s-rf"]==="function"&&l["s-rf"](l)}else{if(t.nodeType===1){if(n){t["s-ih"]=(s=t.hidden)!=null?s:false}t.hidden=true}}}}if(Te){B(u.R)}b.i&=-2;Ie.length=0}Me=void 0};var Ke=(e,t)=>{if(t&&!e.F&&t["s-p"]){const n=t["s-p"].push(new Promise((r=>e.F=()=>{t["s-p"].splice(n-1,1);r()})))}};var Qe=(e,t)=>{{e.i|=16}if(e.i&4){e.i|=512;return}Ke(e,e._);const n=()=>Xe(e,t);return x(n)};var Xe=(e,t)=>{const n=e.$hostElement$;const r=re("scheduleUpdate",e.o.m);const i=e.t;if(!i){throw new Error(`Can't render component <${n.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`)}let o;if(t){{e.i|=256;if(e.D){e.D.map((([e,t])=>ot(i,e,t,n)));e.D=void 0}}o=ot(i,"componentWillLoad",void 0,n)}else{o=ot(i,"componentWillUpdate",void 0,n)}o=Ze(o,(()=>ot(i,"componentWillRender",void 0,n)));r();return Ze(o,(()=>tt(e,i,t)))};var Ze=(e,t)=>et(e)?e.then(t).catch((e=>{console.error(e);t()})):t();var et=e=>e instanceof Promise||e&&e.then&&typeof e.then==="function";var tt=async(e,t,n)=>{var r;const i=e.$hostElement$;const o=re("update",e.o.m);const s=i["s-rc"];if(n){ge(e)}const l=re("render",e.o.m);{nt(e,t,i,n)}if(s){s.map((e=>e()));i["s-rc"]=void 0}l();o();{const t=(r=i["s-p"])!=null?r:[];const n=()=>rt(e);if(t.length===0){n()}else{Promise.all(t).then(n);e.i|=4;t.length=0}}};var nt=(e,t,n,r)=>{try{t=t.render();{e.i&=-17}{e.i|=2}{{{Je(e,t,r)}}}}catch(t){c(t,e.$hostElement$)}return null};var rt=e=>{const t=e.o.m;const n=e.$hostElement$;const r=re("postUpdate",t);const i=e.t;const o=e._;ot(i,"componentDidRender",void 0,n);if(!(e.i&64)){e.i|=64;{st(n)}ot(i,"componentDidLoad",void 0,n);r();{e.p(n);if(!o){it()}}}else{ot(i,"componentDidUpdate",void 0,n);r()}{e.v(n)}{if(e.F){e.F();e.F=void 0}if(e.i&512){E((()=>Qe(e,false)))}e.i&=-517}};var it=t=>{E((()=>me(y,"appload",{detail:{namespace:e}})))};var ot=(e,t,n,r)=>{if(e&&e[t]){try{return e[t](n)}catch(e){c(e,r)}}return void 0};var st=e=>{var n;return e.classList.add((n=t.hydratedSelectorName)!=null?n:"hydrated")};var lt=(e,t)=>s(e).l.get(t);var ft=(e,t,n,r)=>{const i=s(e);if(!i){throw new Error(`Couldn't find host element for "${r.m}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`)}const o=i.$hostElement$;const l=i.l.get(t);const f=i.i;const a=i.t;n=de(n,r.W[t][0]);const u=Number.isNaN(l)&&Number.isNaN(n);const v=n!==l&&!u;if((!(f&8)||l===void 0)&&v){i.l.set(t,n);if(a){if(r.B&&f&128){const e=r.B[t];if(e){e.map((e=>{try{a[e](n,l,t)}catch(e){c(e,o)}}))}}if((f&(2|16))===2){if(a.componentShouldUpdate){if(a.componentShouldUpdate(n,l,t)===false){return}}Qe(i,false)}}}};var at=(e,n,r)=>{var i,o;const l=e.prototype;if(n.W||(n.B||e.watchers)){if(e.watchers&&!n.B){n.B=e.watchers}const f=Object.entries((i=n.W)!=null?i:{});f.map((([e,[t]])=>{if(t&31||r&2&&t&32){const{get:i,set:o}=Object.getOwnPropertyDescriptor(l,e)||{};if(i)n.W[e][0]|=2048;if(o)n.W[e][0]|=4096;if(r&1||!i){Object.defineProperty(l,e,{get(){{if((n.W[e][0]&2048)===0){return lt(this,e)}const t=s(this);const r=t?t.t:l;if(!r)return;return r[e]}},configurable:true,enumerable:true})}Object.defineProperty(l,e,{set(i){const l=s(this);if(o){const r=t&32?this[e]:l.$hostElement$[e];if(typeof r==="undefined"&&l.l.get(e)){i=l.l.get(e)}else if(!l.l.get(e)&&r){l.l.set(e,r)}o.apply(this,[de(i,t)]);i=t&32?this[e]:l.$hostElement$[e];ft(this,e,i,n);return}{if((r&1)===0||(n.W[e][0]&4096)===0){ft(this,e,i,n);if(r&1&&!l.t){l.h.then((()=>{if(n.W[e][0]&4096&&l.t[e]!==l.l.get(e)){l.t[e]=i}}))}return}const o=()=>{const r=l.t[e];if(!l.l.get(e)&&r){l.l.set(e,r)}l.t[e]=de(i,t);ft(this,e,l.t[e],n)};if(l.t){o()}else{l.h.then((()=>o()))}}}})}else if(r&1&&t&64){Object.defineProperty(l,e,{value(...t){var n;const r=s(this);return(n=r==null?void 0:r.u)==null?void 0:n.then((()=>{var n;return(n=r.t)==null?void 0:n[e](...t)}))}})}}));if(r&1){const r=new Map;l.attributeChangedCallback=function(e,i,o){b.jmp((()=>{var f;const a=r.get(e);if(this.hasOwnProperty(a)&&t.lazyLoad){o=this[a];delete this[a]}else if(l.hasOwnProperty(a)&&typeof this[a]==="number"&&this[a]==o){return}else if(a==null){const t=s(this);const r=t==null?void 0:t.i;if(r&&!(r&8)&&r&128&&o!==i){const r=t.t;const s=(f=n.B)==null?void 0:f[e];s==null?void 0:s.forEach((t=>{if(r[t]!=null){r[t].call(r,o,i,e)}}))}return}const c=Object.getOwnPropertyDescriptor(l,a);o=o===null&&typeof this[a]==="boolean"?false:o;if(o!==this[a]&&(!c.get||!!c.set)){this[a]=o}}))};e.observedAttributes=Array.from(new Set([...Object.keys((o=n.B)!=null?o:{}),...f.filter((([e,t])=>t[0]&15)).map((([e,t])=>{var i;const o=t[1]||e;r.set(o,e);if(t[0]&512){(i=n.A)==null?void 0:i.push([e,o])}return o}))]))}}return e};var ct=async(e,t,n,r)=>{let i;if((t.i&32)===0){t.i|=32;const r=n.$;if(r){const r=v(n,t);if(r&&"then"in r){const e=ie();i=await r;e()}else{i=r}if(!i){throw new Error(`Constructor for "${n.m}#${t.H}" was not found`)}if(!i.isProxied){{n.B=i.watchers}at(i,n,2);i.isProxied=true}const o=re("createInstance",n.m);{t.i|=8}try{new i(t)}catch(t){c(t,e)}{t.i&=-9}{t.i|=128}o();ut(t.t,e)}else{i=e.constructor;const n=e.localName;customElements.whenDefined(n).then((()=>t.i|=128))}if(i&&i.style){let e;if(typeof i.style==="string"){e=i.style}const t=$e(n);if(!d.has(t)){const r=re("registerStyles",n.m);be(t,e,!!(n.i&1));r()}}}const o=t._;const s=()=>Qe(t,true);if(o&&o["s-rc"]){o["s-rc"].push(s)}else{s()}};var ut=(e,t)=>{{ot(e,"connectedCallback",void 0,t)}};var vt=e=>{if((b.i&1)===0){const t=s(e);const n=t.o;const r=re("connectedCallback",n.m);if(!(t.i&1)){t.i|=1;{if(n.i&(4|8)){dt(e)}}{let n=e;while(n=n.parentNode||n.host){if(n["s-p"]){Ke(t,t._=n);break}}}if(n.W){Object.entries(n.W).map((([t,[n]])=>{if(n&31&&e.hasOwnProperty(t)){const n=e[t];delete e[t];e[t]=n}}))}{ct(e,t,n)}}else{yt(e,t,n.I);if(t==null?void 0:t.t){ut(t.t,e)}else if(t==null?void 0:t.h){t.h.then((()=>ut(t.t,e)))}}r()}};var dt=e=>{if(!y.document){return}const t=e["s-cr"]=y.document.createComment("");t["s-cn"]=true;qe(e,t,e.firstChild)};var ht=(e,t)=>{{ot(e,"disconnectedCallback",void 0,t||e)}};var pt=async e=>{if((b.i&1)===0){const t=s(e);{if(t.V){t.V.map((e=>e()));t.V=void 0}}if(t==null?void 0:t.t){ht(t.t,e)}else if(t==null?void 0:t.h){t.h.then((()=>ht(t.t,e)))}}if(ye.has(e)){ye.delete(e)}if(e.shadowRoot&&ye.has(e.shadowRoot)){ye.delete(e.shadowRoot)}};var mt=(e,t={})=>{var n;if(!y.document){console.warn("Stencil: No document found. Skipping bootstrapping lazy components.");return}const r=re();const i=[];const o=t.exclude||[];const l=y.customElements;const a=y.document.head;const c=a.querySelector("meta[charset]");const u=y.document.createElement("style");const v=[];let d;let m=true;Object.assign(b,t);b.S=new URL(t.resourcesUrl||"./",y.document.baseURI).href;let w=false;e.map((e=>{e[1].map((t=>{var n;const r={i:t[0],m:t[1],W:t[2],I:t[3]};if(r.i&4){w=true}{r.W=t[2]}{r.I=t[3]}{r.A=[]}{r.B=(n=t[4])!=null?n:{}}const a=r.m;const c=class extends HTMLElement{constructor(e){super(e);this.hasRegisteredEventListeners=false;e=this;f(e,r);if(r.i&1){{if(!e.shadowRoot){W.call(e,r)}else{if(e.shadowRoot.mode!=="open"){throw new Error(`Unable to re-use existing shadow root for ${r.m}! Mode is set to ${e.shadowRoot.mode} but Stencil only supports open shadow roots.`)}}}}}connectedCallback(){const e=s(this);if(!this.hasRegisteredEventListeners){this.hasRegisteredEventListeners=true;yt(this,e,r.I)}if(d){clearTimeout(d);d=null}if(m){v.push(this)}else{b.jmp((()=>vt(this)))}}disconnectedCallback(){b.jmp((()=>pt(this)));b.raf((()=>{var e;const t=s(this);const n=v.findIndex((e=>e===this));if(n>-1){v.splice(n,1)}if(((e=t==null?void 0:t.U)==null?void 0:e.R)instanceof Node&&!t.U.R.isConnected){delete t.U.R}}))}componentOnReady(){return s(this).h}};{{X(c.prototype)}{Q(c.prototype)}}r.$=e[0];if(!o.includes(a)&&!l.get(a)){i.push(a);l.define(a,at(c,r,1))}}))}));if(i.length>0){if(w){u.textContent+=p}{u.textContent+=i.sort()+h}if(u.innerHTML.length){u.setAttribute("data-styles","");const e=(n=b.P)!=null?n:P(y.document);if(e!=null){u.setAttribute("nonce",e)}a.insertBefore(u,c?c.nextSibling:a.firstChild)}}m=false;if(v.length){v.map((e=>e.connectedCallback()))}else{{b.jmp((()=>d=setTimeout(it,30)))}}r()};var yt=(e,t,n,r)=>{if(n&&y.document){n.map((([n,r,i])=>{const o=wt(y.document,e,n);const s=bt(t,i);const l=gt(n);b.ael(o,r,s,l);(t.V=t.V||[]).push((()=>b.rel(o,r,s,l)))}))}};var bt=(e,t)=>n=>{var r;try{{if(e.i&256){(r=e.t)==null?void 0:r[t](n)}else{(e.D=e.D||[]).push([t,n])}}}catch(t){c(t,e.$hostElement$)}};var wt=(e,t,n)=>{if(n&8){return y}if(n&16){return e.body}return t};var gt=e=>w?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0;var $t=e=>b.P=e;export{le as H,e as N,he as a,mt as b,pe as c,n as g,oe as h,g as p,l as r,$t as s,y as w};
//# sourceMappingURL=p-C3J6Z5OX.js.map