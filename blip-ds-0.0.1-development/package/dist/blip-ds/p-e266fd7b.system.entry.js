var __awaiter=this&&this.__awaiter||function(t,i,e,r){function n(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r["throw"](t))}catch(t){o(t)}}function c(t){t.done?e(t.value):n(t.value).then(s,a)}c((r=r.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,n,o,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(t){return function(i){return c([t,i])}}function c(a){if(r)throw new TypeError("Generator is already executing.");while(s&&(s=0,a[0]&&(e=0)),e)try{if(r=1,n&&(o=a[0]&2?n["return"]:a[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;if(n=0,o)a=[a[0]&2,o.value];switch(a[0]){case 0:case 1:o=a;break;case 4:e.label++;return{value:a[1],done:false};case 5:e.label++;n=a[1];a=[0];continue;case 7:a=e.ops.pop();e.trys.pop();continue;default:if(!(o=e.trys,o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){e.label=a[1];break}if(a[0]===6&&e.label<o[1]){e.label=o[1];o=a;break}if(o&&e.label<o[2]){e.label=o[2];e.ops.push(a);break}if(o[2])e.ops.pop();e.trys.pop();continue}a=i.call(t,e)}catch(t){a=[6,t];n=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(t,i,e){if(e||arguments.length===2)for(var r=0,n=i.length,o;r<n;r++){if(o||!(r in i)){if(!o)o=Array.prototype.slice.call(i,0,r);o[r]=i[r]}}return t.concat(o||Array.prototype.slice.call(i))};System.register(["./p-B47mPBRA.system.js","./p-KsAJij7V.system.js","./p-DLraUrU1.system.js"],(function(t){"use strict";var i,e,r,n,o,s,a,c;return{setters:[function(t){i=t.r;e=t.c;r=t.h;n=t.a},function(t){o=t.g;s=t.p},function(t){a=t.w;c=t.e}],execute:function(){var u=':host{display:block}.element_input{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.element_input input{-webkit-box-shadow:inherit;box-shadow:inherit}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 8px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;gap:8px}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 7px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;padding:2px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;width:100%;line-height:22px;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-ghost, rgb(140, 140, 140));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.select{position:relative;outline:none}.select__icon{color:var(--color-content-ghost, rgb(140, 140, 140));display:-ms-flexbox;display:flex}.select__options{background:var(--color-surface-0, rgb(255, 255, 255));width:100%;max-height:200px;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:absolute;left:0;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));overflow-y:auto;z-index:2;pointer-events:none;opacity:0}.select__options::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.select__options::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.select__options--open{opacity:1;pointer-events:auto}.select__options--position-top{bottom:calc(100% + 4px)}.select__options--position-bottom{top:calc(100% + 4px)}.inside-input-left{display:-ms-inline-flexbox;display:inline-flex;gap:8px;-ms-flex-wrap:wrap;flex-wrap:wrap;max-height:200px;overflow-y:auto}.inside-input-left::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.inside-input-left::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input-chips__chip{margin:2px 4px 2px 0px}.input-chips__chips{-ms-flex:1;flex:1}';var l=t("bds_select_chips",function(){function t(t){var r=this;i(this,t);this.bdsChange=e(this,"bdsChange");this.bdsCancel=e(this,"bdsCancel");this.bdsFocus=e(this,"bdsFocus");this.bdsBlur=e(this,"bdsBlur");this.bdsChangeChips=e(this,"bdsChangeChips");this.bdsSelectChipsInput=e(this,"bdsSelectChipsInput");this.bdsSubmit=e(this,"bdsSubmit");this.isOpen=false;this.intoView=null;this.selectedOptions=[];this.validationDanger=false;this.isPressed=false;this.validationMesage="";this.internalChips=[];this.chips=[];this.newPrefix="";this.value="";this.danger=false;this.success=false;this.errorMessage="";this.disabled=false;this.label="";this.icon="";this.duplicated=false;this.canAddNew=true;this.notFoundMessage="No results found";this.type="text";this.delimiters=/,|;/;this.disableSubmit=false;this.helperMessage="";this.successMessage="";this.inputName="";this.placeholder="";this.optionsPosition="auto";this.dataTest=null;this.handleChangeChipsValue=function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return[4,this.resetFilterOptions()];case 1:t.sent();return[2]}}))}))};this.refDropdown=function(t){r.dropElement=t};this.refIconDrop=function(t){r.iconDropElement=t};this.toggle=function(){if(!r.disabled){r.isOpen=!r.isOpen}};this.handler=function(t){return __awaiter(r,void 0,void 0,(function(){var i,e;return __generator(this,(function(r){switch(r.label){case 0:i=t.detail.value;this.selectedOption=i;e=this.getText(i);return[4,this.addChip(e)];case 1:r.sent();this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions});this.toggle();return[2]}}))}))};this.handlerNewOption=function(t){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(i){switch(i.label){case 0:return[4,this.addChip(t)];case 1:i.sent();this.toggle();return[2]}}))}))};this.getText=function(t){var i=r.childOptions.find((function(i){return i.value===t}));return r.getTextFromOption(i)};this.getTextFromOption=function(t){var i,e;if(r.internalOptions){var n=r.internalOptions.find((function(i){return i.value==(t===null||t===void 0?void 0:t.value)}));if(n){return n.label}}return(t===null||t===void 0?void 0:t.titleText)?t.titleText:(e=(i=t===null||t===void 0?void 0:t.textContent)===null||i===void 0?void 0:i.trim())!==null&&e!==void 0?e:""};this.setFocusWrapper=function(){if(r.nativeInput){r.nativeInput.focus()}};this.removeFocusWrapper=function(){r.nativeInput.blur()};this.onClickWrapper=function(){r.onFocus();if(r.nativeInput){r.nativeInput.focus()}};this.onFocus=function(){r.bdsFocus.emit();r.isPressed=true};this.onInput=function(t){var i=t.target;if(i){r.value=i.value||""}r.bdsSelectChipsInput.emit(t);r.changedInputValue()};this.keyPressWrapper=function(t){switch(t.key){case"Enter":if(r.canAddNew!==false){r.handleDelimiters();r.setChip(r.value);r.value="";r.bdsChangeChips.emit({data:r.internalChips,value:r.selectedOption});r.bdsChange.emit({data:r.selectedOptions})}if(!r.disabled){r.isOpen=true}break;case"ArrowDown":if(!r.disabled){r.isOpen=true}break;case"ArrowUp":if(!r.disabled){r.isOpen=false}break;case"Backspace":case"Delete":if((r.value===null||r.value.length<=0)&&r.internalChips.length){r.removeLastChip();r.bdsChangeChips.emit({data:r.internalChips,value:r.selectedOption});r.bdsChange.emit({data:r.selectedOptions})}break}};this.changedInputValue=function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:this.value=this.nativeInput.value;if(!this.nativeInput.value)return[3,2];return[4,this.filterOptions(this.nativeInput.value)];case 1:t.sent();return[3,4];case 2:return[4,this.resetFilterOptions()];case 3:t.sent();t.label=4;case 4:if(this.value&&this.isOpen===false){this.isOpen=true}return[2]}}))}))}}t.prototype.isOpenChanged=function(t){if(this.positionHeightDrop=="bottom"){this.iconDropElement.name=this.isOpen?"arrow-up":"arrow-down"}else{this.iconDropElement.name=this.isOpen?"arrow-down":"arrow-up"}if(t)if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}};t.prototype.handleWindow=function(t){if(!this.el.contains(t.target)){this.isOpen=false}};t.prototype.optionsChanged=function(){if(typeof this.options==="string"){try{this.internalOptions=JSON.parse(this.options)}catch(t){}}else{this.internalOptions=this.options}};t.prototype.valueChanged=function(){if(this.chips){if(typeof this.chips==="string"){try{this.internalChips=JSON.parse(this.chips)}catch(t){this.internalChips=[]}}else{this.internalChips=this.chips}}else{this.internalChips=[]}};t.prototype.internalValueChanged=function(){var t=this;this.handleChangeChipsValue();if(this.internalChips.length>0){this.selectedOptions=this.internalChips.map((function(i){return{label:i,value:"".concat(t.validValueChip(i,t.childOptions))}}))}};t.prototype.validValueChip=function(t,i){var e=i===null||i===void 0?void 0:i.find((function(i){return i.textContent==t}));return"".concat(e?e.value:t)};t.prototype.isValid=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.validateChips()]}))}))};t.prototype.getChips=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.internalChips]}))}))};t.prototype.clear=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.internalChips=[];this.value="";return[2]}))}))};t.prototype.add=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){this.handleDelimiters();if(t){this.setChip(t)}else{this.setChip(this.value)}this.value="";return[2]}))}))};t.prototype.setFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.nativeInput.focus();return[2]}))}))};t.prototype.removeFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.nativeInput.blur();return[2]}))}))};t.prototype.componentWillLoad=function(){this.valueChanged();this.optionsChanged();this.intoView=o(this.el)};t.prototype.componentDidLoad=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return[4,this.resetFilterOptions()];case 1:t.sent();if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}return[2]}}))}))};t.prototype.setDefaultPlacement=function(t){if(t=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}};t.prototype.validatePositionDrop=function(){var t=s({actionElement:this.el,changedElement:this.dropElement,intoView:this.intoView});this.positionHeightDrop=t.y;if(t.y=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}};t.prototype.connectedCallback=function(){return __awaiter(this,void 0,void 0,(function(){var t,i,e;return __generator(this,(function(r){for(t=0,i=this.childOptions;t<i.length;t++){e=i[t];e.addEventListener("optionSelected",this.handler)}return[2]}))}))};Object.defineProperty(t.prototype,"childOptionsEnabled",{get:function(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option:not([invisible]):not(#option-add):not(#no-option)")):Array.from(this.el.querySelectorAll("bds-select-option:not([invisible]):not(#option-add):not(#no-option)"))},enumerable:false,configurable:true});Object.defineProperty(t.prototype,"childOptions",{get:function(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option:not(#option-add):not(#no-option)")):Array.from(this.el.querySelectorAll("bds-select-option:not(#option-add):not(#no-option)"))},enumerable:false,configurable:true});t.prototype.filterOptions=function(t){return __awaiter(this,void 0,void 0,(function(){var i,e,r,n,o,s,a,c;return __generator(this,(function(u){switch(u.label){case 0:if(!!t)return[3,2];return[4,this.resetFilterOptions()];case 1:u.sent();return[2];case 2:i=0,e=this.childOptions;u.label=3;case 3:if(!(i<e.length))return[3,6];r=e[i];o=this.existsChip;s=[r.textContent];return[4,this.getChips()];case 4:n=o.apply(this,s.concat([u.sent()]));a=r.textContent.toLowerCase();c=t.toLowerCase();if(n){r.setAttribute("invisible","invisible")}if(t&&a.includes(c)&&!n){r.removeAttribute("invisible")}if(t&&!a.includes(c)&&!n){r.setAttribute("invisible","invisible")}u.label=5;case 5:i++;return[3,3];case 6:return[2]}}))}))};t.prototype.resetFilterOptions=function(){return __awaiter(this,void 0,void 0,(function(){var t,i,e,r,n;return __generator(this,(function(o){switch(o.label){case 0:t=0,i=this.childOptions;o.label=1;case 1:if(!(t<i.length))return[3,4];e=i[t];r=this.existsChip;n=[e.textContent];return[4,this.getChips()];case 2:if(r.apply(this,n.concat([o.sent()]))){e.setAttribute("invisible","invisible")}else{e.removeAttribute("invisible")}o.label=3;case 3:t++;return[3,1];case 4:return[2]}}))}))};t.prototype.existsChip=function(t,i){return i.some((function(i){return t===i}))};t.prototype.enableCreateOption=function(){return!!(this.childOptionsEnabled.length===0&&this.nativeInput&&this.nativeInput.value)};t.prototype.addChip=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(i){switch(i.label){case 0:return[4,this.setChip(t)];case 1:i.sent();this.nativeInput.value="";return[2]}}))}))};t.prototype.validateChips=function(){var t=this;if(this.type==="email"){return!this.internalChips.some((function(i){return!t.validateChip(i)}))}else{return true}};t.prototype.handleOnBlur=function(){this.bdsBlur.emit();this.isPressed=false};t.prototype.verifyAndSubstituteDelimiters=function(t){if(t.length===1&&t[0].match(this.delimiters)){return""}var i=t.replace(/;/g,",").replace(/\,+|;+/g,",");if(i[0].match(this.delimiters)){i=i.substring(1)}return i};t.prototype.handleDelimiters=function(){var t=this;var i=this.nativeInput.value;this.value=i?i.trim():"";if(i.length===0)return;var e=i.match(this.delimiters);if(!e)return;var r=this.verifyAndSubstituteDelimiters(i);if(!r){this.clearInputValues();return}var n=r.split(this.delimiters);n.forEach((function(i){t.setChip(i.trimStart())}));this.clearInputValues()};t.prototype.handleChange=function(t){return __awaiter(this,void 0,void 0,(function(){var i,e,r,n;var o=this;return __generator(this,(function(s){i=t.detail.value;this.value=i?i.trim():"";if(i.length===0)return[2];e=i.match(this.delimiters);if(!e)return[2];r=this.verifyAndSubstituteDelimiters(i);if(!r){this.clearInputValues();return[2]}n=r.split(this.delimiters);n.forEach((function(t){o.setChip(t)}));this.clearInputValues();return[2]}))}))};t.prototype.clearInputValues=function(t){if(t===void 0){t=""}this.nativeInput.value=t;this.value=t};t.prototype.setChip=function(t){if(!this.duplicated){var i=this.internalChips.some((function(i){return i.toLowerCase()===t.toLowerCase()}));if(i)return}if(!a(t)){return}this.internalChips=__spreadArray(__spreadArray([],this.internalChips,true),[t],false)};t.prototype.validateChip=function(t){var i=t.trim();if(this.type==="email"&&c(i)){return false}return true};t.prototype.removeLastChip=function(){this.internalChips=this.internalChips.slice(0,this.internalChips.length-1)};t.prototype.removeChip=function(t){var i=t.detail.id;this.internalChips=this.internalChips.filter((function(t,e){return e.toString()!==i}));this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions})};t.prototype.renderChips=function(){var t=this;if(!this.internalChips.length){return[]}return this.internalChips.map((function(i,e){var n=e.toString();var o=30;if(i.length<=o){return r("bds-chip-clickable",{id:n,key:n,color:"outline",close:!t.disabled,onChipClickableClose:function(i){return t.removeChip(i)}},i)}else{return r("bds-tooltip",{key:n,position:"top-center","tooltip-text":i},r("bds-chip-clickable",{id:n,key:n,color:"outline",close:!t.disabled,onChipClickableClose:function(i){return t.removeChip(i)}},"".concat(i.slice(0,o)," ...")))}}))};t.prototype.renderIcon=function(){return this.icon&&r("div",{class:{input__icon:true,"input__icon--large":!!this.label}},r("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))};t.prototype.renderLabel=function(){return this.label&&r("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},r("bds-typo",{variant:"fs-12",bold:"bold"},this.label))};t.prototype.renderMessage=function(){var t=this.danger?"error":this.success?"checkball":"info";var i=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!i&&this.validationDanger)i=this.validationMesage;var e=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(i){return r("div",{class:e,part:"input__message"},r("div",{class:"input__message__icon"},r("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),r("bds-typo",{class:"input__message__text",variant:"fs-12"},i))}return undefined};t.prototype.generateKey=function(t){return t.toLowerCase().replace(/ /g,"-")};t.prototype.render=function(){var t=this;var i=this.isPressed&&!this.disabled;var e=[];if(this.options){if(typeof this.options==="string"){try{e=JSON.parse(this.options)}catch(t){}}else{e=this.options}}return r("div",{key:"3df3da1874aaa4e0f131d22d4b63bc5b16e8366f",class:"select",tabindex:"0",onFocus:this.setFocusWrapper,onBlur:this.removeFocusWrapper},r("div",{key:"59b1b21a02059b15def9eaa8da5cfd399c0fb36a",class:{element_input:true},"aria-disabled":this.disabled?"true":null,onClick:this.toggle},r("div",{key:"572d01c470e0630380f58c5c0c41bb67311f0106",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":i},onClick:this.onClickWrapper},this.renderIcon(),r("div",{key:"442ca869cdda603d31835416f8228d5c4c4003a2",class:"input__container"},this.renderLabel(),r("div",{key:"deff52088bcde71354dd5f4f5762c139c7c67ab6",class:{input__container__wrapper:true}},this.internalChips.length>0&&r("span",{key:"b26b3b2394de5dd416ddca962797d500be51a5b4",style:{height:this.height,maxHeight:this.maxHeight},class:"inside-input-left"},this.renderChips()),r("input",{key:"2ce5972966e480523c8d1582acfe9d9d2dcef40d",ref:function(i){return t.nativeInput=i},class:{input__container__text:true},name:this.inputName,maxlength:this.maxlength,placeholder:this.placeholder,onInput:this.onInput,onFocus:this.onFocus,onBlur:function(){return t.handleOnBlur()},onChange:function(){return t.handleChange},value:this.value,disabled:this.disabled,"data-test":this.dataTest,onKeyDown:this.keyPressWrapper}))),r("div",{key:"f15ac21dae66265062a748843832d664edd438ef",class:"select__icon"},r("bds-icon",{key:"303c7455fe8fca1ad9739c0326ca4d3b5d15e3fd",ref:function(i){return t.refIconDrop(i)},size:"small",color:"inherit"})),this.success&&r("bds-icon",{key:"0ef632747adf1bd7ad58233cddbd444531805cb6",class:"icon-success",name:"check",theme:"outline",size:"xxx-small"})),this.renderMessage()),r("div",{key:"9d9d432f436c4c055108bc197e2d5a244299df17",ref:function(i){return t.refDropdown(i)},class:{select__options:true,"select__options--open":this.isOpen}},e.map((function(i){return r("bds-select-option",{key:t.generateKey(i.value),onOptionSelected:t.handler,value:i.value,status:i.status},i.label)})),r("slot",{key:"f73ea2c85e54417dde1327fe70447163c5eb4358"}),this.canAddNew===true&&this.enableCreateOption()&&r("bds-select-option",{key:"f0599212b27fd2be1b6194f5f7054f429ce6219d",id:"option-add",value:"add",onClick:function(){return t.handlerNewOption(t.nativeInput.value)}},this.newPrefix,this.nativeInput.value),!this.canAddNew&&this.enableCreateOption()&&r("bds-select-option",{key:"ccbabb2fb4bfaf044bf19e0280553e6797b81be8",id:"no-option",value:"add"},this.notFoundMessage)))};Object.defineProperty(t.prototype,"el",{get:function(){return n(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{isOpen:["isOpenChanged"],options:["optionsChanged"],chips:["valueChanged"],internalChips:["internalValueChanged"]}},enumerable:false,configurable:true});return t}());l.style=u}}}));
//# sourceMappingURL=p-e266fd7b.system.entry.js.map