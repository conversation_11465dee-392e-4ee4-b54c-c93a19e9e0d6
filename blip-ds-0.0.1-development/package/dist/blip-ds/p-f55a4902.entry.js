import{r as t,c as e,h as s,H as i,a}from"./p-C3J6Z5OX.js";const o=':host{display:block;width:100%}:host .table{display:grid;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "<PERSON><PERSON>", sans-serif;color:var(--color-content-default, rgb(40, 40, 40));width:100%;border:1px solid var(--color-border-3, rgba(0, 0, 0, 0.06));border-radius:8px;overflow-x:auto;background-color:var(--color-surface-1, rgb(246, 246, 246))}:host .table .thead{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));padding:0 16px}:host .table .thead .header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;text-align:left;-ms-flex-align:center;align-items:center;height:64px;gap:16px}:host .table .thead .header .header-title{height:64px;width:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:start;justify-content:flex-start;gap:8px}:host .table .thead .header .header-title .title-click{cursor:pointer}:host .table .body-row{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;-ms-flex-align:center;align-items:center;height:64px;padding:0 16px;gap:16px;border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16))}:host .table .body-row .body-item{height:48px;width:100%;gap:8px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:start;justify-content:flex-start}:host .table .body-row:last-child{border-bottom:none}';const r=class{constructor(s){t(this,s);this.bdsTableClick=e(this,"bdsTableClick");this.bdsTableDelete=e(this,"bdsTableDelete");this.bdsTableChange=e(this,"bdsTableChange");this.newTable=[];this.headerData=[];this.tableData=[];this.avatar=false;this.chips=false;this.sorting=false}componentWillLoad(){this.getDataFromProprety()}getDataFromProprety(){this.headerData=JSON.parse(this.column);this.tableData=JSON.parse(this.options)}renderArrow(t){if(t){return s("bds-icon",{name:"arrow-up",size:"small"})}else{return null}}async deleteItem(t){const e=this.tableData.filter(((e,s)=>s===t&&e));this.bdsTableDelete.emit(e[0]);this.tableData.splice(t,1);this.tableData=[...this.tableData];this.bdsTableChange.emit(this.tableData)}clickButton(t,e,s){this.bdsTableClick.emit({item:t,index:e,nameButton:s})}orderColumn(t){this.headerActive=t;this.sortAscending=this.sortAscending?false:true;if(this.sortAscending===false){this.tableData.sort((function(e,s){return e[t]>s[t]?1:-1}))}else{this.tableData.sort((function(e,s){return e[t]>s[t]?-1:1}))}}render(){return s(i,{key:"ab0f80f5cbd63bdd7cb30bce36b5c3a4b2bcfc57"},s("table",{key:"30a9e88b6eb99cfb85255f1a644ad7d239156226",class:"table"},s("thead",{key:"39ddb051120f8b1109a11f79ef678521634af5d4",class:"thead"},s("tr",{key:"005042e306fcd6b2a44e24be0d10667da5d8a218",class:"header"},this.headerData.map(((t,e)=>s("th",{class:"header-title",key:e},this.sorting?s("bds-typo",{class:"title-click",onClick:()=>this.orderColumn(t.value),variant:"fs-14",bold:this.headerActive===`${t.value}`?"bold":"semi-bold"},t.heading):s("bds-typo",{variant:"fs-14",bold:"semi-bold"},t.heading),this.sortAscending===true&&this.sorting===true&&this.headerActive===`${t.value}`?s("bds-icon",{class:"header-icon",name:"arrow-up",size:"small"}):this.sortAscending===false&&this.sorting===true&&this.headerActive===`${t.value}`?s("bds-icon",{name:"arrow-down",size:"small"}):""))))),s("tbody",{key:"dee098be4fb6d74fc062714f25cc2f3564b8e216"},this.tableData.map(((t,e)=>s("tr",{class:"body-row",key:e},this.headerData.map(((i,a)=>s("td",{class:"body-item",key:a},this.actionArea&&i.editAction?s("bds-button-icon",{onClick:()=>this.clickButton(t,e,i.editAction),variant:"secondary",icon:t[`${i.editAction}`],size:"short"}):"",this.actionArea&&i.deleteAction?s("bds-button-icon",{onClick:()=>this.clickButton(t,e,i.deleteAction),variant:"secondary",icon:t[`${i.deleteAction}`],size:"short"}):"",this.actionArea&&i.customAction?s("bds-button-icon",{onClick:()=>this.clickButton(t,e,i.customAction),variant:"secondary",icon:t[`${i.customAction}`],size:"short"}):"",this.chips&&i.chips?s("bds-chip-tag",{color:t[`${i.chips}`]?t[`${i.chips}`]:"default"},t[`${i.value}`]):"",this.avatar&&i.img?s("bds-avatar",{size:"extra-small",thumbnail:t[`${i.img}`],name:t[`${i.value}`]}):"",i.chips?"":s("bds-typo",{variant:"fs-14",bold:this.headerActive===`${i.value}`?"bold":"regular"},t[`${i.value}`]))))))))))}get el(){return a(this)}};r.style=o;export{r as bds_data_table};
//# sourceMappingURL=p-f55a4902.entry.js.map