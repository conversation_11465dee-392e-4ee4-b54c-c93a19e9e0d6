import{r as t,c as i,h as e,H as o,a as s}from"./p-C3J6Z5OX.js";const a=':host{display:block;width:100%}:host(.list_item_content){display:-ms-flexbox;display:flex;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.list_item{display:-ms-flexbox;display:flex;gap:16px;-ms-flex-align:center;align-items:center}.list_item_tall{padding:16px}.list_item_standard{padding:8px 16px}.list_item_short{padding:8px}.list_item .input_list{position:relative}.list_item .avatar-item{position:relative;display:block}.list_item .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.list_item .grow-up{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-slot{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;gap:8px}.list_item .content-item{position:relative;display:-ms-flexbox;display:flex;gap:2px;-ms-flex-direction:column;flex-direction:column}.list_item .content-item .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-item .subtitle-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-area{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-area .internal-chips,.list_item .content-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;gap:8px}.list_item .action-area{position:relative}.list_item .action-area .internal-actions-buttons,.list_item .action-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-arrow{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.list_item .icon-arrow-active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.border_radius{border-radius:8px}.border_radius:before,.border_radius:after,.border_radius .active{border-radius:8px}.active{position:absolute;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08;inset:0}.clickable{position:relative;cursor:pointer;gap:8px}.clickable:before{content:"";position:absolute;inset:0}.clickable:hover:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.clickable:active:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}';const l=class{constructor(e){t(this,e);this.bdsListCheckboxChange=i(this,"bdsListCheckboxChange");this.bdsListRadioChange=i(this,"bdsListRadioChange");this.bdsListSwitchChange=i(this,"bdsListSwitchChange");this.bdsClickActionsButtons=i(this,"bdsClickActionsButtons");this.itemListElement=null;this.typeList=null;this.chagedOptions=t=>{const{detail:i}=t;if(i.typeList=="radio"){if(i.checked==true){this.value=i}}if(i.typeList=="checkbox"){this.setSelectedCheckbox()}if(i.typeList=="switch"){this.setSelectedSwitch()}};this.onClickActionsButtons=t=>{const{detail:i}=t;this.bdsClickActionsButtons.emit(i)}}componentWillLoad(){this.data&&this.dataChanged()}componentWillRender(){this.data&&this.updateData();if(!this.data){this.setitemListElement()}}componentDidRender(){if(this.data){this.internalDataChanged()}}dataChanged(){this.updateData()}valueChanged(t){this.setSelectedRadio(t)}internalDataChanged(){this.itemListElement=this.element.shadowRoot.querySelectorAll("bds-list-item")}setitemListElement(){this.itemListElement=this.element.getElementsByTagName("bds-list-item");for(let t=0;t<this.itemListElement.length;t++){this.itemListElement[t].typeList=this.typeList;this.itemListElement[t].addEventListener("bdsChecked",(t=>this.chagedOptions(t)))}}updateData(){if(this.data){if(typeof this.data==="string"){this.internalData=JSON.parse(this.data)}else{this.internalData=this.data}}}setSelectedRadio(t){var i,e,o,s,a;const l=Array.from(this.itemListElement);const n=l.filter((t=>t.typeList=="radio"));for(let l=0;l<n.length;l++){if(n[l].value!=t.value){n[l].checked=false}else{const t={value:n[l].value,text:(i=n[l])===null||i===void 0?void 0:i.text,secondaryText:(e=n[l])===null||e===void 0?void 0:e.secondaryText,avatarName:(o=n[l])===null||o===void 0?void 0:o.avatarName,avatarThumbnail:(s=n[l])===null||s===void 0?void 0:s.avatarThumbnail,typeList:(a=n[l])===null||a===void 0?void 0:a.typeList};this.bdsListRadioChange.emit(t)}}}setSelectedCheckbox(){const t=this.itemListElement;const i=Array.from(t).filter((t=>t.typeList=="checkbox"));const e=i.filter((t=>t.checked)).map((t=>({value:t.value,text:t===null||t===void 0?void 0:t.text,secondaryText:t===null||t===void 0?void 0:t.secondaryText,avatarName:t===null||t===void 0?void 0:t.avatarName,avatarThumbnail:t===null||t===void 0?void 0:t.avatarThumbnail,typeList:t===null||t===void 0?void 0:t.typeList})));this.bdsListCheckboxChange.emit(e)}setSelectedSwitch(){const t=this.itemListElement;const i=Array.from(t).filter((t=>t.typeList=="switch"));const e=i.filter((t=>t.checked)).map((t=>({value:t.value,text:t===null||t===void 0?void 0:t.text,secondaryText:t===null||t===void 0?void 0:t.secondaryText,avatarName:t===null||t===void 0?void 0:t.avatarName,avatarThumbnail:t===null||t===void 0?void 0:t.avatarThumbnail,typeList:t===null||t===void 0?void 0:t.typeList})));this.bdsListSwitchChange.emit(e)}render(){return e(o,{key:"74e1b4e602cd6e1f0ab6351394a494cbb74e5a6e"},e("div",{key:"576759d97c75e15ef10b3d34bdf7044cc00b70b0",class:{list:true}},this.internalData?this.internalData.map(((t,i)=>e("bds-list-item",{key:i,value:t.value,text:t.text,"type-list":this.typeList?this.typeList:t.typeList,"secondary-text":t.secondaryText,"avatar-name":t.avatarName,"avatar-thumbnail":t.avatarThumbnail,checked:t.checked,icon:t.icon,chips:t.chips,actionsButtons:t.actionsButtons,onBdsChecked:t=>this.chagedOptions(t),onBdsClickActionButtom:t=>this.onClickActionsButtons(t),dataTest:t.dataTest}))):e("slot",null)))}get element(){return s(this)}static get watchers(){return{data:["dataChanged"],value:["valueChanged"],internalData:["internalDataChanged"]}}};l.style=a;export{l as bds_list};
//# sourceMappingURL=p-a1a9f625.entry.js.map