{"version": 3, "names": ["dataTableCss", "DataTable", "exports", "class_1", "hostRef", "this", "newTable", "headerData", "tableData", "avatar", "chips", "sorting", "prototype", "componentWillLoad", "getDataFromProprety", "JSON", "parse", "column", "options", "renderArrow", "value", "h", "name", "size", "deleteItem", "index", "itemDelete", "filter", "item", "i", "bdsTableDelete", "emit", "splice", "__spread<PERSON><PERSON>y", "bdsTableChange", "clickButton", "btn", "bdsTableClick", "nameButton", "orderColumn", "idx", "headerActive", "sortAscending", "sort", "a", "b", "render", "_this", "Host", "key", "class", "map", "onClick", "variant", "bold", "concat", "heading", "columnItem", "actionArea", "editAction", "icon", "deleteAction", "customAction", "color", "img", "thumbnail"], "sources": ["src/components/table/data-table.scss?tag=bds-data-table&encapsulation=shadow", "src/components/table/data-table.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n\n  .table {\n    display: grid;\n    font-family: $font-family;\n    color: $color-content-default;\n    width: 100%;\n    border: 1px solid $color-border-3;\n    border-radius: 8px;\n    overflow-x: auto;\n    background-color: $color-surface-1;\n\n    .thead {\n      border-bottom: 1px solid $color-border-1;\n      padding: 0 16px;\n      .header {\n        display: flex;\n        flex-direction: row;\n        justify-content: space-between;\n        text-align: left;\n        align-items: center;\n        height: 64px;\n        gap: 16px;\n        .header-title {\n          height: 64px;\n          width: 100%;\n          display: flex;\n          flex-direction: row;\n          align-items: center;\n          justify-content: flex-start;\n          gap: 8px;\n          \n          .title-click {\n            cursor: pointer;\n          }\n        }\n      }\n    }\n\n    .body-row {\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      height: 64px;\n      padding: 0 16px;\n      gap: 16px;\n      border-bottom: 1px solid $color-border-2;\n\n      .body-item {\n        height: 48px;\n        width: 100%;\n        gap: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n      }\n    }\n\n    .body-row:last-child {\n      border-bottom: none;\n    }\n  }\n}\n", "import { Component, Host, h, Prop, State, Element, Event, EventEmitter, Method } from '@stencil/core';\n\ntype Data = {\n  [key: string]: any;\n};\n@Component({\n  tag: 'bds-data-table',\n  styleUrl: 'data-table.scss',\n  shadow: true,\n})\nexport class DataTable {\n  @Element() el!: HTMLElement;\n  @State() newTable: Data = [];\n  /**\n   * For keep the Object of header;\n   */\n  @State() headerData?: Data = [];\n  /**\n   * For keep the Object of table content.\n   */\n  @State() tableData?: Data[] = [];\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() sortAscending?: boolean;\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() headerActive: string;\n  /**\n   * Prop to recive the content of the table.\n   */\n  @Prop() options?: string;\n  /**\n   * Prop to recive the header and configuration of table.\n   */\n  @Prop() column?: string;\n  /**\n   * Prop to activate the possibility of use avatar in any column.\n   */\n  @Prop() avatar?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() chips?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() actionArea?: boolean;\n  /**\n   * Prop to activate the sorting.\n   */\n  @Prop() sorting?: boolean = false;\n  @Event() bdsTableClick: EventEmitter;\n  @Event() bdsTableDelete: EventEmitter;\n  @Event() bdsTableChange: EventEmitter;\n\n  componentWillLoad() {\n    this.getDataFromProprety();\n  }\n\n  private getDataFromProprety() {\n    this.headerData = JSON.parse(this.column);\n    this.tableData = JSON.parse(this.options);\n  }\n\n  renderArrow(value) {\n    if (value) {\n      return <bds-icon name=\"arrow-up\" size=\"small\"></bds-icon>;\n    } else {\n      return null;\n    }\n  }\n\n  @Method()\n  async deleteItem(index: number) {\n    const itemDelete = this.tableData.filter((item, i) => i === index && item);\n    this.bdsTableDelete.emit(itemDelete[0]);\n    this.tableData.splice(index, 1);\n    this.tableData = [...this.tableData];\n    this.bdsTableChange.emit(this.tableData);\n  }\n\n  clickButton(item, index, btn) {\n    this.bdsTableClick.emit({ item: item, index: index, nameButton: btn });\n  }\n\n  orderColumn(idx) {\n    this.headerActive = idx;\n    this.sortAscending = this.sortAscending ? false : true;\n\n    if (this.sortAscending === false) {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? 1 : -1;\n      });\n    } else {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? -1 : 1;\n      });\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <table class=\"table\">\n          <thead class=\"thead\">\n            <tr class=\"header\">\n              {this.headerData.map((item, index) => (\n                <th class=\"header-title\" key={index}>\n                  {this.sorting ? (\n                    <bds-typo\n                      class=\"title-click\"\n                      onClick={() => this.orderColumn(item.value)}\n                      variant=\"fs-14\"\n                      bold={this.headerActive === `${item.value}` ? 'bold' : 'semi-bold'}\n                    >\n                      {item.heading}\n                    </bds-typo>\n                  ) : (\n                    <bds-typo variant=\"fs-14\" bold=\"semi-bold\">\n                      {item.heading}\n                    </bds-typo>\n                  )}\n                  {this.sortAscending === true && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon class=\"header-icon\" name=\"arrow-up\" size=\"small\"></bds-icon>\n                  ) : this.sortAscending === false && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon name=\"arrow-down\" size=\"small\"></bds-icon>\n                  ) : (\n                    ''\n                  )}\n                </th>\n              ))}\n            </tr>\n          </thead>\n          <tbody>\n            {this.tableData.map((item, index) => (\n              <tr class=\"body-row\" key={index}>\n                {this.headerData.map((columnItem, idx) => {\n                  return (\n                    <td class=\"body-item\" key={idx}>\n                      {this.actionArea && columnItem.editAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.editAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.editAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.deleteAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.deleteAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.deleteAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.customAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.customAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.customAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.chips && columnItem.chips ? (\n                        <bds-chip-tag color={item[`${columnItem.chips}`] ? item[`${columnItem.chips}`] : 'default'}>\n                          {item[`${columnItem.value}`]}\n                        </bds-chip-tag>\n                      ) : (\n                        ''\n                      )}\n                      {this.avatar && columnItem.img ? (\n                        <bds-avatar\n                          size=\"extra-small\"\n                          thumbnail={item[`${columnItem.img}`]}\n                          name={item[`${columnItem.value}`]}\n                        ></bds-avatar>\n                      ) : (\n                        ''\n                      )}\n                      {columnItem.chips ? (\n                        ''\n                      ) : (\n                        <bds-typo\n                          variant=\"fs-14\"\n                          bold={this.headerActive === `${columnItem.value}` ? 'bold' : 'regular'}\n                        >\n                          {item[`${columnItem.value}`]}\n                        </bds-typo>\n                      )}\n                    </td>\n                  );\n                })}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </Host>\n    );\n  }\n}\n"], "mappings": "k2DAAA,IAAMA,EAAe,2/C,ICURC,EAASC,EAAA,4BALtB,SAAAC,EAAAC,G,+IAOWC,KAAQC,SAAS,GAIjBD,KAAUE,WAAU,GAIpBF,KAASG,UAAY,GAoBtBH,KAAMI,OAAa,MAInBJ,KAAKK,MAAa,MAQlBL,KAAOM,QAAa,KA2J7B,CAtJCR,EAAAS,UAAAC,kBAAA,WACER,KAAKS,qB,EAGCX,EAAAS,UAAAE,oBAAA,WACNT,KAAKE,WAAaQ,KAAKC,MAAMX,KAAKY,QAClCZ,KAAKG,UAAYO,KAAKC,MAAMX,KAAKa,Q,EAGnCf,EAAAS,UAAAO,YAAA,SAAYC,GACV,GAAIA,EAAO,CACT,OAAOC,EAAA,YAAUC,KAAK,WAAWC,KAAK,S,KACjC,CACL,OAAO,I,GAKLpB,EAAAS,UAAAY,WAAN,SAAiBC,G,2FACTC,EAAarB,KAAKG,UAAUmB,QAAO,SAACC,EAAMC,GAAM,OAAAA,IAAMJ,GAASG,CAAf,IACtDvB,KAAKyB,eAAeC,KAAKL,EAAW,IACpCrB,KAAKG,UAAUwB,OAAOP,EAAO,GAC7BpB,KAAKG,UAASyB,cAAA,GAAO5B,KAAKG,UAAS,MACnCH,KAAK6B,eAAeH,KAAK1B,KAAKG,W,iBAGhCL,EAAAS,UAAAuB,YAAA,SAAYP,EAAMH,EAAOW,GACvB/B,KAAKgC,cAAcN,KAAK,CAAEH,KAAMA,EAAMH,MAAOA,EAAOa,WAAYF,G,EAGlEjC,EAAAS,UAAA2B,YAAA,SAAYC,GACVnC,KAAKoC,aAAeD,EACpBnC,KAAKqC,cAAgBrC,KAAKqC,cAAgB,MAAQ,KAElD,GAAIrC,KAAKqC,gBAAkB,MAAO,CAChCrC,KAAKG,UAAUmC,MAAK,SAAUC,EAAGC,GAC/B,OAAOD,EAAEJ,GAAOK,EAAEL,GAAO,GAAI,CAC/B,G,KACK,CACLnC,KAAKG,UAAUmC,MAAK,SAAUC,EAAGC,GAC/B,OAAOD,EAAEJ,GAAOK,EAAEL,IAAO,EAAK,CAChC,G,GAIJrC,EAAAS,UAAAkC,OAAA,eAAAC,EAAA1C,KACE,OACEgB,EAAC2B,EAAI,CAAAC,IAAA,4CACH5B,EAAO,SAAA4B,IAAA,2CAAAC,MAAM,SACX7B,EAAO,SAAA4B,IAAA,2CAAAC,MAAM,SACX7B,EAAI,MAAA4B,IAAA,2CAAAC,MAAM,UACP7C,KAAKE,WAAW4C,KAAI,SAACvB,EAAMH,GAAK,OAC/BJ,EAAA,MAAI6B,MAAM,eAAeD,IAAKxB,GAC3BsB,EAAKpC,QACJU,EAAA,YACE6B,MAAM,cACNE,QAAS,WAAM,OAAAL,EAAKR,YAAYX,EAAKR,MAAtB,EACfiC,QAAQ,QACRC,KAAMP,EAAKN,eAAiB,GAAAc,OAAG3B,EAAKR,OAAU,OAAS,aAEtDQ,EAAK4B,SAGRnC,EAAU,YAAAgC,QAAQ,QAAQC,KAAK,aAC5B1B,EAAK4B,SAGTT,EAAKL,gBAAkB,MAAQK,EAAKpC,UAAY,MAAQoC,EAAKN,eAAiB,GAAAc,OAAG3B,EAAKR,OACrFC,EAAA,YAAU6B,MAAM,cAAc5B,KAAK,WAAWC,KAAK,UACjDwB,EAAKL,gBAAkB,OAASK,EAAKpC,UAAY,MAAQoC,EAAKN,eAAiB,GAAAc,OAAG3B,EAAKR,OACzFC,EAAA,YAAUC,KAAK,aAAaC,KAAK,UAAmB,GAnBzB,MA2BrCF,EAAA,SAAA4B,IAAA,4CACG5C,KAAKG,UAAU2C,KAAI,SAACvB,EAAMH,GAAK,OAC9BJ,EAAA,MAAI6B,MAAM,WAAWD,IAAKxB,GACvBsB,EAAKxC,WAAW4C,KAAI,SAACM,EAAYjB,GAChC,OACEnB,EAAA,MAAI6B,MAAM,YAAYD,IAAKT,GACxBO,EAAKW,YAAcD,EAAWE,WAC7BtC,EACE,mBAAA+B,QAAS,WAAM,OAAAL,EAAKZ,YAAYP,EAAMH,EAAOgC,EAAWE,WAAzC,EACfN,QAAQ,YACRO,KAAMhC,EAAK,GAAA2B,OAAGE,EAAWE,aACzBpC,KAAK,UACY,GAIpBwB,EAAKW,YAAcD,EAAWI,aAC7BxC,EACE,mBAAA+B,QAAS,WAAM,OAAAL,EAAKZ,YAAYP,EAAMH,EAAOgC,EAAWI,aAAzC,EACfR,QAAQ,YACRO,KAAMhC,EAAK,GAAA2B,OAAGE,EAAWI,eACzBtC,KAAK,UACY,GAIpBwB,EAAKW,YAAcD,EAAWK,aAC7BzC,EACE,mBAAA+B,QAAS,WAAM,OAAAL,EAAKZ,YAAYP,EAAMH,EAAOgC,EAAWK,aAAzC,EACfT,QAAQ,YACRO,KAAMhC,EAAK,GAAA2B,OAAGE,EAAWK,eACzBvC,KAAK,UACY,GAIpBwB,EAAKrC,OAAS+C,EAAW/C,MACxBW,EAAc,gBAAA0C,MAAOnC,EAAK,GAAA2B,OAAGE,EAAW/C,QAAWkB,EAAK,GAAA2B,OAAGE,EAAW/C,QAAW,WAC9EkB,EAAK,GAAA2B,OAAGE,EAAWrC,SACP,GAIhB2B,EAAKtC,QAAUgD,EAAWO,IACzB3C,EACE,cAAAE,KAAK,cACL0C,UAAWrC,EAAK,GAAA2B,OAAGE,EAAWO,MAC9B1C,KAAMM,EAAK,GAAA2B,OAAGE,EAAWrC,UACb,GAIfqC,EAAW/C,MAAK,GAGfW,EACE,YAAAgC,QAAQ,QACRC,KAAMP,EAAKN,eAAiB,GAAAc,OAAGE,EAAWrC,OAAU,OAAS,WAE5DQ,EAAK,GAAA2B,OAAGE,EAAWrC,S,IA1DF,M,uHA9HtB,I", "ignoreList": []}