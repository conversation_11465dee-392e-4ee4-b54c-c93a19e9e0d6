{"version": 3, "names": ["autocompleteCss", "BdsAutocomplete", "exports", "class_1", "hostRef", "_this", "this", "intoView", "isPressed", "isOpen", "text", "textMultiselect", "placeholderState", "placeholder", "isFocused", "validationDanger", "validationMesage", "danger", "success", "disabled", "searchOnlyTitle", "label", "icon", "helperMessage", "errorMessage", "successMessage", "optionsPosition", "clearIconOnFocus", "dataTest", "loading", "selectionType", "selectionTitle", "<PERSON><PERSON><PERSON>", "refDropdown", "el", "dropElement", "refIconDrop", "iconDropElement", "refCheckAllInput", "input", "checkAllInput", "onFocus", "bdsFocus", "emit", "onFocusout", "nativeInput", "value", "getText", "onBlur", "bdsBlur", "cleanInputSelection", "_a", "checkedOptions", "length", "getTextMultiselect", "onClickWrapper", "toggle", "focus", "getTextFromOption", "opt", "internalOptions", "internalOption", "find", "option", "titleText", "innerText", "childOptions", "data", "valueInput", "concat", "handlerMultiselect", "updateListChecked", "undefined", "resetFilterOptions", "setTimeout", "checked", "handleCheckAll", "event", "detail", "_i", "_e", "toMark", "<PERSON><PERSON><PERSON>", "data_1", "classList", "add", "remove", "defaultCheckedOptions", "Array", "from", "filter", "item", "map", "term", "textContent", "handler", "__awaiter", "bdsCancel", "sent", "changedInputValue", "ev", "target", "bdsInput", "filterOptions", "setTimeoutFilter", "getSelectedValue", "prototype", "isOpenChanged", "positionHeightDrop", "name", "setDefaultPlacement", "validatePositionDrop", "itemSelectedChanged", "bdsSelectedChange", "selected", "valueChanged", "bdsChange", "toString", "childOptionSelected", "handleWindow", "contains", "changeCheckedOptions", "bdsMultiselectedChange", "parseOptions", "options", "JSON", "parse", "e", "changeSelectionType", "typeOption", "addEventListener", "componentWillLoad", "getScrollParent", "componentDidLoad", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "Object", "defineProperty", "shadowRoot", "querySelectorAll", "keyPressWrapper", "key", "nextS<PERSON>ling", "_b", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "previousSibling", "_d", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanMultipleSelection", "_f", "optionTextLowercase", "toLowerCase", "termLower", "includes", "removeAttribute", "setAttribute", "childOptions_1", "renderIcon", "h", "class", "input__icon", "size", "color", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "render", "Host", "select", "onClick", "tabindex", "input__container__wrapper", "input__container__text", "ref", "onInput", "type", "onKeyDown", "bind", "select__options", "refer", "onBdsChange", "idx", "onOptionSelected", "onOptionChecked", "bulkOption", "status"], "sources": ["src/components/autocomplete/autocomplete.scss?tag=bds-autocomplete&encapsulation=shadow", "src/components/autocomplete/autocomplete.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 250px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n  .input__container__text:placeholder-shown {\n    color: $color-content-ghost;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n    &__text::placeholder {\n      color: $color-content-ghost;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n  &__text::placeholder {\n    color: $color-content-ghost;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 8px;\n\n  .inside-input-left {\n    display: inline;\n  }\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n  flex-shrink: 99999;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n\n:host {\n  display: block;\n}\n\n.select {\n  position: relative;\n  outline: none;\n  overflow: hidden;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n\n    bds-icon {\n      margin-left: 10px;\n    }\n  }\n\n  .icon-hidden {\n    visibility: hidden;\n  }\n\n  &__options {\n    display: grid;\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    ::slotted(*) {\n      display: flex;\n      flex-flow: column;\n    }\n\n    .selection-title {\n      order: -2;\n      width: 100%;\n      padding: 8px 16px;\n      box-sizing: border-box;\n    }\n\n    .select-all {\n      order: -3;\n      padding: 8px 8px 8px 12px;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n    }\n\n    .content-divisor {\n      display: block;\n      width: 100%;\n      height: 1px;\n      background-color: $color-surface-1;\n\n      .divisor {\n        display: block;\n        margin: 0 16px;\n        height: 1px;\n        background-color: $color-border-2;\n      }\n    }\n\n    .load-spinner {\n      background-color: $color-surface-1;\n      height: 200px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n\n    .options-checked {\n      order: -1;\n    }\n  }\n}\n", "import { Component, h, Host, State, Prop, EventEmitter, Event, Watch, Element, Listen, Method } from '@stencil/core';\nimport {\n  AutocompleteOption,\n  AutocompleteChangeEventDetail,\n  AutocompleteSelectedChangeEventDetail,\n  AutocompleteOptionsPositionType,\n  AutocompleteMultiSelectedChangeEventDetail,\n} from './autocomplete-select-interface';\nimport { SelectOptionsPositionType } from '../selects/select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type SelectionType = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-autocomplete',\n  styleUrl: 'autocomplete.scss',\n  shadow: true,\n})\nexport class BdsAutocomplete {\n  private checkAllInput?: HTMLBdsCheckboxElement;\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() intoView?: HTMLElement = null;\n\n  @State() isPressed? = false;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  @State() textMultiselect? = '';\n\n  @State() placeholderState?: string = this.placeholder;\n\n  @State() internalOptions: AutocompleteOption[];\n\n  @State() cloneOptions: AutocompleteOption[];\n\n  @State() checkedOptions: AutocompleteOption[];\n\n  @State() isFocused?: boolean = false;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | AutocompleteOption[];\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null;\n\n  /**\n   * the item selected.\n   */\n  @Prop({ mutable: true }) selected?: HTMLBdsSelectOptionElement | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Search only the title property\n   */\n  @Prop({ reflect: true }) searchOnlyTitle? = true;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop() optionsPosition?: AutocompleteOptionsPositionType = 'auto';\n\n  /**\n   * If true, the X icon will appear only when component is focused.\n   */\n  @Prop() clearIconOnFocus?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Is Loading, is the prop to enable that the component is loading.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Multiselect, Prop to enable multi selections.\n   */\n  @Prop() selectionType?: SelectionType = 'single';\n\n  /**\n   * Selection Title, Prop to enable title to select.\n   */\n  @Prop() selectionTitle?: string = '';\n\n    /**\n   * Selection Title, Prop to enable title to select.\n   */\n    @Prop() selectedAll?: boolean = true;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsSelectedChange!: EventEmitter<AutocompleteSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsMultiselectedChange!: EventEmitter<AutocompleteMultiSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('selected')\n  itemSelectedChanged(): void {\n    this.bdsSelectedChange.emit(this.selected);\n  }\n\n  @Watch('value')\n  protected valueChanged(): void {\n    this.bdsChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n    this.selected = this.childOptionSelected;\n    this.text = this.getText();\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('checkedOptions')\n  protected changeCheckedOptions() {\n    this.placeholderState =\n      this.selectionType === 'multiple'\n        ? this.checkedOptions?.length === 0 || this.checkedOptions === null\n          ? this.placeholder\n          : ''\n        : this.placeholder;\n    this.getTextMultiselect(this.checkedOptions);\n    this.bdsMultiselectedChange.emit({ value: this.checkedOptions });\n  }\n\n  @Watch('options')\n  parseOptions() {\n    if (this.options) {\n      this.resetFilterOptions();\n      try {\n        this.internalOptions = typeof this.options === 'string' ? JSON.parse(this.options) : this.options;\n      } catch (e) {\n        this.internalOptions = [];\n      }\n    }\n  }\n\n  @Watch('selectionType')\n  protected changeSelectionType() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.intoView = getScrollParent(this.el);\n    this.options && this.parseOptions();\n  }\n\n  componentDidLoad() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n\n    this.text = this.getText();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: AutocompleteOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private refCheckAllInput = (input: HTMLBdsCheckboxElement): void => {\n    this.checkAllInput = input;\n  };\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  private onFocus = (): void => {\n    this.isFocused = true;\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  private onFocusout = (): void => {\n    if (!this.isOpen) {\n      this.nativeInput.value = this.getText();\n    }\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n    if (!this.isOpen) {\n      this.isFocused = false;\n      this.nativeInput.value = this.getText();\n      if (this.selectionType == 'multiple') this.cleanInputSelection();\n    }\n    if (this.selectionType == 'multiple' && this.checkedOptions?.length > 0)\n      this.getTextMultiselect(this.checkedOptions);\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.toggle();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.innerText ?? '');\n  };\n\n  private getText = (): string => {\n    const opt = this.childOptions.find((option) => option.value == this.value);\n    return this.getTextFromOption(opt);\n  };\n\n  private getTextMultiselect = (data): void => {\n    const valueInput = data?.length > 0 && `${data?.length} selecionados`;\n    this.textMultiselect = valueInput;\n  };\n\n  private handlerMultiselect = (): void => {\n    this.updateListChecked(this.childOptions);\n    this.nativeInput.value = '';\n    this.value = undefined;\n    this.resetFilterOptions();\n    if (this.childOptions.length != this.checkedOptions.length) {\n      setTimeout(() => {\n        this.checkAllInput.checked = false;\n      }, 10);\n    }\n  };\n\n  private handleCheckAll = (event: CustomEvent): void => {\n    const {\n      detail: { checked },\n    } = event;\n    for (const option of this.childOptions) {\n      if (checked) {\n        option.toMark();\n      } else {\n        option.markOff();\n      }\n    }\n    setTimeout(() => {\n      this.updateListChecked(this.childOptions);\n    }, 10);\n  };\n\n  private updateListChecked = (data: HTMLBdsSelectOptionElement[]): void => {\n    for (const option of data) {\n      option.checked ? option.classList.add('option-checked') : option.classList.remove('option-checked');\n    }\n    const defaultCheckedOptions = Array.from(data).filter((item) => item.checked == true);\n    const value = defaultCheckedOptions.map((term) => ({\n      value: term.value,\n      label: term.textContent,\n      checked: term.checked,\n    }));\n    this.checkedOptions = value;\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private cleanInputSelection = async () => {\n    if (!this.disabled) {\n      this.value = '';\n      this.nativeInput.value = '';\n      this.isOpen = false;\n      this.bdsCancel.emit({ value: '' });\n      await this.resetFilterOptions();\n    }\n  };\n\n  @Method()\n  async cleanMultipleSelection() {\n    if (this.selectionType === 'multiple' && this.checkedOptions?.length > 0) {\n      for (const option of this.childOptions) {\n        option.checked = false;\n        option.classList.remove('option-checked');\n      }\n      this.checkedOptions = [];\n      this.checkAllInput.checked = false;\n      this.nativeInput.value = '';\n      this.value = undefined;\n      this.resetFilterOptions();\n    } else {\n      this.cleanInputSelection();\n    }\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      this.value = '';\n      if (this.isOpen) {\n        await this.resetFilterOptions();\n      } else {\n        this.setTimeoutFilter();\n      }\n    }\n\n    if (this.isOpen === false) {\n      this.value = this.getSelectedValue();\n      this.setTimeoutFilter();\n    }\n  };\n\n  private setTimeoutFilter(): void {\n    setTimeout(() => {\n      this.resetFilterOptions();\n    }, 500);\n  }\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n    }\n\n    for (const option of this.childOptions) {\n      const optionTextLowercase = this.searchOnlyTitle\n        ? this.getTextFromOption(option).toLowerCase()\n        : option.textContent.toLowerCase();\n\n      const termLower = term.toLowerCase();\n\n      optionTextLowercase.includes(termLower)\n        ? option.removeAttribute('invisible')\n        : option.setAttribute('invisible', 'invisible');\n    }\n  }\n\n  private async resetFilterOptions() {\n    const childOptions = this.childOptions;\n    for (const option of childOptions) {\n      option.removeAttribute('invisible');\n    }\n  }\n\n  private getSelectedValue() {\n    return this.childOptionSelected?.value;\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            select: true,\n            'input--state-primary': !this.danger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': this.isPressed,\n          }}\n          onClick={this.onClickWrapper}\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\" tabindex=\"0\" onFocusout={this.onFocusout}>\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              {this.textMultiselect?.length > 0 && (\n                <bds-typo variant=\"fs-14\" class=\"inside-input-left\">\n                  {this.textMultiselect}\n                </bds-typo>\n              )}\n              <input\n                class={{ input__container__text: true }}\n                ref={(input) => (this.nativeInput = input)}\n                disabled={this.disabled}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.changedInputValue}\n                placeholder={this.placeholderState}\n                type=\"text\"\n                value={this.text}\n                data-test={this.dataTest}\n                onKeyDown={this.keyPressWrapper.bind(this)}\n              />\n            </div>\n          </div>\n          <div class=\"select__icon\">\n            <bds-icon\n              size=\"small\"\n              name=\"error\"\n              theme=\"solid\"\n              onClick={this.cleanInputSelection}\n              class={{\n                'icon-hidden': (this.clearIconOnFocus && (!this.isFocused || !this.isOpen)) || !this.value,\n              }}\n            ></bds-icon>\n            <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n          </div>\n        </div>\n        {this.renderMessage()}\n        {this.loading ? (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            <bds-loading-spinner class=\"load-spinner\" size=\"small\"></bds-loading-spinner>\n          </div>\n        ) : (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            {this.selectionTitle && this.selectionType == 'multiple' && (\n              <bds-typo class=\"selection-title\" variant=\"fs-10\" bold=\"bold\">\n                {this.selectionTitle}\n              </bds-typo>\n            )}\n            {this.selectionType == 'multiple' && this.selectedAll && (\n              <bds-checkbox\n                ref={this.refCheckAllInput}\n                refer={`refer-multiselect`}\n                label={`Selecionar Todos`}\n                name=\"chack-all\"\n                class=\"select-all\"\n                onBdsChange={(ev) => this.handleCheckAll(ev)}\n              ></bds-checkbox>\n            )}\n            {this.checkedOptions?.length > 0 && (\n              <span class=\"content-divisor\">\n                <span class=\"divisor\"></span>\n              </span>\n            )}\n            {this.internalOptions ? (\n              this.internalOptions.map((option, idx) => (\n                <bds-select-option\n                  onOptionSelected={this.handler}\n                  onOptionChecked={this.handlerMultiselect}\n                  selected={this.value === option.value}\n                  value={option.value}\n                  key={idx}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                  type-option={this.selectionType == 'multiple' ? 'checkbox' : 'default'}\n                >\n                  {option.label}\n                </bds-select-option>\n              ))\n            ) : (\n              <slot />\n            )}\n          </div>\n        )}\n      </Host>\n    );\n  }\n}"], "mappings": "sqDAAA,IAAMA,EAAkB,61c,ICkBXC,EAAeC,EAAA,8BAL5B,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,iSAiBWA,KAAQC,SAAiB,KAEzBD,KAASE,UAAI,MAEbF,KAAMG,OAAI,MAEVH,KAAII,KAAI,GAERJ,KAAeK,gBAAI,GAEnBL,KAAAM,iBAA4BN,KAAKO,YAQjCP,KAASQ,UAAa,MAKtBR,KAAgBS,iBAAa,MAK7BT,KAAgBU,iBAAI,GAuBJV,KAAMW,OAAI,MAKKX,KAAOY,QAAa,MAInCZ,KAAQa,SAAI,MAKZb,KAAec,gBAAI,KAKpCd,KAAKe,MAAI,GAKQf,KAAIgB,KAAY,GAKjChB,KAAWO,YAAY,GAKvBP,KAAaiB,cAAY,GAIzBjB,KAAYkB,aAAY,GAIPlB,KAAcmB,eAAY,GAI3CnB,KAAeoB,gBAAqC,OAKpDpB,KAAgBqB,iBAAa,MAK7BrB,KAAQsB,SAAY,KAKpBtB,KAAOuB,QAAa,MAKpBvB,KAAawB,cAAmB,SAKhCxB,KAAcyB,eAAY,GAKxBzB,KAAW0B,YAAa,KAuK1B1B,KAAA2B,YAAc,SAACC,GACrB7B,EAAK8B,YAAcD,CACrB,EAEQ5B,KAAA8B,YAAc,SAACF,GACrB7B,EAAKgC,gBAAkBH,CACzB,EAEQ5B,KAAAgC,iBAAmB,SAACC,GAC1BlC,EAAKmC,cAAgBD,CACvB,EAcQjC,KAAOmC,QAAG,WAChBpC,EAAKS,UAAY,KACjBT,EAAKG,UAAY,KACjBH,EAAKqC,SAASC,MAChB,EAEQrC,KAAUsC,WAAG,WACnB,IAAKvC,EAAKI,OAAQ,CAChBJ,EAAKwC,YAAYC,MAAQzC,EAAK0C,S,CAElC,EAEQzC,KAAM0C,OAAG,W,MACf3C,EAAK4C,QAAQN,OACbtC,EAAKG,UAAY,MACjB,IAAKH,EAAKI,OAAQ,CAChBJ,EAAKS,UAAY,MACjBT,EAAKwC,YAAYC,MAAQzC,EAAK0C,UAC9B,GAAI1C,EAAKyB,eAAiB,WAAYzB,EAAK6C,qB,CAE7C,GAAI7C,EAAKyB,eAAiB,cAAcqB,EAAA9C,EAAK+C,kBAAgB,MAAAD,SAAA,SAAAA,EAAAE,QAAS,EACpEhD,EAAKiD,mBAAmBjD,EAAK+C,eACjC,EAEQ9C,KAAciD,eAAG,WACvBlD,EAAKoC,UACLpC,EAAKmD,SACL,GAAInD,EAAKwC,YAAa,CACpBxC,EAAKwC,YAAYY,O,CAErB,EAEQnD,KAAMkD,OAAG,WACf,IAAKnD,EAAKc,SAAU,CAClBd,EAAKI,QAAUJ,EAAKI,M,CAExB,EAEQH,KAAAoD,kBAAoB,SAACC,G,MAC3B,GAAItD,EAAKuD,gBAAiB,CACxB,IAAMC,EAAiBxD,EAAKuD,gBAAgBE,MAAK,SAACC,GAAW,OAAAA,EAAOjB,QAASa,IAAA,MAAAA,SAAA,SAAAA,EAAKb,MAArB,IAC7D,GAAIe,EAAgB,CAClB,OAAOA,EAAexC,K,EAG1B,OAAOsC,IAAA,MAAAA,SAAA,SAAAA,EAAKK,WAAYL,EAAIK,WAAab,EAAAQ,IAAA,MAAAA,SAAA,SAAAA,EAAKM,aAAa,MAAAd,SAAA,EAAAA,EAAA,EAC7D,EAEQ7C,KAAOyC,QAAG,WAChB,IAAMY,EAAMtD,EAAK6D,aAAaJ,MAAK,SAACC,GAAW,OAAAA,EAAOjB,OAASzC,EAAKyC,KAArB,IAC/C,OAAOzC,EAAKqD,kBAAkBC,EAChC,EAEQrD,KAAAgD,mBAAqB,SAACa,GAC5B,IAAMC,GAAaD,IAAI,MAAJA,SAAI,SAAJA,EAAMd,QAAS,GAAK,GAAAgB,OAAGF,IAAA,MAAAA,SAAI,SAAJA,EAAMd,OAAM,iBACtDhD,EAAKM,gBAAkByD,CACzB,EAEQ9D,KAAkBgE,mBAAG,WAC3BjE,EAAKkE,kBAAkBlE,EAAK6D,cAC5B7D,EAAKwC,YAAYC,MAAQ,GACzBzC,EAAKyC,MAAQ0B,UACbnE,EAAKoE,qBACL,GAAIpE,EAAK6D,aAAab,QAAUhD,EAAK+C,eAAeC,OAAQ,CAC1DqB,YAAW,WACTrE,EAAKmC,cAAcmC,QAAU,K,GAC5B,G,CAEP,EAEQrE,KAAAsE,eAAiB,SAACC,GAEZ,IAAAF,EACRE,EAAKC,OAAAH,QACT,IAAqB,IAAAI,EAAA,EAAAC,EAAA3E,EAAK6D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAAnC,IAAMhB,EAAMiB,EAAAD,GACf,GAAIJ,EAAS,CACXZ,EAAOkB,Q,KACF,CACLlB,EAAOmB,S,EAGXR,YAAW,WACTrE,EAAKkE,kBAAkBlE,EAAK6D,a,GAC3B,GACL,EAEQ5D,KAAAiE,kBAAoB,SAACJ,GAC3B,IAAqB,IAAAY,EAAA,EAAAI,EAAAhB,EAAAY,EAAAI,EAAA9B,OAAA0B,IAAM,CAAtB,IAAMhB,EAAMoB,EAAAJ,GACfhB,EAAOY,QAAUZ,EAAOqB,UAAUC,IAAI,kBAAoBtB,EAAOqB,UAAUE,OAAO,iB,CAEpF,IAAMC,EAAwBC,MAAMC,KAAKtB,GAAMuB,QAAO,SAACC,GAAS,OAAAA,EAAKhB,SAAW,IAAhB,IAChE,IAAM7B,EAAQyC,EAAsBK,KAAI,SAACC,GAAI,OAC3C/C,MAAO+C,EAAK/C,MACZzB,MAAOwE,EAAKC,YACZnB,QAASkB,EAAKlB,QAH6B,IAK7CtE,EAAK+C,eAAiBN,CACxB,EAEQxC,KAAAyF,QAAU,SAAClB,GAEL,IAAA/B,EACR+B,EAAKC,OAAAhC,MACTzC,EAAKyC,MAAQA,EACbzC,EAAKmD,QACP,EA2BQlD,KAAmB4C,oBAAG,kBAAA8C,UAAA3F,OAAA,qB,iEACvBC,KAAKa,SAAN,YACFb,KAAKwC,MAAQ,GACbxC,KAAKuC,YAAYC,MAAQ,GACzBxC,KAAKG,OAAS,MACdH,KAAK2F,UAAUtD,KAAK,CAAEG,MAAO,KAC7B,SAAMxC,KAAKmE,sB,OAAXO,EAAAkB,O,mCAqBI5F,KAAA6F,kBAAoB,SAAOC,GAAc,OAAAJ,UAAA3F,OAAA,qB,kEACzCkC,EAAQ6D,EAAGC,OACjB,GAAI9D,EAAO,CACTjC,KAAKwC,MAAQP,EAAMO,OAAS,E,CAE9BxC,KAAKgG,SAAS3D,KAAKyD,G,IACf9F,KAAKuC,YAAYC,MAAjB,YACF,SAAMxC,KAAKiG,cAAcjG,KAAKuC,YAAYC,Q,OAA1CkC,EAAAkB,O,mBAEA5F,KAAKwC,MAAQ,G,IACTxC,KAAKG,OAAL,YACF,SAAMH,KAAKmE,sB,OAAXO,EAAAkB,O,mBAEA5F,KAAKkG,mB,iBAIT,GAAIlG,KAAKG,SAAW,MAAO,CACzBH,KAAKwC,MAAQxC,KAAKmG,mBAClBnG,KAAKkG,kB,kBAuNV,CAliBWrG,EAAAuG,UAAAC,cAAA,SAAclG,GACtB,GAAIH,KAAKsG,oBAAsB,SAAU,CACvCtG,KAAK+B,gBAAgBwE,KAAOvG,KAAKG,OAAS,WAAa,Y,KAClD,CACLH,KAAK+B,gBAAgBwE,KAAOvG,KAAKG,OAAS,aAAe,U,CAE3D,GAAIA,EACF,GAAIH,KAAKoB,iBAAmB,OAAQ,CAClCpB,KAAKwG,oBAAoBxG,KAAKoB,gB,KACzB,CACLpB,KAAKyG,sB,GAKX5G,EAAAuG,UAAAM,oBAAA,WACE1G,KAAK2G,kBAAkBtE,KAAKrC,KAAK4G,S,EAIzB/G,EAAAuG,UAAAS,aAAA,WACR7G,KAAK8G,UAAUzE,KAAK,CAAEG,MAAOxC,KAAKwC,OAAS,KAAOxC,KAAKwC,MAAQxC,KAAKwC,MAAMuE,aAC1E,IAAqB,IAAAtC,EAAA,EAAAC,EAAA1E,KAAK4D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAAnC,IAAMhB,EAAMiB,EAAAD,GACfhB,EAAOmD,SAAW5G,KAAKwC,QAAUiB,EAAOjB,K,CAE1CxC,KAAK4G,SAAW5G,KAAKgH,oBACrBhH,KAAKI,KAAOJ,KAAKyC,S,EAInB5C,EAAAuG,UAAAa,aAAA,SAAanB,GACX,IAAK9F,KAAK4B,GAAGsF,SAASpB,EAAGC,QAA6B,CACpD/F,KAAKG,OAAS,K,GAKRN,EAAAuG,UAAAe,qBAAA,W,MACRnH,KAAKM,iBACHN,KAAKwB,gBAAkB,aACnBqB,EAAA7C,KAAK8C,kBAAc,MAAAD,SAAA,SAAAA,EAAEE,UAAW,GAAK/C,KAAK8C,iBAAmB,KAC3D9C,KAAKO,YACL,GACFP,KAAKO,YACXP,KAAKgD,mBAAmBhD,KAAK8C,gBAC7B9C,KAAKoH,uBAAuB/E,KAAK,CAAEG,MAAOxC,KAAK8C,gB,EAIjDjD,EAAAuG,UAAAiB,aAAA,WACE,GAAIrH,KAAKsH,QAAS,CAChBtH,KAAKmE,qBACL,IACEnE,KAAKsD,uBAAyBtD,KAAKsH,UAAY,SAAWC,KAAKC,MAAMxH,KAAKsH,SAAWtH,KAAKsH,O,CAC1F,MAAOG,GACPzH,KAAKsD,gBAAkB,E,IAMnBzD,EAAAuG,UAAAsB,oBAAA,WACR,IAAK1H,KAAKsH,QAAS,CACjB,IAAqB,IAAA7C,EAAA,EAAAC,EAAA1E,KAAK4D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAAnC,IAAMhB,EAAMiB,EAAAD,GACf,GAAIzE,KAAKwB,gBAAkB,WAAY,CACrCiC,EAAOkE,WAAa,WACpBlE,EAAOmE,iBAAiB,gBAAiB5H,KAAKgE,mB,KACzC,CACLP,EAAOkE,WAAa,UACpBlE,EAAOmD,SAAW5G,KAAKwC,QAAUiB,EAAOjB,MACxCiB,EAAOmE,iBAAiB,iBAAkB5H,KAAKyF,Q,KAMvD5F,EAAAuG,UAAAyB,kBAAA,WACE7H,KAAKC,SAAW6H,EAAgB9H,KAAK4B,IACrC5B,KAAKsH,SAAWtH,KAAKqH,c,EAGvBxH,EAAAuG,UAAA2B,iBAAA,WACE,IAAK/H,KAAKsH,QAAS,CACjB,IAAqB,IAAA7C,EAAA,EAAAC,EAAA1E,KAAK4D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAAnC,IAAMhB,EAAMiB,EAAAD,GACf,GAAIzE,KAAKwB,gBAAkB,WAAY,CACrCiC,EAAOkE,WAAa,WACpBlE,EAAOmE,iBAAiB,gBAAiB5H,KAAKgE,mB,KACzC,CACLP,EAAOkE,WAAa,UACpBlE,EAAOmD,SAAW5G,KAAKwC,QAAUiB,EAAOjB,MACxCiB,EAAOmE,iBAAiB,iBAAkB5H,KAAKyF,Q,GAKrDzF,KAAKI,KAAOJ,KAAKyC,UACjB,GAAIzC,KAAKoB,iBAAmB,OAAQ,CAClCpB,KAAKwG,oBAAoBxG,KAAKoB,gB,KACzB,CACLpB,KAAKyG,sB,GAID5G,EAAAuG,UAAAI,oBAAA,SAAoBhE,GAC1B,GAAIA,GAAS,SAAU,CACrBxC,KAAK6B,YAAYiD,UAAUC,IAAI,oCAC/B/E,KAAK+B,gBAAgBwE,KAAO,Y,KACvB,CACLvG,KAAK6B,YAAYiD,UAAUC,IAAI,iCAC/B/E,KAAK+B,gBAAgBwE,KAAO,U,GAIxB1G,EAAAuG,UAAAK,qBAAA,WACN,IAAMuB,EAAgBC,EAAwB,CAC5CC,cAAelI,KAAK4B,GACpBuG,eAAgBnI,KAAK6B,YACrB5B,SAAUD,KAAKC,WAEjBD,KAAKsG,mBAAqB0B,EAAcI,EACxC,GAAIJ,EAAcI,GAAK,SAAU,CAC/BpI,KAAK6B,YAAYiD,UAAUC,IAAI,oCAC/B/E,KAAK+B,gBAAgBwE,KAAO,Y,KACvB,CACLvG,KAAK6B,YAAYiD,UAAUC,IAAI,iCAC/B/E,KAAK+B,gBAAgBwE,KAAO,U,GAgBhC8B,OAAAC,eAAYzI,EAAAuG,UAAA,eAAY,C,IAAxB,WACE,OAAOpG,KAAKsH,QACRpC,MAAMC,KAAKnF,KAAK4B,GAAG2G,WAAWC,iBAAiB,sBAC/CtD,MAAMC,KAAKnF,KAAK4B,GAAG4G,iBAAiB,qB,uCAG1CH,OAAAC,eAAYzI,EAAAuG,UAAA,sBAAmB,C,IAA/B,WACE,OAAOpG,KAAKsH,QACRpC,MAAMC,KAAKnF,KAAK4B,GAAG2G,WAAWC,iBAAiB,sBAAsBhF,MAAK,SAACC,GAAW,OAAAA,EAAOmD,QAAP,IACtF1B,MAAMC,KAAKnF,KAAK4B,GAAG4G,iBAAiB,sBAAsBhF,MAAK,SAACC,GAAW,OAAAA,EAAOmD,QAAP,G,uCA8GzE/G,EAAAuG,UAAAqC,gBAAA,SAAgBlE,G,YACtB,OAAQA,EAAMmE,KACZ,IAAK,QACH1I,KAAKkD,SACL,MACF,IAAK,YACH,IAAKlD,KAAKa,SAAU,CAClBb,KAAKG,OAAS,I,CAEhB,GAAIH,KAAKgH,oBAAqB,CAC5BhH,KAAKwC,OAASK,EAAA7C,KAAKgH,oBAAoB2B,eAA4C,MAAA9F,SAAA,SAAAA,EAAAL,MACnF,M,CAEFxC,KAAKwC,OAASoG,EAAA5I,KAAK4B,GAAGiH,qBAAkD,MAAAD,SAAA,SAAAA,EAAApG,MACxE,MACF,IAAK,UACH,GAAIxC,KAAKgH,oBAAqB,CAC5BhH,KAAKwC,OAASsG,EAAA9I,KAAKgH,oBAAoB+B,mBAAgD,MAAAD,SAAA,SAAAA,EAAAtG,MACvF,M,CAEFxC,KAAKwC,OAASwG,EAAAhJ,KAAK4B,GAAGqH,oBAAiD,MAAAD,SAAA,SAAAA,EAAAxG,MACvE,M,EAeA3C,EAAAuG,UAAA8C,uBAAN,W,iGACE,GAAIlJ,KAAKwB,gBAAkB,cAAcqB,EAAA7C,KAAK8C,kBAAgB,MAAAD,SAAA,SAAAA,EAAAE,QAAS,EAAG,CACxE,IAAA0B,EAAA,EAAqBC,EAAA1E,KAAK4D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAA7BhB,EAAMiB,EAAAD,GACfhB,EAAOY,QAAU,MACjBZ,EAAOqB,UAAUE,OAAO,iB,CAE1BhF,KAAK8C,eAAiB,GACtB9C,KAAKkC,cAAcmC,QAAU,MAC7BrE,KAAKuC,YAAYC,MAAQ,GACzBxC,KAAKwC,MAAQ0B,UACblE,KAAKmE,oB,KACA,CACLnE,KAAK4C,qB,kBA2BD/C,EAAAuG,UAAAF,iBAAA,eAAAnG,EAAAC,KACNoE,YAAW,WACTrE,EAAKoE,oB,GACJ,I,EAGStE,EAAAuG,UAAAH,cAAN,SAAoBV,G,+HACrBA,EAAD,YACF,SAAMvF,KAAKmE,sB,OAAXgF,EAAAvD,O,iBAGF,IAAAnB,EAAA,EAAqBC,EAAA1E,KAAK4D,aAALa,EAAAC,EAAA3B,OAAA0B,IAAmB,CAA7BhB,EAAMiB,EAAAD,GACT2E,EAAsBpJ,KAAKc,gBAC7Bd,KAAKoD,kBAAkBK,GAAQ4F,cAC/B5F,EAAO+B,YAAY6D,cAEjBC,EAAY/D,EAAK8D,cAEvBD,EAAoBG,SAASD,GACzB7F,EAAO+F,gBAAgB,aACvB/F,EAAOgG,aAAa,YAAa,Y,mBAI3B5J,EAAAuG,UAAAjC,mBAAN,W,iGACAP,EAAe5D,KAAK4D,aAC1B,IAAAa,EAAA,EAAqBiF,EAAA9F,EAAAa,EAAAiF,EAAA3G,OAAA0B,IAAc,CAAxBhB,EAAMiG,EAAAjF,GACfhB,EAAO+F,gBAAgB,Y,kBAInB3J,EAAAuG,UAAAD,iBAAA,W,MACN,OAAOtD,EAAA7C,KAAKgH,uBAAmB,MAAAnE,SAAA,SAAAA,EAAEL,K,EAG3B3C,EAAAuG,UAAAuD,WAAA,WACN,OACE3J,KAAKgB,MACH4I,EAAA,OACEC,MAAO,CACLC,YAAa,KACb,uBAAwB9J,KAAKe,QAG/B6I,EAAU,YAAAG,KAAM/J,KAAKe,MAAQ,SAAW,QAASwF,KAAMvG,KAAKgB,KAAMgJ,MAAM,Y,EAMxEnK,EAAAuG,UAAA6D,YAAA,WACN,OACEjK,KAAKe,OACH6I,EAAA,SACEC,MAAO,CACLK,wBAAyB,KACzB,mCAAoClK,KAAKE,YAAcF,KAAKa,WAG9D+I,EAAA,YAAUO,QAAQ,QAAQC,KAAK,QAC5BpK,KAAKe,O,EAORlB,EAAAuG,UAAAiE,cAAA,WACN,IAAMrJ,EAAOhB,KAAKW,OAAS,QAAUX,KAAKY,QAAU,YAAc,OAClE,IAAI0J,EAAUtK,KAAKW,OAASX,KAAKkB,aAAelB,KAAKY,QAAUZ,KAAKmB,eAAiBnB,KAAKiB,cAE1F,IAAKqJ,GAAWtK,KAAKS,iBAAkB6J,EAAUtK,KAAKU,iBAEtD,IAAM6J,EACJvK,KAAKW,QAAUX,KAAKS,iBAChB,wCACAT,KAAKY,QACH,yCACA,iBAER,GAAI0J,EAAS,CACX,OACEV,EAAA,OAAKC,MAAOU,EAAQC,KAAK,kBACvBZ,EAAK,OAAAC,MAAM,wBACTD,EAAA,YAAUG,KAAK,UAAUxD,KAAMvF,EAAMyJ,MAAM,UAAUT,MAAM,aAE7DJ,EAAA,YAAUC,MAAM,uBAAuBM,QAAQ,SAC5CG,G,CAMT,OAAOpG,S,EAGTrE,EAAAuG,UAAAsE,OAAA,eAAA3K,EAAAC,K,QACE,OACE4J,EAACe,EAAI,CAAAjC,IAAA,2DAAgB1I,KAAKa,SAAW,OAAS,MAC5C+I,EAAA,OAAAlB,IAAA,2CACEmB,MAAO,CACL5H,MAAO,KACP2I,OAAQ,KACR,wBAAyB5K,KAAKW,OAC9B,sBAAuBX,KAAKW,QAAUX,KAAKS,iBAC3C,uBAAwBT,KAAKY,QAC7B,wBAAyBZ,KAAKa,SAC9B,iBAAkBb,KAAKe,MACvB,iBAAkBf,KAAKE,WAEzB2K,QAAS7K,KAAKiD,gBAEbjD,KAAK2J,aACNC,EAAA,OAAAlB,IAAA,2CAAKmB,MAAM,mBAAmBiB,SAAS,IAAIxI,WAAYtC,KAAKsC,YACzDtC,KAAKiK,cACNL,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEkB,0BAA2B,SACtClI,EAAA7C,KAAKK,mBAAe,MAAAwC,SAAA,SAAAA,EAAEE,QAAS,GAC9B6G,EAAA,YAAAlB,IAAA,2CAAUyB,QAAQ,QAAQN,MAAM,qBAC7B7J,KAAKK,iBAGVuJ,EAAA,SAAAlB,IAAA,2CACEmB,MAAO,CAAEmB,uBAAwB,MACjCC,IAAK,SAAChJ,GAAK,OAAMlC,EAAKwC,YAAcN,CAAzB,EACXpB,SAAUb,KAAKa,SACf6B,OAAQ1C,KAAK0C,OACbP,QAASnC,KAAKmC,QACd+I,QAASlL,KAAK6F,kBACdtF,YAAaP,KAAKM,iBAClB6K,KAAK,OACL3I,MAAOxC,KAAKI,KAAI,YACLJ,KAAKsB,SAChB8J,UAAWpL,KAAKyI,gBAAgB4C,KAAKrL,UAI3C4J,EAAK,OAAAlB,IAAA,2CAAAmB,MAAM,gBACTD,EAAA,YAAAlB,IAAA,2CACEqB,KAAK,QACLxD,KAAK,QACLkE,MAAM,QACNI,QAAS7K,KAAK4C,oBACdiH,MAAO,CACL,cAAgB7J,KAAKqB,oBAAsBrB,KAAKQ,YAAcR,KAAKG,UAAaH,KAAKwC,SAGzFoH,EAAU,YAAAlB,IAAA,2CAAAuC,IAAK,SAACrJ,GAAO,OAAA7B,EAAK+B,YAAYF,EAAjB,EAAsBmI,KAAK,QAAQC,MAAM,cAGnEhK,KAAKqK,gBACLrK,KAAKuB,QACJqI,EAAA,OACEqB,IAAK,SAACrJ,GAAO,OAAA7B,EAAK4B,YAAYC,EAAjB,EACbiI,MAAO,CACLyB,gBAAiB,KACjB,wBAAyBtL,KAAKG,SAGhCyJ,EAAA,uBAAqBC,MAAM,eAAeE,KAAK,WAGjDH,EAAA,OACEqB,IAAK,SAACrJ,GAAO,OAAA7B,EAAK4B,YAAYC,EAAjB,EACbiI,MAAO,CACLyB,gBAAiB,KACjB,wBAAyBtL,KAAKG,SAG/BH,KAAKyB,gBAAkBzB,KAAKwB,eAAiB,YAC5CoI,EAAA,YAAUC,MAAM,kBAAkBM,QAAQ,QAAQC,KAAK,QACpDpK,KAAKyB,gBAGTzB,KAAKwB,eAAiB,YAAcxB,KAAK0B,aACxCkI,EAAA,gBACEqB,IAAKjL,KAAKgC,iBACVuJ,MAAO,oBACPxK,MAAO,mBACPwF,KAAK,YACLsD,MAAM,aACN2B,YAAa,SAAC1F,GAAO,OAAA/F,EAAKuE,eAAewB,EAApB,MAGxB8C,EAAA5I,KAAK8C,kBAAgB,MAAA8F,SAAA,SAAAA,EAAA7F,QAAS,GAC7B6G,EAAM,QAAAC,MAAM,mBACVD,EAAA,QAAMC,MAAM,aAGf7J,KAAKsD,gBACJtD,KAAKsD,gBAAgBgC,KAAI,SAAC7B,EAAQgI,GAAG,OACnC7B,EAAA,qBACE8B,iBAAkB3L,EAAK0F,QACvBkG,gBAAiB5L,EAAKiE,mBACtB4C,SAAU7G,EAAKyC,QAAUiB,EAAOjB,MAChCA,MAAOiB,EAAOjB,MACdkG,IAAK+C,EACLG,WAAYnI,EAAOmI,WACnBC,OAAQpI,EAAOoI,OACF,cAAA9L,EAAKyB,eAAiB,WAAa,WAAa,WAE5DiC,EAAO1C,MAXyB,IAerC6I,EAAA,c,gZA5sBc,I", "ignoreList": []}