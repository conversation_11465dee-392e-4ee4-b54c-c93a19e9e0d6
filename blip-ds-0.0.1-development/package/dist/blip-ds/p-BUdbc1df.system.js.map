{"version": 3, "file": "p-BUdbc1df.system.js", "sources": ["src/components/table/table-body/table-body.scss?tag=bds-table-body&encapsulation=scoped", "src/components/table/table-body/table-body.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n    display: table-row-group;\n    height: 64px;\n  }\n\n  :host(.multiple) {\n    border-bottom: 1px solid $color-border-2;\n  }\n  \n  :host:last-child {\n    border-bottom: none;\n  }", "import { Component, h, Host, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-body',\n  styleUrl: 'table-body.scss',\n  scoped: true,\n})\nexport class TableBody {\n  @Element() private element: HTMLElement;\n  @State() multipleRows = false;\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('collapse') === 'true' || bdsTable.collapse === true)) {\n      this.multipleRows = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ host: true, multiple: this.multipleRows }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,YAAY,GAAG,+MAA+M;;YCOvN,SAAS,6BAAA,MAAA;MALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;MAOW,QAAA,IAAY,CAAA,YAAA,GAAG,KAAK;MAgB9B;UAdC,iBAAiB,GAAA;cACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;MAClD,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,EAAE;MAC5F,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;UAI5B,MAAM,GAAA;MACJ,QAAA,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,EAAA,EACtD,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;;;;;"}