import{r as e,c as i,h as a,a as t}from"./p-C3J6Z5OX.js";const r=[{uploaded:"Arquivos enviados",dropHere:"Solte aqui para anexar o arquivo",dropOrClick:"Arraste e solte seus arquivos aqui ou clique para fazer upload do arquivo",formatError:"Ocorreu um erro ao anexar o arquivo, tente novamente ou selecione outro arquivo"}];const o=[{uploaded:"Archivos subidos",dropHere:"Soltar aquí para adjuntar archivo",dropOrClick:"Arrastre y suelte sus archivos aquí o haga clic para cargar el archivo",formatError:"Se produjo un error al adjuntar el archivo, inténtelo nuevamente o seleccione otro archivo"}];const s=[{uploaded:"Files uploaded",dropHere:"Drop here to attach file",dropOrClick:"Drag and drop your files here or click to upload file",formatError:"There was an error attaching the file, please try again or select another file"}];const l=(e,i)=>{let a;switch(e){case"pt_BR":a=r.map((e=>e[i]));break;case"es_ES":a=o.map((e=>e[i]));break;case"en_US":a=s.map((e=>e[i]));break;default:a=r.map((e=>e[i]))}return a};const d="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzg0IiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMzg0IDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfMzc4OF8yMTU2NDApIj4KPGxpbmUgeDE9Ii0xMC45NzY3IiB5MT0iNzQuMzg0MyIgeDI9IjIyLjc3NzgiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjIyLjc3NzciIHkxPSI3NC4zODQzIiB4Mj0iNTYuNTMyMiIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iNTYuNTMyIiB5MT0iNzQuMzg0MyIgeDI9IjkwLjI4NjYiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjkwLjI4NjkiIHkxPSI3NC4zODQzIiB4Mj0iMTI0LjA0MiIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMTI0LjA0MSIgeTE9Ijc0LjM4NDMiIHgyPSIxNTcuNzk2IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjxsaW5lIHgxPSIxNTcuNzk2IiB5MT0iNzQuMzg0MyIgeDI9IjE5MS41NTEiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjE5MS41NTEiIHkxPSI3NC4zODQzIiB4Mj0iMjI1LjMwNSIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMjI1LjMwNSIgeTE9Ijc0LjM4NDMiIHgyPSIyNTkuMDYiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjI1OS4wNiIgeTE9Ijc0LjM4NDMiIHgyPSIyOTIuODE0IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjxsaW5lIHgxPSIyOTIuODE0IiB5MT0iNzQuMzg0MyIgeDI9IjMyNi41NjkiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjMyNi41NjkiIHkxPSI3NC4zODQzIiB4Mj0iMzYwLjMyMyIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMzYwLjMyNCIgeTE9Ijc0LjM4NDMiIHgyPSIzOTQuMDc4IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjwvZz4KPGRlZnM+CjxjbGlwUGF0aCBpZD0iY2xpcDBfMzc4OF8yMTU2NDAiPgo8cmVjdCB3aWR0aD0iMzg0IiBoZWlnaHQ9IjgwIiBmaWxsPSJ3aGl0ZSIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo=";const c='.upload{min-width:400px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;gap:16px}.upload .upload-header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;-ms-flex-align:center;align-items:center;gap:8px;color:var(--color-content-default, rgb(40, 40, 40))}.upload .upload-header_text{color:var(--color-content-default, rgb(40, 40, 40));display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.upload__edit--label{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:8px;cursor:pointer;font-weight:normal;-webkit-box-sizing:border-box;box-sizing:border-box;padding:23px 16px;position:relative}.upload__edit--label::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.upload__edit--label:focus-visible{outline:none}.upload__edit--label:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.upload__edit--label .upload__img--visible{display:-ms-flexbox;display:flex;width:100%;height:100%;border-radius:8px;position:absolute;background-color:var(--color-surface-2, rgb(237, 237, 237));z-index:1}.upload__edit--label .text-box{display:-ms-flexbox;display:flex;padding:8px;width:100%;text-align:center;z-index:2}.upload__edit--label .text-box .text{color:var(--color-content-default, rgb(40, 40, 40));width:100%;-ms-flex-wrap:wrap;flex-wrap:wrap}.upload__edit--label .text-box--hover{background-color:var(--color-surface-2, rgb(237, 237, 237))}.upload__edit--label .text-box--hover .text{color:var(--color-primary, rgb(30, 107, 241))}.upload__edit--label:hover{border:2px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;padding:22px 16px;cursor:pointer;-webkit-text-decoration:underline var(--color-primary, rgb(30, 107, 241));text-decoration:underline var(--color-primary, rgb(30, 107, 241));color:var(--color-brand, rgb(0, 150, 250))}.upload__edit--label:hover .text{color:var(--color-primary, rgb(30, 107, 241))}.upload__edit--hover{background-size:cover;border:1px dashed var(--color-surface-4, rgb(20, 20, 20));color:var(--color-primary, rgb(30, 107, 241));font-weight:bold;border-radius:8px}.upload__img--invisible{display:none}.list-preview{border-top:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));max-height:200px;overflow-y:auto}.upload__preview{-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;padding:16px 0}.upload__preview .preview{display:-ms-flexbox;display:flex;padding:0 16px;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;gap:8px}.upload__preview .preview-text{font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;font-weight:700;margin:0;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:100%;color:var(--color-content-default, rgb(40, 40, 40))}.upload__preview .preview-icon{color:var(--color-content-default, rgb(40, 40, 40))}.upload__preview .preview-icon:hover{cursor:pointer}.preview-length{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:end;padding-top:16px;text-align:end}.upload__edit input{position:absolute;left:0;top:0;right:0;bottom:0;opacity:0;width:0;height:100%}';const n=class{constructor(a){e(this,a);this.bdsUploadDelete=i(this,"bdsUploadDelete");this.bdsUploadChange=i(this,"bdsUploadChange");this.files=[];this.haveFiles=false;this.hover=false;this.size=[];this.internalAccepts=[];this.formatError=false;this.language="pt_BR";this.dataAccept=[];this.dtInputFiles=null;this.dtLabelAddFile=null;this.dtButtonDelete=null;this.validationFiles=(e,i)=>{const a=`.${e.name.split(".").pop()}`;const t=this.internalAccepts.includes(a);if(t){this.formatError=false;return}else{this.formatError=true;this.deleteFile(i);return}};this.handleDrop=e=>{this.haveFiles=true;const i=e.dataTransfer;const a=i.files;this.handleFiles(a)};this.handleFiles=e=>{if(!this.multiple){this.files=[e[0]]}else{this.files=[...this.files,...e]}this.bdsUploadChange.emit({value:this.files})};this.refInputElement=e=>{this.inputElement=e}}dataAcceptChanged(){if(this.dataAccept){if(typeof this.dataAccept==="string"){try{this.internalAccepts=JSON.parse(this.dataAccept)}catch(e){this.internalAccepts=[]}}else{this.internalAccepts=this.dataAccept}}else{this.internalAccepts=[]}}filesChanged(){if(this.files.length>0){for(let e=0;e<this.files.length;e++){if(this.internalAccepts.length>0){this.validationFiles(this.files[e],e)}}}}formatErrorChanged(e){if(e){this.error=l(this.language,"formatError");setTimeout((()=>this.error=null),5e3)}}componentWillLoad(){this.dataAcceptChanged()}componentDidLoad(){["dragenter","dragover","dragleave","drop"].forEach((e=>{this.dropArea.shadowRoot.addEventListener(e,this.preventDefaults,false);this.dropArea.shadowRoot.addEventListener(e,(()=>this.hoverFile(true)),false)}));["dragenter","dragover"].forEach((e=>{this.dropArea.shadowRoot.addEventListener(e,(()=>this.preventDefaults),false);this.dropArea.shadowRoot.addEventListener(e,(()=>this.hoverFile(true)),false)}));["dragleave","drop"].forEach((e=>{this.dropArea.shadowRoot.addEventListener(e,(()=>this.preventDefaults),false);this.dropArea.shadowRoot.addEventListener(e,(()=>this.hoverFile(false)),false)}));this.dropArea.shadowRoot.addEventListener("drop",this.handleDrop,false)}preventDefaults(e){e.preventDefault();e.stopPropagation()}hoverFile(e){this.hover=e}onUploadClick(e){if(e.length>0){if(!this.multiple){this.files=[e[0]]}else{this.files=[...this.files,...e]}this.haveFiles=true;this.getSize()}else{return false}this.bdsUploadChange.emit({value:this.files})}getSize(){this.files.map((e=>{const i=e.size;this.size.push(i)}))}async deleteFile(e){const i=this.files.filter(((i,a)=>a==e&&i));this.bdsUploadDelete.emit({value:i});this.files.splice(e,1);this.files=[...this.files];if(this.files.length===0){this.haveFiles=false}else{this.haveFiles=true}this.bdsUploadChange.emit({value:this.files})}async deleteAllFiles(){this.bdsUploadDelete.emit({value:this.files});this.files=[];if(this.files.length===0){this.haveFiles=false}else{this.haveFiles=true}this.bdsUploadChange.emit({value:this.files})}handleKeyDown(e){if(e.key=="Enter"){this.inputElement.click()}}render(){return a("div",{key:"1f75f1bf649088e0893fe64f27a9ef37de84b78a",class:"upload"},a("div",{key:"b8fc280093d1fd6bdc46583c71a56a4f5a27f9e4",class:"upload-header"},a("bds-icon",{key:"145546937a1399ef5c121b2a1c0bd2545667e987",class:"upload-header_icon",size:"xxx-large",name:"upload"}),a("div",{key:"9fff2b1c4d1c5b6a50c18c5ea8fd8a9840552263",class:"upload-header_text"},a("bds-typo",{key:"726cf65caa77d4f372918868d050c8b69e29c038",variant:"fs-16",bold:"bold","aria-label":this.titleName},this.titleName),a("bds-typo",{key:"2065f3a6ad9157bc014ddb053b0fe73b0b7497b7",variant:"fs-14",bold:"regular","aria-label":this.subtitle},this.subtitle))),this.error?a("bds-banner",{context:"inside",variant:"error","aria-label":this.error},this.error):"",this.haveFiles?a("div",null,a("div",{class:"list-preview"},this.files.map(((e,i)=>a("div",{class:"upload__preview",key:i,id:"drop-area"},a("div",{class:"preview",id:"preview"},a("bds-icon",{size:"x-small",name:"attach"}),a("p",{class:"preview-text",id:"preview-text","aria-label":e.name},e.name),a("bds-button-icon",{class:"preview-icon",size:"short",icon:"trash",variant:"secondary",onClick:()=>this.deleteFile(i),"aria-label":`delete ${e.name}`,"data-test":`${this.dtButtonDelete}-${i}`})))))),this.multiple?a("bds-typo",{variant:"fs-14",italic:true,class:"preview-length","aria-label":l(this.language,"uploaded")},this.files.length>1?`${this.files.length} ${l(this.language,"uploaded")}`:""):""):"",a("div",{key:"d4e4954f155c51071cf3e884a73dd840b0912a8c",class:{upload__edit:true}},a("label",{key:"27ba7a823d4d48d726706f0ab2edecd1e77a4ebf",class:{"upload__edit--label":true,"upload__edit--hover":this.hover},id:"file-label",htmlFor:"file","data-test":this.dtLabelAddFile,tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)},a("div",{key:"c3bd4bb2ab9c02c22f12cec3e6de4fa708bcd060",class:{"text-box":true,"text-box--hover":this.hover},id:"file-text_box"},this.hover?a("bds-typo",{class:"text",variant:"fs-14",bold:"regular","aria-label":l(this.language,"dropHere")},l(this.language,"dropHere")):a("bds-typo",{class:"text",variant:"fs-14",bold:"regular","aria-label":l(this.language,"dropOrClick")},l(this.language,"dropOrClick"))),a("img",{key:"a38b8c18d54da95ac8f75e036f5b17035ea0cbb0",class:{"upload__img--invisible":true,"upload__img--visible":this.hover},src:d})),a("input",{key:"e7ebf4a4a9180540ee0608ab06278ce07f5db04b",ref:this.refInputElement,type:"file",name:"files[]",id:"file",class:"upload__input",multiple:this.multiple,accept:this.internalAccepts.length>0?this.internalAccepts.toString():this.accept,onChange:e=>this.onUploadClick(e.target.files),"data-test":this.dtInputFiles})))}get dropArea(){return t(this)}static get watchers(){return{dataAccept:["dataAcceptChanged"],files:["filesChanged"],formatError:["formatErrorChanged"]}}};n.style=c;export{n as bds_upload};
//# sourceMappingURL=p-735590a4.entry.js.map