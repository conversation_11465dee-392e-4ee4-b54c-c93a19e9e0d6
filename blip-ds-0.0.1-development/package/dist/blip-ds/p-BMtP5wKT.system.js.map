{"version": 3, "file": "p-BMtP5wKT.system.js", "sources": ["src/components/chip/chip.scss?tag=bds-chip&encapsulation=shadow", "src/components/chip/chip.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  white-space: nowrap;\n  font-family: $font-family;\n  font-weight: 600;\n}\n\n:host(.chip) {\n  border-radius: 8px;\n  padding: 3px 8px;\n}\n\n:host(.chip--primary) {\n  background: $color-disabled-bg;\n  color: $color-primary-main;\n}\n\n:host(.chip--click.chip--primary:hover) {\n  background: $color-hover-light;\n  color: $color-primary-dark;\n}\n\n:host(.chip--watermelon) {\n  background: $color-primary-pinks-watermelon;\n  color: $color-neutral-light-snow;\n}\n\n:host(.chip--default) {\n  background: $color-neutral-light-breeze;\n  color: $color-neutral-medium-cloud;\n}\n\n:host(.chip--danger) {\n  background: $color-extend-reds-flower;\n  color: $color-extend-reds-delete;\n}\n\n:host(.chip--click.chip--danger:hover) {\n  background: $color-disabled-delete;\n  color: $color-extend-reds-dragon;\n}\n\n:host(.chip--filter) {\n  background: $color-primary-dark;\n  color: $color-neutral-light-snow;\n  \n}\n\n:host(.chip--click.chip--filter:hover) {\n  background: $color-primary-night;\n  color: $color-neutral-light-snow;\n}\n\n:host(.chip--standard) {\n  height: 24px;\n  font-size: $fs-12;\n}\n\n:host(.chip--tall) {\n  height: 32px;\n  font-size: $fs-14;\n}\n\n.chip__delete {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 6px;\n  cursor: pointer;\n}\n\n.chip__icon {\n  display: inline-flex;\n  align-items: center;\n  padding-right: 4px;\n}\n", "import { Component, Host, h, Prop, Event, EventEmitter, Element } from '@stencil/core';\n\nexport type ChipSize = 'standard' | 'tall';\nexport type ChipVariant = 'primary' | 'default' | 'watermelon';\n\n@Component({\n  tag: 'bds-chip',\n  styleUrl: 'chip.scss',\n  shadow: true,\n})\nexport class Chip {\n  @Element() private element: HTMLElement;\n\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n\n  /**\n   * Chip size. Entered as one of the size design tokens. Can be one of:\n   * \"standard\" and \"tall\"\n   */\n  @Prop() size?: ChipSize = 'standard';\n\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'primary', 'default';\n   */\n  @Prop() variant?: ChipVariant = 'default';\n\n  /**\n   * Add state danger on chip, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state filter on chip whith specific color.\n   */\n  @Prop() filter = false;\n\n  /**\n   * When 'true' and the component is using the primary variant, a hover is added\n   */\n  @Prop() clickable = false;\n\n  /**\n   * When 'true', the component recive remove button and dispach event onBdsDelete\n   */\n  @Prop() deletable = false;\n\n  /**\n   * When 'true', no events will be dispatched\n   */\n  @Prop() disabled = false;\n\n  /**\n   *  Triggered after a mouse click on delete icon, return id element. Only fired when deletable is true.\n   */\n  @Event() bdsDelete: EventEmitter;\n\n  handleClickDelete(event) {\n    if (!this.deletable || this.disabled) return;\n    event.preventDefault();\n    this.bdsDelete.emit({ id: this.element.id });\n  }\n\n  private getClickClass() {\n    return this.clickable ? { 'chip--click': true } : {};\n  }\n\n  private getSizeClass() {\n    return this.size === 'standard' ? { 'chip--standard': true } : { 'chip--tall': true };\n  }\n\n  private getStateClass() {\n    if (this.disabled) {\n      return { 'chip--default': true };\n    }\n\n    if (this.danger) {\n      return { 'chip--danger': true };\n    }\n\n    if (this.filter) {\n      return { 'chip--filter': true };\n    }\n\n    if (this.variant === 'primary') {\n      return { 'chip--primary': true };\n    }\n\n    if (this.variant === 'watermelon') {\n      return { 'chip--watermelon': true };\n    }\n\n    return { 'chip--default': true };\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          chip: true,\n          ...this.getClickClass(),\n          ...this.getStateClass(),\n          ...this.getSizeClass(),\n        }}\n      >\n        {this.icon && (\n          <div class=\"chip__icon\">\n            <bds-icon size=\"x-small\" name={this.icon}></bds-icon>\n          </div>\n        )}\n        <slot />\n        {this.deletable && (\n          <div class=\"chip__delete\" onClick={this.handleClickDelete.bind(this)}>\n            <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,OAAO,GAAG,4mCAA4mC;;YCU/mC,IAAI,uBAAA,MAAA;MALjB,IAAA,WAAA,CAAA,OAAA,EAAA;;;MAaE;;;MAGG;MACK,QAAA,IAAI,CAAA,IAAA,GAAc,UAAU;MAEpC;;;MAGG;MACK,QAAA,IAAO,CAAA,OAAA,GAAiB,SAAS;MAEzC;;MAEG;MACsB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MAExC;;MAEG;MACK,QAAA,IAAM,CAAA,MAAA,GAAG,KAAK;MAEtB;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;MAEzB;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;MAEzB;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAqEzB;MA9DC,IAAA,iBAAiB,CAAC,KAAK,EAAA;MACrB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ;kBAAE;cACtC,KAAK,CAAC,cAAc,EAAE;MACtB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;;UAGtC,aAAa,GAAA;MACnB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE;;UAG9C,YAAY,GAAA;cAClB,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE;;UAG/E,aAAa,GAAA;MACnB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;MACjB,YAAA,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE;;MAGlC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;MACf,YAAA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE;;MAGjC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;MACf,YAAA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE;;MAGjC,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;MAC9B,YAAA,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE;;MAGlC,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,EAAE;MACjC,YAAA,OAAO,EAAE,kBAAkB,EAAE,IAAI,EAAE;;MAGrC,QAAA,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE;;UAGlC,MAAM,GAAA;MACJ,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EACH,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,IAAI,EACP,EAAA,IAAI,CAAC,aAAa,EAAE,CACpB,EAAA,IAAI,CAAC,aAAa,EAAE,CACpB,EAAA,IAAI,CAAC,YAAY,EAAE,CAAA,EAAA,EAGvB,IAAI,CAAC,IAAI,KACR,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,YAAY,EAAA,EACrB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAa,CAAA,CACjD,CACP,EACD,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACP,IAAI,CAAC,SAAS,KACb,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAClE,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,EAAY,CAAA,CAC3D,CACP,CACI;;;;;;;;;;;;"}