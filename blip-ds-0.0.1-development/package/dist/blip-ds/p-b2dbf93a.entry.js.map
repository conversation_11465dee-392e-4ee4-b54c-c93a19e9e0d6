{"version": 3, "names": ["navTreeCss", "NavTreeItem", "constructor", "hostRef", "this", "navTreeParent", "navTreeChild", "itensElement", "collapse", "icon", "secondaryText", "isOpen", "loading", "disable", "dataTest", "handler", "i", "length", "element", "toggle", "isOpenChanged", "value", "bdsToogleChange", "emit", "componentWillLoad", "_a", "parentElement", "tagName", "_b", "querySelector", "componentWillRender", "querySelectorAll", "handleKeyDown", "event", "key", "render", "h", "Host", "tabindex", "onKeyDown", "bind", "class", "nav_tree_item", "nav_tree_item_active", "nav_tree_item_button", "nav_tree_item_button_active", "onClick", "text", "size", "name", "color", "theme", "variant", "tag", "bold", "margin", "accordion", "accordion_open"], "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree-item&encapsulation=shadow", "src/components/nav-tree/nav-tree-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, h, Element, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree-item',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTreeItem {\n  private navTreeParent?: HTMLBdsNavTreeElement | HTMLBdsNavTreeItemElement = null;\n  private navTreeChild?: HTMLBdsNavTreeItemElement = null;\n  private itensElement?: NodeListOf<HTMLBdsNavTreeItemElement> = null;\n\n  @Element() private element: HTMLElement;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * When de activation of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    // if (this.navTreeChild) this.navTreeChild.isOpen = value;\n  }\n\n  componentWillLoad() {\n    this.navTreeParent =\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE' && (this.element.parentElement as HTMLBdsNavTreeElement)) ||\n      (this.element.parentElement?.tagName == 'BDS-NAV-TREE-ITEM' && (this.element.parentElement as HTMLBdsNavTreeItemElement)) ||\n      null;\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item');\n  }\n  componentWillRender() {\n    if (this.navTreeParent) {\n      this.itensElement = this.navTreeParent.querySelectorAll(\n        'bds-nav-tree-item',\n      ) as NodeListOf<HTMLBdsNavTreeItemElement>;\n    }\n  }\n\n  private handler = () => {\n    if (!this.loading && !this.disable) {\n      if (this.navTreeParent && this.navTreeParent.collapse == 'single' && this.itensElement) {\n        for (let i = 0; i < this.itensElement.length; i++) {\n          if (this.itensElement[i] != this.element) this.itensElement[i].isOpen = false;\n        }\n      }\n      this.toggle();\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.handler();\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              nav_tree_item: true,\n              nav_tree_item_active: this.isOpen,\n              nav_tree_item_button: !this.navTreeChild,\n              nav_tree_item_button_active: !this.navTreeChild && this.isOpen,\n              [`nav_tree_item--loading`]: this.loading,\n              [`nav_tree_item--disable`]: this.disable,\n            }}\n            onClick={() => this.handler()}\n            data-test={this.dataTest}\n            aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n          >\n            {this.loading ? (\n              <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n            ) : this.icon ? (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.isOpen,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme=\"outline\"\n              ></bds-icon>\n            ) : (\n              ''\n            )}\n            <div class=\"nav_tree_item_content\">\n              {this.text && (\n                <bds-typo\n                  class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                  variant=\"fs-14\"\n                  tag=\"span\"\n                  line-height=\"small\"\n                  bold={this.isOpen ? 'bold' : 'semi-bold'}\n                >\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo\n                  class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                  variant=\"fs-12\"\n                  line-height=\"small\"\n                  tag=\"span\"\n                  margin={false}\n                >\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n            <div class=\"nav_tree_item_slot\">\n              <slot name=\"header-content\"></slot>\n            </div>\n            {this.navTreeChild && (\n              <bds-icon\n                class={{\n                  [`nav_main_arrow`]: true,\n                  [`nav_main_arrow_active`]: this.isOpen,\n                  [`nav_main_arrow--loading`]: this.loading,\n                }}\n                name=\"arrow-down\"\n              ></bds-icon>\n            )}\n          </div>\n        </div>\n        {this.navTreeChild && (\n          <div\n            class={{\n              accordion: true,\n              accordion_open: this.isOpen,\n            }}\n          >\n            <div class=\"container\">\n              <slot></slot>\n            </div>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAa,i4K,MCSNC,EAAW,MALxB,WAAAC,CAAAC,G,yDAMUC,KAAaC,cAAuD,KACpED,KAAYE,aAA+B,KAC3CF,KAAYG,aAA2C,KAMvDH,KAAQI,SAAe,SAIvBJ,KAAIK,KAAY,KAQhBL,KAAaM,cAAY,KAION,KAAMO,OAAa,MAInDP,KAAOQ,QAAa,MAKpBR,KAAOS,QAAa,MAIpBT,KAAQU,SAAY,KAgCpBV,KAAOW,QAAG,KAChB,IAAKX,KAAKQ,UAAYR,KAAKS,QAAS,CAClC,GAAIT,KAAKC,eAAiBD,KAAKC,cAAcG,UAAY,UAAYJ,KAAKG,aAAc,CACtF,IAAK,IAAIS,EAAI,EAAGA,EAAIZ,KAAKG,aAAaU,OAAQD,IAAK,CACjD,GAAIZ,KAAKG,aAAaS,IAAMZ,KAAKc,QAASd,KAAKG,aAAaS,GAAGL,OAAS,K,EAG5EP,KAAKe,Q,EAiGV,CAjIC,YAAMA,GACJf,KAAKO,QAAUP,KAAKO,M,CAIZ,aAAAS,CAAcC,GACtBjB,KAAKkB,gBAAgBC,KAAK,CAAEF,MAAOA,EAAOH,QAASd,KAAKc,S,CAI1D,iBAAAM,G,QACEpB,KAAKC,gBACFoB,EAAArB,KAAKc,QAAQQ,iBAAa,MAAAD,SAAA,SAAAA,EAAEE,UAAW,gBAAmBvB,KAAKc,QAAQQ,iBACvEE,EAAAxB,KAAKc,QAAQQ,iBAAa,MAAAE,SAAA,SAAAA,EAAED,UAAW,qBAAwBvB,KAAKc,QAAQQ,eAC7E,KACFtB,KAAKE,aAAeF,KAAKc,QAAQW,cAAc,oB,CAEjD,mBAAAC,GACE,GAAI1B,KAAKC,cAAe,CACtBD,KAAKG,aAAeH,KAAKC,cAAc0B,iBACrC,oB,EAgBE,aAAAC,CAAcC,GACpB,GAAIA,EAAMC,KAAO,QAAS,CACxB9B,KAAKW,S,EAIT,MAAAoB,GACE,OACEC,EAACC,EAAI,CAAAH,IAAA,4CACHE,EAAA,OAAAF,IAAA,2CAAKI,SAAS,IAAIC,UAAWnC,KAAK4B,cAAcQ,KAAKpC,MAAOqC,MAAM,SAChEL,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACLC,cAAe,KACfC,qBAAsBvC,KAAKO,OAC3BiC,sBAAuBxC,KAAKE,aAC5BuC,6BAA8BzC,KAAKE,cAAgBF,KAAKO,OACxD,CAAC,0BAA2BP,KAAKQ,QACjC,CAAC,0BAA2BR,KAAKS,SAEnCiC,QAAS,IAAM1C,KAAKW,UAAS,YAClBX,KAAKU,SAAQ,aACZV,KAAK2C,MAAQ3C,KAAKM,eAAiB,KAAKN,KAAKM,kBAExDN,KAAKQ,QACJwB,EAAqB,uBAAAY,KAAK,gBACxB5C,KAAKK,KACP2B,EACE,YAAAK,MAAO,CACL,CAAC,aAAc,KACf,CAAC,oBAAqBrC,KAAKO,QAE7BqC,KAAK,SACLC,KAAM7C,KAAKK,KACXyC,MAAM,UACNC,MAAM,YACI,GAIdf,EAAK,OAAAF,IAAA,2CAAAO,MAAM,yBACRrC,KAAK2C,MACJX,EAAA,YAAAF,IAAA,2CACEO,MAAO,CAAE,CAAC,cAAe,KAAM,CAAC,uBAAwBrC,KAAKQ,SAC7DwC,QAAQ,QACRC,IAAI,OACQ,sBACZC,KAAMlD,KAAKO,OAAS,OAAS,aAE5BP,KAAK2C,MAGT3C,KAAKM,eACJ0B,EACE,YAAAF,IAAA,2CAAAO,MAAO,CAAE,CAAC,iBAAkB,KAAM,CAAC,0BAA2BrC,KAAKQ,SACnEwC,QAAQ,QAAO,cACH,QACZC,IAAI,OACJE,OAAQ,OAEPnD,KAAKM,gBAIZ0B,EAAK,OAAAF,IAAA,2CAAAO,MAAM,sBACTL,EAAA,QAAAF,IAAA,2CAAMe,KAAK,oBAEZ7C,KAAKE,cACJ8B,EAAA,YAAAF,IAAA,2CACEO,MAAO,CACL,CAAC,kBAAmB,KACpB,CAAC,yBAA0BrC,KAAKO,OAChC,CAAC,2BAA4BP,KAAKQ,SAEpCqC,KAAK,iBAKZ7C,KAAKE,cACJ8B,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACLe,UAAW,KACXC,eAAgBrD,KAAKO,SAGvByB,EAAK,OAAAF,IAAA,2CAAAO,MAAM,aACTL,EAAA,QAAAF,IAAA,+C", "ignoreList": []}