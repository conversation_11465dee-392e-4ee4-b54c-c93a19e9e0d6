{"version": 3, "file": "p-BGN9u3Vc.system.js", "sources": ["src/components/input/input.scss?tag=bds-input&encapsulation=shadow", "src/components/input/input.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: 22px;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @if ($name == 'disabled') {\n    background: $color-surface-2;\n  }\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n\n      &[type='date'] {\n        &::-webkit-calendar-picker-indicator {\n          opacity: 0;\n          pointer-events: none;\n        }\n      }\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    gap: 4px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      margin-top: 0px;\n    }\n\n    &--danger {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-negative;\n        }\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { Component, h, Prop, State, Watch, Event, EventEmitter, Method, Host } from '@stencil/core';\nimport { InputType, InputAutocapitalize, InputAutoComplete, InputCounterLengthRules } from './input-interface';\nimport { emailValidation, numberValidation } from '../../utils/validations';\n\n@Component({\n  tag: 'bds-input',\n  styleUrl: 'input.scss',\n  shadow: true,\n})\nexport class Input {\n  private nativeInput?: HTMLInputElement;\n\n  @State() isPressed? = false;\n  @State() isPassword? = false;\n  @State() validationMesage? = '';\n  @State() validationDanger? = false;\n  /**\n   * Nome do input, usado para identificação no formulário.\n   */\n  @Prop() inputName? = '';\n\n  /**\n   * Define o tipo do input (por exemplo, `text`, `password`, etc).\n   */\n  @Prop({ reflect: true }) type?: InputType = 'text';\n\n  /**\n   * <PERSON><PERSON><PERSON><PERSON> que será exibido acima do input.\n   */\n  @Prop() label? = '';\n\n  /**\n   * Texto que será exibido como sugestão ou dica no input.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Define a capitalização automática do texto (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Define o comportamento de autocompletar do navegador (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * Define o valor máximo permitido para o input.\n   */\n  @Prop() max?: string;\n\n  /**\n   * Define o número máximo de caracteres permitidos no input.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * Define o valor mínimo permitido para o input.\n   */\n  @Prop() min?: string;\n\n  /**\n   * Define o número mínimo de caracteres permitidos no input.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * Torna o input somente leitura.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Define se o input é obrigatório.\n   */\n  @Prop() required: boolean;\n\n  /**\n   * Define um padrão regex que o valor do input deve seguir.\n   */\n  @Prop() pattern?: string;\n\n  /**\n   * Mensagem de ajuda exibida abaixo do input.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Mensagem de erro exibida quando o valor do input é inválido.\n   */\n  @Prop({ mutable: true }) errorMessage?: string = '';\n\n  /**\n   * Mensagem exibida quando o valor do input é válido.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n\n  /**\n   * Nome do ícone a ser exibido dentro do input.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Define se o input está desabilitado.\n   */\n  @Prop({ reflect: true, mutable: true }) disabled?: boolean = false;\n\n  /**\n   * Define se o input está em estado de erro.\n   */\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\n\n  /**\n   * Define se o input está em estado de sucesso.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n\n  /**\n   * O valor atual do input.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Define se será exibido um contador de comprimento de caracteres.\n   */\n  @Prop() counterLength? = false;\n\n  /**\n   * Define a regra do contador de comprimento de caracteres (min, max, etc).\n   */\n  @Prop() counterLengthRule?: InputCounterLengthRules = null;\n\n  /**\n   * Define se o input será submetido ao pressionar Enter.\n   */\n  @Prop() isSubmit = false;\n\n  /**\n   * Define se o input é uma área de texto (textarea).\n   */\n  @Prop() isTextarea = false;\n\n  /**\n   * Define a quantidade de linhas da área de texto (se for `textarea`).\n   */\n  @Prop() rows?: number = 1;\n\n  /**\n   * Define a quantidade de colunas da área de texto (se for `textarea`).\n   */\n  @Prop() cols?: number = 0;\n\n  /**\n   * Mensagem de erro exibida quando o input não é preenchido e é obrigatório.\n   */\n  @Prop() requiredErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao comprimento mínimo.\n   */\n  @Prop() minlengthErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor mínimo permitido.\n   */\n  @Prop() minErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor máximo permitido.\n   */\n  @Prop() maxErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um email válido.\n   */\n  @Prop() emailErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um número válido.\n   */\n  @Prop() numberErrorMessage: string;\n\n  /**\n   * Define se o input será exibido como chips (um tipo de entrada com múltiplos valores).\n   */\n  @Prop() chips: boolean;\n\n  /**\n   * Data test é a prop para testar especificamente a ação do componente.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Prop() encode?: boolean = false;\n\n  /**\n   * Evento disparado quando o valor do input muda.\n   */\n  @Event({ bubbles: true, composed: true }) bdsChange!: EventEmitter;\n\n  /**\n   * Evento disparado quando o input recebe um input (digitação).\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Evento disparado quando o input perde o foco.\n   */\n  @Event() bdsOnBlur: EventEmitter;\n\n  /**\n   * Evento disparado quando o input ganha o foco.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  /**\n   * Evento disparado quando o formulário é submetido.\n   */\n  @Event() bdsSubmit: EventEmitter;\n\n  /**\n   * Evento disparado para validação de padrão regex.\n   */\n  @Event() bdsPatternValidation: EventEmitter;\n\n  /**\n   * Evento disparado quando a tecla \"Backspace\" é pressionada.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  /**\n   * Define o foco no campo de entrada.\n   */\n  @Method()\n  async setFocus(): Promise<void> {\n    this.onClickWrapper();\n  }\n\n  /**\n   * Remove o foco do campo de entrada.\n   */\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.onBlur();\n  }\n\n  /**\n   * Retorna o elemento de input do componente.\n   */\n  @Method()\n  async getInputElement(): Promise<HTMLInputElement> {\n    return this.nativeInput;\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.nativeInput.validity.valid;\n  }\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.value = '';\n  }\n\n  /**\n   * Codifica os caracteres especiais para exibição segura (evita injeção de código HTML).\n   */\n  private encodeValue(value?: string): string {\n    const lt = /</g,\n      gt = />/g,\n      ap = /'/g,\n      ic = /\"/g,\n      amp = /&/g,\n      slash = /\\//g;\nif(!this.encode) return value;\n    return (\n      value &&\n      value\n        .toString()\n        .replace(lt, '&lt;')\n        .replace(gt, '&gt;')\n        .replace(ap, '&#39;')\n        .replace(ic, '&#34;')\n        .replace(amp, '&amp;')\n        .replace(slash, '&#47;')\n    );\n  }\n\n  /**\n   * Avisa sobre a mudança do valor do campo de entrada.\n   */\n  @Watch('value')\n  protected valueChanged(newValue: string | null): void {\n    const changeValue = this.encode ? this.encodeValue(newValue || '') : newValue || '';\n    this.bdsChange.emit({ value: changeValue });\n  }\n\n  /**\n   * Tratamento de eventos de pressionamento de tecla (Enter, Backspace, etc).\n   */\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsSubmit.emit({ event, value: this.value });\n\n        if (this.isSubmit) {\n          this.clearTextInput();\n          event.preventDefault();\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  /**\n   * Função chamada ao digitar no campo de entrada.\n   */\n  private onInput = (ev: InputEvent): void => {\n    this.onBdsInputValidations();\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n  };\n\n  /**\n   * Função chamada ao perder o foco do campo de entrada.\n   */\n  private onBlur = (): void => {\n    this.onBlurValidations();\n    this.isPressed = false;\n    this.bdsOnBlur.emit();\n  };\n\n  /**\n   * Função chamada ao ganhar o foco do campo de entrada.\n   */\n  private onFocus = (): void => {\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  /**\n   * Função chamada ao clicar no campo de entrada.\n   */\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  private clearTextInput = (ev?: Event) => {\n    if (!this.readonly && !this.disabled && ev) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n\n    this.value = '';\n\n    if (this.nativeInput) {\n      this.nativeInput.value = '';\n    }\n  };\n\n  /**\n   * Função que renderiza o ícone dentro do campo de entrada.\n   */\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon\n            class=\"input__icon--color\"\n            size={this.label ? 'medium' : 'small'}\n            name={this.icon}\n            color=\"inherit\"\n          ></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza a label do campo de entrada.\n   */\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza as mensagens de erro ou sucesso abaixo do campo de entrada.\n   */\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Valida o campo de entrada ao perder o foco.\n   */\n  private onBlurValidations() {\n    this.required && this.requiredValidation();\n    this.pattern && this.patternValidation();\n    (this.minlength || this.maxlength) && this.lengthValidation();\n    (this.min || this.max) && this.minMaxValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Realiza as validações do campo enquanto o usuário digita.\n   */\n  private onBdsInputValidations() {\n    this.type === 'email' && this.emailValidation();\n    this.type === 'phonenumber' && this.numberValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Valida o padrão regex do campo.\n   */\n  private patternValidation() {\n    const regex = new RegExp(this.pattern);\n    this.bdsPatternValidation.emit(regex.test(this.nativeInput.value));\n  }\n\n  /**\n   * Valida se o campo é obrigatório.\n   */\n  private requiredValidation() {\n    if (this.nativeInput.validity.valueMissing) {\n      this.validationMesage = this.requiredErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida o comprimento do texto no campo de entrada.\n   */\n  private lengthValidation() {\n    if (this.nativeInput.validity.tooShort) {\n      this.validationMesage = this.minlengthErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.tooLong) {\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida os valores mínimos e máximos do campo de entrada.\n   */\n  private minMaxValidation() {\n    if (this.nativeInput.validity.rangeUnderflow) {\n      this.validationMesage = this.minErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.rangeOverflow) {\n      this.validationMesage = this.maxErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um email válido.\n   */\n  private emailValidation() {\n    if (emailValidation(this.nativeInput.value)) {\n      this.validationMesage = this.emailErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um número válido.\n   */\n  private numberValidation() {\n    if (numberValidation(this.nativeInput.value)) {\n      this.validationMesage = this.numberErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  /**\n   * Atualiza o valor do campo de entrada após as mudanças.\n   */\n  componentDidUpdate() {\n    if (this.nativeInput && this.value != this.nativeInput.value) {\n      this.nativeInput.value = this.value;\n    }\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const Element = this.isTextarea ? 'textarea' : 'input';\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <slot name=\"input-left\"></slot>\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: !this.chips, input__container__wrapper__chips: this.chips }}>\n              <slot name=\"inside-input-left\"></slot>\n              <Element\n                class={{ input__container__text: true, input__container__text__chips: this.chips }}\n                ref={(input) => (this.nativeInput = input)}\n                rows={this.rows}\n                cols={this.cols}\n                autocapitalize={this.autoCapitalize}\n                autocomplete={this.autoComplete}\n                disabled={this.disabled}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                name={this.inputName}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.onInput}\n                placeholder={this.placeholder}\n                readOnly={this.readonly}\n                type={this.type}\n                value={this.encodeValue(this.value)}\n                pattern={this.pattern}\n                required={this.required}\n                part=\"input\"\n                data-test={this.dataTest}\n              ></Element>\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text\n              length={this.value.length}\n              max={this.maxlength}\n              active={isPressed}\n              {...this.counterLengthRule}\n            />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"small\" />}\n          <slot name=\"input-right\" />\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;MAAA,MAAM,QAAQ,GAAG,8/TAA8/T;;YCUlgU,KAAK,wBAAA,MAAA;MALlB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;MAQW,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;MAClB,QAAA,IAAU,CAAA,UAAA,GAAI,KAAK;MACnB,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;MACtB,QAAA,IAAgB,CAAA,gBAAA,GAAI,KAAK;MAClC;;MAEG;MACK,QAAA,IAAS,CAAA,SAAA,GAAI,EAAE;MAEvB;;MAEG;MACsB,QAAA,IAAI,CAAA,IAAA,GAAe,MAAM;MAElD;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;MAEnB;;MAEG;MACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;MAEjC;;MAEG;MACK,QAAA,IAAc,CAAA,cAAA,GAAyB,KAAK;MAEpD;;MAEG;MACK,QAAA,IAAY,CAAA,YAAA,GAAuB,KAAK;MAsBhD;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAYxB;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;MAEnC;;MAEG;MACsB,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;MAEnD;;MAEG;MACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;MAErD;;MAEG;MACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;MAE3C;;MAEG;MACqC,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;MAElE;;MAEG;MACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;MAEhE;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MAEjE;;MAEG;MACsB,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;MAEnD;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAI,KAAK;MAE9B;;MAEG;MACK,QAAA,IAAiB,CAAA,iBAAA,GAA6B,IAAI;MAE1D;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAExB;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAG,KAAK;MAE1B;;MAEG;MACK,QAAA,IAAI,CAAA,IAAA,GAAY,CAAC;MAEzB;;MAEG;MACK,QAAA,IAAI,CAAA,IAAA,GAAY,CAAC;MAqCzB;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAExB,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;MA8GhC;;MAEG;MACK,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;MACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;MACf,gBAAA,KAAK,OAAO;MACV,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;MAEjD,oBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;8BACjB,IAAI,CAAC,cAAc,EAAE;8BACrB,KAAK,CAAC,cAAc,EAAE;;0BAExB;MACF,gBAAA,KAAK,WAAW;MAChB,gBAAA,KAAK,QAAQ;MACX,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;0BAC3D;;MAEN,SAAC;MAED;;MAEG;MACK,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;kBACzC,IAAI,CAAC,qBAAqB,EAAE;MAC5B,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;kBAClD,IAAI,KAAK,EAAE;sBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;MAEhC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;MACxB,SAAC;MAED;;MAEG;MACK,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;kBAC1B,IAAI,CAAC,iBAAiB,EAAE;MACxB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;MACtB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;MACvB,SAAC;MAED;;MAEG;MACK,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;MAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;MACtB,SAAC;MAED;;MAEG;MACK,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;kBAClC,IAAI,CAAC,OAAO,EAAE;MACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAE5B,SAAC;MAED;;MAEG;MACK,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAU,KAAI;MACtC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE;sBAC1C,EAAE,CAAC,cAAc,EAAE;sBACnB,EAAE,CAAC,eAAe,EAAE;;MAGtB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE;MAEf,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;;MAE/B,SAAC;MA8PF;MAhZC;;MAEG;MAEH,IAAA,MAAM,QAAQ,GAAA;cACZ,IAAI,CAAC,cAAc,EAAE;;MAGvB;;MAEG;MAEH,IAAA,MAAM,WAAW,GAAA;cACf,IAAI,CAAC,MAAM,EAAE;;MAGf;;MAEG;MAEH,IAAA,MAAM,eAAe,GAAA;cACnB,OAAO,IAAI,CAAC,WAAW;;MAGzB;;MAEG;MAEH,IAAA,MAAM,OAAO,GAAA;MACX,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;;MAGxC;;MAEG;MAEH,IAAA,MAAM,KAAK,GAAA;MACT,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;MAGjB;;MAEG;MACK,IAAA,WAAW,CAAC,KAAc,EAAA;cAChC,MAAM,EAAE,GAAG,IAAI,EACb,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,GAAG,GAAG,IAAI,EACV,KAAK,GAAG,KAAK;cACnB,IAAG,CAAC,IAAI,CAAC,MAAM;MAAE,YAAA,OAAO,KAAK;MACzB,QAAA,QACE,KAAK;kBACL;MACG,iBAAA,QAAQ;MACR,iBAAA,OAAO,CAAC,EAAE,EAAE,MAAM;MAClB,iBAAA,OAAO,CAAC,EAAE,EAAE,MAAM;MAClB,iBAAA,OAAO,CAAC,EAAE,EAAE,OAAO;MACnB,iBAAA,OAAO,CAAC,EAAE,EAAE,OAAO;MACnB,iBAAA,OAAO,CAAC,GAAG,EAAE,OAAO;MACpB,iBAAA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC;;MAI9B;;MAEG;MAEO,IAAA,YAAY,CAAC,QAAuB,EAAA;cAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAE;cACnF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;;MA8E7C;;MAEG;UACK,UAAU,GAAA;MAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,WAAW,EAAE,IAAI;MACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MACnC,aAAA,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,oBAAoB,EAC1B,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EACrC,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EAAA,CACL,CACR,CACP;;MAIL;;MAEG;UACK,WAAW,GAAA;MACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,uBAAuB,EAAE,IAAI;sBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;mBACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;MAIL;;MAEG;UACK,aAAa,GAAA;cACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;MACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;MAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;MAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;cAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;MAClB,cAAE;oBACA,IAAI,CAAC;MACL,kBAAE;wBACA,gBAAgB;cAExB,IAAI,OAAO,EAAE;MACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;MAIV,QAAA,OAAO,SAAS;;MAGlB;;MAEG;UACK,iBAAiB,GAAA;MACvB,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE;MAC1C,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE;MACxC,QAAA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,EAAE;MAC7D,QAAA,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,gBAAgB,EAAE;cACjD,IAAI,CAAC,aAAa,EAAE;;MAGtB;;MAEG;UACK,qBAAqB,GAAA;cAC3B,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;cAC/C,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE;cACtD,IAAI,CAAC,aAAa,EAAE;;MAGtB;;MAEG;UACK,iBAAiB,GAAA;cACvB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;MACtC,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;MAGpE;;MAEG;UACK,kBAAkB,GAAA;cACxB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE;MAC1C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;MACjD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;MAIhC;;MAEG;UACK,gBAAgB,GAAA;cACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE;MACtC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB;MAClD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;kBAC5B;;cAGF,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE;MACrC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;kBAC5B;;;MAIJ;;MAEG;UACK,gBAAgB,GAAA;cACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,EAAE;MAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe;MAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;kBAC5B;;cAGF,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE;MAC3C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe;MAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;kBAC5B;;;MAIJ;;MAEG;UACK,eAAe,GAAA;cACrB,IAAI,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;MAC3C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;MAC9C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;MAIhC;;MAEG;UACK,gBAAgB,GAAA;cACtB,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;MAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB;MAC/C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;MAIhC;;MAEG;UACK,aAAa,GAAA;cACnB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;MACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;MAIjC;;MAEG;UACH,kBAAkB,GAAA;MAChB,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;kBAC5D,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;;;UAIvC,MAAM,GAAA;cACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;MAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,OAAO;MAEtD,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,KAAK,EAAE,IAAI;sBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;MAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;sBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;sBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;MACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MAC5B,gBAAA,gBAAgB,EAAE,SAAS;MAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClB,CAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,YAAY,EAAQ,CAAA,EAC/B,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,gCAAgC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAA,EAClG,CAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,mBAAmB,EAAQ,CAAA,EACtC,CAAA,CAAC,OAAO,EACN,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,KAAK,EAAE,EAClF,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAC,OAAO,EACD,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,CACf,CACP,CACF,EACL,IAAI,CAAC,aAAa,KACjB,CAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,EAAA,0CAAA,EACE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EACzB,GAAG,EAAE,IAAI,CAAC,SAAS,EACnB,MAAM,EAAE,SAAS,EAAA,EACb,IAAI,CAAC,iBAAiB,CAAA,CAC1B,CACH,EACA,IAAI,CAAC,OAAO,IAAI,iEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,OAAO,EAAG,CAAA,EAC5F,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,aAAa,EAAA,CAAG,CACvB,EACL,IAAI,CAAC,aAAa,EAAE,CAChB;;;;;;;;;;;;;;"}