{"version": 3, "names": ["accordionCss", "AccordionBody", "exports", "class_1", "hostRef", "_this", "this", "container", "isOpen", "isOpenAftAnimation", "numberElement", "hasDivisor", "dataTest", "ref<PERSON><PERSON><PERSON>", "el", "prototype", "toggle", "open", "close", "divisor", "valor", "isOpenChanged", "heightContainer", "_a", "offsetHeight", "setTimeout", "render", "h", "key", "class", "accordion_body", "accordion_body_divisor", "accordion_body_isOpen", "style", "height", "concat", "ref"], "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion-body&encapsulation=shadow", "src/components/accordion/accordion-body.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, State, h, Method, Prop, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-body',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionBody {\n  private container?: HTMLElement = null;\n\n  @State() isOpen?: boolean = false;\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() heightContainer?: number;\n  @State() numberElement?: number = null;\n  @State() hasDivisor?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  @Method()\n  async divisor(valor) {\n    this.hasDivisor = valor;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(): void {\n    this.heightContainer = this.isOpen ? (this.container?.offsetHeight || 0) : 0;\n    if (this.isOpen) {\n      setTimeout(() => {\n        this.isOpenAftAnimation = true;\n      }, 500);\n    } else {\n      this.isOpenAftAnimation = false;\n    }\n  }\n\n  private refContainer = (el: HTMLElement): void => {\n    this.container = el;\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          accordion_body: true,\n          accordion_body_divisor: this.hasDivisor,\n          accordion_body_isOpen: this.isOpenAftAnimation,\n        }}\n        style={{ height: `${this.heightContainer}px` }}\n        data-test={this.dataTest}\n      >\n        <div class=\"container\" ref={(el) => this.refContainer(el)}>\n          <slot></slot>\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "wlDAAA,IAAMA,EAAe,ylE,ICORC,EAAaC,EAAA,gCAL1B,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,UAMUA,KAASC,UAAiB,KAEzBD,KAAME,OAAa,MACnBF,KAAkBG,mBAAa,MAE/BH,KAAaI,cAAY,KACzBJ,KAAUK,WAAa,KAKxBL,KAAQM,SAAY,KAkCpBN,KAAAO,aAAe,SAACC,GACtBT,EAAKE,UAAYO,CACnB,CAmBD,CApDOX,EAAAY,UAAAC,OAAN,W,qFACEV,KAAKE,QAAUF,KAAKE,O,iBAIhBL,EAAAY,UAAAE,KAAN,W,qFACEX,KAAKE,OAAS,K,iBAIVL,EAAAY,UAAAG,MAAN,W,qFACEZ,KAAKE,OAAS,M,iBAIVL,EAAAY,UAAAI,QAAN,SAAcC,G,qFACZd,KAAKK,WAAaS,E,iBAIpBjB,EAAAY,UAAAM,cAAA,eAAAhB,EAAAC,K,MACEA,KAAKgB,gBAAkBhB,KAAKE,SAAUe,EAAAjB,KAAKC,aAAW,MAAAgB,SAAA,SAAAA,EAAAC,eAAgB,EAAK,EAC3E,GAAIlB,KAAKE,OAAQ,CACfiB,YAAW,WACTpB,EAAKI,mBAAqB,I,GACzB,I,KACE,CACLH,KAAKG,mBAAqB,K,GAQ9BN,EAAAY,UAAAW,OAAA,eAAArB,EAAAC,KACE,OACEqB,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACLC,eAAgB,KAChBC,uBAAwBzB,KAAKK,WAC7BqB,sBAAuB1B,KAAKG,oBAE9BwB,MAAO,CAAEC,OAAQ,GAAAC,OAAG7B,KAAKgB,gBAAe,OAC7B,YAAAhB,KAAKM,UAEhBe,EAAA,OAAAC,IAAA,2CAAKC,MAAM,YAAYO,IAAK,SAACtB,GAAO,OAAAT,EAAKQ,aAAaC,EAAlB,GAClCa,EAAa,QAAAC,IAAA,8C,qIA9DG,I", "ignoreList": []}