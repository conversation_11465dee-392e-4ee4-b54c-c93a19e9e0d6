import{r as t,c as s,h as i,H as e,a}from"./p-C3J6Z5OX.js";import{g as h}from"./p-BNEKIkjk.js";const n={itemsPerPage:"Items per page",of:"of",items:"items",pages:"pages"};const c={itemsPerPage:"Itens por página",of:"de",items:"itens",pages:"páginas"};const d={itemsPerPage:"Itens por página",of:"de",items:"itens",pages:"páginas"};const o=":host{display:block}:host .actions_select{width:74px}:host(.full_width){width:100%}@media screen and (max-width: 905px){.items_per_page{display:none}.actions{width:100%;-ms-flex-pack:center;justify-content:center}}@media screen and (max-width: 600px){.actions--text{display:none}}";const r=class{constructor(i){t(this,i);this.bdsPaginationChange=s(this,"bdsPaginationChange");this.bdsItemsPerPageChange=s(this,"bdsItemsPerPageChange");this.value=this.startedPage;this.paginationNumbers=[];this.intoView=null;this.optionsPosition="auto";this.pageCounter=false;this.language="pt_BR";this.dtButtonInitial=null;this.dtButtonPrev=null;this.dtSelectNumber=null;this.dtButtonNext=null;this.dtButtonEnd=null;this.nextPage=t=>{const s=this.value;if(s<this.pages){t.preventDefault();this.value=this.value+1;this.updateItemRange()}};this.previewPage=t=>{const s=this.value;if(s>1){t.preventDefault();this.value=this.value-1;this.updateItemRange()}};this.firstPage=t=>{const s=this.value;if(s>1){t.preventDefault();this.value=this.paginationNumbers[0];this.updateItemRange()}};this.lastPage=t=>{const s=this.value;if(s<this.pages){t.preventDefault();this.value=this.pages;this.updateItemRange()}};this.openOptions=()=>{this.openSelect=!this.openSelect};this.onBlur=()=>{this.openSelect=false}}componentWillLoad(){this.countPage();this.intoView=h(this.el);this.processItemsPage();if(this.pageCounter){this.itemValue=this.itemsPage[0]}this.itemSelected(this.itemValue);this.countItem()}pagesChanged(){this.countPage()}valueChanged(){this.bdsPaginationChange.emit(this.value)}processItemsPage(){if(typeof this.itemsPage==="string"){try{this.itemsPage=JSON.parse(this.itemsPage.replace(/'/g,'"'))}catch(t){this.itemsPage=[]}}}countItem(){if(this.pageCounter){const t=this.numberItems/this.itemValue;this.pages=Math.ceil(t)}}countPage(){if(this.paginationNumbers.length!==0){this.paginationNumbers=[]}if(this.paginationNumbers.length===0){for(let t=1;t<=this.pages;t++){this.paginationNumbers.push(t)}if(this.startedPage&&this.startedPage<this.pages){this.value=this.startedPage}else{this.value=this.paginationNumbers[0]}}}optionSelected(t){this.value=t;this.openOptions();this.updateItemRange()}itemSelected(t){this.itemValue=t;this.itemsPerPage=t;this.openOptions();this.countItem();this.updateItemRange();this.bdsItemsPerPageChange.emit(this.itemsPerPage)}updateItemRange(){this.startItem=(this.value-1)*this.itemsPerPage+1;this.endItem=Math.min(this.value*this.itemsPerPage,this.numberItems)}get currentLanguage(){switch(this.language){case"en_US":return n;case"es_MX":return d;default:return c}}render(){var t;const{currentLanguage:s}=this;return i(e,{key:"827398cd46162dab203a12acaf172a472a69f304",class:{full_width:this.pageCounter}},i("bds-grid",{key:"d2dfbbfed1dec81c08fe6764c0b7a6f966408755","justify-content":"space-between"},this.itemsPerPage&&this.itemsPage&&i("bds-grid",{key:"5d70db426806bdb660de721d75630496cbaeb103",gap:"1","align-items":"center",class:"items_per_page"},i("bds-typo",{key:"1d97b8ead46b0c6bb74b772a81ef43e392dc1a11",variant:"fs-14"},s.itemsPerPage,":"),i("bds-select",{key:"a414451b008a42851aa904490cb20ee601718842",class:"actions_select",value:this.itemValue,"options-position":this.optionsPosition},(t=this.itemsPage)===null||t===void 0?void 0:t.map(((t,s)=>i("bds-select-option",{key:s,value:t,onClick:()=>this.itemSelected(t)},t)))),i("bds-typo",{key:"0f352f773dcf8b54c21e886b9f2f4e0d08d03736",variant:"fs-14","no-wrap":"true"},this.startItem,"-",this.endItem," ",s.of," ",this.numberItems)),i("bds-grid",{key:"5bc8edd434d5781844ce58c6190000d7fd3dbd18",gap:"1","align-items":"center",class:"actions"},i("bds-button-icon",{key:"e060ce147917a37cbaa683e9114851f98fb88961",onBdsClick:t=>this.firstPage(t),size:"short",variant:"secondary",icon:"arrow-first",dataTest:this.dtButtonInitial}),i("bds-button-icon",{key:"170ad484c3e35f79640944634a3c79cffe9d50d9",onBdsClick:t=>this.previewPage(t),size:"short",variant:"secondary",icon:"arrow-left",dataTest:this.dtButtonPrev}),i("bds-select",{key:"5e3283faed149fddbca73f2f6bdc6b30535d1385",class:"actions_select",value:this.value,"options-position":this.optionsPosition},this.paginationNumbers.map(((t,s)=>i("bds-select-option",{key:s,value:t,onClick:()=>this.optionSelected(t)},t)))),this.pageCounter&&i("bds-typo",{key:"4c609b1e5b96aa69624416cfec569d88df690e8f",class:"actions--text",variant:"fs-14","no-wrap":"true"},s.of," ",this.pages," ",s.pages),i("bds-button-icon",{key:"dd633f7150efb7211945a61b98feefd6eb914f25",onBdsClick:t=>this.nextPage(t),size:"short",variant:"secondary",icon:"arrow-right",dataTest:this.dtButtonNext}),i("bds-button-icon",{key:"67efef479945f2e29d89b425e45a55b10cb5dc50",onBdsClick:t=>this.lastPage(t),size:"short",variant:"secondary",icon:"arrow-last",dataTest:this.dtButtonEnd}))))}get el(){return a(this)}static get watchers(){return{pages:["pagesChanged"],startedPage:["pagesChanged"],value:["valueChanged"],itemValue:["itemSelected"]}}};r.style=o;export{r as bds_pagination};
//# sourceMappingURL=p-d7e64d8a.entry.js.map