{"version": 3, "file": "bds-card.entry.esm.js", "sources": ["src/components/card/card.scss?tag=bds-card&encapsulation=shadow", "src/components/card/card.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n    display: flex;\n    position: relative;\n  \n    &:focus-visible {\n      outline: none;\n    }\n  }\n  \n  .card {\n    display: flex;\n    width: 100%;\n  }\n  \n  .card_hover:hover {\n    transform: scale(1.02);\n    transition: all .3s;\n    cursor: pointer;\n}\n\n.card_hover_selectable:hover {\n  box-shadow: 0px 0px 0px 2px rgba(30, 107, 241, 0.08);\n}\n\n.card_hover_selectable:active {\n  box-shadow: 0px 0px 0px 2px rgba(30, 107, 241, 0.24);\n}\n\n  .focus:focus-visible {\n    display: flex;\n    position: absolute;\n    border: 2px solid $color-focus;\n    border-radius: 4px;\n    width: 100%;\n    height: 100%;\n    top: -4px;\n    left: -4px;\n    padding-right: 4px;\n    padding-bottom: 4px;\n    outline: none;\n  }\n  ", "import { Component, ComponentInterface, Host, h, Prop, Event, Element, State, EventEmitter } from '@stencil/core';\nimport { PaperBackground, BorderColor } from '../paper/paper-interface';\n\nexport type elevationType = 'primary' | 'secondary' | 'static' | 'none';\n\n@Component({\n  tag: 'bds-card',\n  styleUrl: 'card.scss',\n  shadow: true,\n})\nexport class Card implements ComponentInterface {\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string = null;\n\n  /**\n   * Prop for set the width of the component.\n   */\n  @Prop() width?: string = 'fit-content';\n  /**\n   * If the prop is true, the component will be clickable.\n   */\n  @Prop() clickable?: boolean = false;\n  /**\n   * Prop for set the background color.\n   */\n  @Prop() bgColor?: PaperBackground = 'surface-1';\n  \n  /**\n   * Prop for set the background color.\n   */\n   @Prop() selectable?: boolean = false;\n  /**\n   * Prop for set the border color.\n   */\n  @Prop({ mutable: true }) borderColor?: BorderColor = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @State() isHovered = false;\n  @State() isPressed = false;\n  @State() elevation: elevationType = 'primary';\n\n  /**\n   * This event will be dispatch when click on the component.\n   */\n  @Event() bdsClick: EventEmitter;\n\n  @Element() element: HTMLElement;\n\n  private cardElement: HTMLElement;\n\n  componentDidLoad() {\n    this.cardElement = this.element.shadowRoot.querySelector('.card');\n\n    if (this.cardElement && (this.clickable === true || this.selectable === true)) {\n      this.cardElement.addEventListener('mouseenter', () => {\n        this.isHovered = true;\n      });\n\n      this.cardElement.addEventListener('mouseleave', () => {\n        this.isHovered = false;\n      });\n\n      this.cardElement.addEventListener('mousedown', () => {\n        this.isPressed = true;\n        this.bdsClick.emit();\n      });\n\n      document.addEventListener('mouseup', () => {\n        this.isPressed = false;\n      });\n\n      this.cardElement.addEventListener('keydown', (event: KeyboardEvent) => {\n        if (event.key === 'Enter') {\n          this.isPressed = true;\n          this.bdsClick.emit();\n        }\n      });\n\n      this.cardElement.addEventListener('keyup', (event: KeyboardEvent) => {\n        if (event.key === 'Enter') {\n          this.isPressed = false;\n        }\n      });\n    }\n  }\n\n  componentDidUpdate() {\n    if (this.isPressed) {\n      this.elevation = 'static';\n    } else if (this.isHovered) {\n      this.elevation = 'secondary';\n    }\n  }\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.isPressed = true;\n      this.bdsClick.emit(event);\n    }\n  }\n\n  render() {\n    const styleHost = {\n      width: this.width,\n    };\n\n    return (\n      <Host style={styleHost}>\n        <bds-paper\n          border={this.clickable ? false : true}\n          elevation={this.elevation}\n          class={{\n            card: true,\n            card_hover: this.clickable,\n            card_hover_selectable: this.isHovered && this.selectable ? true : false,\n            card_hover_pressed: this.isHovered && this.selectable ? true : false\n          }}\n          height={this.height}\n          width={this.width}\n          bgColor={this.bgColor}\n          data-test={this.dataTest}\n          border-color={this.borderColor ? this.borderColor : 'border-1'}\n        >\n          <div tabindex=\"0\" class=\"focus\" onKeyDown={this.handleKeyDown.bind(this)}></div>\n          <bds-grid xxs=\"12\" direction=\"column\" gap=\"2\" padding=\"2\">\n            <slot></slot>\n          </bds-grid>\n        </bds-paper>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,OAAO,GAAG,2xBAA2xB;;MCU9xB,IAAI,GAAA,MAAA;AALjB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAME;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAY,IAAI;AAE9B;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,aAAa;AACtC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AACnC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAqB,WAAW;AAE/C;;AAEG;AACM,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;AACrC;;AAEG;AACsB,QAAA,IAAW,CAAA,WAAA,GAAiB,IAAI;AACzD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAEvB,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AACjB,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AACjB,QAAA,IAAS,CAAA,SAAA,GAAkB,SAAS;AA4F9C;IAjFC,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC;AAEjE,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;YAC7E,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAK;AACnD,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,aAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAK;AACnD,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,aAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAK;AAClD,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,aAAC,CAAC;AAEF,YAAA,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAK;AACxC,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,aAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAoB,KAAI;AACpE,gBAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;AACzB,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;AAExB,aAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAoB,KAAI;AAClE,gBAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;AACzB,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAE1B,aAAC,CAAC;;;IAIN,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;AACpB,aAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AACzB,YAAA,IAAI,CAAC,SAAS,GAAG,WAAW;;;AAIxB,IAAA,aAAa,CAAC,KAAK,EAAA;AACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;AACxB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAI7B,MAAM,GAAA;AACJ,QAAA,MAAM,SAAS,GAAG;YAChB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB;QAED,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,SAAS,EAAA,EACpB,CACE,CAAA,WAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,MAAM,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,EACrC,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,KAAK,EAAE;AACL,gBAAA,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI,CAAC,SAAS;AAC1B,gBAAA,qBAAqB,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,KAAK;AACvE,gBAAA,kBAAkB,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG;aAChE,EACD,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,OAAO,EAAE,IAAI,CAAC,OAAO,EACV,WAAA,EAAA,IAAI,CAAC,QAAQ,EACV,cAAA,EAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,UAAU,EAAA,EAE9D,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,QAAQ,EAAC,GAAG,EAAC,KAAK,EAAC,OAAO,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAQ,CAAA,EAChF,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,GAAG,EAAC,IAAI,EAAC,SAAS,EAAC,QAAQ,EAAC,GAAG,EAAC,GAAG,EAAC,OAAO,EAAC,GAAG,EAAA,EACvD,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACJ,CACD,CACP;;;;;;;;"}