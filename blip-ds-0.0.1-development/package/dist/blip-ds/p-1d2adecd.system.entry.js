var __awaiter=this&&this.__awaiter||function(t,r,o,e){function n(t){return t instanceof o?t:new o((function(r){r(t)}))}return new(o||(o=Promise))((function(o,i){function a(t){try{s(e.next(t))}catch(t){i(t)}}function c(t){try{s(e["throw"](t))}catch(t){i(t)}}function s(t){t.done?o(t.value):n(t.value).then(a,c)}s((e=e.apply(t,r||[])).next())}))};var __generator=this&&this.__generator||function(t,r){var o={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},e,n,i,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(t){return function(r){return s([t,r])}}function s(c){if(e)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(o=0)),o)try{if(e=1,n&&(i=c[0]&2?n["return"]:c[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,c[1])).done)return i;if(n=0,i)c=[c[0]&2,i.value];switch(c[0]){case 0:case 1:i=c;break;case 4:o.label++;return{value:c[1],done:false};case 5:o.label++;n=c[1];c=[0];continue;case 7:c=o.ops.pop();o.trys.pop();continue;default:if(!(i=o.trys,i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){o=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(c[0]===6&&o.label<i[1]){o.label=i[1];i=c;break}if(i&&o.label<i[2]){o.label=i[2];o.ops.push(c);break}if(i[2])o.ops.pop();o.trys.pop();continue}c=r.call(t,o)}catch(t){c=[6,t];n=0}finally{e=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var r,o;return{setters:[function(t){r=t.r;o=t.h}],execute:function(){var e='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';var n=t("bds_accordion_body",function(){function t(t){var o=this;r(this,t);this.container=null;this.isOpen=false;this.isOpenAftAnimation=false;this.numberElement=null;this.hasDivisor=true;this.dataTest=null;this.refContainer=function(t){o.container=t}}t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=!this.isOpen;return[2]}))}))};t.prototype.open=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=true;return[2]}))}))};t.prototype.close=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=false;return[2]}))}))};t.prototype.divisor=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){this.hasDivisor=t;return[2]}))}))};t.prototype.isOpenChanged=function(){var t=this;var r;this.heightContainer=this.isOpen?((r=this.container)===null||r===void 0?void 0:r.offsetHeight)||0:0;if(this.isOpen){setTimeout((function(){t.isOpenAftAnimation=true}),500)}else{this.isOpenAftAnimation=false}};t.prototype.render=function(){var t=this;return o("div",{key:"5976bbf52d123b3534d352a00dabe2df406dad25",class:{accordion_body:true,accordion_body_divisor:this.hasDivisor,accordion_body_isOpen:this.isOpenAftAnimation},style:{height:"".concat(this.heightContainer,"px")},"data-test":this.dataTest},o("div",{key:"c1038dc51ecd25fbfd5a18b11c52fb901b504f56",class:"container",ref:function(r){return t.refContainer(r)}},o("slot",{key:"ba113dcabd2c5ed50dd672284a61702b1741f877"})))};Object.defineProperty(t,"watchers",{get:function(){return{isOpen:["isOpenChanged"]}},enumerable:false,configurable:true});return t}());n.style=e}}}));
//# sourceMappingURL=p-1d2adecd.system.entry.js.map