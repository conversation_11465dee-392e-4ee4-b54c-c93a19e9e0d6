{"version": 3, "file": "bds-divider.entry.esm.js", "sources": ["src/components/divider/divider.scss?tag=bds-divider&encapsulation=shadow", "src/components/divider/divider.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host(.horizontal) { \n  width: 100%;\n}\n:host(.vertical) { \n  height: 100%;\n}\nhr {\n  border: none;\n\n  &.horizontal {\n    width: 100%;\n    border-top-width: 1px;\n    margin: 16px 0;\n\n    &.solid {\n      border-top-style: solid;\n    }\n\n    &.dotted {\n      border-top-style: dotted;\n    }\n\n    &.dashed {\n      border-top-style: dashed;\n    }\n  }\n\n  &.vertical {\n    height: 100%;\n    border-left-width: 1px;\n    margin: 0 16px;\n\n    &.solid {\n      border-left-style: solid;\n    }\n\n    &.dotted {\n      border-left-style: dotted;\n    }\n\n    &.dashed {\n      border-left-style: dashed;\n    }\n  }\n\n  /* Cores */\n  &.color-divider-1 {\n    border-color: $color-border-1;\n  }\n\n  &.color-divider-2 {\n    border-color: $color-border-2;\n  }\n\n  &.color-divider-3 {\n    border-color: $color-border-3;\n  }\n\n  /* Margens */\n  @for $i from 4 through 64 {\n    &.margin-#{$i} {\n      margin: #{$i}px;\n    }\n  }\n}\n", "import { Component, h, Prop, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-divider',\n  styleUrl: 'divider.scss',\n  shadow: true,\n})\nexport class BdsDivider {\n\n  /**\n   * O tipo de estilo da linha: sólida, pontil<PERSON>a, tracejada\n   */\n  @Prop() styleType: 'solid' | 'dotted' | 'dashed' = 'solid';\n\n  /**\n   * Define se o divider deve ser exibido horizontalmente ou verticalmente\n   */\n  @Prop() orientation: 'horizontal' | 'vertical' = 'horizontal';\n\n  /**\n   * Cor da linha, aceitando qualquer valor válido em CSS (hex, rgb, nome da cor)\n   */\n  @Prop() color: 'divider-1' | 'divider-2' | 'divider-3' = 'divider-1';\n\n  render() {\n    const orientationClass = `${this.orientation} ${this.styleType} color-${this.color} `;\n\n    return (\n    <Host class={orientationClass}>\n      <hr class={`${this.orientation} ${this.styleType} color-${this.color}`} />\n    </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,UAAU,GAAG,qpEAAqpE;;MCO3pE,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOE;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAkC,OAAO;AAE1D;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAA8B,YAAY;AAE7D;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAA4C,WAAW;AAWrE;IATC,MAAM,GAAA;AACJ,QAAA,MAAM,gBAAgB,GAAG,CAAG,EAAA,IAAI,CAAC,WAAW,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,CAAU,OAAA,EAAA,IAAI,CAAC,KAAK,GAAG;AAErF,QAAA,QACA,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,gBAAgB,EAAA,EAC3B,CAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAI,KAAK,EAAE,CAAA,EAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAU,OAAA,EAAA,IAAI,CAAC,KAAK,CAAA,CAAE,EAAI,CAAA,CACrE;;;;;;;"}