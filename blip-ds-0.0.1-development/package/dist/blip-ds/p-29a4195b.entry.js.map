{"version": 3, "names": ["carouselCss", "BdsCarousel", "constructor", "hostRef", "this", "itemsElement", "bulletElement", "bulletElements", "itemActivated", "seconds", "isWhole", "heightCarousel", "framePressed", "autoplayState", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoHeight", "bullets", "bulletsPosition", "infiniteLoop", "arrows", "slidePerPage", "gap", "grab", "loading", "dtSlideContent", "dtButtonPrev", "dtButtonNext", "secondsLimit", "setInternalItens", "ItensElement", "floor", "Math", "length", "numberOfColumns", "newItens", "getItems", "internalItens", "startCountSeconds", "incrementSeconds", "setInterval", "updateHeight", "elementActive", "heightFrame", "getVisibleItens", "slice", "getHighestItem", "offsetHeight", "frame", "style", "height", "refFrame", "el", "refThemeProviderArrows", "themeProviderArrows", "ref<PERSON>ram<PERSON><PERSON><PERSON><PERSON><PERSON>", "frameRepeater", "refBulletElement", "push", "onMouseOver", "pauseAutoplay", "onMouseOut", "runAutoplay", "onMouseDown", "ev", "offsetFrame", "offsetLeft", "element", "startX", "pageX", "endX", "cursor", "onMouseEnter", "onMouseUp", "boundItems", "onMouseMove", "preventDefault", "nextSlide", "prevSlide", "setKeydownNavigation", "key", "focus", "componentWillLoad", "getElementsByTagName", "Array", "from", "componentDidRender", "width", "gapChanged", "marginLeft", "i", "widthFrame", "offsetWidth", "padding", "firstItemActived", "theme", "round", "componentDidLoad", "itemActivatedChanged", "currentItemSelected", "find", "item", "id", "slideFrame", "isWholeWidth", "right", "bdsChangeCarousel", "emit", "value", "autoplayTimeoutChanged", "secondsC<PERSON>ed", "isWholeChanged", "undefined", "newItem", "_a", "label", "_b", "buildCarousel", "setTimeout", "setActivated", "clearInterval", "render", "ThemeOrDivArrows", "justifybulletsPosition", "h", "class", "carousel", "carousel_slide", "carousel_slide_fullwidth", "tabindex", "onKeyDown", "ref", "carousel_slide_frame", "carousel_slide_frame_loading", "carousel_slide_frame_repeater", "carousel_slide_loading", "carousel_slide_loading_visible", "shape", "carousel_buttons", "carousel_buttons_fullwidth", "variant", "iconLeft", "color", "onBdsClick", "disabled", "dataTest", "carousel_bullets", "carousel_bullets_inside", "xxs", "carousel_bullets_card", "carousel_bullets_card_inside", "map", "index", "carousel_bullets_item", "carousel_bullets_item_active", "onClick", "carousel_bullets_item_conclude", "carousel_bullets_item_loader", "animationDuration", "animationPlayState", "name"], "sources": ["src/components/carousel/carousel.scss?tag=bds-carousel&encapsulation=shadow", "src/components/carousel/carousel.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  position: relative;\n}\n\n.carousel {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  max-width: 1920px;\n  position: relative;\n\n  &_slide {\n    width: 100%;\n    position: relative;\n    box-sizing: border-box;\n    padding: 0 48px;\n\n    &::after {\n      content: '';\n      position: absolute;\n      inset: -8px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n      pointer-events: none;\n    }\n\n    &:focus-visible {\n      outline: none;\n      &::after {\n        border-color: $color-focus;\n      }\n    }\n\n    &_fullwidth {\n      padding: 0;\n    }\n\n    &_frame {\n      width: 100%;\n      display: flex;\n      overflow: hidden;\n      -webkit-transition: height ease-in-out 0.5s;\n      -moz-transition: height ease-in-out 0.5s;\n      transition: height ease-in-out 0.5s;\n\n      &_loading {\n        opacity: 0;\n        pointer-events: none;\n      }\n\n      & * {\n        -webkit-user-select: none; /* Safari */\n        -ms-user-select: none; /* IE 10 and IE 11 */\n        user-select: none; /* Standard syntax */\n        -webkit-user-drag: none;\n        -khtml-user-drag: none;\n        -moz-user-drag: none;\n        -o-user-drag: none;\n      }\n\n      & *[slot='loop'] {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n\n      &_repeater {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n    }\n    &_loading {\n      opacity: 0;\n      pointer-events: none;\n      position: absolute;\n      inset: 0;\n\n      &_visible {\n        opacity: 1;\n        pointer-events: all;\n      }\n    }\n  }\n  &_loading_bar {\n    box-sizing: border-box;\n    padding: 0 60px;\n    margin-top: 8px;\n\n    &_fullwidth {\n      padding: 0 4px;\n    }\n  }\n\n  &_buttons {\n    position: absolute;\n    width: 100%;\n    height: 0px;\n    top: calc(50% - 20px);\n    left: 0;\n    display: flex;\n    justify-content: space-between;\n    box-sizing: border-box;\n\n    &_fullwidth {\n      padding: 0 8px;\n    }\n  }\n\n  &_bullets {\n    position: relative;\n    margin-top: 8px;\n\n    &_inside {\n      position: absolute;\n      bottom: 0px;\n      width: 100%;\n      margin: 0;\n      padding: 0px 16px;\n      box-sizing: border-box;\n    }\n\n    &_card {\n      width: fit-content;\n      display: inline-flex;\n      gap: 8px;\n\n      &_inside {\n        border-top-left-radius: 8px;\n        border-top-right-radius: 8px;\n        padding: 8px;\n        background-color: $color-surface-0;\n      }\n    }\n\n    &_item {\n      width: 16px;\n      height: 16px;\n      border: 2px solid $color-border-1;\n      border-radius: 50%;\n      position: relative;\n      transform: rotate(45deg);\n      cursor: pointer;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: 4px;\n        border-radius: 50%;\n      }\n\n      &::after {\n        content: '';\n        position: absolute;\n        inset: -8px;\n        transform: rotate(-45deg);\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n        &::after {\n          border-color: $color-focus;\n        }\n      }\n\n      &_active {\n        &::before {\n          background-color: $color-primary;\n        }\n      }\n\n      &_conclude {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-content-disable;\n      }\n\n      &_loader {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-primary;\n        animation: l18 linear;\n      }\n    }\n  }\n}\n\n@keyframes l18 {\n  0% {\n    clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);\n  }\n  25% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);\n  }\n  50% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);\n  }\n  75% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);\n  }\n  100% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);\n  }\n}\n", "import { Component, h, Element, State, Watch, Prop, Method, Event, EventEmitter } from '@stencil/core';\nimport { Itens, arrows, bullets, bulletsPositions, gap } from './carousel-interface';\nimport { gapChanged, getHighestItem, getItems } from '../../utils/position-element';\n\n@Component({\n  tag: 'bds-carousel',\n  styleUrl: 'carousel.scss',\n  shadow: true,\n})\nexport class BdsCarousel {\n  private itemsElement?: HTMLCollectionOf<HTMLBdsCarouselItemElement> = null;\n  private bulletElement?: HTMLElement = null;\n  private bulletElements: HTMLElement[] = [];\n  private frame?: HTMLElement;\n  private themeProviderArrows?: any;\n  private frameRepeater?: HTMLElement;\n  private incrementSeconds?: any;\n\n  @Element() element: HTMLElement;\n\n  @State() itemActivated = 1;\n  @State() seconds = 0;\n  @State() internalItens: Itens[];\n  @State() isWhole = 0;\n  @State() heightCarousel?: number = 240;\n  @State() framePressed?: boolean = false;\n  @State() startX?: number;\n  @State() endX?: number;\n  @State() autoplayState: 'paused' | 'running' = 'running';\n\n  /**\n   * Autoplay. Prop to Enable component autoplay.\n   */\n  @Prop() autoplay?: boolean = false;\n\n  /**\n   * AutoplayTimeout. Prop to Choose the Autoplay time in milliseconds, ex: 5000.\n   */\n  @Prop() autoplayTimeout?: number = 5000;\n\n  /**\n   * AutoplayHoverPause. Prop to Enable it if you will have the function to pause autoplay when on hover.\n   */\n  @Prop() autoplayHoverPause?: boolean = false;\n\n  /**\n   * autoHeight. Prop to Enable it if you want the component to adjust its height relative to the active items..\n   */\n  @Prop() autoHeight?: boolean = false;\n\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bullets?: boolean | bullets = 'outside';\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bulletsPosition?: bulletsPositions = 'center';\n\n  /**\n   * InfiniteLoop. Prop to Enable if the component will have infinite loop.\n   */\n  @Prop() infiniteLoop?: boolean = false;\n\n  /**\n   * arrows. Prop to select type of arrows in component. Are available \"outside\" | \"inside\" | \"none\".\n   */\n  @Prop() arrows?: arrows = 'outside';\n\n  /**\n   * SlidePerPage. Prop to Choose the number of slide per page you will have available in the carousel.\n   */\n  @Prop() slidePerPage?: number = 1;\n\n  /**\n   * Gap. Prop to Select the gap distance between items.\n   */\n  @Prop() gap?: gap = 'none';\n\n  /**\n   * Grab. Prop to enable function of grab in carousel.\n   */\n  @Prop() grab?: boolean = true;\n\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop({ mutable: true, reflect: true }) loading?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtSlideContent is the data-test to slide action.\n   */\n  @Prop() dtSlideContent?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  @State() secondsLimit: number = this.autoplayTimeout / 1000;\n\n  /**\n   * Emitted when active frame value.\n   */\n  @Event() bdsChangeCarousel!: EventEmitter;\n\n  componentWillLoad() {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.setInternalItens(Array.from(this.itemsElement));\n    if (this.bullets == true) {\n      this.bullets = 'outside';\n    }\n    if (this.bullets == false) {\n      this.bullets = 'none';\n    }\n  }\n\n  componentDidRender() {\n    if (!this.loading) {\n      if (this.gap != 'none') {\n        this.frame.style.width = `calc(100% + ${gapChanged(this.gap)}px)`;\n        this.frame.style.marginLeft = `-${gapChanged(this.gap) / 2}px`;\n      }\n      for (let i = 0; i < this.itemsElement.length; i++) {\n        const widthFrame = this.frame.offsetWidth >= 1920 ? 1920 : this.frame.offsetWidth;\n        this.itemsElement[i].style.width = `${widthFrame / this.slidePerPage}px`;\n        this.itemsElement[i].style.padding = `0 ${gapChanged(this.gap) / 2}px`;\n      }\n      if (this.autoHeight) this.updateHeight(Array.from(this.itemsElement));\n    }\n    if (this.arrows == 'inside') {\n      const firstItemActived = (this.itemActivated - 1) * (this.itemsElement.length / this.internalItens.length) + 1;\n      this.themeProviderArrows.theme =\n        this.slidePerPage <= 1\n          ? this.itemsElement[this.itemActivated - 1].theme\n          : this.itemsElement[Math.round(firstItemActived)].theme;\n    }\n  }\n\n  componentDidLoad() {\n    this.startCountSeconds();\n  }\n\n  @Watch('itemActivated')\n  protected itemActivatedChanged(): void {\n    const currentItemSelected: Itens = this.internalItens.find((item) => item.id === this.itemActivated);\n    const slideFrame = !this.frame ? 0 : this.frame.offsetWidth * (this.itemActivated - 1);\n    if (this.frameRepeater) {\n      if (currentItemSelected.isWhole) {\n        const isWholeWidth = this.itemsElement[1].offsetWidth * (this.slidePerPage - this.isWhole);\n        this.frameRepeater.style.right = `${slideFrame - isWholeWidth}px`;\n      } else {\n        this.frameRepeater.style.right = `${slideFrame}px`;\n      }\n    }\n    this.bdsChangeCarousel.emit({ value: currentItemSelected });\n  }\n\n  @Watch('autoplayTimeout')\n  protected autoplayTimeoutChanged(): void {\n    this.secondsLimit = this.autoplayTimeout / 1000;\n  }\n\n  @Watch('seconds')\n  protected secondsChanged(): void {\n    if (this.seconds >= this.secondsLimit) {\n      this.nextSlide();\n      this.seconds = 0;\n    }\n  }\n\n  @Watch('isWhole')\n  protected isWholeChanged(): void {\n    if (this.internalItens != undefined) {\n      if (this.isWhole > 0) {\n        const newItem = {\n          id: this.internalItens?.length + 1,\n          label: `Frame - ${this.internalItens?.length + 1}`,\n          isWhole: true,\n        };\n        this.internalItens = [...this.internalItens, newItem];\n      }\n    }\n  }\n\n  private setInternalItens = (ItensElement) => {\n    const floor = Math.floor(ItensElement.length / this.slidePerPage);\n    const numberOfColumns = ItensElement.length / this.slidePerPage;\n    const newItens = getItems(numberOfColumns);\n    this.internalItens = newItens;\n    this.isWhole = ItensElement.length - this.slidePerPage * floor;\n  };\n\n  private startCountSeconds = () => {\n    if (this.autoplay) {\n      this.incrementSeconds = setInterval(() => {\n        this.seconds += 0.1;\n      }, 100);\n    }\n  };\n\n  private updateHeight = (itemsElement) => {\n    const elementActive = itemsElement[this.itemActivated * this.slidePerPage - this.slidePerPage];\n    let heightFrame = 240;\n    if (this.slidePerPage > 1) {\n      const getVisibleItens =\n        this.isWhole > 0 && this.itemActivated == this.internalItens.length\n          ? itemsElement.slice(\n              this.internalItens.length - this.internalItens.length - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            )\n          : itemsElement.slice(\n              this.itemActivated * this.slidePerPage - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            );\n\n      heightFrame = getHighestItem(getVisibleItens)[0];\n    } else {\n      heightFrame = elementActive.offsetHeight;\n    }\n    this.frame.style.height = `${heightFrame}px`;\n  };\n\n  @Method()\n  async buildCarousel(): Promise<void> {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.loading = true;\n    setTimeout(\n      () => (this.setInternalItens(Array.from(this.itemsElement)), (this.loading = false), this.setActivated(1)),\n      1000,\n    );\n  }\n\n  @Method()\n  async nextSlide(): Promise<void> {\n    if (this.itemActivated == this.internalItens.length) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = 1;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated + 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async prevSlide(): Promise<void> {\n    if (this.itemActivated == 1) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = this.internalItens.length;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated - 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async setActivated(item: number): Promise<void> {\n    this.itemActivated = item;\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  @Method()\n  async pauseAutoplay(): Promise<void> {\n    clearInterval(this.incrementSeconds);\n    this.autoplayState = 'paused';\n  }\n\n  @Method()\n  async runAutoplay(): Promise<void> {\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  private refFrame = (el: HTMLElement): void => {\n    this.frame = el;\n  };\n\n  private refThemeProviderArrows = (el: HTMLBdsThemeProviderElement | HTMLElement): void => {\n    this.themeProviderArrows = el;\n  };\n\n  private refFrameRepeater = (el: HTMLElement): void => {\n    this.frameRepeater = el;\n  };\n\n  private refBulletElement = (el: HTMLElement): void => {\n    if (el) {\n      this.bulletElement = el; // Keep the current behavior\n      this.bulletElements.push(el); // Store all bullet elements\n    }\n  };\n\n  private onMouseOver = () => {\n    if (this.autoplayHoverPause) {\n      this.pauseAutoplay();\n    }\n  };\n\n  private onMouseOut = () => {\n    if (this.autoplayHoverPause) {\n      this.runAutoplay();\n    }\n  };\n\n  private onMouseDown = (ev: MouseEvent) => {\n    if (this.grab) {\n      this.framePressed = true;\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n      this.startX = ev.pageX - offsetFrame;\n      this.endX = ev.pageX - offsetFrame;\n      this.frame.style.cursor = 'grabbing';\n    }\n  };\n\n  private onMouseEnter = () => {\n    if (this.grab) {\n      this.frame.style.cursor = 'grab';\n    }\n  };\n\n  private onMouseUp = () => {\n    if (this.grab) {\n      this.framePressed = false;\n      this.frame.style.cursor = 'grab';\n      this.boundItems();\n      if (this.autoplayHoverPause) {\n        this.pauseAutoplay();\n      }\n    }\n  };\n\n  private onMouseMove = (ev: MouseEvent) => {\n    if (this.grab) {\n      if (!this.framePressed) return;\n      ev.preventDefault();\n\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n\n      this.endX = ev.pageX - offsetFrame;\n    }\n  };\n\n  private boundItems = () => {\n    if (this.endX < this.startX) {\n      this.nextSlide();\n      this.seconds = 0;\n    } else if (this.endX > this.startX) {\n      this.prevSlide();\n      this.seconds = 0;\n    }\n  };\n\n  private setKeydownNavigation = (ev) => {\n    if (ev.key === 'Tab') {\n      if (this.bulletElements.length > 0) {\n        this.bulletElements[0].focus();\n      } else if (this.bulletElement) {\n        this.bulletElement.focus();\n      }\n    }\n    if (ev.key === 'ArrowRight') {\n      this.nextSlide();\n    }\n    if (ev.key === 'ArrowLeft') {\n      this.prevSlide();\n    }\n  };\n\n  render() {\n    // Reset bullet elements array at start of render\n    this.bulletElements = [];\n    \n    const ThemeOrDivArrows = this.arrows == 'inside' ? 'bds-theme-provider' : 'div';\n    const justifybulletsPosition =\n      this.bulletsPosition == 'center'\n        ? 'center'\n        : this.bulletsPosition == 'right'\n          ? 'flex-end'\n          : this.bulletsPosition == 'left' && 'flex-start';\n    return (\n      <div class={{ carousel: true }}>\n        <div\n          class={{\n            carousel_slide: true,\n            carousel_slide_fullwidth: this.arrows != 'outside',\n            [`carousel_slide_state_${this.autoplayState}`]: this.autoplay,\n          }}\n          tabindex=\"0\"\n          onKeyDown={(ev) => this.setKeydownNavigation(ev)}\n          data-test={this.dtSlideContent}\n        >\n          <div\n            ref={(el) => this.refFrame(el)}\n            class={{ carousel_slide_frame: true, carousel_slide_frame_loading: this.loading }}\n            onMouseOver={() => this.onMouseOver()}\n            onMouseOut={() => this.onMouseOut()}\n            onMouseDown={(ev) => this.onMouseDown(ev)}\n            onMouseEnter={() => this.onMouseEnter()}\n            onMouseUp={() => this.onMouseUp()}\n            onMouseMove={(ev) => this.onMouseMove(ev)}\n          >\n            <div ref={(el) => this.refFrameRepeater(el)} class={{ carousel_slide_frame_repeater: true }}>\n              <slot />\n            </div>\n          </div>\n          <bds-grid class={{ carousel_slide_loading: true, carousel_slide_loading_visible: this.loading }}>\n            <bds-skeleton height=\"100%\" shape=\"square\" width=\"100%\" />\n          </bds-grid>\n          {this.arrows != 'none' && !this.loading && (\n            <ThemeOrDivArrows\n              ref={(el) => this.refThemeProviderArrows(el)}\n              class={{\n                carousel_buttons: true,\n                carousel_buttons_fullwidth: this.arrows != 'outside',\n              }}\n            >\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-left\"\n                color=\"content\"\n                onBdsClick={() => this.prevSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated <= 1}\n                dataTest={this.dtButtonPrev}\n              ></bds-button>\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-right\"\n                color=\"content\"\n                onBdsClick={() => this.nextSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated >= this.internalItens.length}\n                dataTest={this.dtButtonNext}\n              ></bds-button>\n            </ThemeOrDivArrows>\n          )}\n        </div>\n        {this.internalItens.length > 1 && this.bullets != 'none' && (\n          <div\n            class={{\n              carousel_bullets: true,\n              carousel_bullets_inside: this.bullets == 'inside',\n            }}\n          >\n            {this.loading && this.bullets != 'inside' ? (\n              <bds-grid\n                xxs=\"12\"\n                gap=\"1\"\n                justify-content={justifybulletsPosition}\n                padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n              >\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n              </bds-grid>\n            ) : (\n              this.internalItens && (\n                <bds-grid\n                  xxs=\"12\"\n                  justify-content={justifybulletsPosition}\n                  padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n                >\n                  <div\n                    class={{\n                      carousel_bullets_card: true,\n                      carousel_bullets_card_inside: this.bullets == 'inside',\n                    }}\n                  >\n                    {this.internalItens.map((item, index) => (\n                      <div\n                        key={index}\n                        ref={(el) => this.refBulletElement(el)}\n                        class={{\n                          carousel_bullets_item: true,\n                          carousel_bullets_item_active: item.id == this.itemActivated,\n                        }}\n                        tabindex=\"0\"\n                        onClick={() => this.setActivated(item.id)}\n                      >\n                        {item.id < this.itemActivated && this.autoplay && (\n                          <div class={{ carousel_bullets_item_conclude: true }}></div>\n                        )}\n                        {item.id == this.itemActivated && this.autoplay && (\n                          <div\n                            class={{ carousel_bullets_item_loader: true }}\n                            style={{\n                              animationDuration: `${this.autoplayTimeout / 1000 - 0.1}s`,\n                              animationPlayState: this.autoplayState,\n                            }}\n                          ></div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </bds-grid>\n              )\n            )}\n          </div>\n        )}\n        <slot name=\"after\"></slot>\n      </div>\n    );\n  }\n}\n"], "mappings": "2GAAA,MAAMA,EAAc,kgK,MCSPC,EAAW,MALxB,WAAAC,CAAAC,G,6DAMUC,KAAYC,aAAkD,KAC9DD,KAAaE,cAAiB,KAC9BF,KAAcG,eAAkB,GAQ/BH,KAAaI,cAAG,EAChBJ,KAAOK,QAAG,EAEVL,KAAOM,QAAG,EACVN,KAAcO,eAAY,IAC1BP,KAAYQ,aAAa,MAGzBR,KAAaS,cAAyB,UAKvCT,KAAQU,SAAa,MAKrBV,KAAeW,gBAAY,IAK3BX,KAAkBY,mBAAa,MAK/BZ,KAAUa,WAAa,MAKvBb,KAAOc,QAAuB,UAI9Bd,KAAee,gBAAsB,SAKrCf,KAAYgB,aAAa,MAKzBhB,KAAMiB,OAAY,UAKlBjB,KAAYkB,aAAY,EAKxBlB,KAAGmB,IAAS,OAKZnB,KAAIoB,KAAa,KAKepB,KAAOqB,QAAa,MAMpDrB,KAAcsB,eAAY,KAM1BtB,KAAYuB,aAAY,KAMxBvB,KAAYwB,aAAY,KAEvBxB,KAAAyB,aAAuBzB,KAAKW,gBAAkB,IAwF/CX,KAAA0B,iBAAoBC,IAC1B,MAAMC,EAAQC,KAAKD,MAAMD,EAAaG,OAAS9B,KAAKkB,cACpD,MAAMa,EAAkBJ,EAAaG,OAAS9B,KAAKkB,aACnD,MAAMc,EAAWC,EAASF,GAC1B/B,KAAKkC,cAAgBF,EACrBhC,KAAKM,QAAUqB,EAAaG,OAAS9B,KAAKkB,aAAeU,CAAK,EAGxD5B,KAAiBmC,kBAAG,KAC1B,GAAInC,KAAKU,SAAU,CACjBV,KAAKoC,iBAAmBC,aAAY,KAClCrC,KAAKK,SAAW,EAAG,GAClB,I,GAICL,KAAAsC,aAAgBrC,IACtB,MAAMsC,EAAgBtC,EAAaD,KAAKI,cAAgBJ,KAAKkB,aAAelB,KAAKkB,cACjF,IAAIsB,EAAc,IAClB,GAAIxC,KAAKkB,aAAe,EAAG,CACzB,MAAMuB,EACJzC,KAAKM,QAAU,GAAKN,KAAKI,eAAiBJ,KAAKkC,cAAcJ,OACzD7B,EAAayC,MACX1C,KAAKkC,cAAcJ,OAAS9B,KAAKkC,cAAcJ,OAAS9B,KAAKkB,aAC7DlB,KAAKI,cAAgBJ,KAAKkB,cAE5BjB,EAAayC,MACX1C,KAAKI,cAAgBJ,KAAKkB,aAAelB,KAAKkB,aAC9ClB,KAAKI,cAAgBJ,KAAKkB,cAGlCsB,EAAcG,EAAeF,GAAiB,E,KACzC,CACLD,EAAcD,EAAcK,Y,CAE9B5C,KAAK6C,MAAMC,MAAMC,OAAS,GAAGP,KAAe,EAoEtCxC,KAAAgD,SAAYC,IAClBjD,KAAK6C,MAAQI,CAAE,EAGTjD,KAAAkD,uBAA0BD,IAChCjD,KAAKmD,oBAAsBF,CAAE,EAGvBjD,KAAAoD,iBAAoBH,IAC1BjD,KAAKqD,cAAgBJ,CAAE,EAGjBjD,KAAAsD,iBAAoBL,IAC1B,GAAIA,EAAI,CACNjD,KAAKE,cAAgB+C,EACrBjD,KAAKG,eAAeoD,KAAKN,E,GAIrBjD,KAAWwD,YAAG,KACpB,GAAIxD,KAAKY,mBAAoB,CAC3BZ,KAAKyD,e,GAIDzD,KAAU0D,WAAG,KACnB,GAAI1D,KAAKY,mBAAoB,CAC3BZ,KAAK2D,a,GAID3D,KAAA4D,YAAeC,IACrB,GAAI7D,KAAKoB,KAAM,CACbpB,KAAKQ,aAAe,KACpB,MAAMsD,EAAc9D,KAAK6C,MAAMkB,WAAa/D,KAAKgE,QAAQD,WACzD/D,KAAKiE,OAASJ,EAAGK,MAAQJ,EACzB9D,KAAKmE,KAAON,EAAGK,MAAQJ,EACvB9D,KAAK6C,MAAMC,MAAMsB,OAAS,U,GAItBpE,KAAYqE,aAAG,KACrB,GAAIrE,KAAKoB,KAAM,CACbpB,KAAK6C,MAAMC,MAAMsB,OAAS,M,GAItBpE,KAASsE,UAAG,KAClB,GAAItE,KAAKoB,KAAM,CACbpB,KAAKQ,aAAe,MACpBR,KAAK6C,MAAMC,MAAMsB,OAAS,OAC1BpE,KAAKuE,aACL,GAAIvE,KAAKY,mBAAoB,CAC3BZ,KAAKyD,e,IAKHzD,KAAAwE,YAAeX,IACrB,GAAI7D,KAAKoB,KAAM,CACb,IAAKpB,KAAKQ,aAAc,OACxBqD,EAAGY,iBAEH,MAAMX,EAAc9D,KAAK6C,MAAMkB,WAAa/D,KAAKgE,QAAQD,WAEzD/D,KAAKmE,KAAON,EAAGK,MAAQJ,C,GAInB9D,KAAUuE,WAAG,KACnB,GAAIvE,KAAKmE,KAAOnE,KAAKiE,OAAQ,CAC3BjE,KAAK0E,YACL1E,KAAKK,QAAU,C,MACV,GAAIL,KAAKmE,KAAOnE,KAAKiE,OAAQ,CAClCjE,KAAK2E,YACL3E,KAAKK,QAAU,C,GAIXL,KAAA4E,qBAAwBf,IAC9B,GAAIA,EAAGgB,MAAQ,MAAO,CACpB,GAAI7E,KAAKG,eAAe2B,OAAS,EAAG,CAClC9B,KAAKG,eAAe,GAAG2E,O,MAClB,GAAI9E,KAAKE,cAAe,CAC7BF,KAAKE,cAAc4E,O,EAGvB,GAAIjB,EAAGgB,MAAQ,aAAc,CAC3B7E,KAAK0E,W,CAEP,GAAIb,EAAGgB,MAAQ,YAAa,CAC1B7E,KAAK2E,W,EAyIV,CA5ZC,iBAAAI,GACE/E,KAAKC,aAAeD,KAAKgE,QAAQgB,qBAC/B,qBAEFhF,KAAK0B,iBAAiBuD,MAAMC,KAAKlF,KAAKC,eACtC,GAAID,KAAKc,SAAW,KAAM,CACxBd,KAAKc,QAAU,S,CAEjB,GAAId,KAAKc,SAAW,MAAO,CACzBd,KAAKc,QAAU,M,EAInB,kBAAAqE,GACE,IAAKnF,KAAKqB,QAAS,CACjB,GAAIrB,KAAKmB,KAAO,OAAQ,CACtBnB,KAAK6C,MAAMC,MAAMsC,MAAQ,eAAeC,EAAWrF,KAAKmB,UACxDnB,KAAK6C,MAAMC,MAAMwC,WAAa,IAAID,EAAWrF,KAAKmB,KAAO,K,CAE3D,IAAK,IAAIoE,EAAI,EAAGA,EAAIvF,KAAKC,aAAa6B,OAAQyD,IAAK,CACjD,MAAMC,EAAaxF,KAAK6C,MAAM4C,aAAe,KAAO,KAAOzF,KAAK6C,MAAM4C,YACtEzF,KAAKC,aAAasF,GAAGzC,MAAMsC,MAAQ,GAAGI,EAAaxF,KAAKkB,iBACxDlB,KAAKC,aAAasF,GAAGzC,MAAM4C,QAAU,KAAKL,EAAWrF,KAAKmB,KAAO,K,CAEnE,GAAInB,KAAKa,WAAYb,KAAKsC,aAAa2C,MAAMC,KAAKlF,KAAKC,c,CAEzD,GAAID,KAAKiB,QAAU,SAAU,CAC3B,MAAM0E,GAAoB3F,KAAKI,cAAgB,IAAMJ,KAAKC,aAAa6B,OAAS9B,KAAKkC,cAAcJ,QAAU,EAC7G9B,KAAKmD,oBAAoByC,MACvB5F,KAAKkB,cAAgB,EACjBlB,KAAKC,aAAaD,KAAKI,cAAgB,GAAGwF,MAC1C5F,KAAKC,aAAa4B,KAAKgE,MAAMF,IAAmBC,K,EAI1D,gBAAAE,GACE9F,KAAKmC,mB,CAIG,oBAAA4D,GACR,MAAMC,EAA6BhG,KAAKkC,cAAc+D,MAAMC,GAASA,EAAKC,KAAOnG,KAAKI,gBACtF,MAAMgG,GAAcpG,KAAK6C,MAAQ,EAAI7C,KAAK6C,MAAM4C,aAAezF,KAAKI,cAAgB,GACpF,GAAIJ,KAAKqD,cAAe,CACtB,GAAI2C,EAAoB1F,QAAS,CAC/B,MAAM+F,EAAerG,KAAKC,aAAa,GAAGwF,aAAezF,KAAKkB,aAAelB,KAAKM,SAClFN,KAAKqD,cAAcP,MAAMwD,MAAQ,GAAGF,EAAaC,K,KAC5C,CACLrG,KAAKqD,cAAcP,MAAMwD,MAAQ,GAAGF,K,EAGxCpG,KAAKuG,kBAAkBC,KAAK,CAAEC,MAAOT,G,CAI7B,sBAAAU,GACR1G,KAAKyB,aAAezB,KAAKW,gBAAkB,G,CAInC,cAAAgG,GACR,GAAI3G,KAAKK,SAAWL,KAAKyB,aAAc,CACrCzB,KAAK0E,YACL1E,KAAKK,QAAU,C,EAKT,cAAAuG,G,QACR,GAAI5G,KAAKkC,eAAiB2E,UAAW,CACnC,GAAI7G,KAAKM,QAAU,EAAG,CACpB,MAAMwG,EAAU,CACdX,KAAIY,EAAA/G,KAAKkC,iBAAa,MAAA6E,SAAA,SAAAA,EAAEjF,QAAS,EACjCkF,MAAO,aAAWC,EAAAjH,KAAKkC,iBAAe,MAAA+E,SAAA,SAAAA,EAAAnF,QAAS,IAC/CxB,QAAS,MAEXN,KAAKkC,cAAgB,IAAIlC,KAAKkC,cAAe4E,E,GA4CnD,mBAAMI,GACJlH,KAAKC,aAAeD,KAAKgE,QAAQgB,qBAC/B,qBAEFhF,KAAKqB,QAAU,KACf8F,YACE,KAAOnH,KAAK0B,iBAAiBuD,MAAMC,KAAKlF,KAAKC,eAAiBD,KAAKqB,QAAU,MAAQrB,KAAKoH,aAAa,KACvG,I,CAKJ,eAAM1C,GACJ,GAAI1E,KAAKI,eAAiBJ,KAAKkC,cAAcJ,OAAQ,CACnD,GAAI9B,KAAKgB,cAAgBhB,KAAKU,SAAU,CACtCV,KAAKI,cAAgB,C,KAChB,CACLJ,KAAKI,cAAgBJ,KAAKI,a,MAEvB,CACLJ,KAAKI,cAAgBJ,KAAKI,cAAgB,C,CAE5CiH,cAAcrH,KAAKoC,kBACnBpC,KAAKK,QAAU,EACfL,KAAKmC,mB,CAIP,eAAMwC,GACJ,GAAI3E,KAAKI,eAAiB,EAAG,CAC3B,GAAIJ,KAAKgB,cAAgBhB,KAAKU,SAAU,CACtCV,KAAKI,cAAgBJ,KAAKkC,cAAcJ,M,KACnC,CACL9B,KAAKI,cAAgBJ,KAAKI,a,MAEvB,CACLJ,KAAKI,cAAgBJ,KAAKI,cAAgB,C,CAE5CiH,cAAcrH,KAAKoC,kBACnBpC,KAAKK,QAAU,EACfL,KAAKmC,mB,CAIP,kBAAMiF,CAAalB,GACjBlG,KAAKI,cAAgB8F,EACrBmB,cAAcrH,KAAKoC,kBACnBpC,KAAKK,QAAU,EACfL,KAAKmC,oBACLnC,KAAKS,cAAgB,S,CAIvB,mBAAMgD,GACJ4D,cAAcrH,KAAKoC,kBACnBpC,KAAKS,cAAgB,Q,CAIvB,iBAAMkD,GACJ3D,KAAKmC,oBACLnC,KAAKS,cAAgB,S,CAkGvB,MAAA6G,GAEEtH,KAAKG,eAAiB,GAEtB,MAAMoH,EAAmBvH,KAAKiB,QAAU,SAAW,qBAAuB,MAC1E,MAAMuG,EACJxH,KAAKe,iBAAmB,SACpB,SACAf,KAAKe,iBAAmB,QACtB,WACAf,KAAKe,iBAAmB,QAAU,aAC1C,OACE0G,EAAA,OAAA5C,IAAA,2CAAK6C,MAAO,CAAEC,SAAU,OACtBF,EAAA,OAAA5C,IAAA,2CACE6C,MAAO,CACLE,eAAgB,KAChBC,yBAA0B7H,KAAKiB,QAAU,UACzC,CAAC,wBAAwBjB,KAAKS,iBAAkBT,KAAKU,UAEvDoH,SAAS,IACTC,UAAYlE,GAAO7D,KAAK4E,qBAAqBf,GAClC,YAAA7D,KAAKsB,gBAEhBmG,EACE,OAAA5C,IAAA,2CAAAmD,IAAM/E,GAAOjD,KAAKgD,SAASC,GAC3ByE,MAAO,CAAEO,qBAAsB,KAAMC,6BAA8BlI,KAAKqB,SACxEmC,YAAa,IAAMxD,KAAKwD,cACxBE,WAAY,IAAM1D,KAAK0D,aACvBE,YAAcC,GAAO7D,KAAK4D,YAAYC,GACtCQ,aAAc,IAAMrE,KAAKqE,eACzBC,UAAW,IAAMtE,KAAKsE,YACtBE,YAAcX,GAAO7D,KAAKwE,YAAYX,IAEtC4D,EAAK,OAAA5C,IAAA,2CAAAmD,IAAM/E,GAAOjD,KAAKoD,iBAAiBH,GAAKyE,MAAO,CAAES,8BAA+B,OACnFV,EAAA,QAAA5C,IAAA,+CAGJ4C,EAAA,YAAA5C,IAAA,2CAAU6C,MAAO,CAAEU,uBAAwB,KAAMC,+BAAgCrI,KAAKqB,UACpFoG,EAAA,gBAAA5C,IAAA,2CAAc9B,OAAO,OAAOuF,MAAM,SAASlD,MAAM,UAElDpF,KAAKiB,QAAU,SAAWjB,KAAKqB,SAC9BoG,EAACF,EAAgB,CAAA1C,IAAA,2CACfmD,IAAM/E,GAAOjD,KAAKkD,uBAAuBD,GACzCyE,MAAO,CACLa,iBAAkB,KAClBC,2BAA4BxI,KAAKiB,QAAU,YAG7CwG,EAAA,cAAA5C,IAAA,2CACE4D,QAAQ,OACRC,SAAS,aACTC,MAAM,UACNC,WAAY,IAAM5I,KAAK2E,YACvBkE,UAAW7I,KAAKgB,cAAgBhB,KAAKI,eAAiB,EACtD0I,SAAU9I,KAAKuB,eAEjBkG,EACE,cAAA5C,IAAA,2CAAA4D,QAAQ,OACRC,SAAS,cACTC,MAAM,UACNC,WAAY,IAAM5I,KAAK0E,YACvBmE,UAAW7I,KAAKgB,cAAgBhB,KAAKI,eAAiBJ,KAAKkC,cAAcJ,OACzEgH,SAAU9I,KAAKwB,iBAKtBxB,KAAKkC,cAAcJ,OAAS,GAAK9B,KAAKc,SAAW,QAChD2G,EAAA,OAAA5C,IAAA,2CACE6C,MAAO,CACLqB,iBAAkB,KAClBC,wBAAyBhJ,KAAKc,SAAW,WAG1Cd,KAAKqB,SAAWrB,KAAKc,SAAW,SAC/B2G,EAAA,YACEwB,IAAI,KACJ9H,IAAI,IAAG,kBACUqG,EACjB9B,QAAS1F,KAAKiB,SAAW,UAAY,MAAQ,QAE7CwG,EAAc,gBAAA1E,OAAO,OAAOqC,MAAM,OAAOkD,MAAM,WAC/Cb,EAAc,gBAAA1E,OAAO,OAAOqC,MAAM,OAAOkD,MAAM,WAC/Cb,EAAc,gBAAA1E,OAAO,OAAOqC,MAAM,OAAOkD,MAAM,YAGjDtI,KAAKkC,eACHuF,EACE,YAAAwB,IAAI,KAAI,kBACSzB,EACjB9B,QAAS1F,KAAKiB,SAAW,UAAY,MAAQ,QAE7CwG,EAAA,OACEC,MAAO,CACLwB,sBAAuB,KACvBC,6BAA8BnJ,KAAKc,SAAW,WAG/Cd,KAAKkC,cAAckH,KAAI,CAAClD,EAAMmD,IAC7B5B,EAAA,OACE5C,IAAKwE,EACLrB,IAAM/E,GAAOjD,KAAKsD,iBAAiBL,GACnCyE,MAAO,CACL4B,sBAAuB,KACvBC,6BAA8BrD,EAAKC,IAAMnG,KAAKI,eAEhD0H,SAAS,IACT0B,QAAS,IAAMxJ,KAAKoH,aAAalB,EAAKC,KAErCD,EAAKC,GAAKnG,KAAKI,eAAiBJ,KAAKU,UACpC+G,EAAA,OAAKC,MAAO,CAAE+B,+BAAgC,QAE/CvD,EAAKC,IAAMnG,KAAKI,eAAiBJ,KAAKU,UACrC+G,EACE,OAAAC,MAAO,CAAEgC,6BAA8B,MACvC5G,MAAO,CACL6G,kBAAmB,GAAG3J,KAAKW,gBAAkB,IAAO,MACpDiJ,mBAAoB5J,KAAKS,uBAY/CgH,EAAA,QAAA5C,IAAA,2CAAMgF,KAAK,U", "ignoreList": []}