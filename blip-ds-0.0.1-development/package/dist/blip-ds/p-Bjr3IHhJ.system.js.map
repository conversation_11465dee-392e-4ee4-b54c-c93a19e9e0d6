{"version": 3, "file": "p-Bjr3IHhJ.system.js", "sources": ["node_modules/@stencil/core/internal/client/patch-browser.js", "@lazy-browser-entrypoint?app-data=conditional"], "sourcesContent": ["/*\n Stencil Client Patch Browser v4.35.1 | MIT Licensed | https://stenciljs.com\n */\n\n// src/client/client-patch-browser.ts\nimport { BUILD, NAMESPACE } from \"@stencil/core/internal/app-data\";\nimport { consoleDevInfo, H, promiseResolve, win } from \"@stencil/core\";\nvar patchBrowser = () => {\n  if (BUILD.isDev && !BUILD.isTesting) {\n    consoleDevInfo(\"Running in development mode.\");\n  }\n  if (BUILD.cloneNodeFix) {\n    patchCloneNodeFix(H.prototype);\n  }\n  const scriptElm = BUILD.scriptDataOpts ? win.document && Array.from(win.document.querySelectorAll(\"script\")).find(\n    (s) => new RegExp(`/${NAMESPACE}(\\\\.esm)?\\\\.js($|\\\\?|#)`).test(s.src) || s.getAttribute(\"data-stencil-namespace\") === NAMESPACE\n  ) : null;\n  const importMeta = import.meta.url;\n  const opts = BUILD.scriptDataOpts ? (scriptElm || {})[\"data-opts\"] || {} : {};\n  if (importMeta !== \"\") {\n    opts.resourcesUrl = new URL(\".\", importMeta).href;\n  }\n  return promiseResolve(opts);\n};\nvar patchCloneNodeFix = (HTMLElementPrototype) => {\n  const nativeCloneNodeFn = HTMLElementPrototype.cloneNode;\n  HTMLElementPrototype.cloneNode = function(deep) {\n    if (this.nodeName === \"TEMPLATE\") {\n      return nativeCloneNodeFn.call(this, deep);\n    }\n    const clonedNode = nativeCloneNodeFn.call(this, false);\n    const srcChildNodes = this.childNodes;\n    if (deep) {\n      for (let i = 0; i < srcChildNodes.length; i++) {\n        if (srcChildNodes[i].nodeType !== 2) {\n          clonedNode.appendChild(srcChildNodes[i].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nexport {\n  patchBrowser\n};\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { patchBrowser } from '@stencil/core/internal/client/patch-browser';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\npatchBrowser().then(async (options) => {\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;;MAAA;MACA;MACA;;MAKA,IAAI,YAAY,GAAG,MAAM;MAOzB,EAAE,MAAM,SAAS,GAA0B,GAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;MACnH,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,wBAAwB,CAAC,KAAK;MAC1H,GAAG,CAAO;MACV,EAAE,MAAM,UAAU,GAAG,eAAe;MACpC,EAAE,MAAM,IAAI,GAA0B,CAAC,SAAS,IAAI,EAAE,EAAE,WAAW,CAAC,IAAI,EAAE,CAAK;MAC/E,EAAE,IAAI,UAAU,KAAK,EAAE,EAAE;MACzB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI;MACrD;MACA,EAAE,OAAO,cAAc,CAAC,IAAI,CAAC;MAC7B,CAAC;;MCnBD,YAAY,EAAE,CAAC,IAAI,CAAC,OAAO,OAAO,KAAK;MACvC,EAAE,MAAM,aAAa,EAAE;MACvB,EAAE,OAAO,aAAa,CAAC,4BAA4B,EAAE,OAAO,CAAC;MAC7D,CAAC,CAAC;;;;;;;;", "x_google_ignoreList": [0]}