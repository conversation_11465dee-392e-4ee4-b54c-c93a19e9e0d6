import{r as e,h as l,H as t,a as s}from"./p-C3J6Z5OX.js";const c='.sc-bds-table-cell-h{display:table-cell;padding:0 8px;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:14px;vertical-align:middle}.cell.sc-bds-table-cell{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;min-height:48px;margin:8px 0;color:var(--color-content-default, rgb(40, 40, 40));font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap}.dense_cell.sc-bds-table-cell{margin:0}.cell_custom.sc-bds-table-cell{gap:8px}.cell_action.sc-bds-table-cell{-ms-flex-direction:row;flex-direction:row;gap:8px}.justify--left.sc-bds-table-cell{-ms-flex-pack:start;justify-content:flex-start}.justify--center.sc-bds-table-cell{-ms-flex-pack:center;justify-content:center}.justify--right.sc-bds-table-cell{-ms-flex-pack:end;justify-content:flex-end}.sc-bds-table-cell-h:first-child{padding-left:16px}.sc-bds-table-cell-h:last-child{padding-right:16px}';const a=class{constructor(l){e(this,l);this.isDense=false;this.type="text";this.sortable=false;this.justifyContent="left"}renderContent(){return this.type==="custom"?l("div",{class:{cell:true,cell_custom:true,dense_cell:true,[`justify--${this.justifyContent}`]:true}},l("slot",null)):this.type==="text"?l("div",{class:{cell:true,dense_cell:true,[`justify--${this.justifyContent}`]:true}},l("bds-typo",{variant:"fs-14",bold:this.sortable?"bold":"regular"},l("slot",null))):this.type==="action"?l("div",{class:{cell:true,cell_action:true,dense_cell:true,[`justify--${this.justifyContent}`]:true}},l("slot",null)):this.type==="collapse"?l("td",{colSpan:2,class:{cell:true,cell_action:true,dense_cell:true,[`justify--${this.justifyContent}`]:true}},l("slot",null)):l("slot",null)}componentWillLoad(){const e=this.element.closest("bds-table");if(e&&e.getAttribute("dense-table")==="true"){this.isDense=true}}render(){return l(t,{key:"308fc20da6c74c8808a4eca46cc3768985a73419"},this.renderContent())}get element(){return s(this)}};a.style=c;export{a as bds_table_cell};
//# sourceMappingURL=p-14647a02.entry.js.map