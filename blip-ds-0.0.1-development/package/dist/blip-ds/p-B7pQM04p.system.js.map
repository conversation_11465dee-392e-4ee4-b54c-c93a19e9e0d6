{"version": 3, "file": "p-B7pQM04p.system.js", "sources": ["src/components/modal/modal-close-button/modal-close-button.scss?tag=bds-modal-close-button&encapsulation=shadow", "src/components/modal/modal-close-button/modal-close-button.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.modal__close__button-icon {\n  opacity: 0;\n  visibility: hidden;\n  color: $color-content-default;\n  display: flex;\n  justify-content: flex-end;\n  padding-bottom: 16px;\n\n  &--active {\n    opacity: 1;\n    visibility: visible;\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-close-button',\n  styleUrl: 'modal-close-button.scss',\n  shadow: true,\n})\nexport class BdsModalCloseButton implements ComponentInterface {\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public active?: boolean = true;\n\n  render() {\n    return (\n      <div\n        class={{\n          'modal__close__button-icon': true,\n          'modal__close__button-icon--active': this.active,\n        }}\n      >\n        <bds-icon size=\"medium\" name=\"close\"></bds-icon>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;MAAA,MAAM,mBAAmB,GAAG,6QAA6Q;;YCO5R,mBAAmB,qCAAA,MAAA;MALhC,IAAA,WAAA,CAAA,OAAA,EAAA;;MAME;;MAEG;MAKI,QAAA,IAAM,CAAA,MAAA,GAAa,IAAI;MAc/B;UAZC,MAAM,GAAA;cACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;MACL,gBAAA,2BAA2B,EAAE,IAAI;sBACjC,mCAAmC,EAAE,IAAI,CAAC,MAAM;mBACjD,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAA,CAAY,CAC5C;;;;;;;;;;;"}