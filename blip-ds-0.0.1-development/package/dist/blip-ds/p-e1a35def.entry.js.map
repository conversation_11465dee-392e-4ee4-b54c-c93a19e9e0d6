{"version": 3, "names": ["warningCss", "Warning", "render", "h", "Host", "key", "class", "theme", "size", "name", "variant", "tag"], "sources": ["src/components/warning/warning.scss?tag=bds-warning&encapsulation=shadow", "src/components/warning/warning.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  align-items: center;\n}\n\n.warning__body {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  background-color: $color-neutral-light-breeze;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.warning__icon {\n  color: $color-extend-browns-cheetos;\n}\n\n.warning__message {\n  color: $color-neutral-dark-rooftop;\n  margin-left: 8px;\n}\n", "import { Component, ComponentInterface, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-warning',\n  styleUrl: 'warning.scss',\n  shadow: true,\n})\nexport class Warning implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <div class=\"warning__body\">\n          <bds-icon class=\"warning__icon\" theme=\"solid\" size=\"small\" name=\"warning\"></bds-icon>\n          <bds-typo variant=\"fs-14\" tag=\"span\" class=\"warning__message\">\n            <slot />\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAa,2V,MCONC,EAAO,M,yBAClB,MAAAC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,iBACTH,EAAA,YAAAE,IAAA,2CAAUC,MAAM,gBAAgBC,MAAM,QAAQC,KAAK,QAAQC,KAAK,YAChEN,EAAU,YAAAE,IAAA,2CAAAK,QAAQ,QAAQC,IAAI,OAAOL,MAAM,oBACzCH,EAAA,QAAAE,IAAA,+C", "ignoreList": []}