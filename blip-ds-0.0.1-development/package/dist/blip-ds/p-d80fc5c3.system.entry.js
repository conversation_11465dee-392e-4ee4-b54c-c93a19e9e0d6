var __awaiter=this&&this.__awaiter||function(t,i,e,n){function r(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,o){function s(t){try{c(n.next(t))}catch(t){o(t)}}function a(t){try{c(n["throw"](t))}catch(t){o(t)}}function c(t){t.done?e(t.value):r(t.value).then(s,a)}c((n=n.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,r,o,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(t){return function(i){return c([t,i])}}function c(a){if(n)throw new TypeError("Generator is already executing.");while(s&&(s=0,a[0]&&(e=0)),e)try{if(n=1,r&&(o=a[0]&2?r["return"]:a[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;if(r=0,o)a=[a[0]&2,o.value];switch(a[0]){case 0:case 1:o=a;break;case 4:e.label++;return{value:a[1],done:false};case 5:e.label++;r=a[1];a=[0];continue;case 7:a=e.ops.pop();e.trys.pop();continue;default:if(!(o=e.trys,o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){e.label=a[1];break}if(a[0]===6&&e.label<o[1]){e.label=o[1];o=a;break}if(o&&e.label<o[2]){e.label=o[2];e.ops.push(a);break}if(o[2])e.ops.pop();e.trys.pop();continue}a=i.call(t,e)}catch(t){a=[6,t];r=0}finally{n=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js","./p-DLraUrU1.system.js"],(function(t){"use strict";var i,e,n,r,o,s;return{setters:[function(t){i=t.r;e=t.c;n=t.h;r=t.H},function(t){o=t.e;s=t.n}],execute:function(){var a=':host{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;width:100%;max-width:100%;max-height:100%}:host input,:host textarea{-webkit-box-shadow:inherit;box-shadow:inherit}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-moz-placeholder,:host textarea::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input:-ms-input-placeholder,:host textarea:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-ms-input-placeholder,:host textarea::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::placeholder,:host textarea::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}:host input::-webkit-input-placeholder,:host textarea::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 8px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;gap:8px}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 8px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;padding:2px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:22px;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__text[type=date]::-webkit-calendar-picker-indicator{opacity:0;pointer-events:none}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;gap:4px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-disable, rgb(89, 89, 89));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;margin-top:0px}.input__message--danger .input__message__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:22px;width:100%;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text__chips{width:auto;min-width:216px;max-width:216px}';var c=t("bds_input",function(){function t(t){var n=this;i(this,t);this.bdsChange=e(this,"bdsChange");this.bdsInput=e(this,"bdsInput");this.bdsOnBlur=e(this,"bdsOnBlur");this.bdsFocus=e(this,"bdsFocus");this.bdsSubmit=e(this,"bdsSubmit");this.bdsPatternValidation=e(this,"bdsPatternValidation");this.bdsKeyDownBackspace=e(this,"bdsKeyDownBackspace");this.isPressed=false;this.isPassword=false;this.validationMesage="";this.validationDanger=false;this.inputName="";this.type="text";this.label="";this.placeholder="";this.autoCapitalize="off";this.autoComplete="off";this.readonly=false;this.helperMessage="";this.errorMessage="";this.successMessage="";this.icon="";this.disabled=false;this.danger=false;this.success=false;this.value="";this.counterLength=false;this.counterLengthRule=null;this.isSubmit=false;this.isTextarea=false;this.rows=1;this.cols=0;this.dataTest=null;this.encode=false;this.keyPressWrapper=function(t){switch(t.key){case"Enter":n.bdsSubmit.emit({event:t,value:n.value});if(n.isSubmit){n.clearTextInput();t.preventDefault()}break;case"Backspace":case"Delete":n.bdsKeyDownBackspace.emit({event:t,value:n.value});break}};this.onInput=function(t){n.onBdsInputValidations();var i=t.target;if(i){n.value=i.value||""}n.bdsInput.emit(t)};this.onBlur=function(){n.onBlurValidations();n.isPressed=false;n.bdsOnBlur.emit()};this.onFocus=function(){n.isPressed=true;n.bdsFocus.emit()};this.onClickWrapper=function(){n.onFocus();if(n.nativeInput){n.nativeInput.focus()}};this.clearTextInput=function(t){if(!n.readonly&&!n.disabled&&t){t.preventDefault();t.stopPropagation()}n.value="";if(n.nativeInput){n.nativeInput.value=""}}}t.prototype.setFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.onClickWrapper();return[2]}))}))};t.prototype.removeFocus=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.onBlur();return[2]}))}))};t.prototype.getInputElement=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.nativeInput]}))}))};t.prototype.isValid=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.nativeInput.validity.valid]}))}))};t.prototype.clear=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.value="";return[2]}))}))};t.prototype.encodeValue=function(t){var i=/</g,e=/>/g,n=/'/g,r=/"/g,o=/&/g,s=/\//g;if(!this.encode)return t;return t&&t.toString().replace(i,"&lt;").replace(e,"&gt;").replace(n,"&#39;").replace(r,"&#34;").replace(o,"&amp;").replace(s,"&#47;")};t.prototype.valueChanged=function(t){var i=this.encode?this.encodeValue(t||""):t||"";this.bdsChange.emit({value:i})};t.prototype.renderIcon=function(){return this.icon&&n("div",{class:{input__icon:true,"input__icon--large":!!this.label}},n("bds-icon",{class:"input__icon--color",size:this.label?"medium":"small",name:this.icon,color:"inherit"}))};t.prototype.renderLabel=function(){return this.label&&n("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},n("bds-typo",{variant:"fs-12",bold:"bold"},this.label))};t.prototype.renderMessage=function(){var t=this.danger?"error":this.success?"checkball":"info";var i=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!i&&this.validationDanger)i=this.validationMesage;var e=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(i){return n("div",{class:e,part:"input__message"},n("div",{class:"input__message__icon"},n("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),n("bds-typo",{class:"input__message__text",variant:"fs-12"},i))}return undefined};t.prototype.onBlurValidations=function(){this.required&&this.requiredValidation();this.pattern&&this.patternValidation();(this.minlength||this.maxlength)&&this.lengthValidation();(this.min||this.max)&&this.minMaxValidation();this.checkValidity()};t.prototype.onBdsInputValidations=function(){this.type==="email"&&this.emailValidation();this.type==="phonenumber"&&this.numberValidation();this.checkValidity()};t.prototype.patternValidation=function(){var t=new RegExp(this.pattern);this.bdsPatternValidation.emit(t.test(this.nativeInput.value))};t.prototype.requiredValidation=function(){if(this.nativeInput.validity.valueMissing){this.validationMesage=this.requiredErrorMessage;this.validationDanger=true}};t.prototype.lengthValidation=function(){if(this.nativeInput.validity.tooShort){this.validationMesage=this.minlengthErrorMessage;this.validationDanger=true;return}if(this.nativeInput.validity.tooLong){this.validationDanger=true;return}};t.prototype.minMaxValidation=function(){if(this.nativeInput.validity.rangeUnderflow){this.validationMesage=this.minErrorMessage;this.validationDanger=true;return}if(this.nativeInput.validity.rangeOverflow){this.validationMesage=this.maxErrorMessage;this.validationDanger=true;return}};t.prototype.emailValidation=function(){if(o(this.nativeInput.value)){this.validationMesage=this.emailErrorMessage;this.validationDanger=true}};t.prototype.numberValidation=function(){if(s(this.nativeInput.value)){this.validationMesage=this.numberErrorMessage;this.validationDanger=true}};t.prototype.checkValidity=function(){if(this.nativeInput.validity.valid){this.validationDanger=false}};t.prototype.componentDidUpdate=function(){if(this.nativeInput&&this.value!=this.nativeInput.value){this.nativeInput.value=this.value}};t.prototype.render=function(){var t=this;var i=this.isPressed&&!this.disabled;var e=this.isTextarea?"textarea":"input";return n(r,{key:"b4d1fee2987836035820e6d8403d9a9821042305","aria-disabled":this.disabled?"true":null},n("div",{key:"776d31af06d3628c30666c87038a5c22c8a4f789",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":i},onClick:this.onClickWrapper,onKeyDown:this.keyPressWrapper,part:"input-container"},this.renderIcon(),n("slot",{key:"4b15c0a203aff95741236294bebf6bb83d51359e",name:"input-left"}),n("div",{key:"1b00b4bf38799a0297859f49c738de23611ac159",class:"input__container"},this.renderLabel(),n("div",{key:"b9cb4b621f663815e034932e11f5ec620c6e014a",class:{input__container__wrapper:!this.chips,input__container__wrapper__chips:this.chips}},n("slot",{key:"1585fcf339ca992766ae705e4c2b5455a6a6d4b0",name:"inside-input-left"}),n(e,{key:"a66e09fbbeb90287f84fc62d1dbcf0d6e40cbc03",class:{input__container__text:true,input__container__text__chips:this.chips},ref:function(i){return t.nativeInput=i},rows:this.rows,cols:this.cols,autocapitalize:this.autoCapitalize,autocomplete:this.autoComplete,disabled:this.disabled,min:this.min,max:this.max,minLength:this.minlength,maxLength:this.maxlength,name:this.inputName,onBlur:this.onBlur,onFocus:this.onFocus,onInput:this.onInput,placeholder:this.placeholder,readOnly:this.readonly,type:this.type,value:this.encodeValue(this.value),pattern:this.pattern,required:this.required,part:"input","data-test":this.dataTest}))),this.counterLength&&n("bds-counter-text",Object.assign({key:"2ae6766b6751a6b6b10d73f4bee841dabd894a0a",length:this.value.length,max:this.maxlength,active:i},this.counterLengthRule)),this.success&&n("bds-icon",{key:"3026ab9be7fe408e2ba51637514ff332d939600d",class:"icon-success",name:"check",theme:"outline",size:"small"}),n("slot",{key:"4eb783d9f97093eedbb6b4fd953fca324161b88a",name:"input-right"})),this.renderMessage())};Object.defineProperty(t,"watchers",{get:function(){return{value:["valueChanged"]}},enumerable:false,configurable:true});return t}());c.style=a}}}));
//# sourceMappingURL=p-d80fc5c3.system.entry.js.map