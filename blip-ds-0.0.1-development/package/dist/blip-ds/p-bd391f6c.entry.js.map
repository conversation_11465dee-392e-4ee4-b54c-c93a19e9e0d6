{"version": 3, "names": ["tableBodyCss", "TableBody", "constructor", "hostRef", "this", "multipleRows", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "collapse", "render", "h", "Host", "key", "class", "host", "multiple"], "sources": ["src/components/table/table-body/table-body.scss?tag=bds-table-body&encapsulation=scoped", "src/components/table/table-body/table-body.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n    display: table-row-group;\n    height: 64px;\n  }\n\n  :host(.multiple) {\n    border-bottom: 1px solid $color-border-2;\n  }\n  \n  :host:last-child {\n    border-bottom: none;\n  }", "import { Component, h, Host, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-body',\n  styleUrl: 'table-body.scss',\n  scoped: true,\n})\nexport class TableBody {\n  @Element() private element: HTMLElement;\n  @State() multipleRows = false;\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('collapse') === 'true' || bdsTable.collapse === true)) {\n      this.multipleRows = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ host: true, multiple: this.multipleRows }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "yDAAA,MAAMA,EAAe,gN,MCORC,EAAS,MALtB,WAAAC,CAAAC,G,UAOWC,KAAYC,aAAG,KAgBzB,CAdC,iBAAAC,GACE,MAAMC,EAAWH,KAAKI,QAAQC,QAAQ,aACtC,GAAIF,IAAaA,EAASG,aAAa,cAAgB,QAAUH,EAASI,WAAa,MAAO,CAC5FP,KAAKC,aAAe,I,EAIxB,MAAAO,GACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAO,CAAEC,KAAM,KAAMC,SAAUd,KAAKC,eACxCQ,EAAQ,QAAAE,IAAA,6C", "ignoreList": []}