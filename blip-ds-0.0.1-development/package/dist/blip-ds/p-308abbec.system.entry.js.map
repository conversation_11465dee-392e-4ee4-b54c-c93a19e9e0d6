{"version": 3, "names": ["stepCss", "BdsStep", "exports", "class_1", "hostRef", "this", "last", "completed", "active", "disabled", "index", "pointer", "dataTest", "prototype", "render", "h", "key", "class", "step__content", "step__content__ellipse", "name", "variant", "step__content__text", "bold"], "sources": ["src/components/stepper/step/step.scss?tag=bds-step&encapsulation=shadow", "src/components/stepper/step/step.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n@use '../../../globals/theme/color-legacy' as *;\n\n:host {\n  padding: 8px;\n\n  .step {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    &__content {\n      display: flex;\n      align-items: center;\n\n      &--completed {\n        color: $color-content-disable;\n      }\n\n      &--active {\n        color: $color-primary;\n        font-weight: bold;\n      }\n\n      &--pointer {\n        cursor: pointer;\n      }\n\n      &--disabled {\n        cursor: no-drop;\n      }\n\n      &__ellipse {\n        display: inline-flex;\n        justify-content: center;\n        border-radius: 50%;\n        background: $color-content-default;\n        margin-right: 4px;\n        min-width: 24px;\n        min-height: 24px;\n\n        & bds-typo,\n        bds-icon {\n          color: $color-surface-0;\n        }\n\n        &--completed {\n          background: $color-content-ghost;\n          & bds-typo,\n          bds-icon {\n            color: $color-surface-0;\n          }\n        }\n\n        &--active {\n          background: $color-surface-primary;\n          & bds-typo,\n          bds-icon {\n            color: $color-content-bright;\n          }\n        }\n\n        &--disabled {\n          background: $color-content-ghost;\n          & bds-typo,\n          bds-icon {\n            color: $color-surface-0;\n          }\n        }\n      }\n\n      &__text {\n        &--completed {\n          color: $color-content-ghost;\n        }\n\n        &--active {\n          color: $color-content-default;\n        }\n\n        &--disabled {\n          color: $color-content-ghost;\n        }\n      }\n    }\n  }\n\n  @media (max-width: $sm-screen) {\n    display: flex;\n    flex: inherit;\n  }\n}\n", "import { Component, ComponentInterface, h, Prop, Element } from '@stencil/core';\n@Component({\n  tag: 'bds-step',\n  styleUrl: 'step.scss',\n  shadow: true,\n})\nexport class BdsStep implements ComponentInterface {\n  @Element() el: HTMLBdsToastElement;\n  /**\n   * Used to define the last step component on the list\n   */\n  @Prop() last?: boolean = false;\n\n  /**\n   * Used to complete the step\n   */\n  @Prop() completed?: boolean = false;\n\n  /**\n   * Used to set the step as active\n   */\n  @Prop() active?: boolean = false;\n\n  /**\n   * Used to set the step as disabled\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Used to set the index of the steps\n   */\n  @Prop() index?: number = 0;\n\n  /**\n   * Used to set cursor pointer on the step (useful to allow clicks on the steps)\n   */\n  @Prop() pointer?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  render() {\n    return (\n      <div class=\"step\">\n        <div\n          class={{\n            step__content: true,\n            'step__content--active': this.active,\n            'step__content--completed': this.completed,\n            'step__content--disabled': this.disabled,\n            'step__content--pointer': this.pointer,\n            'step--last': this.last,\n          }}\n          data-test={this.dataTest}\n        >\n          <div\n            class={{\n              step__content__ellipse: true,\n              'step__content__ellipse--active': this.active,\n              'step__content__ellipse--completed': this.completed,\n              'step__content__ellipse--disabled': this.disabled,\n            }}\n          >\n            {this.completed && <bds-icon name=\"true\"></bds-icon>}\n            {!this.completed && <bds-typo>{this.index + 1}</bds-typo>}\n          </div>\n          <bds-typo\n            variant=\"fs-16\"\n            class={{\n              step__content__text: true,\n              'step__content__text--completed': this.completed && !this.active,\n              'step__content__text--active': this.active,\n              'step__content__text--disabled': this.disabled,\n            }}\n            bold={this.active ? 'bold' : 'regular'}\n          >\n            <slot />\n          </bds-typo>\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAU,i6D,ICMHC,EAAOC,EAAA,sBALpB,SAAAC,EAAAC,G,UAUUC,KAAIC,KAAa,MAKjBD,KAASE,UAAa,MAKtBF,KAAMG,OAAa,MAKnBH,KAAQI,SAAa,MAKrBJ,KAAKK,MAAY,EAKjBL,KAAOM,QAAa,MAIpBN,KAAQO,SAAY,IA0C7B,CAzCCT,EAAAU,UAAAC,OAAA,WACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,QACTF,EAAA,OAAAC,IAAA,2CACEC,MAAO,CACLC,cAAe,KACf,wBAAyBb,KAAKG,OAC9B,2BAA4BH,KAAKE,UACjC,0BAA2BF,KAAKI,SAChC,yBAA0BJ,KAAKM,QAC/B,aAAcN,KAAKC,MAEV,YAAAD,KAAKO,UAEhBG,EAAA,OAAAC,IAAA,2CACEC,MAAO,CACLE,uBAAwB,KACxB,iCAAkCd,KAAKG,OACvC,oCAAqCH,KAAKE,UAC1C,mCAAoCF,KAAKI,WAG1CJ,KAAKE,WAAaQ,EAAA,YAAAC,IAAA,2CAAUI,KAAK,UAChCf,KAAKE,WAAaQ,EAAA,YAAAC,IAAA,4CAAWX,KAAKK,MAAQ,IAE9CK,EAAA,YAAAC,IAAA,2CACEK,QAAQ,QACRJ,MAAO,CACLK,oBAAqB,KACrB,iCAAkCjB,KAAKE,YAAcF,KAAKG,OAC1D,8BAA+BH,KAAKG,OACpC,gCAAiCH,KAAKI,UAExCc,KAAMlB,KAAKG,OAAS,OAAS,WAE7BO,EAAA,QAAAC,IAAA,+C,uHAtEQ,I", "ignoreList": []}