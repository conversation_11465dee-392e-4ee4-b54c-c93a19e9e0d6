{"version": 3, "names": ["modalActionCss", "BdsModalAction", "exports", "class_1", "prototype", "render", "h", "key", "class"], "sources": ["src/components/modal/modal-action/modal-action.scss?tag=bds-modal-action&encapsulation=shadow", "src/components/modal/modal-action/modal-action.tsx"], "sourcesContent": [".modal__action {\n  display: flex;\n  padding-top: 16px;\n  bottom: 32px;\n  right: 32px;\n\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-action',\n  styleUrl: 'modal-action.scss',\n  shadow: true,\n})\nexport class BdsModalAction implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"modal__action\">\n        <slot />\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAiB,2F,ICOVC,EAAcC,EAAA,8B,wBACzBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,iBACTF,EAAQ,QAAAC,IAAA,6C,WAJW,I", "ignoreList": []}