{"version": 3, "file": "bds-breadcrumb.entry.esm.js", "sources": ["src/components/breadcrumb/breadcrumb.scss?tag=bds-breadcrumb&encapsulation=shadow", "src/components/breadcrumb/breadcrumb.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n\n  * {\n    color: $color-content-default;\n  }\n}\n\n.button--icon {\n  transform: rotate(180deg);\n  color: $color-content-ghost;\n}\n\n.breadcrumb__button {\n  &--0 {\n    padding-left: 0;\n\n    .button--icon {\n      display: none;\n    }\n  }\n\n  &--1 {\n    padding-left: 8px;\n  }\n\n  &--2 {\n    padding-left: 16px;\n  }\n\n  &--3 {\n    padding-left: 24px;\n  }\n\n  &--4 {\n    padding-left: 32px;\n  }\n}\n\n.breadcrumb__link--text {\n  color: $color-content-disable;\n}\n\n.breadcrumb__link {\n  text-decoration: none;\n}", "import { Component, h, Prop, State, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-breadcrumb',\n  styleUrl: 'breadcrumb.scss',\n  shadow: true,\n})\nexport class Breadcrumb {\n  @Prop() items: string | Array<{ label: string; href?: string }> = [];\n\n  @State() parsedItems: Array<{ label: string; href?: string }> = [];\n\n  @State() isDropdownOpen: boolean = false;\n\n  @Watch('items')\n  parseItems(newValue: string | Array<{ label: string; href?: string }>) {\n    if (typeof newValue === 'string') {\n      try {\n        this.parsedItems = JSON.parse(newValue);\n      } catch (error) {\n        this.parsedItems = [];\n      }\n    } else {\n      this.parsedItems = newValue;\n    }\n  }\n\n  componentWillLoad() {\n    this.parseItems(this.items);\n  }\n\n  toggleDropdown() {\n    this.isDropdownOpen = !this.isDropdownOpen;\n  }\n\n  render() {\n    if (!this.parsedItems || this.parsedItems.length === 0) {\n      return <p>Sem itens para exibir no Breadcrumb.</p>;\n    }\n\n    const visibleItems =\n      this.parsedItems.length <= 3\n        ? this.parsedItems\n        : [\n            this.parsedItems[0],\n            { label: '...', href: null },\n            this.parsedItems[this.parsedItems.length - 1],\n          ];\n\n    return (\n      <nav aria-label=\"breadcrumb\">\n        <bds-grid direction=\"row\" align-items=\"center\">\n          {visibleItems.map((item, index) => (\n            <bds-grid\n              class={{\n                breadcrumb__item: true,\n                'breadcrumb__item--active': index === visibleItems.length - 1,\n              }}\n              aria-current={index === visibleItems.length - 1 ? 'page' : null}\n            >\n              {item.label === '...' ? (\n                <bds-dropdown active-mode=\"click\" position=\"auto\">\n                  <bds-grid slot=\"dropdown-content\">\n                    <bds-grid direction=\"column\" padding=\"1\" gap=\"half\">\n                      {this.parsedItems.slice(1, -1).map((subItem, idx) => (\n                        <bds-grid class={`breadcrumb__button--${idx}`}>\n                          {subItem.href ? (\n                            <a\n                              href={subItem.href}\n                              class={`breadcrumb__link breadcrumb__button--${idx}`}\n                            >\n                              <bds-grid align-items=\"center\" gap=\"half\">\n                                <bds-icon\n                                  name=\"reply\"\n                                  theme=\"outline\"\n                                  class=\"button--icon\"\n                                  size=\"x-small\"\n                                ></bds-icon>\n                                <bds-button\n                                  variant=\"text\"\n                                  color=\"content\"\n                                  size=\"short\"\n                                >\n                                  {subItem.label}\n                                </bds-button>\n                              </bds-grid>\n                            </a>\n                          ) : (\n                            <span>{subItem.label}</span>\n                          )}\n                        </bds-grid>\n                      ))}\n                    </bds-grid>\n                  </bds-grid>\n                  <bds-grid slot=\"dropdown-activator\" align-items=\"center\">\n                    <bds-button\n                      variant=\"text\"\n                      color=\"content\"\n                      size=\"short\"\n                      onClick={() => this.toggleDropdown()}\n                      icon-left=\"more-options-horizontal\"\n                    ></bds-button>\n                    <bds-icon name=\"arrow-right\" size=\"x-small\"></bds-icon>\n                  </bds-grid>\n                </bds-dropdown>\n              ) : item.href ? (\n                <bds-grid direction=\"row\">\n                  <bds-typo\n                    variant=\"fs-12\"\n                    margin={false}\n                    class=\"breadcrumb__link--text\"\n                  >\n                    <a href={item.href} class=\"breadcrumb__link\">\n                      {item.label}\n                    </a>\n                  </bds-typo>\n                  <bds-icon name=\"arrow-right\" size=\"x-small\"></bds-icon>\n                </bds-grid>\n              ) : (\n                <bds-grid direction=\"row\">\n                  <bds-typo variant=\"fs-12\" bold=\"semi-bold\" margin={false}>\n                    {item.label}\n                  </bds-typo>\n                </bds-grid>\n              )}\n            </bds-grid>\n          ))}\n        </bds-grid>\n      </nav>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,aAAa,GAAG,6jBAA6jB;;MCOtkB,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAMU,QAAA,IAAK,CAAA,KAAA,GAAqD,EAAE;AAE3D,QAAA,IAAW,CAAA,WAAA,GAA4C,EAAE;AAEzD,QAAA,IAAc,CAAA,cAAA,GAAY,KAAK;AAuHzC;AApHC,IAAA,UAAU,CAAC,QAA0D,EAAA;AACnE,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAChC,YAAA,IAAI;gBACF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;;YACvC,OAAO,KAAK,EAAE;AACd,gBAAA,IAAI,CAAC,WAAW,GAAG,EAAE;;;aAElB;AACL,YAAA,IAAI,CAAC,WAAW,GAAG,QAAQ;;;IAI/B,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;;IAG7B,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,cAAc;;IAG5C,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YACtD,OAAO,oDAA2C;;QAGpD,MAAM,YAAY,GAChB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI;cACvB,IAAI,CAAC;AACP,cAAE;AACE,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AACnB,gBAAA,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;aAC9C;AAEP,QAAA,QACE,yBAAgB,YAAY,EAAA,EAC1B,CAAU,CAAA,UAAA,EAAA,EAAA,SAAS,EAAC,KAAK,EAAA,aAAA,EAAa,QAAQ,EAC3C,EAAA,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,MAC5B,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,gBAAgB,EAAE,IAAI;AACtB,gBAAA,0BAA0B,EAAE,KAAK,KAAK,YAAY,CAAC,MAAM,GAAG,CAAC;aAC9D,EAAA,cAAA,EACa,KAAK,KAAK,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,EAE9D,EAAA,IAAI,CAAC,KAAK,KAAK,KAAK,IACnB,CAA0B,CAAA,cAAA,EAAA,EAAA,aAAA,EAAA,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAA,EAC/C,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAC,kBAAkB,EAAA,EAC/B,CAAU,CAAA,UAAA,EAAA,EAAA,SAAS,EAAC,QAAQ,EAAC,OAAO,EAAC,GAAG,EAAC,GAAG,EAAC,MAAM,EAChD,EAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,MAC9C,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAE,CAAuB,oBAAA,EAAA,GAAG,EAAE,EAAA,EAC1C,OAAO,CAAC,IAAI,IACX,CAAA,CAAA,GAAA,EAAA,EACE,IAAI,EAAE,OAAO,CAAC,IAAI,EAClB,KAAK,EAAE,CAAwC,qCAAA,EAAA,GAAG,EAAE,EAAA,EAEpD,CAAA,CAAA,UAAA,EAAA,EAAA,aAAA,EAAsB,QAAQ,EAAC,GAAG,EAAC,MAAM,EAAA,EACvC,CAAA,CAAA,UAAA,EAAA,EACE,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,SAAS,EACf,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,SAAS,EACJ,CAAA,EACZ,CAAA,CAAA,YAAA,EAAA,EACE,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EAEX,EAAA,OAAO,CAAC,KAAK,CACH,CACJ,CACT,KAEJ,CAAO,CAAA,MAAA,EAAA,IAAA,EAAA,OAAO,CAAC,KAAK,CAAQ,CAC7B,CACQ,CACZ,CAAC,CACO,CACF,EACX,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,oBAAoB,EAAA,aAAA,EAAa,QAAQ,EAAA,EACtD,CACE,CAAA,YAAA,EAAA,EAAA,OAAO,EAAC,MAAM,EACd,KAAK,EAAC,SAAS,EACf,IAAI,EAAC,OAAO,EACZ,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE,EAC1B,WAAA,EAAA,yBAAyB,EACvB,CAAA,EACd,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAC,aAAa,EAAC,IAAI,EAAC,SAAS,EAAA,CAAY,CAC9C,CACE,IACb,IAAI,CAAC,IAAI,IACX,CAAA,CAAA,UAAA,EAAA,EAAU,SAAS,EAAC,KAAK,EAAA,EACvB,CACE,CAAA,UAAA,EAAA,EAAA,OAAO,EAAC,OAAO,EACf,MAAM,EAAE,KAAK,EACb,KAAK,EAAC,wBAAwB,EAAA,EAE9B,CAAA,CAAA,GAAA,EAAA,EAAG,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,kBAAkB,EACzC,EAAA,IAAI,CAAC,KAAK,CACT,CACK,EACX,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,aAAa,EAAC,IAAI,EAAC,SAAS,EAAA,CAAY,CAC9C,KAEX,CAAU,CAAA,UAAA,EAAA,EAAA,SAAS,EAAC,KAAK,EAAA,EACvB,CAAU,CAAA,UAAA,EAAA,EAAA,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,EAAC,MAAM,EAAE,KAAK,EAAA,EACrD,IAAI,CAAC,KAAK,CACF,CACF,CACZ,CACQ,CACZ,CAAC,CACO,CACP;;;;;;;;;;"}