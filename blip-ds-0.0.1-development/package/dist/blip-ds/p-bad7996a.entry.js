import{r as o,c as i,h as r,a as t}from"./p-C3J6Z5OX.js";const e='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';const s=class{constructor(r){o(this,r);this.bdsToggle=i(this,"bdsToggle");this.bdsAccordionOpen=i(this,"bdsAccordionOpen");this.bdsAccordionClose=i(this,"bdsAccordionClose");this.accGroup=null;this.accheaders=null;this.accBodies=null;this.isOpen=false;this.numberElement=null;this.condition=false;this.startOpen=false;this.divisor=true}async toggle(){this.isOpen=!this.isOpen;this.bdsToggle.emit({value:this.isOpen})}async open(){var o,i;(o=this.accheaders)===null||o===void 0?void 0:o.open();(i=this.accBodies)===null||i===void 0?void 0:i.open();this.isOpen=true}async close(){var o,i;(o=this.accheaders)===null||o===void 0?void 0:o.close();(i=this.accBodies)===null||i===void 0?void 0:i.close();this.isOpen=false}async notStart(){this.startOpen=false}async reciveNumber(o){this.numberElement=o}isOpenChanged(o){var i,r,t,e,s;if(o){if(this.accGroup.collapse=="single"&&this.condition===false){(i=this.accGroup)===null||i===void 0?void 0:i.closeAll(this.numberElement)}(r=this.accheaders)===null||r===void 0?void 0:r.open();(t=this.accBodies)===null||t===void 0?void 0:t.open();this.bdsAccordionOpen.emit()}else{(e=this.accheaders)===null||e===void 0?void 0:e.close();(s=this.accBodies)===null||s===void 0?void 0:s.close();this.bdsAccordionClose.emit()}this.condition=false}divisorChanged(o){const i=this.element.querySelector("bds-accordion-body");if(i){i.divisor(o)}}componentWillLoad(){this.accGroup=this.element.parentElement.tagName=="BDS-ACCORDION-GROUP"&&this.element.parentElement;this.accheaders=this.element.querySelector("bds-accordion-header");this.accBodies=this.element.querySelector("bds-accordion-body");const o=this.element.querySelector("bds-accordion-body");if(o){o.divisor(this.divisor)}if(this.startOpen===true){this.condition=true;this.isOpen=true}}render(){return r("div",{key:"5026d0a595341208663ef3020c3930364ee50195",class:"accordion_group"},r("slot",{key:"b2f75fc28ba9752f1bcb935daaa9df818063e31d"}))}get element(){return t(this)}static get watchers(){return{isOpen:["isOpenChanged"],divisor:["divisorChanged"]}}};s.style=e;export{s as bds_accordion};
//# sourceMappingURL=p-bad7996a.entry.js.map