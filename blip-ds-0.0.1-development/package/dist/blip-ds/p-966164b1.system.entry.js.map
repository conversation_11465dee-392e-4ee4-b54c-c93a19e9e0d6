{"version": 3, "names": ["ptTerms", "bold", "italic", "strike", "underline", "link", "code", "align_left", "align_center", "align_right", "unordered_list", "ordered_list", "quote", "h1", "h2", "h3", "h4", "h5", "h6", "clear_formatting", "expand", "esTerms", "enTerms", "termTranslate", "lang", "string", "translate", "map", "term", "richTextCss", "RichText", "exports", "class_1", "hostRef", "_this", "this", "buttonsListElement", "buttonsEditElements", "editor", "dropDownLink", "buttomBoldActive", "buttomItalicActive", "buttomStrikeActive", "buttomUnderlineActive", "buttomCodeActive", "buttomLinkActive", "buttomLinkValidDisabled", "buttomAlignLeftActive", "buttomAlignCenterActive", "buttomAlignRightActive", "buttomUnorderedListActive", "buttomOrderedListActive", "buttomQuoteActive", "buttomH1Active", "buttomH2Active", "buttomH3Active", "buttomH4Active", "buttomH5Active", "buttomH6Active", "buttomAccordionActive", "headerHeight", "hasSelectionRange", "selectedLinesList", "treeElementsEditor", "styleSectorActive", "styleOnHover", "whenSelectionLink", "linkButtonInput", "insideComponent", "language", "weightButton", "italicButton", "strikeThroughButton", "underlineButton", "linkButton", "codeButton", "alignmentButtons", "listButtons", "quoteButton", "headingButtons", "unstyledButton", "height", "maxHeight", "positionBar", "dataTest", "refButtonsListElement", "el", "refeditorElement", "refDropDownLinkElement", "refInputSetLink", "inputSetLink", "clearToolbar", "setheader<PERSON>eight", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "removeAllRanges", "addRange", "onBlur", "classList", "remove", "bdsBlur", "emit", "onFocus", "add", "bdsFocus", "onKeydown", "event", "key", "startNode", "startContainer", "blockElement", "nodeType", "Node", "TEXT_NODE", "parentElement", "contains", "tagName", "innerText", "length", "preventDefault", "textContent", "innerHTML", "setCursorToEnd", "ctrl<PERSON>ey", "metaKey", "stopPropagation", "prototype", "componentDidLoad", "trim", "getElementsByTagName", "<PERSON><PERSON><PERSON><PERSON>", "style", "buttomsHeaderChanged", "setTimeout", "buttomAccordionActiveChanged", "updateToolbarState", "commonAncestor", "commonAncestorContainer", "getParentsUntil", "value", "allbuttonsEditElementsWidth", "buttonsListWidth", "offsetWidth", "buttonAccordion", "querySelector", "diferrence", "numberOfColumns", "Math", "ceil", "allbuttonsEditElements", "Array", "from", "slice", "floor", "for<PERSON>ach", "element", "concat", "treeElementsEditorChanged", "tagList", "toLowerCase", "tagVerifyName", "tag", "includes", "getLine", "find", "textAlign", "onInput", "ev", "bdsRichTextInput", "bdsRichTextChange", "currentNode", "ELEMENT_NODE", "divElement", "pElement", "document", "createElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "querySelectorAll", "div", "p", "replaceWith", "onFocusEditorBar", "<PERSON><PERSON><PERSON>", "target", "NextButton", "nextElement<PERSON><PERSON>ling", "ElementToFocus", "shadowRoot", "focus", "createRange", "sel", "selectNodeContents", "collapse", "wrapSelection", "detail", "KeyboardEvent", "anchorNode", "isTagApplied", "content", "collapsedCursor", "appliedTag", "parent", "isFullSelection", "toString", "isAtEndOfTag", "endOffset", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "createDocumentFragment", "placeholder", "createTextNode", "append<PERSON><PERSON><PERSON>", "newRange_1", "setStartAfter", "setEndAfter", "insertNode", "collapsed", "extractContents", "wrapper", "setAttribute", "children", "newRange", "setStart", "setEnd", "setStartBefore", "<PERSON><PERSON><PERSON><PERSON>", "wrapSelectionLine", "enableLinesReturn", "endNode", "endContainer", "selectedLines", "Set", "nextS<PERSON>ling", "_a", "endElement", "allAreSameTag", "__spread<PERSON><PERSON>y", "every", "line", "returnSelected", "newElement", "lastNode", "_b", "_c", "alignText", "alignment", "currentAlignment", "createHeading", "type", "firstItemList", "firstParent", "previousElementSibling", "lastParent", "item", "insertAdjacentElement", "createList", "lastItemList", "verifyList", "parentListElements", "parentList", "addSelectionLink", "<PERSON><PERSON><PERSON>", "addLinkInput", "input", "createLinkKeyDown", "createLink", "setClose", "firstItem", "lastItem", "firstItemValue", "lastItemValue", "handlePaste", "clipboardData", "plainText", "getData", "deleteContents", "fragment", "split", "clearFormatting", "render", "h", "Host", "class", "_d", "tabindex", "onMouseEnter", "onMouseLeave", "ref", "contentEditable", "onMouseUp", "onKeyUp", "onKeyDown", "onPaste", "bind", "_e", "position", "variant", "color", "size", "onBdsClick", "activeMode", "slot", "padding", "alignItems", "gap", "onBdsInput", "flexShrink", "open", "disabled", "id"], "sources": ["src/components/rict-text/languages/pt_BR.tsx", "src/components/rict-text/languages/es_ES.tsx", "src/components/rict-text/languages/en_US.tsx", "src/components/rict-text/languages/index.ts", "src/components/rict-text/rich-text.scss?tag=bds-rich-text", "src/components/rict-text/rich-text.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    bold: 'Negrito',\n    italic: 'It<PERSON>lico',\n    strike: '<PERSON><PERSON><PERSON>',\n    underline: 'Sublin<PERSON>o',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: '<PERSON><PERSON><PERSON> à esquerda',\n    align_center: '<PERSON><PERSON>ar ao centro',\n    align_right: 'Alinhar à direita',\n    unordered_list: 'Lista não ordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Citação',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpar formatação',\n    expand: 'Expandir',\n  },\n];\n", "export const esTerms = [\n  {\n    bold: 'Negrita',\n    italic: '<PERSON>urs<PERSON>',\n    strike: 'Tacha<PERSON>',\n    underline: 'Subrayado',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: 'Alinear a la izquierda',\n    align_center: 'Alinear al centro',\n    align_right: 'Alinear a la derecha',\n    unordered_list: 'Lista desordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Cita',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpiar formato',\n    expand: 'Expandir',\n  },\n];\n", "export const enTerms = [\n  {\n    bold: 'Bold',\n    italic: 'Italic',\n    strike: 'Strikethrough',\n    underline: 'Underline',\n    link: 'Link',\n    code: 'Code',\n    align_left: 'Align left',\n    align_center: 'Align center',\n    align_right: 'Align right',\n    unordered_list: 'Unordered list',\n    ordered_list: 'Ordered list',\n    quote: 'Quote',\n    h1: 'Heading 1',\n    h2: 'Heading 2',\n    h3: 'Heading 3',\n    h4: 'Heading 4',\n    h5: 'Heading 5',\n    h6: 'Heading 6',\n    clear_formatting: 'Clear formatting',\n    expand: 'Expand',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "@use '../../globals/helpers' as *;\n\n.rich-text {\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 8px;\n  gap: 8px;\n  border: 1px solid $color-border-1;\n  border-radius: 16px;\n  background-color: $color-surface-1;\n\n  &-top {\n    .format-buttons {\n      order: 1;\n    }\n    .preview {\n      order: 2;\n    }\n  }\n\n  &-bottom {\n    .format-buttons {\n      order: 2;\n    }\n    .preview {\n      order: 1;\n    }\n  }\n\n  &.active {\n    border-color: $color-primary;\n    box-shadow: 0 0 0 2px $color-info;\n  }\n\n  .format-buttons {\n    display: none !important;\n\n    &-active {\n      display: flex !important;\n      position: relative;\n      background-color: $color-surface-0;\n      border: 1px solid $color-border-1;\n      border-radius: 16px;\n      padding: 8px;\n    }\n\n    .style-onhover {\n      position: absolute;\n      background-color: $color-surface-1;\n      border-radius: 32px;\n      bottom: -32px;\n      right: 0;\n      opacity: 0;\n      -webkit-transition: opacity ease-in-out 0.5s;\n      -moz-transition: opacity ease-in-out 0.5s;\n      transition: opacity ease-in-out 0.5s;\n      pointer-events: none;\n\n      &.active {\n        opacity: 1;\n      }\n    }\n\n    .accordion-header {\n      width: 100%;\n      position: relative;\n      padding-right: 40px;\n\n      .buttons-list {\n        column-gap: 8px;\n\n        .editor-bar {\n          width: 0;\n          margin-right: -8px;\n        }\n\n        & bds-tooltip {\n          -webkit-transition: height ease-in-out 0.25s;\n          -moz-transition: height ease-in-out 0.25s;\n          transition: height ease-in-out 0.25s;\n          height: 0px;\n\n          & > bds-button,\n          & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n            height: 0;\n            opacity: 0;\n            display: block;\n            overflow: hidden;\n            -webkit-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            -moz-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n          }\n\n          &.active {\n            height: 32px;\n            & > bds-button,\n            & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n              overflow: inherit;\n              height: 32px;\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .arrow-down {\n        position: absolute;\n        right: 0;\n        top: 0;\n        display: none;\n        &.active {\n          display: block;\n        }\n      }\n    }\n  }\n  .preview {\n    box-sizing: border-box;\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    padding: 8px;\n    -webkit-transition: height ease-in-out 0.25s;\n    -moz-transition: height ease-in-out 0.25s;\n    transition: height ease-in-out 0.25s;\n\n    .editor-uai-design-system {\n      min-height: 48px;\n      height: 100%;\n      background-color: transparent;\n      font-size: 1rem;\n      line-height: 1.5;\n      overflow-y: auto;\n      outline: none;\n      font-family: $font-family;\n      font-style: normal;\n      font-weight: normal;\n      color: $color-content-default;\n      @include custom-scroll;\n\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5,\n      h6,\n      ul,\n      ol,\n      blockquote {\n        margin: 0 0 8px 0;\n      }\n\n      h1 {\n        font-size: 32px;\n        font-weight: 600;\n      }\n      h2 {\n        font-size: 28px;\n        font-weight: 600;\n      }\n      h3 {\n        font-size: 24px;\n        font-weight: 600;\n      }\n      h4 {\n        font-size: 20px;\n        font-weight: 600;\n      }\n      h5 {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      h6 {\n        font-size: 12px;\n        font-weight: 600;\n      }\n\n      a {\n        text-decoration: none;\n        color: $color-primary;\n      }\n\n      blockquote {\n        padding: 4px 16px 4px 32px;\n        font-size: 14px;\n        position: relative;\n        display: inline-block;\n\n        &::before,\n        &::after {\n          content: '\"';\n          position: absolute;\n          font-size: 24px;\n          color: $color-content-ghost;\n        }\n\n        &::before {\n          left: 8px;\n          top: -6px;\n        }\n\n        &::after {\n          right: 0px;\n          bottom: 0px;\n        }\n      }\n      code {\n        font-family: monospace;\n        font-size: 12px;\n        background-color: $color-surface-2;\n        padding: 4px;\n        border-radius: 4px;\n      }\n    }\n  }\n}\n\n/* Editor */\n", "import { Element, Component, h, Host, Prop, State, Watch, Event, EventEmitter } from '@stencil/core';\nimport { getParentsUntil } from '../../utils/position-element';\nimport { languages } from './rich-text-interface';\nimport { termTranslate } from './languages';\n\nexport type positionBar = 'top' | 'bottom';\n\n@Component({\n  tag: 'bds-rich-text',\n  styleUrl: 'rich-text.scss',\n  shadow: false,\n})\nexport class RichText {\n  private buttonsListElement?: HTMLElement = null;\n  private buttonsEditElements?: HTMLCollectionOf<HTMLBdsTooltipElement> = null;\n  private editor?: HTMLElement = null;\n  private dropDownLink?: HTMLBdsDropdownElement = null;\n  private inputSetLink?: HTMLBdsInputElement;\n\n  @Element() el: HTMLElement;\n  @State() buttomBoldActive?: boolean = false;\n  @State() buttomItalicActive?: boolean = false;\n  @State() buttomStrikeActive?: boolean = false;\n  @State() buttomUnderlineActive?: boolean = false;\n  @State() buttomCodeActive?: boolean = false;\n  @State() buttomLinkActive?: boolean = false;\n  @State() buttomLinkValidDisabled?: boolean = true;\n  @State() buttomAlignLeftActive?: boolean = false;\n  @State() buttomAlignCenterActive?: boolean = false;\n  @State() buttomAlignRightActive?: boolean = false;\n  @State() buttomUnorderedListActive?: boolean = false;\n  @State() buttomOrderedListActive?: boolean = false;\n  @State() buttomQuoteActive?: boolean = false;\n  @State() buttomH1Active?: boolean = false;\n  @State() buttomH2Active?: boolean = false;\n  @State() buttomH3Active?: boolean = false;\n  @State() buttomH4Active?: boolean = false;\n  @State() buttomH5Active?: boolean = false;\n  @State() buttomH6Active?: boolean = false;\n  @State() buttomAccordionActive?: boolean = false;\n  @State() headerHeight?: string = '32px';\n  @State() hasSelectionRange?: boolean = false;\n  @State() selectedLinesList?: { element: HTMLElement }[] = null;\n  @State() treeElementsEditor?: HTMLElement[] = null;\n  @State() styleSectorActive?: string = null;\n  @State() styleOnHover?: string = 'teste';\n  @State() whenSelectionLink?: Range = null;\n  @State() linkButtonInput?: string = null;\n  @State() insideComponent?: boolean = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * weightButton to define if component has Bold Control.\n   */\n  @Prop() weightButton?: boolean = true;\n  /**\n   * italicButton to define if component has Italic Control.\n   */\n  @Prop() italicButton?: boolean = true;\n  /**\n   * strikeThroughbutton to define if component has Strike Control.\n   */\n  @Prop() strikeThroughButton?: boolean = true;\n  /**\n   * underlineButton to define if component has Underline Control.\n   */\n  @Prop() underlineButton?: boolean = true;\n  /**\n   * linkButton to define if component has Link Control.\n   */\n  @Prop() linkButton?: boolean = true;\n  /**\n   * codeButton to define if component has Code Control.\n   */\n  @Prop() codeButton?: boolean = true;\n  /**\n   * alignmentButtons to define if component has TextAlign Control.\n   */\n  @Prop() alignmentButtons?: boolean = true;\n  /**\n   * listButtons to define if component has List Control.\n   */\n  @Prop() listButtons?: boolean = true;\n  /**\n   * quoteButton to define if component has Quote Control.\n   */\n  @Prop() quoteButton?: boolean = true;\n  /**\n   * headingButtons to define if component has Heading Control.\n   */\n  @Prop() headingButtons?: boolean = true;\n  /**\n   * unstyledButton to define if component has Unstyled Control.\n   */\n  @Prop() unstyledButton?: boolean = true;\n  /**\n   * height is the prop to define height of component.\n   */\n  @Prop() height?: string = null;\n  /**\n   * maxHeight is the prop to define max height of component.\n   */\n  @Prop() maxHeight?: string = null;\n  /**\n   * positionBar is the prop to define max height of component.\n   */\n  @Prop() positionBar?: positionBar = 'top';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsRichTextChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsRichTextInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  componentDidLoad() {\n    if (this.editor.innerHTML.trim() === '') {\n      this.editor.innerHTML = '<p class=\"line\"><br></p>';\n    }\n    if (\n      this.weightButton ||\n      this.italicButton ||\n      this.strikeThroughButton ||\n      this.underlineButton ||\n      this.linkButton ||\n      this.codeButton ||\n      this.alignmentButtons ||\n      this.listButtons ||\n      this.quoteButton ||\n      this.headingButtons ||\n      this.unstyledButton\n    ) {\n      this.buttonsEditElements = this.buttonsListElement.getElementsByTagName(\n        'bds-tooltip',\n      ) as HTMLCollectionOf<HTMLBdsTooltipElement>;\n      this.accordionHeader(false);\n      this.editor.parentElement.style.height = `calc(100% - 56px)`;\n    } else {\n      this.editor.parentElement.style.height = `100%`;\n    }\n  }\n\n  @Watch('weightButton')\n  @Watch('italicButton')\n  @Watch('strikeThroughButton')\n  @Watch('underlineButton')\n  @Watch('linkButton')\n  @Watch('codeButton')\n  @Watch('alignmentButtons')\n  @Watch('listButtons')\n  @Watch('quoteButton')\n  @Watch('headingButtons')\n  @Watch('unstyledButton')\n  protected buttomsHeaderChanged(): void {\n    setTimeout(() => this.accordionHeader(this.buttomAccordionActive), 500);\n  }\n\n  @Watch('buttomAccordionActive')\n  protected buttomAccordionActiveChanged(): void {\n    this.accordionHeader(this.buttomAccordionActive);\n  }\n\n  private updateToolbarState() {\n    const selection = window.getSelection();\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    this.treeElementsEditor = getParentsUntil(parentElement, '.editor-uai-design-system');\n  }\n\n  // Coloca o cursor no final do editor\n  private accordionHeader(value: boolean) {\n    const allbuttonsEditElementsWidth = this.buttonsEditElements.length * 40;\n    const buttonsListWidth = this.buttonsListElement.offsetWidth;\n    const buttonAccordion = this.el.querySelector('#buttonAccordion') as HTMLBdsButtonElement;\n    if (buttonsListWidth < allbuttonsEditElementsWidth) {\n      buttonAccordion.classList.add('active');\n    } else {\n      buttonAccordion.classList.remove('active');\n    }\n    const diferrence = (buttonsListWidth * this.buttonsEditElements.length) / allbuttonsEditElementsWidth;\n    const numberOfColumns = Math.ceil(allbuttonsEditElementsWidth / buttonsListWidth);\n    const allbuttonsEditElements = Array.from(this.buttonsEditElements);\n    allbuttonsEditElements.slice(0, Math.floor(diferrence)).forEach((element) => {\n      element.classList.add('active');\n    });\n    if (value) {\n      allbuttonsEditElements.forEach((element) => {\n        element.classList.add('active');\n        this.editor.parentElement.style.height = `calc(100% - ${numberOfColumns * 32 + 24}px)`;\n      });\n    } else {\n      allbuttonsEditElements.slice(Math.floor(diferrence)).forEach((element) => {\n        element.classList.remove('active');\n        this.editor.parentElement.style.height = `calc(100% - 56px)`;\n      });\n    }\n  }\n\n  @Watch('treeElementsEditor')\n  protected treeElementsEditorChanged(value): void {\n    const tagList = value.map((element) => element?.tagName.toLowerCase());\n    const tagVerifyName = (tag) => tagList.includes(tag);\n    const getLine = value.find((el) => el?.classList.contains('line'));\n    this.buttomBoldActive = tagVerifyName('b');\n    this.buttomItalicActive = tagVerifyName('i');\n    this.buttomStrikeActive = tagVerifyName('strike');\n    this.buttomUnderlineActive = tagVerifyName('u');\n    this.buttomLinkActive = tagVerifyName('a');\n    this.buttomCodeActive = tagVerifyName('code');\n    this.buttomAlignLeftActive = getLine?.style.textAlign === 'left';\n    this.buttomAlignCenterActive = getLine?.style.textAlign === 'center';\n    this.buttomAlignRightActive = getLine?.style.textAlign === 'right';\n    this.buttomUnorderedListActive = tagList[0] === 'ul';\n    this.buttomOrderedListActive = tagList[0] === 'ol';\n    this.buttomQuoteActive = tagVerifyName('blockquote');\n    this.buttomH1Active = tagVerifyName('h1');\n    this.buttomH2Active = tagVerifyName('h2');\n    this.buttomH3Active = tagVerifyName('h3');\n    this.buttomH4Active = tagVerifyName('h4');\n    this.buttomH5Active = tagVerifyName('h5');\n    this.buttomH6Active = tagVerifyName('h6');\n  }\n\n  private refButtonsListElement = (el: HTMLElement): void => {\n    this.buttonsListElement = el;\n  };\n  private refeditorElement = (el: HTMLElement): void => {\n    this.editor = el;\n  };\n  private refDropDownLinkElement = (el: HTMLBdsDropdownElement): void => {\n    this.dropDownLink = el;\n  };\n\n  private refInputSetLink = (el: HTMLBdsInputElement): void => {\n    this.inputSetLink = el;\n  };\n\n  private clearToolbar = () => {\n    this.buttomBoldActive = false;\n    this.buttomItalicActive = false;\n    this.buttomStrikeActive = false;\n    this.buttomUnderlineActive = false;\n    this.buttomLinkActive = false;\n    this.buttomCodeActive = false;\n    this.buttomAlignLeftActive = false;\n    this.buttomAlignCenterActive = false;\n    this.buttomAlignRightActive = false;\n    this.buttomUnorderedListActive = false;\n    this.buttomOrderedListActive = false;\n    this.buttomQuoteActive = false;\n    this.buttomH1Active = false;\n    this.buttomH2Active = false;\n    this.buttomH3Active = false;\n    this.buttomH4Active = false;\n    this.buttomH5Active = false;\n    this.buttomH6Active = false;\n  };\n\n  private setheaderHeight = () => {\n    this.buttomAccordionActive = !this.buttomAccordionActive;\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    const range = selection.getRangeAt(0);\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  };\n\n  private onBlur = () => {\n    this.el.classList.remove('active');\n    if (this.insideComponent === false) {\n      this.clearToolbar();\n    }\n    this.bdsBlur.emit();\n  };\n\n  private onFocus = () => {\n    this.el.classList.add('active');\n    this.bdsFocus.emit();\n  };\n\n  // Função para ajustar parágrafos durante a edição\n  private onInput(ev: InputEvent) {\n    ev.preventDefault();\n    this.bdsRichTextInput.emit(ev);\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const currentNode = range.startContainer;\n\n    // Se o nó atual é uma `div`, converta-o em um `p`\n    if (currentNode.nodeType === Node.ELEMENT_NODE && (currentNode as HTMLElement).tagName === 'DIV') {\n      const divElement = currentNode as HTMLElement;\n\n      const pElement = document.createElement('p');\n      pElement.classList.add('line');\n      pElement.innerHTML = divElement.innerHTML;\n\n      divElement.parentNode.replaceChild(pElement, divElement);\n    }\n\n    // Garante que novas linhas (Enter) criem <p> ao invés de <div>\n    this.editor.querySelectorAll('div').forEach((div) => {\n      const p = document.createElement('p');\n      p.classList.add('line');\n      p.innerHTML = div.innerHTML;\n      div.replaceWith(p);\n    });\n  }\n\n  private onKeydown = (event: KeyboardEvent) => {\n    if (event.key === 'Backspace') {\n      const selection = window.getSelection();\n      if (!selection || selection.rangeCount === 0) return;\n\n      const range = selection.getRangeAt(0);\n      const startNode = range.startContainer;\n\n      // Encontra o elemento de bloco que contém o cursor\n      let blockElement = startNode.nodeType === Node.TEXT_NODE ? startNode.parentElement : (startNode as HTMLElement);\n\n      while (blockElement && !blockElement.classList.contains('line') && blockElement !== this.editor) {\n        blockElement = blockElement.parentElement;\n      }\n\n      // Se o elemento atual for um <blockquote> e estiver vazio, removê-lo\n      if (\n        blockElement &&\n        blockElement.tagName === 'BLOCKQUOTE' &&\n        blockElement.classList.contains('line') &&\n        blockElement.innerText.length <= 1\n      ) {\n        event.preventDefault(); // Impede a exclusão padrão\n        blockElement.remove(); // Remove apenas o blockquote vazio\n      }\n    }\n    if (this.editor.textContent.length === 0 && event.key === 'Backspace') {\n      event.preventDefault();\n      this.editor.innerHTML = `<p class=\"line\"><br></p>`;\n      this.setCursorToEnd();\n    }\n    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {\n      event.preventDefault(); // Impede o Ctrl+Z\n      event.stopPropagation(); // Evita que afete outros elementos\n    }\n  };\n\n  // Controle a navegação do componente\n  private onFocusEditorBar(ev: FocusEvent) {\n    const editorBar = ev.target as HTMLElement;\n    const NextButton = editorBar.nextElementSibling.querySelector('bds-button');\n    const ElementToFocus = NextButton.shadowRoot.querySelector('.focus') as HTMLElement;\n    ElementToFocus.focus();\n    this.buttomAccordionActive = true;\n  }\n\n  // Coloca o cursor no final do editor\n  private setCursorToEnd() {\n    const range = document.createRange();\n    const sel = window.getSelection();\n    range.selectNodeContents(this.editor);\n    range.collapse(false);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n\n  private tagName(tag: string, tagList: HTMLElement[]): boolean {\n    const value = tagList.map((element) => element?.tagName.toLowerCase());\n    return value.includes(tag);\n  }\n\n  private wrapSelection(ev: CustomEvent, tag: string, link?: string) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n\n    const tagList = getParentsUntil(parentElement, '.line');\n    const isTagApplied = this.tagName(tag, tagList);\n\n    // Se a seleção estiver vazia, cria um espaço invisível para edição\n    let content: DocumentFragment;\n    let collapsedCursor = false;\n\n    // Se a tag já está aplicada e o usuário quer remover\n    if (isTagApplied) {\n      const appliedTag = tagList.find((el) => el.tagName.toLowerCase() === tag);\n      if (appliedTag) {\n        const parent = appliedTag.parentElement;\n        const isFullSelection = range.toString().trim() === appliedTag.textContent.trim();\n        const isAtEndOfTag = range.endOffset === appliedTag.textContent.length;\n\n        if (isFullSelection && parent) {\n          // Remove a tag se toda a seleção corresponde ao conteúdo da tag\n          while (appliedTag.firstChild) {\n            parent.insertBefore(appliedTag.firstChild, appliedTag);\n          }\n          parent.removeChild(appliedTag);\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        } else if (isAtEndOfTag) {\n          // Se o cursor está no final da tag, move para fora dela\n          content = document.createDocumentFragment();\n          const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n          content.appendChild(placeholder);\n          collapsedCursor = true;\n          const newRange = document.createRange();\n          newRange.setStartAfter(appliedTag); // Define o início depois do elemento\n          newRange.setEndAfter(appliedTag);\n          newRange.insertNode(content);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n          this.updateToolbarState();\n        } else {\n          // Se o cursor está no final da tag, move para fora dela\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        }\n      }\n      return;\n    }\n\n    if (range.collapsed) {\n      content = document.createDocumentFragment();\n      const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n      content.appendChild(placeholder);\n      collapsedCursor = true;\n    } else {\n      content = range.extractContents();\n    }\n\n    // Remove tags desnecessárias dentro da seleção\n    content.querySelectorAll('*').forEach((element) => {\n      while (element.firstChild) {\n        element.parentNode.insertBefore(element.firstChild, element);\n      }\n      element.remove();\n    });\n\n    // Cria a nova tag e aplica o conteúdo extraído\n    const wrapper = document.createElement(tag);\n    if (tag === 'a' && link) {\n      wrapper.setAttribute('href', link);\n    }\n    wrapper.appendChild(content);\n    range.insertNode(wrapper);\n\n    // Remove tags vazias no editor\n    this.editor.querySelectorAll('*').forEach((el) => {\n      if (!el.textContent.trim() && el.children.length === 0) {\n        el.remove();\n      }\n    });\n\n    // Se o cursor estava no meio da edição, mantém a seleção original\n    const newRange = document.createRange();\n    if (collapsedCursor) {\n      newRange.setStart(wrapper, 0);\n      newRange.setEnd(wrapper, 1);\n    } else {\n      newRange.setStartBefore(wrapper.firstChild || wrapper);\n      newRange.setEndAfter(wrapper.lastChild || wrapper);\n    }\n    selection.removeAllRanges();\n\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    // Emite o evento para atualizar o estado\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  private wrapSelectionLine(tag: string, enableLinesReturn: boolean = false) {\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Verifica se todas as linhas já possuem a tag escolhida\n    const allAreSameTag = [...selectedLines].every((line) =>\n      tag === 'li' ? false : line.tagName.toLowerCase() === tag,\n    );\n\n    const returnSelected: HTMLElement[] = [...selectedLines].map((el) => {\n      const newElement = document.createElement(allAreSameTag ? 'p' : tag);\n      newElement.classList.add('line');\n      newElement.innerHTML = el.innerHTML;\n      el.replaceWith(newElement);\n      return newElement;\n    });\n\n    if (enableLinesReturn) {\n      this.selectedLinesList = returnSelected.map((element) => ({ element }));\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    const newRange = document.createRange();\n    let lastNode = returnSelected[0].lastChild;\n\n    // Se não houver filhos, cria um nó de texto para evitar erro\n    if (!lastNode) {\n      lastNode = document.createTextNode('');\n      returnSelected[0].appendChild(lastNode);\n    }\n\n    // Se o último nó for outro elemento, busca um nó de texto dentro dele\n    while (lastNode && lastNode.nodeType !== Node.TEXT_NODE) {\n      lastNode = lastNode.lastChild || lastNode;\n    }\n\n    // Define o range no final do último nó de texto\n    newRange.setStart(lastNode, lastNode.textContent?.length || 0);\n    newRange.setEnd(lastNode, lastNode.textContent?.length || 0);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para aplicar alinhamento ao texto selecionado\n  private alignText(ev: CustomEvent, alignment: 'left' | 'center' | 'right' | 'justify') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    let blockElement = range.startContainer as HTMLElement;\n\n    // Percorre os elementos até encontrar um bloco válido que tenha a classe \"line\"\n    while (blockElement && blockElement !== this.editor) {\n      if (blockElement.nodeType === Node.ELEMENT_NODE && blockElement.classList.contains('line')) {\n        break;\n      }\n      blockElement = blockElement.parentElement;\n    }\n\n    // Se encontrou um elemento de bloco com a classe \"line\"\n    if (blockElement && blockElement !== this.editor) {\n      // Verifica se o alinhamento já está aplicado\n      const currentAlignment = (blockElement as HTMLElement).style.textAlign;\n      if (currentAlignment === alignment) {\n        // Se já estiver alinhado, remove o alinhamento\n        (blockElement as HTMLElement).style.textAlign = '';\n      } else {\n        // Caso contrário, aplica o alinhamento\n        (blockElement as HTMLElement).style.textAlign = alignment;\n      }\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createHeading(ev: CustomEvent, type: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine(type, true);\n    const firstItemList = this.selectedLinesList[0]?.element;\n    const firstParent = firstItemList.parentElement.previousElementSibling;\n    const lastParent = firstItemList.parentElement.nextElementSibling;\n    const parent = firstItemList.parentElement;\n    if (parent.tagName.toLowerCase() === 'ul') {\n      this.selectedLinesList.forEach((item) => {\n        if (firstParent) {\n          firstParent.insertAdjacentElement('afterend', item.element);\n        } else if (lastParent) {\n          lastParent.insertAdjacentElement('beforebegin', item.element);\n        } else {\n          this.editor.insertAdjacentElement('afterbegin', item.element);\n        }\n      });\n      if (Array.from(parent.getElementsByTagName('li')).length == 0) {\n        parent.remove();\n      }\n    }\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createList(ev: CustomEvent, type: 'ol' | 'ul') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine('li', true);\n    const firstItemList = this.selectedLinesList[0].element;\n    const lastItemList = this.selectedLinesList[this.selectedLinesList.length - 1]?.element;\n    const wrapper = document.createElement(type);\n    const parent = firstItemList.parentElement;\n\n    if (!this.verifyList(firstItemList, lastItemList)) {\n      parent.insertBefore(wrapper, firstItemList);\n      this.selectedLinesList.forEach((item) => {\n        wrapper.appendChild(item.element);\n      });\n    } else {\n      const parentListElements = parent.getElementsByTagName('li');\n      const parentList = Array.from(parentListElements).map((element) => ({ element }));\n\n      if (parentList.length == this.selectedLinesList.length) {\n        if (type !== parent.tagName.toLowerCase()) {\n          wrapper.innerHTML = parent.innerHTML;\n          parent.parentNode.replaceChild(wrapper, parent);\n        } else {\n          this.selectedLinesList.forEach((item) => {\n            const tagList = parent.parentElement.tagName.toLowerCase() === 'li' ? 'li' : 'p';\n            const newElement = document.createElement(tagList);\n            newElement.classList.add('line');\n            newElement.innerHTML = item.element.innerHTML;\n            if (parent.parentElement.tagName.toLowerCase() === 'li') {\n              parent.parentElement.insertAdjacentElement('afterend', newElement);\n            } else {\n              parent.previousElementSibling.insertAdjacentElement('afterend', newElement);\n            }\n            parent.removeChild(item.element);\n          });\n          parent.remove();\n        }\n      } else {\n        // parent.insertBefore(wrapper, firstItemList);\n        firstItemList.previousElementSibling.insertAdjacentElement('beforeend', wrapper);\n        this.selectedLinesList.forEach((item) => {\n          wrapper.appendChild(item.element);\n        });\n      }\n    }\n  }\n\n  private addSelectionLink(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      this.dropDownLink.setOpen();\n    }\n    this.editor.focus();\n    const selection = window.getSelection();\n    this.whenSelectionLink = selection.getRangeAt(0);\n    const ElementToFocus = this.inputSetLink.shadowRoot.querySelector('.input__container__text') as HTMLInputElement;\n    ElementToFocus.focus();\n  }\n\n  private addLinkInput(ev: InputEvent) {\n    ev.preventDefault();\n    const input = ev.target as HTMLInputElement | null;\n    this.linkButtonInput = input.value;\n    if (this.linkButtonInput.length > 0) {\n      this.buttomLinkValidDisabled = false;\n    } else {\n      this.buttomLinkValidDisabled = true;\n    }\n  }\n\n  private createLinkKeyDown(ev: KeyboardEvent) {\n    if (ev.key == 'Enter') {\n      this.createLink(ev);\n    }\n  }\n\n  private createLink(ev) {\n    ev.preventDefault();\n    const selection = window.getSelection();\n    selection.removeAllRanges();\n    selection.addRange(this.whenSelectionLink);\n    this.wrapSelection(ev, 'a', this.linkButtonInput);\n    if (this.dropDownLink) {\n      this.dropDownLink.setClose();\n    }\n  }\n\n  private verifyList(firstItem: HTMLElement, lastItem: HTMLElement) {\n    const firstItemValue =\n      firstItem.parentElement.tagName.toLowerCase() === 'ul' || firstItem.parentElement.tagName.toLowerCase() === 'ol';\n    const lastItemValue =\n      lastItem.parentElement.tagName.toLowerCase() === 'ul' || lastItem.parentElement.tagName.toLowerCase() === 'ol';\n    return firstItemValue && lastItemValue;\n  }\n\n  // Função para limpar o HTML ao colar conteúdo\n  private handlePaste(event: ClipboardEvent) {\n    event.preventDefault(); // Bloqueia a colagem padrão\n    event.stopPropagation(); // Evita que afete outros elementos\n\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const plainText = clipboardData.getData('text/plain'); // Obtém apenas texto puro\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    if (parentElement.classList.contains('line')) {\n      parentElement.remove();\n    }\n\n    range.deleteContents(); // Remove qualquer seleção existente\n\n    // Converte cada linha do texto colado em <p class=\"line\">\n    const fragment = document.createDocumentFragment();\n    plainText.split('\\n').forEach((line) => {\n      if (line.trim()) {\n        const p = document.createElement('p');\n        p.classList.add('line');\n        p.textContent = line.trim();\n        fragment.appendChild(p);\n      }\n    });\n\n    // Insere o conteúdo processado no local do cursor\n    range.insertNode(fragment);\n\n    // Ajusta o cursor para o final do texto inserido\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  // Função para restaurar a formatação do texto selecionado\n  private clearFormatting(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Remove a formatação de cada linha\n    selectedLines.forEach((line) => {\n      line.innerHTML = line.textContent; // Remove todas as tags HTML\n      line.style.textAlign = ''; // Remove o alinhamento\n    });\n\n    this.wrapSelectionLine('p', true);\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`rich-text`]: true,\n          [`rich-text-${this.positionBar}`]: true,\n        }}\n        style={{ height: this.height, maxHeight: this.maxHeight }}\n        tabindex=\"0\"\n        onMouseEnter={() => (this.insideComponent = true)}\n        onMouseLeave={() => (this.insideComponent = false)}\n      >\n        <div class=\"preview\">\n          <div\n            data-test={this.dataTest}\n            ref={(el) => this.refeditorElement(el)}\n            contentEditable=\"true\"\n            class=\"editor-uai-design-system\"\n            tabindex=\"0\"\n            onBlur={this.onBlur}\n            onFocus={this.onFocus}\n            onMouseUp={() => this.updateToolbarState()}\n            onKeyUp={() => this.updateToolbarState()}\n            onKeyDown={(ev) => this.onKeydown(ev)}\n            onInput={(ev) => this.onInput(ev)}\n            onPaste={this.handlePaste.bind(this)}\n          ></div>\n        </div>\n\n        <bds-grid\n          class={{\n            [`format-buttons`]: true,\n            [`format-buttons-active`]:\n              this.weightButton ||\n              this.italicButton ||\n              this.strikeThroughButton ||\n              this.underlineButton ||\n              this.linkButton ||\n              this.codeButton ||\n              this.alignmentButtons ||\n              this.listButtons ||\n              this.quoteButton ||\n              this.headingButtons ||\n              this.unstyledButton,\n          }}\n        >\n          <div class=\"accordion-header\">\n            <bds-grid ref={(el) => this.refButtonsListElement(el)} class=\"buttons-list\" flex-wrap=\"wrap\">\n              <div onFocus={(ev) => this.onFocusEditorBar(ev)} tabindex=\"1\" class=\"editor-bar\"></div>\n              {this.weightButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'bold')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomBoldActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'b')}\n                    icon-left=\"text-style-bold\"\n                    aria-label={`${termTranslate(this.language, 'bold')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.italicButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'italic')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomItalicActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'i')}\n                    icon-left=\"text-style-italic\"\n                    aria-label={`${termTranslate(this.language, 'italic')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.strikeThroughButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'strike')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomStrikeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'strike')}\n                    icon-left=\"text-style-strikethrough\"\n                    aria-label={`${termTranslate(this.language, 'strike')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.underlineButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'underline')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnderlineActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'u')}\n                    icon-left=\"text-style-underline\"\n                    aria-label={`${termTranslate(this.language, 'underline')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.linkButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'link')}`} position=\"top-center\">\n                  {this.buttomLinkActive ? (\n                    <bds-button\n                      variant=\"solid\"\n                      color=\"content\"\n                      size=\"short\"\n                      onBdsClick={(ev) => this.wrapSelection(ev, 'a')}\n                      icon-left=\"link\"\n                      aria-label={`${termTranslate(this.language, 'link')}`}\n                    ></bds-button>\n                  ) : (\n                    <bds-dropdown\n                      ref={(el) => this.refDropDownLinkElement(el)}\n                      activeMode=\"click\"\n                      position=\"bottom-left\"\n                    >\n                      <div slot=\"dropdown-activator\">\n                        <bds-button\n                          slot=\"dropdown-activator\"\n                          variant=\"text\"\n                          color=\"content\"\n                          size=\"short\"\n                          onBdsClick={(ev) => this.addSelectionLink(ev)}\n                          icon-left=\"link\"\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </div>\n                      <bds-grid padding=\"half\" alignItems=\"center\" gap=\"half\" slot=\"dropdown-content\">\n                        <bds-input\n                          ref={this.refInputSetLink}\n                          onBdsInput={(ev) => this.addLinkInput(ev.detail)}\n                          style={{ flexShrink: '99999' }}\n                          placeholder=\"adcione o link aqui\"\n                          onKeyDown={(ev) => this.createLinkKeyDown(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                        ></bds-input>\n                        <bds-button\n                          disabled={this.buttomLinkValidDisabled}\n                          icon-left=\"check\"\n                          onBdsClick={(ev) => this.createLink(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </bds-grid>\n                    </bds-dropdown>\n                  )}\n                </bds-tooltip>\n              )}\n              {this.codeButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'code')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomCodeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'code')}\n                    icon-left=\"code\"\n                    aria-label={`${termTranslate(this.language, 'code')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_left')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignLeftActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'left')}\n                    icon-left=\"align-left\"\n                    aria-label={`${termTranslate(this.language, 'align_left')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_center')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignCenterActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'center')}\n                    icon-left=\"align-center\"\n                    aria-label={`${termTranslate(this.language, 'align_center')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_right')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignRightActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'right')}\n                    icon-left=\"align-right\"\n                    aria-label={`${termTranslate(this.language, 'align_right')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'unordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnorderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ul')}\n                    icon-left=\"unordered-list\"\n                    aria-label={`${termTranslate(this.language, 'unordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'ordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomOrderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ol')}\n                    icon-left=\"ordered-list\"\n                    aria-label={`${termTranslate(this.language, 'ordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.quoteButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'quote')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomQuoteActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'blockquote')}\n                    icon-left=\"quote\"\n                    aria-label={`${termTranslate(this.language, 'quote')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h1')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH1Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h1')}\n                    icon-left=\"h-1\"\n                    aria-label={`${termTranslate(this.language, 'h1')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h2')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH2Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h2')}\n                    icon-left=\"h-2\"\n                    aria-label={`${termTranslate(this.language, 'h2')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h3')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH3Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h3')}\n                    icon-left=\"h-3\"\n                    aria-label={`${termTranslate(this.language, 'h3')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h4')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH4Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h4')}\n                    icon-left=\"h-4\"\n                    aria-label={`${termTranslate(this.language, 'h4')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h5')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH5Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h5')}\n                    icon-left=\"h-5\"\n                    aria-label={`${termTranslate(this.language, 'h5')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h6')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH6Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h6')}\n                    icon-left=\"h-6\"\n                    aria-label={`${termTranslate(this.language, 'h6')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.unstyledButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'clear_formatting')}`} position=\"top-center\">\n                  <bds-button\n                    variant=\"text\"\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.clearFormatting(ev)}\n                    icon-left=\"unstyled\"\n                    aria-label={`${termTranslate(this.language, 'clear_formatting')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n            </bds-grid>\n            <bds-button\n              id=\"buttonAccordion\"\n              variant={this.buttomAccordionActive ? 'solid' : 'text'}\n              class=\"arrow-down\"\n              color=\"content\"\n              size=\"short\"\n              onBdsClick={() => this.setheaderHeight()}\n              icon-left={\n                this.positionBar == 'top'\n                  ? this.buttomAccordionActive\n                    ? 'arrow-up'\n                    : 'arrow-down'\n                  : this.buttomAccordionActive\n                    ? 'arrow-down'\n                    : 'arrow-up'\n              }\n            ></bds-button>\n          </div>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kcAAO,IAAMA,EAAU,CACrB,CACEC,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,UAAW,aACXC,KAAM,OACNC,KAAM,SACNC,WAAY,qBACZC,aAAc,oBACdC,YAAa,oBACbC,eAAgB,qBAChBC,aAAc,iBACdC,MAAO,UACPC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,iBAAkB,oBAClBC,OAAQ,aCrBL,IAAMC,EAAU,CACrB,CACEpB,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,UAAW,YACXC,KAAM,OACNC,KAAM,SACNC,WAAY,yBACZC,aAAc,oBACdC,YAAa,uBACbC,eAAgB,oBAChBC,aAAc,iBACdC,MAAO,OACPC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,iBAAkB,kBAClBC,OAAQ,aCrBL,IAAME,EAAU,CACrB,CACErB,KAAM,OACNC,OAAQ,SACRC,OAAQ,gBACRC,UAAW,YACXC,KAAM,OACNC,KAAM,OACNC,WAAY,aACZC,aAAc,eACdC,YAAa,cACbC,eAAgB,iBAChBC,aAAc,eACdC,MAAO,QACPC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,iBAAkB,mBAClBC,OAAQ,WCfL,IAAMG,EAAgB,SAACC,EAAiBC,GAC7C,IAAIC,EACJ,OAAQF,GACN,IAAK,QACHE,EAAY1B,EAAQ2B,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,IAAK,QACHC,EAAYL,EAAQM,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,IAAK,QACHC,EAAYJ,EAAQK,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAClC,MACF,QACEC,EAAY1B,EAAQ2B,KAAI,SAACC,GAAS,OAAAA,EAAKH,EAAL,IAEtC,OAAOC,CACT,ECtBA,IAAMG,EAAc,qnL,ICYPC,EAAQC,EAAA,2BALrB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,8KAMUA,KAAkBC,mBAAiB,KACnCD,KAAmBE,oBAA6C,KAChEF,KAAMG,OAAiB,KACvBH,KAAYI,aAA4B,KAIvCJ,KAAgBK,iBAAa,MAC7BL,KAAkBM,mBAAa,MAC/BN,KAAkBO,mBAAa,MAC/BP,KAAqBQ,sBAAa,MAClCR,KAAgBS,iBAAa,MAC7BT,KAAgBU,iBAAa,MAC7BV,KAAuBW,wBAAa,KACpCX,KAAqBY,sBAAa,MAClCZ,KAAuBa,wBAAa,MACpCb,KAAsBc,uBAAa,MACnCd,KAAyBe,0BAAa,MACtCf,KAAuBgB,wBAAa,MACpChB,KAAiBiB,kBAAa,MAC9BjB,KAAckB,eAAa,MAC3BlB,KAAcmB,eAAa,MAC3BnB,KAAcoB,eAAa,MAC3BpB,KAAcqB,eAAa,MAC3BrB,KAAcsB,eAAa,MAC3BtB,KAAcuB,eAAa,MAC3BvB,KAAqBwB,sBAAa,MAClCxB,KAAYyB,aAAY,OACxBzB,KAAiB0B,kBAAa,MAC9B1B,KAAiB2B,kBAAgC,KACjD3B,KAAkB4B,mBAAmB,KACrC5B,KAAiB6B,kBAAY,KAC7B7B,KAAY8B,aAAY,QACxB9B,KAAiB+B,kBAAW,KAC5B/B,KAAegC,gBAAY,KAC3BhC,KAAeiC,gBAAa,MAI7BjC,KAAQkC,SAAe,QAIvBlC,KAAYmC,aAAa,KAIzBnC,KAAYoC,aAAa,KAIzBpC,KAAmBqC,oBAAa,KAIhCrC,KAAesC,gBAAa,KAI5BtC,KAAUuC,WAAa,KAIvBvC,KAAUwC,WAAa,KAIvBxC,KAAgByC,iBAAa,KAI7BzC,KAAW0C,YAAa,KAIxB1C,KAAW2C,YAAa,KAIxB3C,KAAc4C,eAAa,KAI3B5C,KAAc6C,eAAa,KAI3B7C,KAAM8C,OAAY,KAIlB9C,KAAS+C,UAAY,KAIrB/C,KAAWgD,YAAiB,MAK5BhD,KAAQiD,SAAY,KAoIpBjD,KAAAkD,sBAAwB,SAACC,GAC/BpD,EAAKE,mBAAqBkD,CAC5B,EACQnD,KAAAoD,iBAAmB,SAACD,GAC1BpD,EAAKI,OAASgD,CAChB,EACQnD,KAAAqD,uBAAyB,SAACF,GAChCpD,EAAKK,aAAe+C,CACtB,EAEQnD,KAAAsD,gBAAkB,SAACH,GACzBpD,EAAKwD,aAAeJ,CACtB,EAEQnD,KAAYwD,aAAG,WACrBzD,EAAKM,iBAAmB,MACxBN,EAAKO,mBAAqB,MAC1BP,EAAKQ,mBAAqB,MAC1BR,EAAKS,sBAAwB,MAC7BT,EAAKW,iBAAmB,MACxBX,EAAKU,iBAAmB,MACxBV,EAAKa,sBAAwB,MAC7Bb,EAAKc,wBAA0B,MAC/Bd,EAAKe,uBAAyB,MAC9Bf,EAAKgB,0BAA4B,MACjChB,EAAKiB,wBAA0B,MAC/BjB,EAAKkB,kBAAoB,MACzBlB,EAAKmB,eAAiB,MACtBnB,EAAKoB,eAAiB,MACtBpB,EAAKqB,eAAiB,MACtBrB,EAAKsB,eAAiB,MACtBtB,EAAKuB,eAAiB,MACtBvB,EAAKwB,eAAiB,KACxB,EAEQvB,KAAeyD,gBAAG,WACxB1D,EAAKyB,uBAAyBzB,EAAKyB,sBACnC,IAAMkC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAMC,EAAQJ,EAAUK,WAAW,GAEnCL,EAAUM,kBACVN,EAAUO,SAASH,EACrB,EAEQ9D,KAAMkE,OAAG,WACfnE,EAAKoD,GAAGgB,UAAUC,OAAO,UACzB,GAAIrE,EAAKkC,kBAAoB,MAAO,CAClClC,EAAKyD,c,CAEPzD,EAAKsE,QAAQC,MACf,EAEQtE,KAAOuE,QAAG,WAChBxE,EAAKoD,GAAGgB,UAAUK,IAAI,UACtBzE,EAAK0E,SAASH,MAChB,EAmCQtE,KAAA0E,UAAY,SAACC,GACnB,GAAIA,EAAMC,MAAQ,YAAa,CAC7B,IAAMlB,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,IAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAMc,EAAYf,EAAMgB,eAGxB,IAAIC,EAAeF,EAAUG,WAAaC,KAAKC,UAAYL,EAAUM,cAAiBN,EAEtF,MAAOE,IAAiBA,EAAaZ,UAAUiB,SAAS,SAAWL,IAAiBhF,EAAKI,OAAQ,CAC/F4E,EAAeA,EAAaI,a,CAI9B,GACEJ,GACAA,EAAaM,UAAY,cACzBN,EAAaZ,UAAUiB,SAAS,SAChCL,EAAaO,UAAUC,QAAU,EACjC,CACAZ,EAAMa,iBACNT,EAAaX,Q,EAGjB,GAAIrE,EAAKI,OAAOsF,YAAYF,SAAW,GAAKZ,EAAMC,MAAQ,YAAa,CACrED,EAAMa,iBACNzF,EAAKI,OAAOuF,UAAY,2BACxB3F,EAAK4F,gB,CAEP,IAAKhB,EAAMiB,SAAWjB,EAAMkB,UAAYlB,EAAMC,MAAQ,IAAK,CACzDD,EAAMa,iBACNb,EAAMmB,iB,CAEV,CA8yBD,CA1hCCjG,EAAAkG,UAAAC,iBAAA,WACE,GAAIhG,KAAKG,OAAOuF,UAAUO,SAAW,GAAI,CACvCjG,KAAKG,OAAOuF,UAAY,0B,CAE1B,GACE1F,KAAKmC,cACLnC,KAAKoC,cACLpC,KAAKqC,qBACLrC,KAAKsC,iBACLtC,KAAKuC,YACLvC,KAAKwC,YACLxC,KAAKyC,kBACLzC,KAAK0C,aACL1C,KAAK2C,aACL3C,KAAK4C,gBACL5C,KAAK6C,eACL,CACA7C,KAAKE,oBAAsBF,KAAKC,mBAAmBiG,qBACjD,eAEFlG,KAAKmG,gBAAgB,OACrBnG,KAAKG,OAAOgF,cAAciB,MAAMtD,OAAS,mB,KACpC,CACL9C,KAAKG,OAAOgF,cAAciB,MAAMtD,OAAS,M,GAenCjD,EAAAkG,UAAAM,qBAAA,eAAAtG,EAAAC,KACRsG,YAAW,WAAM,OAAAvG,EAAKoG,gBAAgBpG,EAAKyB,sBAA1B,GAAkD,I,EAI3D3B,EAAAkG,UAAAQ,6BAAA,WACRvG,KAAKmG,gBAAgBnG,KAAKwB,sB,EAGpB3B,EAAAkG,UAAAS,mBAAA,WACN,IAAM9C,EAAYC,OAAOC,eACzB,IAAME,EAAQJ,EAAUK,WAAW,GACnC,IAAM0C,EAAiB3C,EAAM4C,wBAC7B,IAAMvB,EACJsB,EAAezB,WAAaC,KAAKC,UAAYuB,EAAetB,cAAiBsB,EAC/EzG,KAAK4B,mBAAqB+E,EAAgBxB,EAAe,4B,EAInDtF,EAAAkG,UAAAI,gBAAA,SAAgBS,GAAhB,IAAA7G,EAAAC,KACN,IAAM6G,EAA8B7G,KAAKE,oBAAoBqF,OAAS,GACtE,IAAMuB,EAAmB9G,KAAKC,mBAAmB8G,YACjD,IAAMC,EAAkBhH,KAAKmD,GAAG8D,cAAc,oBAC9C,GAAIH,EAAmBD,EAA6B,CAClDG,EAAgB7C,UAAUK,IAAI,S,KACzB,CACLwC,EAAgB7C,UAAUC,OAAO,S,CAEnC,IAAM8C,EAAcJ,EAAmB9G,KAAKE,oBAAoBqF,OAAUsB,EAC1E,IAAMM,EAAkBC,KAAKC,KAAKR,EAA8BC,GAChE,IAAMQ,EAAyBC,MAAMC,KAAKxH,KAAKE,qBAC/CoH,EAAuBG,MAAM,EAAGL,KAAKM,MAAMR,IAAaS,SAAQ,SAACC,GAC/DA,EAAQzD,UAAUK,IAAI,SACxB,IACA,GAAIoC,EAAO,CACTU,EAAuBK,SAAQ,SAACC,GAC9BA,EAAQzD,UAAUK,IAAI,UACtBzE,EAAKI,OAAOgF,cAAciB,MAAMtD,OAAS,eAAA+E,OAAeV,EAAkB,GAAK,GAAE,MACnF,G,KACK,CACLG,EAAuBG,MAAML,KAAKM,MAAMR,IAAaS,SAAQ,SAACC,GAC5DA,EAAQzD,UAAUC,OAAO,UACzBrE,EAAKI,OAAOgF,cAAciB,MAAMtD,OAAS,mBAC3C,G,GAKMjD,EAAAkG,UAAA+B,0BAAA,SAA0BlB,GAClC,IAAMmB,EAAUnB,EAAMpH,KAAI,SAACoI,GAAY,OAAAA,IAAA,MAAAA,SAAO,SAAPA,EAASvC,QAAQ2C,aAAjB,IACvC,IAAMC,EAAgB,SAACC,GAAQ,OAAAH,EAAQI,SAASD,EAAjB,EAC/B,IAAME,EAAUxB,EAAMyB,MAAK,SAAClF,GAAO,OAAAA,IAAE,MAAFA,SAAE,SAAFA,EAAIgB,UAAUiB,SAAS,OAAvB,IACnCpF,KAAKK,iBAAmB4H,EAAc,KACtCjI,KAAKM,mBAAqB2H,EAAc,KACxCjI,KAAKO,mBAAqB0H,EAAc,UACxCjI,KAAKQ,sBAAwByH,EAAc,KAC3CjI,KAAKU,iBAAmBuH,EAAc,KACtCjI,KAAKS,iBAAmBwH,EAAc,QACtCjI,KAAKY,uBAAwBwH,IAAA,MAAAA,SAAA,SAAAA,EAAShC,MAAMkC,aAAc,OAC1DtI,KAAKa,yBAA0BuH,IAAA,MAAAA,SAAA,SAAAA,EAAShC,MAAMkC,aAAc,SAC5DtI,KAAKc,wBAAyBsH,IAAA,MAAAA,SAAA,SAAAA,EAAShC,MAAMkC,aAAc,QAC3DtI,KAAKe,0BAA4BgH,EAAQ,KAAO,KAChD/H,KAAKgB,wBAA0B+G,EAAQ,KAAO,KAC9C/H,KAAKiB,kBAAoBgH,EAAc,cACvCjI,KAAKkB,eAAiB+G,EAAc,MACpCjI,KAAKmB,eAAiB8G,EAAc,MACpCjI,KAAKoB,eAAiB6G,EAAc,MACpCjI,KAAKqB,eAAiB4G,EAAc,MACpCjI,KAAKsB,eAAiB2G,EAAc,MACpCjI,KAAKuB,eAAiB0G,EAAc,K,EA8D9BpI,EAAAkG,UAAAwC,QAAA,SAAQC,GACdA,EAAGhD,iBACHxF,KAAKyI,iBAAiBnE,KAAKkE,GAE3BxI,KAAK0I,kBAAkBpE,KAAK,CAAEsC,MAAO5G,KAAKG,OAAOuF,YAEjD,IAAMhC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,IAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAM4E,EAAc7E,EAAMgB,eAG1B,GAAI6D,EAAY3D,WAAaC,KAAK2D,cAAiBD,EAA4BtD,UAAY,MAAO,CAChG,IAAMwD,EAAaF,EAEnB,IAAMG,EAAWC,SAASC,cAAc,KACxCF,EAAS3E,UAAUK,IAAI,QACvBsE,EAASpD,UAAYmD,EAAWnD,UAEhCmD,EAAWI,WAAWC,aAAaJ,EAAUD,E,CAI/C7I,KAAKG,OAAOgJ,iBAAiB,OAAOxB,SAAQ,SAACyB,GAC3C,IAAMC,EAAIN,SAASC,cAAc,KACjCK,EAAElF,UAAUK,IAAI,QAChB6E,EAAE3D,UAAY0D,EAAI1D,UAClB0D,EAAIE,YAAYD,EAClB,G,EAyCMxJ,EAAAkG,UAAAwD,iBAAA,SAAiBf,GACvB,IAAMgB,EAAYhB,EAAGiB,OACrB,IAAMC,EAAaF,EAAUG,mBAAmB1C,cAAc,cAC9D,IAAM2C,EAAiBF,EAAWG,WAAW5C,cAAc,UAC3D2C,EAAeE,QACf9J,KAAKwB,sBAAwB,I,EAIvB3B,EAAAkG,UAAAJ,eAAA,WACN,IAAM7B,EAAQiF,SAASgB,cACvB,IAAMC,EAAMrG,OAAOC,eACnBE,EAAMmG,mBAAmBjK,KAAKG,QAC9B2D,EAAMoG,SAAS,OACfF,EAAIhG,kBACJgG,EAAI/F,SAASH,E,EAGPjE,EAAAkG,UAAAV,QAAA,SAAQ6C,EAAaH,GAC3B,IAAMnB,EAAQmB,EAAQvI,KAAI,SAACoI,GAAY,OAAAA,IAAA,MAAAA,SAAO,SAAPA,EAASvC,QAAQ2C,aAAjB,IACvC,OAAOpB,EAAMuB,SAASD,E,EAGhBrI,EAAAkG,UAAAoE,cAAA,SAAc3B,EAAiBN,EAAahK,GAClD,IAAMkM,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7DwF,EAAO5E,iBACP4E,EAAOtE,iB,CAGT,IAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU4G,YAAa,OAEjD,IAAMxG,EAAQJ,EAAUK,WAAW,GACnC,IAAM0C,EAAiB3C,EAAM4C,wBAC7B,IAAMvB,EACJsB,EAAezB,WAAaC,KAAKC,UAAYuB,EAAetB,cAAiBsB,EAE/E,IAAMsB,EAAUpB,EAAgBxB,EAAe,SAC/C,IAAMoF,EAAevK,KAAKqF,QAAQ6C,EAAKH,GAGvC,IAAIyC,EACJ,IAAIC,EAAkB,MAGtB,GAAIF,EAAc,CAChB,IAAMG,EAAa3C,EAAQM,MAAK,SAAClF,GAAO,OAAAA,EAAGkC,QAAQ2C,gBAAkBE,CAA7B,IACxC,GAAIwC,EAAY,CACd,IAAMC,EAASD,EAAWvF,cAC1B,IAAMyF,EAAkB9G,EAAM+G,WAAW5E,SAAWyE,EAAWjF,YAAYQ,OAC3E,IAAM6E,EAAehH,EAAMiH,YAAcL,EAAWjF,YAAYF,OAEhE,GAAIqF,GAAmBD,EAAQ,CAE7B,MAAOD,EAAWM,WAAY,CAC5BL,EAAOM,aAAaP,EAAWM,WAAYN,E,CAE7CC,EAAOO,YAAYR,GACnBhH,EAAUM,kBACVN,EAAUO,SAASH,GACnB9D,KAAKwG,oB,MACA,GAAIsE,EAAc,CAEvBN,EAAUzB,SAASoC,yBACnB,IAAMC,EAAcrC,SAASsC,eAAe,KAC5Cb,EAAQc,YAAYF,GACpBX,EAAkB,KAClB,IAAMc,EAAWxC,SAASgB,cAC1BwB,EAASC,cAAcd,GACvBa,EAASE,YAAYf,GACrBa,EAASG,WAAWlB,GACpB9G,EAAUM,kBACVN,EAAUO,SAASsH,GACnBvL,KAAKwG,oB,KACA,CAEL9C,EAAUM,kBACVN,EAAUO,SAASH,GACnB9D,KAAKwG,oB,EAGT,M,CAGF,GAAI1C,EAAM6H,UAAW,CACnBnB,EAAUzB,SAASoC,yBACnB,IAAMC,EAAcrC,SAASsC,eAAe,KAC5Cb,EAAQc,YAAYF,GACpBX,EAAkB,I,KACb,CACLD,EAAU1G,EAAM8H,iB,CAIlBpB,EAAQrB,iBAAiB,KAAKxB,SAAQ,SAACC,GACrC,MAAOA,EAAQoD,WAAY,CACzBpD,EAAQqB,WAAWgC,aAAarD,EAAQoD,WAAYpD,E,CAEtDA,EAAQxD,QACV,IAGA,IAAMyH,EAAU9C,SAASC,cAAcd,GACvC,GAAIA,IAAQ,KAAOhK,EAAM,CACvB2N,EAAQC,aAAa,OAAQ5N,E,CAE/B2N,EAAQP,YAAYd,GACpB1G,EAAM4H,WAAWG,GAGjB7L,KAAKG,OAAOgJ,iBAAiB,KAAKxB,SAAQ,SAACxE,GACzC,IAAKA,EAAGsC,YAAYQ,QAAU9C,EAAG4I,SAASxG,SAAW,EAAG,CACtDpC,EAAGiB,Q,CAEP,IAGA,IAAM4H,EAAWjD,SAASgB,cAC1B,GAAIU,EAAiB,CACnBuB,EAASC,SAASJ,EAAS,GAC3BG,EAASE,OAAOL,EAAS,E,KACpB,CACLG,EAASG,eAAeN,EAAQb,YAAca,GAC9CG,EAASP,YAAYI,EAAQO,WAAaP,E,CAE5CnI,EAAUM,kBAEVN,EAAUO,SAAS+H,GAEnBhM,KAAKwG,qBAGLxG,KAAK0I,kBAAkBpE,KAAK,CAAEsC,MAAO5G,KAAKG,OAAOuF,W,EAG3C7F,EAAAkG,UAAAsG,kBAAA,SAAkBnE,EAAaoE,GAAA,GAAAA,SAAA,GAAAA,EAAA,KAAkC,C,UACvE,IAAM5I,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU4G,YAAa,OAEjD,IAAMxG,EAAQJ,EAAUK,WAAW,GACnC,IAAMc,EAAYf,EAAMgB,eACxB,IAAMyH,EAAUzI,EAAM0I,aAGtB,IAAMC,EAAgB,IAAIC,IAE1B,IAAI/D,EAAc9D,EAAUM,cAC5B,MAAOwD,GAAeA,IAAgB4D,EAAQpH,cAAe,CAC3D,IAAIyC,EAAUe,EAAY3D,WAAaC,KAAKC,UAAYyD,EAAYxD,cAAiBwD,EACrF,GAAIf,GAAWA,EAAQzD,UAAUiB,SAAS,QAAS,CACjDqH,EAAcjI,IAAIoD,E,CAEpBe,EAAeA,EAAYgE,eAAgCC,EAAAjE,EAAYxD,iBAAa,MAAAyH,SAAA,SAAAA,EAAED,Y,CAIxF,IAAIE,EAAaN,EAAQvH,WAAaC,KAAKC,UAAYqH,EAAQpH,cAAiBoH,EAChF,MAAOM,IAAeA,EAAW1I,UAAUiB,SAAS,SAAWyH,IAAe7M,KAAKG,OAAQ,CACzF0M,EAAaA,EAAW1H,a,CAE1B,GAAI0H,GAAcA,EAAW1I,UAAUiB,SAAS,QAAS,CACvDqH,EAAcjI,IAAIqI,E,CAIpB,IAAMC,EAAgBC,cAAA,GAAIN,EAAa,MAAEO,OAAM,SAACC,GAC9C,OAAA/E,IAAQ,KAAO,MAAQ+E,EAAK5H,QAAQ2C,gBAAkBE,CAAtD,IAGF,IAAMgF,EAAgCH,cAAA,GAAIN,EAAa,MAAEjN,KAAI,SAAC2D,GAC5D,IAAMgK,EAAapE,SAASC,cAAc8D,EAAgB,IAAM5E,GAChEiF,EAAWhJ,UAAUK,IAAI,QACzB2I,EAAWzH,UAAYvC,EAAGuC,UAC1BvC,EAAGmG,YAAY6D,GACf,OAAOA,CACT,IAEA,GAAIb,EAAmB,CACrBtM,KAAK2B,kBAAoBuL,EAAe1N,KAAI,SAACoI,GAAO,OAAQA,QAAOA,EAAf,G,CAItD,IAAMoE,EAAWjD,SAASgB,cAC1B,IAAIqD,EAAWF,EAAe,GAAGd,UAGjC,IAAKgB,EAAU,CACbA,EAAWrE,SAASsC,eAAe,IACnC6B,EAAe,GAAG5B,YAAY8B,E,CAIhC,MAAOA,GAAYA,EAASpI,WAAaC,KAAKC,UAAW,CACvDkI,EAAWA,EAAShB,WAAagB,C,CAInCpB,EAASC,SAASmB,IAAUC,EAAAD,EAAS3H,eAAa,MAAA4H,SAAA,SAAAA,EAAA9H,SAAU,GAC5DyG,EAASE,OAAOkB,IAAUE,EAAAF,EAAS3H,eAAa,MAAA6H,SAAA,SAAAA,EAAA/H,SAAU,GAC1D7B,EAAUM,kBACVN,EAAUO,SAAS+H,GAEnBhM,KAAKwG,qBAELxG,KAAK0I,kBAAkBpE,KAAK,CAAEsC,MAAO5G,KAAKG,OAAOuF,W,EAI3C7F,EAAAkG,UAAAwH,UAAA,SAAU/E,EAAiBgF,GACjC,IAAMpD,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7DwF,EAAO5E,iBACP4E,EAAOtE,iB,CAET,IAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,IAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAIgB,EAAejB,EAAMgB,eAGzB,MAAOC,GAAgBA,IAAiB/E,KAAKG,OAAQ,CACnD,GAAI4E,EAAaC,WAAaC,KAAK2D,cAAgB7D,EAAaZ,UAAUiB,SAAS,QAAS,CAC1F,K,CAEFL,EAAeA,EAAaI,a,CAI9B,GAAIJ,GAAgBA,IAAiB/E,KAAKG,OAAQ,CAEhD,IAAMsN,EAAoB1I,EAA6BqB,MAAMkC,UAC7D,GAAImF,IAAqBD,EAAW,CAEjCzI,EAA6BqB,MAAMkC,UAAY,E,KAC3C,CAEJvD,EAA6BqB,MAAMkC,UAAYkF,C,EAKpD9J,EAAUM,kBACVN,EAAUO,SAASH,GAEnB9D,KAAKwG,qBAELxG,KAAK0I,kBAAkBpE,KAAK,CAAEsC,MAAO5G,KAAKG,OAAOuF,W,EAI3C7F,EAAAkG,UAAA2H,cAAA,SAAclF,EAAiBmF,GAA/B,IAAA5N,EAAAC,K,MACN,IAAMoK,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7DwF,EAAO5E,iBACP4E,EAAOtE,iB,CAET,IAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU4G,YAAa,OACjDtK,KAAKqM,kBAAkBsB,EAAM,MAC7B,IAAMC,GAAgBhB,EAAA5M,KAAK2B,kBAAkB,MAAI,MAAAiL,SAAA,SAAAA,EAAAhF,QACjD,IAAMiG,EAAcD,EAAczI,cAAc2I,uBAChD,IAAMC,EAAaH,EAAczI,cAAcwE,mBAC/C,IAAMgB,EAASiD,EAAczI,cAC7B,GAAIwF,EAAOtF,QAAQ2C,gBAAkB,KAAM,CACzChI,KAAK2B,kBAAkBgG,SAAQ,SAACqG,GAC9B,GAAIH,EAAa,CACfA,EAAYI,sBAAsB,WAAYD,EAAKpG,Q,MAC9C,GAAImG,EAAY,CACrBA,EAAWE,sBAAsB,cAAeD,EAAKpG,Q,KAChD,CACL7H,EAAKI,OAAO8N,sBAAsB,aAAcD,EAAKpG,Q,CAEzD,IACA,GAAIL,MAAMC,KAAKmD,EAAOzE,qBAAqB,OAAOX,QAAU,EAAG,CAC7DoF,EAAOvG,Q,IAMLvE,EAAAkG,UAAAmI,WAAA,SAAW1F,EAAiBmF,G,MAClC,IAAMvD,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7DwF,EAAO5E,iBACP4E,EAAOtE,iB,CAET,IAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU4G,YAAa,OACjDtK,KAAKqM,kBAAkB,KAAM,MAC7B,IAAMuB,EAAgB5N,KAAK2B,kBAAkB,GAAGiG,QAChD,IAAMuG,GAAevB,EAAA5M,KAAK2B,kBAAkB3B,KAAK2B,kBAAkB4D,OAAS,MAAE,MAAAqH,SAAA,SAAAA,EAAEhF,QAChF,IAAMiE,EAAU9C,SAASC,cAAc2E,GACvC,IAAMhD,EAASiD,EAAczI,cAE7B,IAAKnF,KAAKoO,WAAWR,EAAeO,GAAe,CACjDxD,EAAOM,aAAaY,EAAS+B,GAC7B5N,KAAK2B,kBAAkBgG,SAAQ,SAACqG,GAC9BnC,EAAQP,YAAY0C,EAAKpG,QAC3B,G,KACK,CACL,IAAMyG,EAAqB1D,EAAOzE,qBAAqB,MACvD,IAAMoI,EAAa/G,MAAMC,KAAK6G,GAAoB7O,KAAI,SAACoI,GAAO,OAAQA,QAAOA,EAAf,IAE9D,GAAI0G,EAAW/I,QAAUvF,KAAK2B,kBAAkB4D,OAAQ,CACtD,GAAIoI,IAAShD,EAAOtF,QAAQ2C,cAAe,CACzC6D,EAAQnG,UAAYiF,EAAOjF,UAC3BiF,EAAO1B,WAAWC,aAAa2C,EAASlB,E,KACnC,CACL3K,KAAK2B,kBAAkBgG,SAAQ,SAACqG,GAC9B,IAAMjG,EAAU4C,EAAOxF,cAAcE,QAAQ2C,gBAAkB,KAAO,KAAO,IAC7E,IAAMmF,EAAapE,SAASC,cAAcjB,GAC1CoF,EAAWhJ,UAAUK,IAAI,QACzB2I,EAAWzH,UAAYsI,EAAKpG,QAAQlC,UACpC,GAAIiF,EAAOxF,cAAcE,QAAQ2C,gBAAkB,KAAM,CACvD2C,EAAOxF,cAAc8I,sBAAsB,WAAYd,E,KAClD,CACLxC,EAAOmD,uBAAuBG,sBAAsB,WAAYd,E,CAElExC,EAAOO,YAAY8C,EAAKpG,QAC1B,IACA+C,EAAOvG,Q,MAEJ,CAELwJ,EAAcE,uBAAuBG,sBAAsB,YAAapC,GACxE7L,KAAK2B,kBAAkBgG,SAAQ,SAACqG,GAC9BnC,EAAQP,YAAY0C,EAAKpG,QAC3B,G,IAKE/H,EAAAkG,UAAAwI,iBAAA,SAAiB/F,GACvB,IAAM4B,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7D5E,KAAKI,aAAaoO,S,CAEpBxO,KAAKG,OAAO2J,QACZ,IAAMpG,EAAYC,OAAOC,eACzB5D,KAAK+B,kBAAoB2B,EAAUK,WAAW,GAC9C,IAAM6F,EAAiB5J,KAAKuD,aAAasG,WAAW5C,cAAc,2BAClE2C,EAAeE,O,EAGTjK,EAAAkG,UAAA0I,aAAA,SAAajG,GACnBA,EAAGhD,iBACH,IAAMkJ,EAAQlG,EAAGiB,OACjBzJ,KAAKgC,gBAAkB0M,EAAM9H,MAC7B,GAAI5G,KAAKgC,gBAAgBuD,OAAS,EAAG,CACnCvF,KAAKW,wBAA0B,K,KAC1B,CACLX,KAAKW,wBAA0B,I,GAI3Bd,EAAAkG,UAAA4I,kBAAA,SAAkBnG,GACxB,GAAIA,EAAG5D,KAAO,QAAS,CACrB5E,KAAK4O,WAAWpG,E,GAIZ3I,EAAAkG,UAAA6I,WAAA,SAAWpG,GACjBA,EAAGhD,iBACH,IAAM9B,EAAYC,OAAOC,eACzBF,EAAUM,kBACVN,EAAUO,SAASjE,KAAK+B,mBACxB/B,KAAKmK,cAAc3B,EAAI,IAAKxI,KAAKgC,iBACjC,GAAIhC,KAAKI,aAAc,CACrBJ,KAAKI,aAAayO,U,GAIdhP,EAAAkG,UAAAqI,WAAA,SAAWU,EAAwBC,GACzC,IAAMC,EACJF,EAAU3J,cAAcE,QAAQ2C,gBAAkB,MAAQ8G,EAAU3J,cAAcE,QAAQ2C,gBAAkB,KAC9G,IAAMiH,EACJF,EAAS5J,cAAcE,QAAQ2C,gBAAkB,MAAQ+G,EAAS5J,cAAcE,QAAQ2C,gBAAkB,KAC5G,OAAOgH,GAAkBC,C,EAInBpP,EAAAkG,UAAAmJ,YAAA,SAAYvK,GAClBA,EAAMa,iBACNb,EAAMmB,kBAEN,IAAMqJ,EAAgBxK,EAAMwK,eAAkBxL,OAAewL,cAC7D,IAAMC,EAAYD,EAAcE,QAAQ,cAExC,IAAM3L,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,IAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAM0C,EAAiB3C,EAAM4C,wBAC7B,IAAMvB,EACJsB,EAAezB,WAAaC,KAAKC,UAAYuB,EAAetB,cAAiBsB,EAC/E,GAAItB,EAAchB,UAAUiB,SAAS,QAAS,CAC5CD,EAAcf,Q,CAGhBN,EAAMwL,iBAGN,IAAMC,EAAWxG,SAASoC,yBAC1BiE,EAAUI,MAAM,MAAM7H,SAAQ,SAACsF,GAC7B,GAAIA,EAAKhH,OAAQ,CACf,IAAMoD,EAAIN,SAASC,cAAc,KACjCK,EAAElF,UAAUK,IAAI,QAChB6E,EAAE5D,YAAcwH,EAAKhH,OACrBsJ,EAASjE,YAAYjC,E,CAEzB,IAGAvF,EAAM4H,WAAW6D,GAGjB7L,EAAUM,kBACVN,EAAUO,SAASH,E,EAIbjE,EAAAkG,UAAA0J,gBAAA,SAAgBjH,G,MACtB,IAAM4B,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOxF,MAAQ,QAAS,CAC7DwF,EAAO5E,iBACP4E,EAAOtE,iB,CAET,IAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,IAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAMc,EAAYf,EAAMgB,eACxB,IAAMyH,EAAUzI,EAAM0I,aAGtB,IAAMC,EAAgB,IAAIC,IAE1B,IAAI/D,EAAc9D,EAAUM,cAC5B,MAAOwD,GAAeA,IAAgB4D,EAAQpH,cAAe,CAC3D,IAAIyC,EAAUe,EAAY3D,WAAaC,KAAKC,UAAYyD,EAAYxD,cAAiBwD,EACrF,GAAIf,GAAWA,EAAQzD,UAAUiB,SAAS,QAAS,CACjDqH,EAAcjI,IAAIoD,E,CAEpBe,EAAeA,EAAYgE,eAAgCC,EAAAjE,EAAYxD,iBAAa,MAAAyH,SAAA,SAAAA,EAAED,Y,CAIxF,IAAIE,EAAaN,EAAQvH,WAAaC,KAAKC,UAAYqH,EAAQpH,cAAiBoH,EAChF,MAAOM,IAAeA,EAAW1I,UAAUiB,SAAS,SAAWyH,IAAe7M,KAAKG,OAAQ,CACzF0M,EAAaA,EAAW1H,a,CAE1B,GAAI0H,GAAcA,EAAW1I,UAAUiB,SAAS,QAAS,CACvDqH,EAAcjI,IAAIqI,E,CAIpBJ,EAAc9E,SAAQ,SAACsF,GACrBA,EAAKvH,UAAYuH,EAAKxH,YACtBwH,EAAK7G,MAAMkC,UAAY,EACzB,IAEAtI,KAAKqM,kBAAkB,IAAK,MAG5B3I,EAAUM,kBACVN,EAAUO,SAASH,E,EAGrBjE,EAAAkG,UAAA2J,OAAA,W,QAAA,IAAA3P,EAAAC,K,QACE,OACE2P,EAACC,EAAI,CAAAhL,IAAA,2CACHiL,OAAKC,EAAA,GACHA,EAAC,aAAc,KACfA,EAAC,aAAAjI,OAAa7H,KAAKgD,cAAgB,K,GAErCoD,MAAO,CAAEtD,OAAQ9C,KAAK8C,OAAQC,UAAW/C,KAAK+C,WAC9CgN,SAAS,IACTC,aAAc,kBAAOjQ,EAAKkC,gBAAkB,IAA9B,EACdgO,aAAc,kBAAOlQ,EAAKkC,gBAAkB,KAA9B,GAEd0N,EAAK,OAAA/K,IAAA,2CAAAiL,MAAM,WACTF,EACa,OAAA/K,IAAA,uDAAA5E,KAAKiD,SAChBiN,IAAK,SAAC/M,GAAO,OAAApD,EAAKqD,iBAAiBD,EAAtB,EACbgN,gBAAgB,OAChBN,MAAM,2BACNE,SAAS,IACT7L,OAAQlE,KAAKkE,OACbK,QAASvE,KAAKuE,QACd6L,UAAW,WAAM,OAAArQ,EAAKyG,oBAAL,EACjB6J,QAAS,WAAM,OAAAtQ,EAAKyG,oBAAL,EACf8J,UAAW,SAAC9H,GAAO,OAAAzI,EAAK2E,UAAU8D,EAAf,EACnBD,QAAS,SAACC,GAAO,OAAAzI,EAAKwI,QAAQC,EAAb,EACjB+H,QAASvQ,KAAKkP,YAAYsB,KAAKxQ,SAInC2P,EAAA,YAAA/K,IAAA,2CACEiL,OAAKY,EAAA,GACHA,EAAC,kBAAmB,KACpBA,EAAC,yBACCzQ,KAAKmC,cACLnC,KAAKoC,cACLpC,KAAKqC,qBACLrC,KAAKsC,iBACLtC,KAAKuC,YACLvC,KAAKwC,YACLxC,KAAKyC,kBACLzC,KAAK0C,aACL1C,KAAK2C,aACL3C,KAAK4C,gBACL5C,KAAK6C,e,IAGT8M,EAAK,OAAA/K,IAAA,2CAAAiL,MAAM,oBACTF,EAAA,YAAA/K,IAAA,2CAAUsL,IAAK,SAAC/M,GAAO,OAAApD,EAAKmD,sBAAsBC,EAA3B,EAAgC0M,MAAM,eAAc,YAAW,QACpFF,EAAK,OAAA/K,IAAA,2CAAAL,QAAS,SAACiE,GAAO,OAAAzI,EAAKwJ,iBAAiBf,EAAtB,EAA2BuH,SAAS,IAAIF,MAAM,eACnE7P,KAAKmC,cACJwN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,SAAWwO,SAAS,cAC7Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKK,iBAAmB,QAAU,OAC3CuQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,IAAvB,EAA2B,YACrC,kBAAiB,aACf,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,YAIjDlC,KAAKoC,cACJuN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,WAAawO,SAAS,cAC/Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKM,mBAAqB,QAAU,OAC7CsQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,IAAvB,EAA2B,YACrC,oBAAmB,aACjB,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,cAIjDlC,KAAKqC,qBACJsN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,WAAawO,SAAS,cAC/Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKO,mBAAqB,QAAU,OAC7CqQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,SAAvB,EAAgC,YAC1C,2BAA0B,aACxB,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,cAIjDlC,KAAKsC,iBACJqN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,cAAgBwO,SAAS,cAClFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKQ,sBAAwB,QAAU,OAChDoQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,IAAvB,EAA2B,YACrC,uBAAsB,aACpB,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,iBAIjDlC,KAAKuC,YACJoN,EAAA,eAAA/K,IAAA,0DAA2B,GAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,SAAWwO,SAAS,cAC5E1Q,KAAKU,iBACJiP,EAAA,cACEgB,QAAQ,QACRC,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,IAAvB,EAA2B,YACrC,OACE,gBAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,WAG9CyN,EAAA,gBACEO,IAAK,SAAC/M,GAAO,OAAApD,EAAKsD,uBAAuBF,EAA5B,EACb4N,WAAW,QACXL,SAAS,eAETf,EAAK,OAAAqB,KAAK,sBACRrB,EAAA,cACEqB,KAAK,qBACLL,QAAQ,OACRC,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKwO,iBAAiB/F,EAAtB,EAAyB,YACnC,OACE,gBAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,YAGhDyN,EAAA,YAAUsB,QAAQ,OAAOC,WAAW,SAASC,IAAI,OAAOH,KAAK,oBAC3DrB,EAAA,aACEO,IAAKlQ,KAAKsD,gBACV8N,WAAY,SAAC5I,GAAO,OAAAzI,EAAK0O,aAAajG,EAAG4B,OAArB,EACpBhE,MAAO,CAAEiL,WAAY,SACrBjG,YAAY,sBACZkF,UAAW,SAAC9H,GAAO,OAAAzI,EAAK4O,kBAAkBnG,EAAvB,EACnBuH,WAAUnD,EAAA5M,KAAKI,gBAAY,MAAAwM,SAAA,SAAAA,EAAE0E,MAAO,IAAM,OAE5C3B,EACE,cAAA4B,SAAUvR,KAAKW,wBAAuB,YAC5B,QACVmQ,WAAY,SAACtI,GAAO,OAAAzI,EAAK6O,WAAWpG,EAAhB,EACpBuH,WAAU1C,EAAArN,KAAKI,gBAAY,MAAAiN,SAAA,SAAAA,EAAEiE,MAAO,IAAM,KAAI,aAClC,GAAAzJ,OAAGzI,EAAcY,KAAKkC,SAAU,cAOvDlC,KAAKwC,YACJmN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,SAAWwO,SAAS,cAC7Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKS,iBAAmB,QAAU,OAC3CmQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKoK,cAAc3B,EAAI,OAAvB,EAA8B,YACxC,OAAM,aACJ,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,YAIjDlC,KAAKyC,kBACJkN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,eAAiBwO,SAAS,cACnFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKY,sBAAwB,QAAU,OAChDgQ,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKwN,UAAU/E,EAAI,OAAnB,EAA0B,YACpC,aAAY,aACV,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,kBAIjDlC,KAAKyC,kBACJkN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,iBAAmBwO,SAAS,cACrFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKa,wBAA0B,QAAU,OAClD+P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKwN,UAAU/E,EAAI,SAAnB,EAA4B,YACtC,eAAc,aACZ,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,oBAIjDlC,KAAKyC,kBACJkN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,gBAAkBwO,SAAS,cACpFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKc,uBAAyB,QAAU,OACjD8P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKwN,UAAU/E,EAAI,QAAnB,EAA2B,YACrC,cAAa,aACX,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,mBAIjDlC,KAAK0C,aACJiN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,mBAAqBwO,SAAS,cACvFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKe,0BAA4B,QAAU,OACpD6P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKmO,WAAW1F,EAAI,KAApB,EAAyB,YACnC,iBAAgB,aACd,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,sBAIjDlC,KAAK0C,aACJiN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,iBAAmBwO,SAAS,cACrFf,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKgB,wBAA0B,QAAU,OAClD4P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAKmO,WAAW1F,EAAI,KAApB,EAAyB,YACnC,eAAc,aACZ,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,oBAIjDlC,KAAK2C,aACJgN,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,UAAYwO,SAAS,cAC9Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKiB,kBAAoB,QAAU,OAC5C2P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,aAAvB,EAAoC,YAC9C,QAAO,aACL,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,aAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKkB,eAAiB,QAAU,OACzC0P,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKmB,eAAiB,QAAU,OACzCyP,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKoB,eAAiB,QAAU,OACzCwP,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKqB,eAAiB,QAAU,OACzCuP,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKsB,eAAiB,QAAU,OACzCsP,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK4C,gBACJ+M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,OAASwO,SAAS,cAC3Ef,EACE,cAAA/K,IAAA,2CAAA+L,QAAS3Q,KAAKuB,eAAiB,QAAU,OACzCqP,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK2N,cAAclF,EAAI,KAAvB,EAA4B,YACtC,MAAK,aACH,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,UAIjDlC,KAAK6C,gBACJ8M,EAA2B,eAAA/K,IAAA,6DAAAiD,OAAGzI,EAAcY,KAAKkC,SAAU,qBAAuBwO,SAAS,cACzFf,EAAA,cAAA/K,IAAA,2CACE+L,QAAQ,OACRC,MAAM,UACNC,KAAK,QACLC,WAAY,SAACtI,GAAO,OAAAzI,EAAK0P,gBAAgBjH,EAArB,EAAwB,YAClC,WAAU,aACR,GAAAX,OAAGzI,EAAcY,KAAKkC,SAAU,yBAKpDyN,EACE,cAAA/K,IAAA,2CAAA4M,GAAG,kBACHb,QAAS3Q,KAAKwB,sBAAwB,QAAU,OAChDqO,MAAM,aACNe,MAAM,UACNC,KAAK,QACLC,WAAY,WAAM,OAAA/Q,EAAK0D,iBAAL,EAAsB,YAEtCzD,KAAKgD,aAAe,MAChBhD,KAAKwB,sBACH,WACA,aACFxB,KAAKwB,sBACH,aACA,e,8uBA7oCD,I", "ignoreList": []}