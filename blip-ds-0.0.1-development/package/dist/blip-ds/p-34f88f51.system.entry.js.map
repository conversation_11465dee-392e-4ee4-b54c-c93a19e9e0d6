{"version": 3, "names": ["tabsCss", "Tabs", "exports", "class_1", "hostRef", "_this", "this", "SCROLL_BEHAVIOR", "align", "handleHeaderResize", "tabsHeaderChildElement", "offsetWidth", "scrollWidth", "updateButtonsVisibility", "isScrollable", "setLeftButtonVisibility", "setRightButtonVisibility", "handleScrollButtonClick", "direction", "scrollButtonClick", "emit", "prototype", "onScrollButtonClick", "event", "preventDefault", "options", "behavior", "top", "left", "detail", "distance", "_a", "getDistance", "scrollTo", "onSelectedTab", "handleButtonOverlay", "componentDidLoad", "getChildElements", "attachEvents", "handleActiveTab", "tabs", "Array", "from", "getElementsByTagName", "activeTab", "find", "tab", "active", "bdsTabInit", "group", "firstTab", "el", "querySelector", "leftButtonChildElement", "rightButtonChildElement", "window", "onresize", "onscroll", "clientWidth", "Math", "ceil", "scrollLeft", "style", "display", "header", "buttons", "for<PERSON>ach", "button", "isButtonOverlappingTab", "getAdjutScrollDistance", "tabRect", "getBoundingClientRect", "buttonRect", "elementIsOverlapping", "element", "overlaidElement", "elementStart", "x", "elementEnd", "width", "comparatorStart", "comparatorEnd", "id", "distanceDifference", "parseInt", "getComputedStyle", "marginRight", "parentElement", "render", "h", "Host", "key", "class", "_b", "concat", "icon", "size", "onClick", "variant"], "sources": ["src/components/tabs/tab (depreciated)/tabs.scss?tag=bds-tabs", "src/components/tabs/tab (depreciated)/tabs.tsx"], "sourcesContent": [".bds-tabs {\n  width: 100%;\n  display: flex;\n  z-index: 1100;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  flex-direction: row;\n  align-items: center;\n  height: 48px;\n  padding: 0 10px 0 10px;\n\n  &--center {\n    justify-content: center;\n  }\n\n  &--left {\n    justify-content: flex-start;\n  }\n\n  &--right {\n    justify-content: flex-end;\n  }\n\n  .bds-tabs__header {\n    display: flex;\n    flex-direction: row;\n    overflow: hidden;\n    align-items: stretch;\n    width: fit-content;\n  }\n\n  .bds-tabs__header-button-container {\n    padding: 0px;\n    min-width: 40px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { ScrollDirection, Display, Overflow } from './tabs-interface';\nimport { Component, Element, h, Host, Event, EventEmitter, Listen, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tabs',\n  styleUrl: 'tabs.scss',\n})\nexport class Tabs {\n  tabsHeaderChildElement: HTMLElement;\n  leftButtonChildElement: HTMLElement;\n  rightButtonChildElement: HTMLElement;\n\n  readonly SCROLL_BEHAVIOR = 'smooth';\n\n  @Element() el!: HTMLElement;\n\n  @Event() scrollButtonClick: EventEmitter<Overflow>;\n\n  @Event() bdsTabInit: EventEmitter;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  @Listen('scrollButtonClick')\n  onScrollButtonClick(event: CustomEvent<Overflow>) {\n    event.preventDefault();\n\n    const options: ScrollToOptions = {\n      behavior: this.SCROLL_BEHAVIOR,\n      top: 0,\n      left: event.detail.distance,\n    };\n    options.left ??= this.getDistance(options, event);\n    this.tabsHeaderChildElement.scrollTo(options);\n  }\n\n  @Listen('bdsTabChange', { target: 'body' })\n  onSelectedTab(event: CustomEvent) {\n    this.handleButtonOverlay(event.detail);\n  }\n\n  componentDidLoad() {\n    this.getChildElements();\n    this.attachEvents();\n    this.setLeftButtonVisibility(false);\n    this.setRightButtonVisibility(true);\n    this.handleActiveTab();\n  }\n\n  private handleActiveTab() {\n    const tabs = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab'));\n    const activeTab = tabs.find((tab) => tab.active);\n    if (activeTab) {\n      this.bdsTabInit.emit(activeTab.group);\n    } else {\n      const [firstTab] = tabs;\n      this.bdsTabInit.emit(firstTab.group);\n    }\n  }\n\n  private getChildElements() {\n    this.tabsHeaderChildElement = this.el.querySelector('.bds-tabs__header');\n    this.leftButtonChildElement = this.el.querySelector('#bds-tabs-button-left');\n    this.rightButtonChildElement = this.el.querySelector('#bds-tabs-button-right');\n  }\n\n  private attachEvents() {\n    window.onresize = this.handleHeaderResize;\n    this.tabsHeaderChildElement.onscroll = () =>\n      this.updateButtonsVisibility(this.tabsHeaderChildElement.scrollWidth > this.tabsHeaderChildElement.clientWidth);\n  }\n\n  private handleHeaderResize = () => {\n    if (this.tabsHeaderChildElement.offsetWidth < this.tabsHeaderChildElement.scrollWidth) {\n      this.updateButtonsVisibility(true);\n    } else {\n      this.updateButtonsVisibility(false);\n    }\n  };\n\n  private updateButtonsVisibility = (isScrollable: boolean) => {\n    this.setLeftButtonVisibility(isScrollable);\n    this.setRightButtonVisibility(isScrollable);\n  };\n\n  private handleScrollButtonClick = (direction: ScrollDirection) => {\n    this.scrollButtonClick.emit({ direction });\n  };\n\n  private setRightButtonVisibility(isScrollable: boolean) {\n    if (\n      isScrollable &&\n      this.tabsHeaderChildElement.scrollWidth >\n        Math.ceil(this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n    ) {\n      this.rightButtonChildElement.style.display = Display.BLOCK;\n    } else {\n      this.rightButtonChildElement.style.display = Display.NONE;\n    }\n  }\n\n  private setLeftButtonVisibility(isScrollable: boolean) {\n    this.leftButtonChildElement.style.display =\n      this.tabsHeaderChildElement.scrollLeft > 0 && isScrollable ? Display.BLOCK : Display.NONE;\n  }\n\n  private handleButtonOverlay(group: string) {\n    const tab = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab')).find((header) => {\n      return header.group == group;\n    });\n\n    const buttons = [this.leftButtonChildElement, this.rightButtonChildElement];\n    buttons.forEach((button) => {\n      if (this.isButtonOverlappingTab(button, tab)) {\n        const distance = this.getAdjutScrollDistance(button, tab);\n        this.scrollButtonClick.emit({ distance: distance });\n      }\n    });\n  }\n\n  private isButtonOverlappingTab(button: HTMLElement, tab: HTMLElement) {\n    const tabRect = tab.getBoundingClientRect();\n    const buttonRect = button.getBoundingClientRect();\n\n    return this.elementIsOverlapping(buttonRect, tabRect);\n  }\n\n  private elementIsOverlapping(element: DOMRect, overlaidElement: DOMRect): boolean {\n    const elementStart = element.x;\n    const elementEnd = element.x + element.width;\n\n    const comparatorStart = overlaidElement.x;\n    const comparatorEnd = overlaidElement.x + overlaidElement.width;\n\n    return (\n      (elementStart >= comparatorStart && elementStart <= comparatorEnd) ||\n      (elementEnd >= comparatorStart && elementEnd <= comparatorEnd)\n    );\n  }\n\n  private getAdjutScrollDistance(button: HTMLElement, tab: HTMLElement) {\n    const direction = button.id == 'bds-tabs-button-left' ? ScrollDirection.LEFT : ScrollDirection.RIGHT;\n\n    const distanceDifference = tab.clientWidth + parseInt(getComputedStyle(tab).marginRight) - button.offsetWidth;\n\n    if (direction == ScrollDirection.RIGHT) {\n      return tab.parentElement.scrollLeft + distanceDifference;\n    } else {\n      return tab.parentElement.scrollLeft - distanceDifference;\n    }\n  }\n\n  private getDistance(options: ScrollToOptions, event: CustomEvent<Overflow>): number {\n    return event.detail.direction == ScrollDirection.RIGHT\n      ? (options.left = this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n      : (options.left = this.tabsHeaderChildElement.scrollLeft - this.tabsHeaderChildElement.clientWidth);\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tabs': true,\n          [`bds-tabs--${this.align}`]: true,\n        }}\n      >\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-left\"\n            size=\"short\"\n            id=\"bds-tabs-button-left\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.LEFT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n\n        <div class=\"bds-tabs__header\">\n          <slot />\n        </div>\n\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-right\"\n            size=\"short\"\n            id=\"bds-tabs-button-right\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.RIGHT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAA,IAAMA,EAAU,uvB,ICQHC,EAAIC,EAAA,sBAJjB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,kGASWA,KAAeC,gBAAG,SAQnBD,KAAKE,MAAgC,SAmDrCF,KAAkBG,mBAAG,WAC3B,GAAIJ,EAAKK,uBAAuBC,YAAcN,EAAKK,uBAAuBE,YAAa,CACrFP,EAAKQ,wBAAwB,K,KACxB,CACLR,EAAKQ,wBAAwB,M,CAEjC,EAEQP,KAAAO,wBAA0B,SAACC,GACjCT,EAAKU,wBAAwBD,GAC7BT,EAAKW,yBAAyBF,EAChC,EAEQR,KAAAW,wBAA0B,SAACC,GACjCb,EAAKc,kBAAkBC,KAAK,CAAEF,UAASA,GACzC,CA2GD,CA1KCf,EAAAkB,UAAAC,oBAAA,SAAoBC,G,MAClBA,EAAMC,iBAEN,IAAMC,EAA2B,CAC/BC,SAAUpB,KAAKC,gBACfoB,IAAK,EACLC,KAAML,EAAMM,OAAOC,WAErBC,EAAAN,EAAQG,QAAR,MAAAG,SAAA,EAAAA,EAAAN,EAAQG,KAAStB,KAAK0B,YAAYP,EAASF,GAC3CjB,KAAKI,uBAAuBuB,SAASR,E,EAIvCtB,EAAAkB,UAAAa,cAAA,SAAcX,GACZjB,KAAK6B,oBAAoBZ,EAAMM,O,EAGjC1B,EAAAkB,UAAAe,iBAAA,WACE9B,KAAK+B,mBACL/B,KAAKgC,eACLhC,KAAKS,wBAAwB,OAC7BT,KAAKU,yBAAyB,MAC9BV,KAAKiC,iB,EAGCpC,EAAAkB,UAAAkB,gBAAA,WACN,IAAMC,EAAOC,MAAMC,KAAKpC,KAAKI,uBAAuBiC,qBAAqB,YACzE,IAAMC,EAAYJ,EAAKK,MAAK,SAACC,GAAQ,OAAAA,EAAIC,MAAJ,IACrC,GAAIH,EAAW,CACbtC,KAAK0C,WAAW5B,KAAKwB,EAAUK,M,KAC1B,CACE,IAAAC,EAAYV,EAAI,GACvBlC,KAAK0C,WAAW5B,KAAK8B,EAASD,M,GAI1B9C,EAAAkB,UAAAgB,iBAAA,WACN/B,KAAKI,uBAAyBJ,KAAK6C,GAAGC,cAAc,qBACpD9C,KAAK+C,uBAAyB/C,KAAK6C,GAAGC,cAAc,yBACpD9C,KAAKgD,wBAA0BhD,KAAK6C,GAAGC,cAAc,yB,EAG/CjD,EAAAkB,UAAAiB,aAAA,eAAAjC,EAAAC,KACNiD,OAAOC,SAAWlD,KAAKG,mBACvBH,KAAKI,uBAAuB+C,SAAW,WACrC,OAAApD,EAAKQ,wBAAwBR,EAAKK,uBAAuBE,YAAcP,EAAKK,uBAAuBgD,YAAnG,C,EAoBIvD,EAAAkB,UAAAL,yBAAA,SAAyBF,GAC/B,GACEA,GACAR,KAAKI,uBAAuBE,YAC1B+C,KAAKC,KAAKtD,KAAKI,uBAAuBmD,WAAavD,KAAKI,uBAAuBgD,aACjF,CACApD,KAAKgD,wBAAwBQ,MAAMC,QAAO,O,KACrC,CACLzD,KAAKgD,wBAAwBQ,MAAMC,QAAO,M,GAItC5D,EAAAkB,UAAAN,wBAAA,SAAwBD,GAC9BR,KAAK+C,uBAAuBS,MAAMC,QAChCzD,KAAKI,uBAAuBmD,WAAa,GAAK/C,EAA6B,c,EAGvEX,EAAAkB,UAAAc,oBAAA,SAAoBc,GAApB,IAAA5C,EAAAC,KACN,IAAMwC,EAAML,MAAMC,KAAKpC,KAAKI,uBAAuBiC,qBAAqB,YAAYE,MAAK,SAACmB,GACxF,OAAOA,EAAOf,OAASA,CACzB,IAEA,IAAMgB,EAAU,CAAC3D,KAAK+C,uBAAwB/C,KAAKgD,yBACnDW,EAAQC,SAAQ,SAACC,GACf,GAAI9D,EAAK+D,uBAAuBD,EAAQrB,GAAM,CAC5C,IAAMhB,EAAWzB,EAAKgE,uBAAuBF,EAAQrB,GACrDzC,EAAKc,kBAAkBC,KAAK,CAAEU,SAAUA,G,CAE5C,G,EAGM3B,EAAAkB,UAAA+C,uBAAA,SAAuBD,EAAqBrB,GAClD,IAAMwB,EAAUxB,EAAIyB,wBACpB,IAAMC,EAAaL,EAAOI,wBAE1B,OAAOjE,KAAKmE,qBAAqBD,EAAYF,E,EAGvCnE,EAAAkB,UAAAoD,qBAAA,SAAqBC,EAAkBC,GAC7C,IAAMC,EAAeF,EAAQG,EAC7B,IAAMC,EAAaJ,EAAQG,EAAIH,EAAQK,MAEvC,IAAMC,EAAkBL,EAAgBE,EACxC,IAAMI,EAAgBN,EAAgBE,EAAIF,EAAgBI,MAE1D,OACGH,GAAgBI,GAAmBJ,GAAgBK,GACnDH,GAAcE,GAAmBF,GAAcG,C,EAI5C9E,EAAAkB,UAAAgD,uBAAA,SAAuBF,EAAqBrB,GAClD,IAAM5B,EAAYiD,EAAOe,IAAM,uBAAwB,OAAuB,QAE9E,IAAMC,EAAqBrC,EAAIY,YAAc0B,SAASC,iBAAiBvC,GAAKwC,aAAenB,EAAOxD,YAElG,GAAIO,GAAkC,QAAE,CACtC,OAAO4B,EAAIyC,cAAc1B,WAAasB,C,KACjC,CACL,OAAOrC,EAAIyC,cAAc1B,WAAasB,C,GAIlChF,EAAAkB,UAAAW,YAAA,SAAYP,EAA0BF,GAC5C,OAAOA,EAAMM,OAAOX,WAAkC,QACjDO,EAAQG,KAAOtB,KAAKI,uBAAuBmD,WAAavD,KAAKI,uBAAuBgD,YACpFjC,EAAQG,KAAOtB,KAAKI,uBAAuBmD,WAAavD,KAAKI,uBAAuBgD,W,EAG3FvD,EAAAkB,UAAAmE,OAAA,W,MAAA,IAAAnF,EAAAC,KACE,OACEmF,EAACC,EAAI,CAAAC,IAAA,2CACHC,OAAKC,EAAA,CACH,WAAY,MACZA,EAAC,aAAAC,OAAaxF,KAAKE,QAAU,K,IAG/BiF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,qCACTH,EAAA,mBAAAE,IAAA,2CACEC,MAAM,0BACNG,KAAK,aACLC,KAAK,QACLd,GAAG,uBACHe,QAAS,WAAM,OAAA5F,EAAKY,wBAAuB,OAA5B,EACfiF,QAAQ,eAIZT,EAAK,OAAAE,IAAA,2CAAAC,MAAM,oBACTH,EAAA,QAAAE,IAAA,8CAGFF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,qCACTH,EAAA,mBAAAE,IAAA,2CACEC,MAAM,0BACNG,KAAK,cACLC,KAAK,QACLd,GAAG,wBACHe,QAAS,WAAM,OAAA5F,EAAKY,wBAAuB,QAA5B,EACfiF,QAAQ,e,uHApLH,I", "ignoreList": []}