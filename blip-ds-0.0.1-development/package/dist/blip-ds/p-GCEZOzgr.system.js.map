{"version": 3, "file": "p-GCEZOzgr.system.js", "sources": ["src/components/tabs/tab (depreciated)/tabs.scss?tag=bds-tabs", "src/components/tabs/tab (depreciated)/tabs.tsx"], "sourcesContent": [".bds-tabs {\n  width: 100%;\n  display: flex;\n  z-index: 1100;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  flex-direction: row;\n  align-items: center;\n  height: 48px;\n  padding: 0 10px 0 10px;\n\n  &--center {\n    justify-content: center;\n  }\n\n  &--left {\n    justify-content: flex-start;\n  }\n\n  &--right {\n    justify-content: flex-end;\n  }\n\n  .bds-tabs__header {\n    display: flex;\n    flex-direction: row;\n    overflow: hidden;\n    align-items: stretch;\n    width: fit-content;\n  }\n\n  .bds-tabs__header-button-container {\n    padding: 0px;\n    min-width: 40px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { ScrollDirection, Display, Overflow } from './tabs-interface';\nimport { Component, Element, h, Host, Event, EventEmitter, Listen, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tabs',\n  styleUrl: 'tabs.scss',\n})\nexport class Tabs {\n  tabsHeaderChildElement: HTMLElement;\n  leftButtonChildElement: HTMLElement;\n  rightButtonChildElement: HTMLElement;\n\n  readonly SCROLL_BEHAVIOR = 'smooth';\n\n  @Element() el!: HTMLElement;\n\n  @Event() scrollButtonClick: EventEmitter<Overflow>;\n\n  @Event() bdsTabInit: EventEmitter;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  @Listen('scrollButtonClick')\n  onScrollButtonClick(event: CustomEvent<Overflow>) {\n    event.preventDefault();\n\n    const options: ScrollToOptions = {\n      behavior: this.SCROLL_BEHAVIOR,\n      top: 0,\n      left: event.detail.distance,\n    };\n    options.left ??= this.getDistance(options, event);\n    this.tabsHeaderChildElement.scrollTo(options);\n  }\n\n  @Listen('bdsTabChange', { target: 'body' })\n  onSelectedTab(event: CustomEvent) {\n    this.handleButtonOverlay(event.detail);\n  }\n\n  componentDidLoad() {\n    this.getChildElements();\n    this.attachEvents();\n    this.setLeftButtonVisibility(false);\n    this.setRightButtonVisibility(true);\n    this.handleActiveTab();\n  }\n\n  private handleActiveTab() {\n    const tabs = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab'));\n    const activeTab = tabs.find((tab) => tab.active);\n    if (activeTab) {\n      this.bdsTabInit.emit(activeTab.group);\n    } else {\n      const [firstTab] = tabs;\n      this.bdsTabInit.emit(firstTab.group);\n    }\n  }\n\n  private getChildElements() {\n    this.tabsHeaderChildElement = this.el.querySelector('.bds-tabs__header');\n    this.leftButtonChildElement = this.el.querySelector('#bds-tabs-button-left');\n    this.rightButtonChildElement = this.el.querySelector('#bds-tabs-button-right');\n  }\n\n  private attachEvents() {\n    window.onresize = this.handleHeaderResize;\n    this.tabsHeaderChildElement.onscroll = () =>\n      this.updateButtonsVisibility(this.tabsHeaderChildElement.scrollWidth > this.tabsHeaderChildElement.clientWidth);\n  }\n\n  private handleHeaderResize = () => {\n    if (this.tabsHeaderChildElement.offsetWidth < this.tabsHeaderChildElement.scrollWidth) {\n      this.updateButtonsVisibility(true);\n    } else {\n      this.updateButtonsVisibility(false);\n    }\n  };\n\n  private updateButtonsVisibility = (isScrollable: boolean) => {\n    this.setLeftButtonVisibility(isScrollable);\n    this.setRightButtonVisibility(isScrollable);\n  };\n\n  private handleScrollButtonClick = (direction: ScrollDirection) => {\n    this.scrollButtonClick.emit({ direction });\n  };\n\n  private setRightButtonVisibility(isScrollable: boolean) {\n    if (\n      isScrollable &&\n      this.tabsHeaderChildElement.scrollWidth >\n        Math.ceil(this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n    ) {\n      this.rightButtonChildElement.style.display = Display.BLOCK;\n    } else {\n      this.rightButtonChildElement.style.display = Display.NONE;\n    }\n  }\n\n  private setLeftButtonVisibility(isScrollable: boolean) {\n    this.leftButtonChildElement.style.display =\n      this.tabsHeaderChildElement.scrollLeft > 0 && isScrollable ? Display.BLOCK : Display.NONE;\n  }\n\n  private handleButtonOverlay(group: string) {\n    const tab = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab')).find((header) => {\n      return header.group == group;\n    });\n\n    const buttons = [this.leftButtonChildElement, this.rightButtonChildElement];\n    buttons.forEach((button) => {\n      if (this.isButtonOverlappingTab(button, tab)) {\n        const distance = this.getAdjutScrollDistance(button, tab);\n        this.scrollButtonClick.emit({ distance: distance });\n      }\n    });\n  }\n\n  private isButtonOverlappingTab(button: HTMLElement, tab: HTMLElement) {\n    const tabRect = tab.getBoundingClientRect();\n    const buttonRect = button.getBoundingClientRect();\n\n    return this.elementIsOverlapping(buttonRect, tabRect);\n  }\n\n  private elementIsOverlapping(element: DOMRect, overlaidElement: DOMRect): boolean {\n    const elementStart = element.x;\n    const elementEnd = element.x + element.width;\n\n    const comparatorStart = overlaidElement.x;\n    const comparatorEnd = overlaidElement.x + overlaidElement.width;\n\n    return (\n      (elementStart >= comparatorStart && elementStart <= comparatorEnd) ||\n      (elementEnd >= comparatorStart && elementEnd <= comparatorEnd)\n    );\n  }\n\n  private getAdjutScrollDistance(button: HTMLElement, tab: HTMLElement) {\n    const direction = button.id == 'bds-tabs-button-left' ? ScrollDirection.LEFT : ScrollDirection.RIGHT;\n\n    const distanceDifference = tab.clientWidth + parseInt(getComputedStyle(tab).marginRight) - button.offsetWidth;\n\n    if (direction == ScrollDirection.RIGHT) {\n      return tab.parentElement.scrollLeft + distanceDifference;\n    } else {\n      return tab.parentElement.scrollLeft - distanceDifference;\n    }\n  }\n\n  private getDistance(options: ScrollToOptions, event: CustomEvent<Overflow>): number {\n    return event.detail.direction == ScrollDirection.RIGHT\n      ? (options.left = this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n      : (options.left = this.tabsHeaderChildElement.scrollLeft - this.tabsHeaderChildElement.clientWidth);\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tabs': true,\n          [`bds-tabs--${this.align}`]: true,\n        }}\n      >\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-left\"\n            size=\"short\"\n            id=\"bds-tabs-button-left\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.LEFT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n\n        <div class=\"bds-tabs__header\">\n          <slot />\n        </div>\n\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-right\"\n            size=\"short\"\n            id=\"bds-tabs-button-right\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.RIGHT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,OAAO,GAAG,svBAAsvB;;YCQzvB,IAAI,uBAAA,MAAA;MAJjB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MASW,QAAA,IAAe,CAAA,eAAA,GAAG,QAAQ;MAQ3B,QAAA,IAAK,CAAA,KAAA,GAAgC,QAAQ;MAmD7C,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAK;MAChC,YAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;MACrF,gBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;;uBAC7B;MACL,gBAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;;MAEvC,SAAC;MAEO,QAAA,IAAA,CAAA,uBAAuB,GAAG,CAAC,YAAqB,KAAI;MAC1D,YAAA,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;MAC1C,YAAA,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC;MAC7C,SAAC;MAEO,QAAA,IAAA,CAAA,uBAAuB,GAAG,CAAC,SAA0B,KAAI;kBAC/D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC;MAC5C,SAAC;MA2GF;MA1KC,IAAA,mBAAmB,CAAC,KAA4B,EAAA;;cAC9C,KAAK,CAAC,cAAc,EAAE;MAEtB,QAAA,MAAM,OAAO,GAAoB;kBAC/B,QAAQ,EAAE,IAAI,CAAC,eAAe;MAC9B,YAAA,GAAG,EAAE,CAAC;MACN,YAAA,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;eAC5B;MACD,QAAA,CAAA,EAAA,GAAA,OAAO,CAAC,IAAI,MAAZ,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,IAAA,OAAO,CAAC,IAAI,GAAK,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MAClD,QAAA,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,OAAO,CAAC;;MAI/C,IAAA,aAAa,CAAC,KAAkB,EAAA;MAC9B,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC;;UAGxC,gBAAgB,GAAA;cACd,IAAI,CAAC,gBAAgB,EAAE;cACvB,IAAI,CAAC,YAAY,EAAE;MACnB,QAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;MACnC,QAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;cACnC,IAAI,CAAC,eAAe,EAAE;;UAGhB,eAAe,GAAA;MACrB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;MACpF,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC;cAChD,IAAI,SAAS,EAAE;kBACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;mBAChC;MACL,YAAA,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI;kBACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;;;UAIhC,gBAAgB,GAAA;cACtB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC;cACxE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,uBAAuB,CAAC;cAC5E,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,wBAAwB,CAAC;;UAGxE,YAAY,GAAA;MAClB,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB;cACzC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,GAAG,MACrC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,sBAAsB,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;;MAoB3G,IAAA,wBAAwB,CAAC,YAAqB,EAAA;MACpD,QAAA,IACE,YAAY;kBACZ,IAAI,CAAC,sBAAsB,CAAC,WAAW;MACrC,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,EAC7F;kBACA,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,OAAO;;mBACrC;kBACL,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,OAAO;;;MAItC,IAAA,uBAAuB,CAAC,YAAqB,EAAA;MACnD,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO;kBACvC,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,GAAiB,OAAA;;MAGvE,IAAA,mBAAmB,CAAC,KAAa,EAAA;cACvC,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAI;MAClG,YAAA,OAAO,MAAM,CAAC,KAAK,IAAI,KAAK;MAC9B,SAAC,CAAC;cAEF,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3E,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;kBACzB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;sBAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAG,CAAC;sBACzD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;;MAEvD,SAAC,CAAC;;UAGI,sBAAsB,CAAC,MAAmB,EAAE,GAAgB,EAAA;MAClE,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,qBAAqB,EAAE;MAC3C,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,EAAE;cAEjD,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC;;UAG/C,oBAAoB,CAAC,OAAgB,EAAE,eAAwB,EAAA;MACrE,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC;cAC9B,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK;MAE5C,QAAA,MAAM,eAAe,GAAG,eAAe,CAAC,CAAC;cACzC,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK;cAE/D,QACE,CAAC,YAAY,IAAI,eAAe,IAAI,YAAY,IAAI,aAAa;mBAChE,UAAU,IAAI,eAAe,IAAI,UAAU,IAAI,aAAa,CAAC;;UAI1D,sBAAsB,CAAC,MAAmB,EAAE,GAAgB,EAAA;MAClE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,IAAI,sBAAsB,GAAE,MAAA,8BAAuB,OAAA;MAE9E,QAAA,MAAM,kBAAkB,GAAG,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,WAAW;MAE7G,QAAA,IAAI,SAAS,IAAyB,OAAA,8BAAE;MACtC,YAAA,OAAO,GAAG,CAAC,aAAa,CAAC,UAAU,GAAG,kBAAkB;;mBACnD;MACL,YAAA,OAAO,GAAG,CAAC,aAAa,CAAC,UAAU,GAAG,kBAAkB;;;UAIpD,WAAW,CAAC,OAAwB,EAAE,KAA4B,EAAA;cACxE,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,IAAyB,OAAA;MACpD,eAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW;MAClG,eAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;;UAGvG,MAAM,GAAA;cACJ,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;MACL,gBAAA,UAAU,EAAE,IAAI;MAChB,gBAAA,CAAC,aAAa,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;MAClC,aAAA,EAAA,EAED,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,mCAAmC,EAAA,EAC5C,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,yBAAyB,EAC/B,IAAI,EAAC,YAAY,EACjB,IAAI,EAAC,OAAO,EACZ,EAAE,EAAC,sBAAsB,EACzB,OAAO,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAA,MAAA,4BAAsB,EACjE,OAAO,EAAC,WAAW,GACF,CACf,EAEN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC3B,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,EAEN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,mCAAmC,EAAA,EAC5C,CAAA,CAAA,iBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,yBAAyB,EAC/B,IAAI,EAAC,aAAa,EAClB,IAAI,EAAC,OAAO,EACZ,EAAE,EAAC,uBAAuB,EAC1B,OAAO,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAA,OAAA,6BAAuB,EAClE,OAAO,EAAC,WAAW,EAAA,CACF,CACf,CACD;;;;;;;;;;;;"}