import{r as t,c as e,h as i,H as s,a}from"./p-C3J6Z5OX.js";const o=':host{display:block;width:100%}:host(.list_item_content){display:-ms-flexbox;display:flex;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.list_item{display:-ms-flexbox;display:flex;gap:16px;-ms-flex-align:center;align-items:center}.list_item_tall{padding:16px}.list_item_standard{padding:8px 16px}.list_item_short{padding:8px}.list_item .input_list{position:relative}.list_item .avatar-item{position:relative;display:block}.list_item .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.list_item .grow-up{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-slot{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;gap:8px}.list_item .content-item{position:relative;display:-ms-flexbox;display:flex;gap:2px;-ms-flex-direction:column;flex-direction:column}.list_item .content-item .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-item .subtitle-item{color:var(--color-content-default, rgb(40, 40, 40))}.list_item .content-area{position:relative;-ms-flex-positive:2;flex-grow:2}.list_item .content-area .internal-chips,.list_item .content-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;gap:8px}.list_item .action-area{position:relative}.list_item .action-area .internal-actions-buttons,.list_item .action-area ::slotted(*){display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;color:var(--color-content-default, rgb(40, 40, 40))}.list_item .icon-arrow{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.list_item .icon-arrow-active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.border_radius{border-radius:8px}.border_radius:before,.border_radius:after,.border_radius .active{border-radius:8px}.active{position:absolute;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08;inset:0}.clickable{position:relative;cursor:pointer;gap:8px}.clickable:before{content:"";position:absolute;inset:0}.clickable:hover:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.clickable:active:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}';const l=class{constructor(i){t(this,i);this.bdsChecked=e(this,"bdsChecked");this.bdsClickActionButtom=e(this,"bdsClickActionButtom");this.internalChips=[];this.internalActionsButtons=[];this.checked=false;this.typeList=null;this.avatarName=null;this.avatarThumbnail=null;this.icon=null;this.value=null;this.text=null;this.secondaryText=null;this.chips=[];this.actionsButtons=[];this.clickable=false;this.active=false;this.borderRadius=false;this.size="standard";this.dataTest=null;this.handler=()=>{this.typeList=="radio"?this.checked=true:this.checked=!this.checked};this.clickActionButtons=(t,e)=>{const i=e.composedPath()[0];this.bdsClickActionButtom.emit({value:this.value,icon:t,elementButton:i})}}componentWillLoad(){this.hasActionAreaSlot=!!this.hostElement.querySelector('[slot="action-area"]');this.hasContentAreaSlot=!!this.hostElement.querySelector('[slot="content-area"]');this.chipsChanged();this.actionsButtonsChanged()}checkedChanged(t){this.bdsChecked.emit({value:this.value,text:this.text,secondaryText:this.secondaryText,typeList:this.typeList,checked:t})}chipsChanged(){if(this.chips){if(typeof this.chips==="string"){this.internalChips=JSON.parse(this.chips)}else{this.internalChips=this.chips}}else{this.internalChips=[]}}actionsButtonsChanged(){if(this.actionsButtons){if(typeof this.actionsButtons==="string"){this.internalActionsButtons=JSON.parse(this.actionsButtons)}else{this.internalActionsButtons=this.actionsButtons}}else{this.internalActionsButtons=[]}}renderChips(){if(!this.internalChips.length){return[]}return this.internalChips.map(((t,e)=>{const s=e.toString();const a=30;if(t.length<=a){return i("bds-chip-clickable",{id:s,key:s,color:"default"},t)}else{return i("bds-tooltip",{key:s,position:"top-center","tooltip-text":t},i("bds-chip-clickable",{id:s,key:s,color:"default"},`${t.slice(0,a)} ...`))}}))}renderActionsButtons(){if(!this.internalActionsButtons.length){return[]}return this.internalActionsButtons.map(((t,e)=>{const s=e.toString();return i("bds-button-icon",{key:s,variant:"secondary",icon:t,size:"short",onClick:e=>this.clickActionButtons(t,e)})}))}render(){const t=this.clickable==true||this.typeList=="checkbox"||this.typeList=="radio"||this.typeList=="switch";const e=this.typeList=="checkbox"||this.typeList=="radio";const a=this.avatarName||this.avatarThumbnail;return i(s,{key:"c6f535bacd09a7380a8bfed5aa43cafd6e77480e"},i("div",{key:"6069a62d8aaf8da2a44087c543e7bf03945d94b3",onClick:this.handler,tabindex:"0",class:{list_item:true,clickable:t,border_radius:this.borderRadius,[`list_item_${this.size}`]:true},"data-test":this.dataTest},this.active&&i("div",{key:"1c9877baeb95d6d0b442ffef23a3036b0aaf6f05",class:"active"}),e&&i("div",{key:"cb8d6e06b0461667d8d833de543a354a2695790b",class:{input_list:true}},this.typeList=="radio"&&i("bds-radio",{key:"d2bb56ce9d7368ebf2042bd120a46897032d918d",value:this.value,checked:this.checked}),this.typeList=="checkbox"&&i("bds-checkbox",{key:"d2b1192fe200be921f27b13ff404b77636c248b7",refer:"",label:"",name:"cb1",disabled:false,checked:this.checked})),a?i("bds-avatar",{class:"avatar-item",name:this.avatarName,thumbnail:this.avatarThumbnail,size:"extra-small"}):this.icon&&i("bds-icon",{class:{[`icon-item`]:true,[`icon-item-active`]:this.active},size:"medium",name:this.icon,color:"inherit",theme:this.active?"solid":"outline"}),i("div",{key:"552b5e8b789d876a36152ffe08d3345a00432964",class:{[`content-slot`]:true}},i("slot",{key:"9d29123adda77805387e2cec7c48a8f8420f629d"})),(this.text||this.secondaryText)&&i("div",{key:"13e2dcacf1d732f6fe7ceec0dd35b573e1d516e6",class:{[`content-item`]:true,[`grow-up`]:!this.hasActionAreaSlot&&!this.hasContentAreaSlot&&this.internalChips.length<0}},this.text&&i("bds-typo",{key:"d9a70e2a6f86587228999282950c22ffd783d8dd",class:"title-item",variant:"fs-16",tag:"span",bold:this.active?"bold":"regular"},this.text),this.secondaryText&&i("bds-typo",{key:"64f9a2d8603a984f2e21f6e5fc2bb4cd0dac5bdd",class:"subtitle-item",variant:"fs-12","line-height":"small",tag:"span"},this.secondaryText)),i("div",{key:"8fd955b1692070969a655b9b915d603037fc7b76",class:{[`content-area`]:true,[`grow-up`]:true}},this.internalChips.length>0&&i("div",{key:"f4d5a1b4f3853bca8be36ada97194c9c5cb0d5ff",class:"internal-chips"},this.renderChips()),i("slot",{key:"f354984737785227a7a6616ccb14a36cdb019573",name:"content-area"})),(!this.typeList||this.typeList=="default")&&i("div",{key:"7ad2aab79dc28f5bb3be6a2bc191f6d23363069c",class:{[`action-area`]:true}},this.internalActionsButtons.length>0&&i("div",{key:"fb7e71327931ebfd91f089d2f75d50b468cd0d05",class:"internal-actions-buttons"},this.renderActionsButtons()),i("slot",{key:"dee52851703625af2e149d5e40a5eecfe1a2194c",name:"action-area"})),this.typeList=="switch"&&i("bds-switch",{key:"c4aee037502773767d1ccad20a9331a18232860f",refer:"",name:"",checked:this.checked})))}get hostElement(){return a(this)}static get watchers(){return{checked:["checkedChanged"],chips:["chipsChanged"],actionsButtons:["actionsButtonsChanged"]}}};l.style=o;export{l as bds_list_item};
//# sourceMappingURL=p-2c6a6497.entry.js.map