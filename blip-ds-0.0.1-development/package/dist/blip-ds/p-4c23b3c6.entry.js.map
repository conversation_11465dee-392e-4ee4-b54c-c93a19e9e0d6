{"version": 3, "names": ["menuSeparationCss", "BdsMenuSeparation", "constructor", "hostRef", "this", "value", "size", "render", "h", "key", "class", "menuseparation", "variant", "tag"], "sources": ["src/components/menu/menu-separation/menu-separation.scss?tag=bds-menu-separation&encapsulation=shadow", "src/components/menu/menu-separation/menu-separation.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuseparation {\n  display: flex;\n  align-items: center;\n  padding: 0 16px;\n\n  &__small {\n    margin: 8px 0;\n  }\n  &__default {\n    margin: 12px 0;\n  }\n  &__large {\n    margin: 16px 0;\n  }\n\n  & .dividor-item{\n    height: 1px;\n    width: 100%;\n    background-color: $color-neutral-medium-wave;\n  }\n\n  & .title-item{\n    margin-right: 8px;\n    margin-top: -4px;\n    color: $color-neutral-medium-elephant;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\n\nexport type divisorSize = 'small' | 'default' | 'large';\n\n@Component({\n  tag: 'bds-menu-separation',\n  styleUrl: 'menu-separation.scss',\n  shadow: true,\n})\nexport class BdsMenuSeparation {\n  /**\n   * Value. Used to insert a title to the divider.\n   */\n  @Prop() value?: string = null;\n  /**\n   * Size. Used to set the size of the divider.\n   */\n  @Prop() size?: string = null;\n  render() {\n    return (\n      <div\n        class={{\n          menuseparation: true,\n          [`menuseparation__${this.size}`]: true,\n        }}\n      >\n        {this.value && (\n          <bds-typo class=\"title-item\" variant=\"fs-10\" tag=\"span\">\n            {this.value}\n          </bds-typo>\n        )}\n        <div class=\"dividor-item\"></div>\n      </div>\n    );\n  }\n}\n"], "mappings": "2CAAA,MAAMA,EAAoB,oX,MCSbC,EAAiB,MAL9B,WAAAC,CAAAC,G,UASUC,KAAKC,MAAY,KAIjBD,KAAIE,KAAY,IAkBzB,CAjBC,MAAAC,GACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACLC,eAAgB,KAChB,CAAC,mBAAmBP,KAAKE,QAAS,OAGnCF,KAAKC,OACJG,EAAA,YAAAC,IAAA,2CAAUC,MAAM,aAAaE,QAAQ,QAAQC,IAAI,QAC9CT,KAAKC,OAGVG,EAAA,OAAAC,IAAA,2CAAKC,MAAM,iB", "ignoreList": []}