{"version": 3, "file": "bds-carousel.entry.esm.js", "sources": ["src/components/carousel/carousel.scss?tag=bds-carousel&encapsulation=shadow", "src/components/carousel/carousel.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  position: relative;\n}\n\n.carousel {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  max-width: 1920px;\n  position: relative;\n\n  &_slide {\n    width: 100%;\n    position: relative;\n    box-sizing: border-box;\n    padding: 0 48px;\n\n    &::after {\n      content: '';\n      position: absolute;\n      inset: -8px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n      pointer-events: none;\n    }\n\n    &:focus-visible {\n      outline: none;\n      &::after {\n        border-color: $color-focus;\n      }\n    }\n\n    &_fullwidth {\n      padding: 0;\n    }\n\n    &_frame {\n      width: 100%;\n      display: flex;\n      overflow: hidden;\n      -webkit-transition: height ease-in-out 0.5s;\n      -moz-transition: height ease-in-out 0.5s;\n      transition: height ease-in-out 0.5s;\n\n      &_loading {\n        opacity: 0;\n        pointer-events: none;\n      }\n\n      & * {\n        -webkit-user-select: none; /* Safari */\n        -ms-user-select: none; /* IE 10 and IE 11 */\n        user-select: none; /* Standard syntax */\n        -webkit-user-drag: none;\n        -khtml-user-drag: none;\n        -moz-user-drag: none;\n        -o-user-drag: none;\n      }\n\n      & *[slot='loop'] {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n\n      &_repeater {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n    }\n    &_loading {\n      opacity: 0;\n      pointer-events: none;\n      position: absolute;\n      inset: 0;\n\n      &_visible {\n        opacity: 1;\n        pointer-events: all;\n      }\n    }\n  }\n  &_loading_bar {\n    box-sizing: border-box;\n    padding: 0 60px;\n    margin-top: 8px;\n\n    &_fullwidth {\n      padding: 0 4px;\n    }\n  }\n\n  &_buttons {\n    position: absolute;\n    width: 100%;\n    height: 0px;\n    top: calc(50% - 20px);\n    left: 0;\n    display: flex;\n    justify-content: space-between;\n    box-sizing: border-box;\n\n    &_fullwidth {\n      padding: 0 8px;\n    }\n  }\n\n  &_bullets {\n    position: relative;\n    margin-top: 8px;\n\n    &_inside {\n      position: absolute;\n      bottom: 0px;\n      width: 100%;\n      margin: 0;\n      padding: 0px 16px;\n      box-sizing: border-box;\n    }\n\n    &_card {\n      width: fit-content;\n      display: inline-flex;\n      gap: 8px;\n\n      &_inside {\n        border-top-left-radius: 8px;\n        border-top-right-radius: 8px;\n        padding: 8px;\n        background-color: $color-surface-0;\n      }\n    }\n\n    &_item {\n      width: 16px;\n      height: 16px;\n      border: 2px solid $color-border-1;\n      border-radius: 50%;\n      position: relative;\n      transform: rotate(45deg);\n      cursor: pointer;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: 4px;\n        border-radius: 50%;\n      }\n\n      &::after {\n        content: '';\n        position: absolute;\n        inset: -8px;\n        transform: rotate(-45deg);\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n        &::after {\n          border-color: $color-focus;\n        }\n      }\n\n      &_active {\n        &::before {\n          background-color: $color-primary;\n        }\n      }\n\n      &_conclude {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-content-disable;\n      }\n\n      &_loader {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-primary;\n        animation: l18 linear;\n      }\n    }\n  }\n}\n\n@keyframes l18 {\n  0% {\n    clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);\n  }\n  25% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);\n  }\n  50% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);\n  }\n  75% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);\n  }\n  100% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);\n  }\n}\n", "import { Component, h, Element, State, Watch, Prop, Method, Event, EventEmitter } from '@stencil/core';\nimport { Itens, arrows, bullets, bulletsPositions, gap } from './carousel-interface';\nimport { gapChanged, getHighestItem, getItems } from '../../utils/position-element';\n\n@Component({\n  tag: 'bds-carousel',\n  styleUrl: 'carousel.scss',\n  shadow: true,\n})\nexport class BdsCarousel {\n  private itemsElement?: HTMLCollectionOf<HTMLBdsCarouselItemElement> = null;\n  private bulletElement?: HTMLElement = null;\n  private bulletElements: HTMLElement[] = [];\n  private frame?: HTMLElement;\n  private themeProviderArrows?: any;\n  private frameRepeater?: HTMLElement;\n  private incrementSeconds?: any;\n\n  @Element() element: HTMLElement;\n\n  @State() itemActivated = 1;\n  @State() seconds = 0;\n  @State() internalItens: Itens[];\n  @State() isWhole = 0;\n  @State() heightCarousel?: number = 240;\n  @State() framePressed?: boolean = false;\n  @State() startX?: number;\n  @State() endX?: number;\n  @State() autoplayState: 'paused' | 'running' = 'running';\n\n  /**\n   * Autoplay. Prop to Enable component autoplay.\n   */\n  @Prop() autoplay?: boolean = false;\n\n  /**\n   * AutoplayTimeout. Prop to Choose the Autoplay time in milliseconds, ex: 5000.\n   */\n  @Prop() autoplayTimeout?: number = 5000;\n\n  /**\n   * AutoplayHoverPause. Prop to Enable it if you will have the function to pause autoplay when on hover.\n   */\n  @Prop() autoplayHoverPause?: boolean = false;\n\n  /**\n   * autoHeight. Prop to Enable it if you want the component to adjust its height relative to the active items..\n   */\n  @Prop() autoHeight?: boolean = false;\n\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bullets?: boolean | bullets = 'outside';\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bulletsPosition?: bulletsPositions = 'center';\n\n  /**\n   * InfiniteLoop. Prop to Enable if the component will have infinite loop.\n   */\n  @Prop() infiniteLoop?: boolean = false;\n\n  /**\n   * arrows. Prop to select type of arrows in component. Are available \"outside\" | \"inside\" | \"none\".\n   */\n  @Prop() arrows?: arrows = 'outside';\n\n  /**\n   * SlidePerPage. Prop to Choose the number of slide per page you will have available in the carousel.\n   */\n  @Prop() slidePerPage?: number = 1;\n\n  /**\n   * Gap. Prop to Select the gap distance between items.\n   */\n  @Prop() gap?: gap = 'none';\n\n  /**\n   * Grab. Prop to enable function of grab in carousel.\n   */\n  @Prop() grab?: boolean = true;\n\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop({ mutable: true, reflect: true }) loading?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtSlideContent is the data-test to slide action.\n   */\n  @Prop() dtSlideContent?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  @State() secondsLimit: number = this.autoplayTimeout / 1000;\n\n  /**\n   * Emitted when active frame value.\n   */\n  @Event() bdsChangeCarousel!: EventEmitter;\n\n  componentWillLoad() {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.setInternalItens(Array.from(this.itemsElement));\n    if (this.bullets == true) {\n      this.bullets = 'outside';\n    }\n    if (this.bullets == false) {\n      this.bullets = 'none';\n    }\n  }\n\n  componentDidRender() {\n    if (!this.loading) {\n      if (this.gap != 'none') {\n        this.frame.style.width = `calc(100% + ${gapChanged(this.gap)}px)`;\n        this.frame.style.marginLeft = `-${gapChanged(this.gap) / 2}px`;\n      }\n      for (let i = 0; i < this.itemsElement.length; i++) {\n        const widthFrame = this.frame.offsetWidth >= 1920 ? 1920 : this.frame.offsetWidth;\n        this.itemsElement[i].style.width = `${widthFrame / this.slidePerPage}px`;\n        this.itemsElement[i].style.padding = `0 ${gapChanged(this.gap) / 2}px`;\n      }\n      if (this.autoHeight) this.updateHeight(Array.from(this.itemsElement));\n    }\n    if (this.arrows == 'inside') {\n      const firstItemActived = (this.itemActivated - 1) * (this.itemsElement.length / this.internalItens.length) + 1;\n      this.themeProviderArrows.theme =\n        this.slidePerPage <= 1\n          ? this.itemsElement[this.itemActivated - 1].theme\n          : this.itemsElement[Math.round(firstItemActived)].theme;\n    }\n  }\n\n  componentDidLoad() {\n    this.startCountSeconds();\n  }\n\n  @Watch('itemActivated')\n  protected itemActivatedChanged(): void {\n    const currentItemSelected: Itens = this.internalItens.find((item) => item.id === this.itemActivated);\n    const slideFrame = !this.frame ? 0 : this.frame.offsetWidth * (this.itemActivated - 1);\n    if (this.frameRepeater) {\n      if (currentItemSelected.isWhole) {\n        const isWholeWidth = this.itemsElement[1].offsetWidth * (this.slidePerPage - this.isWhole);\n        this.frameRepeater.style.right = `${slideFrame - isWholeWidth}px`;\n      } else {\n        this.frameRepeater.style.right = `${slideFrame}px`;\n      }\n    }\n    this.bdsChangeCarousel.emit({ value: currentItemSelected });\n  }\n\n  @Watch('autoplayTimeout')\n  protected autoplayTimeoutChanged(): void {\n    this.secondsLimit = this.autoplayTimeout / 1000;\n  }\n\n  @Watch('seconds')\n  protected secondsChanged(): void {\n    if (this.seconds >= this.secondsLimit) {\n      this.nextSlide();\n      this.seconds = 0;\n    }\n  }\n\n  @Watch('isWhole')\n  protected isWholeChanged(): void {\n    if (this.internalItens != undefined) {\n      if (this.isWhole > 0) {\n        const newItem = {\n          id: this.internalItens?.length + 1,\n          label: `Frame - ${this.internalItens?.length + 1}`,\n          isWhole: true,\n        };\n        this.internalItens = [...this.internalItens, newItem];\n      }\n    }\n  }\n\n  private setInternalItens = (ItensElement) => {\n    const floor = Math.floor(ItensElement.length / this.slidePerPage);\n    const numberOfColumns = ItensElement.length / this.slidePerPage;\n    const newItens = getItems(numberOfColumns);\n    this.internalItens = newItens;\n    this.isWhole = ItensElement.length - this.slidePerPage * floor;\n  };\n\n  private startCountSeconds = () => {\n    if (this.autoplay) {\n      this.incrementSeconds = setInterval(() => {\n        this.seconds += 0.1;\n      }, 100);\n    }\n  };\n\n  private updateHeight = (itemsElement) => {\n    const elementActive = itemsElement[this.itemActivated * this.slidePerPage - this.slidePerPage];\n    let heightFrame = 240;\n    if (this.slidePerPage > 1) {\n      const getVisibleItens =\n        this.isWhole > 0 && this.itemActivated == this.internalItens.length\n          ? itemsElement.slice(\n              this.internalItens.length - this.internalItens.length - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            )\n          : itemsElement.slice(\n              this.itemActivated * this.slidePerPage - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            );\n\n      heightFrame = getHighestItem(getVisibleItens)[0];\n    } else {\n      heightFrame = elementActive.offsetHeight;\n    }\n    this.frame.style.height = `${heightFrame}px`;\n  };\n\n  @Method()\n  async buildCarousel(): Promise<void> {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.loading = true;\n    setTimeout(\n      () => (this.setInternalItens(Array.from(this.itemsElement)), (this.loading = false), this.setActivated(1)),\n      1000,\n    );\n  }\n\n  @Method()\n  async nextSlide(): Promise<void> {\n    if (this.itemActivated == this.internalItens.length) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = 1;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated + 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async prevSlide(): Promise<void> {\n    if (this.itemActivated == 1) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = this.internalItens.length;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated - 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async setActivated(item: number): Promise<void> {\n    this.itemActivated = item;\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  @Method()\n  async pauseAutoplay(): Promise<void> {\n    clearInterval(this.incrementSeconds);\n    this.autoplayState = 'paused';\n  }\n\n  @Method()\n  async runAutoplay(): Promise<void> {\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  private refFrame = (el: HTMLElement): void => {\n    this.frame = el;\n  };\n\n  private refThemeProviderArrows = (el: HTMLBdsThemeProviderElement | HTMLElement): void => {\n    this.themeProviderArrows = el;\n  };\n\n  private refFrameRepeater = (el: HTMLElement): void => {\n    this.frameRepeater = el;\n  };\n\n  private refBulletElement = (el: HTMLElement): void => {\n    if (el) {\n      this.bulletElement = el; // Keep the current behavior\n      this.bulletElements.push(el); // Store all bullet elements\n    }\n  };\n\n  private onMouseOver = () => {\n    if (this.autoplayHoverPause) {\n      this.pauseAutoplay();\n    }\n  };\n\n  private onMouseOut = () => {\n    if (this.autoplayHoverPause) {\n      this.runAutoplay();\n    }\n  };\n\n  private onMouseDown = (ev: MouseEvent) => {\n    if (this.grab) {\n      this.framePressed = true;\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n      this.startX = ev.pageX - offsetFrame;\n      this.endX = ev.pageX - offsetFrame;\n      this.frame.style.cursor = 'grabbing';\n    }\n  };\n\n  private onMouseEnter = () => {\n    if (this.grab) {\n      this.frame.style.cursor = 'grab';\n    }\n  };\n\n  private onMouseUp = () => {\n    if (this.grab) {\n      this.framePressed = false;\n      this.frame.style.cursor = 'grab';\n      this.boundItems();\n      if (this.autoplayHoverPause) {\n        this.pauseAutoplay();\n      }\n    }\n  };\n\n  private onMouseMove = (ev: MouseEvent) => {\n    if (this.grab) {\n      if (!this.framePressed) return;\n      ev.preventDefault();\n\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n\n      this.endX = ev.pageX - offsetFrame;\n    }\n  };\n\n  private boundItems = () => {\n    if (this.endX < this.startX) {\n      this.nextSlide();\n      this.seconds = 0;\n    } else if (this.endX > this.startX) {\n      this.prevSlide();\n      this.seconds = 0;\n    }\n  };\n\n  private setKeydownNavigation = (ev) => {\n    if (ev.key === 'Tab') {\n      if (this.bulletElements.length > 0) {\n        this.bulletElements[0].focus();\n      } else if (this.bulletElement) {\n        this.bulletElement.focus();\n      }\n    }\n    if (ev.key === 'ArrowRight') {\n      this.nextSlide();\n    }\n    if (ev.key === 'ArrowLeft') {\n      this.prevSlide();\n    }\n  };\n\n  render() {\n    // Reset bullet elements array at start of render\n    this.bulletElements = [];\n    \n    const ThemeOrDivArrows = this.arrows == 'inside' ? 'bds-theme-provider' : 'div';\n    const justifybulletsPosition =\n      this.bulletsPosition == 'center'\n        ? 'center'\n        : this.bulletsPosition == 'right'\n          ? 'flex-end'\n          : this.bulletsPosition == 'left' && 'flex-start';\n    return (\n      <div class={{ carousel: true }}>\n        <div\n          class={{\n            carousel_slide: true,\n            carousel_slide_fullwidth: this.arrows != 'outside',\n            [`carousel_slide_state_${this.autoplayState}`]: this.autoplay,\n          }}\n          tabindex=\"0\"\n          onKeyDown={(ev) => this.setKeydownNavigation(ev)}\n          data-test={this.dtSlideContent}\n        >\n          <div\n            ref={(el) => this.refFrame(el)}\n            class={{ carousel_slide_frame: true, carousel_slide_frame_loading: this.loading }}\n            onMouseOver={() => this.onMouseOver()}\n            onMouseOut={() => this.onMouseOut()}\n            onMouseDown={(ev) => this.onMouseDown(ev)}\n            onMouseEnter={() => this.onMouseEnter()}\n            onMouseUp={() => this.onMouseUp()}\n            onMouseMove={(ev) => this.onMouseMove(ev)}\n          >\n            <div ref={(el) => this.refFrameRepeater(el)} class={{ carousel_slide_frame_repeater: true }}>\n              <slot />\n            </div>\n          </div>\n          <bds-grid class={{ carousel_slide_loading: true, carousel_slide_loading_visible: this.loading }}>\n            <bds-skeleton height=\"100%\" shape=\"square\" width=\"100%\" />\n          </bds-grid>\n          {this.arrows != 'none' && !this.loading && (\n            <ThemeOrDivArrows\n              ref={(el) => this.refThemeProviderArrows(el)}\n              class={{\n                carousel_buttons: true,\n                carousel_buttons_fullwidth: this.arrows != 'outside',\n              }}\n            >\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-left\"\n                color=\"content\"\n                onBdsClick={() => this.prevSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated <= 1}\n                dataTest={this.dtButtonPrev}\n              ></bds-button>\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-right\"\n                color=\"content\"\n                onBdsClick={() => this.nextSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated >= this.internalItens.length}\n                dataTest={this.dtButtonNext}\n              ></bds-button>\n            </ThemeOrDivArrows>\n          )}\n        </div>\n        {this.internalItens.length > 1 && this.bullets != 'none' && (\n          <div\n            class={{\n              carousel_bullets: true,\n              carousel_bullets_inside: this.bullets == 'inside',\n            }}\n          >\n            {this.loading && this.bullets != 'inside' ? (\n              <bds-grid\n                xxs=\"12\"\n                gap=\"1\"\n                justify-content={justifybulletsPosition}\n                padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n              >\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n              </bds-grid>\n            ) : (\n              this.internalItens && (\n                <bds-grid\n                  xxs=\"12\"\n                  justify-content={justifybulletsPosition}\n                  padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n                >\n                  <div\n                    class={{\n                      carousel_bullets_card: true,\n                      carousel_bullets_card_inside: this.bullets == 'inside',\n                    }}\n                  >\n                    {this.internalItens.map((item, index) => (\n                      <div\n                        key={index}\n                        ref={(el) => this.refBulletElement(el)}\n                        class={{\n                          carousel_bullets_item: true,\n                          carousel_bullets_item_active: item.id == this.itemActivated,\n                        }}\n                        tabindex=\"0\"\n                        onClick={() => this.setActivated(item.id)}\n                      >\n                        {item.id < this.itemActivated && this.autoplay && (\n                          <div class={{ carousel_bullets_item_conclude: true }}></div>\n                        )}\n                        {item.id == this.itemActivated && this.autoplay && (\n                          <div\n                            class={{ carousel_bullets_item_loader: true }}\n                            style={{\n                              animationDuration: `${this.autoplayTimeout / 1000 - 0.1}s`,\n                              animationPlayState: this.autoplayState,\n                            }}\n                          ></div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </bds-grid>\n              )\n            )}\n          </div>\n        )}\n        <slot name=\"after\"></slot>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,GAAG,ugKAAugK;;MCS9gK,WAAW,GAAA,MAAA;AALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAMU,QAAA,IAAY,CAAA,YAAA,GAAkD,IAAI;AAClE,QAAA,IAAa,CAAA,aAAA,GAAiB,IAAI;AAClC,QAAA,IAAc,CAAA,cAAA,GAAkB,EAAE;AAQjC,QAAA,IAAa,CAAA,aAAA,GAAG,CAAC;AACjB,QAAA,IAAO,CAAA,OAAA,GAAG,CAAC;AAEX,QAAA,IAAO,CAAA,OAAA,GAAG,CAAC;AACX,QAAA,IAAc,CAAA,cAAA,GAAY,GAAG;AAC7B,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;AAG9B,QAAA,IAAa,CAAA,aAAA,GAAyB,SAAS;AAExD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAElC;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AAEvC;;AAEG;AACK,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;AAE5C;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;AAEpC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAuB,SAAS;AAC/C;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAsB,QAAQ;AAErD;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;AAEtC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAY,SAAS;AAEnC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,CAAC;AAEjC;;AAEG;AACK,QAAA,IAAG,CAAA,GAAA,GAAS,MAAM;AAE1B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAa,IAAI;AAE7B;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjE;;;AAGG;AACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;AAEtC;;;AAGG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AAEpC;;;AAGG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;QAE3B,IAAA,CAAA,YAAY,GAAW,IAAI,CAAC,eAAe,GAAG,IAAI;AAwFnD,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,YAAY,KAAI;AAC1C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YACjE,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;AAC/D,YAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,CAAC;AAC1C,YAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;AAC7B,YAAA,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK;AAChE,SAAC;AAEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,MAAK;AAC/B,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAK;AACvC,oBAAA,IAAI,CAAC,OAAO,IAAI,GAAG;iBACpB,EAAE,GAAG,CAAC;;AAEX,SAAC;AAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,YAAY,KAAI;AACtC,YAAA,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YAC9F,IAAI,WAAW,GAAG,GAAG;AACrB,YAAA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;AACzB,gBAAA,MAAM,eAAe,GACnB,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;AAC3D,sBAAE,YAAY,CAAC,KAAK,CAChB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EACzE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;sBAExC,YAAY,CAAC,KAAK,CAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,EAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CACvC;gBAEP,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;iBAC3C;AACL,gBAAA,WAAW,GAAG,aAAa,CAAC,YAAY;;YAE1C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,EAAG,WAAW,CAAA,EAAA,CAAI;AAC9C,SAAC;AAmEO,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,EAAe,KAAU;AAC3C,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACjB,SAAC;AAEO,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,EAA6C,KAAU;AACvF,YAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE;AAC/B,SAAC;AAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;AACnD,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACzB,SAAC;AAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;YACnD,IAAI,EAAE,EAAE;AACN,gBAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;gBACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;AAEjC,SAAC;AAEO,QAAA,IAAW,CAAA,WAAA,GAAG,MAAK;AACzB,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,IAAI,CAAC,aAAa,EAAE;;AAExB,SAAC;AAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAK;AACxB,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,IAAI,CAAC,WAAW,EAAE;;AAEtB,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAc,KAAI;AACvC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;gBACpC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU;;AAExC,SAAC;AAEO,QAAA,IAAY,CAAA,YAAA,GAAG,MAAK;AAC1B,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;;AAEpC,SAAC;AAEO,QAAA,IAAS,CAAA,SAAA,GAAG,MAAK;AACvB,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK;gBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;gBAChC,IAAI,CAAC,UAAU,EAAE;AACjB,gBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC3B,IAAI,CAAC,aAAa,EAAE;;;AAG1B,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAc,KAAI;AACvC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,YAAY;oBAAE;gBACxB,EAAE,CAAC,cAAc,EAAE;AAEnB,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;gBAEnE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;;AAEtC,SAAC;AAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAK;YACxB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;gBAC3B,IAAI,CAAC,SAAS,EAAE;AAChB,gBAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;iBACX,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,SAAS,EAAE;AAChB,gBAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;AAEpB,SAAC;AAEO,QAAA,IAAA,CAAA,oBAAoB,GAAG,CAAC,EAAE,KAAI;AACpC,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,EAAE;gBACpB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;AACzB,qBAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AAC7B,oBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAG9B,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,YAAY,EAAE;gBAC3B,IAAI,CAAC,SAAS,EAAE;;AAElB,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,WAAW,EAAE;gBAC1B,IAAI,CAAC,SAAS,EAAE;;AAEpB,SAAC;AAuIF;IA5ZC,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACnD,mBAAmB,CAC4B;AACjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpD,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,GAAG,SAAS;;AAE1B,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM;;;IAIzB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,IAAI,CAAC,GAAG,IAAI,MAAM,EAAE;AACtB,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAe,YAAA,EAAA,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;AACjE,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,CAAA,CAAA,EAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;;AAEhE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;AACjF,gBAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,IAAI;gBACxE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;;YAExE,IAAI,IAAI,CAAC,UAAU;AAAE,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;AAEvE,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;YAC3B,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;YAC9G,IAAI,CAAC,mBAAmB,CAAC,KAAK;gBAC5B,IAAI,CAAC,YAAY,IAAI;AACnB,sBAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AAC5C,sBAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK;;;IAI/D,gBAAgB,GAAA;QACd,IAAI,CAAC,iBAAiB,EAAE;;IAIhB,oBAAoB,GAAA;QAC5B,MAAM,mBAAmB,GAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;QACpG,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AACtF,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,mBAAmB,CAAC,OAAO,EAAE;gBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;AAC1F,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,UAAU,GAAG,YAAY,CAAA,EAAA,CAAI;;iBAC5D;gBACL,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,UAAU,CAAA,EAAA,CAAI;;;QAGtD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;;IAInD,sBAAsB,GAAA;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI;;IAIvC,cAAc,GAAA;QACtB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;;IAKV,cAAc,GAAA;;AACtB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;AACnC,YAAA,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;AACpB,gBAAA,MAAM,OAAO,GAAG;AACd,oBAAA,EAAE,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,IAAG,CAAC;AAClC,oBAAA,KAAK,EAAE,CAAA,QAAA,EAAW,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,CAAE,CAAA;AAClD,oBAAA,OAAO,EAAE,IAAI;iBACd;gBACD,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;;;;AA4C3D,IAAA,MAAM,aAAa,GAAA;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACnD,mBAAmB,CAC4B;AACjD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,QAAA,UAAU,CACR,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAC1G,IAAI,CACL;;AAIH,IAAA,MAAM,SAAS,GAAA;QACb,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACnD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AACtC,gBAAA,IAAI,CAAC,aAAa,GAAG,CAAC;;iBACjB;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;;aAEpC;YACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;;AAE7C,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,iBAAiB,EAAE;;AAI1B,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;YAC3B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACtC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM;;iBACzC;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;;aAEpC;YACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;;AAE7C,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,iBAAiB,EAAE;;IAI1B,MAAM,YAAY,CAAC,IAAY,EAAA;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;AAIhC,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;;AAI/B,IAAA,MAAM,WAAW,GAAA;QACf,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;IAkGhC,MAAM,GAAA;;AAEJ,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AAExB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,oBAAoB,GAAG,KAAK;AAC/E,QAAA,MAAM,sBAAsB,GAC1B,IAAI,CAAC,eAAe,IAAI;AACtB,cAAE;AACF,cAAE,IAAI,CAAC,eAAe,IAAI;AACxB,kBAAE;kBACA,IAAI,CAAC,eAAe,IAAI,MAAM,IAAI,YAAY;AACtD,QAAA,QACE,4DAAK,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAA,EAC5B,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,wBAAwB,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;gBAClD,CAAC,CAAA,qBAAA,EAAwB,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,QAAQ;AAC9D,aAAA,EACD,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,EACrC,WAAA,EAAA,IAAI,CAAC,cAAc,EAAA,EAE9B,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAC9B,KAAK,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,OAAO,EAAE,EACjF,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,EACrC,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,EACnC,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACzC,YAAY,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,EACvC,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EACjC,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA,EAEzC,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,6BAA6B,EAAE,IAAI,EAAE,EAAA,EACzF,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,CACF,EACN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,8BAA8B,EAAE,IAAI,CAAC,OAAO,EAAE,EAAA,EAC7F,CAAA,CAAA,cAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAc,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAA,CAAG,CACjD,EACV,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,KACrC,CAAA,CAAC,gBAAgB,EAAA,EAAA,GAAA,EAAA,0CAAA,EACf,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAC5C,KAAK,EAAE;AACL,gBAAA,gBAAgB,EAAE,IAAI;AACtB,gBAAA,0BAA0B,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;AACrD,aAAA,EAAA,EAED,CAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAC,MAAM,EACd,QAAQ,EAAC,YAAY,EACrB,KAAK,EAAC,SAAS,EACf,UAAU,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAClC,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EACvD,QAAQ,EAAE,IAAI,CAAC,YAAY,EACf,CAAA,EACd,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,MAAM,EACd,QAAQ,EAAC,aAAa,EACtB,KAAK,EAAC,SAAS,EACf,UAAU,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAClC,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAC/E,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAA,CACf,CACG,CACpB,CACG,EACL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,KACtD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,gBAAgB,EAAE,IAAI;AACtB,gBAAA,uBAAuB,EAAE,IAAI,CAAC,OAAO,IAAI,QAAQ;AAClD,aAAA,EAAA,EAEA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,IACvC,gBACE,GAAG,EAAC,IAAI,EACR,GAAG,EAAC,GAAG,qBACU,sBAAsB,EACvC,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,EAAA,EAEnD,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,EAC1D,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,EAC1D,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,CACjD,KAEX,IAAI,CAAC,aAAa,KAChB,CACE,CAAA,UAAA,EAAA,EAAA,GAAG,EAAC,IAAI,EAAA,iBAAA,EACS,sBAAsB,EACvC,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,EAAA,EAEnD,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,qBAAqB,EAAE,IAAI;AAC3B,gBAAA,4BAA4B,EAAE,IAAI,CAAC,OAAO,IAAI,QAAQ;AACvD,aAAA,EAAA,EAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,MAClC,WACE,GAAG,EAAE,KAAK,EACV,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EACtC,KAAK,EAAE;AACL,gBAAA,qBAAqB,EAAE,IAAI;AAC3B,gBAAA,4BAA4B,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa;AAC5D,aAAA,EACD,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA,EAExC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,KAC5C,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,EAAE,8BAA8B,EAAE,IAAI,EAAE,EAAA,CAAQ,CAC7D,EACA,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,KAC7C,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,4BAA4B,EAAE,IAAI,EAAE,EAC7C,KAAK,EAAE;gBACL,iBAAiB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAG,CAAA,CAAA;gBAC1D,kBAAkB,EAAE,IAAI,CAAC,aAAa;AACvC,aAAA,EACI,CAAA,CACR,CACG,CACP,CAAC,CACE,CACG,CACZ,CACF,CACG,CACP,EACD,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,OAAO,EAAQ,CAAA,CACtB;;;;;;;;;;;;;;"}