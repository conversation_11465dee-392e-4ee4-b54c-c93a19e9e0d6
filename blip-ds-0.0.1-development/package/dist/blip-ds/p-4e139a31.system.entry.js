System.register(["./p-B47mPBRA.system.js"],(function(a){"use strict";var t,r,i,e;return{setters:[function(a){t=a.r;r=a.c;i=a.h;e=a.H}],execute:function(){var s=".host{position:relative;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content}.avatar__group{display:-ms-flexbox;display:flex}.avatar__group>*:nth-child(1){z-index:1}.avatar__group>*:nth-child(2){z-index:2}.avatar__group>*:nth-child(3){z-index:3}.avatar__group>*:nth-child(4){z-index:4}.avatar__group>*:nth-child(5){z-index:5}.avatar__group>*:nth-child(6){z-index:6;width:auto}.avatar__group>*:nth-child(6) div{background-color:#292929;padding:0 16px;width:auto}.avatar__group__click--true{cursor:pointer}.avatar__group .avatar{position:relative}.avatar__group__size--extra-small{margin-left:8px}.avatar__group__size--extra-small>*{margin-left:-8px}.avatar__group__size--extra-small>*:nth-child(6) div{padding:0 8px}.avatar__group__size--extra-small .avatar{height:32px}.avatar__group__size--small{margin-left:8px}.avatar__group__size--small>*{margin-left:-8px}.avatar__group__size--small .avatar{height:40px}.avatar__group__size--standard{margin-left:16px}.avatar__group__size--standard>*{margin-left:-16px}.avatar__group__size--standard .avatar{height:56px}.avatar__group__size--large{margin-left:16px}.avatar__group__size--large>*{margin-left:-16px}.avatar__group__size--large .avatar{height:64px}.avatar__group__size--extra-large{margin-left:16px}.avatar__group__size--extra-large>*{margin-left:-16px}.avatar__group__size--extra-large .avatar{height:72px}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}";var _=a("bds_avatar_group",function(){function a(a){t(this,a);this.bdsClickAvatarGroup=r(this,"bdsClickAvatarGroup");this.size="standard";this.avatarBgColor=function(a){var t=["system","success","warning","error","info"];return t[a]}}a.prototype.handleClickGroup=function(a){a.preventDefault();this.bdsClickAvatarGroup.emit(a)};a.prototype.handleClickKey=function(a){if((a.key==="Enter"||a.key===" ")&&this.canClick){a.preventDefault();this.bdsClickAvatarGroup.emit()}};a.prototype.parseUsers=function(){if(this.users){try{this.internalUsers=typeof this.users==="string"?JSON.parse(this.users):this.users}catch(a){this.internalUsers=[]}}};a.prototype.componentWillLoad=function(){this.users&&this.parseUsers();this.leftoversUsers=this.internalUsers.length-5};a.prototype.render=function(){var a;var t=this;return i(e,{key:"31b6ae002cf06ab2e205c4658d5af725f8fd3409",class:"host"},i("div",{key:"e22014bf3f77fc82b4ee3c5517ea7d74b1be8d76",class:(a={avatar__group:true},a["avatar__group__size--".concat(this.size)]=true,a["avatar__group__click--".concat(this.canClick)]=true,a),tabindex:"0",onClick:function(a){return t.handleClickGroup(a)}},this.internalUsers?this.internalUsers.slice(0,6).map((function(a,r,e){return r+1===e.length&&t.internalUsers.length>5?i("bds-avatar",{key:r,name:a.name,color:"surface",size:t.size,ellipsis:t.leftoversUsers}):i("bds-avatar",{key:r,id:a.id,name:a.name,thumbnail:a.thumbnail,color:t.avatarBgColor(r),size:t.size})})):i("slot",null)),this.canClick?i("div",{class:"focus",tabindex:"0",onClick:function(){return t.handleClickKey}}):"")};return a}());_.style=s}}}));
//# sourceMappingURL=p-4e139a31.system.entry.js.map