import{r as a,h as e,H as s}from"./p-C3J6Z5OX.js";const t='.bds-tab-panel{display:none;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:1rem;font-style:normal;font-weight:normal}.bds-tab-panel--selected{display:block}';const n=class{constructor(e){a(this,e);this.isActive=false}handleTabChange(a){this.isActive=a.detail==this.group}render(){return e(s,{key:"2cb4066270fc5ca03707d1e9e81da301d9ce46d6",class:{"bds-tab-panel":true,["bds-tab-panel--selected"]:this.isActive}},e("slot",{key:"1dfd4ccfb46ae9e1ab2fbda3b18241884b842ef3"}))}};n.style=t;export{n as bds_tab_panel};
//# sourceMappingURL=p-3427de64.entry.js.map