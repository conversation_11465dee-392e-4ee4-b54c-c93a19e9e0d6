{"version": 3, "file": "p-DLraUrU1.system.js", "sources": ["src/utils/validations.ts"], "sourcesContent": ["const emailRegex = /^\\w+([.+,-]\\w+)*@\\w+([.-]\\w+)*\\.\\w{2,}$/;\nconst whitespaaceRegex = /\\S/;\nconst phoneNumberRegex = /^(\\(?\\+?[0-9]*\\)?)?[0-9_\\- \\(\\)]*$/;\n// const numberRegex = /^[0-9]*$/;\nconst dateRegex = /^((0?[1-9]|[12][0-9]|3[01])[- /.](0?[1-9]|1[012])[- /.](19|20|21)?[0-9]{2})*$/;\n\nexport const emailValidation = (term: string): boolean => {\n  if (term && !emailRegex.test(term)) {\n    return true;\n  }\n};\n\nexport const numberValidation = (term: string): boolean => {\n  if (term && !phoneNumberRegex.test(term)) {\n    return true;\n  }\n};\n\nexport const whitespaceValidation = (term: string): boolean => {\n  return whitespaaceRegex.test(term);\n};\n\nexport const dateValidation = (term: string): boolean => {\n  return dateRegex.test(term);\n};\n\nexport const maskDate = (term: string): string => {\n  let value = term;\n  value = value.replace(/\\D+/g, '');\n  value = value.replace(/^(\\d{2})(\\d)/, '$1/$2');\n  value = value.replace(/^(\\d{2}\\/\\d{2})(\\d)/, '$1/$2');\n\n  return value;\n};\n"], "names": [], "mappings": ";;;;;YAAA,MAAM,UAAU,GAAG,yCAAyC;YAC5D,MAAM,gBAAgB,GAAG,IAAI;YAC7B,MAAM,gBAAgB,GAAG,oCAAoC;YAC7D;YACA,MAAM,SAAS,GAAG,+EAA+E;AAEpF,kBAAA,eAAe,gBAAG,CAAC,IAAY,KAAa;gBACvD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAClC,QAAA,OAAO,IAAI;;YAEf;AAEa,kBAAA,gBAAgB,gBAAG,CAAC,IAAY,KAAa;gBACxD,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACxC,QAAA,OAAO,IAAI;;YAEf;AAEa,kBAAA,oBAAoB,gBAAG,CAAC,IAAY,KAAa;YAC5D,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;YACpC;AAEa,kBAAA,cAAc,gBAAG,CAAC,IAAY,KAAa;YACtD,IAAA,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7B;;;;;;;;"}