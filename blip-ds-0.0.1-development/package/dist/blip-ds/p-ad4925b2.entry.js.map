{"version": 3, "names": ["modalCloseButtonCss", "BdsModalCloseButton", "constructor", "hostRef", "this", "active", "render", "h", "key", "class", "size", "name"], "sources": ["src/components/modal/modal-close-button/modal-close-button.scss?tag=bds-modal-close-button&encapsulation=shadow", "src/components/modal/modal-close-button/modal-close-button.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.modal__close__button-icon {\n  opacity: 0;\n  visibility: hidden;\n  color: $color-content-default;\n  display: flex;\n  justify-content: flex-end;\n  padding-bottom: 16px;\n\n  &--active {\n    opacity: 1;\n    visibility: visible;\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-close-button',\n  styleUrl: 'modal-close-button.scss',\n  shadow: true,\n})\nexport class BdsModalCloseButton implements ComponentInterface {\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public active?: boolean = true;\n\n  render() {\n    return (\n      <div\n        class={{\n          'modal__close__button-icon': true,\n          'modal__close__button-icon--active': this.active,\n        }}\n      >\n        <bds-icon size=\"medium\" name=\"close\"></bds-icon>\n      </div>\n    );\n  }\n}\n"], "mappings": "2CAAA,MAAMA,EAAsB,8Q,MCOfC,EAAmB,MALhC,WAAAC,CAAAC,G,UAaSC,KAAMC,OAAa,IAc3B,CAZC,MAAAC,GACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACL,4BAA6B,KAC7B,oCAAqCL,KAAKC,SAG5CE,EAAU,YAAAC,IAAA,2CAAAE,KAAK,SAASC,KAAK,U", "ignoreList": []}