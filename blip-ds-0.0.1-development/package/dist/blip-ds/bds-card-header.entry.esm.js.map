{"version": 3, "file": "bds-card-header.entry.esm.js", "sources": ["src/components/card/card-header/card-header.scss?tag=bds-card-header&encapsulation=shadow", "src/components/card/card-header/card-header.tsx"], "sourcesContent": [":host {\n    width: 100%;\n}", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n\n@Component({\n  tag: 'bds-card-header',\n  styleUrl: 'card-header.scss',\n  shadow: true,\n})\nexport class CardHeader implements ComponentInterface {\n  /**\n   * Prop for internal elements alignment. Will follow the same values of css.\n   */\n  @Prop() align?: justifyContent = 'space-between';\n\n  render() {\n    return (\n      <bds-grid xxs=\"12\" direction=\"row\" gap=\"1\" justifyContent={this.align} alignItems=\"center\">\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,aAAa,GAAG,mBAAmB;;MCS5B,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAoB,eAAe;AASjD;IAPC,MAAM,GAAA;QACJ,QACE,iEAAU,GAAG,EAAC,IAAI,EAAC,SAAS,EAAC,KAAK,EAAC,GAAG,EAAC,GAAG,EAAC,cAAc,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAC,QAAQ,EAAA,EACxF,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACC;;;;;;;"}