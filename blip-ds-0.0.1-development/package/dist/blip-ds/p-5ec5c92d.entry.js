import{r as t,c as a,h as e}from"./p-C3J6Z5OX.js";const i=".alert__dialog{opacity:0;visibility:hidden;background-color:rgba(0, 0, 0, 0.7);width:100%;height:100%;position:fixed;top:0;left:0;-webkit-transition:opacity 0.3s ease-in-out;transition:opacity 0.3s ease-in-out;z-index:80000}.alert__dialog .alert{position:relative;margin:48px auto 0;overflow:hidden;max-width:424px;border-radius:8px;background:var(--color-surface-1, rgb(246, 246, 246));-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16))}.alert__dialog--open{opacity:1;visibility:visible}.alert__dialog--fixed{position:fixed}.alert__dialog--contain{position:absolute}";const s=class{constructor(e){t(this,e);this.bdsAlertChanged=a(this,"bdsAlertChanged");this.open=false;this.dataTest=null;this.position="fixed";this.listener=t=>{if(t.key=="Enter"||t.key=="Escape"){this.toggle()}}}async toggle(){this.open=!this.open;if(this.open){this.bdsAlertChanged.emit({alertStatus:"opened"})}else{this.bdsAlertChanged.emit({alertStatus:"closed"})}}isOpenChanged(){if(this.open){document.addEventListener("keydown",this.listener,false)}else document.removeEventListener("keydown",this.listener,false)}render(){return e("div",{key:"ac8c0b4e12840f60698d573b221ec689645bdd11",class:{alert__dialog:true,"alert__dialog--open":this.open,[`alert__dialog--${this.position}`]:true}},e("div",{key:"1130ed827571cfad2827e36f73cb43436d3a48f6",class:"alert","data-test":this.dataTest},e("slot",{key:"828e303a6465c71579e6ff2082c7a899669d43d8"})))}static get watchers(){return{open:["isOpenChanged"]}}};s.style=i;export{s as bds_alert};
//# sourceMappingURL=p-5ec5c92d.entry.js.map