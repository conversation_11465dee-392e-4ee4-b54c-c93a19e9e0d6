{"version": 3, "names": ["modalActionCss", "BdsModalAction", "render", "h", "key", "class"], "sources": ["src/components/modal/modal-action/modal-action.scss?tag=bds-modal-action&encapsulation=shadow", "src/components/modal/modal-action/modal-action.tsx"], "sourcesContent": [".modal__action {\n  display: flex;\n  padding-top: 16px;\n  bottom: 32px;\n  right: 32px;\n\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-action',\n  styleUrl: 'modal-action.scss',\n  shadow: true,\n})\nexport class BdsModalAction implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"modal__action\">\n        <slot />\n      </div>\n    );\n  }\n}\n"], "mappings": "2CAAA,MAAMA,EAAiB,2F,MCOVC,EAAc,M,yBACzB,MAAAC,GACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,iBACTF,EAAQ,QAAAC,IAAA,6C", "ignoreList": []}