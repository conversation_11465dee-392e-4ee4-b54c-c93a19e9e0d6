{"version": 3, "names": ["RadioGroup", "constructor", "hostRef", "this", "radioGroupElement", "chagedOptions", "value", "event", "detail", "checked", "valueChanged", "setSelectedRadio", "bdsRadioGroupChange", "emit", "componentWillRender", "element", "getElementsByTagName", "i", "length", "addEventListener", "radios", "getValue", "render", "h", "Host", "key"], "sources": ["src/components/radio-button/radio-group.tsx"], "sourcesContent": ["import { Component, h, Host, Element, Prop, Watch, Event, EventEmitter, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-radio-group',\n  scoped: true,\n})\nexport class RadioGroup implements ComponentInterface {\n  private radioGroupElement?: HTMLCollectionOf<HTMLBdsRadioElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n  /**\n   * Emitted when the value has changed due to a click event.\n   */\n  @Event() bdsRadioGroupChange: EventEmitter;\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n\n    this.bdsRadioGroupChange.emit({ value });\n  }\n\n  componentWillRender() {\n    this.radioGroupElement = this.element.getElementsByTagName('bds-radio') as HTMLCollectionOf<HTMLBdsRadioElement>;\n    for (let i = 0; i < this.radioGroupElement.length; i++) {\n      this.radioGroupElement[i].addEventListener('bdsChange', (event: CustomEvent) =>\n        this.chagedOptions(this.radioGroupElement[i].value, event),\n      );\n    }\n  }\n\n  private chagedOptions = (value: string, event: CustomEvent): void => {\n    if (event.detail.checked == true) {\n      this.value = value;\n    }\n  };\n\n  private setSelectedRadio(value: string) {\n    const radios = this.radioGroupElement;\n    for (let i = 0; i < radios.length; i++) {\n      const getValue = radios[i].value;\n      radios[i].checked = false;\n      if (radios[i].checked == false && value == getValue) {\n        radios[i].checked = true;\n      }\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "mappings": "sEAMaA,EAAU,MAJvB,WAAAC,CAAAC,G,iEAKUC,KAAiBC,kBAA2C,KA6B5DD,KAAAE,cAAgB,CAACC,EAAeC,KACtC,GAAIA,EAAMC,OAAOC,SAAW,KAAM,CAChCN,KAAKG,MAAQA,C,EAsBlB,CAvCC,YAAAI,CAAaJ,GACXH,KAAKQ,iBAAiBL,GAEtBH,KAAKS,oBAAoBC,KAAK,CAAEP,S,CAGlC,mBAAAQ,GACEX,KAAKC,kBAAoBD,KAAKY,QAAQC,qBAAqB,aAC3D,IAAK,IAAIC,EAAI,EAAGA,EAAId,KAAKC,kBAAkBc,OAAQD,IAAK,CACtDd,KAAKC,kBAAkBa,GAAGE,iBAAiB,aAAcZ,GACvDJ,KAAKE,cAAcF,KAAKC,kBAAkBa,GAAGX,MAAOC,I,EAWlD,gBAAAI,CAAiBL,GACvB,MAAMc,EAASjB,KAAKC,kBACpB,IAAK,IAAIa,EAAI,EAAGA,EAAIG,EAAOF,OAAQD,IAAK,CACtC,MAAMI,EAAWD,EAAOH,GAAGX,MAC3Bc,EAAOH,GAAGR,QAAU,MACpB,GAAIW,EAAOH,GAAGR,SAAW,OAASH,GAASe,EAAU,CACnDD,EAAOH,GAAGR,QAAU,I,GAK1B,MAAAa,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAa,QAAAE,IAAA,6C", "ignoreList": []}