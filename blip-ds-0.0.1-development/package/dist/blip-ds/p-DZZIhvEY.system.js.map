{"version": 3, "file": "p-DZZIhvEY.system.js", "sources": ["src/components/button/button-group.scss?tag=bds-button-group&encapsulation=shadow", "src/components/button/button-group.tsx"], "sourcesContent": [":host {\n    width: fit-content;\n}", "import { Component, h, Element, State, Event, EventEmitter, Prop, Host, Watch, Method } from '@stencil/core';\nimport { direction } from '../grid/grid-interface';\nimport { ButtonSize } from './button';\n\ninterface HTMLBdsButtonElement extends HTMLElement {\n  setVariant(variant: string): void;\n  setColor(color: string): void;\n  setSize(size: string): void;\n  setDirection(direction: string): void;\n  isActive(active: boolean): void;\n  setPosition(position: string): void;\n}\n\n@Component({\n  tag: 'bds-button-group',\n  styleUrl: 'button-group.scss',\n  shadow: true,\n})\nexport class ButtonGroup {\n  @Element() el!: HTMLElement;\n\n  @State() activeIndexes: Set<number> = new Set();\n\n  /**\n   * Size of the buttons. Can be one of:\n   * 'medium', 'large'.\n   */\n  @Prop({ mutable: true }) size?: ButtonSize = 'medium';\n\n  /**\n   * Direction of the button group layout. Can be one of:\n   * 'row', 'column'.\n   */\n  @Prop({ mutable: true }) direction?: direction = 'row';\n\n  /**\n   * Color scheme for the buttons. Default is 'primary'.\n   */\n  @Prop({ mutable: true }) color?: string = 'primary';\n\n  /**\n   * Allows multiple buttons to be selected simultaneously if true.\n   */\n  @Prop({ mutable: true }) multiple? = false;\n\n  @Event() buttonSelected: EventEmitter;\n\n  private buttons: HTMLCollectionOf<HTMLBdsButtonElement>;\n\n  componentDidLoad() {\n    this.buttons = this.el.getElementsByTagName('bds-button') as HTMLCollectionOf<HTMLBdsButtonElement>;\n    this.setupButtons();\n  }\n\n  componentDidUpdate() {\n    this.setupButtons();\n  }\n\n  @Watch('size')\n  @Watch('direction')\n  @Watch('color')\n  @Watch('multiple')\n  handlePropChanges() {\n    // Re-setup buttons when props change\n    this.setupButtons();\n  }\n\n  setupButtons() {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      button.setAttribute('data-index', i.toString());\n      button.addEventListener('click', () => this.selectButton(i));\n      button.setVariant('outline');\n      this.updateButtonPosition(i);\n      this.updateButtonDirection(i);\n      this.updateButtonSize(i);\n      this.updateButtonColor(i);\n    }\n  }\n\n  @Method()\n  async activateButton(index: number) {\n    if (index >= 0 && index < this.buttons.length) {\n      this.selectButton(index);\n    }\n  }\n\n  selectButton(index: number) {\n    if (this.multiple) {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.delete(index);\n      } else {\n        this.activeIndexes.add(index);\n      }\n    } else {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.clear();\n      } else {\n        this.activeIndexes.clear();\n        this.activeIndexes.add(index);\n      }\n    }\n    this.updateButtonStates(index);\n  }\n\n  updateButtonStates(clickedIndex: number) {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      if (this.activeIndexes.has(i)) {\n        button.isActive(true);\n        button.setVariant('solid');\n        button.classList.add('active');\n      } else {\n        button.isActive(false);\n        button.setVariant('outline');\n        button.classList.remove('active');\n      }\n      if (i === clickedIndex) {\n        this.buttonSelected.emit(button.id);\n      }\n    }\n  }\n\n  updateButtonPosition(index: number) {\n    const button = this.buttons[index];\n    if (index === 0) {\n      button.setPosition('first');\n    } else if (index === this.buttons.length - 1) {\n      button.setPosition('last');\n    } else {\n      button.setPosition('middle');\n    }\n  }\n\n  updateButtonDirection(index: number) {\n    const button = this.buttons[index];\n    this.direction === 'row' ? button.setDirection('row') : button.setDirection('column');\n  }\n\n  updateButtonSize(index: number) {\n    const button = this.buttons[index];\n    this.size === 'medium' ? button.setSize('medium') : button.setSize('large');\n  }\n\n  updateButtonColor(index: number) {\n    const button = this.buttons[index];\n    button.setColor(this.color);\n  }\n\n  render() {\n    return (\n      <Host class=\"button_group\">\n        <bds-grid direction={this.direction}>\n          <slot></slot>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;YAAA,MAAM,cAAc,GAAG,2EAA2E;;kBCkBrF,WAAW,+BAAA,MAAA;YALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;YAQW,QAAA,IAAA,CAAA,aAAa,GAAgB,IAAI,GAAG,EAAE;YAE/C;;;YAGG;YACsB,QAAA,IAAI,CAAA,IAAA,GAAgB,QAAQ;YAErD;;;YAGG;YACsB,QAAA,IAAS,CAAA,SAAA,GAAe,KAAK;YAEtD;;YAEG;YACsB,QAAA,IAAK,CAAA,KAAA,GAAY,SAAS;YAEnD;;YAEG;YACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;YAmH3C;gBA7GC,gBAAgB,GAAA;oBACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,oBAAoB,CAAC,YAAY,CAA2C;oBACnG,IAAI,CAAC,YAAY,EAAE;;gBAGrB,kBAAkB,GAAA;oBAChB,IAAI,CAAC,YAAY,EAAE;;gBAOrB,iBAAiB,GAAA;;oBAEf,IAAI,CAAC,YAAY,EAAE;;gBAGrB,YAAY,GAAA;YACV,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;wBAC9B,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC/C,YAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5D,YAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YAC5B,YAAA,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAC5B,YAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAC7B,YAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACxB,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;gBAK7B,MAAM,cAAc,CAAC,KAAa,EAAA;YAChC,QAAA,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC7C,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;;YAI5B,IAAA,YAAY,CAAC,KAAa,EAAA;YACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACjC,gBAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC;;6BAC3B;YACL,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;;;yBAE1B;wBACL,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACjC,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;6BACrB;YACL,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;YAC1B,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;;;YAGjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;;YAGhC,IAAA,kBAAkB,CAAC,YAAoB,EAAA;YACrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;wBAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC7B,gBAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;YACrB,gBAAA,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,gBAAA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC;;6BACzB;YACL,gBAAA,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtB,gBAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YAC5B,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;YAEnC,YAAA,IAAI,CAAC,KAAK,YAAY,EAAE;4BACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;;;;YAKzC,IAAA,oBAAoB,CAAC,KAAa,EAAA;oBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YAClC,QAAA,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,YAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC;;yBACtB,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,YAAA,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;;yBACrB;YACL,YAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;;;YAIhC,IAAA,qBAAqB,CAAC,KAAa,EAAA;oBACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;oBAClC,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;;YAGvF,IAAA,gBAAgB,CAAC,KAAa,EAAA;oBAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;oBAClC,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;;YAG7E,IAAA,iBAAiB,CAAC,KAAa,EAAA;oBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YAClC,QAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;gBAG7B,MAAM,GAAA;oBACJ,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACxB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,SAAS,EAAE,IAAI,CAAC,SAAS,EAAA,EACjC,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACJ,CACN;;;;;;;;;;;;;;;;;;"}