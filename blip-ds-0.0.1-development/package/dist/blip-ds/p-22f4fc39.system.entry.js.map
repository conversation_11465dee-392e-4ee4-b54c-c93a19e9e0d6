{"version": 3, "names": ["expansionPanelBodyCss", "ExpansionPanelBody", "exports", "class_1", "hostRef", "this", "open", "text", "prototype", "render", "h", "Host", "key", "class", "hidden"], "sources": ["src/components/expansion-panel/expansion-panel-body/expansion-panel-body.scss?tag=bds-expansion-panel-body&encapsulation=shadow", "src/components/expansion-panel/expansion-panel-body/expansion-panel-body.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: block;\n}\n.expansion-content {\n  display: flex;\n  padding-left: 22px;\n\n\n  .with-line {\n    border-left: 2px solid $color-neutral-medium-cloud;\n    padding-left: 9px;\n    padding-top: 16px;\n    display: flex;\n    justify-content: space-around;\n    flex-direction: column;\n    width: 100%;\n  }\n\n  .circle {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: $color-neutral-medium-cloud;\n    position: relative;\n    right: 14px;\n  }\n\n  .text{\n    right: 28px;\n    width: 37px;\n    height: 30px;\n    position: relative;\n    background: $color-neutral-medium-wave;\n    justify-content: center;\n    border-radius: 8px;\n    font-family: $font-family;\n    font-style: normal;\n    font-weight: 600;\n    font-size: 14px;\n    line-height: 22px;\n    display: flex;\n    align-items: center;\n    color: $color-neutral-dark-city;\n    top: 10px ;\n  }\n}\n", "import { Component, Host, h, Prop, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel-body',\n  styleUrl: 'expansion-panel-body.scss',\n  shadow: true,\n})\nexport class ExpansionPanelBody implements ComponentInterface {\n  @Prop() open?: boolean = false;\n  @Prop() text?: string = null;\n\n  render() {\n    return (\n      <Host>\n        <div class=\"expansion-content\" hidden={this.open}>\n          <div class=\"with-line\">\n            <slot></slot>\n            {this.text ? (\n              <div class=\"text\">\n                <p>{this.text}</p>\n              </div>\n            ) : (\n              <div class=\"circle\"></div>\n            )}\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAwB,u1B,ICOjBC,EAAkBC,EAAA,sCAL/B,SAAAC,EAAAC,G,UAMUC,KAAIC,KAAa,MACjBD,KAAIE,KAAY,IAoBzB,CAlBCJ,EAAAK,UAAAC,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,oBAAoBC,OAAQT,KAAKC,MAC1CI,EAAK,OAAAE,IAAA,2CAAAC,MAAM,aACTH,EAAa,QAAAE,IAAA,6CACZP,KAAKE,KACJG,EAAA,OAAKG,MAAM,QACTH,EAAI,SAAAL,KAAKE,OAGXG,EAAA,OAAKG,MAAM,a,WAfM,I", "ignoreList": []}