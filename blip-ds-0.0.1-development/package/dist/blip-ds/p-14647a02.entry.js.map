{"version": 3, "names": ["tableCellCss", "TableCell", "constructor", "hostRef", "this", "isDense", "type", "sortable", "justifyContent", "renderContent", "h", "class", "cell", "cell_custom", "dense_cell", "variant", "bold", "cell_action", "colSpan", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "render", "Host", "key"], "sources": ["src/components/table/table-cell/table-cell.scss?tag=bds-table-cell&encapsulation=scoped", "src/components/table/table-cell/table-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0 8px;\n  font-family: $font-family;\n  font-size: 14px;\n  vertical-align: middle;\n}\n.cell {\n  display: flex;\n  align-items: center;\n  min-height: 48px;\n  margin: 8px 0;\n  color: $color-content-default;\n  font-family: $font-family;\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.dense_cell {\n  margin: 0;\n}\n\n.cell_custom {\n  gap: 8px;\n}\n\n.cell_action {\n  flex-direction: row;\n  gap: 8px;\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type IconType = 'text' | 'custom' | 'emoji' | 'collapse';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n\n@Component({\n  tag: 'bds-table-cell',\n  styleUrl: 'table-cell.scss',\n  scoped: true,\n})\nexport class TableCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() type?: string = 'text';\n  @Prop() sortable = false;\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  renderContent(): HTMLElement {\n    return this.type === 'custom' ? (\n      <div class={{ cell:true, cell_custom:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'text' ? (\n      <div class={{ cell:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <bds-typo variant=\"fs-14\" bold={this.sortable ? 'bold' : 'regular'}>\n          <slot />\n        </bds-typo>\n      </div>\n    ) : this.type === 'action' ? (\n      <div class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'collapse' ? (\n      <td colSpan={2} class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </td>\n    ) : (\n      <slot />\n    );\n  }\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && bdsTable.getAttribute('dense-table') === 'true') {\n      this.isDense = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return <Host>{this.renderContent()}</Host>;\n  }\n}\n"], "mappings": "yDAAA,MAAMA,EAAe,g/B,MCWRC,EAAS,MALtB,WAAAC,CAAAC,G,UAOWC,KAAOC,QAAG,MACXD,KAAIE,KAAY,OAChBF,KAAQG,SAAG,MACXH,KAAcI,eAAmB,MAoC1C,CAlCC,aAAAC,GACE,OAAOL,KAAKE,OAAS,SACnBI,EAAA,OAAKC,MAAO,CAAEC,KAAK,KAAMC,YAAY,KAAMC,WAAW,KAAM,CAAC,YAAYV,KAAKI,kBAAkB,OAC9FE,EAAA,cAEAN,KAAKE,OAAS,OAChBI,EAAA,OAAKC,MAAO,CAAEC,KAAK,KAAME,WAAW,KAAM,CAAC,YAAYV,KAAKI,kBAAkB,OAC5EE,EAAA,YAAUK,QAAQ,QAAQC,KAAMZ,KAAKG,SAAW,OAAS,WACvDG,EAAA,eAGFN,KAAKE,OAAS,SAChBI,EAAK,OAAAC,MAAO,CAAEC,KAAK,KAAMK,YAAY,KAAMH,WAAW,KAAM,CAAC,YAAYV,KAAKI,kBAAkB,OAC9FE,EAAA,cAEAN,KAAKE,OAAS,WAChBI,EAAA,MAAIQ,QAAS,EAAGP,MAAO,CAAEC,KAAK,KAAMK,YAAY,KAAMH,WAAW,KAAM,CAAC,YAAYV,KAAKI,kBAAkB,OACzGE,EAAQ,cAGVA,EAAA,Y,CAIJ,iBAAAS,GACE,MAAMC,EAAWhB,KAAKiB,QAAQC,QAAQ,aACtC,GAAIF,GAAYA,EAASG,aAAa,iBAAmB,OAAQ,CAC/DnB,KAAKC,QAAU,I,EAInB,MAAAmB,GACE,OAAOd,EAACe,EAAM,CAAAC,IAAA,4CAAAtB,KAAKK,gB", "ignoreList": []}