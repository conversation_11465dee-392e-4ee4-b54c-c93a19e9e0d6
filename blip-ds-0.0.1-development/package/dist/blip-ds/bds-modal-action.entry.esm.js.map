{"version": 3, "file": "bds-modal-action.entry.esm.js", "sources": ["src/components/modal/modal-action/modal-action.scss?tag=bds-modal-action&encapsulation=shadow", "src/components/modal/modal-action/modal-action.tsx"], "sourcesContent": [".modal__action {\n  display: flex;\n  padding-top: 16px;\n  bottom: 32px;\n  right: 32px;\n\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-action',\n  styleUrl: 'modal-action.scss',\n  shadow: true,\n})\nexport class BdsModalAction implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"modal__action\">\n        <slot />\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAG,0FAA0F;;MCOpG,cAAc,GAAA,MAAA;;;;IACzB,MAAM,GAAA;QACJ,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,eAAe,EAAA,EACxB,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACJ;;;;;;;"}