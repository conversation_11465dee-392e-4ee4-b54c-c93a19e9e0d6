{"version": 3, "file": "p-Cyz0M7zk.system.js", "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion-header&encapsulation=shadow", "src/components/accordion/accordion-header.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, State, h, Prop, Element, Method } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-header',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionHeader {\n  private accordionElement?: HTMLBdsAccordionElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n\n  @State() btToggleIsfocus?: boolean = false;\n\n  @State() numberElement?: number = null;\n\n  /**\n   * Accordion Title. Used to add title in header accordion.\n   */\n  @Prop() accordionTitle?: string = null;\n\n  /**\n   * Icon. Used to add icon in header accordion.\n   */\n  @Prop() icon?: string = null;\n\n  /**\n   * Avatar Name. Used to add avatar in header accordion.\n   */\n  @Prop() avatarName?: string = null;\n\n  /**\n   * Avatar Thumb. Used to add avatar in header accordion.\n   */\n  @Prop() avatarThumb?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  componentWillRender() {\n    this.accordionElement = this.element.parentElement as HTMLBdsAccordionElement;\n  }\n\n  private toggleHeader = (): void => {\n    if (this.isOpen) {\n      this.accordionElement?.close();\n    } else {\n      this.accordionElement?.open();\n    }\n  };\n\n  handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      if (this.isOpen) {\n        this.accordionElement?.close();\n      } else {\n        this.accordionElement?.open();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <div onClick={this.toggleHeader} class={{ accordion_header: true }} data-test={this.dataTest}>\n        {this.avatarName || this.avatarThumb ? (\n          <bds-avatar name={this.avatarName} thumbnail={this.avatarThumb} size=\"extra-small\"></bds-avatar>\n        ) : (\n          this.icon && <bds-icon size=\"x-large\" name={this.icon} color=\"inherit\"></bds-icon>\n        )}\n        {this.accordionTitle && (\n          <bds-typo bold=\"bold\" variant=\"fs-16\" line-height=\"double\">\n            {this.accordionTitle}\n          </bds-typo>\n        )}\n        <slot></slot>\n        <bds-icon\n          class={{\n            accButton: true,\n            accButton__isopen: this.isOpen,\n            accButton__isfocus: this.btToggleIsfocus,\n          }}\n          size=\"x-large\"\n          name=\"arrow-down\"\n          color=\"inherit\"\n          tabindex=\"0\"\n          onKeyDown={this.handleKeyDown.bind(this)}\n        ></bds-icon>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;MAAA,MAAM,YAAY,GAAG,4lEAA4lE;;YCOpmE,eAAe,mCAAA,MAAA;MAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;MAMU,QAAA,IAAgB,CAAA,gBAAA,GAA6B,IAAI;MAIhD,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;MAExB,QAAA,IAAe,CAAA,eAAA,GAAa,KAAK;MAEjC,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;MAEtC;;MAEG;MACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;MAEtC;;MAEG;MACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;MAE5B;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAY,IAAI;MAElC;;MAEG;MACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;MAEnC;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAqBxB,QAAA,IAAY,CAAA,YAAA,GAAG,MAAW;;MAChC,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;sBACf,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;;uBACzB;sBACL,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;MAEjC,SAAC;MAyCF;MAjEC,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;MAI5B,IAAA,MAAM,IAAI,GAAA;MACR,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;MAIpB,IAAA,MAAM,KAAK,GAAA;MACT,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;UAGrB,mBAAmB,GAAA;cACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAwC;;MAW/E,IAAA,aAAa,CAAC,KAAK,EAAA;;MACjB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;MACxB,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;sBACf,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK,EAAE;;uBACzB;sBACL,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,EAAE;;;;UAKnC,MAAM,GAAA;MACJ,QAAA,QACE,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAa,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EACzF,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,IAClC,CAAA,CAAA,YAAA,EAAA,EAAY,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAC,aAAa,EAAc,CAAA,KAEhG,IAAI,CAAC,IAAI,IAAI,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAA,CAAY,CACnF,EACA,IAAI,CAAC,cAAc,KAClB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,iBAAa,QAAQ,EAAA,EACvD,IAAI,CAAC,cAAc,CACX,CACZ,EACD,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACb,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,SAAS,EAAE,IAAI;sBACf,iBAAiB,EAAE,IAAI,CAAC,MAAM;sBAC9B,kBAAkB,EAAE,IAAI,CAAC,eAAe;MACzC,aAAA,EACD,IAAI,EAAC,SAAS,EACd,IAAI,EAAC,YAAY,EACjB,KAAK,EAAC,SAAS,EACf,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B,CAAA,CACR;;;;;;;;;;;;"}