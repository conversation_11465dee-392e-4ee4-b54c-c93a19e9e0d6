{"version": 3, "file": "p-BbDFes-I.system.js", "sources": ["src/components/banner/banner-link/banner-link.scss?tag=bds-banner-link&encapsulation=shadow", "src/components/banner/banner-link/banner-link.tsx"], "sourcesContent": ["@use '../../../globals/colors' as *;\n\n:Host {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  text-decoration: underline;\n  white-space: nowrap;\n  margin-left: 16px;\n  order: 2;\n}\n\n.banner__link{\n  position: relative;\n  \n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n  \n    &:focus-visible {\n      outline: none;\n  \n      &::before {\n        border-color: $color-focus;\n      }\n    }\n}", "import { Component, h, Element, Event, EventEmitter, Prop } from '@stencil/core';\n\nexport type targets = 'blank' | 'self' | 'parent' | 'top' | 'framename';\n\n@Component({\n  tag: 'bds-banner-link',\n  styleUrl: 'banner-link.scss',\n  shadow: true,\n})\nexport class BannerLink {\n  @Element() el: HTMLBdsBannerElement;\n  /**\n   * Set the link pass.\n   */\n  @Prop() link: string;\n\n  /**\n   * Set the link pass.\n   */\n  @Prop() target: targets = 'blank';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the link is clicked.\n   */\n  @Event() bdsBannerLink!: EventEmitter;\n\n  private _buttonClickHandler = () => {\n    this.bdsBannerLink.emit(this.el);\n    window.open(this.link, `_${this.target}`);\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.bdsBannerLink.emit(this.el);\n      window.open(this.link, `_${this.target}`);\n    }\n  }\n\n  render(): HTMLElement {\n    const Element = 'a';\n\n    return (\n      <Element\n        class={{ banner__link: true }}\n        onClick={() => this._buttonClickHandler()}\n        data-test={this.dataTest}\n        tabindex=\"0\"\n        onKeyDown={this.handleKeyDown.bind(this)}\n      >\n        <slot></slot>\n      </Element>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,aAAa,GAAG,ucAAuc;;YCShd,UAAU,8BAAA,MAAA;MALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;MAYE;;MAEG;MACK,QAAA,IAAM,CAAA,MAAA,GAAY,OAAO;MAEjC;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAMxB,QAAA,IAAmB,CAAA,mBAAA,GAAG,MAAK;kBACjC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAChC,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,MAAM,CAAA,CAAE,CAAC;MAC3C,SAAC;MAwBF;MAtBS,IAAA,aAAa,CAAC,KAAK,EAAA;MACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;kBACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAChC,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,MAAM,CAAA,CAAE,CAAC;;;UAI7C,MAAM,GAAA;cACJ,MAAM,OAAO,GAAG,GAAG;MAEnB,QAAA,QACE,CAAC,CAAA,OAAO,EACN,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,EAC7B,OAAO,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE,eAC9B,IAAI,CAAC,QAAQ,EACxB,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAExC,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACL;;;;;;;;;;;;"}