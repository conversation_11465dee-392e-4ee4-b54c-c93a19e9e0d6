var __awaiter=this&&this.__awaiter||function(e,t,o,n){function i(e){return e instanceof o?e:new o((function(t){t(e)}))}return new(o||(o=Promise))((function(o,c){function r(e){try{a(n.next(e))}catch(e){c(e)}}function s(e){try{a(n["throw"](e))}catch(e){c(e)}}function a(e){e.done?o(e.value):i(e.value).then(r,s)}a((n=n.apply(e,t||[])).next())}))};var __generator=this&&this.__generator||function(e,t){var o={label:0,sent:function(){if(c[0]&1)throw c[1];return c[1]},trys:[],ops:[]},n,i,c,r;return r={next:s(0),throw:s(1),return:s(2)},typeof Symbol==="function"&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return a([e,t])}}function a(s){if(n)throw new TypeError("Generator is already executing.");while(r&&(r=0,s[0]&&(o=0)),o)try{if(n=1,i&&(c=s[0]&2?i["return"]:s[0]?i["throw"]||((c=i["return"])&&c.call(i),0):i.next)&&!(c=c.call(i,s[1])).done)return c;if(i=0,c)s=[s[0]&2,c.value];switch(s[0]){case 0:case 1:c=s;break;case 4:o.label++;return{value:s[1],done:false};case 5:o.label++;i=s[1];s=[0];continue;case 7:s=o.ops.pop();o.trys.pop();continue;default:if(!(c=o.trys,c=c.length>0&&c[c.length-1])&&(s[0]===6||s[0]===2)){o=0;continue}if(s[0]===3&&(!c||s[1]>c[0]&&s[1]<c[3])){o.label=s[1];break}if(s[0]===6&&o.label<c[1]){o.label=c[1];c=s;break}if(c&&o.label<c[2]){o.label=c[2];o.ops.push(s);break}if(c[2])o.ops.pop();o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e];i=0}finally{n=c=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,o,n,i;return{setters:[function(e){t=e.r;o=e.c;n=e.h;i=e.a}],execute:function(){var c='.checkbox{display:inline}.checkbox input[type=checkbox]{display:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-tap-highlight-color:transparent;cursor:pointer;margin:0}.checkbox input[type=checkbox]:focus{outline:0}.checkbox__icon{position:relative}.checkbox__icon::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.checkbox__icon:focus-visible{outline:none}.checkbox__icon:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.checkbox__icon:hover{border-color:var(--color-brand, rgb(0, 150, 250))}.checkbox--selected .checkbox__icon{background-color:var(--color-surface-primary, rgb(30, 107, 241));border-color:var(--color-surface-primary, rgb(30, 107, 241))}.checkbox--selected .checkbox__icon__svg{color:var(--color-content-bright, rgb(255, 255, 255))}.checkbox--selected .checkbox__icon:hover{background-color:var(--color-brand, rgb(0, 150, 250))}.checkbox--selected-disabled .checkbox__label{cursor:not-allowed}.checkbox--selected-disabled .checkbox__icon{color:var(--color-content-default, rgb(40, 40, 40));border-color:var(--color-content-default, rgb(40, 40, 40));background-color:var(--color-surface-3, rgb(227, 227, 227));opacity:50%}.checkbox--selected-disabled .checkbox__text{opacity:50%}.checkbox--deselected .checkbox__icon__svg{display:none}.checkbox--deselected-disabled .checkbox__label{cursor:not-allowed}.checkbox--deselected-disabled .checkbox__icon{opacity:50%;background-color:var(--color-surface-1, rgb(246, 246, 246));border:1px solid var(--color-brand, rgb(0, 150, 250))}.checkbox--deselected-disabled .checkbox__icon__svg{display:none}.checkbox__label{-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-o-user-select:none;-ms-user-select:none;user-select:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;cursor:pointer;max-width:-webkit-fit-content;max-width:-moz-fit-content;max-width:fit-content}.checkbox__icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:18px;width:18px;min-width:18px;border-radius:4px;color:var(--color-surface-1, rgb(246, 246, 246));border:1px solid var(--color-content-default, rgb(40, 40, 40));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:4px;-webkit-transition:all 0.3s;transition:all 0.3s;-webkit-transition-property:all;transition-property:all;-webkit-transition-duration:0.3s;transition-duration:0.3s;-webkit-transition-timing-function:ease;transition-timing-function:ease;-webkit-transition-delay:0s;transition-delay:0s}.checkbox__text{margin-left:8px;color:var(--color-content-default, rgb(40, 40, 40))}';var r=0;var s=e("bds_checkbox",function(){function e(e){var n=this;t(this,e);this.bdsChange=o(this,"bdsChange");this.bdsInput=o(this,"bdsInput");this.checked=false;this.disabled=false;this.dataTest=null;this.onClick=function(e){e.stopPropagation();n.checked=!n.checked;n.bdsChange.emit({checked:n.checked})};this.refNativeInput=function(e){n.nativeInput=e};this.getStyleState=function(){if(n.checked&&!n.disabled){return"checkbox--selected"}if(!n.checked&&!n.disabled){return"checkbox--deselected"}if(n.checked&&n.disabled){return"checkbox--selected-disabled"}if(!n.checked&&n.disabled){return"checkbox--deselected-disabled"}return""}}e.prototype.connectedCallback=function(){this.checkBoxId=this.refer||"bds-checkbox-".concat(r++)};e.prototype.getInputElement=function(){return Promise.resolve(this.nativeInput)};e.prototype.getValue=function(){return Promise.resolve(this.nativeInput.checked)};e.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.checked=!this.checked;this.bdsChange.emit({checked:this.checked});return[2]}))}))};e.prototype.handleKeyDown=function(e){if(e.key=="Enter"){this.checked=!this.checked;this.bdsChange.emit({checked:this.checked})}};e.prototype.render=function(){var e;var t=this;var o=this.getStyleState();return n("div",{key:"98f55751effaa89fb5fc721a95605775111f5d13",class:(e={checkbox:true},e[o]=true,e)},n("input",{key:"49a20fdd4ba8211e64c1e37ee44c2358a1c30703",type:"checkbox",ref:this.refNativeInput,id:this.checkBoxId,name:this.name,onClick:function(e){return t.onClick(e)},checked:this.checked,disabled:this.disabled,"data-test":this.dataTest}),n("label",{key:"e12e568e603356b179cfff801166fea188505f8b",class:"checkbox__label",htmlFor:this.checkBoxId},n("div",{key:"a229cd9a79988a94ab54f6aeb301e332d243a0fe",class:"checkbox__icon",tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)},n("bds-icon",{key:"5e4603d9e36b5a0230836ca6028445c6478bbe6a",class:"checkbox__icon__svg",size:"x-small",name:"true",color:"inherit"})),this.label&&n("bds-typo",{key:"7ae297f34d69825b0468f72840df1ec211a8ff59",class:"checkbox__text",variant:"fs-14",tag:"span"},this.label)))};return e}());s.style=c;var a=":host(.option-checked){-ms-flex-order:-1;order:-1}.load-spinner{background-color:var(--color-surface-0, rgb(255, 255, 255));height:200px}.select-option{display:grid;width:100%;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-o-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;background-color:var(--color-surface-0, rgb(255, 255, 255));padding:8px;padding-left:12px;-webkit-box-sizing:border-box;box-sizing:border-box;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;outline:none;-ms-flex-order:1;order:1}.select-option--selected .select-option__container--value{color:var(--color-primary, rgb(30, 107, 241))}.select-option--disabled .select-option__container--value,.select-option--disabled .select-option__container--bulk{cursor:not-allowed;color:var(--color-content-disable, rgb(89, 89, 89))}.select-option--disabled .select-option__container--value:hover,.select-option--disabled .select-option__container--bulk:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.select-option ::slotted(bds-icon){margin-right:10px}.select-option__container{color:var(--color-content-default, rgb(40, 40, 40));display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.select-option__container__checkbox{cursor:pointer;padding:8px;padding-left:12px;-webkit-box-sizing:border-box;box-sizing:border-box;display:-ms-flexbox;display:flex;gap:8px;-ms-flex-align:center;align-items:center;outline:none;-ms-flex-direction:row;flex-direction:row}.select-option__container__checkbox bds-checkbox{pointer-events:none}.select-option__container__fill_space{width:100%}.select-option__container--bulk,.select-option__container--status{color:var(--color-content-ghost, rgb(140, 140, 140))}.select-option__container--status{margin-left:4px}.select-option__container__overflow{overflow:hidden;padding-right:16px}.select-option__container:hover>.select-option__container--value,.select-option__container:hover>.select-option__container--bulk,.select-option__container:hover>.select-option__container--status{color:var(--color-primary, rgb(30, 107, 241))}.select-option__container:active>.select-option__container--value,.select-option__container:active>.select-option__container--bulk,.select-option__container:active>.select-option__container--status{color:var(--color-primary, rgb(30, 107, 241))}.select-option:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.select-option:focus{background-color:var(--color-surface-1, rgb(246, 246, 246));color:#3f7de8}.select-option--selected{background-color:var(--color-surface-1, rgb(246, 246, 246))}.select-option--invisible{display:none}";var l=e("bds_select_option",function(){function e(e){var n=this;t(this,e);this.optionSelected=o(this,"optionSelected");this.optionChecked=o(this,"optionChecked");this.selected=false;this.disabled=false;this.invisible=false;this.danger=false;this.bulkOption="";this.slotAlign="center";this.typeOption="default";this.checked=false;this.dataTest=null;this.refNativeInput=function(e){n.nativeInput=e};this.checkedCurrent=function(){if(n.typeOption!=="checkbox")return;n.nativeInput.toggle()};this.onClickSelectOption=function(){if(n.typeOption=="checkbox")return;if(!n.disabled){n.optionSelected.emit({value:n.value,label:n.element.innerHTML})}};this.optionHandle=function(e){var t=e.target;var o={value:t.name,label:n.element.innerHTML,checked:t.checked};n.checked=!n.checked;n.optionChecked.emit(o)};this.attachOptionKeyboardListeners=function(e){var t=e.target;switch(e.key){case"Enter":n.onClickSelectOption();break;case"ArrowDown":if(t.parentElement.nextElementSibling&&!t.parentElement.nextElementSibling.hasAttribute("invisible")){e.preventDefault();e.stopPropagation();t.parentElement.nextElementSibling.firstElementChild.focus()}break;case"ArrowUp":if(t.parentElement.previousElementSibling&&!t.parentElement.previousElementSibling.hasAttribute("invisible")){e.preventDefault();e.stopPropagation();t.parentElement.previousElementSibling.firstElementChild.focus()}}}}e.prototype.changeSelectionType=function(){this.typeOption=this.typeOption};e.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.checked=!this.checked;return[2]}))}))};e.prototype.toMark=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.checked=true;return[2]}))}))};e.prototype.markOff=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.checked=false;return[2]}))}))};e.prototype.render=function(){var e=this;return n("div",{key:"606cdb0a653a89da9875f700e82a42ab8484f641",id:"bds-select-option-".concat(this.value),"data-event":"click",role:"button",onKeyDown:this.attachOptionKeyboardListeners,onClick:this.onClickSelectOption,"data-value":this.value,"data-test":this.dataTest,class:{"select-option":this.typeOption!="checkbox","select-option--selected":this.selected,"select-option--disabled":this.disabled,"select-option--invisible":this.invisible}},n("div",{key:"3ecdaf47a248a04dbc73b415d2372822f6b9cd38",style:{alignSelf:this.slotAlign}},n("slot",{key:"50741f2d520e6710ee504ce39ba6313afa86ee5f",name:"input-left"})),n("div",{key:"4b6ead24b263297f5e1ee295781a23e15d901477",class:{"select-option__container":true,"select-option__container__fill_space":!!this.status,"select-option__container__checkbox":this.typeOption=="checkbox"},onClick:function(){return e.checkedCurrent()}},this.titleText&&n("bds-typo",{key:"6d11836176f2fbac8290dd882b2c6ba523f438e6",class:"select-option__container--value",variant:"fs-16",bold:"semi-bold"},this.titleText),this.typeOption==="checkbox"?n("bds-checkbox",{ref:this.refNativeInput,refer:"html-for-".concat(this.value),label:this.element.innerHTML,name:this.value,checked:this.checked,onBdsChange:function(t){return e.optionHandle(t)}}):n("bds-typo",{class:{"select-option__container--value":true,"select-option__container__overflow":!!this.status},noWrap:!!this.status,variant:"fs-14"},n("slot",null))),this.bulkOption&&n("bds-typo",{key:"5677cc4ba79f3d70370e5782438f1d0a5f133b0a",class:"select-option__container--bulk",variant:"fs-10"},this.bulkOption),this.status&&n("bds-typo",{key:"902fe35eced9f30928efd22946dc84cc2a55672d",class:"select-option__container--status",noWrap:true,variant:"fs-10"},this.status))};Object.defineProperty(e.prototype,"element",{get:function(){return i(this)},enumerable:false,configurable:true});Object.defineProperty(e,"watchers",{get:function(){return{typeOption:["changeSelectionType"]}},enumerable:false,configurable:true});return e}());l.style=a}}}));
//# sourceMappingURL=p-eb7db130.system.entry.js.map