{"version": 3, "file": "p-CJXav75v.system.js", "sources": ["src/components/carousel/carousel.scss?tag=bds-carousel&encapsulation=shadow", "src/components/carousel/carousel.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  position: relative;\n}\n\n.carousel {\n  display: block;\n  box-sizing: border-box;\n  width: 100%;\n  max-width: 1920px;\n  position: relative;\n\n  &_slide {\n    width: 100%;\n    position: relative;\n    box-sizing: border-box;\n    padding: 0 48px;\n\n    &::after {\n      content: '';\n      position: absolute;\n      inset: -8px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n      pointer-events: none;\n    }\n\n    &:focus-visible {\n      outline: none;\n      &::after {\n        border-color: $color-focus;\n      }\n    }\n\n    &_fullwidth {\n      padding: 0;\n    }\n\n    &_frame {\n      width: 100%;\n      display: flex;\n      overflow: hidden;\n      -webkit-transition: height ease-in-out 0.5s;\n      -moz-transition: height ease-in-out 0.5s;\n      transition: height ease-in-out 0.5s;\n\n      &_loading {\n        opacity: 0;\n        pointer-events: none;\n      }\n\n      & * {\n        -webkit-user-select: none; /* Safari */\n        -ms-user-select: none; /* IE 10 and IE 11 */\n        user-select: none; /* Standard syntax */\n        -webkit-user-drag: none;\n        -khtml-user-drag: none;\n        -moz-user-drag: none;\n        -o-user-drag: none;\n      }\n\n      & *[slot='loop'] {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n\n      &_repeater {\n        display: flex;\n        align-items: center;\n        position: relative;\n        right: 0;\n        -webkit-transition: right ease-in-out 0.75s;\n        -moz-transition: right ease-in-out 0.75s;\n        transition: right ease-in-out 0.75s;\n      }\n    }\n    &_loading {\n      opacity: 0;\n      pointer-events: none;\n      position: absolute;\n      inset: 0;\n\n      &_visible {\n        opacity: 1;\n        pointer-events: all;\n      }\n    }\n  }\n  &_loading_bar {\n    box-sizing: border-box;\n    padding: 0 60px;\n    margin-top: 8px;\n\n    &_fullwidth {\n      padding: 0 4px;\n    }\n  }\n\n  &_buttons {\n    position: absolute;\n    width: 100%;\n    height: 0px;\n    top: calc(50% - 20px);\n    left: 0;\n    display: flex;\n    justify-content: space-between;\n    box-sizing: border-box;\n\n    &_fullwidth {\n      padding: 0 8px;\n    }\n  }\n\n  &_bullets {\n    position: relative;\n    margin-top: 8px;\n\n    &_inside {\n      position: absolute;\n      bottom: 0px;\n      width: 100%;\n      margin: 0;\n      padding: 0px 16px;\n      box-sizing: border-box;\n    }\n\n    &_card {\n      width: fit-content;\n      display: inline-flex;\n      gap: 8px;\n\n      &_inside {\n        border-top-left-radius: 8px;\n        border-top-right-radius: 8px;\n        padding: 8px;\n        background-color: $color-surface-0;\n      }\n    }\n\n    &_item {\n      width: 16px;\n      height: 16px;\n      border: 2px solid $color-border-1;\n      border-radius: 50%;\n      position: relative;\n      transform: rotate(45deg);\n      cursor: pointer;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: 4px;\n        border-radius: 50%;\n      }\n\n      &::after {\n        content: '';\n        position: absolute;\n        inset: -8px;\n        transform: rotate(-45deg);\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n        &::after {\n          border-color: $color-focus;\n        }\n      }\n\n      &_active {\n        &::before {\n          background-color: $color-primary;\n        }\n      }\n\n      &_conclude {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-content-disable;\n      }\n\n      &_loader {\n        position: absolute;\n        inset: -2px;\n        border-radius: 50%;\n        border: 2px solid $color-primary;\n        animation: l18 linear;\n      }\n    }\n  }\n}\n\n@keyframes l18 {\n  0% {\n    clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);\n  }\n  25% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);\n  }\n  50% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);\n  }\n  75% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);\n  }\n  100% {\n    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);\n  }\n}\n", "import { Component, h, Element, State, Watch, Prop, Method, Event, EventEmitter } from '@stencil/core';\nimport { Itens, arrows, bullets, bulletsPositions, gap } from './carousel-interface';\nimport { gapChanged, getHighestItem, getItems } from '../../utils/position-element';\n\n@Component({\n  tag: 'bds-carousel',\n  styleUrl: 'carousel.scss',\n  shadow: true,\n})\nexport class BdsCarousel {\n  private itemsElement?: HTMLCollectionOf<HTMLBdsCarouselItemElement> = null;\n  private bulletElement?: HTMLElement = null;\n  private bulletElements: HTMLElement[] = [];\n  private frame?: HTMLElement;\n  private themeProviderArrows?: any;\n  private frameRepeater?: HTMLElement;\n  private incrementSeconds?: any;\n\n  @Element() element: HTMLElement;\n\n  @State() itemActivated = 1;\n  @State() seconds = 0;\n  @State() internalItens: Itens[];\n  @State() isWhole = 0;\n  @State() heightCarousel?: number = 240;\n  @State() framePressed?: boolean = false;\n  @State() startX?: number;\n  @State() endX?: number;\n  @State() autoplayState: 'paused' | 'running' = 'running';\n\n  /**\n   * Autoplay. Prop to Enable component autoplay.\n   */\n  @Prop() autoplay?: boolean = false;\n\n  /**\n   * AutoplayTimeout. Prop to Choose the Autoplay time in milliseconds, ex: 5000.\n   */\n  @Prop() autoplayTimeout?: number = 5000;\n\n  /**\n   * AutoplayHoverPause. Prop to Enable it if you will have the function to pause autoplay when on hover.\n   */\n  @Prop() autoplayHoverPause?: boolean = false;\n\n  /**\n   * autoHeight. Prop to Enable it if you want the component to adjust its height relative to the active items..\n   */\n  @Prop() autoHeight?: boolean = false;\n\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bullets?: boolean | bullets = 'outside';\n  /**\n   * Bullet. Prop to Enable component bullets navigation.\n   */\n  @Prop() bulletsPosition?: bulletsPositions = 'center';\n\n  /**\n   * InfiniteLoop. Prop to Enable if the component will have infinite loop.\n   */\n  @Prop() infiniteLoop?: boolean = false;\n\n  /**\n   * arrows. Prop to select type of arrows in component. Are available \"outside\" | \"inside\" | \"none\".\n   */\n  @Prop() arrows?: arrows = 'outside';\n\n  /**\n   * SlidePerPage. Prop to Choose the number of slide per page you will have available in the carousel.\n   */\n  @Prop() slidePerPage?: number = 1;\n\n  /**\n   * Gap. Prop to Select the gap distance between items.\n   */\n  @Prop() gap?: gap = 'none';\n\n  /**\n   * Grab. Prop to enable function of grab in carousel.\n   */\n  @Prop() grab?: boolean = true;\n\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop({ mutable: true, reflect: true }) loading?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtSlideContent is the data-test to slide action.\n   */\n  @Prop() dtSlideContent?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  @State() secondsLimit: number = this.autoplayTimeout / 1000;\n\n  /**\n   * Emitted when active frame value.\n   */\n  @Event() bdsChangeCarousel!: EventEmitter;\n\n  componentWillLoad() {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.setInternalItens(Array.from(this.itemsElement));\n    if (this.bullets == true) {\n      this.bullets = 'outside';\n    }\n    if (this.bullets == false) {\n      this.bullets = 'none';\n    }\n  }\n\n  componentDidRender() {\n    if (!this.loading) {\n      if (this.gap != 'none') {\n        this.frame.style.width = `calc(100% + ${gapChanged(this.gap)}px)`;\n        this.frame.style.marginLeft = `-${gapChanged(this.gap) / 2}px`;\n      }\n      for (let i = 0; i < this.itemsElement.length; i++) {\n        const widthFrame = this.frame.offsetWidth >= 1920 ? 1920 : this.frame.offsetWidth;\n        this.itemsElement[i].style.width = `${widthFrame / this.slidePerPage}px`;\n        this.itemsElement[i].style.padding = `0 ${gapChanged(this.gap) / 2}px`;\n      }\n      if (this.autoHeight) this.updateHeight(Array.from(this.itemsElement));\n    }\n    if (this.arrows == 'inside') {\n      const firstItemActived = (this.itemActivated - 1) * (this.itemsElement.length / this.internalItens.length) + 1;\n      this.themeProviderArrows.theme =\n        this.slidePerPage <= 1\n          ? this.itemsElement[this.itemActivated - 1].theme\n          : this.itemsElement[Math.round(firstItemActived)].theme;\n    }\n  }\n\n  componentDidLoad() {\n    this.startCountSeconds();\n  }\n\n  @Watch('itemActivated')\n  protected itemActivatedChanged(): void {\n    const currentItemSelected: Itens = this.internalItens.find((item) => item.id === this.itemActivated);\n    const slideFrame = !this.frame ? 0 : this.frame.offsetWidth * (this.itemActivated - 1);\n    if (this.frameRepeater) {\n      if (currentItemSelected.isWhole) {\n        const isWholeWidth = this.itemsElement[1].offsetWidth * (this.slidePerPage - this.isWhole);\n        this.frameRepeater.style.right = `${slideFrame - isWholeWidth}px`;\n      } else {\n        this.frameRepeater.style.right = `${slideFrame}px`;\n      }\n    }\n    this.bdsChangeCarousel.emit({ value: currentItemSelected });\n  }\n\n  @Watch('autoplayTimeout')\n  protected autoplayTimeoutChanged(): void {\n    this.secondsLimit = this.autoplayTimeout / 1000;\n  }\n\n  @Watch('seconds')\n  protected secondsChanged(): void {\n    if (this.seconds >= this.secondsLimit) {\n      this.nextSlide();\n      this.seconds = 0;\n    }\n  }\n\n  @Watch('isWhole')\n  protected isWholeChanged(): void {\n    if (this.internalItens != undefined) {\n      if (this.isWhole > 0) {\n        const newItem = {\n          id: this.internalItens?.length + 1,\n          label: `Frame - ${this.internalItens?.length + 1}`,\n          isWhole: true,\n        };\n        this.internalItens = [...this.internalItens, newItem];\n      }\n    }\n  }\n\n  private setInternalItens = (ItensElement) => {\n    const floor = Math.floor(ItensElement.length / this.slidePerPage);\n    const numberOfColumns = ItensElement.length / this.slidePerPage;\n    const newItens = getItems(numberOfColumns);\n    this.internalItens = newItens;\n    this.isWhole = ItensElement.length - this.slidePerPage * floor;\n  };\n\n  private startCountSeconds = () => {\n    if (this.autoplay) {\n      this.incrementSeconds = setInterval(() => {\n        this.seconds += 0.1;\n      }, 100);\n    }\n  };\n\n  private updateHeight = (itemsElement) => {\n    const elementActive = itemsElement[this.itemActivated * this.slidePerPage - this.slidePerPage];\n    let heightFrame = 240;\n    if (this.slidePerPage > 1) {\n      const getVisibleItens =\n        this.isWhole > 0 && this.itemActivated == this.internalItens.length\n          ? itemsElement.slice(\n              this.internalItens.length - this.internalItens.length - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            )\n          : itemsElement.slice(\n              this.itemActivated * this.slidePerPage - this.slidePerPage,\n              this.itemActivated * this.slidePerPage,\n            );\n\n      heightFrame = getHighestItem(getVisibleItens)[0];\n    } else {\n      heightFrame = elementActive.offsetHeight;\n    }\n    this.frame.style.height = `${heightFrame}px`;\n  };\n\n  @Method()\n  async buildCarousel(): Promise<void> {\n    this.itemsElement = this.element.getElementsByTagName(\n      'bds-carousel-item',\n    ) as HTMLCollectionOf<HTMLBdsCarouselItemElement>;\n    this.loading = true;\n    setTimeout(\n      () => (this.setInternalItens(Array.from(this.itemsElement)), (this.loading = false), this.setActivated(1)),\n      1000,\n    );\n  }\n\n  @Method()\n  async nextSlide(): Promise<void> {\n    if (this.itemActivated == this.internalItens.length) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = 1;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated + 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async prevSlide(): Promise<void> {\n    if (this.itemActivated == 1) {\n      if (this.infiniteLoop || this.autoplay) {\n        this.itemActivated = this.internalItens.length;\n      } else {\n        this.itemActivated = this.itemActivated;\n      }\n    } else {\n      this.itemActivated = this.itemActivated - 1;\n    }\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n  }\n\n  @Method()\n  async setActivated(item: number): Promise<void> {\n    this.itemActivated = item;\n    clearInterval(this.incrementSeconds);\n    this.seconds = 0;\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  @Method()\n  async pauseAutoplay(): Promise<void> {\n    clearInterval(this.incrementSeconds);\n    this.autoplayState = 'paused';\n  }\n\n  @Method()\n  async runAutoplay(): Promise<void> {\n    this.startCountSeconds();\n    this.autoplayState = 'running';\n  }\n\n  private refFrame = (el: HTMLElement): void => {\n    this.frame = el;\n  };\n\n  private refThemeProviderArrows = (el: HTMLBdsThemeProviderElement | HTMLElement): void => {\n    this.themeProviderArrows = el;\n  };\n\n  private refFrameRepeater = (el: HTMLElement): void => {\n    this.frameRepeater = el;\n  };\n\n  private refBulletElement = (el: HTMLElement): void => {\n    if (el) {\n      this.bulletElement = el; // Keep the current behavior\n      this.bulletElements.push(el); // Store all bullet elements\n    }\n  };\n\n  private onMouseOver = () => {\n    if (this.autoplayHoverPause) {\n      this.pauseAutoplay();\n    }\n  };\n\n  private onMouseOut = () => {\n    if (this.autoplayHoverPause) {\n      this.runAutoplay();\n    }\n  };\n\n  private onMouseDown = (ev: MouseEvent) => {\n    if (this.grab) {\n      this.framePressed = true;\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n      this.startX = ev.pageX - offsetFrame;\n      this.endX = ev.pageX - offsetFrame;\n      this.frame.style.cursor = 'grabbing';\n    }\n  };\n\n  private onMouseEnter = () => {\n    if (this.grab) {\n      this.frame.style.cursor = 'grab';\n    }\n  };\n\n  private onMouseUp = () => {\n    if (this.grab) {\n      this.framePressed = false;\n      this.frame.style.cursor = 'grab';\n      this.boundItems();\n      if (this.autoplayHoverPause) {\n        this.pauseAutoplay();\n      }\n    }\n  };\n\n  private onMouseMove = (ev: MouseEvent) => {\n    if (this.grab) {\n      if (!this.framePressed) return;\n      ev.preventDefault();\n\n      const offsetFrame = this.frame.offsetLeft + this.element.offsetLeft;\n\n      this.endX = ev.pageX - offsetFrame;\n    }\n  };\n\n  private boundItems = () => {\n    if (this.endX < this.startX) {\n      this.nextSlide();\n      this.seconds = 0;\n    } else if (this.endX > this.startX) {\n      this.prevSlide();\n      this.seconds = 0;\n    }\n  };\n\n  private setKeydownNavigation = (ev) => {\n    if (ev.key === 'Tab') {\n      if (this.bulletElements.length > 0) {\n        this.bulletElements[0].focus();\n      } else if (this.bulletElement) {\n        this.bulletElement.focus();\n      }\n    }\n    if (ev.key === 'ArrowRight') {\n      this.nextSlide();\n    }\n    if (ev.key === 'ArrowLeft') {\n      this.prevSlide();\n    }\n  };\n\n  render() {\n    // Reset bullet elements array at start of render\n    this.bulletElements = [];\n    \n    const ThemeOrDivArrows = this.arrows == 'inside' ? 'bds-theme-provider' : 'div';\n    const justifybulletsPosition =\n      this.bulletsPosition == 'center'\n        ? 'center'\n        : this.bulletsPosition == 'right'\n          ? 'flex-end'\n          : this.bulletsPosition == 'left' && 'flex-start';\n    return (\n      <div class={{ carousel: true }}>\n        <div\n          class={{\n            carousel_slide: true,\n            carousel_slide_fullwidth: this.arrows != 'outside',\n            [`carousel_slide_state_${this.autoplayState}`]: this.autoplay,\n          }}\n          tabindex=\"0\"\n          onKeyDown={(ev) => this.setKeydownNavigation(ev)}\n          data-test={this.dtSlideContent}\n        >\n          <div\n            ref={(el) => this.refFrame(el)}\n            class={{ carousel_slide_frame: true, carousel_slide_frame_loading: this.loading }}\n            onMouseOver={() => this.onMouseOver()}\n            onMouseOut={() => this.onMouseOut()}\n            onMouseDown={(ev) => this.onMouseDown(ev)}\n            onMouseEnter={() => this.onMouseEnter()}\n            onMouseUp={() => this.onMouseUp()}\n            onMouseMove={(ev) => this.onMouseMove(ev)}\n          >\n            <div ref={(el) => this.refFrameRepeater(el)} class={{ carousel_slide_frame_repeater: true }}>\n              <slot />\n            </div>\n          </div>\n          <bds-grid class={{ carousel_slide_loading: true, carousel_slide_loading_visible: this.loading }}>\n            <bds-skeleton height=\"100%\" shape=\"square\" width=\"100%\" />\n          </bds-grid>\n          {this.arrows != 'none' && !this.loading && (\n            <ThemeOrDivArrows\n              ref={(el) => this.refThemeProviderArrows(el)}\n              class={{\n                carousel_buttons: true,\n                carousel_buttons_fullwidth: this.arrows != 'outside',\n              }}\n            >\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-left\"\n                color=\"content\"\n                onBdsClick={() => this.prevSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated <= 1}\n                dataTest={this.dtButtonPrev}\n              ></bds-button>\n              <bds-button\n                variant=\"text\"\n                iconLeft=\"arrow-right\"\n                color=\"content\"\n                onBdsClick={() => this.nextSlide()}\n                disabled={!this.infiniteLoop && this.itemActivated >= this.internalItens.length}\n                dataTest={this.dtButtonNext}\n              ></bds-button>\n            </ThemeOrDivArrows>\n          )}\n        </div>\n        {this.internalItens.length > 1 && this.bullets != 'none' && (\n          <div\n            class={{\n              carousel_bullets: true,\n              carousel_bullets_inside: this.bullets == 'inside',\n            }}\n          >\n            {this.loading && this.bullets != 'inside' ? (\n              <bds-grid\n                xxs=\"12\"\n                gap=\"1\"\n                justify-content={justifybulletsPosition}\n                padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n              >\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n                <bds-skeleton height=\"16px\" width=\"16px\" shape=\"circle\" />\n              </bds-grid>\n            ) : (\n              this.internalItens && (\n                <bds-grid\n                  xxs=\"12\"\n                  justify-content={justifybulletsPosition}\n                  padding={this.arrows === 'outside' ? 'x-7' : 'none'}\n                >\n                  <div\n                    class={{\n                      carousel_bullets_card: true,\n                      carousel_bullets_card_inside: this.bullets == 'inside',\n                    }}\n                  >\n                    {this.internalItens.map((item, index) => (\n                      <div\n                        key={index}\n                        ref={(el) => this.refBulletElement(el)}\n                        class={{\n                          carousel_bullets_item: true,\n                          carousel_bullets_item_active: item.id == this.itemActivated,\n                        }}\n                        tabindex=\"0\"\n                        onClick={() => this.setActivated(item.id)}\n                      >\n                        {item.id < this.itemActivated && this.autoplay && (\n                          <div class={{ carousel_bullets_item_conclude: true }}></div>\n                        )}\n                        {item.id == this.itemActivated && this.autoplay && (\n                          <div\n                            class={{ carousel_bullets_item_loader: true }}\n                            style={{\n                              animationDuration: `${this.autoplayTimeout / 1000 - 0.1}s`,\n                              animationPlayState: this.autoplayState,\n                            }}\n                          ></div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </bds-grid>\n              )\n            )}\n          </div>\n        )}\n        <slot name=\"after\"></slot>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;MAAA,MAAM,WAAW,GAAG,ugKAAugK;;YCS9gK,WAAW,2BAAA,MAAA;MALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;MAMU,QAAA,IAAY,CAAA,YAAA,GAAkD,IAAI;MAClE,QAAA,IAAa,CAAA,aAAA,GAAiB,IAAI;MAClC,QAAA,IAAc,CAAA,cAAA,GAAkB,EAAE;MAQjC,QAAA,IAAa,CAAA,aAAA,GAAG,CAAC;MACjB,QAAA,IAAO,CAAA,OAAA,GAAG,CAAC;MAEX,QAAA,IAAO,CAAA,OAAA,GAAG,CAAC;MACX,QAAA,IAAc,CAAA,cAAA,GAAY,GAAG;MAC7B,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;MAG9B,QAAA,IAAa,CAAA,aAAA,GAAyB,SAAS;MAExD;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;MAElC;;MAEG;MACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;MAEvC;;MAEG;MACK,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;MAE5C;;MAEG;MACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;MAEpC;;MAEG;MACK,QAAA,IAAO,CAAA,OAAA,GAAuB,SAAS;MAC/C;;MAEG;MACK,QAAA,IAAe,CAAA,eAAA,GAAsB,QAAQ;MAErD;;MAEG;MACK,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;MAEtC;;MAEG;MACK,QAAA,IAAM,CAAA,MAAA,GAAY,SAAS;MAEnC;;MAEG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,CAAC;MAEjC;;MAEG;MACK,QAAA,IAAG,CAAA,GAAA,GAAS,MAAM;MAE1B;;MAEG;MACK,QAAA,IAAI,CAAA,IAAA,GAAa,IAAI;MAE7B;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MAEjE;;;MAGG;MACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;MAEtC;;;MAGG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;MAEpC;;;MAGG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;cAE3B,IAAA,CAAA,YAAY,GAAW,IAAI,CAAC,eAAe,GAAG,IAAI;MAwFnD,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,YAAY,KAAI;MAC1C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;kBACjE,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;MAC/D,YAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,CAAC;MAC1C,YAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;MAC7B,YAAA,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK;MAChE,SAAC;MAEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,MAAK;MAC/B,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;MACjB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAK;MACvC,oBAAA,IAAI,CAAC,OAAO,IAAI,GAAG;uBACpB,EAAE,GAAG,CAAC;;MAEX,SAAC;MAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,YAAY,KAAI;MACtC,YAAA,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;kBAC9F,IAAI,WAAW,GAAG,GAAG;MACrB,YAAA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;MACzB,gBAAA,MAAM,eAAe,GACnB,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;MAC3D,sBAAE,YAAY,CAAC,KAAK,CAChB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EACzE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;4BAExC,YAAY,CAAC,KAAK,CAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,EAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CACvC;sBAEP,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;uBAC3C;MACL,gBAAA,WAAW,GAAG,aAAa,CAAC,YAAY;;kBAE1C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,EAAG,WAAW,CAAA,EAAA,CAAI;MAC9C,SAAC;MAmEO,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,EAAe,KAAU;MAC3C,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE;MACjB,SAAC;MAEO,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,EAA6C,KAAU;MACvF,YAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE;MAC/B,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;MACnD,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;MACzB,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;kBACnD,IAAI,EAAE,EAAE;MACN,gBAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;sBACxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;MAEjC,SAAC;MAEO,QAAA,IAAW,CAAA,WAAA,GAAG,MAAK;MACzB,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;sBAC3B,IAAI,CAAC,aAAa,EAAE;;MAExB,SAAC;MAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAK;MACxB,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;sBAC3B,IAAI,CAAC,WAAW,EAAE;;MAEtB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAc,KAAI;MACvC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;MACb,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;MACxB,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;sBACnE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;sBACpC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;sBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU;;MAExC,SAAC;MAEO,QAAA,IAAY,CAAA,YAAA,GAAG,MAAK;MAC1B,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;sBACb,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;;MAEpC,SAAC;MAEO,QAAA,IAAS,CAAA,SAAA,GAAG,MAAK;MACvB,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;MACb,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK;sBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;sBAChC,IAAI,CAAC,UAAU,EAAE;MACjB,gBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;0BAC3B,IAAI,CAAC,aAAa,EAAE;;;MAG1B,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAc,KAAI;MACvC,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;sBACb,IAAI,CAAC,IAAI,CAAC,YAAY;0BAAE;sBACxB,EAAE,CAAC,cAAc,EAAE;MAEnB,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;sBAEnE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,GAAG,WAAW;;MAEtC,SAAC;MAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAK;kBACxB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;sBAC3B,IAAI,CAAC,SAAS,EAAE;MAChB,gBAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;uBACX,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;sBAClC,IAAI,CAAC,SAAS,EAAE;MAChB,gBAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;MAEpB,SAAC;MAEO,QAAA,IAAA,CAAA,oBAAoB,GAAG,CAAC,EAAE,KAAI;MACpC,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,KAAK,EAAE;sBACpB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;0BAClC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;MACzB,qBAAA,IAAI,IAAI,CAAC,aAAa,EAAE;MAC7B,oBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;;;MAG9B,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,YAAY,EAAE;sBAC3B,IAAI,CAAC,SAAS,EAAE;;MAElB,YAAA,IAAI,EAAE,CAAC,GAAG,KAAK,WAAW,EAAE;sBAC1B,IAAI,CAAC,SAAS,EAAE;;MAEpB,SAAC;MAuIF;UA5ZC,iBAAiB,GAAA;cACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACnD,mBAAmB,CAC4B;MACjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;MACpD,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;MACxB,YAAA,IAAI,CAAC,OAAO,GAAG,SAAS;;MAE1B,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;MACzB,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM;;;UAIzB,kBAAkB,GAAA;MAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,YAAA,IAAI,IAAI,CAAC,GAAG,IAAI,MAAM,EAAE;MACtB,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAe,YAAA,EAAA,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;MACjE,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,CAAA,CAAA,EAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;;MAEhE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;sBACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;MACjF,gBAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,IAAI;sBACxE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;;kBAExE,IAAI,IAAI,CAAC,UAAU;MAAE,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;MAEvE,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;kBAC3B,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;kBAC9G,IAAI,CAAC,mBAAmB,CAAC,KAAK;sBAC5B,IAAI,CAAC,YAAY,IAAI;MACnB,sBAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;MAC5C,sBAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK;;;UAI/D,gBAAgB,GAAA;cACd,IAAI,CAAC,iBAAiB,EAAE;;UAIhB,oBAAoB,GAAA;cAC5B,MAAM,mBAAmB,GAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;cACpG,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;MACtF,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;MACtB,YAAA,IAAI,mBAAmB,CAAC,OAAO,EAAE;sBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;MAC1F,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,UAAU,GAAG,YAAY,CAAA,EAAA,CAAI;;uBAC5D;sBACL,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,UAAU,CAAA,EAAA,CAAI;;;cAGtD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;;UAInD,sBAAsB,GAAA;cAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI;;UAIvC,cAAc,GAAA;cACtB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;kBACrC,IAAI,CAAC,SAAS,EAAE;MAChB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC;;;UAKV,cAAc,GAAA;;MACtB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;MACnC,YAAA,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;MACpB,gBAAA,MAAM,OAAO,GAAG;MACd,oBAAA,EAAE,EAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,IAAG,CAAC;MAClC,oBAAA,KAAK,EAAE,CAAA,QAAA,EAAW,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,CAAE,CAAA;MAClD,oBAAA,OAAO,EAAE,IAAI;uBACd;sBACD,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC;;;;MA4C3D,IAAA,MAAM,aAAa,GAAA;cACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACnD,mBAAmB,CAC4B;MACjD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;MACnB,QAAA,UAAU,CACR,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAC1G,IAAI,CACL;;MAIH,IAAA,MAAM,SAAS,GAAA;cACb,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;kBACnD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;MACtC,gBAAA,IAAI,CAAC,aAAa,GAAG,CAAC;;uBACjB;MACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;;mBAEpC;kBACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;;MAE7C,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;MACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;cAChB,IAAI,CAAC,iBAAiB,EAAE;;MAI1B,IAAA,MAAM,SAAS,GAAA;MACb,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;kBAC3B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;sBACtC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM;;uBACzC;MACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;;;mBAEpC;kBACL,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;;MAE7C,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;MACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;cAChB,IAAI,CAAC,iBAAiB,EAAE;;UAI1B,MAAM,YAAY,CAAC,IAAY,EAAA;MAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;MACzB,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;MACpC,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC;cAChB,IAAI,CAAC,iBAAiB,EAAE;MACxB,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;MAIhC,IAAA,MAAM,aAAa,GAAA;MACjB,QAAA,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;MACpC,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ;;MAI/B,IAAA,MAAM,WAAW,GAAA;cACf,IAAI,CAAC,iBAAiB,EAAE;MACxB,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS;;UAkGhC,MAAM,GAAA;;MAEJ,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;MAExB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,oBAAoB,GAAG,KAAK;MAC/E,QAAA,MAAM,sBAAsB,GAC1B,IAAI,CAAC,eAAe,IAAI;MACtB,cAAE;MACF,cAAE,IAAI,CAAC,eAAe,IAAI;MACxB,kBAAE;wBACA,IAAI,CAAC,eAAe,IAAI,MAAM,IAAI,YAAY;MACtD,QAAA,QACE,4DAAK,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAA,EAC5B,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,cAAc,EAAE,IAAI;MACpB,gBAAA,wBAAwB,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;sBAClD,CAAC,CAAA,qBAAA,EAAwB,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,QAAQ;MAC9D,aAAA,EACD,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,EACrC,WAAA,EAAA,IAAI,CAAC,cAAc,EAAA,EAE9B,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAC9B,KAAK,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,OAAO,EAAE,EACjF,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,EACrC,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,EACnC,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACzC,YAAY,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,EACvC,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EACjC,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA,EAEzC,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,6BAA6B,EAAE,IAAI,EAAE,EAAA,EACzF,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,CACF,EACN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,8BAA8B,EAAE,IAAI,CAAC,OAAO,EAAE,EAAA,EAC7F,CAAA,CAAA,cAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAc,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAA,CAAG,CACjD,EACV,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,KACrC,CAAA,CAAC,gBAAgB,EAAA,EAAA,GAAA,EAAA,0CAAA,EACf,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAC5C,KAAK,EAAE;MACL,gBAAA,gBAAgB,EAAE,IAAI;MACtB,gBAAA,0BAA0B,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;MACrD,aAAA,EAAA,EAED,CAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAC,MAAM,EACd,QAAQ,EAAC,YAAY,EACrB,KAAK,EAAC,SAAS,EACf,UAAU,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAClC,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EACvD,QAAQ,EAAE,IAAI,CAAC,YAAY,EACf,CAAA,EACd,CACE,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,MAAM,EACd,QAAQ,EAAC,aAAa,EACtB,KAAK,EAAC,SAAS,EACf,UAAU,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAClC,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAC/E,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAA,CACf,CACG,CACpB,CACG,EACL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,KACtD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,gBAAgB,EAAE,IAAI;MACtB,gBAAA,uBAAuB,EAAE,IAAI,CAAC,OAAO,IAAI,QAAQ;MAClD,aAAA,EAAA,EAEA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,IACvC,gBACE,GAAG,EAAC,IAAI,EACR,GAAG,EAAC,GAAG,qBACU,sBAAsB,EACvC,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,EAAA,EAEnD,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,EAC1D,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,EAC1D,CAAc,CAAA,cAAA,EAAA,EAAA,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAG,CAAA,CACjD,KAEX,IAAI,CAAC,aAAa,KAChB,CACE,CAAA,UAAA,EAAA,EAAA,GAAG,EAAC,IAAI,EAAA,iBAAA,EACS,sBAAsB,EACvC,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,MAAM,EAAA,EAEnD,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,qBAAqB,EAAE,IAAI;MAC3B,gBAAA,4BAA4B,EAAE,IAAI,CAAC,OAAO,IAAI,QAAQ;MACvD,aAAA,EAAA,EAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,MAClC,WACE,GAAG,EAAE,KAAK,EACV,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EACtC,KAAK,EAAE;MACL,gBAAA,qBAAqB,EAAE,IAAI;MAC3B,gBAAA,4BAA4B,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa;MAC5D,aAAA,EACD,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA,EAExC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,KAC5C,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,EAAE,8BAA8B,EAAE,IAAI,EAAE,EAAA,CAAQ,CAC7D,EACA,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,KAC7C,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,4BAA4B,EAAE,IAAI,EAAE,EAC7C,KAAK,EAAE;sBACL,iBAAiB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAG,CAAA,CAAA;sBAC1D,kBAAkB,EAAE,IAAI,CAAC,aAAa;MACvC,aAAA,EACI,CAAA,CACR,CACG,CACP,CAAC,CACE,CACG,CACZ,CACF,CACG,CACP,EACD,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,OAAO,EAAQ,CAAA,CACtB;;;;;;;;;;;;;;;;;;"}