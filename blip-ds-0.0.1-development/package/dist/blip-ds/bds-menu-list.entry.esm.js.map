{"version": 3, "file": "bds-menu-list.entry.esm.js", "sources": ["src/components/menu-list/menu-list.scss?tag=bds-menu-list&encapsulation=shadow", "src/components/menu-list/menu-list.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$menu-list-width: 7px;\n$menu-list-height: 56px;\n$menu-list-border: 8px;\n\n$menu-list-border-left: 8px 0px 0px 8px;\n$menu-list-border-right: 0px 8px 8px 0px;\n\n.menu-list {\n  display: flex;\n  width: fit-content;\n  box-shadow: $shadow-2;\n  height: $menu-list-height;\n  border-radius: $menu-list-border;\n\n  bds-menu-list-item + bds-menu-list-item {\n    border-left: 1px solid $color-neutral-medium-wave;\n  }\n\n  &__left {\n    width: $menu-list-width;\n    height: $menu-list-height;\n    border-radius: $menu-list-border-left;\n    background-color: $color-neutral-light-snow;\n  }\n\n  &__right {\n    width: $menu-list-width;\n    height: $menu-list-height;\n    border-radius: $menu-list-border-right;\n    background-color: $color-neutral-light-snow;\n  }\n}\n", "import { Component, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-menu-list',\n  styleUrl: 'menu-list.scss',\n  shadow: true,\n})\nexport class MenuList {\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div class=\"menu-list\">\n          <div class=\"menu-list__left\"></div>\n          <slot></slot>\n          <div class=\"menu-list__right\"></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,WAAW,GAAG,mjBAAmjB;;MCO1jB,QAAQ,GAAA,MAAA;;;;IACnB,MAAM,GAAA;AACJ,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,WAAW,EAAA,EACpB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,iBAAiB,EAAO,CAAA,EACnC,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACb,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,kBAAkB,GAAO,CAChC,CACD;;;;;;;"}