{"version": 3, "names": ["chipCss", "Chip", "exports", "class_1", "hostRef", "this", "size", "variant", "danger", "filter", "clickable", "deletable", "disabled", "prototype", "handleClickDelete", "event", "preventDefault", "bdsDelete", "emit", "id", "element", "getClickClass", "getSizeClass", "getStateClass", "render", "h", "Host", "key", "class", "Object", "assign", "chip", "icon", "name", "onClick", "bind", "theme"], "sources": ["src/components/chip/chip.scss?tag=bds-chip&encapsulation=shadow", "src/components/chip/chip.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  white-space: nowrap;\n  font-family: $font-family;\n  font-weight: 600;\n}\n\n:host(.chip) {\n  border-radius: 8px;\n  padding: 3px 8px;\n}\n\n:host(.chip--primary) {\n  background: $color-disabled-bg;\n  color: $color-primary-main;\n}\n\n:host(.chip--click.chip--primary:hover) {\n  background: $color-hover-light;\n  color: $color-primary-dark;\n}\n\n:host(.chip--watermelon) {\n  background: $color-primary-pinks-watermelon;\n  color: $color-neutral-light-snow;\n}\n\n:host(.chip--default) {\n  background: $color-neutral-light-breeze;\n  color: $color-neutral-medium-cloud;\n}\n\n:host(.chip--danger) {\n  background: $color-extend-reds-flower;\n  color: $color-extend-reds-delete;\n}\n\n:host(.chip--click.chip--danger:hover) {\n  background: $color-disabled-delete;\n  color: $color-extend-reds-dragon;\n}\n\n:host(.chip--filter) {\n  background: $color-primary-dark;\n  color: $color-neutral-light-snow;\n  \n}\n\n:host(.chip--click.chip--filter:hover) {\n  background: $color-primary-night;\n  color: $color-neutral-light-snow;\n}\n\n:host(.chip--standard) {\n  height: 24px;\n  font-size: $fs-12;\n}\n\n:host(.chip--tall) {\n  height: 32px;\n  font-size: $fs-14;\n}\n\n.chip__delete {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 6px;\n  cursor: pointer;\n}\n\n.chip__icon {\n  display: inline-flex;\n  align-items: center;\n  padding-right: 4px;\n}\n", "import { Component, Host, h, Prop, Event, EventEmitter, Element } from '@stencil/core';\n\nexport type ChipSize = 'standard' | 'tall';\nexport type ChipVariant = 'primary' | 'default' | 'watermelon';\n\n@Component({\n  tag: 'bds-chip',\n  styleUrl: 'chip.scss',\n  shadow: true,\n})\nexport class Chip {\n  @Element() private element: HTMLElement;\n\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n\n  /**\n   * Chip size. Entered as one of the size design tokens. Can be one of:\n   * \"standard\" and \"tall\"\n   */\n  @Prop() size?: ChipSize = 'standard';\n\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'primary', 'default';\n   */\n  @Prop() variant?: ChipVariant = 'default';\n\n  /**\n   * Add state danger on chip, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state filter on chip whith specific color.\n   */\n  @Prop() filter = false;\n\n  /**\n   * When 'true' and the component is using the primary variant, a hover is added\n   */\n  @Prop() clickable = false;\n\n  /**\n   * When 'true', the component recive remove button and dispach event onBdsDelete\n   */\n  @Prop() deletable = false;\n\n  /**\n   * When 'true', no events will be dispatched\n   */\n  @Prop() disabled = false;\n\n  /**\n   *  Triggered after a mouse click on delete icon, return id element. Only fired when deletable is true.\n   */\n  @Event() bdsDelete: EventEmitter;\n\n  handleClickDelete(event) {\n    if (!this.deletable || this.disabled) return;\n    event.preventDefault();\n    this.bdsDelete.emit({ id: this.element.id });\n  }\n\n  private getClickClass() {\n    return this.clickable ? { 'chip--click': true } : {};\n  }\n\n  private getSizeClass() {\n    return this.size === 'standard' ? { 'chip--standard': true } : { 'chip--tall': true };\n  }\n\n  private getStateClass() {\n    if (this.disabled) {\n      return { 'chip--default': true };\n    }\n\n    if (this.danger) {\n      return { 'chip--danger': true };\n    }\n\n    if (this.filter) {\n      return { 'chip--filter': true };\n    }\n\n    if (this.variant === 'primary') {\n      return { 'chip--primary': true };\n    }\n\n    if (this.variant === 'watermelon') {\n      return { 'chip--watermelon': true };\n    }\n\n    return { 'chip--default': true };\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          chip: true,\n          ...this.getClickClass(),\n          ...this.getStateClass(),\n          ...this.getSizeClass(),\n        }}\n      >\n        {this.icon && (\n          <div class=\"chip__icon\">\n            <bds-icon size=\"x-small\" name={this.icon}></bds-icon>\n          </div>\n        )}\n        <slot />\n        {this.deletable && (\n          <div class=\"chip__delete\" onClick={this.handleClickDelete.bind(this)}>\n            <bds-icon size=\"x-small\" theme=\"solid\" name=\"error\"></bds-icon>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAA,IAAMA,EAAU,mmC,ICUHC,EAAIC,EAAA,sBALjB,SAAAC,EAAAC,G,6CAiBUC,KAAIC,KAAc,WAMlBD,KAAOE,QAAiB,UAKPF,KAAMG,OAAI,MAK3BH,KAAMI,OAAG,MAKTJ,KAASK,UAAG,MAKZL,KAASM,UAAG,MAKZN,KAAQO,SAAG,KAqEpB,CA9DCT,EAAAU,UAAAC,kBAAA,SAAkBC,GAChB,IAAKV,KAAKM,WAAaN,KAAKO,SAAU,OACtCG,EAAMC,iBACNX,KAAKY,UAAUC,KAAK,CAAEC,GAAId,KAAKe,QAAQD,I,EAGjChB,EAAAU,UAAAQ,cAAA,WACN,OAAOhB,KAAKK,UAAY,CAAE,cAAe,MAAS,E,EAG5CP,EAAAU,UAAAS,aAAA,WACN,OAAOjB,KAAKC,OAAS,WAAa,CAAE,iBAAkB,MAAS,CAAE,aAAc,K,EAGzEH,EAAAU,UAAAU,cAAA,WACN,GAAIlB,KAAKO,SAAU,CACjB,MAAO,CAAE,gBAAiB,K,CAG5B,GAAIP,KAAKG,OAAQ,CACf,MAAO,CAAE,eAAgB,K,CAG3B,GAAIH,KAAKI,OAAQ,CACf,MAAO,CAAE,eAAgB,K,CAG3B,GAAIJ,KAAKE,UAAY,UAAW,CAC9B,MAAO,CAAE,gBAAiB,K,CAG5B,GAAIF,KAAKE,UAAY,aAAc,CACjC,MAAO,CAAE,mBAAoB,K,CAG/B,MAAO,CAAE,gBAAiB,K,EAG5BJ,EAAAU,UAAAW,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MACEC,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,CAAAC,KAAM,MACH1B,KAAKgB,iBACLhB,KAAKkB,iBACLlB,KAAKiB,iBAGTjB,KAAK2B,MACJP,EAAK,OAAAE,IAAA,2CAAAC,MAAM,cACTH,EAAU,YAAAE,IAAA,2CAAArB,KAAK,UAAU2B,KAAM5B,KAAK2B,QAGxCP,EAAQ,QAAAE,IAAA,6CACPtB,KAAKM,WACJc,EAAK,OAAAE,IAAA,2CAAAC,MAAM,eAAeM,QAAS7B,KAAKS,kBAAkBqB,KAAK9B,OAC7DoB,EAAA,YAAAE,IAAA,2CAAUrB,KAAK,UAAU8B,MAAM,QAAQH,KAAK,W,4HA1GvC,I", "ignoreList": []}