import{r as t,h as e}from"./p-C3J6Z5OX.js";const i=".menuexibition{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:16px}.menuexibition__disabled{opacity:0.5;cursor:no-drop}.menuexibition .avatar-item{display:block;margin-right:8px}.menuexibition .content-item{width:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.menuexibition .content-item .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.menuexibition .content-item .subtitle-item{color:var(--color-content-disable, rgb(89, 89, 89))}.menuexibition .content-item .description-item{color:var(--color-content-default, rgb(40, 40, 40))}";const s=class{constructor(e){t(this,e);this.avatarName=null;this.avatarThumbnail=null;this.avatarSize="standard";this.value=null;this.subtitle=null;this.description=null;this.disabled=false}render(){const t=this.avatarName||this.avatarThumbnail;return e("div",{key:"26f4d44f79e894f2221a77d4601d4fe3827a4df1",class:{menuexibition:true,[`menuexibition__disabled`]:this.disabled}},t&&e("bds-avatar",{key:"f472e20dcf583ecd9cd1c1b25b2ecb9420d568ff",class:"avatar-item",name:this.avatarName,thumbnail:this.avatarThumbnail,size:this.avatarSize}),e("div",{key:"c03868ec242cdbaaf7f207d1b25dc1ed5ab1c4e3",class:"content-item"},this.value&&e("bds-typo",{key:"5f6b4be0f567e01cd11aca057898da0314fd3507",class:"title-item",variant:"fs-16",tag:"span"},this.value),this.subtitle&&e("bds-typo",{key:"73daa9934056f64e320efd3665ebe2e636d915db",class:"subtitle-item",variant:"fs-10",tag:"span"},this.subtitle),this.description&&e("bds-typo",{key:"2c66e992d6d051de9582d927cbceeb1ffb9588cb",class:"description-item",variant:"fs-10",tag:"span"},this.description)))}};s.style=i;export{s as bds_menu_exibition};
//# sourceMappingURL=p-212fad9d.entry.js.map