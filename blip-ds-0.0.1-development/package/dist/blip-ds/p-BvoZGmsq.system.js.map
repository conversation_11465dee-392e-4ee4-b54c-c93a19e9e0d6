{"version": 3, "file": "p-BvoZGmsq.system.js", "sources": ["src/components/list/list.scss?tag=bds-list&encapsulation=shadow", "src/components/list/list.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { Data } from './list-interface';\n\nexport type TypeList = 'checkbox' | 'radio' | 'switch' | 'default';\n\n@Component({\n  tag: 'bds-list',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class List {\n  private itemListElement?: HTMLCollectionOf<HTMLBdsListItemElement> | NodeListOf<HTMLBdsListItemElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalData: Data[];\n  /**\n   * Typelist. Used to .\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n\n  /**\n   * The Data of the list\n   * Should be passed this way:\n   * data='[{\"value\": \"01\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"true\",\"icon\": \"settings-builder\"}, {\"value\": \"02\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"false\",\"icon\": \"settings-builder\",}]'\n   * Data can also be passed as child by using bds-list-item component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true, reflect: true }) data?: string | Data[];\n\n  /**\n   * Emitted when the value checkboxes has changed because of a click event.\n   */\n  @Event() bdsListCheckboxChange!: EventEmitter;\n  /**\n   * Emitted when the value radios has changed because of a click event.\n   */\n  @Event() bdsListRadioChange!: EventEmitter;\n  /**\n   * Emitted when the value switches has changed because of a click event.\n   */\n  @Event() bdsListSwitchChange!: EventEmitter;\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionsButtons!: EventEmitter;\n\n  componentWillLoad() {\n    this.data && this.dataChanged();\n  }\n\n  componentWillRender() {\n    this.data && this.updateData();\n    if (!this.data) {\n      this.setitemListElement();\n    }\n  }\n  componentDidRender() {\n    if (this.data) {\n      this.internalDataChanged();\n    }\n  }\n\n  @Watch('data')\n  dataChanged() {\n    this.updateData();\n  }\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n  }\n\n  @Watch('internalData')\n  internalDataChanged() {\n    this.itemListElement = this.element.shadowRoot.querySelectorAll('bds-list-item');\n  }\n\n  private setitemListElement() {\n    this.itemListElement = this.element.getElementsByTagName(\n      'bds-list-item',\n    ) as HTMLCollectionOf<HTMLBdsListItemElement>;\n\n    for (let i = 0; i < this.itemListElement.length; i++) {\n      this.itemListElement[i].typeList = this.typeList;\n      this.itemListElement[i].addEventListener('bdsChecked', (event: CustomEvent) => this.chagedOptions(event));\n    }\n  }\n\n  private updateData() {\n    if (this.data) {\n      if (typeof this.data === 'string') {\n        this.internalData = JSON.parse(this.data);\n      } else {\n        this.internalData = this.data;\n      }\n    }\n  }\n\n  private chagedOptions = (event: CustomEvent): void => {\n    const { detail } = event;\n    if (detail.typeList == 'radio') {\n      if (detail.checked == true) {\n        this.value = detail;\n      }\n    }\n    if (detail.typeList == 'checkbox') {\n      this.setSelectedCheckbox();\n    }\n    if (detail.typeList == 'switch') {\n      this.setSelectedSwitch();\n    }\n  };\n\n  private setSelectedRadio(itemList) {\n    const itens = Array.from(this.itemListElement);\n    const radios = itens.filter((item) => item.typeList == 'radio');\n    for (let i = 0; i < radios.length; i++) {\n      if (radios[i].value != itemList.value) {\n        radios[i].checked = false;\n      } else {\n        const construct = {\n          value: radios[i].value,\n          text: radios[i]?.text,\n          secondaryText: radios[i]?.secondaryText,\n          avatarName: radios[i]?.avatarName,\n          avatarThumbnail: radios[i]?.avatarThumbnail,\n          typeList: radios[i]?.typeList,\n        };\n        this.bdsListRadioChange.emit(construct);\n      }\n    }\n  }\n\n  private setSelectedCheckbox() {\n    const checkboxs = this.itemListElement;\n    const itens = Array.from(checkboxs).filter((item) => item.typeList == 'checkbox');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListCheckboxChange.emit(result);\n  }\n\n  private setSelectedSwitch() {\n    const Switch = this.itemListElement;\n    const itens = Array.from(Switch).filter((item) => item.typeList == 'switch');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListSwitchChange.emit(result);\n  }\n\n  private onClickActionsButtons = (event: CustomEvent): void => {\n    const { detail } = event;\n    this.bdsClickActionsButtons.emit(detail);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            list: true,\n          }}\n        >\n          {this.internalData ? (\n            this.internalData.map((item, idx) => (\n              <bds-list-item\n                key={idx}\n                value={item.value}\n                text={item.text}\n                type-list={this.typeList ? this.typeList : item.typeList}\n                secondary-text={item.secondaryText}\n                avatar-name={item.avatarName}\n                avatar-thumbnail={item.avatarThumbnail}\n                checked={item.checked}\n                icon={item.icon}\n                chips={item.chips}\n                actionsButtons={item.actionsButtons}\n                onBdsChecked={(ev) => this.chagedOptions(ev)}\n                onBdsClickActionButtom={(ev) => this.onClickActionsButtons(ev)}\n                dataTest={item.dataTest}\n              ></bds-list-item>\n            ))\n          ) : (\n            <slot></slot>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,OAAO,GAAG,i1EAAi1E;;YCUp1E,IAAI,uBAAA,MAAA;MALjB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;MAMU,QAAA,IAAe,CAAA,eAAA,GAAmF,IAAI;MAK9G;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAc,IAAI;MAmF1B,QAAA,IAAA,CAAA,aAAa,GAAG,CAAC,KAAkB,KAAU;MACnD,YAAA,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK;MACxB,YAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,OAAO,EAAE;MAC9B,gBAAA,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;MAC1B,oBAAA,IAAI,CAAC,KAAK,GAAG,MAAM;;;MAGvB,YAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,UAAU,EAAE;sBACjC,IAAI,CAAC,mBAAmB,EAAE;;MAE5B,YAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,EAAE;sBAC/B,IAAI,CAAC,iBAAiB,EAAE;;MAE5B,SAAC;MAsDO,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,KAAkB,KAAU;MAC3D,YAAA,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK;MACxB,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC;MAC1C,SAAC;MAoCF;UA9JC,iBAAiB,GAAA;MACf,QAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;;UAGjC,mBAAmB,GAAA;MACjB,QAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;MAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;kBACd,IAAI,CAAC,kBAAkB,EAAE;;;UAG7B,kBAAkB,GAAA;MAChB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;kBACb,IAAI,CAAC,mBAAmB,EAAE;;;UAK9B,WAAW,GAAA;cACT,IAAI,CAAC,UAAU,EAAE;;MAInB,IAAA,YAAY,CAAC,KAAa,EAAA;MACxB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;UAI9B,mBAAmB,GAAA;MACjB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,eAAe,CAAC;;UAG1E,kBAAkB,GAAA;cACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACtD,eAAe,CAC4B;MAE7C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;kBACpD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;kBAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,KAAkB,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;;UAIrG,UAAU,GAAA;MAChB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;MACb,YAAA,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;sBACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;uBACpC;MACL,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI;;;;MAoB3B,IAAA,gBAAgB,CAAC,QAAQ,EAAA;;cAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;MAC9C,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC;MAC/D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;kBACtC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,EAAE;MACrC,gBAAA,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK;;uBACpB;MACL,gBAAA,MAAM,SAAS,GAAG;MAChB,oBAAA,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;0BACtB,IAAI,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,IAAI;0BACrB,aAAa,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,aAAa;0BACvC,UAAU,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,UAAU;0BACjC,eAAe,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,eAAe;0BAC3C,QAAQ,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,CAAC,CAAC,0CAAE,QAAQ;uBAC9B;MACD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;;;;UAKrC,mBAAmB,GAAA;MACzB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe;cACtC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC;cACjF,MAAM,MAAM,GAAG;mBACZ,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO;MAC7B,aAAA,GAAG,CAAC,CAAC,IAAI,MAAM;kBACd,KAAK,EAAE,IAAI,CAAC,KAAK;MACjB,YAAA,IAAI,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,IAAI;MAChB,YAAA,aAAa,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,aAAa;MAClC,YAAA,UAAU,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,UAAU;MAC5B,YAAA,eAAe,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,eAAe;MACtC,YAAA,QAAQ,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,QAAQ;MACzB,SAAA,CAAC,CAAC;MACL,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;;UAGjC,iBAAiB,GAAA;MACvB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe;cACnC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;cAC5E,MAAM,MAAM,GAAG;mBACZ,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO;MAC7B,aAAA,GAAG,CAAC,CAAC,IAAI,MAAM;kBACd,KAAK,EAAE,IAAI,CAAC,KAAK;MACjB,YAAA,IAAI,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,IAAI;MAChB,YAAA,aAAa,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,aAAa;MAClC,YAAA,UAAU,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,UAAU;MAC5B,YAAA,eAAe,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,eAAe;MACtC,YAAA,QAAQ,EAAE,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,MAAA,GAAA,MAAA,GAAA,IAAI,CAAE,QAAQ;MACzB,SAAA,CAAC,CAAC;MACL,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC;;UAQvC,MAAM,GAAA;cACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,IAAI,EAAE,IAAI;MACX,aAAA,EAAA,EAEA,IAAI,CAAC,YAAY,IAChB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,MAC9B,qBACE,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,EACJ,WAAA,EAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAA,gBAAA,EACxC,IAAI,CAAC,aAAa,EACrB,aAAA,EAAA,IAAI,CAAC,UAAU,EACV,kBAAA,EAAA,IAAI,CAAC,eAAe,EACtC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EAC5C,sBAAsB,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,EAC9D,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAA,CACR,CAClB,CAAC,KAEF,CAAa,CAAA,MAAA,EAAA,IAAA,CAAA,CACd,CACG,CACD;;;;;;;;;;;;;;;;;"}