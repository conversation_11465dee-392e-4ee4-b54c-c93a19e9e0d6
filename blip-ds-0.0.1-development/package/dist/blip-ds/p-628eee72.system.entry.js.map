{"version": 3, "names": ["bannerCss", "Banner", "exports", "class_1", "hostRef", "_this", "this", "visible", "bannerAlign", "buttonClose", "context", "variant", "dtButtonClose", "_buttonClickHandler", "bdsBannerClose", "emit", "prototype", "toggle", "render", "h", "Host", "key", "class", "banner", "_a", "banner__holder", "concat", "banner__content", "theme", "size", "name", "banner__action", "onClick", "dataTest", "icon"], "sources": ["src/components/banner/banner.scss?tag=bds-banner&encapsulation=shadow", "src/components/banner/banner.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: fit-content;\n\n  font-family: $font-family;\n  font-size: $fs-14;\n  font-weight: $font-weight-bold;\n\n  .banner {\n    &__holder {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      width: 100%;\n      padding: 8px 16px;\n      min-height: 40px;\n      color: $color-content-default;\n\n      &--context--inside {\n        border-radius: 8px;\n      }\n\n      &--align--left {\n        justify-content: flex-start;\n      }\n\n      &--align--right {\n        justify-content: flex-start;\n      }\n\n      &--warning {\n        background-color: $color-warning;\n      }\n\n      &--system {\n        background-color: $color-system;\n      }\n\n      &--info {\n        background-color: $color-info;\n      }\n\n      &--error {\n        background-color: $color-error;\n      }\n\n      &--success {\n        background-color: $color-success;\n      }\n    }\n\n    &__content {\n      display: flex;\n      align-items: center;\n      width: 100%;\n\n      &--left {\n        justify-content: flex-start;\n      }\n\n      &--center {\n        justify-content: flex-start;\n      }\n\n      &--right {\n        justify-content: flex-start;\n      }\n      .slot {\n        display: flex;\n        justify-content: space-between;\n        margin-left: 8px;\n        width: 100%;\n        color: $color-content-default;\n      }\n    }\n\n    &__action {\n      display: inline-flex;\n      align-items: center;\n\n      .close {\n        cursor: pointer;\n        display: flex;\n        margin-left: 8px;\n      }\n    }\n  }\n}\n\n.space_left {\n  display: flex;\n}\n\n:host(.banner--hide) {\n  display: none;\n}\n", "import {\n  Component,\n  Host,\n  h,\n  ComponentInterface,\n  Prop,\n  State,\n  Method,\n  Event,\n  EventEmitter,\n  Element,\n} from '@stencil/core';\n\nexport type BannerVariant = 'system' | 'warning' | 'info' | 'error' | 'success';\nexport type BannerAlign = 'left' | 'right' | 'center';\nexport type ButtonClose = 'true' | 'false';\nexport type Link = 'true' | 'false';\nexport type Context = 'inside' | 'outside';\n@Component({\n  tag: 'bds-banner',\n  styleUrl: 'banner.scss',\n  shadow: true,\n})\nexport class Banner implements ComponentInterface {\n  @Element() el: HTMLBdsBannerElement;\n  @State() visible = true;\n  /**\n   * Set the banner aligment, it can be one of: 'center', 'right' or 'left'.\n   */\n  @Prop() bannerAlign?: BannerAlign = 'center';\n  /**\n   * Set if show up the close button.\n   */\n  @Prop() buttonClose?: ButtonClose = 'false';\n  /**\n   * Set if the banner is external or internal.\n   */\n  @Prop() context?: Context = 'outside';\n  /**\n   * Set the banner varient, it can be 'system' or 'warning'.\n   */\n  @Prop() variant?: BannerVariant = 'system';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Emitted when the banner is closed.\n   */\n  @Event() bdsBannerClose!: EventEmitter;\n  @Method()\n  async toggle() {\n    this.visible = !this.visible;\n  }\n\n  private _buttonClickHandler = () => {\n    this.bdsBannerClose.emit();\n    this.visible = false;\n  };\n\n  render() {\n    return (\n      <Host class={{ banner: true, 'banner--hide': !this.visible }}>\n        <div\n          class={{\n            banner__holder: true,\n            [`banner__holder--align--${this.bannerAlign}`]: true,\n            [`banner__holder--${this.variant}`]: true,\n            [`banner__holder--context--${this.context}`]: true,\n          }}\n        >\n          <div\n            class={{\n              banner__content: true,\n            }}\n          >\n            {this.variant === 'warning' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"warning\"></bds-icon>\n            )}\n            {this.variant === 'system' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"info\"></bds-icon>\n            )}\n            {this.variant === 'info' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"message-ballon\"></bds-icon>\n            )}\n            {this.variant === 'error' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"error\"></bds-icon>\n            )}\n            {this.variant === 'success' && (\n              <bds-icon class=\"icon_left\" theme=\"outline\" size=\"medium\" name=\"checkball\"></bds-icon>\n            )}\n            <div class=\"slot\">\n              <slot></slot>\n            </div>\n          </div>\n          <div\n            class={{\n              banner__action: true,\n            }}\n          >\n            {this.buttonClose === 'true' && (\n              <div class=\"close\" onClick={() => this._buttonClickHandler()}>\n                <bds-button-icon\n                  dataTest={this.dtButtonClose}\n                  icon=\"close\"\n                  size=\"short\"\n                  variant=\"secondary\"\n                ></bds-button-icon>\n              </div>\n            )}\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gnDAAA,IAAMA,EAAY,yiE,ICuBLC,EAAMC,EAAA,wBALnB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,uDAOWA,KAAOC,QAAG,KAIXD,KAAWE,YAAiB,SAI5BF,KAAWG,YAAiB,QAI5BH,KAAOI,QAAa,UAIpBJ,KAAOK,QAAmB,SAM1BL,KAAaM,cAAY,KAUzBN,KAAmBO,oBAAG,WAC5BR,EAAKS,eAAeC,OACpBV,EAAKE,QAAU,KACjB,CAyDD,CAhEOJ,EAAAa,UAAAC,OAAN,W,qFACEX,KAAKC,SAAWD,KAAKC,Q,iBAQvBJ,EAAAa,UAAAE,OAAA,W,MAAA,IAAAb,EAAAC,KACE,OACEa,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAO,CAAEC,OAAQ,KAAM,gBAAiBjB,KAAKC,UACjDY,EAAA,OAAAE,IAAA,2CACEC,OAAKE,EAAA,CACHC,eAAgB,MAChBD,EAAC,0BAAAE,OAA0BpB,KAAKE,cAAgB,KAChDgB,EAAC,mBAAAE,OAAmBpB,KAAKK,UAAY,KACrCa,EAAC,4BAAAE,OAA4BpB,KAAKI,UAAY,K,IAGhDS,EAAA,OAAAE,IAAA,2CACEC,MAAO,CACLK,gBAAiB,OAGlBrB,KAAKK,UAAY,WAChBQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,YAAYM,MAAM,UAAUC,KAAK,SAASC,KAAK,YAEhExB,KAAKK,UAAY,UAChBQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,YAAYM,MAAM,UAAUC,KAAK,SAASC,KAAK,SAEhExB,KAAKK,UAAY,QAChBQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,YAAYM,MAAM,UAAUC,KAAK,SAASC,KAAK,mBAEhExB,KAAKK,UAAY,SAChBQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,YAAYM,MAAM,UAAUC,KAAK,SAASC,KAAK,UAEhExB,KAAKK,UAAY,WAChBQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,YAAYM,MAAM,UAAUC,KAAK,SAASC,KAAK,cAEjEX,EAAK,OAAAE,IAAA,2CAAAC,MAAM,QACTH,EAAA,QAAAE,IAAA,+CAGJF,EAAA,OAAAE,IAAA,2CACEC,MAAO,CACLS,eAAgB,OAGjBzB,KAAKG,cAAgB,QACpBU,EAAA,OAAAE,IAAA,2CAAKC,MAAM,QAAQU,QAAS,WAAM,OAAA3B,EAAKQ,qBAAL,GAChCM,EACE,mBAAAE,IAAA,2CAAAY,SAAU3B,KAAKM,cACfsB,KAAK,QACLL,KAAK,QACLlB,QAAQ,iB,uHArFP,I", "ignoreList": []}