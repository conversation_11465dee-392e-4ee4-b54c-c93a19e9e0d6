import{r as t,c as i,h as e,H as o,a as s}from"./p-C3J6Z5OX.js";import{e as n}from"./p-BNEKIkjk.js";const r=[{bold:"Negrito",italic:"<PERSON><PERSON>lic<PERSON>",strike:"<PERSON><PERSON><PERSON>",underline:"<PERSON>linhado",link:"Link",code:"Código",align_left:"Alinhar à esquerda",align_center:"Alinhar ao centro",align_right:"Alinhar à direita",unordered_list:"Lista não ordenada",ordered_list:"Lista ordenada",quote:"Citação",h1:"Título 1",h2:"Título 2",h3:"Título 3",h4:"Título 4",h5:"Título 5",h6:"Título 6",clear_formatting:"Limpar formatação",expand:"Expandir"}];const a=[{bold:"Negrita",italic:"<PERSON>urs<PERSON>",strike:"<PERSON><PERSON><PERSON>",underline:"<PERSON><PERSON><PERSON>",link:"<PERSON>",code:"<PERSON><PERSON><PERSON>",align_left:"Alinear a la izquierda",align_center:"Alinear al centro",align_right:"Alinear a la derecha",unordered_list:"Lista desordenada",ordered_list:"Lista ordenada",quote:"Cita",h1:"Título 1",h2:"Título 2",h3:"Título 3",h4:"Título 4",h5:"Título 5",h6:"Título 6",clear_formatting:"Limpiar formato",expand:"Expandir"}];const d=[{bold:"Bold",italic:"Italic",strike:"Strikethrough",underline:"Underline",link:"Link",code:"Code",align_left:"Align left",align_center:"Align center",align_right:"Align right",unordered_list:"Unordered list",ordered_list:"Ordered list",quote:"Quote",h1:"Heading 1",h2:"Heading 2",h3:"Heading 3",h4:"Heading 4",h5:"Heading 5",h6:"Heading 6",clear_formatting:"Clear formatting",expand:"Expand"}];const c=(t,i)=>{let e;switch(t){case"pt_BR":e=r.map((t=>t[i]));break;case"es_ES":e=a.map((t=>t[i]));break;case"en_US":e=d.map((t=>t[i]));break;default:e=r.map((t=>t[i]))}return e};const l='.rich-text{-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;padding:8px;gap:8px;border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:16px;background-color:var(--color-surface-1, rgb(246, 246, 246))}.rich-text-top .format-buttons{-ms-flex-order:1;order:1}.rich-text-top .preview{-ms-flex-order:2;order:2}.rich-text-bottom .format-buttons{-ms-flex-order:2;order:2}.rich-text-bottom .preview{-ms-flex-order:1;order:1}.rich-text.active{border-color:var(--color-primary, rgb(30, 107, 241));-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.rich-text .format-buttons{display:none !important}.rich-text .format-buttons-active{display:-ms-flexbox !important;display:flex !important;position:relative;background-color:var(--color-surface-0, rgb(255, 255, 255));border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:16px;padding:8px}.rich-text .format-buttons .style-onhover{position:absolute;background-color:var(--color-surface-1, rgb(246, 246, 246));border-radius:32px;bottom:-32px;right:0;opacity:0;-webkit-transition:opacity ease-in-out 0.5s;-moz-transition:opacity ease-in-out 0.5s;transition:opacity ease-in-out 0.5s;pointer-events:none}.rich-text .format-buttons .style-onhover.active{opacity:1}.rich-text .format-buttons .accordion-header{width:100%;position:relative;padding-right:40px}.rich-text .format-buttons .accordion-header .buttons-list{-webkit-column-gap:8px;-moz-column-gap:8px;column-gap:8px}.rich-text .format-buttons .accordion-header .buttons-list .editor-bar{width:0;margin-right:-8px}.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip{-webkit-transition:height ease-in-out 0.25s;-moz-transition:height ease-in-out 0.25s;transition:height ease-in-out 0.25s;height:0px}.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip>bds-button,.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip>bds-dropdown>div[slot=dropdown-activator]>bds-button{height:0;opacity:0;display:block;overflow:hidden;-webkit-transition:height ease-in-out 0.25s, opacity 0.5s ease-in-out 0.25s;-moz-transition:height ease-in-out 0.25s, opacity 0.5s ease-in-out 0.25s;transition:height ease-in-out 0.25s, opacity 0.5s ease-in-out 0.25s}.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip.active{height:32px}.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip.active>bds-button,.rich-text .format-buttons .accordion-header .buttons-list bds-tooltip.active>bds-dropdown>div[slot=dropdown-activator]>bds-button{overflow:inherit;height:32px;opacity:1}.rich-text .format-buttons .accordion-header .arrow-down{position:absolute;right:0;top:0;display:none}.rich-text .format-buttons .accordion-header .arrow-down.active{display:block}.rich-text .preview{-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;padding:8px;-webkit-transition:height ease-in-out 0.25s;-moz-transition:height ease-in-out 0.25s;transition:height ease-in-out 0.25s}.rich-text .preview .editor-uai-design-system{min-height:48px;height:100%;background-color:transparent;font-size:1rem;line-height:1.5;overflow-y:auto;outline:none;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-style:normal;font-weight:normal;color:var(--color-content-default, rgb(40, 40, 40))}.rich-text .preview .editor-uai-design-system::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.rich-text .preview .editor-uai-design-system::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.rich-text .preview .editor-uai-design-system p,.rich-text .preview .editor-uai-design-system h1,.rich-text .preview .editor-uai-design-system h2,.rich-text .preview .editor-uai-design-system h3,.rich-text .preview .editor-uai-design-system h4,.rich-text .preview .editor-uai-design-system h5,.rich-text .preview .editor-uai-design-system h6,.rich-text .preview .editor-uai-design-system ul,.rich-text .preview .editor-uai-design-system ol,.rich-text .preview .editor-uai-design-system blockquote{margin:0 0 8px 0}.rich-text .preview .editor-uai-design-system h1{font-size:32px;font-weight:600}.rich-text .preview .editor-uai-design-system h2{font-size:28px;font-weight:600}.rich-text .preview .editor-uai-design-system h3{font-size:24px;font-weight:600}.rich-text .preview .editor-uai-design-system h4{font-size:20px;font-weight:600}.rich-text .preview .editor-uai-design-system h5{font-size:16px;font-weight:600}.rich-text .preview .editor-uai-design-system h6{font-size:12px;font-weight:600}.rich-text .preview .editor-uai-design-system a{text-decoration:none;color:var(--color-primary, rgb(30, 107, 241))}.rich-text .preview .editor-uai-design-system blockquote{padding:4px 16px 4px 32px;font-size:14px;position:relative;display:inline-block}.rich-text .preview .editor-uai-design-system blockquote::before,.rich-text .preview .editor-uai-design-system blockquote::after{content:\'"\';position:absolute;font-size:24px;color:var(--color-content-ghost, rgb(140, 140, 140))}.rich-text .preview .editor-uai-design-system blockquote::before{left:8px;top:-6px}.rich-text .preview .editor-uai-design-system blockquote::after{right:0px;bottom:0px}.rich-text .preview .editor-uai-design-system code{font-family:monospace;font-size:12px;background-color:var(--color-surface-2, rgb(237, 237, 237));padding:4px;border-radius:4px}';const h=class{constructor(e){t(this,e);this.bdsRichTextChange=i(this,"bdsRichTextChange");this.bdsRichTextInput=i(this,"bdsRichTextInput");this.bdsBlur=i(this,"bdsBlur");this.bdsFocus=i(this,"bdsFocus");this.buttonsListElement=null;this.buttonsEditElements=null;this.editor=null;this.dropDownLink=null;this.buttomBoldActive=false;this.buttomItalicActive=false;this.buttomStrikeActive=false;this.buttomUnderlineActive=false;this.buttomCodeActive=false;this.buttomLinkActive=false;this.buttomLinkValidDisabled=true;this.buttomAlignLeftActive=false;this.buttomAlignCenterActive=false;this.buttomAlignRightActive=false;this.buttomUnorderedListActive=false;this.buttomOrderedListActive=false;this.buttomQuoteActive=false;this.buttomH1Active=false;this.buttomH2Active=false;this.buttomH3Active=false;this.buttomH4Active=false;this.buttomH5Active=false;this.buttomH6Active=false;this.buttomAccordionActive=false;this.headerHeight="32px";this.hasSelectionRange=false;this.selectedLinesList=null;this.treeElementsEditor=null;this.styleSectorActive=null;this.styleOnHover="teste";this.whenSelectionLink=null;this.linkButtonInput=null;this.insideComponent=false;this.language="pt_BR";this.weightButton=true;this.italicButton=true;this.strikeThroughButton=true;this.underlineButton=true;this.linkButton=true;this.codeButton=true;this.alignmentButtons=true;this.listButtons=true;this.quoteButton=true;this.headingButtons=true;this.unstyledButton=true;this.height=null;this.maxHeight=null;this.positionBar="top";this.dataTest=null;this.refButtonsListElement=t=>{this.buttonsListElement=t};this.refeditorElement=t=>{this.editor=t};this.refDropDownLinkElement=t=>{this.dropDownLink=t};this.refInputSetLink=t=>{this.inputSetLink=t};this.clearToolbar=()=>{this.buttomBoldActive=false;this.buttomItalicActive=false;this.buttomStrikeActive=false;this.buttomUnderlineActive=false;this.buttomLinkActive=false;this.buttomCodeActive=false;this.buttomAlignLeftActive=false;this.buttomAlignCenterActive=false;this.buttomAlignRightActive=false;this.buttomUnorderedListActive=false;this.buttomOrderedListActive=false;this.buttomQuoteActive=false;this.buttomH1Active=false;this.buttomH2Active=false;this.buttomH3Active=false;this.buttomH4Active=false;this.buttomH5Active=false;this.buttomH6Active=false};this.setheaderHeight=()=>{this.buttomAccordionActive=!this.buttomAccordionActive;const t=window.getSelection();if(!t||t.rangeCount===0)return;const i=t.getRangeAt(0);t.removeAllRanges();t.addRange(i)};this.onBlur=()=>{this.el.classList.remove("active");if(this.insideComponent===false){this.clearToolbar()}this.bdsBlur.emit()};this.onFocus=()=>{this.el.classList.add("active");this.bdsFocus.emit()};this.onKeydown=t=>{if(t.key==="Backspace"){const i=window.getSelection();if(!i||i.rangeCount===0)return;const e=i.getRangeAt(0);const o=e.startContainer;let s=o.nodeType===Node.TEXT_NODE?o.parentElement:o;while(s&&!s.classList.contains("line")&&s!==this.editor){s=s.parentElement}if(s&&s.tagName==="BLOCKQUOTE"&&s.classList.contains("line")&&s.innerText.length<=1){t.preventDefault();s.remove()}}if(this.editor.textContent.length===0&&t.key==="Backspace"){t.preventDefault();this.editor.innerHTML=`<p class="line"><br></p>`;this.setCursorToEnd()}if((t.ctrlKey||t.metaKey)&&t.key==="z"){t.preventDefault();t.stopPropagation()}}}componentDidLoad(){if(this.editor.innerHTML.trim()===""){this.editor.innerHTML='<p class="line"><br></p>'}if(this.weightButton||this.italicButton||this.strikeThroughButton||this.underlineButton||this.linkButton||this.codeButton||this.alignmentButtons||this.listButtons||this.quoteButton||this.headingButtons||this.unstyledButton){this.buttonsEditElements=this.buttonsListElement.getElementsByTagName("bds-tooltip");this.accordionHeader(false);this.editor.parentElement.style.height=`calc(100% - 56px)`}else{this.editor.parentElement.style.height=`100%`}}buttomsHeaderChanged(){setTimeout((()=>this.accordionHeader(this.buttomAccordionActive)),500)}buttomAccordionActiveChanged(){this.accordionHeader(this.buttomAccordionActive)}updateToolbarState(){const t=window.getSelection();const i=t.getRangeAt(0);const e=i.commonAncestorContainer;const o=e.nodeType===Node.TEXT_NODE?e.parentElement:e;this.treeElementsEditor=n(o,".editor-uai-design-system")}accordionHeader(t){const i=this.buttonsEditElements.length*40;const e=this.buttonsListElement.offsetWidth;const o=this.el.querySelector("#buttonAccordion");if(e<i){o.classList.add("active")}else{o.classList.remove("active")}const s=e*this.buttonsEditElements.length/i;const n=Math.ceil(i/e);const r=Array.from(this.buttonsEditElements);r.slice(0,Math.floor(s)).forEach((t=>{t.classList.add("active")}));if(t){r.forEach((t=>{t.classList.add("active");this.editor.parentElement.style.height=`calc(100% - ${n*32+24}px)`}))}else{r.slice(Math.floor(s)).forEach((t=>{t.classList.remove("active");this.editor.parentElement.style.height=`calc(100% - 56px)`}))}}treeElementsEditorChanged(t){const i=t.map((t=>t===null||t===void 0?void 0:t.tagName.toLowerCase()));const e=t=>i.includes(t);const o=t.find((t=>t===null||t===void 0?void 0:t.classList.contains("line")));this.buttomBoldActive=e("b");this.buttomItalicActive=e("i");this.buttomStrikeActive=e("strike");this.buttomUnderlineActive=e("u");this.buttomLinkActive=e("a");this.buttomCodeActive=e("code");this.buttomAlignLeftActive=(o===null||o===void 0?void 0:o.style.textAlign)==="left";this.buttomAlignCenterActive=(o===null||o===void 0?void 0:o.style.textAlign)==="center";this.buttomAlignRightActive=(o===null||o===void 0?void 0:o.style.textAlign)==="right";this.buttomUnorderedListActive=i[0]==="ul";this.buttomOrderedListActive=i[0]==="ol";this.buttomQuoteActive=e("blockquote");this.buttomH1Active=e("h1");this.buttomH2Active=e("h2");this.buttomH3Active=e("h3");this.buttomH4Active=e("h4");this.buttomH5Active=e("h5");this.buttomH6Active=e("h6")}onInput(t){t.preventDefault();this.bdsRichTextInput.emit(t);this.bdsRichTextChange.emit({value:this.editor.innerHTML});const i=window.getSelection();if(!i||i.rangeCount===0)return;const e=i.getRangeAt(0);const o=e.startContainer;if(o.nodeType===Node.ELEMENT_NODE&&o.tagName==="DIV"){const t=o;const i=document.createElement("p");i.classList.add("line");i.innerHTML=t.innerHTML;t.parentNode.replaceChild(i,t)}this.editor.querySelectorAll("div").forEach((t=>{const i=document.createElement("p");i.classList.add("line");i.innerHTML=t.innerHTML;t.replaceWith(i)}))}onFocusEditorBar(t){const i=t.target;const e=i.nextElementSibling.querySelector("bds-button");const o=e.shadowRoot.querySelector(".focus");o.focus();this.buttomAccordionActive=true}setCursorToEnd(){const t=document.createRange();const i=window.getSelection();t.selectNodeContents(this.editor);t.collapse(false);i.removeAllRanges();i.addRange(t)}tagName(t,i){const e=i.map((t=>t===null||t===void 0?void 0:t.tagName.toLowerCase()));return e.includes(t)}wrapSelection(t,i,e){const o=t.detail;if(o instanceof KeyboardEvent&&o.key==="Enter"){o.preventDefault();o.stopPropagation()}const s=window.getSelection();if(!s||s.rangeCount===0)return;if(!this.editor.contains(s.anchorNode))return;const r=s.getRangeAt(0);const a=r.commonAncestorContainer;const d=a.nodeType===Node.TEXT_NODE?a.parentElement:a;const c=n(d,".line");const l=this.tagName(i,c);let h;let b=false;if(l){const t=c.find((t=>t.tagName.toLowerCase()===i));if(t){const i=t.parentElement;const e=r.toString().trim()===t.textContent.trim();const o=r.endOffset===t.textContent.length;if(e&&i){while(t.firstChild){i.insertBefore(t.firstChild,t)}i.removeChild(t);s.removeAllRanges();s.addRange(r);this.updateToolbarState()}else if(o){h=document.createDocumentFragment();const i=document.createTextNode("​");h.appendChild(i);b=true;const e=document.createRange();e.setStartAfter(t);e.setEndAfter(t);e.insertNode(h);s.removeAllRanges();s.addRange(e);this.updateToolbarState()}else{s.removeAllRanges();s.addRange(r);this.updateToolbarState()}}return}if(r.collapsed){h=document.createDocumentFragment();const t=document.createTextNode("​");h.appendChild(t);b=true}else{h=r.extractContents()}h.querySelectorAll("*").forEach((t=>{while(t.firstChild){t.parentNode.insertBefore(t.firstChild,t)}t.remove()}));const f=document.createElement(i);if(i==="a"&&e){f.setAttribute("href",e)}f.appendChild(h);r.insertNode(f);this.editor.querySelectorAll("*").forEach((t=>{if(!t.textContent.trim()&&t.children.length===0){t.remove()}}));const u=document.createRange();if(b){u.setStart(f,0);u.setEnd(f,1)}else{u.setStartBefore(f.firstChild||f);u.setEndAfter(f.lastChild||f)}s.removeAllRanges();s.addRange(u);this.updateToolbarState();this.bdsRichTextChange.emit({value:this.editor.innerHTML})}wrapSelectionLine(t,i=false){var e,o,s;const n=window.getSelection();if(!n||n.rangeCount===0)return;if(!this.editor.contains(n.anchorNode))return;const r=n.getRangeAt(0);const a=r.startContainer;const d=r.endContainer;const c=new Set;let l=a.parentElement;while(l&&l!==d.parentElement){let t=l.nodeType===Node.TEXT_NODE?l.parentElement:l;if(t&&t.classList.contains("line")){c.add(t)}l=l.nextSibling||((e=l.parentElement)===null||e===void 0?void 0:e.nextSibling)}let h=d.nodeType===Node.TEXT_NODE?d.parentElement:d;while(h&&!h.classList.contains("line")&&h!==this.editor){h=h.parentElement}if(h&&h.classList.contains("line")){c.add(h)}const b=[...c].every((i=>t==="li"?false:i.tagName.toLowerCase()===t));const f=[...c].map((i=>{const e=document.createElement(b?"p":t);e.classList.add("line");e.innerHTML=i.innerHTML;i.replaceWith(e);return e}));if(i){this.selectedLinesList=f.map((t=>({element:t})))}const u=document.createRange();let p=f[0].lastChild;if(!p){p=document.createTextNode("");f[0].appendChild(p)}while(p&&p.nodeType!==Node.TEXT_NODE){p=p.lastChild||p}u.setStart(p,((o=p.textContent)===null||o===void 0?void 0:o.length)||0);u.setEnd(p,((s=p.textContent)===null||s===void 0?void 0:s.length)||0);n.removeAllRanges();n.addRange(u);this.updateToolbarState();this.bdsRichTextChange.emit({value:this.editor.innerHTML})}alignText(t,i){const e=t.detail;if(e instanceof KeyboardEvent&&e.key==="Enter"){e.preventDefault();e.stopPropagation()}const o=window.getSelection();if(!o||o.rangeCount===0)return;const s=o.getRangeAt(0);let n=s.startContainer;while(n&&n!==this.editor){if(n.nodeType===Node.ELEMENT_NODE&&n.classList.contains("line")){break}n=n.parentElement}if(n&&n!==this.editor){const t=n.style.textAlign;if(t===i){n.style.textAlign=""}else{n.style.textAlign=i}}o.removeAllRanges();o.addRange(s);this.updateToolbarState();this.bdsRichTextChange.emit({value:this.editor.innerHTML})}createHeading(t,i){var e;const o=t.detail;if(o instanceof KeyboardEvent&&o.key==="Enter"){o.preventDefault();o.stopPropagation()}const s=window.getSelection();if(!s||s.rangeCount===0)return;if(!this.editor.contains(s.anchorNode))return;this.wrapSelectionLine(i,true);const n=(e=this.selectedLinesList[0])===null||e===void 0?void 0:e.element;const r=n.parentElement.previousElementSibling;const a=n.parentElement.nextElementSibling;const d=n.parentElement;if(d.tagName.toLowerCase()==="ul"){this.selectedLinesList.forEach((t=>{if(r){r.insertAdjacentElement("afterend",t.element)}else if(a){a.insertAdjacentElement("beforebegin",t.element)}else{this.editor.insertAdjacentElement("afterbegin",t.element)}}));if(Array.from(d.getElementsByTagName("li")).length==0){d.remove()}}}createList(t,i){var e;const o=t.detail;if(o instanceof KeyboardEvent&&o.key==="Enter"){o.preventDefault();o.stopPropagation()}const s=window.getSelection();if(!s||s.rangeCount===0)return;if(!this.editor.contains(s.anchorNode))return;this.wrapSelectionLine("li",true);const n=this.selectedLinesList[0].element;const r=(e=this.selectedLinesList[this.selectedLinesList.length-1])===null||e===void 0?void 0:e.element;const a=document.createElement(i);const d=n.parentElement;if(!this.verifyList(n,r)){d.insertBefore(a,n);this.selectedLinesList.forEach((t=>{a.appendChild(t.element)}))}else{const t=d.getElementsByTagName("li");const e=Array.from(t).map((t=>({element:t})));if(e.length==this.selectedLinesList.length){if(i!==d.tagName.toLowerCase()){a.innerHTML=d.innerHTML;d.parentNode.replaceChild(a,d)}else{this.selectedLinesList.forEach((t=>{const i=d.parentElement.tagName.toLowerCase()==="li"?"li":"p";const e=document.createElement(i);e.classList.add("line");e.innerHTML=t.element.innerHTML;if(d.parentElement.tagName.toLowerCase()==="li"){d.parentElement.insertAdjacentElement("afterend",e)}else{d.previousElementSibling.insertAdjacentElement("afterend",e)}d.removeChild(t.element)}));d.remove()}}else{n.previousElementSibling.insertAdjacentElement("beforeend",a);this.selectedLinesList.forEach((t=>{a.appendChild(t.element)}))}}}addSelectionLink(t){const i=t.detail;if(i instanceof KeyboardEvent&&i.key==="Enter"){this.dropDownLink.setOpen()}this.editor.focus();const e=window.getSelection();this.whenSelectionLink=e.getRangeAt(0);const o=this.inputSetLink.shadowRoot.querySelector(".input__container__text");o.focus()}addLinkInput(t){t.preventDefault();const i=t.target;this.linkButtonInput=i.value;if(this.linkButtonInput.length>0){this.buttomLinkValidDisabled=false}else{this.buttomLinkValidDisabled=true}}createLinkKeyDown(t){if(t.key=="Enter"){this.createLink(t)}}createLink(t){t.preventDefault();const i=window.getSelection();i.removeAllRanges();i.addRange(this.whenSelectionLink);this.wrapSelection(t,"a",this.linkButtonInput);if(this.dropDownLink){this.dropDownLink.setClose()}}verifyList(t,i){const e=t.parentElement.tagName.toLowerCase()==="ul"||t.parentElement.tagName.toLowerCase()==="ol";const o=i.parentElement.tagName.toLowerCase()==="ul"||i.parentElement.tagName.toLowerCase()==="ol";return e&&o}handlePaste(t){t.preventDefault();t.stopPropagation();const i=t.clipboardData||window.clipboardData;const e=i.getData("text/plain");const o=window.getSelection();if(!o||o.rangeCount===0)return;const s=o.getRangeAt(0);const n=s.commonAncestorContainer;const r=n.nodeType===Node.TEXT_NODE?n.parentElement:n;if(r.classList.contains("line")){r.remove()}s.deleteContents();const a=document.createDocumentFragment();e.split("\n").forEach((t=>{if(t.trim()){const i=document.createElement("p");i.classList.add("line");i.textContent=t.trim();a.appendChild(i)}}));s.insertNode(a);o.removeAllRanges();o.addRange(s)}clearFormatting(t){var i;const e=t.detail;if(e instanceof KeyboardEvent&&e.key==="Enter"){e.preventDefault();e.stopPropagation()}const o=window.getSelection();if(!o||o.rangeCount===0)return;const s=o.getRangeAt(0);const n=s.startContainer;const r=s.endContainer;const a=new Set;let d=n.parentElement;while(d&&d!==r.parentElement){let t=d.nodeType===Node.TEXT_NODE?d.parentElement:d;if(t&&t.classList.contains("line")){a.add(t)}d=d.nextSibling||((i=d.parentElement)===null||i===void 0?void 0:i.nextSibling)}let c=r.nodeType===Node.TEXT_NODE?r.parentElement:r;while(c&&!c.classList.contains("line")&&c!==this.editor){c=c.parentElement}if(c&&c.classList.contains("line")){a.add(c)}a.forEach((t=>{t.innerHTML=t.textContent;t.style.textAlign=""}));this.wrapSelectionLine("p",true);o.removeAllRanges();o.addRange(s)}render(){var t,i;return e(o,{key:"186e17309019be4059b9d3a06c42e6b529372e99",class:{[`rich-text`]:true,[`rich-text-${this.positionBar}`]:true},style:{height:this.height,maxHeight:this.maxHeight},tabindex:"0",onMouseEnter:()=>this.insideComponent=true,onMouseLeave:()=>this.insideComponent=false},e("div",{key:"d1e58870cd9af2582c7ae1c83a2f6e58d22eb2b3",class:"preview"},e("div",{key:"1d1b4198db4a8a5644f86fa68a466d2dcd6a40c8","data-test":this.dataTest,ref:t=>this.refeditorElement(t),contentEditable:"true",class:"editor-uai-design-system",tabindex:"0",onBlur:this.onBlur,onFocus:this.onFocus,onMouseUp:()=>this.updateToolbarState(),onKeyUp:()=>this.updateToolbarState(),onKeyDown:t=>this.onKeydown(t),onInput:t=>this.onInput(t),onPaste:this.handlePaste.bind(this)})),e("bds-grid",{key:"c85400cb170be5c8ea50297a3f746165aacecb81",class:{[`format-buttons`]:true,[`format-buttons-active`]:this.weightButton||this.italicButton||this.strikeThroughButton||this.underlineButton||this.linkButton||this.codeButton||this.alignmentButtons||this.listButtons||this.quoteButton||this.headingButtons||this.unstyledButton}},e("div",{key:"2c4ee5806855a6b15c3e53328e7a3813312498b3",class:"accordion-header"},e("bds-grid",{key:"b752796b0ea174441a38925f8fe59b74fc3a26e5",ref:t=>this.refButtonsListElement(t),class:"buttons-list","flex-wrap":"wrap"},e("div",{key:"6fe823c099e63801bb3ff8f6e193c0a38cc513e3",onFocus:t=>this.onFocusEditorBar(t),tabindex:"1",class:"editor-bar"}),this.weightButton&&e("bds-tooltip",{key:"427394332b7fa230249a68ecb7254e5edbf34ba1","tooltip-text":`${c(this.language,"bold")}`,position:"top-center"},e("bds-button",{key:"a7f33e90578999586d0ba816c294cb4863805013",variant:this.buttomBoldActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"b"),"icon-left":"text-style-bold","aria-label":`${c(this.language,"bold")}`})),this.italicButton&&e("bds-tooltip",{key:"96a94d6cf136b30f7bddc911f9f8fe5ad6289591","tooltip-text":`${c(this.language,"italic")}`,position:"top-center"},e("bds-button",{key:"6657f3231b4c315326b917ea56115afe32168e96",variant:this.buttomItalicActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"i"),"icon-left":"text-style-italic","aria-label":`${c(this.language,"italic")}`})),this.strikeThroughButton&&e("bds-tooltip",{key:"d68bad409237d09237a364c71a56d8d222c47534","tooltip-text":`${c(this.language,"strike")}`,position:"top-center"},e("bds-button",{key:"10d8f85e9ea1687d74d4f587cf24e40db0270c12",variant:this.buttomStrikeActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"strike"),"icon-left":"text-style-strikethrough","aria-label":`${c(this.language,"strike")}`})),this.underlineButton&&e("bds-tooltip",{key:"ebf985ddc138cd38eaa001cac0c0aba06f709aff","tooltip-text":`${c(this.language,"underline")}`,position:"top-center"},e("bds-button",{key:"b55eb643c802d67124df4dbc1c87d629979bbc74",variant:this.buttomUnderlineActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"u"),"icon-left":"text-style-underline","aria-label":`${c(this.language,"underline")}`})),this.linkButton&&e("bds-tooltip",{key:"d708047ad395c0daf141328c122b5309e92bade2","tooltip-text":`${c(this.language,"link")}`,position:"top-center"},this.buttomLinkActive?e("bds-button",{variant:"solid",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"a"),"icon-left":"link","aria-label":`${c(this.language,"link")}`}):e("bds-dropdown",{ref:t=>this.refDropDownLinkElement(t),activeMode:"click",position:"bottom-left"},e("div",{slot:"dropdown-activator"},e("bds-button",{slot:"dropdown-activator",variant:"text",color:"content",size:"short",onBdsClick:t=>this.addSelectionLink(t),"icon-left":"link","aria-label":`${c(this.language,"link")}`})),e("bds-grid",{padding:"half",alignItems:"center",gap:"half",slot:"dropdown-content"},e("bds-input",{ref:this.refInputSetLink,onBdsInput:t=>this.addLinkInput(t.detail),style:{flexShrink:"99999"},placeholder:"adcione o link aqui",onKeyDown:t=>this.createLinkKeyDown(t),tabindex:((t=this.dropDownLink)===null||t===void 0?void 0:t.open)?"1":"-1"}),e("bds-button",{disabled:this.buttomLinkValidDisabled,"icon-left":"check",onBdsClick:t=>this.createLink(t),tabindex:((i=this.dropDownLink)===null||i===void 0?void 0:i.open)?"1":"-1","aria-label":`${c(this.language,"link")}`})))),this.codeButton&&e("bds-tooltip",{key:"9f7a3a9d9726c2394f464e28a26f20424d5d6f36","tooltip-text":`${c(this.language,"code")}`,position:"top-center"},e("bds-button",{key:"004fdfd26c825986150c562e7d45efa1e4069111",variant:this.buttomCodeActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.wrapSelection(t,"code"),"icon-left":"code","aria-label":`${c(this.language,"code")}`})),this.alignmentButtons&&e("bds-tooltip",{key:"2d5d5100d2446c73a8e7c5c203bb0e2945f2fd9e","tooltip-text":`${c(this.language,"align_left")}`,position:"top-center"},e("bds-button",{key:"1219e820f01766b6a4db5b8d1da618ca08e63d83",variant:this.buttomAlignLeftActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.alignText(t,"left"),"icon-left":"align-left","aria-label":`${c(this.language,"align_left")}`})),this.alignmentButtons&&e("bds-tooltip",{key:"d0e9f10a0efa01fcdbb0245f9101908762c3ebff","tooltip-text":`${c(this.language,"align_center")}`,position:"top-center"},e("bds-button",{key:"68123791d60e36030043896d9ab538750d4716b6",variant:this.buttomAlignCenterActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.alignText(t,"center"),"icon-left":"align-center","aria-label":`${c(this.language,"align_center")}`})),this.alignmentButtons&&e("bds-tooltip",{key:"8b4f301100ebfb9e0e58fcdfc7713c6816c321a2","tooltip-text":`${c(this.language,"align_right")}`,position:"top-center"},e("bds-button",{key:"672c77eba1ce815776842a3f4540a4b681eaa3b0",variant:this.buttomAlignRightActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.alignText(t,"right"),"icon-left":"align-right","aria-label":`${c(this.language,"align_right")}`})),this.listButtons&&e("bds-tooltip",{key:"6b3ff21f4d56c9bf7f8c629916bec80ee252651e","tooltip-text":`${c(this.language,"unordered_list")}`,position:"top-center"},e("bds-button",{key:"4e2558a7597dc0d26c50904b9879af4c247a02c1",variant:this.buttomUnorderedListActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createList(t,"ul"),"icon-left":"unordered-list","aria-label":`${c(this.language,"unordered_list")}`})),this.listButtons&&e("bds-tooltip",{key:"70fe57225a58f851f6a492cbe8955833040bbf9f","tooltip-text":`${c(this.language,"ordered_list")}`,position:"top-center"},e("bds-button",{key:"fb5323419093038fcc9fcf16b5d33d3fe9c207cd",variant:this.buttomOrderedListActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createList(t,"ol"),"icon-left":"ordered-list","aria-label":`${c(this.language,"ordered_list")}`})),this.quoteButton&&e("bds-tooltip",{key:"fb39a22d455c56a5377395e628ccbe323756de43","tooltip-text":`${c(this.language,"quote")}`,position:"top-center"},e("bds-button",{key:"9de29cf1d9e87b11ca0ea80bbf8d8cf0fc4e42df",variant:this.buttomQuoteActive?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"blockquote"),"icon-left":"quote","aria-label":`${c(this.language,"quote")}`})),this.headingButtons&&e("bds-tooltip",{key:"d0307061b0d123ce2cfa744bd0bdc977e1a0ec09","tooltip-text":`${c(this.language,"h1")}`,position:"top-center"},e("bds-button",{key:"2df004ae4b50d1bd60338e97e0f28e4133058d6c",variant:this.buttomH1Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h1"),"icon-left":"h-1","aria-label":`${c(this.language,"h1")}`})),this.headingButtons&&e("bds-tooltip",{key:"22e5b80ccbfb71a977eaf2027d692624b98c84f9","tooltip-text":`${c(this.language,"h2")}`,position:"top-center"},e("bds-button",{key:"91759e8841bb5af7b32146eb3ab600426fb52b89",variant:this.buttomH2Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h2"),"icon-left":"h-2","aria-label":`${c(this.language,"h2")}`})),this.headingButtons&&e("bds-tooltip",{key:"920eb3ef7eedca85b222be90cf262b8f53165ed2","tooltip-text":`${c(this.language,"h3")}`,position:"top-center"},e("bds-button",{key:"f0f72edf697ff01633ecddf4534113351f12e3bb",variant:this.buttomH3Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h3"),"icon-left":"h-3","aria-label":`${c(this.language,"h3")}`})),this.headingButtons&&e("bds-tooltip",{key:"cd4e1943f0ca21b68095f40919ec35f249d9c5f8","tooltip-text":`${c(this.language,"h4")}`,position:"top-center"},e("bds-button",{key:"08e0675464dd50e07f64f850eb0ced4c6356815d",variant:this.buttomH4Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h4"),"icon-left":"h-4","aria-label":`${c(this.language,"h4")}`})),this.headingButtons&&e("bds-tooltip",{key:"d0c2a080ca083b864ab1ab96f30a3ca2c367d18e","tooltip-text":`${c(this.language,"h5")}`,position:"top-center"},e("bds-button",{key:"cbf3b821977b624ad0143b281cb0cec31a2ca36b",variant:this.buttomH5Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h5"),"icon-left":"h-5","aria-label":`${c(this.language,"h5")}`})),this.headingButtons&&e("bds-tooltip",{key:"6d54c84fad1807013bdd5e7b9078cb828dbfe6cf","tooltip-text":`${c(this.language,"h6")}`,position:"top-center"},e("bds-button",{key:"01104398593bb297a95338b861a433831e791a42",variant:this.buttomH6Active?"solid":"text",color:"content",size:"short",onBdsClick:t=>this.createHeading(t,"h6"),"icon-left":"h-6","aria-label":`${c(this.language,"h6")}`})),this.unstyledButton&&e("bds-tooltip",{key:"e8bf9cedb09867c4e1c477e12a8849c2facd43b2","tooltip-text":`${c(this.language,"clear_formatting")}`,position:"top-center"},e("bds-button",{key:"daac2b7cf9aa69f7f05343120654837d8316bfd0",variant:"text",color:"content",size:"short",onBdsClick:t=>this.clearFormatting(t),"icon-left":"unstyled","aria-label":`${c(this.language,"clear_formatting")}`}))),e("bds-button",{key:"23921ffe445c0759825961da4f85db1f1dc14843",id:"buttonAccordion",variant:this.buttomAccordionActive?"solid":"text",class:"arrow-down",color:"content",size:"short",onBdsClick:()=>this.setheaderHeight(),"icon-left":this.positionBar=="top"?this.buttomAccordionActive?"arrow-up":"arrow-down":this.buttomAccordionActive?"arrow-down":"arrow-up"}))))}get el(){return s(this)}static get watchers(){return{weightButton:["buttomsHeaderChanged"],italicButton:["buttomsHeaderChanged"],strikeThroughButton:["buttomsHeaderChanged"],underlineButton:["buttomsHeaderChanged"],linkButton:["buttomsHeaderChanged"],codeButton:["buttomsHeaderChanged"],alignmentButtons:["buttomsHeaderChanged"],listButtons:["buttomsHeaderChanged"],quoteButton:["buttomsHeaderChanged"],headingButtons:["buttomsHeaderChanged"],unstyledButton:["buttomsHeaderChanged"],buttomAccordionActive:["buttomAccordionActiveChanged"],treeElementsEditor:["treeElementsEditorChanged"]}}};h.style=l;export{h as bds_rich_text};
//# sourceMappingURL=p-789ec288.entry.js.map