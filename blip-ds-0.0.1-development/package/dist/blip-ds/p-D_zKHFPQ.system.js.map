{"version": 3, "file": "p-D_zKHFPQ.system.js", "sources": ["src/components/dropdown/dropdown.scss?tag=bds-dropdown&encapsulation=shadow", "src/components/dropdown/dropdown.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  position: relative;\n  width: fit-content;\n}\n\n:host(.is_child_drop) {\n  display: block;\n  width: 100%;\n}\n\n.dropdown {\n  position: absolute;\n  pointer-events: none;\n  padding: 2px;\n  background-color: $color-surface-0;\n  border-radius: 8px;\n  box-shadow: $shadow-2;\n  min-width: 240px;\n  width: max-content;\n  opacity: 0;\n  -webkit-transition: opacity 0.5s;\n  -moz-transition: opacity 0.5s;\n  transition: opacity 0.5s;\n  z-index: $zindex-modal;\n\n  &__open {\n    pointer-events: auto;\n    opacity: 1;\n  }\n\n  &__basic {\n    &__top-center {\n      bottom: calc(100% + 16px);\n      left: calc(50% - 122px);\n    }\n    &__top-left {\n      bottom: calc(100% + 16px);\n      left: 0;\n    }\n    &__top-right {\n      bottom: calc(100% + 16px);\n      right: 0;\n    }\n    &__bottom-center {\n      top: calc(100% + 16px);\n      left: calc(50% - 122px);\n    }\n    &__bottom-right {\n      top: calc(100% + 16px);\n      right: 0;\n    }\n    &__bottom-left {\n      top: calc(100% + 16px);\n      left: 0;\n    }\n    &__right-center {\n      right: calc(100% + 8px);\n    }\n    &__right-top {\n      right: calc(100% + 8px);\n      top: 0;\n    }\n    &__right-bottom {\n      right: calc(100% + 8px);\n      bottom: 0;\n    }\n    &__left-center {\n      left: calc(100% + 8px);\n    }\n    &__left-top {\n      left: calc(100% + 8px);\n      top: 0;\n    }\n    &__left-bottom {\n      left: calc(100% + 8px);\n      bottom: 0;\n    }\n  }\n  &:after {\n    content: '';\n    position: absolute;\n    inset: 0;\n    border-radius: 8px;\n    box-shadow: $color-surface-0 0px 0px 0px 2px inset;\n    pointer-events: none;\n  }\n}\n\n.outzone {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  z-index: $zindex-modal-overlay;\n}\n", "import {\n  Component,\n  Host,\n  ComponentInterface,\n  h,\n  Element,\n  State,\n  Method,\n  Prop,\n  Event,\n  EventEmitter,\n  Watch,\n} from '@stencil/core';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type activeMode = 'hover' | 'click';\nexport type dropVerticalPosition = 'bottom' | 'top' | 'left' | 'right';\nexport type dropHorizontalPosition = 'left' | 'center' | 'right' | 'bottom' | 'top';\n//^^ dropHorizontalPosition: For version 2.0 change to values: \"start\", \"center\", \"end\". ^^//\nexport type subMenuState = 'close' | 'pending' | 'open';\n\nexport type DropdownPostionType =\n  | 'auto'\n  | 'top-center'\n  | 'top-left'\n  | 'top-right'\n  | 'bottom-center'\n  | 'bottom-right'\n  | 'bottom-left'\n  | 'right-center'\n  | 'right-top'\n  | 'right-bottom'\n  | 'left-center'\n  | 'left-top'\n  | 'left-bottom';\n\n@Component({\n  tag: 'bds-dropdown',\n  styleUrl: 'dropdown.scss',\n  shadow: true,\n})\nexport class BdsDropdown implements ComponentInterface {\n  private activatorElement?: Element;\n  private dropElement?: HTMLElement;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() stateOpenSubMenu?: boolean = false;\n  @State() stateSubMenu?: subMenuState = 'close';\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop() public activeMode?: activeMode = 'click';\n\n  /**\n   * Open. Used to open/close the dropdown.\n   */\n  @Prop({ mutable: true, reflect: true }) public open?: boolean = false;\n\n  /**\n   * Used to set drop position\n   */\n  @Prop() position?: DropdownPostionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * bdsToggle. Event to return selected date value.\n   */\n  @Event() bdsToggle?: EventEmitter;\n\n  componentWillLoad() {\n    this.activatorElement = this.hostElement.querySelector('[slot=\"dropdown-activator\"]').children[0];\n    this.intoView = getScrollParent(this.hostElement);\n    this.isPositionChanged;\n    if (this.activeMode == 'hover') {\n      this.activatorElement.addEventListener('mouseover', () => this.onMouseOver());\n      this.activatorElement.addEventListener('click', () => this.onMouseOver());\n      this.activatorElement.addEventListener('mouseout', () => this.onMouseOut());\n    } else {\n      this.activatorElement.addEventListener('click', () => this.toggle());\n    }\n  }\n\n  componentDidLoad() {\n    if (this.position != 'auto') {\n      this.centerDropElement(this.position);\n      this.setDefaultPlacement(this.position);\n    } else {\n      this.validatePositionDrop();\n    }\n\n    document.addEventListener('click', this.handleClickOutside);\n  }\n\n  disconnectedCallback() {\n    document.removeEventListener('click', this.handleClickOutside);\n  }\n\n  private setDefaultPlacement(value: DropdownPostionType) {\n    this.dropElement.classList.add(`dropdown__basic__${value}`);\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.hostElement,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.dropElement.classList.add(`dropdown__basic__${positionValue.y}-${positionValue.x}`);\n  }\n\n  @Watch('open')\n  protected isOpenChanged(open: boolean): void {\n    this.bdsToggle.emit({ value: open });\n    if (open)\n      if (this.position != 'auto') {\n        this.setDefaultPlacement(this.position);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('position')\n  protected isPositionChanged(): void {\n    this.setDefaultPlacement(this.position);\n  }\n\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Method()\n  async setOpen() {\n    this.open = true;\n  }\n\n  @Method()\n  async setClose() {\n    this.stateOpenSubMenu = false;\n    clearTimeout(this.delay);\n    this.open = false;\n  }\n\n  @Watch('stateOpenSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n    return;\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: subMenuState): void {\n    switch (state) {\n      case 'open':\n        this.open = true;\n        break;\n      case 'pending':\n        this.open = true;\n        break;\n      case 'close':\n        this.open = false;\n        break;\n    }\n  }\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  private refDropElement = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private onClickCloseButtom = () => {\n    this.open = false;\n  };\n\n  private onMouseOver = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 1;\n    }\n    this.stateOpenSubMenu = true;\n  };\n\n  private onMouseOut = () => {\n    if (this.activeMode === 'hover') {\n      this.zIndex = 0;\n      this.stateOpenSubMenu = false;\n    }\n  };\n\n  private handleClickOutside = (event: MouseEvent) => {\n    if (this.open && !this.hostElement.contains(event.target as Node)) {\n      this.setClose();\n    }\n  };\n\n  private centerDropElement = (value: DropdownPostionType) => {\n    const arrayPosition = value.split('-');\n    if ((arrayPosition[0] == 'left' || arrayPosition[0] == 'right') && arrayPosition[1] == 'center') {\n      this.dropElement.style.top = `calc(50% - ${this.dropElement.offsetHeight / 2}px)`;\n    }\n  };\n\n  render() {\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n    return (\n      <Host>\n        <slot name=\"dropdown-activator\"></slot>\n        <div\n          ref={(el) => this.refDropElement(el)}\n          class={{\n            dropdown: true,\n            dropdown__open: this.open,\n          }}\n          data-test={this.dataTest}\n          onMouseOver={() => this.onMouseOver()}\n          onMouseOut={() => this.onMouseOut()}\n        >\n          <div class=\"content\" style={zIndexSubmenu}>\n            <slot name=\"dropdown-content\"></slot>\n          </div>\n        </div>\n        {this.activeMode !== 'hover' && this.open && (\n          <div class={{ outzone: true }} onClick={() => this.onClickCloseButtom()}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;MAAA,MAAM,WAAW,GAAG,osDAAosD;;YCyC3sD,WAAW,2BAAA,MAAA;MALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;MAWW,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;MAE7B,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;MAClC,QAAA,IAAY,CAAA,YAAA,GAAkB,OAAO;MACrC,QAAA,IAAM,CAAA,MAAA,GAAY,CAAC;MACnB,QAAA,IAAK,CAAA,KAAA,GAAG,IAAI;MAErB;;MAEG;MACY,QAAA,IAAU,CAAA,UAAA,GAAgB,OAAO;MAEhD;;MAEG;MAC4C,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;MAErE;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAyB,MAAM;MAE/C;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MA8GxB,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;MAClC,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO;MAC7B,SAAC;MAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAe,KAAU;MACjD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;MACvB,SAAC;MAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAK;MAChC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK;MACnB,SAAC;MAEO,QAAA,IAAW,CAAA,WAAA,GAAG,MAAK;MACzB,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE;MAC/B,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC;;MAEjB,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;MAC9B,SAAC;MAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAK;MACxB,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE;MAC/B,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC;MACf,gBAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;MAEjC,SAAC;MAEO,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,KAAiB,KAAI;MACjD,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE;sBACjE,IAAI,CAAC,QAAQ,EAAE;;MAEnB,SAAC;MAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,KAA0B,KAAI;kBACzD,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;kBACtC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,OAAO,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;MAC/F,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,CAAA,WAAA,EAAc,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,KAAK;;MAErF,SAAC;MA6BF;UAzKC,iBAAiB,GAAA;MACf,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;cACjG,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;MAEjD,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,EAAE;MAC9B,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;MAC7E,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;MACzE,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;;mBACtE;MACL,YAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;;;UAIxE,gBAAgB,GAAA;MACd,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE;MAC3B,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;MACrC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;;mBAClC;kBACL,IAAI,CAAC,oBAAoB,EAAE;;cAG7B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;;UAG7D,oBAAoB,GAAA;cAClB,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;;MAGxD,IAAA,mBAAmB,CAAC,KAA0B,EAAA;cACpD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAoB,iBAAA,EAAA,KAAK,CAAE,CAAA,CAAC;;UAGrD,oBAAoB,GAAA;cAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;kBAC5C,aAAa,EAAE,IAAI,CAAC,WAAW;kBAC/B,cAAc,EAAE,IAAI,CAAC,WAAW;kBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;MACxB,SAAA,CAAC;MACF,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,iBAAA,EAAoB,aAAa,CAAC,CAAC,CAAI,CAAA,EAAA,aAAa,CAAC,CAAC,CAAA,CAAE,CAAC;;MAIhF,IAAA,aAAa,CAAC,IAAa,EAAA;cACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;MACpC,QAAA,IAAI,IAAI;MACN,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,EAAE;MAC3B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;;uBAClC;sBACL,IAAI,CAAC,oBAAoB,EAAE;;;UAKvB,iBAAiB,GAAA;MACzB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;;MAIzC,IAAA,MAAM,MAAM,GAAA;MACV,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI;;MAIxB,IAAA,MAAM,OAAO,GAAA;MACX,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;MAIlB,IAAA,MAAM,QAAQ,GAAA;MACZ,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;MAC7B,QAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;MACxB,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK;;MAIT,IAAA,kBAAkB,CAAC,MAAe,EAAA;MAC1C,QAAA,IAAI,MAAM,IAAI,KAAK,EAAE;MACnB,YAAA,IAAI,CAAC,YAAY,GAAG,SAAS;kBAC7B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;;MAEpD,QAAA,IAAI,MAAM,IAAI,IAAI,EAAE;MAClB,YAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;MACxB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;MACjB,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;;cAE5B;;MAIQ,IAAA,mBAAmB,CAAC,KAAmB,EAAA;cAC/C,QAAQ,KAAK;MACX,YAAA,KAAK,MAAM;MACT,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI;sBAChB;MACF,YAAA,KAAK,SAAS;MACZ,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI;sBAChB;MACF,YAAA,KAAK,OAAO;MACV,gBAAA,IAAI,CAAC,IAAI,GAAG,KAAK;sBACjB;;;UA2CN,MAAM,GAAA;MACJ,QAAA,MAAM,aAAa,GAAG;MACpB,YAAA,MAAM,EAAE,CAAA,EAAG,IAAI,CAAC,MAAM,CAAE,CAAA;eACzB;cACD,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,oBAAoB,EAAQ,CAAA,EACvC,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EACpC,KAAK,EAAE;MACL,gBAAA,QAAQ,EAAE,IAAI;sBACd,cAAc,EAAE,IAAI,CAAC,IAAI;mBAC1B,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,EACrC,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,EAAA,EAEnC,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,SAAS,EAAC,KAAK,EAAE,aAAa,EAAA,EACvC,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,kBAAkB,EAAA,CAAQ,CACjC,CACF,EACL,IAAI,CAAC,UAAU,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KACvC,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAAA,CAAQ,CAChF,CACI;;;;;;;;;;;;;;;;;;"}