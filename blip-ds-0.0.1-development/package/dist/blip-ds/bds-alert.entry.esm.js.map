{"version": 3, "file": "bds-alert.entry.esm.js", "sources": ["src/components/alert/alert.scss?tag=bds-alert&encapsulation=shadow", "src/components/alert/alert.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.alert__dialog {\n  opacity: 0;\n  visibility: hidden;\n  background-color: rgba(0, 0, 0, 0.7);\n  width: 100%;\n  height: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: opacity 0.3s ease-in-out;\n  z-index: $zindex-modal-overlay;\n\n  .alert {\n    position: relative;\n    margin: 48px auto 0;\n    overflow: hidden;\n    max-width: 424px;\n    border-radius: 8px;\n    background: $color-surface-1;\n    box-shadow: $shadow-3;\n  }\n\n  &--open {\n    opacity: 1;\n    visibility: visible;\n  }\n  &--fixed {\n    position: fixed;\n  }\n  &--contain {\n    position: absolute;\n  }\n}\n", "import { Component, ComponentInterface, h, Method, Prop, Event, EventEmitter, Watch } from '@stencil/core';\n\nexport type collapses = 'fixed' | 'contain';\n@Component({\n  tag: 'bds-alert',\n  styleUrl: 'alert.scss',\n  shadow: true,\n})\nexport class BdsAlert implements ComponentInterface {\n  /**\n   * Used to open/close the alert\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Define whether the component will occupy the entire screen or just the parent.\n   */\n  @Prop() position?: string = 'fixed';\n\n  /**\n   * Emitted when modal status has changed.\n   */\n  @Event() bdsAlertChanged!: EventEmitter;\n\n  /**\n   * Can be used outside to open/close the alert\n   */\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n\n    if (this.open) {\n      this.bdsAlertChanged.emit({ alertStatus: 'opened' });\n    } else {\n      this.bdsAlertChanged.emit({ alertStatus: 'closed' });\n    }\n  }\n\n  @Watch('open')\n  protected isOpenChanged(): void {\n    if (this.open) {\n      document.addEventListener('keydown', this.listener, false);\n    } else document.removeEventListener('keydown', this.listener, false);\n  }\n\n  private listener = (event) => {\n    if (event.key == 'Enter' || event.key == 'Escape') {\n      this.toggle();\n    }\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          alert__dialog: true,\n          'alert__dialog--open': this.open,\n          [`alert__dialog--${this.position}`]: true,\n        }}\n      >\n        <div class=\"alert\" data-test={this.dataTest}>\n          <slot></slot>\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,QAAQ,GAAG,0xBAA0xB;;MCQ9xB,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAME;;AAEG;AAKI,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;AAE7B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAEhC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,OAAO;AA4B3B,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,KAAK,KAAI;AAC3B,YAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,QAAQ,EAAE;gBACjD,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC;AAiBF;AA1CC;;AAEG;AAEH,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI;AAEtB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;;aAC/C;YACL,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;;;IAK9C,aAAa,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;;;YACrD,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;;IAStE,MAAM,GAAA;QACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;gBACnB,qBAAqB,EAAE,IAAI,CAAC,IAAI;AAChC,gBAAA,CAAC,kBAAkB,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;AAC1C,aAAA,EAAA,EAED,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,OAAO,EAAY,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EACzC,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACT,CACF;;;;;;;;;;"}