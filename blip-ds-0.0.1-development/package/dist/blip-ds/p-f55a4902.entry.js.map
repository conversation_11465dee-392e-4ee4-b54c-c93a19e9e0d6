{"version": 3, "names": ["dataTableCss", "DataTable", "constructor", "hostRef", "this", "newTable", "headerData", "tableData", "avatar", "chips", "sorting", "componentWillLoad", "getDataFromProprety", "JSON", "parse", "column", "options", "renderArrow", "value", "h", "name", "size", "deleteItem", "index", "itemDelete", "filter", "item", "i", "bdsTableDelete", "emit", "splice", "bdsTableChange", "clickButton", "btn", "bdsTableClick", "nameButton", "orderColumn", "idx", "headerActive", "sortAscending", "sort", "a", "b", "render", "Host", "key", "class", "map", "onClick", "variant", "bold", "heading", "columnItem", "actionArea", "editAction", "icon", "deleteAction", "customAction", "color", "img", "thumbnail"], "sources": ["src/components/table/data-table.scss?tag=bds-data-table&encapsulation=shadow", "src/components/table/data-table.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n\n  .table {\n    display: grid;\n    font-family: $font-family;\n    color: $color-content-default;\n    width: 100%;\n    border: 1px solid $color-border-3;\n    border-radius: 8px;\n    overflow-x: auto;\n    background-color: $color-surface-1;\n\n    .thead {\n      border-bottom: 1px solid $color-border-1;\n      padding: 0 16px;\n      .header {\n        display: flex;\n        flex-direction: row;\n        justify-content: space-between;\n        text-align: left;\n        align-items: center;\n        height: 64px;\n        gap: 16px;\n        .header-title {\n          height: 64px;\n          width: 100%;\n          display: flex;\n          flex-direction: row;\n          align-items: center;\n          justify-content: flex-start;\n          gap: 8px;\n          \n          .title-click {\n            cursor: pointer;\n          }\n        }\n      }\n    }\n\n    .body-row {\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      height: 64px;\n      padding: 0 16px;\n      gap: 16px;\n      border-bottom: 1px solid $color-border-2;\n\n      .body-item {\n        height: 48px;\n        width: 100%;\n        gap: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n      }\n    }\n\n    .body-row:last-child {\n      border-bottom: none;\n    }\n  }\n}\n", "import { Component, Host, h, Prop, State, Element, Event, EventEmitter, Method } from '@stencil/core';\n\ntype Data = {\n  [key: string]: any;\n};\n@Component({\n  tag: 'bds-data-table',\n  styleUrl: 'data-table.scss',\n  shadow: true,\n})\nexport class DataTable {\n  @Element() el!: HTMLElement;\n  @State() newTable: Data = [];\n  /**\n   * For keep the Object of header;\n   */\n  @State() headerData?: Data = [];\n  /**\n   * For keep the Object of table content.\n   */\n  @State() tableData?: Data[] = [];\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() sortAscending?: boolean;\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() headerActive: string;\n  /**\n   * Prop to recive the content of the table.\n   */\n  @Prop() options?: string;\n  /**\n   * Prop to recive the header and configuration of table.\n   */\n  @Prop() column?: string;\n  /**\n   * Prop to activate the possibility of use avatar in any column.\n   */\n  @Prop() avatar?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() chips?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() actionArea?: boolean;\n  /**\n   * Prop to activate the sorting.\n   */\n  @Prop() sorting?: boolean = false;\n  @Event() bdsTableClick: EventEmitter;\n  @Event() bdsTableDelete: EventEmitter;\n  @Event() bdsTableChange: EventEmitter;\n\n  componentWillLoad() {\n    this.getDataFromProprety();\n  }\n\n  private getDataFromProprety() {\n    this.headerData = JSON.parse(this.column);\n    this.tableData = JSON.parse(this.options);\n  }\n\n  renderArrow(value) {\n    if (value) {\n      return <bds-icon name=\"arrow-up\" size=\"small\"></bds-icon>;\n    } else {\n      return null;\n    }\n  }\n\n  @Method()\n  async deleteItem(index: number) {\n    const itemDelete = this.tableData.filter((item, i) => i === index && item);\n    this.bdsTableDelete.emit(itemDelete[0]);\n    this.tableData.splice(index, 1);\n    this.tableData = [...this.tableData];\n    this.bdsTableChange.emit(this.tableData);\n  }\n\n  clickButton(item, index, btn) {\n    this.bdsTableClick.emit({ item: item, index: index, nameButton: btn });\n  }\n\n  orderColumn(idx) {\n    this.headerActive = idx;\n    this.sortAscending = this.sortAscending ? false : true;\n\n    if (this.sortAscending === false) {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? 1 : -1;\n      });\n    } else {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? -1 : 1;\n      });\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <table class=\"table\">\n          <thead class=\"thead\">\n            <tr class=\"header\">\n              {this.headerData.map((item, index) => (\n                <th class=\"header-title\" key={index}>\n                  {this.sorting ? (\n                    <bds-typo\n                      class=\"title-click\"\n                      onClick={() => this.orderColumn(item.value)}\n                      variant=\"fs-14\"\n                      bold={this.headerActive === `${item.value}` ? 'bold' : 'semi-bold'}\n                    >\n                      {item.heading}\n                    </bds-typo>\n                  ) : (\n                    <bds-typo variant=\"fs-14\" bold=\"semi-bold\">\n                      {item.heading}\n                    </bds-typo>\n                  )}\n                  {this.sortAscending === true && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon class=\"header-icon\" name=\"arrow-up\" size=\"small\"></bds-icon>\n                  ) : this.sortAscending === false && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon name=\"arrow-down\" size=\"small\"></bds-icon>\n                  ) : (\n                    ''\n                  )}\n                </th>\n              ))}\n            </tr>\n          </thead>\n          <tbody>\n            {this.tableData.map((item, index) => (\n              <tr class=\"body-row\" key={index}>\n                {this.headerData.map((columnItem, idx) => {\n                  return (\n                    <td class=\"body-item\" key={idx}>\n                      {this.actionArea && columnItem.editAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.editAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.editAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.deleteAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.deleteAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.deleteAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.customAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.customAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.customAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.chips && columnItem.chips ? (\n                        <bds-chip-tag color={item[`${columnItem.chips}`] ? item[`${columnItem.chips}`] : 'default'}>\n                          {item[`${columnItem.value}`]}\n                        </bds-chip-tag>\n                      ) : (\n                        ''\n                      )}\n                      {this.avatar && columnItem.img ? (\n                        <bds-avatar\n                          size=\"extra-small\"\n                          thumbnail={item[`${columnItem.img}`]}\n                          name={item[`${columnItem.value}`]}\n                        ></bds-avatar>\n                      ) : (\n                        ''\n                      )}\n                      {columnItem.chips ? (\n                        ''\n                      ) : (\n                        <bds-typo\n                          variant=\"fs-14\"\n                          bold={this.headerActive === `${columnItem.value}` ? 'bold' : 'regular'}\n                        >\n                          {item[`${columnItem.value}`]}\n                        </bds-typo>\n                      )}\n                    </td>\n                  );\n                })}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </Host>\n    );\n  }\n}\n"], "mappings": "2DAAA,MAAMA,EAAe,2/C,MCURC,EAAS,MALtB,WAAAC,CAAAC,G,+IAOWC,KAAQC,SAAS,GAIjBD,KAAUE,WAAU,GAIpBF,KAASG,UAAY,GAoBtBH,KAAMI,OAAa,MAInBJ,KAAKK,MAAa,MAQlBL,KAAOM,QAAa,KA2J7B,CAtJC,iBAAAC,GACEP,KAAKQ,qB,CAGC,mBAAAA,GACNR,KAAKE,WAAaO,KAAKC,MAAMV,KAAKW,QAClCX,KAAKG,UAAYM,KAAKC,MAAMV,KAAKY,Q,CAGnC,WAAAC,CAAYC,GACV,GAAIA,EAAO,CACT,OAAOC,EAAA,YAAUC,KAAK,WAAWC,KAAK,S,KACjC,CACL,OAAO,I,EAKX,gBAAMC,CAAWC,GACf,MAAMC,EAAapB,KAAKG,UAAUkB,QAAO,CAACC,EAAMC,IAAMA,IAAMJ,GAASG,IACrEtB,KAAKwB,eAAeC,KAAKL,EAAW,IACpCpB,KAAKG,UAAUuB,OAAOP,EAAO,GAC7BnB,KAAKG,UAAY,IAAIH,KAAKG,WAC1BH,KAAK2B,eAAeF,KAAKzB,KAAKG,U,CAGhC,WAAAyB,CAAYN,EAAMH,EAAOU,GACvB7B,KAAK8B,cAAcL,KAAK,CAAEH,KAAMA,EAAMH,MAAOA,EAAOY,WAAYF,G,CAGlE,WAAAG,CAAYC,GACVjC,KAAKkC,aAAeD,EACpBjC,KAAKmC,cAAgBnC,KAAKmC,cAAgB,MAAQ,KAElD,GAAInC,KAAKmC,gBAAkB,MAAO,CAChCnC,KAAKG,UAAUiC,MAAK,SAAUC,EAAGC,GAC/B,OAAOD,EAAEJ,GAAOK,EAAEL,GAAO,GAAI,CAC/B,G,KACK,CACLjC,KAAKG,UAAUiC,MAAK,SAAUC,EAAGC,GAC/B,OAAOD,EAAEJ,GAAOK,EAAEL,IAAO,EAAK,CAChC,G,EAIJ,MAAAM,GACE,OACExB,EAACyB,EAAI,CAAAC,IAAA,4CACH1B,EAAO,SAAA0B,IAAA,2CAAAC,MAAM,SACX3B,EAAO,SAAA0B,IAAA,2CAAAC,MAAM,SACX3B,EAAI,MAAA0B,IAAA,2CAAAC,MAAM,UACP1C,KAAKE,WAAWyC,KAAI,CAACrB,EAAMH,IAC1BJ,EAAA,MAAI2B,MAAM,eAAeD,IAAKtB,GAC3BnB,KAAKM,QACJS,EAAA,YACE2B,MAAM,cACNE,QAAS,IAAM5C,KAAKgC,YAAYV,EAAKR,OACrC+B,QAAQ,QACRC,KAAM9C,KAAKkC,eAAiB,GAAGZ,EAAKR,QAAU,OAAS,aAEtDQ,EAAKyB,SAGRhC,EAAU,YAAA8B,QAAQ,QAAQC,KAAK,aAC5BxB,EAAKyB,SAGT/C,KAAKmC,gBAAkB,MAAQnC,KAAKM,UAAY,MAAQN,KAAKkC,eAAiB,GAAGZ,EAAKR,QACrFC,EAAA,YAAU2B,MAAM,cAAc1B,KAAK,WAAWC,KAAK,UACjDjB,KAAKmC,gBAAkB,OAASnC,KAAKM,UAAY,MAAQN,KAAKkC,eAAiB,GAAGZ,EAAKR,QACzFC,EAAA,YAAUC,KAAK,aAAaC,KAAK,UAAmB,QAQ9DF,EAAA,SAAA0B,IAAA,4CACGzC,KAAKG,UAAUwC,KAAI,CAACrB,EAAMH,IACzBJ,EAAA,MAAI2B,MAAM,WAAWD,IAAKtB,GACvBnB,KAAKE,WAAWyC,KAAI,CAACK,EAAYf,IAE9BlB,EAAA,MAAI2B,MAAM,YAAYD,IAAKR,GACxBjC,KAAKiD,YAAcD,EAAWE,WAC7BnC,EACE,mBAAA6B,QAAS,IAAM5C,KAAK4B,YAAYN,EAAMH,EAAO6B,EAAWE,YACxDL,QAAQ,YACRM,KAAM7B,EAAK,GAAG0B,EAAWE,cACzBjC,KAAK,UACY,GAIpBjB,KAAKiD,YAAcD,EAAWI,aAC7BrC,EACE,mBAAA6B,QAAS,IAAM5C,KAAK4B,YAAYN,EAAMH,EAAO6B,EAAWI,cACxDP,QAAQ,YACRM,KAAM7B,EAAK,GAAG0B,EAAWI,gBACzBnC,KAAK,UACY,GAIpBjB,KAAKiD,YAAcD,EAAWK,aAC7BtC,EACE,mBAAA6B,QAAS,IAAM5C,KAAK4B,YAAYN,EAAMH,EAAO6B,EAAWK,cACxDR,QAAQ,YACRM,KAAM7B,EAAK,GAAG0B,EAAWK,gBACzBpC,KAAK,UACY,GAIpBjB,KAAKK,OAAS2C,EAAW3C,MACxBU,EAAc,gBAAAuC,MAAOhC,EAAK,GAAG0B,EAAW3C,SAAWiB,EAAK,GAAG0B,EAAW3C,SAAW,WAC9EiB,EAAK,GAAG0B,EAAWlC,UACP,GAIhBd,KAAKI,QAAU4C,EAAWO,IACzBxC,EACE,cAAAE,KAAK,cACLuC,UAAWlC,EAAK,GAAG0B,EAAWO,OAC9BvC,KAAMM,EAAK,GAAG0B,EAAWlC,WACb,GAIfkC,EAAW3C,MAAK,GAGfU,EACE,YAAA8B,QAAQ,QACRC,KAAM9C,KAAKkC,eAAiB,GAAGc,EAAWlC,QAAU,OAAS,WAE5DQ,EAAK,GAAG0B,EAAWlC,kB", "ignoreList": []}