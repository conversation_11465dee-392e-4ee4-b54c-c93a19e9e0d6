{"version": 3, "file": "p-DimE3qJZ.system.js", "sources": ["src/components/upload/languages/pt_BR.tsx", "src/components/upload/languages/es_ES.tsx", "src/components/upload/languages/en_US.tsx", "src/components/upload/languages/index.ts", "src/assets/svg/pattern.svg", "src/components/upload/bds-upload.scss?tag=bds-upload&encapsulation=shadow", "src/components/upload/bds-upload.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    uploaded: 'Arquivos enviados',\n    dropHere: 'Solte aqui para anexar o arquivo',\n    dropOrClick: 'Arraste e solte seus arquivos aqui ou clique para fazer upload do arquivo',\n    formatError: 'Ocorreu um erro ao anexar o arquivo, tente novamente ou selecione outro arquivo',\n  },\n];\n", "export const esTerms = [\n  {\n    uploaded: 'Archivos subidos',\n    dropHere: 'Soltar aquí para adjuntar archivo',\n    dropOrClick: 'Arrastre y suelte sus archivos aquí o haga clic para cargar el archivo',\n    formatError: 'Se produjo un error al adjuntar el archivo, inténtelo nuevamente o seleccione otro archivo',\n  },\n];\n", "export const enTerms = [\n  {\n    uploaded: 'Files uploaded',\n    dropHere: 'Drop here to attach file',\n    dropOrClick: 'Drag and drop your files here or click to upload file',\n    formatError: 'There was an error attaching the file, please try again or select another file',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "<svg width=\"384\" height=\"80\" viewBox=\"0 0 384 80\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_3788_215640)\">\n<line x1=\"-10.9767\" y1=\"74.3843\" x2=\"22.7778\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"22.7777\" y1=\"74.3843\" x2=\"56.5322\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"56.532\" y1=\"74.3843\" x2=\"90.2866\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"90.2869\" y1=\"74.3843\" x2=\"124.042\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"124.041\" y1=\"74.3843\" x2=\"157.796\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"157.796\" y1=\"74.3843\" x2=\"191.551\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"191.551\" y1=\"74.3843\" x2=\"225.305\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"225.305\" y1=\"74.3843\" x2=\"259.06\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"259.06\" y1=\"74.3843\" x2=\"292.814\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"292.814\" y1=\"74.3843\" x2=\"326.569\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"326.569\" y1=\"74.3843\" x2=\"360.323\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n<line x1=\"360.324\" y1=\"74.3843\" x2=\"394.078\" y2=\"5.17719\" stroke=\"#E7EDF4\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_3788_215640\">\n<rect width=\"384\" height=\"80\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n", "@use '../../globals/helpers' as *;\n\n.upload {\n  min-width: 400px;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n\n  .upload-header {\n    display: flex;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    align-items: center;\n    gap: 8px;\n    color: $color-content-default;\n\n    &_text {\n      color: $color-content-default;\n      display: flex;\n      flex-direction: column;\n    }\n  }\n}\n\n.upload__edit--label {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border: 1px solid $color-border-1;\n  border-radius: 8px;\n  cursor: pointer;\n  font-weight: normal;\n  box-sizing: border-box;\n  padding: 23px 16px;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &::before {\n      border-color: $color-focus;\n    }\n  }\n\n  .upload__img--visible {\n    display: flex;\n    width: 100%;\n    height: 100%;\n    border-radius: 8px;\n    position: absolute;\n    background-color: $color-surface-2;\n    z-index: 1;\n  }\n\n  .text-box {\n    display: flex;\n    padding: 8px;\n    width: 100%;\n    text-align: center;\n    z-index: 2;\n    .text {\n      color: $color-content-default;\n      width: 100%;\n      flex-wrap: wrap;\n    }\n  }\n  .text-box--hover {\n    background-color: $color-surface-2;\n\n    .text {\n      color: $color-primary;\n    }\n  }\n}\n\n.upload__edit--label:hover {\n  border: 2px solid $color-primary;\n  box-sizing: border-box;\n  padding: 22px 16px;\n  cursor: pointer;\n  text-decoration: underline $color-primary;\n  color: $color-brand;\n\n  .text {\n    color: $color-primary;\n  }\n}\n\n.upload__edit--hover {\n  background-size: cover;\n  border: 1px dashed $color-surface-4;\n  color: $color-primary;\n  font-weight: bold;\n  border-radius: 8px;\n}\n\n.upload__img--invisible {\n  display: none;\n}\n\n.list-preview {\n  border-top: 1px solid $color-border-1;\n  border-bottom: 1px solid $color-border-1;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.upload__preview {\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n\n  padding: 16px 0;\n\n  .preview {\n    display: flex;\n    padding: 0 16px;\n    align-items: center;\n    justify-content: center;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    gap: 8px;\n\n    &-text {\n      font-family: $font-family;\n      font-size: 0.875rem;\n      font-weight: 700;\n      margin: 0;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      overflow: hidden;\n      width: 100%;\n      color: $color-content-default;\n    }\n    &-icon {\n      color: $color-content-default;\n    }\n    &-icon:hover {\n      cursor: pointer;\n    }\n  }\n}\n\n.preview-length {\n  display: flex;\n  justify-content: end;\n  padding-top: 16px;\n  text-align: end;\n}\n\n.upload__edit input {\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0;\n  width: 0;\n  height: 100%;\n}\n", "import { Component, h, Element, State, Prop, Method, Event, EventEmitter, Watch } from '@stencil/core';\nimport { termTranslate, languages } from './languages';\nimport background from '../../assets/svg/pattern.svg';\n\n@Component({\n  tag: 'bds-upload',\n  styleUrl: 'bds-upload.scss',\n  shadow: true,\n})\nexport class BdsUpload {\n  private inputElement?: HTMLInputElement;\n\n  @Element() private dropArea: HTMLElement;\n  @State() files: File[] = [];\n  @State() haveFiles = false;\n  @State() hover = false;\n  @State() background: string;\n  @State() size: number[] = [];\n  @State() internalAccepts: string[] = [];\n  @State() formatError = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * Used for add a text on title.\n   */\n  @Prop() titleName: string;\n  /**\n   * Used for add a text on subtitle.\n   */\n  @Prop() subtitle: string;\n  /**\n   * Used for add a error message. In case a verify.\n   */\n  @Prop({ reflect: true, mutable: true }) error: string;\n  /**\n   * Used to allow upload multiple files.\n   */\n  @Prop() multiple: boolean;\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() accept: string;\n\n  /**\n   * Used to accept a especific type of file.\n   */\n  @Prop() dataAccept: string[] | string = [];\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtInputFiles is the data-test to button clear.\n   */\n  @Prop() dtInputFiles?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtLabelAddFile is the data-test to button clear.\n   */\n  @Prop() dtLabelAddFile?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonDelete is the data-test to button clear.\n   */\n  @Prop() dtButtonDelete?: string = null;\n  /**\n   * Event emited when delete a item from the list.\n   */\n  @Event() bdsUploadDelete: EventEmitter;\n  /**\n   * Event emited when change the value of Upload.\n   */\n  @Event() bdsUploadChange: EventEmitter;\n\n  @Watch('dataAccept')\n  protected dataAcceptChanged(): void {\n    if (this.dataAccept) {\n      if (typeof this.dataAccept === 'string') {\n        try {\n          this.internalAccepts = JSON.parse(this.dataAccept);\n        } catch {\n          this.internalAccepts = [];\n        }\n      } else {\n        this.internalAccepts = this.dataAccept;\n      }\n    } else {\n      this.internalAccepts = [];\n    }\n  }\n\n  @Watch('files')\n  protected filesChanged(): void {\n    if (this.files.length > 0) {\n      for (let i = 0; i < this.files.length; i++) {\n        if (this.internalAccepts.length > 0) {\n          this.validationFiles(this.files[i], i);\n        }\n      }\n    }\n  }\n\n  @Watch('formatError')\n  protected formatErrorChanged(value): void {\n    if (value) {\n      this.error = termTranslate(this.language, 'formatError');\n      setTimeout(() => (this.error = null), 5000);\n    }\n  }\n\n  componentWillLoad() {\n    this.dataAcceptChanged();\n  }\n\n  componentDidLoad() {\n    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragenter', 'dragover'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(true), false);\n    });\n    ['dragleave', 'drop'].forEach((eventName) => {\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.preventDefaults, false);\n      this.dropArea.shadowRoot.addEventListener(eventName, () => this.hoverFile(false), false);\n    });\n    this.dropArea.shadowRoot.addEventListener('drop', this.handleDrop, false);\n  }\n\n  validationFiles = (File: File, index: number) => {\n    const filetype = `.${File.name.split('.').pop()}`;\n    const validate = this.internalAccepts.includes(filetype);\n    if (validate) {\n      this.formatError = false;\n      return;\n    } else {\n      this.formatError = true;\n      this.deleteFile(index);\n      return;\n    }\n  };\n\n  /**\n   * Recive the file data using drag and drop.\n   */\n  handleDrop = (Event) => {\n    this.haveFiles = true;\n    const dt = Event.dataTransfer;\n    const files = dt.files;\n    this.handleFiles(files);\n  };\n\n  /**\n   * Verify if allow the state recive one or more items.\n   */\n  handleFiles = (files) => {\n    if (!this.multiple) {\n      this.files = [files[0]];\n    } else {\n      this.files = [...this.files, ...files];\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  };\n  /**\n   * Prevent the screen to reload.\n   */\n  preventDefaults(e) {\n    e.preventDefault();\n    e.stopPropagation();\n  }\n  /**\n   * Definy if are hover to aply styles in drop area.\n   */\n  hoverFile(boolean) {\n    this.hover = boolean;\n  }\n  /**\n   * Recive the file data using click.\n   */\n  public onUploadClick(files) {\n    if (files.length > 0) {\n      if (!this.multiple) {\n        this.files = [files[0]];\n      } else {\n        this.files = [...this.files, ...files];\n      }\n      this.haveFiles = true;\n      this.getSize();\n    } else {\n      return false;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n  /**\n   * Return the size information from the file.\n   */\n  getSize() {\n    this.files.map((size: any) => {\n      const listSize = size.size;\n      this.size.push(listSize);\n    });\n  }\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteFile(index) {\n    const fileToDelete = this.files.filter((item, i) => i == index && item);\n    this.bdsUploadDelete.emit({ value: fileToDelete });\n    this.files.splice(index, 1);\n    this.files = [...this.files];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  /**\n   * Used for delete a item from the list.\n   */\n  @Method()\n  async deleteAllFiles() {\n    this.bdsUploadDelete.emit({ value: this.files });\n    this.files = [];\n    if (this.files.length === 0) {\n      this.haveFiles = false;\n    } else {\n      this.haveFiles = true;\n    }\n    this.bdsUploadChange.emit({ value: this.files });\n  }\n\n  private refInputElement = (el: HTMLInputElement): void => {\n    this.inputElement = el as HTMLInputElement;\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.inputElement.click();\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"upload\">\n        <div class=\"upload-header\">\n          <bds-icon class=\"upload-header_icon\" size=\"xxx-large\" name=\"upload\"></bds-icon>\n          <div class=\"upload-header_text\">\n            <bds-typo variant=\"fs-16\" bold=\"bold\" aria-label={this.titleName}>\n              {this.titleName}\n            </bds-typo>\n            <bds-typo variant=\"fs-14\" bold=\"regular\" aria-label={this.subtitle}>\n              {this.subtitle}\n            </bds-typo>\n          </div>\n        </div>\n        {this.error ? (\n          <bds-banner context=\"inside\" variant=\"error\" aria-label={this.error}>\n            {this.error}\n          </bds-banner>\n        ) : (\n          ''\n        )}\n        {this.haveFiles ? (\n          <div>\n            <div class=\"list-preview\">\n              {this.files.map((names: any, index) => (\n                <div class=\"upload__preview\" key={index} id=\"drop-area\">\n                  <div class=\"preview\" id=\"preview\">\n                    <bds-icon size=\"x-small\" name=\"attach\"></bds-icon>\n                    <p class=\"preview-text\" id=\"preview-text\" aria-label={names.name}>\n                      {names.name}\n                    </p>\n                    <bds-button-icon\n                      class=\"preview-icon\"\n                      size=\"short\"\n                      icon=\"trash\"\n                      variant=\"secondary\"\n                      onClick={() => this.deleteFile(index)}\n                      aria-label={`delete ${names.name}`}\n                      data-test={`${this.dtButtonDelete}-${index}`}\n                    ></bds-button-icon>\n                  </div>\n                </div>\n              ))}\n            </div>\n            {this.multiple ? (\n              <bds-typo\n                variant=\"fs-14\"\n                italic\n                class=\"preview-length\"\n                aria-label={termTranslate(this.language, 'uploaded')}\n              >\n                {this.files.length > 1 ? `${this.files.length} ${termTranslate(this.language, 'uploaded')}` : ''}\n              </bds-typo>\n            ) : (\n              ''\n            )}\n          </div>\n        ) : (\n          ''\n        )}\n        <div class={{ upload__edit: true }}>\n          <label\n            class={{ 'upload__edit--label': true, 'upload__edit--hover': this.hover }}\n            id=\"file-label\"\n            htmlFor=\"file\"\n            data-test={this.dtLabelAddFile}\n            tabindex=\"0\"\n            onKeyDown={this.handleKeyDown.bind(this)}\n          >\n            <div class={{ 'text-box': true, 'text-box--hover': this.hover }} id=\"file-text_box\">\n              {this.hover ? (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropHere')}\n                >\n                  {termTranslate(this.language, 'dropHere')}\n                </bds-typo>\n              ) : (\n                <bds-typo\n                  class=\"text\"\n                  variant=\"fs-14\"\n                  bold=\"regular\"\n                  aria-label={termTranslate(this.language, 'dropOrClick')}\n                >\n                  {termTranslate(this.language, 'dropOrClick')}\n                </bds-typo>\n              )}\n            </div>\n            <img class={{ 'upload__img--invisible': true, 'upload__img--visible': this.hover }} src={background} />\n          </label>\n          <input\n            ref={this.refInputElement}\n            type=\"file\"\n            name=\"files[]\"\n            id=\"file\"\n            class=\"upload__input\"\n            multiple={this.multiple}\n            accept={this.internalAccepts.length > 0 ? this.internalAccepts.toString() : this.accept}\n            onChange={($event: any) => this.onUploadClick($event.target.files)}\n            data-test={this.dtInputFiles}\n          />\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": ["background"], "mappings": ";;;;;;;;;;;;YAAO,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,QAAQ,EAAE,mBAAmB;YAC7B,QAAA,QAAQ,EAAE,kCAAkC;YAC5C,QAAA,WAAW,EAAE,2EAA2E;YACxF,QAAA,WAAW,EAAE,iFAAiF;YAC/F,KAAA;aACF;;YCPM,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,QAAQ,EAAE,kBAAkB;YAC5B,QAAA,QAAQ,EAAE,mCAAmC;YAC7C,QAAA,WAAW,EAAE,wEAAwE;YACrF,QAAA,WAAW,EAAE,4FAA4F;YAC1G,KAAA;aACF;;YCPM,MAAM,OAAO,GAAG;YACrB,IAAA;YACE,QAAA,QAAQ,EAAE,gBAAgB;YAC1B,QAAA,QAAQ,EAAE,0BAA0B;YACpC,QAAA,WAAW,EAAE,uDAAuD;YACpE,QAAA,WAAW,EAAE,gFAAgF;YAC9F,KAAA;aACF;;YCDM,MAAM,aAAa,GAAG,CAAC,IAAe,EAAE,MAAc,KAAY;YACvE,IAAA,IAAI,SAAS;gBACb,QAAQ,IAAI;YACV,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA,KAAK,OAAO;YACV,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC/C;YACF,QAAA;YACE,YAAA,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;;YAEnD,IAAA,OAAO,SAAS;YAClB,CAAC;;YCtBD,MAAM,UAAU,GAAG,gkDAAgkD;;YCAnlD,MAAM,YAAY,GAAG,0gHAA0gH;;kBCSlhH,SAAS,yBAAA,MAAA;YALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;YASW,QAAA,IAAK,CAAA,KAAA,GAAW,EAAE;YAClB,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;YACjB,QAAA,IAAK,CAAA,KAAA,GAAG,KAAK;YAEb,QAAA,IAAI,CAAA,IAAA,GAAa,EAAE;YACnB,QAAA,IAAe,CAAA,eAAA,GAAa,EAAE;YAC9B,QAAA,IAAW,CAAA,WAAA,GAAG,KAAK;YAC5B;;YAEG;YACK,QAAA,IAAQ,CAAA,QAAA,GAAe,OAAO;YAsBtC;;YAEG;YACK,QAAA,IAAU,CAAA,UAAA,GAAsB,EAAE;YAE1C;;;YAGG;YACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;YAEpC;;;YAGG;YACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;YAEtC;;;YAGG;YACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;oBAkEtC,IAAA,CAAA,eAAe,GAAG,CAAC,IAAU,EAAE,KAAa,KAAI;YAC9C,YAAA,MAAM,QAAQ,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;wBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBACxD,IAAI,QAAQ,EAAE;YACZ,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK;4BACxB;;6BACK;YACL,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;YACvB,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;4BACtB;;YAEJ,SAAC;YAED;;YAEG;YACH,QAAA,IAAA,CAAA,UAAU,GAAG,CAAC,KAAK,KAAI;YACrB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;YACrB,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY;YAC7B,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK;YACtB,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACzB,SAAC;YAED;;YAEG;YACH,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,KAAK,KAAI;YACtB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAClB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;6BAClB;YACL,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC;;YAExC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;YAClD,SAAC;YAwEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,EAAoB,KAAU;YACvD,YAAA,IAAI,CAAC,YAAY,GAAG,EAAsB;YAC5C,SAAC;YAmHF;gBArRW,iBAAiB,GAAA;YACzB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,YAAA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;YACvC,gBAAA,IAAI;gCACF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;;4BAClD,OAAA,EAAA,EAAM;YACN,oBAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;;6BAEtB;YACL,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU;;;yBAEnC;YACL,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;;gBAKnB,YAAY,GAAA;oBACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC1C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACnC,oBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;;;;YAOpC,IAAA,kBAAkB,CAAC,KAAK,EAAA;oBAChC,IAAI,KAAK,EAAE;wBACT,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC;YACxD,YAAA,UAAU,CAAC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC;;;gBAI/C,iBAAiB,GAAA;oBACf,IAAI,CAAC,iBAAiB,EAAE;;gBAG1B,gBAAgB,GAAA;YACd,QAAA,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;YACnE,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;wBACjF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC;YACzF,SAAC,CAAC;oBACF,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;YAC9C,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;wBACvF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC;YACzF,SAAC,CAAC;oBACF,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;YAC1C,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;wBACvF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;YAC1F,SAAC,CAAC;YACF,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;;YAqC3E;;YAEG;YACH,IAAA,eAAe,CAAC,CAAC,EAAA;oBACf,CAAC,CAAC,cAAc,EAAE;oBAClB,CAAC,CAAC,eAAe,EAAE;;YAErB;;YAEG;YACH,IAAA,SAAS,CAAC,OAAO,EAAA;YACf,QAAA,IAAI,CAAC,KAAK,GAAG,OAAO;;YAEtB;;YAEG;YACI,IAAA,aAAa,CAAC,KAAK,EAAA;YACxB,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAClB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;6BAClB;YACL,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC;;YAExC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;wBACrB,IAAI,CAAC,OAAO,EAAE;;yBACT;YACL,YAAA,OAAO,KAAK;;YAEd,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;;YAElD;;YAEG;gBACH,OAAO,GAAA;oBACL,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,KAAI;YAC3B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI;YAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,SAAC,CAAC;;YAEJ;;YAEG;gBAEH,MAAM,UAAU,CAAC,KAAK,EAAA;oBACpB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;oBACvE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;oBAClD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC3B,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;oBAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;yBACjB;YACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;YAEvB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;;YAGlD;;YAEG;YAEH,IAAA,MAAM,cAAc,GAAA;YAClB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;YAChD,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;oBACf,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;yBACjB;YACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;YAEvB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;;YAO1C,IAAA,aAAa,CAAC,KAAK,EAAA;YACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;YACxB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;;gBAI7B,MAAM,GAAA;YACJ,QAAA,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,QAAQ,EAAA,EACjB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACxB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,oBAAoB,EAAC,IAAI,EAAC,WAAW,EAAC,IAAI,EAAC,QAAQ,EAAY,CAAA,EAC/E,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,oBAAoB,EAAA,EAC7B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAa,YAAA,EAAA,IAAI,CAAC,SAAS,EAAA,EAC7D,IAAI,CAAC,SAAS,CACN,EACX,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAA,YAAA,EAAa,IAAI,CAAC,QAAQ,EAC/D,EAAA,IAAI,CAAC,QAAQ,CACL,CACP,CACF,EACL,IAAI,CAAC,KAAK,IACT,CAAY,CAAA,YAAA,EAAA,EAAA,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAa,YAAA,EAAA,IAAI,CAAC,KAAK,EAChE,EAAA,IAAI,CAAC,KAAK,CACA,KAEb,EAAE,CACH,EACA,IAAI,CAAC,SAAS,IACb,CAAA,CAAA,KAAA,EAAA,IAAA,EACE,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,cAAc,EAAA,EACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,KAAK,MAChC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,iBAAiB,EAAC,GAAG,EAAE,KAAK,EAAE,EAAE,EAAC,WAAW,EAAA,EACrD,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,SAAS,EAAC,EAAE,EAAC,SAAS,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAY,CAAA,EAClD,CAAA,CAAA,GAAA,EAAA,EAAG,KAAK,EAAC,cAAc,EAAC,EAAE,EAAC,cAAc,EAAa,YAAA,EAAA,KAAK,CAAC,IAAI,EAAA,EAC7D,KAAK,CAAC,IAAI,CACT,EACJ,CACE,CAAA,iBAAA,EAAA,EAAA,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACzB,YAAA,EAAA,CAAA,OAAA,EAAU,KAAK,CAAC,IAAI,EAAE,EACvB,WAAA,EAAA,CAAA,EAAG,IAAI,CAAC,cAAc,IAAI,KAAK,CAAA,CAAE,GAC3B,CACf,CACF,CACP,CAAC,CACE,EACL,IAAI,CAAC,QAAQ,IACZ,CAAA,CAAA,UAAA,EAAA,EACE,OAAO,EAAC,OAAO,EACf,MAAM,EAAA,IAAA,EACN,KAAK,EAAC,gBAAgB,EACV,YAAA,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAEnD,EAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAI,CAAA,EAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAE,CAAA,GAAG,EAAE,CACvF,KAEX,EAAE,CACH,CACG,KAEN,EAAE,CACH,EACD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,EAAA,EAChC,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,qBAAqB,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC,KAAK,EAAE,EACzE,EAAE,EAAC,YAAY,EACf,OAAO,EAAC,MAAM,eACH,IAAI,CAAC,cAAc,EAC9B,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAExC,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,eAAe,EAChF,EAAA,IAAI,CAAC,KAAK,IACT,gBACE,KAAK,EAAC,MAAM,EACZ,OAAO,EAAC,OAAO,EACf,IAAI,EAAC,SAAS,gBACF,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAEnD,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAChC,KAEX,gBACE,KAAK,EAAC,MAAM,EACZ,OAAO,EAAC,OAAO,EACf,IAAI,EAAC,SAAS,gBACF,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,IAEtD,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CACnC,CACZ,CACG,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,wBAAwB,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAEA,UAAU,GAAI,CACjG,EACR,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,IAAI,CAAC,eAAe,EACzB,IAAI,EAAC,MAAM,EACX,IAAI,EAAC,SAAS,EACd,EAAE,EAAC,MAAM,EACT,KAAK,EAAC,eAAe,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,EACvF,QAAQ,EAAE,CAAC,MAAW,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAA,WAAA,EACvD,IAAI,CAAC,YAAY,GAC5B,CACE,CACF;;;;;;;;;;;;;;;;;"}