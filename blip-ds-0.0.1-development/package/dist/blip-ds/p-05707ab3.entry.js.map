{"version": 3, "names": ["inputCss", "Input", "constructor", "hostRef", "this", "isPressed", "isPassword", "validationMesage", "validationDanger", "inputName", "type", "label", "placeholder", "autoCapitalize", "autoComplete", "readonly", "helperMessage", "errorMessage", "successMessage", "icon", "disabled", "danger", "success", "value", "counterLength", "counterLengthRule", "isSubmit", "isTextarea", "rows", "cols", "dataTest", "encode", "keyPressWrapper", "event", "key", "bdsSubmit", "emit", "clearTextInput", "preventDefault", "bdsKeyDownBackspace", "onInput", "ev", "onBdsInputValidations", "input", "target", "bdsInput", "onBlur", "onBlurValidations", "bdsOnBlur", "onFocus", "bdsFocus", "onClickWrapper", "nativeInput", "focus", "stopPropagation", "setFocus", "removeFocus", "getInputElement", "<PERSON><PERSON><PERSON><PERSON>", "validity", "valid", "clear", "encodeValue", "lt", "gt", "ap", "ic", "amp", "slash", "toString", "replace", "valueChanged", "newValue", "changeValue", "bdsChange", "renderIcon", "h", "class", "input__icon", "size", "name", "color", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "undefined", "required", "requiredValidation", "pattern", "patternValidation", "minlength", "maxlength", "lengthValidation", "min", "max", "minMaxValidation", "checkValidity", "emailValidation", "numberValidation", "regex", "RegExp", "bdsPatternValidation", "test", "valueMissing", "requiredErrorMessage", "tooShort", "minlengthErrorMessage", "tooLong", "rangeUnderflow", "minErrorMessage", "rangeOverflow", "maxErrorMessage", "emailErrorMessage", "numberErrorMessage", "componentDidUpdate", "render", "Element", "Host", "onClick", "onKeyDown", "input__container__wrapper", "chips", "input__container__wrapper__chips", "input__container__text", "input__container__text__chips", "ref", "autocapitalize", "autocomplete", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "readOnly", "Object", "assign", "length", "active"], "sources": ["src/components/input/input.scss?tag=bds-input&encapsulation=shadow", "src/components/input/input.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: 22px;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @if ($name == 'disabled') {\n    background: $color-surface-2;\n  }\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n\n      &[type='date'] {\n        &::-webkit-calendar-picker-indicator {\n          opacity: 0;\n          pointer-events: none;\n        }\n      }\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    gap: 4px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      margin-top: 0px;\n    }\n\n    &--danger {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-negative;\n        }\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { Component, h, Prop, State, Watch, Event, EventEmitter, Method, Host } from '@stencil/core';\nimport { InputType, InputAutocapitalize, InputAutoComplete, InputCounterLengthRules } from './input-interface';\nimport { emailValidation, numberValidation } from '../../utils/validations';\n\n@Component({\n  tag: 'bds-input',\n  styleUrl: 'input.scss',\n  shadow: true,\n})\nexport class Input {\n  private nativeInput?: HTMLInputElement;\n\n  @State() isPressed? = false;\n  @State() isPassword? = false;\n  @State() validationMesage? = '';\n  @State() validationDanger? = false;\n  /**\n   * Nome do input, usado para identificação no formulário.\n   */\n  @Prop() inputName? = '';\n\n  /**\n   * Define o tipo do input (por exemplo, `text`, `password`, etc).\n   */\n  @Prop({ reflect: true }) type?: InputType = 'text';\n\n  /**\n   * <PERSON><PERSON><PERSON><PERSON> que será exibido acima do input.\n   */\n  @Prop() label? = '';\n\n  /**\n   * Texto que será exibido como sugestão ou dica no input.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Define a capitalização automática do texto (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Define o comportamento de autocompletar do navegador (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * Define o valor máximo permitido para o input.\n   */\n  @Prop() max?: string;\n\n  /**\n   * Define o número máximo de caracteres permitidos no input.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * Define o valor mínimo permitido para o input.\n   */\n  @Prop() min?: string;\n\n  /**\n   * Define o número mínimo de caracteres permitidos no input.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * Torna o input somente leitura.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Define se o input é obrigatório.\n   */\n  @Prop() required: boolean;\n\n  /**\n   * Define um padrão regex que o valor do input deve seguir.\n   */\n  @Prop() pattern?: string;\n\n  /**\n   * Mensagem de ajuda exibida abaixo do input.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Mensagem de erro exibida quando o valor do input é inválido.\n   */\n  @Prop({ mutable: true }) errorMessage?: string = '';\n\n  /**\n   * Mensagem exibida quando o valor do input é válido.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n\n  /**\n   * Nome do ícone a ser exibido dentro do input.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Define se o input está desabilitado.\n   */\n  @Prop({ reflect: true, mutable: true }) disabled?: boolean = false;\n\n  /**\n   * Define se o input está em estado de erro.\n   */\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\n\n  /**\n   * Define se o input está em estado de sucesso.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n\n  /**\n   * O valor atual do input.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Define se será exibido um contador de comprimento de caracteres.\n   */\n  @Prop() counterLength? = false;\n\n  /**\n   * Define a regra do contador de comprimento de caracteres (min, max, etc).\n   */\n  @Prop() counterLengthRule?: InputCounterLengthRules = null;\n\n  /**\n   * Define se o input será submetido ao pressionar Enter.\n   */\n  @Prop() isSubmit = false;\n\n  /**\n   * Define se o input é uma área de texto (textarea).\n   */\n  @Prop() isTextarea = false;\n\n  /**\n   * Define a quantidade de linhas da área de texto (se for `textarea`).\n   */\n  @Prop() rows?: number = 1;\n\n  /**\n   * Define a quantidade de colunas da área de texto (se for `textarea`).\n   */\n  @Prop() cols?: number = 0;\n\n  /**\n   * Mensagem de erro exibida quando o input não é preenchido e é obrigatório.\n   */\n  @Prop() requiredErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao comprimento mínimo.\n   */\n  @Prop() minlengthErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor mínimo permitido.\n   */\n  @Prop() minErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor máximo permitido.\n   */\n  @Prop() maxErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um email válido.\n   */\n  @Prop() emailErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um número válido.\n   */\n  @Prop() numberErrorMessage: string;\n\n  /**\n   * Define se o input será exibido como chips (um tipo de entrada com múltiplos valores).\n   */\n  @Prop() chips: boolean;\n\n  /**\n   * Data test é a prop para testar especificamente a ação do componente.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Prop() encode?: boolean = false;\n\n  /**\n   * Evento disparado quando o valor do input muda.\n   */\n  @Event({ bubbles: true, composed: true }) bdsChange!: EventEmitter;\n\n  /**\n   * Evento disparado quando o input recebe um input (digitação).\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Evento disparado quando o input perde o foco.\n   */\n  @Event() bdsOnBlur: EventEmitter;\n\n  /**\n   * Evento disparado quando o input ganha o foco.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  /**\n   * Evento disparado quando o formulário é submetido.\n   */\n  @Event() bdsSubmit: EventEmitter;\n\n  /**\n   * Evento disparado para validação de padrão regex.\n   */\n  @Event() bdsPatternValidation: EventEmitter;\n\n  /**\n   * Evento disparado quando a tecla \"Backspace\" é pressionada.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  /**\n   * Define o foco no campo de entrada.\n   */\n  @Method()\n  async setFocus(): Promise<void> {\n    this.onClickWrapper();\n  }\n\n  /**\n   * Remove o foco do campo de entrada.\n   */\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.onBlur();\n  }\n\n  /**\n   * Retorna o elemento de input do componente.\n   */\n  @Method()\n  async getInputElement(): Promise<HTMLInputElement> {\n    return this.nativeInput;\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.nativeInput.validity.valid;\n  }\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.value = '';\n  }\n\n  /**\n   * Codifica os caracteres especiais para exibição segura (evita injeção de código HTML).\n   */\n  private encodeValue(value?: string): string {\n    const lt = /</g,\n      gt = />/g,\n      ap = /'/g,\n      ic = /\"/g,\n      amp = /&/g,\n      slash = /\\//g;\nif(!this.encode) return value;\n    return (\n      value &&\n      value\n        .toString()\n        .replace(lt, '&lt;')\n        .replace(gt, '&gt;')\n        .replace(ap, '&#39;')\n        .replace(ic, '&#34;')\n        .replace(amp, '&amp;')\n        .replace(slash, '&#47;')\n    );\n  }\n\n  /**\n   * Avisa sobre a mudança do valor do campo de entrada.\n   */\n  @Watch('value')\n  protected valueChanged(newValue: string | null): void {\n    const changeValue = this.encode ? this.encodeValue(newValue || '') : newValue || '';\n    this.bdsChange.emit({ value: changeValue });\n  }\n\n  /**\n   * Tratamento de eventos de pressionamento de tecla (Enter, Backspace, etc).\n   */\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsSubmit.emit({ event, value: this.value });\n\n        if (this.isSubmit) {\n          this.clearTextInput();\n          event.preventDefault();\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  /**\n   * Função chamada ao digitar no campo de entrada.\n   */\n  private onInput = (ev: InputEvent): void => {\n    this.onBdsInputValidations();\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n  };\n\n  /**\n   * Função chamada ao perder o foco do campo de entrada.\n   */\n  private onBlur = (): void => {\n    this.onBlurValidations();\n    this.isPressed = false;\n    this.bdsOnBlur.emit();\n  };\n\n  /**\n   * Função chamada ao ganhar o foco do campo de entrada.\n   */\n  private onFocus = (): void => {\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  /**\n   * Função chamada ao clicar no campo de entrada.\n   */\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  private clearTextInput = (ev?: Event) => {\n    if (!this.readonly && !this.disabled && ev) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n\n    this.value = '';\n\n    if (this.nativeInput) {\n      this.nativeInput.value = '';\n    }\n  };\n\n  /**\n   * Função que renderiza o ícone dentro do campo de entrada.\n   */\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon\n            class=\"input__icon--color\"\n            size={this.label ? 'medium' : 'small'}\n            name={this.icon}\n            color=\"inherit\"\n          ></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza a label do campo de entrada.\n   */\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza as mensagens de erro ou sucesso abaixo do campo de entrada.\n   */\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Valida o campo de entrada ao perder o foco.\n   */\n  private onBlurValidations() {\n    this.required && this.requiredValidation();\n    this.pattern && this.patternValidation();\n    (this.minlength || this.maxlength) && this.lengthValidation();\n    (this.min || this.max) && this.minMaxValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Realiza as validações do campo enquanto o usuário digita.\n   */\n  private onBdsInputValidations() {\n    this.type === 'email' && this.emailValidation();\n    this.type === 'phonenumber' && this.numberValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Valida o padrão regex do campo.\n   */\n  private patternValidation() {\n    const regex = new RegExp(this.pattern);\n    this.bdsPatternValidation.emit(regex.test(this.nativeInput.value));\n  }\n\n  /**\n   * Valida se o campo é obrigatório.\n   */\n  private requiredValidation() {\n    if (this.nativeInput.validity.valueMissing) {\n      this.validationMesage = this.requiredErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida o comprimento do texto no campo de entrada.\n   */\n  private lengthValidation() {\n    if (this.nativeInput.validity.tooShort) {\n      this.validationMesage = this.minlengthErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.tooLong) {\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida os valores mínimos e máximos do campo de entrada.\n   */\n  private minMaxValidation() {\n    if (this.nativeInput.validity.rangeUnderflow) {\n      this.validationMesage = this.minErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.rangeOverflow) {\n      this.validationMesage = this.maxErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um email válido.\n   */\n  private emailValidation() {\n    if (emailValidation(this.nativeInput.value)) {\n      this.validationMesage = this.emailErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um número válido.\n   */\n  private numberValidation() {\n    if (numberValidation(this.nativeInput.value)) {\n      this.validationMesage = this.numberErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  /**\n   * Atualiza o valor do campo de entrada após as mudanças.\n   */\n  componentDidUpdate() {\n    if (this.nativeInput && this.value != this.nativeInput.value) {\n      this.nativeInput.value = this.value;\n    }\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const Element = this.isTextarea ? 'textarea' : 'input';\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <slot name=\"input-left\"></slot>\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: !this.chips, input__container__wrapper__chips: this.chips }}>\n              <slot name=\"inside-input-left\"></slot>\n              <Element\n                class={{ input__container__text: true, input__container__text__chips: this.chips }}\n                ref={(input) => (this.nativeInput = input)}\n                rows={this.rows}\n                cols={this.cols}\n                autocapitalize={this.autoCapitalize}\n                autocomplete={this.autoComplete}\n                disabled={this.disabled}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                name={this.inputName}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.onInput}\n                placeholder={this.placeholder}\n                readOnly={this.readonly}\n                type={this.type}\n                value={this.encodeValue(this.value)}\n                pattern={this.pattern}\n                required={this.required}\n                part=\"input\"\n                data-test={this.dataTest}\n              ></Element>\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text\n              length={this.value.length}\n              max={this.maxlength}\n              active={isPressed}\n              {...this.counterLengthRule}\n            />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"small\" />}\n          <slot name=\"input-right\" />\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "mappings": "oGAAA,MAAMA,EAAW,m+T,MCUJC,EAAK,MALlB,WAAAC,CAAAC,G,qSAQWC,KAASC,UAAI,MACbD,KAAUE,WAAI,MACdF,KAAgBG,iBAAI,GACpBH,KAAgBI,iBAAI,MAIrBJ,KAASK,UAAI,GAKIL,KAAIM,KAAe,OAKpCN,KAAKO,MAAI,GAKTP,KAAWQ,YAAY,GAKvBR,KAAcS,eAAyB,MAKvCT,KAAYU,aAAuB,MAyBnCV,KAAQW,SAAG,MAeXX,KAAaY,cAAY,GAKRZ,KAAYa,aAAY,GAKxBb,KAAcc,eAAY,GAK1Bd,KAAIe,KAAY,GAKDf,KAAQgB,SAAa,MAKrBhB,KAAMiB,OAAa,MAKnBjB,KAAOkB,QAAa,MAKnClB,KAAKmB,MAAmB,GAKzCnB,KAAaoB,cAAI,MAKjBpB,KAAiBqB,kBAA6B,KAK9CrB,KAAQsB,SAAG,MAKXtB,KAAUuB,WAAG,MAKbvB,KAAIwB,KAAY,EAKhBxB,KAAIyB,KAAY,EAwChBzB,KAAQ0B,SAAY,KAEpB1B,KAAM2B,OAAa,MAiHnB3B,KAAA4B,gBAAmBC,IACzB,OAAQA,EAAMC,KACZ,IAAK,QACH9B,KAAK+B,UAAUC,KAAK,CAAEH,QAAOV,MAAOnB,KAAKmB,QAEzC,GAAInB,KAAKsB,SAAU,CACjBtB,KAAKiC,iBACLJ,EAAMK,gB,CAER,MACF,IAAK,YACL,IAAK,SACHlC,KAAKmC,oBAAoBH,KAAK,CAAEH,QAAOV,MAAOnB,KAAKmB,QACnD,M,EAOEnB,KAAAoC,QAAWC,IACjBrC,KAAKsC,wBACL,MAAMC,EAAQF,EAAGG,OACjB,GAAID,EAAO,CACTvC,KAAKmB,MAAQoB,EAAMpB,OAAS,E,CAE9BnB,KAAKyC,SAAST,KAAKK,EAAG,EAMhBrC,KAAM0C,OAAG,KACf1C,KAAK2C,oBACL3C,KAAKC,UAAY,MACjBD,KAAK4C,UAAUZ,MAAM,EAMfhC,KAAO6C,QAAG,KAChB7C,KAAKC,UAAY,KACjBD,KAAK8C,SAASd,MAAM,EAMdhC,KAAc+C,eAAG,KACvB/C,KAAK6C,UACL,GAAI7C,KAAKgD,YAAa,CACpBhD,KAAKgD,YAAYC,O,GAObjD,KAAAiC,eAAkBI,IACxB,IAAKrC,KAAKW,WAAaX,KAAKgB,UAAYqB,EAAI,CAC1CA,EAAGH,iBACHG,EAAGa,iB,CAGLlD,KAAKmB,MAAQ,GAEb,GAAInB,KAAKgD,YAAa,CACpBhD,KAAKgD,YAAY7B,MAAQ,E,EAgQ9B,CA5YC,cAAMgC,GACJnD,KAAK+C,gB,CAOP,iBAAMK,GACJpD,KAAK0C,Q,CAOP,qBAAMW,GACJ,OAAOrD,KAAKgD,W,CAOd,aAAMM,GACJ,OAAOtD,KAAKgD,YAAYO,SAASC,K,CAOnC,WAAMC,GACJzD,KAAKmB,MAAQ,E,CAMP,WAAAuC,CAAYvC,GAClB,MAAMwC,EAAK,KACTC,EAAK,KACLC,EAAK,KACLC,EAAK,KACLC,EAAM,KACNC,EAAQ,MACd,IAAIhE,KAAK2B,OAAQ,OAAOR,EACpB,OACEA,GACAA,EACG8C,WACAC,QAAQP,EAAI,QACZO,QAAQN,EAAI,QACZM,QAAQL,EAAI,SACZK,QAAQJ,EAAI,SACZI,QAAQH,EAAK,SACbG,QAAQF,EAAO,Q,CAQZ,YAAAG,CAAaC,GACrB,MAAMC,EAAcrE,KAAK2B,OAAS3B,KAAK0D,YAAYU,GAAY,IAAMA,GAAY,GACjFpE,KAAKsE,UAAUtC,KAAK,CAAEb,MAAOkD,G,CAiFvB,UAAAE,GACN,OACEvE,KAAKe,MACHyD,EAAA,OACEC,MAAO,CACLC,YAAa,KACb,uBAAwB1E,KAAKO,QAG/BiE,EAAA,YACEC,MAAM,qBACNE,KAAM3E,KAAKO,MAAQ,SAAW,QAC9BqE,KAAM5E,KAAKe,KACX8D,MAAM,Y,CAUR,WAAAC,GACN,OACE9E,KAAKO,OACHiE,EAAA,SACEC,MAAO,CACLM,wBAAyB,KACzB,mCAAoC/E,KAAKC,YAAcD,KAAKgB,WAG9DwD,EAAA,YAAUQ,QAAQ,QAAQC,KAAK,QAC5BjF,KAAKO,O,CAUR,aAAA2E,GACN,MAAMnE,EAAOf,KAAKiB,OAAS,QAAUjB,KAAKkB,QAAU,YAAc,OAClE,IAAIiE,EAAUnF,KAAKiB,OAASjB,KAAKa,aAAeb,KAAKkB,QAAUlB,KAAKc,eAAiBd,KAAKY,cAE1F,IAAKuE,GAAWnF,KAAKI,iBAAkB+E,EAAUnF,KAAKG,iBAEtD,MAAMiF,EACJpF,KAAKiB,QAAUjB,KAAKI,iBAChB,wCACAJ,KAAKkB,QACH,yCACA,iBAER,GAAIiE,EAAS,CACX,OACEX,EAAA,OAAKC,MAAOW,EAAQC,KAAK,kBACvBb,EAAK,OAAAC,MAAM,wBACTD,EAAA,YAAUG,KAAK,UAAUC,KAAM7D,EAAMuE,MAAM,UAAUT,MAAM,aAE7DL,EAAA,YAAUC,MAAM,uBAAuBO,QAAQ,SAC5CG,G,CAMT,OAAOI,S,CAMD,iBAAA5C,GACN3C,KAAKwF,UAAYxF,KAAKyF,qBACtBzF,KAAK0F,SAAW1F,KAAK2F,qBACpB3F,KAAK4F,WAAa5F,KAAK6F,YAAc7F,KAAK8F,oBAC1C9F,KAAK+F,KAAO/F,KAAKgG,MAAQhG,KAAKiG,mBAC/BjG,KAAKkG,e,CAMC,qBAAA5D,GACNtC,KAAKM,OAAS,SAAWN,KAAKmG,kBAC9BnG,KAAKM,OAAS,eAAiBN,KAAKoG,mBACpCpG,KAAKkG,e,CAMC,iBAAAP,GACN,MAAMU,EAAQ,IAAIC,OAAOtG,KAAK0F,SAC9B1F,KAAKuG,qBAAqBvE,KAAKqE,EAAMG,KAAKxG,KAAKgD,YAAY7B,O,CAMrD,kBAAAsE,GACN,GAAIzF,KAAKgD,YAAYO,SAASkD,aAAc,CAC1CzG,KAAKG,iBAAmBH,KAAK0G,qBAC7B1G,KAAKI,iBAAmB,I,EAOpB,gBAAA0F,GACN,GAAI9F,KAAKgD,YAAYO,SAASoD,SAAU,CACtC3G,KAAKG,iBAAmBH,KAAK4G,sBAC7B5G,KAAKI,iBAAmB,KACxB,M,CAGF,GAAIJ,KAAKgD,YAAYO,SAASsD,QAAS,CACrC7G,KAAKI,iBAAmB,KACxB,M,EAOI,gBAAA6F,GACN,GAAIjG,KAAKgD,YAAYO,SAASuD,eAAgB,CAC5C9G,KAAKG,iBAAmBH,KAAK+G,gBAC7B/G,KAAKI,iBAAmB,KACxB,M,CAGF,GAAIJ,KAAKgD,YAAYO,SAASyD,cAAe,CAC3ChH,KAAKG,iBAAmBH,KAAKiH,gBAC7BjH,KAAKI,iBAAmB,KACxB,M,EAOI,eAAA+F,GACN,GAAIA,EAAgBnG,KAAKgD,YAAY7B,OAAQ,CAC3CnB,KAAKG,iBAAmBH,KAAKkH,kBAC7BlH,KAAKI,iBAAmB,I,EAOpB,gBAAAgG,GACN,GAAIA,EAAiBpG,KAAKgD,YAAY7B,OAAQ,CAC5CnB,KAAKG,iBAAmBH,KAAKmH,mBAC7BnH,KAAKI,iBAAmB,I,EAOpB,aAAA8F,GACN,GAAIlG,KAAKgD,YAAYO,SAASC,MAAO,CACnCxD,KAAKI,iBAAmB,K,EAO5B,kBAAAgH,GACE,GAAIpH,KAAKgD,aAAehD,KAAKmB,OAASnB,KAAKgD,YAAY7B,MAAO,CAC5DnB,KAAKgD,YAAY7B,MAAQnB,KAAKmB,K,EAIlC,MAAAkG,GACE,MAAMpH,EAAYD,KAAKC,YAAcD,KAAKgB,SAC1C,MAAMsG,EAAUtH,KAAKuB,WAAa,WAAa,QAE/C,OACEiD,EAAC+C,EAAI,CAAAzF,IAAA,2DAAgB9B,KAAKgB,SAAW,OAAS,MAC5CwD,EAAA,OAAA1C,IAAA,2CACE2C,MAAO,CACLlC,MAAO,KACP,wBAAyBvC,KAAKiB,SAAWjB,KAAKI,iBAC9C,sBAAuBJ,KAAKiB,QAAUjB,KAAKI,iBAC3C,uBAAwBJ,KAAKkB,QAC7B,wBAAyBlB,KAAKgB,SAC9B,iBAAkBhB,KAAKO,MACvB,iBAAkBN,GAEpBuH,QAASxH,KAAK+C,eACd0E,UAAWzH,KAAK4B,gBAChByD,KAAK,mBAEJrF,KAAKuE,aACNC,EAAM,QAAA1C,IAAA,2CAAA8C,KAAK,eACXJ,EAAK,OAAA1C,IAAA,2CAAA2C,MAAM,oBACRzE,KAAK8E,cACNN,EAAA,OAAA1C,IAAA,2CAAK2C,MAAO,CAAEiD,2BAA4B1H,KAAK2H,MAAOC,iCAAkC5H,KAAK2H,QAC3FnD,EAAM,QAAA1C,IAAA,2CAAA8C,KAAK,sBACXJ,EAAC8C,EACC,CAAAxF,IAAA,2CAAA2C,MAAO,CAAEoD,uBAAwB,KAAMC,8BAA+B9H,KAAK2H,OAC3EI,IAAMxF,GAAWvC,KAAKgD,YAAcT,EACpCf,KAAMxB,KAAKwB,KACXC,KAAMzB,KAAKyB,KACXuG,eAAgBhI,KAAKS,eACrBwH,aAAcjI,KAAKU,aACnBM,SAAUhB,KAAKgB,SACf+E,IAAK/F,KAAK+F,IACVC,IAAKhG,KAAKgG,IACVkC,UAAWlI,KAAK4F,UAChBuC,UAAWnI,KAAK6F,UAChBjB,KAAM5E,KAAKK,UACXqC,OAAQ1C,KAAK0C,OACbG,QAAS7C,KAAK6C,QACdT,QAASpC,KAAKoC,QACd5B,YAAaR,KAAKQ,YAClB4H,SAAUpI,KAAKW,SACfL,KAAMN,KAAKM,KACXa,MAAOnB,KAAK0D,YAAY1D,KAAKmB,OAC7BuE,QAAS1F,KAAK0F,QACdF,SAAUxF,KAAKwF,SACfH,KAAK,QACM,YAAArF,KAAK0B,aAIrB1B,KAAKoB,eACJoD,EAAA,mBAAA6D,OAAAC,OAAA,CAAAxG,IAAA,2CACEyG,OAAQvI,KAAKmB,MAAMoH,OACnBvC,IAAKhG,KAAK6F,UACV2C,OAAQvI,GACJD,KAAKqB,oBAGZrB,KAAKkB,SAAWsD,EAAA,YAAA1C,IAAA,2CAAU2C,MAAM,eAAeG,KAAK,QAAQU,MAAM,UAAUX,KAAK,UAClFH,EAAA,QAAA1C,IAAA,2CAAM8C,KAAK,iBAEZ5E,KAAKkF,gB", "ignoreList": []}