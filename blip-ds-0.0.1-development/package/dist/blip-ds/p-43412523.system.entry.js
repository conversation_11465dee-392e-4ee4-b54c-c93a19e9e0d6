System.register(["./p-B47mPBRA.system.js"],(function(a){"use strict";var t,r,e,i,o;return{setters:[function(a){t=a.r;r=a.c;e=a.h;i=a.H;o=a.a}],execute:function(){var s=[{value:"A",color:"system"},{value:"B",color:"success"},{value:"C",color:"warning"},{value:"D",color:"error"},{value:"E",color:"info"},{value:"F",color:"system"},{value:"G",color:"success"},{value:"H",color:"warning"},{value:"I",color:"error"},{value:"J",color:"info"},{value:"K",color:"system"},{value:"L",color:"success"},{value:"M",color:"warning"},{value:"N",color:"error"},{value:"O",color:"info"},{value:"P",color:"system"},{value:"Q",color:"success"},{value:"R",color:"warning"},{value:"S",color:"error"},{value:"T",color:"info"},{value:"U",color:"system"},{value:"V",color:"success"},{value:"X",color:"warning"},{value:"Y",color:"error"},{value:"W",color:"info"},{value:"Z",color:"system"}];var n=':host{position:relative;display:block;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.avatar{position:relative;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;-webkit-appearance:none;-moz-appearance:none;appearance:none;height:100%}.avatar::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.avatar:focus-visible{outline:none}.avatar:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.avatar__ellipsis{color:var(--color-surface-1, rgb(246, 246, 246))}.avatar__text{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__btn{border-radius:40px;border:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16));-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;width:100%;height:100%;overflow:hidden;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.avatar__btn__img{background-position:center;background-size:cover}.avatar__btn__text{color:var(--color-content-default, rgb(40, 40, 40));opacity:1;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__icon{color:var(--color-content-default, rgb(40, 40, 40));opacity:1;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__thumb{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer}.avatar__btn__thumb:before{content:"";position:absolute;inset:0;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0;-webkit-transition:all 0.5s;transition:all 0.5s}.avatar__btn__thumb__icon{position:relative;color:var(--color-surface-1, rgb(246, 246, 246));opacity:0;-webkit-transition:all 0.5s;transition:all 0.5s}.avatar__btn__name{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer;opacity:0;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__name__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__btn__empty{position:absolute;width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;top:0;left:0;cursor:pointer;opacity:0;-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}.avatar__btn__empty__icon{color:var(--color-content-default, rgb(40, 40, 40))}.avatar__size--micro{width:24px;height:24px;min-width:24px;min-height:24px}.avatar__size--extra-small{width:32px;height:32px;min-width:32px;min-height:32px}.avatar__size--small{width:40px;height:40px;min-width:40px;min-height:40px}.avatar__size--standard{width:56px;height:56px;min-width:56px;min-height:56px}.avatar__size--large{width:64px;height:64px;min-width:64px;min-height:64px}.avatar__size--extra-large{width:72px;height:72px;min-width:72px;min-height:72px}.avatar__color--system .avatar__btn{background-color:var(--color-system, rgb(178, 223, 253))}.avatar__color--warning .avatar__btn{background-color:var(--color-warning, rgb(253, 233, 155))}.avatar__color--success .avatar__btn{background-color:var(--color-success, rgb(132, 235, 188))}.avatar__color--info .avatar__btn{background-color:var(--color-info, rgb(128, 227, 235))}.avatar__color--error .avatar__btn{background-color:var(--color-error, rgb(250, 190, 190))}.avatar__color--surface .avatar__btn{background-color:var(--color-surface-2, rgb(237, 237, 237));color:var(--color-content-disable, rgb(89, 89, 89))}.avatar:hover .avatar__btn__thumb:before{opacity:0.5}.avatar:hover .avatar__btn__thumb__icon{opacity:1}.avatar:hover .avatar__btn__text{opacity:0}.avatar:hover .avatar__btn__icon{opacity:0}.avatar:hover .avatar__btn__name{opacity:1}.avatar:hover .avatar__btn__empty{opacity:1}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}';var l=a("bds_avatar",function(){function a(a){var e=this;t(this,a);this.bdsClickAvatar=r(this,"bdsClickAvatar");this.bdsImageUpload=r(this,"bdsImageUpload");this.typoSize="fs-20";this.iconSize="large";this.name=null;this.thumbnail=null;this.size="standard";this.color="colorLetter";this.upload=false;this.openUpload=false;this.ellipsis=null;this.dataTest=null;this.handleOpenUpload=function(a){var t=e.el.shadowRoot.getElementById("file-input");if(a.type==="click"||a.type==="keydown"&&(a.key==="Enter"||a.key===" ")){t.click()}};this.selectTypoSize=function(a){switch(a){case"micro":e.typoSize="fs-12";e.iconSize="xx-small";break;case"extra-small":e.typoSize="fs-14";e.iconSize="x-small";break;case"small":e.typoSize="fs-16";e.iconSize="medium";break;case"standard":e.typoSize="fs-20";e.iconSize="x-large";break;case"large":e.typoSize="fs-24";e.iconSize="xxx-large";break;case"extra-large":e.typoSize="fs-32";e.iconSize="xxx-large";break;default:e.typoSize="fs-20";e.iconSize="medium"}};this.avatarBgColor=function(a){if(e.color!="colorLetter"){return e.color}else if(a){var t=s.find((function(t){return t.value===a}));return t.color}}}a.prototype.onUploadClick=function(a){a.preventDefault();this.bdsClickAvatar.emit(a);if(this.openUpload){this.handleOpenUpload(a)}};a.prototype.onFileInputChange=function(a){var t=this;var r=a.target;var e=r.files;if(e&&e.length>0){var i=e[0];var o=new FileReader;o.onload=function(a){var r=a.target.result;t.thumbnail=r;t.bdsImageUpload.emit(r)};o.readAsDataURL(i)}};a.prototype.componentWillRender=function(){this.hasThumb=this.thumbnail?this.thumbnail.length!==0?true:false:false};a.prototype.render=function(){var a;var t=this;var r=this.name?this.name.split(" "):[];var o=r.length?r.shift().charAt(0).toUpperCase():"";var s=r.length?r.pop().charAt(0).toUpperCase():"";this.selectTypoSize(this.size);var n={backgroundImage:"url(".concat(this.hasThumb?this.thumbnail:null,")")};return e(i,{key:"b4352d630f52526279c919971c8882e14d65000c"},e("input",{key:"567b5f3b5d2875e31ece31afbfa9e75c168e53c3",type:"file",id:"file-input",accept:"image/*",onChange:function(a){return t.onFileInputChange(a)},style:{display:"none"}}),e("div",{key:"54b58c5162f627fcbb76e2b143f6186f0ba98070",class:(a={avatar:true},a["avatar__color--".concat(this.name&&!this.hasThumb?this.avatarBgColor(o):this.hasThumb&&!this.name?"surface":!this.name&&!this.hasThumb?"surface":this.name&&this.hasThumb?this.avatarBgColor(o):null)]=true,a["avatar__size--".concat(this.size)]=true,a.upload=this.upload||this.openUpload,a),onClick:function(a){return t.onUploadClick(a)},tabindex:"0",onKeyDown:function(a){return t.onUploadClick(a)},"data-test":this.dataTest},this.ellipsis?e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,variant:this.typoSize,tag:"span"},"+".concat(this.ellipsis))):this.thumbnail?this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("div",{class:"avatar__btn__img avatar__size--".concat(this.size),style:n}),e("div",{class:"avatar__btn__thumb"},e("bds-icon",{class:"avatar__btn__thumb__icon",name:"upload",theme:"outline",size:this.iconSize}))):e("div",{class:"avatar__btn"},e("div",{class:"avatar__btn__img avatar__size--".concat(this.size),style:n})):this.name?this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,class:"avatar__btn__text",variant:this.typoSize,tag:"span"},o+s),e("div",{class:"avatar__btn__name"},e("bds-icon",{class:"avatar__btn__name__icon",name:"upload",theme:"outline",size:this.iconSize}))):e("div",{class:"avatar__btn"},e("bds-typo",{margin:false,class:"avatar__text",variant:this.typoSize,tag:"span"},o+s)):this.upload||this.openUpload?e("div",{class:"avatar__btn"},e("bds-icon",{class:"avatar__btn__icon",name:"user-default",theme:"outline",size:this.iconSize}),e("div",{class:"avatar__btn__empty"},e("bds-icon",{class:"avatar__btn__empty__icon",name:"upload",theme:"outline",size:this.iconSize}))):this.name===null&&!this.hasThumb?e("div",{class:"avatar__btn"},e("bds-icon",{class:"avatar__icon",name:"user-default",theme:"outline",size:this.iconSize})):""))};Object.defineProperty(a.prototype,"el",{get:function(){return o(this)},enumerable:false,configurable:true});return a}());l.style=n}}}));
//# sourceMappingURL=p-43412523.system.entry.js.map