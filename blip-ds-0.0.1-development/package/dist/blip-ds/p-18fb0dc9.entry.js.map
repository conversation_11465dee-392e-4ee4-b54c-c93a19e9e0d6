{"version": 3, "names": ["tabGroupCss", "BdsTabGroup", "constructor", "hostRef", "this", "tabItensElement", "tabItensSlideElement", "isSlideTabs", "alignTab", "tabRefSlide", "positionLeft", "contentScrollable", "align", "dtButtonPrev", "dtButtonNext", "getEventsDisable", "ItensElement", "for<PERSON>ach", "element", "addEventListener", "setInternalItens", "Array", "from", "checkSlideTabs", "headerElement", "headerSlideElement", "_a", "offsetWidth", "_b", "setFirstActive", "hasOpenDefined", "filter", "obj", "open", "length", "setnumberElement", "i", "numberElement", "arrayItens", "map", "item", "index", "Object", "assign", "label", "badge", "disable", "undefined", "error", "headerStyle", "contentStyle", "icon", "iconPosition", "iconTheme", "badgeShape", "badgeColor", "badgeIcon", "badgeAnimation", "badgeNumber", "badgePosition", "dataTest", "internalItens", "handleClick", "numberItem", "updateInternalItens", "bdsTabChange", "emit", "refHeaderElement", "el", "refHeaderSlideElement", "handleDisabled", "id", "bdsTabDisabled", "nextSlide", "minLeft", "calcNumber", "_c", "_d", "numberClicks", "parseInt", "toString", "newPosition", "_e", "prevSlide", "renderIcon", "Icon", "Theme", "h", "class", "tab_group__header__itens__item__typo__disable", "tab_group__header__itens__item__typo__error", "size", "name", "theme", "renderBadge", "<PERSON><PERSON><PERSON>", "Color", "Animation", "Number", "color", "number", "shape", "animation", "componentWillRender", "getElementsByTagName", "componentDidLoad", "shadowRoot", "querySelectorAll", "connectedCallback", "isSlide", "window", "setInterval", "disconnectedCallback", "clearInterval", "handleKeyDown", "event", "key", "focus", "parseInlineStyle", "styleString", "split", "style", "trim", "reduce", "acc", "property", "value", "s", "camelProperty", "replace", "g", "toUpperCase", "render", "slidePosition", "left", "openTab", "find", "Host", "tab_group", "onClick", "variant", "tab_group__header", "tab_group__slide", "ref", "tab_group__header__itens", "tab_group__slide__itens", "bold", "tab_group__header__itens__item", "tab_group__header__itens__item__open", "tab_group__header__itens__item__disable", "tabindex", "onKeyDown", "ev", "tab_group__content", "tab_group__scrolled"], "sources": ["src/components/tabs/tab-group.scss?tag=bds-tab-group&encapsulation=shadow", "src/components/tabs/tab-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n.tab_group {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n\n  &__header {\n    padding: 4px 16px;\n    overflow: hidden;\n\n    &__itens {\n      display: flex;\n      flex-direction: row;\n      width: max-content;\n      gap: 32px;\n      margin: auto;\n\n      &__center {\n        justify-content: center;\n        margin: auto;\n      }\n\n      &__right {\n        justify-content: right;\n        margin: 0 0 0 auto;\n      }\n\n      &__left {\n        justify-content: left;\n        margin: 0 auto 0 0;\n      }\n\n      &__item {\n        cursor: pointer;\n        height: 46px;\n        gap: 4px;\n        width: auto;\n        display: flex;\n        align-items: center;\n        border-bottom: 2px solid transparent;\n        position: relative;\n\n        &__typo{\n          color: $color-content-disable;\n          &__disable {\n            color: $color-content-ghost;\n          }\n          &__error {\n            color: $color-surface-negative;\n          }\n        }\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: -4px;\n          border: 2px solid transparent;\n          border-radius: 4px;\n        }\n\n        &:focus-visible {\n          outline: none;\n\n          &::before {\n            border-color: $color-focus;\n          }\n        }\n\n        &__open {\n          color: $color-content-default;\n          border-color: $color-primary;\n        }\n        &__disable {\n          cursor: no-drop;\n        }\n      }\n    }\n  }\n\n  &__slide {\n    position: relative;\n    overflow: hidden;\n    padding: 0 16px;\n    height: 54px;\n    margin-left: 56px;\n    margin-right: 56px;\n\n    &-button {\n      position: absolute;\n      z-index: 1;\n      background-color: $color-surface-1;\n\n      &[icon='arrow-left'] {\n        left: 0;\n      }\n      &[icon='arrow-right'] {\n        right: 0;\n      }\n    }\n\n    &__itens {\n      position: absolute;\n      left: 56px;\n      width: max-content;\n      height: 48px;\n      display: flex;\n      flex-direction: row;\n      justify-content: center;\n      padding: 4px;\n      gap: 32px;\n      -webkit-transition: left 0.5s;\n      -moz-transition: left 0.5s;\n      transition: left 0.5s;\n    }\n  }\n  &__content {\n    height: 100%;\n  }\n\n  &__scrolled {\n    flex-shrink: 999;\n    overflow: none;\n\n    @include custom-scroll;\n  }\n}\n", "import { Component, h, Host, Element, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { Itens } from './tab-group-interface';\n\n@Component({\n  tag: 'bds-tab-group',\n  styleUrl: 'tab-group.scss',\n  shadow: true,\n})\nexport class BdsTabGroup {\n  private tabItensElement?: HTMLCollectionOf<HTMLBdsTabItemElement> = null;\n  private tabItensSlideElement?: NodeListOf<HTMLElement> = null;\n  private headerElement?: HTMLElement;\n  private headerSlideElement?: HTMLElement;\n  private isSlide?: number;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalItens: Itens[];\n\n  @State() isSlideTabs?: boolean = false;\n\n  @State() alignTab?: 'left' | 'scrolling' | 'right' = 'left';\n\n  @State() tabRefSlide?: number = 0;\n\n  @State() positionLeft?: number = 0;\n\n  @Prop() contentScrollable?: boolean = true;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * bdsTabChange. Event to return value when Tabs is change.\n   */\n  @Event() bdsTabChange?: EventEmitter;\n  /**\n   * bdsTabDisabled. Event to return value when Tabs disable is change.\n   */\n  @Event() bdsTabDisabled?: EventEmitter;\n\n  componentWillRender() {\n    this.tabItensElement = this.element.getElementsByTagName('bds-tab-item') as HTMLCollectionOf<HTMLBdsTabItemElement>;\n    this.setnumberElement();\n    this.setFirstActive();\n    this.setInternalItens(Array.from(this.tabItensElement));\n    this.getEventsDisable(Array.from(this.tabItensElement));\n  }\n\n  componentDidLoad() {\n    this.tabItensSlideElement = this.element.shadowRoot.querySelectorAll(\n      '.tab_group__header__itens__item',\n    ) as NodeListOf<HTMLElement>;\n  }\n\n  connectedCallback() {\n    this.isSlide = window.setInterval(() => {\n      this.isSlideTabs = this.checkSlideTabs();\n    }, 100);\n  }\n\n  private getEventsDisable = (ItensElement): void => {\n    ItensElement.forEach((element) => {\n      element.addEventListener(\n        'tabDisabled',\n        () => {\n          this.setInternalItens(Array.from(this.tabItensElement));\n        },\n        false,\n      );\n    });\n  };\n\n  disconnectedCallback() {\n    window.clearInterval(this.isSlide);\n  }\n\n  private checkSlideTabs = (): boolean => {\n    if (this.headerElement || this.headerSlideElement) {\n      if (this.headerSlideElement?.offsetWidth > this.headerElement?.offsetWidth) {\n        return true;\n      }\n    }\n  };\n\n  private setFirstActive = () => {\n    const hasOpenDefined = Array.from(this.tabItensElement).filter((obj) => obj.open);\n    if (!hasOpenDefined.length) {\n      this.tabItensElement[0].open = true;\n    }\n  };\n\n  private setnumberElement = () => {\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      this.tabItensElement[i].numberElement = i;\n    }\n  };\n\n  private setInternalItens = (ItensElement) => {\n    const arrayItens = ItensElement.map((item, index) => {\n      return {\n        label: item.label,\n        open: item.open,\n        numberElement: index,\n        badge: item.badge,\n        ...(item.disable !== undefined && { disable: item.disable }),\n        ...(item.error !== undefined && { error: item.error }),\n        ...(item.headerStyle !== undefined && { headerStyle: item.headerStyle }),\n        ...(item.contentStyle !== undefined && { contentStyle: item.contentStyle }),\n        ...(item.icon !== undefined && { icon: item.icon }),\n        ...(item.iconPosition !== undefined && { iconPosition: item.iconPosition }),\n        ...(item.iconTheme !== undefined && { iconTheme: item.iconTheme }),\n        ...(item.badgeShape !== undefined && { badgeShape: item.badgeShape }),\n        ...(item.badgeColor !== undefined && { badgeColor: item.badgeColor }),\n        ...(item.badgeIcon !== undefined && { badgeIcon: item.badgeIcon }),\n        ...(item.badgeAnimation !== undefined && { badgeAnimation: item.badgeAnimation }),\n        ...(item.badgeNumber !== undefined && { badgeNumber: item.badgeNumber }),\n        ...(item.badgePosition !== undefined && { badgePosition: item.badgePosition }),\n        ...(item.dataTest !== undefined && { dataTest: item.dataTest }),\n      };\n    });\n    return (this.internalItens = arrayItens);\n  };\n\n  private handleClick = (numberItem) => {\n    const updateInternalItens = this.internalItens.map((item) => {\n      return {\n        label: item.label,\n        open: false,\n        numberElement: item.numberElement,\n      };\n    });\n    this.internalItens = updateInternalItens;\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      if (this.tabItensElement[i].numberElement != numberItem) {\n        this.tabItensElement[i].open = false;\n      } else {\n        this.tabItensElement[i].open = true;\n        this.bdsTabChange.emit(this.tabItensElement[i]);\n      }\n    }\n  };\n\n  private refHeaderElement = (el: HTMLElement): void => {\n    this.headerElement = el;\n  };\n\n  private refHeaderSlideElement = (el: HTMLElement): void => {\n    this.headerSlideElement = el;\n  };\n\n  private handleDisabled = (id) => {\n    this.bdsTabDisabled.emit(this.tabItensElement[id]);\n  };\n\n  private nextSlide = () => {\n    const minLeft = this.headerElement?.offsetWidth - this.headerSlideElement?.offsetWidth;\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft - this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition < minLeft ? minLeft : newPosition;\n    this.alignTab = newPosition < minLeft ? 'right' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide + 1 : numberClicks;\n  };\n\n  private prevSlide = () => {\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft + this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition > 0 ? 0 : newPosition;\n    this.alignTab = newPosition > 0 ? 'left' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide - 1 : numberClicks;\n  };\n\n  private handleKeyDown(event, item) {\n    if (event.key == 'Enter') {\n      item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement);\n    }\n    if (event.key == 'ArrowRight') {\n      this.tabItensSlideElement[item.numberElement + 1].focus();\n    }\n    if (event.key == 'ArrowLeft') {\n      this.tabItensSlideElement[item.numberElement - 1].focus();\n    }\n  }\n\n  private parseInlineStyle(styleString: string): { [key: string]: string } {\n    if (!styleString) return {};\n    \n    return styleString\n      .split(';')\n      .filter(style => style.trim())\n      .reduce((acc, style) => {\n        const [property, value] = style.split(':').map(s => s.trim());\n        if (property && value) {\n          // Convert kebab-case to camelCase for CSS properties\n          const camelProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase());\n          acc[camelProperty] = value;\n        }\n        return acc;\n      }, {});\n  }\n\n  private renderIcon = (Icon, Theme, disable, error) => {\n    return (\n      <bds-icon\n        class={{ \n          tab_group__header__itens__item__typo__disable: disable,\n          tab_group__header__itens__item__typo__error: error \n        }}\n        size=\"x-small\"\n        name={Icon}\n        theme={Theme}\n      ></bds-icon>\n    );\n  };\n\n  private renderBadge = (Shape, Color, Icon, Animation, Number) => {\n    return (\n      <bds-grid justify-content=\"center\">\n        <bds-badge color={Color} icon={Icon} number={Number} shape={Shape} animation={Animation}></bds-badge>\n      </bds-grid>\n    );\n  };\n\n  render(): HTMLElement {\n    const slidePosition = { left: `${this.positionLeft}px` };\n    \n    // Find the currently open tab to get its headerStyle and contentStyle\n    const openTab = this.internalItens?.find(item => item.open);\n    const headerStyle = openTab?.headerStyle ? this.parseInlineStyle(openTab.headerStyle) : {};\n    const contentStyle = openTab?.contentStyle ? this.parseInlineStyle(openTab.contentStyle) : {};\n    \n    return (\n      <Host>\n        <div class={{ tab_group: true }}>\n          {this.isSlideTabs && this.alignTab != 'left' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-left\"\n              size=\"short\"\n              id=\"bds-tabs-button-left\"\n              onClick={() => this.prevSlide()}\n              dataTest={this.dtButtonPrev}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__header: true, tab_group__slide: this.isSlideTabs }} \n            ref={this.refHeaderElement}\n            style={headerStyle}\n          >\n            <div\n              class={{\n                tab_group__header__itens: true,\n                tab_group__slide__itens: this.isSlideTabs,\n                [`tab_group__header__itens__${this.align}`]: !this.isSlideTabs,\n              }}\n              ref={this.refHeaderSlideElement}\n              style={slidePosition}\n            >\n              {this.internalItens &&\n                this.internalItens.map((item, index) => {\n                  const bold = item.open == true ? 'bold' : 'regular';\n                  return (\n                    <div\n                      class={{\n                        tab_group__header__itens__item: true,\n                        tab_group__header__itens__item__open: item.open,\n                        tab_group__header__itens__item__disable: item.disable,\n                      }}\n                      key={index}\n                      tabindex=\"0\"\n                      onClick={() =>\n                        item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement)\n                      }\n                      onKeyDown={(ev) => this.handleKeyDown(ev, item)}\n                    >\n                      {item.iconPosition === 'left' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'left' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                      <bds-typo\n                        class={{ \n                          tab_group__header__itens__item__typo__disable: item.disable,\n                          tab_group__header__itens__item__typo__error: item.error \n                        }}\n                        variant=\"fs-16\"\n                        bold={bold}\n                      >\n                        {item.label}\n                      </bds-typo>\n                      {item.iconPosition === 'right' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'right' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                    </div>\n                  );\n                })}\n            </div>\n          </div>\n          {this.isSlideTabs && this.alignTab != 'right' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-right\"\n              size=\"short\"\n              id=\"bds-tabs-button-right\"\n              onClick={() => this.nextSlide()}\n              dataTest={this.dtButtonNext}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__content: true, tab_group__scrolled: this.contentScrollable }}\n            style={contentStyle}\n          >\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAc,stF,MCQPC,EAAW,MALxB,WAAAC,CAAAC,G,gGAMUC,KAAeC,gBAA6C,KAC5DD,KAAoBE,qBAA6B,KAShDF,KAAWG,YAAa,MAExBH,KAAQI,SAAoC,OAE5CJ,KAAWK,YAAY,EAEvBL,KAAYM,aAAY,EAEzBN,KAAiBO,kBAAa,KAE9BP,KAAKQ,MAAgC,SAMrCR,KAAYS,aAAY,KAMxBT,KAAYU,aAAY,KA+BxBV,KAAAW,iBAAoBC,IAC1BA,EAAaC,SAASC,IACpBA,EAAQC,iBACN,eACA,KACEf,KAAKgB,iBAAiBC,MAAMC,KAAKlB,KAAKC,iBAAiB,GAEzD,MACD,GACD,EAOID,KAAcmB,eAAG,K,QACvB,GAAInB,KAAKoB,eAAiBpB,KAAKqB,mBAAoB,CACjD,KAAIC,EAAAtB,KAAKqB,sBAAkB,MAAAC,SAAA,SAAAA,EAAEC,eAAcC,EAAAxB,KAAKoB,iBAAa,MAAAI,SAAA,SAAAA,EAAED,aAAa,CAC1E,OAAO,I,IAKLvB,KAAcyB,eAAG,KACvB,MAAMC,EAAiBT,MAAMC,KAAKlB,KAAKC,iBAAiB0B,QAAQC,GAAQA,EAAIC,OAC5E,IAAKH,EAAeI,OAAQ,CAC1B9B,KAAKC,gBAAgB,GAAG4B,KAAO,I,GAI3B7B,KAAgB+B,iBAAG,KACzB,IAAK,IAAIC,EAAI,EAAGA,EAAIhC,KAAKC,gBAAgB6B,OAAQE,IAAK,CACpDhC,KAAKC,gBAAgB+B,GAAGC,cAAgBD,C,GAIpChC,KAAAgB,iBAAoBJ,IAC1B,MAAMsB,EAAatB,EAAauB,KAAI,CAACC,EAAMC,IAEvCC,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,CAAAC,MAAOJ,EAAKI,MACZX,KAAMO,EAAKP,KACXI,cAAeI,EACfI,MAAOL,EAAKK,OACRL,EAAKM,UAAYC,WAAa,CAAED,QAASN,EAAKM,UAC9CN,EAAKQ,QAAUD,WAAa,CAAEC,MAAOR,EAAKQ,QAC1CR,EAAKS,cAAgBF,WAAa,CAAEE,YAAaT,EAAKS,cACtDT,EAAKU,eAAiBH,WAAa,CAAEG,aAAcV,EAAKU,eACxDV,EAAKW,OAASJ,WAAa,CAAEI,KAAMX,EAAKW,OACxCX,EAAKY,eAAiBL,WAAa,CAAEK,aAAcZ,EAAKY,eACxDZ,EAAKa,YAAcN,WAAa,CAAEM,UAAWb,EAAKa,YAClDb,EAAKc,aAAeP,WAAa,CAAEO,WAAYd,EAAKc,aACpDd,EAAKe,aAAeR,WAAa,CAAEQ,WAAYf,EAAKe,aACpDf,EAAKgB,YAAcT,WAAa,CAAES,UAAWhB,EAAKgB,YAClDhB,EAAKiB,iBAAmBV,WAAa,CAAEU,eAAgBjB,EAAKiB,iBAC5DjB,EAAKkB,cAAgBX,WAAa,CAAEW,YAAalB,EAAKkB,cACtDlB,EAAKmB,gBAAkBZ,WAAa,CAAEY,cAAenB,EAAKmB,gBAC1DnB,EAAKoB,WAAab,WAAa,CAAEa,SAAUpB,EAAKoB,aAGxD,OAAQxD,KAAKyD,cAAgBvB,CAAU,EAGjClC,KAAA0D,YAAeC,IACrB,MAAMC,EAAsB5D,KAAKyD,cAActB,KAAKC,IAC3C,CACLI,MAAOJ,EAAKI,MACZX,KAAM,MACNI,cAAeG,EAAKH,kBAGxBjC,KAAKyD,cAAgBG,EACrB,IAAK,IAAI5B,EAAI,EAAGA,EAAIhC,KAAKC,gBAAgB6B,OAAQE,IAAK,CACpD,GAAIhC,KAAKC,gBAAgB+B,GAAGC,eAAiB0B,EAAY,CACvD3D,KAAKC,gBAAgB+B,GAAGH,KAAO,K,KAC1B,CACL7B,KAAKC,gBAAgB+B,GAAGH,KAAO,KAC/B7B,KAAK6D,aAAaC,KAAK9D,KAAKC,gBAAgB+B,G,IAK1ChC,KAAA+D,iBAAoBC,IAC1BhE,KAAKoB,cAAgB4C,CAAE,EAGjBhE,KAAAiE,sBAAyBD,IAC/BhE,KAAKqB,mBAAqB2C,CAAE,EAGtBhE,KAAAkE,eAAkBC,IACxBnE,KAAKoE,eAAeN,KAAK9D,KAAKC,gBAAgBkE,GAAI,EAG5CnE,KAASqE,UAAG,K,cAClB,MAAMC,IAAUhD,EAAAtB,KAAKoB,iBAAa,MAAAE,SAAA,SAAAA,EAAEC,eAAcC,EAAAxB,KAAKqB,sBAAoB,MAAAG,SAAA,SAAAA,EAAAD,aAC3E,MAAMgD,IAAaC,EAAAxE,KAAKqB,sBAAkB,MAAAmD,SAAA,SAAAA,EAAEjD,eAAckD,EAAAzE,KAAKoB,iBAAe,MAAAqD,SAAA,SAAAA,EAAAlD,aAC9E,MAAMmD,EAAeC,SAASJ,EAAWK,YACzC,MAAMC,EAAc7E,KAAKM,eAAewE,EAAA9E,KAAKoB,iBAAe,MAAA0D,SAAA,SAAAA,EAAAvD,aAE5DvB,KAAKM,aAAeuE,EAAcP,EAAUA,EAAUO,EACtD7E,KAAKI,SAAWyE,EAAcP,EAAU,QAAU,YAElDtE,KAAKK,YAAcqE,GAAgB1E,KAAKK,YAAcL,KAAKK,YAAc,EAAIqE,CAAY,EAGnF1E,KAAS+E,UAAG,K,UAClB,MAAMR,IAAajD,EAAAtB,KAAKqB,sBAAkB,MAAAC,SAAA,SAAAA,EAAEC,eAAcC,EAAAxB,KAAKoB,iBAAe,MAAAI,SAAA,SAAAA,EAAAD,aAC9E,MAAMmD,EAAeC,SAASJ,EAAWK,YACzC,MAAMC,EAAc7E,KAAKM,eAAekE,EAAAxE,KAAKoB,iBAAe,MAAAoD,SAAA,SAAAA,EAAAjD,aAE5DvB,KAAKM,aAAeuE,EAAc,EAAI,EAAIA,EAC1C7E,KAAKI,SAAWyE,EAAc,EAAI,OAAS,YAE3C7E,KAAKK,YAAcqE,GAAgB1E,KAAKK,YAAcL,KAAKK,YAAc,EAAIqE,CAAY,EAgCnF1E,KAAUgF,WAAG,CAACC,EAAMC,EAAOxC,EAASE,IAExCuC,EACE,YAAAC,MAAO,CACLC,8CAA+C3C,EAC/C4C,4CAA6C1C,GAE/C2C,KAAK,UACLC,KAAMP,EACNQ,MAAOP,IAKLlF,KAAA0F,YAAc,CAACC,EAAOC,EAAOX,EAAMY,EAAWC,IAElDX,EAAA,8BAA0B,UACxBA,EAAW,aAAAY,MAAOH,EAAO7C,KAAMkC,EAAMe,OAAQF,EAAQG,MAAON,EAAOO,UAAWL,IAsHrF,CA7SC,mBAAAM,GACEnG,KAAKC,gBAAkBD,KAAKc,QAAQsF,qBAAqB,gBACzDpG,KAAK+B,mBACL/B,KAAKyB,iBACLzB,KAAKgB,iBAAiBC,MAAMC,KAAKlB,KAAKC,kBACtCD,KAAKW,iBAAiBM,MAAMC,KAAKlB,KAAKC,iB,CAGxC,gBAAAoG,GACErG,KAAKE,qBAAuBF,KAAKc,QAAQwF,WAAWC,iBAClD,kC,CAIJ,iBAAAC,GACExG,KAAKyG,QAAUC,OAAOC,aAAY,KAChC3G,KAAKG,YAAcH,KAAKmB,gBAAgB,GACvC,I,CAeL,oBAAAyF,GACEF,OAAOG,cAAc7G,KAAKyG,Q,CAwGpB,aAAAK,CAAcC,EAAO3E,GAC3B,GAAI2E,EAAMC,KAAO,QAAS,CACxB5E,EAAKM,QAAU1C,KAAKkE,eAAe9B,EAAKH,eAAiBjC,KAAK0D,YAAYtB,EAAKH,c,CAEjF,GAAI8E,EAAMC,KAAO,aAAc,CAC7BhH,KAAKE,qBAAqBkC,EAAKH,cAAgB,GAAGgF,O,CAEpD,GAAIF,EAAMC,KAAO,YAAa,CAC5BhH,KAAKE,qBAAqBkC,EAAKH,cAAgB,GAAGgF,O,EAI9C,gBAAAC,CAAiBC,GACvB,IAAKA,EAAa,MAAO,GAEzB,OAAOA,EACJC,MAAM,KACNzF,QAAO0F,GAASA,EAAMC,SACtBC,QAAO,CAACC,EAAKH,KACZ,MAAOI,EAAUC,GAASL,EAAMD,MAAM,KAAKjF,KAAIwF,GAAKA,EAAEL,SACtD,GAAIG,GAAYC,EAAO,CAErB,MAAME,EAAgBH,EAASI,QAAQ,aAAcC,GAAMA,EAAE,GAAGC,gBAChEP,EAAII,GAAiBF,C,CAEvB,OAAOF,CAAG,GACT,G,CAyBP,MAAAQ,G,MACE,MAAMC,EAAgB,CAAEC,KAAM,GAAGlI,KAAKM,kBAGtC,MAAM6H,GAAU7G,EAAAtB,KAAKyD,iBAAe,MAAAnC,SAAA,SAAAA,EAAA8G,MAAKhG,GAAQA,EAAKP,OACtD,MAAMgB,GAAcsF,IAAO,MAAPA,SAAA,SAAAA,EAAStF,aAAc7C,KAAKkH,iBAAiBiB,EAAQtF,aAAe,GACxF,MAAMC,GAAeqF,IAAO,MAAPA,SAAA,SAAAA,EAASrF,cAAe9C,KAAKkH,iBAAiBiB,EAAQrF,cAAgB,GAE3F,OACEqC,EAACkD,EAAI,CAAArB,IAAA,4CACH7B,EAAA,OAAA6B,IAAA,2CAAK5B,MAAO,CAAEkD,UAAW,OACtBtI,KAAKG,aAAeH,KAAKI,UAAY,QACpC+E,EAAA,mBAAA6B,IAAA,2CACE5B,MAAM,0BACNrC,KAAK,aACLwC,KAAK,QACLpB,GAAG,uBACHoE,QAAS,IAAMvI,KAAK+E,YACpBvB,SAAUxD,KAAKS,aACf+H,QAAQ,cAGZrD,EACE,OAAA6B,IAAA,2CAAA5B,MAAO,CAAEqD,kBAAmB,KAAMC,iBAAkB1I,KAAKG,aACzDwI,IAAK3I,KAAK+D,iBACVsD,MAAOxE,GAEPsC,EAAA,OAAA6B,IAAA,2CACE5B,MAAO,CACLwD,yBAA0B,KAC1BC,wBAAyB7I,KAAKG,YAC9B,CAAC,6BAA6BH,KAAKQ,UAAWR,KAAKG,aAErDwI,IAAK3I,KAAKiE,sBACVoD,MAAOY,GAENjI,KAAKyD,eACJzD,KAAKyD,cAActB,KAAI,CAACC,EAAMC,KAC5B,MAAMyG,EAAO1G,EAAKP,MAAQ,KAAO,OAAS,UAC1C,OACEsD,EACE,OAAAC,MAAO,CACL2D,+BAAgC,KAChCC,qCAAsC5G,EAAKP,KAC3CoH,wCAAyC7G,EAAKM,SAEhDsE,IAAK3E,EACL6G,SAAS,IACTX,QAAS,IACPnG,EAAKM,QAAU1C,KAAKkE,eAAe9B,EAAKH,eAAiBjC,KAAK0D,YAAYtB,EAAKH,eAEjFkH,UAAYC,GAAOpJ,KAAK8G,cAAcsC,EAAIhH,IAEzCA,EAAKY,eAAiB,QAAUZ,EAAKW,KAClC/C,KAAKgF,WAAW5C,EAAKW,KAAMX,EAAKa,UAAWb,EAAKM,QAASN,EAAKQ,OAC9D,GACHR,EAAKmB,gBAAkB,QAAUnB,EAAKK,MACnCzC,KAAK0F,YACHtD,EAAKc,WACLd,EAAKe,WACLf,EAAKgB,UACLhB,EAAKiB,eACLjB,EAAKkB,aAEP,GACJ6B,EAAA,YACEC,MAAO,CACLC,8CAA+CjD,EAAKM,QACpD4C,4CAA6ClD,EAAKQ,OAEpD4F,QAAQ,QACRM,KAAMA,GAEL1G,EAAKI,OAEPJ,EAAKY,eAAiB,SAAWZ,EAAKW,KACnC/C,KAAKgF,WAAW5C,EAAKW,KAAMX,EAAKa,UAAWb,EAAKM,QAASN,EAAKQ,OAC9D,GACHR,EAAKmB,gBAAkB,SAAWnB,EAAKK,MACpCzC,KAAK0F,YACHtD,EAAKc,WACLd,EAAKe,WACLf,EAAKgB,UACLhB,EAAKiB,eACLjB,EAAKkB,aAEP,GACA,MAKftD,KAAKG,aAAeH,KAAKI,UAAY,SACpC+E,EAAA,mBAAA6B,IAAA,2CACE5B,MAAM,0BACNrC,KAAK,cACLwC,KAAK,QACLpB,GAAG,wBACHoE,QAAS,IAAMvI,KAAKqE,YACpBb,SAAUxD,KAAKU,aACf8H,QAAQ,cAGZrD,EAAA,OAAA6B,IAAA,2CACE5B,MAAO,CAAEiE,mBAAoB,KAAMC,oBAAqBtJ,KAAKO,mBAC7D8G,MAAOvE,GAEPqC,EAAA,QAAA6B,IAAA,+C", "ignoreList": []}