import{r as e,c as i,h as t,H as s,a as r}from"./p-C3J6Z5OX.js";const c=':host{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;cursor:pointer;white-space:nowrap;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-weight:600}:host(.chip){border-radius:8px;padding:3px 8px}:host(.chip--primary){background:#e8f2ff;color:#3f7de8}:host(.chip--click.chip--primary:hover){background:#d1e3fa;color:#125ad5}:host(.chip--watermelon){background:#fb5a8b;color:#f6f6f6}:host(.chip--default){background:#ededed;color:#e3e3e3}:host(.chip--danger){background:#ffa5a5;color:#ff4c4c}:host(.chip--click.chip--danger:hover){background:#fccccc;color:#6a2026}:host(.chip--filter){background:#125ad5;color:#f6f6f6}:host(.chip--click.chip--filter:hover){background:#0747a6;color:#f6f6f6}:host(.chip--standard){height:24px;font-size:0.75rem}:host(.chip--tall){height:32px;font-size:0.875rem}.chip__delete{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;padding-left:6px;cursor:pointer}.chip__icon{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;padding-right:4px}';const a=class{constructor(t){e(this,t);this.bdsDelete=i(this,"bdsDelete");this.size="standard";this.variant="default";this.danger=false;this.filter=false;this.clickable=false;this.deletable=false;this.disabled=false}handleClickDelete(e){if(!this.deletable||this.disabled)return;e.preventDefault();this.bdsDelete.emit({id:this.element.id})}getClickClass(){return this.clickable?{"chip--click":true}:{}}getSizeClass(){return this.size==="standard"?{"chip--standard":true}:{"chip--tall":true}}getStateClass(){if(this.disabled){return{"chip--default":true}}if(this.danger){return{"chip--danger":true}}if(this.filter){return{"chip--filter":true}}if(this.variant==="primary"){return{"chip--primary":true}}if(this.variant==="watermelon"){return{"chip--watermelon":true}}return{"chip--default":true}}render(){return t(s,{key:"d831b25dab1957b462d16c052ebd9d67ba795839",class:Object.assign(Object.assign(Object.assign({chip:true},this.getClickClass()),this.getStateClass()),this.getSizeClass())},this.icon&&t("div",{key:"fef08391ae455996360d9f5e7d723b3b89353e84",class:"chip__icon"},t("bds-icon",{key:"3f003aa3b8611fffb5e836c178c797d92bc40520",size:"x-small",name:this.icon})),t("slot",{key:"decd45998ea5247618739698e01bc38237c9c2dd"}),this.deletable&&t("div",{key:"c351986efc842e885502b0b608ca3bcfd2aadee1",class:"chip__delete",onClick:this.handleClickDelete.bind(this)},t("bds-icon",{key:"cd0d62c912efe8260eb620ddd2bf4024619f1206",size:"x-small",theme:"solid",name:"error"})))}get element(){return r(this)}};a.style=c;export{a as bds_chip};
//# sourceMappingURL=p-4906892c.entry.js.map