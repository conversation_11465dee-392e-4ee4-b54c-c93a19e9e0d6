{"version": 3, "names": ["tableHeaderCss", "TableHeader", "exports", "class_1", "prototype", "render", "h", "Host", "key"], "sources": ["src/components/table/table-header/table-header.scss?tag=bds-table-header&encapsulation=scoped", "src/components/table/table-header/table-header.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-header-group;\n  border-bottom: 1px solid $color-border-1;\n}", "import { Component, h, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-header',\n  styleUrl: 'table-header.scss',\n  scoped: true,\n})\nexport class TableHeader {\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAiB,uH,ICOVC,EAAWC,EAAA,8B,wBACtBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAQ,QAAAE,IAAA,6C,WAJQ,I", "ignoreList": []}