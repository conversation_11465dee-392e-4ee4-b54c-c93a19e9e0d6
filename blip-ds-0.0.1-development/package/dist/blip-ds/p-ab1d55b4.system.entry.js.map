{"version": 3, "names": ["alertCss", "BdsAlert", "exports", "class_1", "hostRef", "_this", "this", "open", "dataTest", "position", "listener", "event", "key", "toggle", "prototype", "bdsAlertChanged", "emit", "alertStatus", "isOpenChanged", "document", "addEventListener", "removeEventListener", "render", "h", "class", "_a", "alert__dialog", "concat"], "sources": ["src/components/alert/alert.scss?tag=bds-alert&encapsulation=shadow", "src/components/alert/alert.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.alert__dialog {\n  opacity: 0;\n  visibility: hidden;\n  background-color: rgba(0, 0, 0, 0.7);\n  width: 100%;\n  height: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: opacity 0.3s ease-in-out;\n  z-index: $zindex-modal-overlay;\n\n  .alert {\n    position: relative;\n    margin: 48px auto 0;\n    overflow: hidden;\n    max-width: 424px;\n    border-radius: 8px;\n    background: $color-surface-1;\n    box-shadow: $shadow-3;\n  }\n\n  &--open {\n    opacity: 1;\n    visibility: visible;\n  }\n  &--fixed {\n    position: fixed;\n  }\n  &--contain {\n    position: absolute;\n  }\n}\n", "import { Component, ComponentInterface, h, Method, Prop, Event, EventEmitter, Watch } from '@stencil/core';\n\nexport type collapses = 'fixed' | 'contain';\n@Component({\n  tag: 'bds-alert',\n  styleUrl: 'alert.scss',\n  shadow: true,\n})\nexport class BdsAlert implements ComponentInterface {\n  /**\n   * Used to open/close the alert\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Define whether the component will occupy the entire screen or just the parent.\n   */\n  @Prop() position?: string = 'fixed';\n\n  /**\n   * Emitted when modal status has changed.\n   */\n  @Event() bdsAlertChanged!: EventEmitter;\n\n  /**\n   * Can be used outside to open/close the alert\n   */\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n\n    if (this.open) {\n      this.bdsAlertChanged.emit({ alertStatus: 'opened' });\n    } else {\n      this.bdsAlertChanged.emit({ alertStatus: 'closed' });\n    }\n  }\n\n  @Watch('open')\n  protected isOpenChanged(): void {\n    if (this.open) {\n      document.addEventListener('keydown', this.listener, false);\n    } else document.removeEventListener('keydown', this.listener, false);\n  }\n\n  private listener = (event) => {\n    if (event.key == 'Enter' || event.key == 'Escape') {\n      this.toggle();\n    }\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          alert__dialog: true,\n          'alert__dialog--open': this.open,\n          [`alert__dialog--${this.position}`]: true,\n        }}\n      >\n        <div class=\"alert\" data-test={this.dataTest}>\n          <slot></slot>\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "gmDAAA,IAAMA,EAAW,2xB,ICQJC,EAAQC,EAAA,uBALrB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,yDAaSA,KAAIC,KAAa,MAKhBD,KAAQE,SAAY,KAKpBF,KAAQG,SAAY,QA4BpBH,KAAAI,SAAW,SAACC,GAClB,GAAIA,EAAMC,KAAO,SAAWD,EAAMC,KAAO,SAAU,CACjDP,EAAKQ,Q,CAET,CAiBD,CAtCOV,EAAAW,UAAAD,OAAN,W,qFACEP,KAAKC,MAAQD,KAAKC,KAElB,GAAID,KAAKC,KAAM,CACbD,KAAKS,gBAAgBC,KAAK,CAAEC,YAAa,U,KACpC,CACLX,KAAKS,gBAAgBC,KAAK,CAAEC,YAAa,U,kBAKnCd,EAAAW,UAAAI,cAAA,WACR,GAAIZ,KAAKC,KAAM,CACbY,SAASC,iBAAiB,UAAWd,KAAKI,SAAU,M,MAC/CS,SAASE,oBAAoB,UAAWf,KAAKI,SAAU,M,EAShEP,EAAAW,UAAAQ,OAAA,W,MACE,OACEC,EACE,OAAAX,IAAA,2CAAAY,OAAKC,EAAA,CACHC,cAAe,KACf,sBAAuBpB,KAAKC,MAC5BkB,EAAC,kBAAAE,OAAkBrB,KAAKG,WAAa,K,IAGvCc,EAAA,OAAAX,IAAA,2CAAKY,MAAM,QAAmB,YAAAlB,KAAKE,UACjCe,EAAa,QAAAX,IAAA,8C,mIA9DF,I", "ignoreList": []}