System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,r,s,b;return{setters:[function(t){e=t.r;r=t.h;s=t.H;b=t.a}],execute:function(){var o=".sc-bds-table-body-h{display:table-row-group;height:64px}.multiple.sc-bds-table-body-h{border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16))}.sc-bds-table-body-h:last-child{border-bottom:none}";var n=t("bds_table_body",function(){function t(t){e(this,t);this.multipleRows=false}t.prototype.componentWillLoad=function(){var t=this.element.closest("bds-table");if(t&&(t.getAttribute("collapse")==="true"||t.collapse===true)){this.multipleRows=true}};t.prototype.render=function(){return r(s,{key:"6fd4435dd90fba8dbdf3ec4cf22b128a27869818",class:{host:true,multiple:this.multipleRows}},r("slot",{key:"3b8c873e9c861b5e6b39b46f8f9097d342d2681d"}))};Object.defineProperty(t.prototype,"element",{get:function(){return b(this)},enumerable:false,configurable:true});return t}());n.style=o}}}));
//# sourceMappingURL=p-e080b25c.system.entry.js.map