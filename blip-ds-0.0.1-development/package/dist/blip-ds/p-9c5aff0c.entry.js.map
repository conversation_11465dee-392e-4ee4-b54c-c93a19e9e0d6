{"version": 3, "names": ["accordionCss", "AccordionBody", "constructor", "hostRef", "this", "container", "isOpen", "isOpenAftAnimation", "numberElement", "hasDivisor", "dataTest", "ref<PERSON><PERSON><PERSON>", "el", "toggle", "open", "close", "divisor", "valor", "isOpenChanged", "heightContainer", "_a", "offsetHeight", "setTimeout", "render", "h", "key", "class", "accordion_body", "accordion_body_divisor", "accordion_body_isOpen", "style", "height", "ref"], "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion-body&encapsulation=shadow", "src/components/accordion/accordion-body.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, State, h, Method, Prop, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-body',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionBody {\n  private container?: HTMLElement = null;\n\n  @State() isOpen?: boolean = false;\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() heightContainer?: number;\n  @State() numberElement?: number = null;\n  @State() hasDivisor?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  @Method()\n  async divisor(valor) {\n    this.hasDivisor = valor;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(): void {\n    this.heightContainer = this.isOpen ? (this.container?.offsetHeight || 0) : 0;\n    if (this.isOpen) {\n      setTimeout(() => {\n        this.isOpenAftAnimation = true;\n      }, 500);\n    } else {\n      this.isOpenAftAnimation = false;\n    }\n  }\n\n  private refContainer = (el: HTMLElement): void => {\n    this.container = el;\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          accordion_body: true,\n          accordion_body_divisor: this.hasDivisor,\n          accordion_body_isOpen: this.isOpenAftAnimation,\n        }}\n        style={{ height: `${this.heightContainer}px` }}\n        data-test={this.dataTest}\n      >\n        <div class=\"container\" ref={(el) => this.refContainer(el)}>\n          <slot></slot>\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "2CAAA,MAAMA,EAAe,ylE,MCORC,EAAa,MAL1B,WAAAC,CAAAC,G,UAMUC,KAASC,UAAiB,KAEzBD,KAAME,OAAa,MACnBF,KAAkBG,mBAAa,MAE/BH,KAAaI,cAAY,KACzBJ,KAAUK,WAAa,KAKxBL,KAAQM,SAAY,KAkCpBN,KAAAO,aAAgBC,IACtBR,KAAKC,UAAYO,CAAE,CAoBtB,CApDC,YAAMC,GACJT,KAAKE,QAAUF,KAAKE,M,CAItB,UAAMQ,GACJV,KAAKE,OAAS,I,CAIhB,WAAMS,GACJX,KAAKE,OAAS,K,CAIhB,aAAMU,CAAQC,GACZb,KAAKK,WAAaQ,C,CAIpB,aAAAC,G,MACEd,KAAKe,gBAAkBf,KAAKE,SAAUc,EAAAhB,KAAKC,aAAW,MAAAe,SAAA,SAAAA,EAAAC,eAAgB,EAAK,EAC3E,GAAIjB,KAAKE,OAAQ,CACfgB,YAAW,KACTlB,KAAKG,mBAAqB,IAAI,GAC7B,I,KACE,CACLH,KAAKG,mBAAqB,K,EAQ9B,MAAAgB,GACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACLC,eAAgB,KAChBC,uBAAwBxB,KAAKK,WAC7BoB,sBAAuBzB,KAAKG,oBAE9BuB,MAAO,CAAEC,OAAQ,GAAG3B,KAAKe,qBACd,YAAAf,KAAKM,UAEhBc,EAAA,OAAAC,IAAA,2CAAKC,MAAM,YAAYM,IAAMpB,GAAOR,KAAKO,aAAaC,IACpDY,EAAa,QAAAC,IAAA,8C", "ignoreList": []}