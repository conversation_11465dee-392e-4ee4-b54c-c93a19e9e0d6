{"version": 3, "file": "bds-badge.entry.esm.js", "sources": ["src/components/badge/badge.scss?tag=bds-badge&encapsulation=shadow", "src/components/badge/badge.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n@keyframes pulse {\n  0% {\n    scale: 100%;\n    opacity: 1;\n  }\n  20% {\n    scale: 140%;\n    opacity: 0;\n  }\n  21% {\n    scale: 100%;\n    opacity: 1;\n  }\n  40% {\n    scale: 140%;\n    opacity: 0;\n  }\n  41% {\n    scale: 140%;\n    opacity: 0;\n  }\n  100% {\n    scale: 140%;\n    opacity: 0;\n  }\n}\n\n.color {\n  &--system {\n    background-color: $color-system;\n    color: $color-system;\n  }\n\n  &--danger {\n    background-color: $color-error;\n    color: $color-error;\n  }\n\n  &--warning {\n    background-color: $color-warning;\n    color: $color-warning;\n  }\n\n  &--success {\n    background-color: $color-success;\n    color: $color-success;\n  }\n\n  &--neutral {\n    background-color: $color-surface-3;\n    color: $color-surface-3;\n  }\n}\n\n:host {\n  display: inline-flex;\n}\n\n.chip_size {\n  min-width: 24px;\n}\n\n.chip_badge {\n  .status {\n    width: 8px;\n    height: 8px;\n\n    &--circle {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n      border-radius: 4px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    &--circle-true::before {\n      content: '';\n      width: 8px;\n      height: 8px;\n      position: absolute;\n      border: 1px solid;\n      border-radius: 8px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 0;\n      height: 0;\n      border-left: 4px solid transparent;\n      border-right: 4px solid transparent;\n\n      border-bottom: 8px solid;\n    }\n    &--triangle-reverse {\n      width: 0;\n      height: 0;\n      border-left: 4px solid transparent;\n      border-right: 4px solid transparent;\n\n      border-top: 8px solid;\n    }\n    &--polygon {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n      rotate: 45deg;\n    }\n    &--square {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n    }\n  }\n\n  .icon {\n    position: relative;\n    bds-icon {\n      position: absolute;\n    }\n    .bds-icon {\n      color: $color-content-default;\n    }\n\n    &--circle {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        border-radius: 16px;\n      }\n      .trim-true::before {\n        content: '';\n        width: 24px;\n        height: 24px;\n        left: -2px;\n        top: -2px;\n        position: absolute;\n        border: 2px solid;\n        border-radius: 16px;\n        animation: pulse 2s ease-out infinite;\n      }\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: end;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0, 100% 100%, 0 100%);\n      }\n    }\n\n    &--triangle-reverse {\n      display: flex;\n      justify-content: center;\n      align-items: start;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 100%, 0 0, 100% 0);\n      }\n    }\n    &--polygon {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);\n      }\n    }\n    &--square {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n      }\n    }\n  }\n\n  .number {\n    display: flex;\n    height: 24px;\n    padding: 0 8px;\n    border-radius: 16px;\n    justify-content: center;\n    align-items: center;\n    position: relative;\n\n    &--true::before {\n      content: '';\n      width: 100%;\n      height: 24px;\n      left: -2px;\n      top: -2px;\n      position: absolute;\n      border: 2px solid;\n      border-radius: 16px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    .number_text {\n      color: $color-content-default;\n    }\n  }\n\n  .empty {\n    display: flex;\n    min-height: 24px;\n    min-width: 24px;\n    position: relative;\n\n    &--true::before {\n      content: '';\n      width: 100%;\n      height: 24px;\n      left: -2px;\n      top: -2px;\n      position: absolute;\n      border: 2px solid;\n      border-radius: 16px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    &--circle {\n      border-radius: 50%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        border-radius: 16px;\n      }\n      .trim-true::before {\n        content: '';\n        width: 24px;\n        height: 24px;\n        left: -2px;\n        top: -2px;\n        position: absolute;\n        border: 2px solid;\n        border-radius: 16px;\n        animation: pulse 2s ease-out infinite;\n      }\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: end;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0, 100% 100%, 0 100%);\n      }\n    }\n\n    &--triangle-reverse {\n      display: flex;\n      justify-content: center;\n      align-items: start;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 100%, 0 0, 100% 0);\n      }\n    }\n    &--polygon {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);\n      }\n    }\n    &--square {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n      }\n    }\n  }\n}\n", "import { Component, h, Host, Prop, State, Watch } from '@stencil/core';\n\nexport type Shape = 'circle' | 'triangle' | 'triangle-reverse' | 'polygon' | 'square';\n\nexport type Color = 'system' | 'danger' | 'warning' | 'success' | 'neutral';\n\nexport type Type = 'status' | 'icon' | 'number' | 'empty';\n\n@Component({\n  tag: 'bds-badge',\n  styleUrl: 'badge.scss',\n  shadow: true,\n})\nexport class Badge {\n  /**\n   * State for keep the value of the type.\n   */\n  @State() type?: Type = 'status';\n  /**\n   * Set the color of the component.\n   */\n  @Prop() color?: string = 'system';\n  /**\n   * Set the shape of the component.\n   */\n  @Prop() shape?: Shape = 'circle';\n  /**\n   * Set witch icon will be render inside the component.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Set the text in shape circle. Is just alow numbers, but if the number pass 999 a symbol '+' will be render.\n   */\n  @Prop() number?: number;\n  /**\n   * If true, actived the pulse animation.\n   */\n  @Prop() animation?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    if (this.icon === null && this.number) {\n      this.type = 'number';\n    } else if (!this.number && this.icon) {\n      this.type = 'icon';\n    } else if (this.number && this.icon) {\n      this.type = 'number';\n    } else if (this.number === 0) {\n      this.type = 'empty';\n    }\n  }\n\n  @Watch('number')\n  numberChanged(newNumber: number) {\n    if (newNumber === 0) {\n      this.type = 'empty';\n    } else if (this.icon === null && newNumber !== null) {\n      this.type = 'number';\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_badge: true,\n            chip_size: this.number !== 0 ? true : false,\n            [`chip_badge--${this.shape}`]: true,\n            [`chip_badge--${this.color}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.type === 'status' && (\n            <div\n              class={{\n                status: true,\n                [`status--${this.shape}`]: true,\n                [`color--${this.color}`]: true,\n                [`status--circle-${this.animation}`]: true,\n              }}\n            ></div>\n          )}\n          {this.type === 'icon' && (\n            <div class={{ icon: true, [`icon--${this.shape}`]: true }}>\n              <div class={{ [`color--${this.color}`]: true, trim: true, [`trim-${this.animation}`]: true }}></div>\n              <bds-icon size=\"xxx-small\" name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.type === 'number' && (\n            <div\n              class={{\n                number: true,\n                [`color--${this.color}`]: true,\n                [`number--${this.animation}`]: true,\n              }}\n            >\n              <bds-typo class=\"number_text\" variant=\"fs-12\" bold=\"bold\" margin={false}>\n                {this.number >= 999 ? '999+' : this.number}\n              </bds-typo>\n            </div>\n          )}\n          {this.type === 'empty' && (\n            <div\n              class={{\n                empty: true,\n                [`color--${this.color}`]: true,\n                [`empty--${this.shape}`]: true,\n                [`empty--${this.animation}`]: true,\n              }}\n            ></div>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,QAAQ,GAAG,siMAAsiM;;MCa1iM,KAAK,GAAA,MAAA;AALlB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACM,QAAA,IAAI,CAAA,IAAA,GAAU,QAAQ;AAC/B;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,QAAQ;AACjC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAW,QAAQ;AAChC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAK5B;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AAEnC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA8EjC;IA5EC,iBAAiB,GAAA;QACf,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,IAAI,GAAG,QAAQ;;aACf,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,GAAG,MAAM;;aACb,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AACnC,YAAA,IAAI,CAAC,IAAI,GAAG,QAAQ;;AACf,aAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,IAAI,GAAG,OAAO;;;AAKvB,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC7B,QAAA,IAAI,SAAS,KAAK,CAAC,EAAE;AACnB,YAAA,IAAI,CAAC,IAAI,GAAG,OAAO;;aACd,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;AACnD,YAAA,IAAI,CAAC,IAAI,GAAG,QAAQ;;;IAIxB,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,SAAS,EAAE,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK;AAC3C,gBAAA,CAAC,eAAe,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AACnC,gBAAA,CAAC,eAAe,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;aACpC,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAEvB,IAAI,CAAC,IAAI,KAAK,QAAQ,KACrB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,MAAM,EAAE,IAAI;AACZ,gBAAA,CAAC,WAAW,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC/B,gBAAA,CAAC,UAAU,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC9B,gBAAA,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAE,CAAA,GAAG,IAAI;aAC3C,EAAA,CACI,CACR,EACA,IAAI,CAAC,IAAI,KAAK,MAAM,KACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA,MAAA,EAAS,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,EAAE,EAAA,EACvD,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,CAAA,OAAA,EAAU,IAAI,CAAC,KAAK,CAAA,CAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAQ,KAAA,EAAA,IAAI,CAAC,SAAS,CAAE,CAAA,GAAG,IAAI,EAAE,EAAQ,CAAA,EACpG,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,WAAW,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAa,CAAA,CACnD,CACP,EACA,IAAI,CAAC,IAAI,KAAK,QAAQ,KACrB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,MAAM,EAAE,IAAI;AACZ,gBAAA,CAAC,UAAU,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC9B,gBAAA,CAAC,WAAW,IAAI,CAAC,SAAS,CAAE,CAAA,GAAG,IAAI;AACpC,aAAA,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,aAAa,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAE,KAAK,EAAA,EACpE,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CACjC,CACP,CACP,EACA,IAAI,CAAC,IAAI,KAAK,OAAO,KACpB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,CAAC,UAAU,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC9B,gBAAA,CAAC,UAAU,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC9B,gBAAA,CAAC,UAAU,IAAI,CAAC,SAAS,CAAE,CAAA,GAAG,IAAI;AACnC,aAAA,EAAA,CACI,CACR,CACG,CACD;;;;;;;;;;"}