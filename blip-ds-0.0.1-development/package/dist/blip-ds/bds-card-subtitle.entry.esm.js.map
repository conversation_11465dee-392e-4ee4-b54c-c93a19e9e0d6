{"version": 3, "file": "bds-card-subtitle.entry.esm.js", "sources": ["src/components/card/card-subtitle/card-subtitle.tsx"], "sourcesContent": ["import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-subtitle',\n  shadow: true,\n})\nexport class CardSubtitle {\n  /**\n   *Set the card subtitle.\n   */\n  @Prop() text?: string;\n  render() {\n    return (\n      <bds-typo variant=\"fs-12\" tag=\"p\" bold=\"regular\" margin={false}>\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "names": [], "mappings": ";;MAMa,YAAY,GAAA,MAAA;;;;IAKvB,MAAM,GAAA;AACJ,QAAA,QACE,iEAAU,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,SAAS,EAAC,MAAM,EAAE,KAAK,EAAA,EAC3D,IAAI,CAAC,IAAI,CACD;;;;;;"}