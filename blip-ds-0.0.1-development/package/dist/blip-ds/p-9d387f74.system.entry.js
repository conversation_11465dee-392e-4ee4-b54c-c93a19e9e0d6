var __awaiter=this&&this.__awaiter||function(t,o,i,n){function a(t){return t instanceof i?t:new i((function(o){o(t)}))}return new(i||(i=Promise))((function(i,s){function e(t){try{c(n.next(t))}catch(t){s(t)}}function r(t){try{c(n["throw"](t))}catch(t){s(t)}}function c(t){t.done?i(t.value):a(t.value).then(e,r)}c((n=n.apply(t,o||[])).next())}))};var __generator=this&&this.__generator||function(t,o){var i={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,a,s,e;return e={next:r(0),throw:r(1),return:r(2)},typeof Symbol==="function"&&(e[Symbol.iterator]=function(){return this}),e;function r(t){return function(o){return c([t,o])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(e&&(e=0,r[0]&&(i=0)),i)try{if(n=1,a&&(s=r[0]&2?a["return"]:r[0]?a["throw"]||((s=a["return"])&&s.call(a),0):a.next)&&!(s=s.call(a,r[1])).done)return s;if(a=0,s)r=[r[0]&2,s.value];switch(r[0]){case 0:case 1:s=r;break;case 4:i.label++;return{value:r[1],done:false};case 5:i.label++;a=r[1];r=[0];continue;case 7:r=i.ops.pop();i.trys.pop();continue;default:if(!(s=i.trys,s=s.length>0&&s[s.length-1])&&(r[0]===6||r[0]===2)){i=0;continue}if(r[0]===3&&(!s||r[1]>s[0]&&r[1]<s[3])){i.label=r[1];break}if(r[0]===6&&i.label<s[1]){i.label=s[1];s=r;break}if(s&&i.label<s[2]){i.label=s[2];i.ops.push(r);break}if(s[2])i.ops.pop();i.trys.pop();continue}r=o.call(t,i)}catch(t){r=[6,t];a=0}finally{n=s=0}if(r[0]&5)throw r[1];return{value:r[0]?r[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var o,i,n,a;return{setters:[function(t){o=t.r;i=t.c;n=t.h;a=t.a}],execute:function(){var s=':host .show,:host .hide{display:-ms-flexbox;display:flex}:host .show{opacity:1}:host .show--top-right,:host .show--bottom-right{-webkit-animation:toastAnimationFadeInFromRight 1s;animation:toastAnimationFadeInFromRight 1s}:host .show--top-left,:host .show--bottom-left{-webkit-animation:toastAnimationFadeInFromLeft 1s;animation:toastAnimationFadeInFromLeft 1s}:host .hide{-webkit-transition:all 1s;transition:all 1s;-webkit-animation:toastAnimationFadeOut 0.5s;animation:toastAnimationFadeOut 0.5s}.toast{display:none;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));color:var(--color-content-default, rgb(40, 40, 40));opacity:0;margin-top:16px;overflow:hidden;gap:16px}.toast--action--icon{min-width:440px;max-width:440px;padding:8px 16px}.toast--action--icon bds-icon-button{height:32px}@media (max-width: 780px){.toast--action--icon{min-width:220px;width:95%;margin:16px auto 0px auto}}.toast--action--button{min-width:440px;max-width:456px;padding:8px 16px}@media (max-width: 780px){.toast--action--button{min-width:220px;width:95%;margin:16px auto 0px auto}}.toast--system{background:var(--color-system, rgb(178, 223, 253))}.toast--error{background:var(--color-error, rgb(250, 190, 190))}.toast--success{background:var(--color-success, rgb(132, 235, 188))}.toast--warning{background:var(--color-warning, rgb(253, 233, 155))}.toast--undo{background-color:var(--color-system, rgb(178, 223, 253))}.toast--redo{background-color:var(--color-system, rgb(178, 223, 253))}.toast--notification{background-color:var(--color-surface-1, rgb(246, 246, 246))}.toast__icon{position:relative;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:8px 0}.toast__ballon{display:-ms-flexbox;display:flex;position:absolute;top:-8px;left:-12px;color:var(--color-system, rgb(178, 223, 253));width:72px}.toast__content{position:relative;height:100%;width:100%;-ms-flex-align:start;align-items:flex-start;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;padding:8px 0}.toast__action{display:-ms-flexbox;display:flex;-ms-flex-align:start;align-items:flex-start}.toast__action bds-button-icon,.toast__action bds-button{position:relative}.toast__action bds-button-icon::before,.toast__action bds-button::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.toast__action bds-button-icon:focus-visible,.toast__action bds-button:focus-visible{outline:none}.toast__action bds-button-icon:focus-visible::before,.toast__action bds-button:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.toast__action__button{white-space:nowrap}@-webkit-keyframes toastAnimationFadeInFromRight{0%{opacity:0;right:-200px}50%{opacity:0.9;right:1px}100%{opacity:1}}@keyframes toastAnimationFadeInFromRight{0%{opacity:0;right:-200px}50%{opacity:0.9;right:1px}100%{opacity:1}}@-webkit-keyframes toastAnimationFadeInFromLeft{0%{opacity:0;left:-200px}50%{opacity:0.9;left:1px}100%{opacity:1}}@keyframes toastAnimationFadeInFromLeft{0%{opacity:0;left:-200px}50%{opacity:0.9;left:1px}100%{opacity:1}}@-webkit-keyframes toastAnimationFadeOut{0%{opacity:1}30%{max-height:60px}80%{opacity:0;max-height:30px}100%{max-height:0px}}@keyframes toastAnimationFadeOut{0%{opacity:1}30%{max-height:60px}80%{opacity:0;max-height:30px}100%{max-height:0px}}';var e=t("bds_toast",function(){function t(t){var n=this;o(this,t);this.toastButtonClick=i(this,"toastButtonClick");this.icon=null;this.actionType="button";this.variant="system";this.duration=0;this.buttonAction="close";this.show=false;this.hide=false;this.position="bottom-left";this.dtButtonAction=null;this.dtButtonClose=null;this._buttonClickHandler=function(){if(n.buttonAction==="close")n.close();else{n.toastButtonClick.emit(n.el);n.close()}};this.mapIconName={system:"bell",error:"error",success:"like",warning:"attention",undo:"undo",redo:"redo",notification:"notification"}}t.prototype._keyPressHandler=function(t){if(t.key==="Enter"||t.key===" "){t.preventDefault();if(this.buttonAction==="close")this.close();else{this.toastButtonClick.emit(this.el);this.close()}}};t.prototype.create=function(t){return __awaiter(this,arguments,void 0,(function(t){var o;var i=this;var n=t.actionType,a=t.buttonAction,s=t.buttonText,e=t.icon,r=t.toastText,c=t.toastTitle,h=t.variant,d=t.duration;return __generator(this,(function(t){o=document.querySelector("bds-toast-container.".concat(h==="notification"?"top-right":"bottom-left"));if(o){o.appendChild(this.el);o.classList.add(h==="notification"?"top-right":"bottom-left")}else{o=document.createElement("bds-toast-container");o.classList.add(h==="notification"?"top-right":"bottom-left");document.body.appendChild(o);o.appendChild(this.el)}this.el.actionType=n||"button";this.el.buttonAction=a||"close";this.el.buttonText=s;this.el.toastText=r;this.el.toastTitle=c;this.el.variant=h||"system";this.el.duration=d*1e3||0;this.el.position=h==="notification"?"top-right":"bottom-left";this.el.icon=e!==null&&e!==void 0?e:this.mapIconName[this.variant];this.el.show=true;if(this.el.duration>0){setTimeout((function(){i.el.hide=true;setTimeout((function(){i.el.remove()}),400)}),this.el.duration)}return[2]}))}))};t.prototype.close=function(){return __awaiter(this,void 0,void 0,(function(){var t=this;return __generator(this,(function(o){if(this.el.shadowRoot){this.el.shadowRoot.querySelector("div").classList.remove("show");this.el.shadowRoot.querySelector("div").classList.add("hide")}else{this.el.querySelector("div").classList.remove("show");this.el.querySelector("div").classList.add("hide")}setTimeout((function(){t.el.remove()}),400);return[2]}))}))};t.prototype.render=function(){var t,o;var i=this;return n("div",{key:"3585d014a0d856a1649d48485f6ef1155ab05b6c",class:(t={toast:true},t["toast--".concat(this.variant)]=true,t["toast--action--".concat(this.actionType)]=true,t["show show--".concat(this.position)]=this.show,t.hide=this.hide,t)},this.variant==="notification"&&n("bds-icon",{key:"ca41b6f8f9e28bc4950fe073a30f504b6e25f52e",class:"toast__ballon",theme:"solid",name:"blip-chat",size:"brand"}),this.icon&&n("bds-icon",{key:"1b2f4cc9d69c0fc0d6a3cd60a0027cac55a30446",class:"toast__icon",theme:"outline",size:"medium",name:this.icon}),n("div",{key:"de5c75c32cb5396a3ceab4b0c613da089af34896",class:"toast__content"},this.toastTitle&&n("bds-typo",{key:"aa9efe355172812f38c0b6a4fc360af2dcd75e68",variant:"fs-14",bold:"bold"},this.toastTitle),this.toastText&&n("bds-typo",{key:"8ff8995c947c670f97264d19836c9acba5deeb13",variant:"fs-14",innerHTML:this.toastText})),n("div",{key:"6d66d73091f4799f1d3e1b5962f307d908ce5227",class:(o={toast__action:true},o["toast__action__".concat(this.actionType)]=true,o)},this.actionType==="button"?n("bds-button",{onKeyDown:this._keyPressHandler.bind(this),tabindex:"0",onClick:function(){return i._buttonClickHandler()},variant:"secondary",size:"standard",dataTest:this.dtButtonAction},this.buttonText):n("bds-button-icon",{onClick:function(){return i._buttonClickHandler()},size:"short",onKeyDown:this._keyPressHandler.bind(this),tabindex:"0",variant:"secondary",icon:"close",dataTest:this.dtButtonClose})))};Object.defineProperty(t.prototype,"el",{get:function(){return a(this)},enumerable:false,configurable:true});return t}());e.style=s}}}));
//# sourceMappingURL=p-9d387f74.system.entry.js.map