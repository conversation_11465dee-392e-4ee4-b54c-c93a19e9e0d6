{"version": 3, "file": "bds-input-password.entry.esm.js", "sources": ["src/components/input-password/input-password.scss?tag=bds-input-password&encapsulation=shadow", "src/components/input-password/input-password.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n$input_expanded: 100%;\n$input_fixed: 140px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__password {\n  &--icon {\n    position: relative;\n    color: $color-content-disable;\n    display: flex;\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n      pointer-events: none;\n    }\n    &:focus-visible {\n      outline: none;\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n  }\n}\n", "import { Component, State, Prop, h, Host, Event, EventEmitter, Watch } from '@stencil/core';\nimport { InputAutocapitalize, InputAutoComplete } from '../input/input-interface';\n\n@Component({\n  tag: 'bds-input-password',\n  styleUrl: 'input-password.scss',\n  shadow: true,\n})\nexport class InputPassword {\n  private nativeInput?: HTMLInputElement;\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @Prop() openEyes? = false;\n\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label?: string = '';\n\n  /**\n   * Input Name\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * The maximum value, which must not be less than its minimum (min attribute) value.\n   */\n  @Prop() max?: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the maximum number of characters that the user can enter.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * The minimum value, which must not be greater than its maximum (max attribute) value.\n   */\n  @Prop() min?: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the minimum number of characters that the user can enter.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger?: boolean = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop() disabled? = false;\n\n  /**\n   * Capitalizes every word's second character.\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Hint for form autofill feature\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsInputPasswordChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInputPasswordInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsInputPasswordBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsInputPasswordFocus: EventEmitter;\n\n  /**\n   * Event input enter.\n   */\n  @Event() bdsInputPasswordSubmit: EventEmitter;\n\n  /**\n   * Event input key down backspace.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  private refNativeInput = (el: HTMLInputElement): void => {\n    this.nativeInput = el;\n  };\n\n  private toggleEyePassword = (): void => {\n    if (!this.disabled) {\n      this.openEyes = !this.openEyes;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.toggleEyePassword();\n    }\n  }\n\n  private getAutoComplete(): string {\n    if (!this.openEyes) return 'current-password';\n    return this.autoComplete;\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  @Watch('value')\n  protected onChange(): void {\n    this.bdsInputPasswordChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInputPasswordInput.emit(ev);\n  };\n\n  private onBlur = (): void => {\n    this.bdsInputPasswordBlur.emit();\n    this.isPressed = false;\n  };\n\n  private onFocus = (): void => {\n    this.bdsInputPasswordFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onSubmit = (): void => {\n    this.bdsInputPasswordSubmit.emit();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsInputPasswordSubmit.emit({ event, value: this.value });\n\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const iconPassword = this.openEyes ? 'eye-open' : 'eye-closed';\n    const type = this.openEyes ? 'text' : 'password';\n    const autocomplete = this.getAutoComplete();\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              <input\n                ref={this.refNativeInput}\n                class={{ input__container__text: true }}\n                type={type}\n                name={this.inputName}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                readOnly={this.readonly}\n                autocomplete={autocomplete}\n                autocapitalize={this.autoCapitalize}\n                placeholder={this.placeholder}\n                onInput={this.onInput}\n                onFocus={this.onFocus}\n                onBlur={this.onBlur}\n                onSubmit={this.onSubmit}\n                value={this.value}\n                disabled={this.disabled}\n                data-test={this.dataTest}\n              ></input>\n            </div>\n          </div>\n          <div\n            class=\"input__password--icon\"\n            onClick={this.toggleEyePassword}\n            onKeyDown={this.handleKeyDown.bind(this)}\n            tabindex=\"0\"\n          >\n            <bds-icon size=\"small\" name={iconPassword} color=\"inherit\"></bds-icon>\n          </div>\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,gBAAgB,GAAG,+vRAA+vR;;MCQ3wR,aAAa,GAAA,MAAA;AAL1B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;AAOE;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAC3C;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAEvB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAEzB;;AAEG;AACqC,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;AAElE;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,EAAE;AAE3B;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAsB/B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAExB;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AAEnC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AAClC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACsB,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AACjD;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAEzB;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAyB,KAAK;AAEpD;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAuB,KAAK;AAEhD;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAgCxB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAoB,KAAU;AACtD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,MAAW;AACrC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;;AAElC,SAAC;AAaO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAOO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;AACzC,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;AAEhC,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;AACrC,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE;AACjC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC;AAEO,QAAA,IAAQ,CAAA,QAAA,GAAG,MAAW;AAC5B,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;AACpC,SAAC;AAEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;AACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;AACf,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE9D;AACF,gBAAA,KAAK,WAAW;AAChB,gBAAA,KAAK,QAAQ;AACX,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC3D;;AAEN,SAAC;AA8HF;AAtLS,IAAA,aAAa,CAAC,KAAK,EAAA;AACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE;;;IAIpB,eAAe,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,kBAAkB;QAC7C,OAAO,IAAI,CAAC,YAAY;;IAWhB,QAAQ,GAAA;AAChB,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;;IAsC9F,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;IAGlB,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;AAClD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,YAAY;AAC9D,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,UAAU;AAChD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;AAE3C,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;AAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC7C,CACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,YAAY,EAAE,YAAY,EAC1B,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACZ,WAAA,EAAA,IAAI,CAAC,QAAQ,EACjB,CAAA,CACL,CACF,EACN,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,uBAAuB,EAC7B,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,QAAQ,EAAC,GAAG,EAAA,EAEZ,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAC,OAAO,EAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAC,SAAS,EAAA,CAAY,CAClE,EACL,IAAI,CAAC,OAAO,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F,EACL,IAAI,CAAC,aAAa,EAAE,CAChB;;;;;;;;;;"}