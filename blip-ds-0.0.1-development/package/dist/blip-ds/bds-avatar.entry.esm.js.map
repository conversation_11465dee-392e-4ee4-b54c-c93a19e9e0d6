{"version": 3, "file": "bds-avatar.entry.esm.js", "sources": ["src/components/avatar/color-letter.ts", "src/components/avatar/avatar.scss?tag=bds-avatar&encapsulation=shadow", "src/components/avatar/avatar.tsx"], "sourcesContent": ["export const colorLetter = [\n  { value: 'A', color: 'system' },\n  { value: 'B', color: 'success' },\n  { value: 'C', color: 'warning' },\n  { value: 'D', color: 'error' },\n  { value: 'E', color: 'info' },\n  { value: 'F', color: 'system' },\n  { value: 'G', color: 'success' },\n  { value: 'H', color: 'warning' },\n  { value: 'I', color: 'error' },\n  { value: 'J', color: 'info' },\n  { value: 'K', color: 'system' },\n  { value: 'L', color: 'success' },\n  { value: 'M', color: 'warning' },\n  { value: 'N', color: 'error' },\n  { value: 'O', color: 'info' },\n  { value: 'P', color: 'system' },\n  { value: 'Q', color: 'success' },\n  { value: 'R', color: 'warning' },\n  { value: 'S', color: 'error' },\n  { value: 'T', color: 'info' },\n  { value: 'U', color: 'system' },\n  { value: 'V', color: 'success' },\n  { value: 'X', color: 'warning' },\n  { value: 'Y', color: 'error' },\n  { value: 'W', color: 'info' },\n  { value: 'Z', color: 'system' },\n];\n", "@use '../../globals/helpers' as *;\n\n$avatar-size-micro: 24px;\n$avatar-size-extra-small: 32px;\n$avatar-size-small: 40px;\n$avatar-size-standard: 56px;\n$avatar-size-large: 64px;\n$avatar-size-extra-large: 72px;\n\n:host {\n  position: relative;\n  display: block;\n  width: fit-content;\n}\n.avatar {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  appearance: none;\n  height: 100%;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &::before {\n      border-color: $color-focus;\n    }\n  }\n\n  &__ellipsis {\n    color: $color-surface-1;\n  }\n  &__text {\n    color: $color-content-default;\n  }\n\n  &__icon {\n    color: $color-content-default;\n  }\n\n  &__btn {\n    border-radius: 40px;\n    border: 1px solid $color-border-2;\n    box-sizing: border-box;\n    position: relative;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    &__img {\n      background-position: center;\n      background-size: cover;\n    }\n\n    &__text {\n      color: $color-content-default;\n      opacity: 1;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n    }\n\n    &__icon {\n      color: $color-content-default;\n      opacity: 1;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n    }\n\n    &__thumb {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n\n      &:before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        background-color: $color-content-default;\n        opacity: 0;\n        transition: all 0.5s;\n      }\n\n      &__icon {\n        position: relative;\n        color: $color-surface-1;\n        opacity: 0;\n        transition: all 0.5s;\n      }\n    }\n\n    &__name {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n\n      &__icon {\n        color: $color-content-default;\n      }\n    }\n\n    &__empty {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      top: 0;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      -webkit-transition: all 0.5s;\n      -moz-transition: all 0.5s;\n      transition: all 0.5s;\n\n      &__icon {\n        color: $color-content-default;\n      }\n    }\n  }\n\n  &__size {\n    &--micro {\n      width: $avatar-size-micro;\n      height: $avatar-size-micro;\n      min-width: $avatar-size-micro;\n      min-height: $avatar-size-micro;\n    }\n    &--extra-small {\n      width: $avatar-size-extra-small;\n      height: $avatar-size-extra-small;\n      min-width: $avatar-size-extra-small;\n      min-height: $avatar-size-extra-small;\n    }\n    &--small {\n      width: $avatar-size-small;\n      height: $avatar-size-small;\n      min-width: $avatar-size-small;\n      min-height: $avatar-size-small;\n    }\n    &--standard {\n      width: $avatar-size-standard;\n      height: $avatar-size-standard;\n      min-width: $avatar-size-standard;\n      min-height: $avatar-size-standard;\n    }\n    &--large {\n      width: $avatar-size-large;\n      height: $avatar-size-large;\n      min-width: $avatar-size-large;\n      min-height: $avatar-size-large;\n    }\n    &--extra-large {\n      width: $avatar-size-extra-large;\n      height: $avatar-size-extra-large;\n      min-width: $avatar-size-extra-large;\n      min-height: $avatar-size-extra-large;\n    }\n  }\n\n  &__color {\n    &--system {\n      & .avatar__btn {\n        background-color: $color-system;\n      }\n    }\n    &--warning {\n      & .avatar__btn {\n        background-color: $color-warning;\n      }\n    }\n    &--success {\n      & .avatar__btn {\n        background-color: $color-success;\n      }\n    }\n    &--info {\n      & .avatar__btn {\n        background-color: $color-info;\n      }\n    }\n    &--error {\n      & .avatar__btn {\n        background-color: $color-error;\n      }\n    }\n    &--surface {\n      & .avatar__btn {\n        background-color: $color-surface-2;\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &:hover {\n    .avatar {\n      &__btn {\n        &__thumb {\n          &:before {\n            opacity: 0.5;\n          }\n          &__icon {\n            opacity: 1;\n          }\n        }\n        &__text {\n          opacity: 0;\n        }\n        &__icon {\n          opacity: 0;\n        }\n        &__name {\n          opacity: 1;\n        }\n        &__empty {\n          opacity: 1;\n        }\n      }\n    }\n  }\n}\n\n.focus:focus-visible {\n  display: flex;\n  position: absolute;\n  border: 2px solid $color-focus;\n  border-radius: 4px;\n  width: 100%;\n  height: 100%;\n  top: -4px;\n  left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  outline: none;\n}\n", "import { Component, EventEmitter, h, Prop, Event, Host, State, Element } from '@stencil/core';\nimport { FontSize } from '../typo/typo';\nimport { IconSize } from '../icon/icon-interface';\nimport { colorLetter } from './color-letter';\n\nexport type avatarSize = 'micro' | 'extra-small' | 'small' | 'standard' | 'large' | 'extra-large';\nexport type colors = 'colorLetter' | 'system' | 'success' | 'warning' | 'error' | 'info' | 'surface';\n\n@Component({\n  tag: 'bds-avatar',\n  styleUrl: 'avatar.scss',\n  shadow: true,\n})\nexport class BdsAvatar {\n  @Element() el: HTMLElement;\n  private typoSize?: FontSize = 'fs-20';\n  private iconSize?: IconSize = 'large';\n  @State() hasThumb: boolean;\n  /**\n   * Name, Inserted for highlighted osuary name. Enter the full name.\n   */\n  @Prop() name?: string = null;\n  /**\n   * Thumbnail, Inserted to highlight user image. Url field.\n   */\n  @Prop({ mutable: true }) thumbnail?: string = null;\n  /**\n   * Size, Entered as one of the size. Can be one of:\n   * 'extra-small', 'small', 'standard', 'large', 'extra-large'.\n   */\n  @Prop() size?: avatarSize = 'standard';\n  /**\n   * Color, Entered as one of the color. Can be one of:\n   * 'system', 'success', 'warning', 'error', 'info'.\n   */\n  @Prop() color?: colors = 'colorLetter';\n  /**\n   * Upload, Serve to enable upload function on avatar.\n   */\n  @Prop() upload?: boolean = false;\n  /**\n   * When set to true, allows the avatar to be clicked to select and upload an image.\n   */\n  @Prop() openUpload?: boolean = false;\n  /**\n   * Ellipses, serves to indicate the user number in the listing.\n   */\n  @Prop() ellipsis?: number = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  @Event() bdsClickAvatar: EventEmitter;\n  @Event() bdsImageUpload: EventEmitter;\n\n  private onUploadClick(e) {\n    e.preventDefault();\n    this.bdsClickAvatar.emit(e);\n    if (this.openUpload) {\n      this.handleOpenUpload(e);\n    }\n  }\n\n  handleOpenUpload = (e) => {\n    const file = this.el.shadowRoot.getElementById('file-input');\n    if (e.type === 'click' || (e.type === 'keydown' && (e.key === 'Enter' || e.key === ' '))) {\n      file.click();\n    }\n  };\n\n  private onFileInputChange(event) {\n    const fileInput = event.target as HTMLInputElement;\n    const files = fileInput.files;\n\n    if (files && files.length > 0) {\n      const selectedFile = files[0];\n      const reader = new FileReader();\n\n      reader.onload = (e) => {\n        const imageUrl = e.target.result as string;\n        this.thumbnail = imageUrl;\n        this.bdsImageUpload.emit(imageUrl);\n      };\n\n      reader.readAsDataURL(selectedFile);\n    }\n  }\n\n  private selectTypoSize = (value): void => {\n    switch (value) {\n      case 'micro':\n        this.typoSize = 'fs-12';\n        this.iconSize = 'xx-small';\n        break;\n      case 'extra-small':\n        this.typoSize = 'fs-14';\n        this.iconSize = 'x-small';\n        break;\n      case 'small':\n        this.typoSize = 'fs-16';\n        this.iconSize = 'medium';\n        break;\n      case 'standard':\n        this.typoSize = 'fs-20';\n        this.iconSize = 'x-large';\n        break;\n      case 'large':\n        this.typoSize = 'fs-24';\n        this.iconSize = 'xxx-large';\n        break;\n      case 'extra-large':\n        this.typoSize = 'fs-32';\n        this.iconSize = 'xxx-large';\n        break;\n      default:\n        this.typoSize = 'fs-20';\n        this.iconSize = 'medium';\n    }\n  };\n\n  private avatarBgColor = (letter: string) => {\n    if (this.color != 'colorLetter') {\n      return this.color;\n    } else if (letter) {\n      const currentColor = colorLetter.find((item) => item.value === letter);\n      return currentColor.color;\n    }\n  };\n\n  componentWillRender() {\n    this.hasThumb = this.thumbnail ? (this.thumbnail.length !== 0 ? true : false) : false;\n  }\n\n  render(): HTMLElement {\n    const arrayName = this.name ? this.name.split(' ') : [];\n    const firstName = arrayName.length ? arrayName.shift().charAt(0).toUpperCase() : '';\n    const lastName = arrayName.length ? arrayName.pop().charAt(0).toUpperCase() : '';\n    this.selectTypoSize(this.size);\n    const thumbnailStyle = {\n      backgroundImage: `url(${this.hasThumb ? this.thumbnail : null})`,\n    };\n\n    return (\n      <Host>\n        <input\n          type=\"file\"\n          id=\"file-input\"\n          accept=\"image/*\"\n          onChange={(event) => this.onFileInputChange(event)}\n          style={{ display: 'none' }}\n        ></input>\n        <div\n          class={{\n            avatar: true,\n            [`avatar__color--${\n              this.name && !this.hasThumb\n                ? this.avatarBgColor(firstName)\n                : this.hasThumb && !this.name\n                  ? 'surface'\n                  : !this.name && !this.hasThumb\n                    ? 'surface'\n                    : this.name && this.hasThumb\n                      ? this.avatarBgColor(firstName)\n                      : null\n            }`]: true,\n            [`avatar__size--${this.size}`]: true,\n            upload: this.upload || this.openUpload,\n          }}\n          onClick={(ev) => this.onUploadClick(ev)}\n          tabindex=\"0\"\n          onKeyDown={(ev) => this.onUploadClick(ev)}\n          data-test={this.dataTest}\n        >\n          {this.ellipsis ? (\n            <div class=\"avatar__btn\">\n              <bds-typo margin={false} variant={this.typoSize} tag=\"span\">{`+${this.ellipsis}`}</bds-typo>\n            </div>\n          ) : this.thumbnail ? (\n            this.upload || this.openUpload ? (\n              <div class=\"avatar__btn\">\n                <div class={`avatar__btn__img avatar__size--${this.size}`} style={thumbnailStyle}></div>\n                <div class=\"avatar__btn__thumb\">\n                  <bds-icon\n                    class=\"avatar__btn__thumb__icon\"\n                    name=\"upload\"\n                    theme=\"outline\"\n                    size={this.iconSize}\n                  ></bds-icon>\n                </div>\n              </div>\n            ) : (\n              <div class=\"avatar__btn\">\n                <div class={`avatar__btn__img avatar__size--${this.size}`} style={thumbnailStyle}></div>\n              </div>\n            )\n          ) : this.name ? (\n            this.upload || this.openUpload ? (\n              <div class=\"avatar__btn\">\n                <bds-typo margin={false} class=\"avatar__btn__text\" variant={this.typoSize} tag=\"span\">\n                  {firstName + lastName}\n                </bds-typo>\n                <div class=\"avatar__btn__name\">\n                  <bds-icon\n                    class=\"avatar__btn__name__icon\"\n                    name=\"upload\"\n                    theme=\"outline\"\n                    size={this.iconSize}\n                  ></bds-icon>\n                </div>\n              </div>\n            ) : (\n              <div class=\"avatar__btn\">\n                <bds-typo margin={false} class=\"avatar__text\" variant={this.typoSize} tag=\"span\">\n                  {firstName + lastName}\n                </bds-typo>\n              </div>\n            )\n          ) : this.upload || this.openUpload ? (\n            <div class=\"avatar__btn\">\n              <bds-icon class=\"avatar__btn__icon\" name=\"user-default\" theme=\"outline\" size={this.iconSize}></bds-icon>\n              <div class=\"avatar__btn__empty\">\n                <bds-icon\n                  class=\"avatar__btn__empty__icon\"\n                  name=\"upload\"\n                  theme=\"outline\"\n                  size={this.iconSize}\n                ></bds-icon>\n              </div>\n            </div>\n          ) : this.name === null && !this.hasThumb ? (\n            <div class=\"avatar__btn\">\n              <bds-icon class=\"avatar__icon\" name=\"user-default\" theme=\"outline\" size={this.iconSize}></bds-icon>\n            </div>\n          ) : (\n            ''\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAO,MAAM,WAAW,GAAG;AACzB,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;AAChC,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAA,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;CAChC;;AC3BD,MAAM,SAAS,GAAG,mtIAAmtI;;MCaxtI,SAAS,GAAA,MAAA;AALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AAOU,QAAA,IAAQ,CAAA,QAAA,GAAc,OAAO;AAC7B,QAAA,IAAQ,CAAA,QAAA,GAAc,OAAO;AAErC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC5B;;AAEG;AACsB,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AAClD;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAgB,UAAU;AACtC;;;AAGG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,aAAa;AACtC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAChC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;AACpC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAEhC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAYhC,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC,KAAI;AACvB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC;AAC5D,YAAA,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE;gBACxF,IAAI,CAAC,KAAK,EAAE;;AAEhB,SAAC;AAoBO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAK,KAAU;YACvC,QAAQ,KAAK;AACX,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;oBAC1B;AACF,gBAAA,KAAK,aAAa;AAChB,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;oBACzB;AACF,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;oBACxB;AACF,gBAAA,KAAK,UAAU;AACb,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;oBACzB;AACF,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,WAAW;oBAC3B;AACF,gBAAA,KAAK,aAAa;AAChB,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,WAAW;oBAC3B;AACF,gBAAA;AACE,oBAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,oBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;AAE9B,SAAC;AAEO,QAAA,IAAA,CAAA,aAAa,GAAG,CAAC,MAAc,KAAI;AACzC,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,aAAa,EAAE;gBAC/B,OAAO,IAAI,CAAC,KAAK;;iBACZ,IAAI,MAAM,EAAE;AACjB,gBAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC;gBACtE,OAAO,YAAY,CAAC,KAAK;;AAE7B,SAAC;AAiHF;AAzLS,IAAA,aAAa,CAAC,CAAC,EAAA;QACrB,CAAC,CAAC,cAAc,EAAE;AAClB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;AAWpB,IAAA,iBAAiB,CAAC,KAAK,EAAA;AAC7B,QAAA,MAAM,SAAS,GAAG,KAAK,CAAC,MAA0B;AAClD,QAAA,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK;QAE7B,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,YAAA,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7B,YAAA,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE;AAE/B,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAI;AACpB,gBAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,MAAgB;AAC1C,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;AACpC,aAAC;AAED,YAAA,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC;;;IA6CtC,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK;;IAGvF,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;QACvD,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE;QACnF,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE;AAChF,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,QAAA,MAAM,cAAc,GAAG;AACrB,YAAA,eAAe,EAAE,CAAA,IAAA,EAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAG,CAAA,CAAA;SACjE;AAED,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,IAAI,EAAC,MAAM,EACX,EAAE,EAAC,YAAY,EACf,MAAM,EAAC,SAAS,EAChB,QAAQ,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAClD,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EACnB,CAAA,EACT,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,MAAM,EAAE,IAAI;gBACZ,CAAC,CAAA,eAAA,EACC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACjB,sBAAE,IAAI,CAAC,aAAa,CAAC,SAAS;sBAC5B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC;AACvB,0BAAE;0BACA,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACpB,8BAAE;AACF,8BAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AAClB,kCAAE,IAAI,CAAC,aAAa,CAAC,SAAS;AAC9B,kCAAE,IACZ,CAAE,CAAA,GAAG,IAAI;AACT,gBAAA,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACpC,gBAAA,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU;aACvC,EACD,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EACvC,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EAAA,WAAA,EAC9B,IAAI,CAAC,QAAQ,IAEvB,IAAI,CAAC,QAAQ,IACZ,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EACtB,CAAU,CAAA,UAAA,EAAA,EAAA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAC,MAAM,IAAE,CAAA,CAAA,EAAI,IAAI,CAAC,QAAQ,CAAA,CAAE,CAAY,CACxF,IACJ,IAAI,CAAC,SAAS,IAChB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,IAC5B,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EACtB,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,CAAA,+BAAA,EAAkC,IAAI,CAAC,IAAI,CAAE,CAAA,EAAE,KAAK,EAAE,cAAc,EAAQ,CAAA,EACxF,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,oBAAoB,EAAA,EAC7B,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,0BAA0B,EAChC,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,SAAS,EACf,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAA,CACT,CACR,CACF,KAEN,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EACtB,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,CAAkC,+BAAA,EAAA,IAAI,CAAC,IAAI,CAAE,CAAA,EAAE,KAAK,EAAE,cAAc,EAAQ,CAAA,CACpF,CACP,IACC,IAAI,CAAC,IAAI,IACX,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,IAC5B,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,aAAa,EAAA,EACtB,CAAU,CAAA,UAAA,EAAA,EAAA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAC,mBAAmB,EAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAC,MAAM,EAClF,EAAA,SAAS,GAAG,QAAQ,CACZ,EACX,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,mBAAmB,EAAA,EAC5B,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,yBAAyB,EAC/B,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,SAAS,EACf,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAA,CACT,CACR,CACF,KAEN,CAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,aAAa,EAAA,EACtB,CAAA,CAAA,UAAA,EAAA,EAAU,MAAM,EAAE,KAAK,EAAE,KAAK,EAAC,cAAc,EAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAC,MAAM,EAAA,EAC7E,SAAS,GAAG,QAAQ,CACZ,CACP,CACP,IACC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,IAChC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,aAAa,EAAA,EACtB,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,mBAAmB,EAAC,IAAI,EAAC,cAAc,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAa,CAAA,EACxG,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,oBAAoB,EAAA,EAC7B,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,0BAA0B,EAChC,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,SAAS,EACf,IAAI,EAAE,IAAI,CAAC,QAAQ,GACT,CACR,CACF,IACJ,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IACtC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,aAAa,EAAA,EACtB,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,cAAc,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAA,CAAa,CAC/F,KAEN,EAAE,CACH,CACG,CACD;;;;;;;;"}