var __awaiter=this&&this.__awaiter||function(t,i,e,o){function r(t){return t instanceof e?t:new e((function(i){i(t)}))}return new(e||(e=Promise))((function(e,c){function l(t){try{n(o.next(t))}catch(t){c(t)}}function a(t){try{n(o["throw"](t))}catch(t){c(t)}}function n(t){t.done?e(t.value):r(t.value).then(l,a)}n((o=o.apply(t,i||[])).next())}))};var __generator=this&&this.__generator||function(t,i){var e={label:0,sent:function(){if(c[0]&1)throw c[1];return c[1]},trys:[],ops:[]},o,r,c,l;return l={next:a(0),throw:a(1),return:a(2)},typeof Symbol==="function"&&(l[Symbol.iterator]=function(){return this}),l;function a(t){return function(i){return n([t,i])}}function n(a){if(o)throw new TypeError("Generator is already executing.");while(l&&(l=0,a[0]&&(e=0)),e)try{if(o=1,r&&(c=a[0]&2?r["return"]:a[0]?r["throw"]||((c=r["return"])&&c.call(r),0):r.next)&&!(c=c.call(r,a[1])).done)return c;if(r=0,c)a=[a[0]&2,c.value];switch(a[0]){case 0:case 1:c=a;break;case 4:e.label++;return{value:a[1],done:false};case 5:e.label++;r=a[1];a=[0];continue;case 7:a=e.ops.pop();e.trys.pop();continue;default:if(!(c=e.trys,c=c.length>0&&c[c.length-1])&&(a[0]===6||a[0]===2)){e=0;continue}if(a[0]===3&&(!c||a[1]>c[0]&&a[1]<c[3])){e.label=a[1];break}if(a[0]===6&&e.label<c[1]){e.label=c[1];c=a;break}if(c&&e.label<c[2]){e.label=c[2];e.ops.push(a);break}if(c[2])e.ops.pop();e.trys.pop();continue}a=i.call(t,e)}catch(t){a=[6,t];r=0}finally{o=c=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var i,e,o,r,c;return{setters:[function(t){i=t.r;e=t.c;o=t.h;r=t.H;c=t.a}],execute:function(){var l=':host{display:-ms-flexbox;display:flex;height:-webkit-max-content;height:-moz-max-content;height:max-content;border-radius:4px;-webkit-box-sizing:border-box;box-sizing:border-box;max-width:100%}:host .chip_clickable{display:-ms-flexbox;display:flex;min-width:32px;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;height:24px;border-radius:12px;padding:2px 6px;-ms-flex-align:center;align-items:center;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-pack:center;justify-content:center;position:relative;z-index:1;-ms-flex-negative:0;flex-shrink:0}:host .chip_clickable--container-text--full{width:100%}:host .chip_clickable--container-text--min{width:calc(100% - 36px)}:host .chip_clickable--container-text--half{width:calc(100% - 16px)}:host .chip_clickable--hide{display:none;padding:0;border:none}:host .chip_clickable .chip_focus:focus{position:absolute;width:100%;height:100%;padding:2px;border-radius:4px;outline:var(--color-focus, rgb(194, 38, 251)) solid 2px}:host .chip_clickable--click{cursor:pointer}:host .chip_clickable--click .chip_darker{opacity:0;position:absolute;width:100%;height:100%;border-radius:inherit;z-index:1;-webkit-backdrop-filter:brightness(1);backdrop-filter:brightness(1);-webkit-box-sizing:border-box;box-sizing:border-box}:host .chip_clickable--click:hover .chip_darker{opacity:1;-webkit-backdrop-filter:brightness(0.9);backdrop-filter:brightness(0.9)}:host .chip_clickable--click:active .chip_darker{opacity:1;-webkit-backdrop-filter:brightness(0.8);backdrop-filter:brightness(0.8)}:host .chip_clickable--disabled{cursor:default;background-color:var(--color-surface-3, rgb(227, 227, 227))}:host .chip_clickable--disabled .chip_clickable--icon{color:var(--color-content-default, rgb(40, 40, 40))}:host .chip_clickable--disabled .chip_clickable--text{color:var(--color-content-default, rgb(40, 40, 40))}:host .chip_clickable--disabled .chip_clickable--close{cursor:default}:host .chip_clickable--text{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:16px;margin:0;padding:0 2px;z-index:2;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;line-height:1}:host .chip_clickable--icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:16px;padding-right:2px;z-index:2}:host .chip_clickable--close{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:16px;padding-left:2px;mix-blend-mode:hard-light;opacity:0.5;z-index:2;position:relative;cursor:pointer}:host .chip_clickable--close .close_focus:focus{position:absolute;width:100%;height:100%;left:-2px;border-radius:4px;outline:var(--color-focus, rgb(194, 38, 251)) solid 2px}:host .chip_clickable--tall{height:32px;border-radius:16px;padding:4px 8px}:host .chip_clickable--tall .chip_clickable--text{height:20px;line-height:1.1}:host .chip_clickable--tall .chip_clickable--icon{height:20px;padding-right:4px}:host .chip_clickable--tall .chip_clickable--close{height:20px;padding-left:4px}:host .chip_clickable--default{background-color:var(--color-system, rgb(178, 223, 253));color:var(--color-content-din, rgb(0, 0, 0))}:host .chip_clickable--info{background-color:var(--color-info, rgb(128, 227, 235));color:var(--color-content-din, rgb(0, 0, 0))}:host .chip_clickable--success{background-color:var(--color-success, rgb(132, 235, 188));color:var(--color-content-din, rgb(0, 0, 0))}:host .chip_clickable--warning{background-color:var(--color-warning, rgb(253, 233, 155));color:var(--color-content-din, rgb(0, 0, 0))}:host .chip_clickable--danger{background-color:var(--color-error, rgb(250, 190, 190));color:var(--color-content-din, rgb(0, 0, 0))}:host .chip_clickable--outline{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));color:var(--color-content-default, rgb(40, 40, 40))}:host .chip_clickable:focus-visible{outline:none}';var a=t("bds_chip_clickable",function(){function t(t){i(this,t);this.chipClickableClose=e(this,"chipClickableClose");this.chipClickableClick=e(this,"chipClickableClick");this.visible=true;this.color="default";this.size="standard";this.clickable=false;this.close=false;this.disabled=false;this.dataTest=null;this.dtButtonClose=null}t.prototype.handleClickKey=function(t){if((t.key==="Enter"||t.key===" ")&&!this.disabled){t.preventDefault();this.chipClickableClick.emit()}};t.prototype.handleClick=function(t){if(!this.disabled){t.preventDefault();this.chipClickableClick.emit()}};t.prototype.handleCloseChip=function(t){t.preventDefault();this.chipClickableClose.emit({id:this.element.id})};t.prototype.handleCloseKey=function(t){if((t.key==="Enter"||t.key===" ")&&!this.disabled){t.preventDefault();this.chipClickableClose.emit({id:this.element.id})}};t.prototype.getSizeAvatarChip=function(){if(this.size==="tall"){return"extra-small"}else return"micro"};t.prototype.getSizeIconChip=function(){if(this.size==="tall"){return"medium"}else return"x-small"};t.prototype.render=function(){var t;return o(r,{key:"701930b3271cd381aee70709c6f3992ed98e6f0d"},o("div",{key:"c0bf981012d37bef741293a72811fee28ae55657",class:(t={chip_clickable:true},t["chip_clickable--".concat(this.color)]=!this.disabled,t["chip_clickable--".concat(this.size)]=true,t["chip_clickable--hide"]=!this.visible,t["chip_clickable--click"]=this.clickable,t["chip_clickable--disabled"]=this.disabled,t),onClick:this.handleClick.bind(this),"data-test":this.dataTest},this.clickable&&!this.disabled&&o("div",{key:"a80c97c41fe2f042b17bfbef4f905c968991532b",class:"chip_focus",onKeyDown:this.handleClickKey.bind(this),tabindex:"0"}),this.clickable&&!this.disabled&&o("div",{key:"f9bd96ff044af8e25ba8e564f6bcb86709079735",class:"chip_darker"}),this.icon&&!this.avatar&&o("div",{key:"056c7ef70c4811b59a3404191d179aad68c2fcdd",class:"chip_clickable--icon"},o("bds-icon",{key:"7c11d82f56fa7426ee6b09c46c9ecde97038561c",size:this.getSizeIconChip(),name:this.icon})),this.avatar&&o("div",{key:"e5dbf9e23452f19f430883caa21db0dc0919a632",class:"chip_clickable--avatar"},o("bds-avatar",{key:"6caa3097f9427b899e5c8c9a8b500f10039f5172",size:this.getSizeAvatarChip(),thumbnail:this.avatar})),o("div",{key:"db9ccaaa668cd48cee88b0ab482aa0c56fdef6dc",class:this.close&&(this.icon||this.avatar)?"chip_clickable--container-text--min":!this.close&&!this.icon&&!this.avatar?"chip_clickable--container-text--full":"chip_clickable--container-text--half"},o("bds-typo",{key:"6c7e0888d9a1dcaec0ac6c362747f73820c8776a","no-wrap":"true",class:"chip_clickable--text",variant:"fs-12",bold:"bold"},o("slot",{key:"86c4b319a3bc1be4f215769deffd5da6131e1a38"}))),this.close&&o("div",{key:"546aa9f33a01e1c01be3221c09854d587e8d2f0d",class:"chip_clickable--close","data-test":this.dtButtonClose,onClick:this.handleCloseChip.bind(this)},!this.disabled&&o("div",{key:"42b605c6ef58d026b236f3e59abbe9d0de5eb629",class:"close_focus",onKeyDown:this.handleCloseKey.bind(this),tabindex:"0"}),o("bds-icon",{key:"20ca03f78f4baea77931d48e9aa5af9abd64119f",size:"x-small",theme:"solid",name:"error"}))))};Object.defineProperty(t.prototype,"element",{get:function(){return c(this)},enumerable:false,configurable:true});return t}());a.style=l;var n='.tooltip__wrapper{display:inline-block;position:relative}.tooltip__tip{position:absolute;left:50%;border-radius:8px;padding:8px;background:var(--color-content-default, rgb(40, 40, 40));z-index:90000;white-space:normal;width:-webkit-max-content;width:-moz-max-content;width:max-content;min-width:32px;max-width:320px;-webkit-box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));visibility:hidden;-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out;-webkit-box-sizing:border-box;box-sizing:border-box;cursor:default}.tooltip__tip--visible{visibility:visible}.tooltip__tip::before{content:"";left:50%;border:solid transparent;height:0;width:0;position:absolute;pointer-events:none;margin-left:-6px;border-width:6px}.tooltip__tip--top-center,.tooltip__tip--top-left,.tooltip__tip--top-right{bottom:calc(100% + 10px)}.tooltip__tip--top-center::before,.tooltip__tip--top-left::before,.tooltip__tip--top-right::before{top:100%;border-top-color:var(--color-content-default, rgb(40, 40, 40))}.tooltip__tip--top-left{left:0;-webkit-transform:translateX(-15%);transform:translateX(-15%)}.tooltip__tip--top-left::before{left:calc(15% + 6px)}.tooltip__tip--top-right{left:initial;right:0;-webkit-transform:translateX(15%);transform:translateX(15%)}.tooltip__tip--top-right::before{left:calc(85% - 6px)}.tooltip__tip--bottom-center,.tooltip__tip--top-center{-webkit-transform:translateX(-50%);transform:translateX(-50%)}.tooltip__tip--left-center,.tooltip__tip--right-center{-webkit-transform:translateX(0) translateY(-50%);transform:translateX(0) translateY(-50%)}.tooltip__tip--right-center,.tooltip__tip--right-top,.tooltip__tip--right-bottom{left:calc(100% + 10px);top:50%}.tooltip__tip--right-center::before,.tooltip__tip--right-top::before,.tooltip__tip--right-bottom::before{left:-5px;top:50%;-webkit-transform:translateX(0) translateY(-50%);transform:translateX(0) translateY(-50%);border-right-color:var(--color-content-default, rgb(40, 40, 40))}.tooltip__tip--right-top{top:0}.tooltip__tip--right-top::before{top:40%}.tooltip__tip--right-bottom{top:initial;bottom:0}.tooltip__tip--right-bottom::before{top:60%}.tooltip__tip--bottom-center,.tooltip__tip--bottom-right,.tooltip__tip--bottom-left{top:calc(100% + 10px)}.tooltip__tip--bottom-center::before,.tooltip__tip--bottom-right::before,.tooltip__tip--bottom-left::before{bottom:100%;border-bottom-color:var(--color-content-default, rgb(40, 40, 40))}.tooltip__tip--bottom-right{left:initial;right:0;-webkit-transform:translateX(15%);transform:translateX(15%)}.tooltip__tip--bottom-right::before{left:calc(85% - 6px)}.tooltip__tip--bottom-left{left:0;-webkit-transform:translateX(-15%);transform:translateX(-15%)}.tooltip__tip--bottom-left::before{left:calc(15% + 6px)}.tooltip__tip--left-center,.tooltip__tip--left-top,.tooltip__tip--left-bottom{left:auto;right:calc(100% + 10px);top:50%}.tooltip__tip--left-center::before,.tooltip__tip--left-top::before,.tooltip__tip--left-bottom::before{left:auto;right:-11px;top:50%;-webkit-transform:translateX(0) translateY(-50%);transform:translateX(0) translateY(-50%);border-left-color:var(--color-content-default, rgb(40, 40, 40))}.tooltip__tip--left-top{top:0}.tooltip__tip--left-top::before{top:40%}.tooltip__tip--left-bottom{top:initial;bottom:0}.tooltip__tip--left-bottom::before{top:60%}.tooltip__tip__text pre{margin:0;display:-ms-flexbox;display:flex;font-family:inherit;white-space:break-spaces}.tooltip__tip__text .text{color:var(--color-surface-1, rgb(246, 246, 246))}';var s=t("bds_tooltip",function(){function t(t){i(this,t);this.isMouseOver=false;this.tooltipText="Tooltip";this.disabled=false;this.position="left-center";this.maxWidth="320px";this.dataTest=null}t.prototype.visible=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isMouseOver=true;return[2]}))}))};t.prototype.invisible=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isMouseOver=false;return[2]}))}))};t.prototype.setVisibility=function(t){if(this.disabled){this.isMouseOver=false;return}this.isMouseOver=t};t.prototype.componentWillLoad=function(){this.textVerify=this.tooltipText?this.tooltipText.replace(/<br>/gi,"\r\n"):"";this.maxWidtTooltip=this.maxWidth};t.prototype.tooltipTextChanged=function(){this.textVerify=this.tooltipText?this.tooltipText.replace(/<br>/gi,"\r\n"):""};t.prototype.maxWidthChanged=function(){this.maxWidtTooltip=this.maxWidth};t.prototype.render=function(){var t;var i=this;var e={maxWidth:this.maxWidtTooltip};return o("div",{key:"00b09b6886eb74ac91fbea2c9fbe3d792d5bd6cb",class:"tooltip__wrapper"},o("div",{key:"e5026004be5450694921a9741f070f620dab6924",onMouseEnter:function(){return i.setVisibility(true)},onMouseLeave:function(){return i.setVisibility(false)},"data-test":this.dataTest},o("slot",{key:"ea0363fa79c3ab2ee36cc0d981025a9dc51cf403"})),o("div",{key:"cb0da1586a8e66456295a212cfabb1e51831c123",class:(t={tooltip__tip:true},t["tooltip__tip--".concat(this.position)]=true,t["tooltip__tip--visible"]=this.isMouseOver,t),style:e},o("div",{key:"3c5bf7638affb6719452ed26d67675028cb39510",class:{tooltip__tip__text:true}},o("pre",{key:"d330774b5fd7140bf3f7ccb75dc096f33162d587"},o("bds-typo",{key:"df2356e9b99a38cb444ed77b283fdedd0c331770",class:"text","no-wrap":"false",variant:"fs-12"},this.textVerify)))))};Object.defineProperty(t,"watchers",{get:function(){return{tooltipText:["tooltipTextChanged"],maxWidth:["maxWidthChanged"]}},enumerable:false,configurable:true});return t}());s.style=n}}}));
//# sourceMappingURL=p-7b349890.system.entry.js.map