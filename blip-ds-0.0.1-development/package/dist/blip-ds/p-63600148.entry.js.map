{"version": 3, "names": ["selectCss", "SelectChips", "constructor", "hostRef", "this", "isOpen", "intoView", "selectedOptions", "validationDanger", "isPressed", "validationMesage", "internalChips", "chips", "newPrefix", "value", "danger", "success", "errorMessage", "disabled", "label", "icon", "duplicated", "canAddNew", "notFoundMessage", "type", "delimiters", "disableSubmit", "helperMessage", "successMessage", "inputName", "placeholder", "optionsPosition", "dataTest", "handleChangeChipsValue", "async", "resetFilterOptions", "refDropdown", "el", "dropElement", "refIconDrop", "iconDropElement", "toggle", "handler", "event", "detail", "selectedOption", "text", "getText", "addChip", "bdsChangeChips", "emit", "data", "bdsChange", "handlerNewOption", "childOptions", "find", "option", "getTextFromOption", "opt", "internalOptions", "internalOption", "titleText", "_b", "_a", "textContent", "trim", "setFocusWrapper", "nativeInput", "focus", "removeFocusWrapper", "blur", "onClickWrapper", "onFocus", "bdsFocus", "onInput", "ev", "input", "target", "bdsSelectChipsInput", "changedInputValue", "keyPressWrapper", "key", "handleDelimiters", "setChip", "length", "removeLastChip", "filterOptions", "isOpenChanged", "positionHeightDrop", "name", "setDefaultPlacement", "validatePositionDrop", "handleWindow", "contains", "optionsChanged", "options", "JSON", "parse", "e", "valueChanged", "internalValueChanged", "map", "item", "validValueChip", "selectOption", "<PERSON><PERSON><PERSON><PERSON>", "validateChips", "getChips", "clear", "add", "setFocus", "removeFocus", "componentWillLoad", "getScrollParent", "componentDidLoad", "classList", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "connectedCallback", "addEventListener", "childOptionsEnabled", "Array", "from", "shadowRoot", "querySelectorAll", "term", "isExistsChip", "existsChip", "optionTextLower", "toLowerCase", "termLower", "setAttribute", "includes", "removeAttribute", "optionChip", "some", "chip", "enableCreateOption", "validateChip", "handleOnBlur", "bdsBlur", "verifyAndSubstituteDelimiters", "match", "newValue", "replace", "substring", "existTerm", "clearInputValues", "words", "split", "for<PERSON>ach", "word", "trimStart", "handleChange", "exists", "whitespaceValidation", "trimmedName", "emailValidation", "slice", "removeChip", "id", "filter", "_chip", "index", "toString", "renderChips", "limit", "h", "color", "close", "onChipClickableClose", "position", "renderIcon", "class", "input__icon", "size", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "undefined", "<PERSON><PERSON>ey", "render", "tabindex", "onBlur", "element_input", "onClick", "input__container__wrapper", "style", "height", "maxHeight", "ref", "input__container__text", "maxlength", "onChange", "onKeyDown", "select__options", "onOptionSelected", "status"], "sources": ["src/components/selects/select.scss?tag=bds-select-chips&encapsulation=shadow", "src/components/selects/select-chips/select-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, Element, h, Prop, Method, Event, EventEmitter, Listen, Watch, State } from '@stencil/core';\nimport { Option, SelectChangeEvent, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\nimport { emailValidation, whitespaceValidation } from '../../../utils/validations';\nimport { InputChipsTypes } from '../../input-chips/input-chips-interface';\n\n@Component({\n  tag: 'bds-select-chips',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class SelectChips {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @State() internalOptions: Option[];\n\n  @Element() el!: HTMLElement;\n\n  @State() isOpen? = false;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() selectedOptions: { label: string; value: any }[] = [];\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  @State() selectedOption: number;\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true }) options?: string | Option[];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * Used for add prefix on new option select.\n   */\n  @Prop({ reflect: true }) newPrefix?: string = '';\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Set maximum length value for the chip content\n   */\n\n  @Prop() maxlength?: number;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() canAddNew?: boolean = true;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() notFoundMessage?: string = 'No results found';\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSelectChipsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('options')\n  protected optionsChanged(): void {\n    if (typeof this.options === 'string') {\n      try {\n        this.internalOptions = JSON.parse(this.options);\n      } catch (e) {}\n    } else {\n      this.internalOptions = this.options;\n    }\n  }\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.handleChangeChipsValue();\n\n    if (this.internalChips.length > 0) {\n      this.selectedOptions = this.internalChips.map((item) => {\n        return {\n          label: item,\n          value: `${this.validValueChip(item, this.childOptions)}`,\n        };\n      });\n    }\n  }\n\n  private validValueChip(value, internalOptions: HTMLBdsSelectOptionElement[]): string {\n    const selectOption = internalOptions?.find((option) => option.textContent == value);\n    return `${selectOption ? selectOption.value : value}`;\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async getChips(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n    this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  async componentDidLoad() {\n    await this.resetFilterOptions();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  async connectedCallback() {\n    for (const option of this.childOptions) {\n      option.addEventListener('optionSelected', this.handler);\n    }\n  }\n\n  private get childOptionsEnabled(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(\n          this.el.shadowRoot.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'),\n        )\n      : Array.from(this.el.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'));\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'))\n      : Array.from(this.el.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'));\n  }\n\n  private handleChangeChipsValue = async () => {\n    await this.resetFilterOptions();\n  };\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n      return;\n    }\n\n    for (const option of this.childOptions) {\n      const isExistsChip = this.existsChip(option.textContent, await this.getChips());\n      const optionTextLower = option.textContent.toLowerCase();\n      const termLower = term.toLowerCase();\n\n      if (isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n\n      if (term && optionTextLower.includes(termLower) && !isExistsChip) {\n        option.removeAttribute('invisible');\n      }\n\n      if (term && !optionTextLower.includes(termLower) && !isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n    }\n  }\n\n  private async resetFilterOptions() {\n    for (const option of this.childOptions) {\n      if (this.existsChip(option.textContent, await this.getChips())) {\n        option.setAttribute('invisible', 'invisible');\n      } else {\n        option.removeAttribute('invisible');\n      }\n    }\n  }\n\n  private refDropdown = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private existsChip(optionChip: string, chips: string[]) {\n    return chips.some((chip) => optionChip === chip);\n  }\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handler = async (event: CustomEvent) => {\n    const {\n      detail: { value },\n    } = event;\n    this.selectedOption = value;\n    const text = this.getText(value);\n    await this.addChip(text);\n\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n    this.toggle();\n  };\n\n  private handlerNewOption = async (text: string) => {\n    await this.addChip(text);\n    this.toggle();\n  };\n\n  private enableCreateOption(): boolean {\n    return !!(this.childOptionsEnabled.length === 0 && this.nativeInput && this.nativeInput.value);\n  }\n\n  private async addChip(chip: string) {\n    await this.setChip(chip);\n    this.nativeInput.value = '';\n  }\n\n  private getText = (value: string) => {\n    const el: HTMLBdsSelectOptionElement = this.childOptions.find((option) => option.value === value);\n    return this.getTextFromOption(el);\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.textContent?.trim() ?? '');\n  };\n\n  private setFocusWrapper = (): void => {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private removeFocusWrapper = (): void => {\n    this.nativeInput.blur();\n  };\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsSelectChipsInput.emit(ev);\n    this.changedInputValue();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        if (this.canAddNew !== false) {\n          this.handleDelimiters();\n          this.setChip(this.value);\n          this.value = '';\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowUp':\n        if (!this.disabled) {\n          this.isOpen = false;\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.handleChangeChipsValue;\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        break;\n    }\n  };\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    this.changedInputValue;\n    const {\n      detail: { value },\n    } = event;\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private changedInputValue = async () => {\n    this.value = this.nativeInput.value;\n\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      await this.resetFilterOptions();\n    }\n\n    if (this.value && this.isOpen === false) {\n      this.isOpen = true;\n    }\n  };\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n            >\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  private generateKey(value: string) {\n    return value.toLowerCase().replace(/ /g, '-');\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n\n    let internalOptions: Option[] = [];\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        try {\n          internalOptions = JSON.parse(this.options);\n        } catch (e) {}\n      } else {\n        internalOptions = this.options;\n      }\n    }\n\n    return (\n      <div class=\"select\" tabindex=\"0\" onFocus={this.setFocusWrapper} onBlur={this.removeFocusWrapper}>\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null} onClick={this.toggle}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                {this.internalChips.length > 0 && (\n                  <span style={{ height: this.height, maxHeight: this.maxHeight }} class=\"inside-input-left\">\n                    {this.renderChips()}\n                  </span>\n                )}\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class={{ input__container__text: true }}\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n        >\n          {internalOptions.map((option) => (\n            <bds-select-option\n              key={this.generateKey(option.value)}\n              onOptionSelected={this.handler}\n              value={option.value}\n              status={option.status}\n            >\n              {option.label}\n            </bds-select-option>\n          ))}\n          <slot />\n          {this.canAddNew === true && this.enableCreateOption() && (\n            <bds-select-option\n              id=\"option-add\"\n              value=\"add\"\n              onClick={() => this.handlerNewOption(this.nativeInput.value)}\n            >\n              {this.newPrefix}\n              {this.nativeInput.value}\n            </bds-select-option>\n          )}\n          {!this.canAddNew && this.enableCreateOption() && (\n            <bds-select-option id=\"no-option\" value=\"add\">\n              {this.notFoundMessage}\n            </bds-select-option>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "+IAAA,MAAMA,EAAY,qmU,MCWLC,EAAW,MALxB,WAAAC,CAAAC,G,uRAeWC,KAAMC,OAAI,MAEVD,KAAQE,SAAiB,KAEzBF,KAAeG,gBAAoC,GAInDH,KAAgBI,iBAAa,MAI7BJ,KAASK,UAAI,MAKbL,KAAgBM,iBAAI,GAEpBN,KAAaO,cAAa,GAiBVP,KAAKQ,MAAsB,GAK3BR,KAASS,UAAY,GAKrBT,KAAKU,MAAmB,GAKTV,KAAMW,OAAI,MAIVX,KAAOY,QAAa,MAUnCZ,KAAYa,aAAI,GAKhBb,KAAQc,SAAI,MAK7Bd,KAAKe,MAAI,GAKQf,KAAIgB,KAAY,GAKjChB,KAAUiB,WAAa,MAKvBjB,KAASkB,UAAa,KAKtBlB,KAAemB,gBAAY,mBAM3BnB,KAAIoB,KAAoB,OAKxBpB,KAAUqB,WAAI,MAKdrB,KAAasB,cAAG,MAIhBtB,KAAauB,cAAY,GAIRvB,KAAcwB,eAAY,GAI3CxB,KAASyB,UAAY,GAKrBzB,KAAW0B,YAAY,GAKS1B,KAAe2B,gBAA+B,OAY9E3B,KAAQ4B,SAAY,KAwNpB5B,KAAsB6B,uBAAGC,gBACzB9B,KAAK+B,oBAAoB,EAsCzB/B,KAAAgC,YAAeC,IACrBjC,KAAKkC,YAAcD,CAAE,EAGfjC,KAAAmC,YAAeF,IACrBjC,KAAKoC,gBAAkBH,CAAE,EAOnBjC,KAAMqC,OAAG,KACf,IAAKrC,KAAKc,SAAU,CAClBd,KAAKC,QAAUD,KAAKC,M,GAIhBD,KAAAsC,QAAUR,MAAOS,IACvB,MACEC,QAAQ9B,MAAEA,IACR6B,EACJvC,KAAKyC,eAAiB/B,EACtB,MAAMgC,EAAO1C,KAAK2C,QAAQjC,SACpBV,KAAK4C,QAAQF,GAEnB1C,KAAK6C,eAAeC,KAAK,CAAEC,KAAM/C,KAAKO,cAAeG,MAAOV,KAAKyC,iBACjEzC,KAAKgD,UAAUF,KAAK,CAAEC,KAAM/C,KAAKG,kBACjCH,KAAKqC,QAAQ,EAGPrC,KAAAiD,iBAAmBnB,MAAOY,UAC1B1C,KAAK4C,QAAQF,GACnB1C,KAAKqC,QAAQ,EAYPrC,KAAA2C,QAAWjC,IACjB,MAAMuB,EAAiCjC,KAAKkD,aAAaC,MAAMC,GAAWA,EAAO1C,QAAUA,IAC3F,OAAOV,KAAKqD,kBAAkBpB,EAAG,EAG3BjC,KAAAqD,kBAAqBC,I,QAC3B,GAAItD,KAAKuD,gBAAiB,CACxB,MAAMC,EAAiBxD,KAAKuD,gBAAgBJ,MAAMC,GAAWA,EAAO1C,QAAS4C,IAAA,MAAAA,SAAA,SAAAA,EAAK5C,SAClF,GAAI8C,EAAgB,CAClB,OAAOA,EAAezC,K,EAG1B,OAAOuC,IAAA,MAAAA,SAAG,SAAHA,EAAKG,WAAYH,EAAIG,WAAaC,GAAAC,EAAAL,IAAG,MAAHA,SAAA,SAAAA,EAAKM,eAAW,MAAAD,SAAA,SAAAA,EAAEE,UAAU,MAAAH,SAAA,EAAAA,EAAA,EAAG,EAGlE1D,KAAe8D,gBAAG,KACxB,GAAI9D,KAAK+D,YAAa,CACpB/D,KAAK+D,YAAYC,O,GAIbhE,KAAkBiE,mBAAG,KAC3BjE,KAAK+D,YAAYG,MAAM,EAWjBlE,KAAcmE,eAAG,KACvBnE,KAAKoE,UACL,GAAIpE,KAAK+D,YAAa,CACpB/D,KAAK+D,YAAYC,O,GAIbhE,KAAOoE,QAAG,KAChBpE,KAAKqE,SAASvB,OACd9C,KAAKK,UAAY,IAAI,EAQfL,KAAAsE,QAAWC,IACjB,MAAMC,EAAQD,EAAGE,OACjB,GAAID,EAAO,CACTxE,KAAKU,MAAQ8D,EAAM9D,OAAS,E,CAE9BV,KAAK0E,oBAAoB5B,KAAKyB,GAC9BvE,KAAK2E,mBAAmB,EAGlB3E,KAAA4E,gBAAmBrC,IACzB,OAAQA,EAAMsC,KACZ,IAAK,QACH,GAAI7E,KAAKkB,YAAc,MAAO,CAC5BlB,KAAK8E,mBACL9E,KAAK+E,QAAQ/E,KAAKU,OAClBV,KAAKU,MAAQ,GACbV,KAAK6C,eAAeC,KAAK,CAAEC,KAAM/C,KAAKO,cAAeG,MAAOV,KAAKyC,iBACjEzC,KAAKgD,UAAUF,KAAK,CAAEC,KAAM/C,KAAKG,iB,CAEnC,IAAKH,KAAKc,SAAU,CAClBd,KAAKC,OAAS,I,CAEhB,MACF,IAAK,YACH,IAAKD,KAAKc,SAAU,CAClBd,KAAKC,OAAS,I,CAEhB,MACF,IAAK,UACH,IAAKD,KAAKc,SAAU,CAClBd,KAAKC,OAAS,K,CAEhB,MACF,IAAK,YACL,IAAK,SACH,IAAKD,KAAKU,QAAU,MAAQV,KAAKU,MAAMsE,QAAU,IAAMhF,KAAKO,cAAcyE,OAAQ,CAChFhF,KAAKiF,iBAELjF,KAAK6C,eAAeC,KAAK,CAAEC,KAAM/C,KAAKO,cAAeG,MAAOV,KAAKyC,iBACjEzC,KAAKgD,UAAUF,KAAK,CAAEC,KAAM/C,KAAKG,iB,CAEnC,M,EAoEEH,KAAiB2E,kBAAG7C,UAC1B9B,KAAKU,MAAQV,KAAK+D,YAAYrD,MAE9B,GAAIV,KAAK+D,YAAYrD,MAAO,OACpBV,KAAKkF,cAAclF,KAAK+D,YAAYrD,M,KACrC,OACCV,KAAK+B,oB,CAGb,GAAI/B,KAAKU,OAASV,KAAKC,SAAW,MAAO,CACvCD,KAAKC,OAAS,I,EAsPnB,CArqBW,aAAAkF,CAAclF,GACtB,GAAID,KAAKoF,oBAAsB,SAAU,CACvCpF,KAAKoC,gBAAgBiD,KAAOrF,KAAKC,OAAS,WAAa,Y,KAClD,CACLD,KAAKoC,gBAAgBiD,KAAOrF,KAAKC,OAAS,aAAe,U,CAE3D,GAAIA,EACF,GAAID,KAAK2B,iBAAmB,OAAQ,CAClC3B,KAAKsF,oBAAoBtF,KAAK2B,gB,KACzB,CACL3B,KAAKuF,sB,EAKX,YAAAC,CAAajB,GACX,IAAKvE,KAAKiC,GAAGwD,SAASlB,EAAGE,QAA6B,CACpDzE,KAAKC,OAAS,K,EAKR,cAAAyF,GACR,UAAW1F,KAAK2F,UAAY,SAAU,CACpC,IACE3F,KAAKuD,gBAAkBqC,KAAKC,MAAM7F,KAAK2F,Q,CACvC,MAAOG,GAAG,C,KACP,CACL9F,KAAKuD,gBAAkBvD,KAAK2F,O,EAQtB,YAAAI,GACR,GAAI/F,KAAKQ,MAAO,CACd,UAAWR,KAAKQ,QAAU,SAAU,CAClC,IACER,KAAKO,cAAgBqF,KAAKC,MAAM7F,KAAKQ,M,CACrC,MAAAmD,GACA3D,KAAKO,cAAgB,E,MAElB,CACLP,KAAKO,cAAgBP,KAAKQ,K,MAEvB,CACLR,KAAKO,cAAgB,E,EAKf,oBAAAyF,GACRhG,KAAK6B,yBAEL,GAAI7B,KAAKO,cAAcyE,OAAS,EAAG,CACjChF,KAAKG,gBAAkBH,KAAKO,cAAc0F,KAAKC,IACtC,CACLnF,MAAOmF,EACPxF,MAAO,GAAGV,KAAKmG,eAAeD,EAAMlG,KAAKkD,mB,EAMzC,cAAAiD,CAAezF,EAAO6C,GAC5B,MAAM6C,EAAe7C,IAAe,MAAfA,SAAA,SAAAA,EAAiBJ,MAAMC,GAAWA,EAAOQ,aAAelD,IAC7E,MAAO,GAAG0F,EAAeA,EAAa1F,MAAQA,G,CAOhD,aAAM2F,GACJ,OAAOrG,KAAKsG,e,CAOd,cAAMC,GACJ,OAAOvG,KAAKO,a,CAOd,WAAMiG,GACJxG,KAAKO,cAAgB,GACrBP,KAAKU,MAAQ,E,CAIf,SAAM+F,CAAI/F,GACRV,KAAK8E,mBACL,GAAIpE,EAAO,CACTV,KAAK+E,QAAQrE,E,KACR,CACLV,KAAK+E,QAAQ/E,KAAKU,M,CAEpBV,KAAKU,MAAQ,E,CAIf,cAAMgG,GACJ1G,KAAK+D,YAAYC,O,CAInB,iBAAM2C,GACJ3G,KAAK+D,YAAYG,M,CAGnB,iBAAA0C,GACE5G,KAAK+F,eACL/F,KAAK0F,iBACL1F,KAAKE,SAAW2G,EAAgB7G,KAAKiC,G,CAGvC,sBAAM6E,SACE9G,KAAK+B,qBACX,GAAI/B,KAAK2B,iBAAmB,OAAQ,CAClC3B,KAAKsF,oBAAoBtF,KAAK2B,gB,KACzB,CACL3B,KAAKuF,sB,EAID,mBAAAD,CAAoB5E,GAC1B,GAAIA,GAAS,SAAU,CACrBV,KAAKkC,YAAY6E,UAAUN,IAAI,oCAC/BzG,KAAKoC,gBAAgBiD,KAAO,Y,KACvB,CACLrF,KAAKkC,YAAY6E,UAAUN,IAAI,iCAC/BzG,KAAKoC,gBAAgBiD,KAAO,U,EAIxB,oBAAAE,GACN,MAAMyB,EAAgBC,EAAwB,CAC5CC,cAAelH,KAAKiC,GACpBkF,eAAgBnH,KAAKkC,YACrBhC,SAAUF,KAAKE,WAEjBF,KAAKoF,mBAAqB4B,EAAcI,EACxC,GAAIJ,EAAcI,GAAK,SAAU,CAC/BpH,KAAKkC,YAAY6E,UAAUN,IAAI,oCAC/BzG,KAAKoC,gBAAgBiD,KAAO,Y,KACvB,CACLrF,KAAKkC,YAAY6E,UAAUN,IAAI,iCAC/BzG,KAAKoC,gBAAgBiD,KAAO,U,EAIhC,uBAAMgC,GACJ,IAAK,MAAMjE,KAAUpD,KAAKkD,aAAc,CACtCE,EAAOkE,iBAAiB,iBAAkBtH,KAAKsC,Q,EAInD,uBAAYiF,GACV,OAAOvH,KAAK2F,QACR6B,MAAMC,KACJzH,KAAKiC,GAAGyF,WAAWC,iBAAiB,wEAEtCH,MAAMC,KAAKzH,KAAKiC,GAAG0F,iBAAiB,uE,CAG1C,gBAAYzE,GACV,OAAOlD,KAAK2F,QACR6B,MAAMC,KAAKzH,KAAKiC,GAAGyF,WAAWC,iBAAiB,uDAC/CH,MAAMC,KAAKzH,KAAKiC,GAAG0F,iBAAiB,sD,CAOlC,mBAAMzC,CAAc0C,GAC1B,IAAKA,EAAM,OACH5H,KAAK+B,qBACX,M,CAGF,IAAK,MAAMqB,KAAUpD,KAAKkD,aAAc,CACtC,MAAM2E,EAAe7H,KAAK8H,WAAW1E,EAAOQ,kBAAmB5D,KAAKuG,YACpE,MAAMwB,EAAkB3E,EAAOQ,YAAYoE,cAC3C,MAAMC,EAAYL,EAAKI,cAEvB,GAAIH,EAAc,CAChBzE,EAAO8E,aAAa,YAAa,Y,CAGnC,GAAIN,GAAQG,EAAgBI,SAASF,KAAeJ,EAAc,CAChEzE,EAAOgF,gBAAgB,Y,CAGzB,GAAIR,IAASG,EAAgBI,SAASF,KAAeJ,EAAc,CACjEzE,EAAO8E,aAAa,YAAa,Y,GAK/B,wBAAMnG,GACZ,IAAK,MAAMqB,KAAUpD,KAAKkD,aAAc,CACtC,GAAIlD,KAAK8H,WAAW1E,EAAOQ,kBAAmB5D,KAAKuG,YAAa,CAC9DnD,EAAO8E,aAAa,YAAa,Y,KAC5B,CACL9E,EAAOgF,gBAAgB,Y,GAarB,UAAAN,CAAWO,EAAoB7H,GACrC,OAAOA,EAAM8H,MAAMC,GAASF,IAAeE,G,CA2BrC,kBAAAC,GACN,SAAUxI,KAAKuH,oBAAoBvC,SAAW,GAAKhF,KAAK+D,aAAe/D,KAAK+D,YAAYrD,M,CAGlF,aAAMkC,CAAQ2F,SACdvI,KAAK+E,QAAQwD,GACnBvI,KAAK+D,YAAYrD,MAAQ,E,CA4BnB,aAAA4F,GACN,GAAItG,KAAKoB,OAAS,QAAS,CACzB,OAAQpB,KAAKO,cAAc+H,MAAMC,IAAUvI,KAAKyI,aAAaF,I,KACxD,CACL,OAAO,I,EAgBH,YAAAG,GACN1I,KAAK2I,QAAQ7F,OACb9C,KAAKK,UAAY,K,CAgDX,6BAAAuI,CAA8BlI,GACpC,GAAIA,EAAMsE,SAAW,GAAKtE,EAAM,GAAGmI,MAAM7I,KAAKqB,YAAa,CACzD,MAAO,E,CAGT,IAAIyH,EAAWpI,EAAMqI,QAAQ,KAAM,KAAKA,QAAQ,UAAW,KAE3D,GAAID,EAAS,GAAGD,MAAM7I,KAAKqB,YAAa,CACtCyH,EAAWA,EAASE,UAAU,E,CAGhC,OAAOF,C,CAGD,gBAAAhE,GACN,MAAMpE,EAAQV,KAAK+D,YAAYrD,MAC/BV,KAAKU,MAAQA,EAAQA,EAAMmD,OAAS,GAEpC,GAAInD,EAAMsE,SAAW,EAAG,OAExB,MAAMiE,EAAYvI,EAAMmI,MAAM7I,KAAKqB,YACnC,IAAK4H,EAAW,OAEhB,MAAMH,EAAW9I,KAAK4I,8BAA8BlI,GACpD,IAAKoI,EAAU,CACb9I,KAAKkJ,mBACL,M,CAGF,MAAMC,EAAQL,EAASM,MAAMpJ,KAAKqB,YAClC8H,EAAME,SAASC,IACbtJ,KAAK+E,QAAQuE,EAAKC,YAAY,IAGhCvJ,KAAKkJ,kB,CAGC,kBAAMM,CAAajH,GAEzB,MACEC,QAAQ9B,MAAEA,IACR6B,EAEJvC,KAAKU,MAAQA,EAAQA,EAAMmD,OAAS,GAEpC,GAAInD,EAAMsE,SAAW,EAAG,OAExB,MAAMiE,EAAYvI,EAAMmI,MAAM7I,KAAKqB,YACnC,IAAK4H,EAAW,OAEhB,MAAMH,EAAW9I,KAAK4I,8BAA8BlI,GACpD,IAAKoI,EAAU,CACb9I,KAAKkJ,mBACL,M,CAGF,MAAMC,EAAQL,EAASM,MAAMpJ,KAAKqB,YAClC8H,EAAME,SAASC,IACbtJ,KAAK+E,QAAQuE,EAAK,IAGpBtJ,KAAKkJ,kB,CAiBC,gBAAAA,CAAiBxI,EAAQ,IAC/BV,KAAK+D,YAAYrD,MAAQA,EACzBV,KAAKU,MAAQA,C,CAGP,OAAAqE,CAAQM,GACd,IAAKrF,KAAKiB,WAAY,CACpB,MAAMwI,EAASzJ,KAAKO,cAAc+H,MAAMC,GAASA,EAAKP,gBAAkB3C,EAAK2C,gBAC7E,GAAIyB,EAAQ,M,CAGd,IAAKC,EAAqBrE,GAAO,CAC/B,M,CAGFrF,KAAKO,cAAgB,IAAIP,KAAKO,cAAe8E,E,CAGvC,YAAAoD,CAAapD,GACnB,MAAMsE,EAActE,EAAKxB,OACzB,GAAI7D,KAAKoB,OAAS,SAAWwI,EAAgBD,GAAc,CACzD,OAAO,K,CAET,OAAO,I,CAGD,cAAA1E,GACNjF,KAAKO,cAAgBP,KAAKO,cAAcsJ,MAAM,EAAG7J,KAAKO,cAAcyE,OAAS,E,CAGvE,UAAA8E,CAAWvH,GACjB,MACEC,QAAQuH,GAAEA,IACRxH,EAEJvC,KAAKO,cAAgBP,KAAKO,cAAcyJ,QAAO,CAACC,EAAOC,IAAUA,EAAMC,aAAeJ,IACtF/J,KAAK6C,eAAeC,KAAK,CAAEC,KAAM/C,KAAKO,cAAeG,MAAOV,KAAKyC,iBACjEzC,KAAKgD,UAAUF,KAAK,CAAEC,KAAM/C,KAAKG,iB,CAG3B,WAAAiK,GACN,IAAKpK,KAAKO,cAAcyE,OAAQ,CAC9B,MAAO,E,CAGT,OAAOhF,KAAKO,cAAc0F,KAAI,CAACsC,EAAM2B,KACnC,MAAMH,EAAKG,EAAMC,WACjB,MAAME,EAAQ,GACd,GAAI9B,EAAKvD,QAAUqF,EAAO,CACxB,OACEC,EACE,sBAAAP,GAAIA,EACJlF,IAAKkF,EACLQ,MAAM,UACNC,OAAQxK,KAAKc,SACb2J,qBAAuBlI,GAAUvC,KAAK8J,WAAWvH,IAEhDgG,E,KAGA,CACL,OACE+B,EAAa,eAAAzF,IAAKkF,EAAIW,SAAS,aAAY,eAAenC,GACxD+B,EACE,sBAAAP,GAAIA,EACJlF,IAAKkF,EACLQ,MAAM,UACNC,OAAQxK,KAAKc,SACb2J,qBAAuBlI,GAAUvC,KAAK8J,WAAWvH,IAEhD,GAAGgG,EAAKsB,MAAM,EAAGQ,U,KAQtB,UAAAM,GACN,OACE3K,KAAKgB,MACHsJ,EAAA,OACEM,MAAO,CACLC,YAAa,KACb,uBAAwB7K,KAAKe,QAG/BuJ,EAAU,YAAAQ,KAAM9K,KAAKe,MAAQ,SAAW,QAASsE,KAAMrF,KAAKgB,KAAMuJ,MAAM,Y,CAMxE,WAAAQ,GACN,OACE/K,KAAKe,OACHuJ,EAAA,SACEM,MAAO,CACLI,wBAAyB,KACzB,mCAAoChL,KAAKK,YAAcL,KAAKc,WAG9DwJ,EAAA,YAAUW,QAAQ,QAAQC,KAAK,QAC5BlL,KAAKe,O,CAOR,aAAAoK,GACN,MAAMnK,EAAOhB,KAAKW,OAAS,QAAUX,KAAKY,QAAU,YAAc,OAClE,IAAIwK,EAAUpL,KAAKW,OAASX,KAAKa,aAAeb,KAAKY,QAAUZ,KAAKwB,eAAiBxB,KAAKuB,cAE1F,IAAK6J,GAAWpL,KAAKI,iBAAkBgL,EAAUpL,KAAKM,iBAEtD,MAAM+K,EACJrL,KAAKW,QAAUX,KAAKI,iBAChB,wCACAJ,KAAKY,QACH,yCACA,iBAER,GAAIwK,EAAS,CACX,OACEd,EAAA,OAAKM,MAAOS,EAAQC,KAAK,kBACvBhB,EAAK,OAAAM,MAAM,wBACTN,EAAA,YAAUQ,KAAK,UAAUzF,KAAMrE,EAAMuK,MAAM,UAAUhB,MAAM,aAE7DD,EAAA,YAAUM,MAAM,uBAAuBK,QAAQ,SAC5CG,G,CAMT,OAAOI,S,CAGD,WAAAC,CAAY/K,GAClB,OAAOA,EAAMsH,cAAce,QAAQ,KAAM,I,CAG3C,MAAA2C,GACE,MAAMrL,EAAYL,KAAKK,YAAcL,KAAKc,SAE1C,IAAIyC,EAA4B,GAChC,GAAIvD,KAAK2F,QAAS,CAChB,UAAW3F,KAAK2F,UAAY,SAAU,CACpC,IACEpC,EAAkBqC,KAAKC,MAAM7F,KAAK2F,Q,CAClC,MAAOG,GAAG,C,KACP,CACLvC,EAAkBvD,KAAK2F,O,EAI3B,OACE2E,EAAA,OAAAzF,IAAA,2CAAK+F,MAAM,SAASe,SAAS,IAAIvH,QAASpE,KAAK8D,gBAAiB8H,OAAQ5L,KAAKiE,oBAC3EqG,EAAK,OAAAzF,IAAA,2CAAA+F,MAAO,CAAEiB,cAAe,MAAM,gBAAiB7L,KAAKc,SAAW,OAAS,KAAMgL,QAAS9L,KAAKqC,QAC/FiI,EAAA,OAAAzF,IAAA,2CACE+F,MAAO,CACLpG,MAAO,KACP,wBAAyBxE,KAAKW,SAAWX,KAAKI,iBAC9C,sBAAuBJ,KAAKW,QAAUX,KAAKI,iBAC3C,uBAAwBJ,KAAKY,QAC7B,wBAAyBZ,KAAKc,SAC9B,iBAAkBd,KAAKe,MACvB,iBAAkBV,GAEpByL,QAAS9L,KAAKmE,gBAEbnE,KAAK2K,aACNL,EAAK,OAAAzF,IAAA,2CAAA+F,MAAM,oBACR5K,KAAK+K,cACNT,EAAA,OAAAzF,IAAA,2CAAK+F,MAAO,CAAEmB,0BAA2B,OACtC/L,KAAKO,cAAcyE,OAAS,GAC3BsF,EAAA,QAAAzF,IAAA,2CAAMmH,MAAO,CAAEC,OAAQjM,KAAKiM,OAAQC,UAAWlM,KAAKkM,WAAatB,MAAM,qBACpE5K,KAAKoK,eAGVE,EACE,SAAAzF,IAAA,2CAAAsH,IAAM3H,GAAWxE,KAAK+D,YAAcS,EACpCoG,MAAO,CAAEwB,uBAAwB,MACjC/G,KAAMrF,KAAKyB,UACX4K,UAAWrM,KAAKqM,UAChB3K,YAAa1B,KAAK0B,YAClB4C,QAAStE,KAAKsE,QACdF,QAASpE,KAAKoE,QACdwH,OAAQ,IAAM5L,KAAK0I,eACnB4D,SAAU,IAAMtM,KAAKwJ,aACrB9I,MAAOV,KAAKU,MACZI,SAAUd,KAAKc,SAAQ,YACZd,KAAK4B,SAChB2K,UAAWvM,KAAK4E,oBAItB0F,EAAK,OAAAzF,IAAA,2CAAA+F,MAAM,gBACTN,EAAU,YAAAzF,IAAA,2CAAAsH,IAAMlK,GAAOjC,KAAKmC,YAAYF,GAAK6I,KAAK,QAAQP,MAAM,aAEjEvK,KAAKY,SAAW0J,EAAA,YAAAzF,IAAA,2CAAU+F,MAAM,eAAevF,KAAK,QAAQkG,MAAM,UAAUT,KAAK,eAEnF9K,KAAKmL,iBAERb,EAAA,OAAAzF,IAAA,2CACEsH,IAAMlK,GAAOjC,KAAKgC,YAAYC,GAC9B2I,MAAO,CACL4B,gBAAiB,KACjB,wBAAyBxM,KAAKC,SAG/BsD,EAAgB0C,KAAK7C,GACpBkH,EAAA,qBACEzF,IAAK7E,KAAKyL,YAAYrI,EAAO1C,OAC7B+L,iBAAkBzM,KAAKsC,QACvB5B,MAAO0C,EAAO1C,MACdgM,OAAQtJ,EAAOsJ,QAEdtJ,EAAOrC,SAGZuJ,EAAQ,QAAAzF,IAAA,6CACP7E,KAAKkB,YAAc,MAAQlB,KAAKwI,sBAC/B8B,EAAA,qBAAAzF,IAAA,2CACEkF,GAAG,aACHrJ,MAAM,MACNoL,QAAS,IAAM9L,KAAKiD,iBAAiBjD,KAAK+D,YAAYrD,QAErDV,KAAKS,UACLT,KAAK+D,YAAYrD,QAGpBV,KAAKkB,WAAalB,KAAKwI,sBACvB8B,EAAA,qBAAAzF,IAAA,2CAAmBkF,GAAG,YAAYrJ,MAAM,OACrCV,KAAKmB,kB", "ignoreList": []}