{"version": 3, "names": ["tableHeaderCellCss", "TableHeaderCell", "constructor", "hostRef", "this", "isDense", "sortable", "arrow", "justifyContent", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "denseTable", "render", "h", "Host", "key", "class", "th_cell", "bold", "variant", "size", "name"], "sources": ["src/components/table/table-header-cell/table-header-cell.scss?tag=bds-table-th&encapsulation=scoped", "src/components/table/table-header-cell/table-header-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0px 8px;\n}\n.th_cell {\n  display: flex;\n  align-items: center;\n  height: 64px;\n  gap: 8px;\n  font-family: $font-family;\n  box-sizing: border-box;\n\n  &--sortable-true:hover, &--sortable-false:hover  {\n    cursor: pointer;\n  }\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n.dense-th {\n  min-height: 48px;\n  height: auto;\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n@Component({\n  tag: 'bds-table-th',\n  styleUrl: 'table-header-cell.scss',\n  scoped: true,\n})\nexport class TableHeaderCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() sortable = false;\n  @Prop() arrow = '';\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div\n          class={{\n            th_cell: true,\n            [`th_cell--sortable-${this.sortable}`]: true,\n            'dense-th': this.isDense,\n            [`justify--${this.justifyContent}`]:true\n          }}\n        >\n          <bds-typo bold={this.sortable ? 'bold' : 'semi-bold'} variant=\"fs-14\">\n            <slot />\n          </bds-typo>\n          {this.sortable ? (\n            <bds-icon\n              size=\"small\"\n              name={this.arrow === 'asc' ? 'arrow-down' : this.arrow === 'dsc' ? 'arrow-up' : ''}\n            ></bds-icon>\n          ) : ''\n            // <div style={{ width: '20px' }}></div>\n          }\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "oDAAA,MAAMA,EAAqB,+xB,MCQdC,EAAe,MAL5B,WAAAC,CAAAC,G,UAOWC,KAAOC,QAAG,MACXD,KAAQE,SAAG,MACXF,KAAKG,MAAG,GACRH,KAAcI,eAAmB,MAkC1C,CAhCC,iBAAAC,GACE,MAAMC,EAAWN,KAAKO,QAAQC,QAAQ,aACtC,GAAIF,IAAaA,EAASG,aAAa,iBAAmB,QAAUH,EAASI,aAAe,MAAO,CACjGV,KAAKC,QAAU,I,EAGnB,MAAAU,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,MAAO,CACLC,QAAS,KACT,CAAC,qBAAqBhB,KAAKE,YAAa,KACxC,WAAYF,KAAKC,QACjB,CAAC,YAAYD,KAAKI,kBAAkB,OAGtCQ,EAAA,YAAAE,IAAA,2CAAUG,KAAMjB,KAAKE,SAAW,OAAS,YAAagB,QAAQ,SAC5DN,EAAA,QAAAE,IAAA,8CAEDd,KAAKE,SACJU,EAAA,YACEO,KAAK,QACLC,KAAMpB,KAAKG,QAAU,MAAQ,aAAeH,KAAKG,QAAU,MAAQ,WAAa,KAEhF,I", "ignoreList": []}