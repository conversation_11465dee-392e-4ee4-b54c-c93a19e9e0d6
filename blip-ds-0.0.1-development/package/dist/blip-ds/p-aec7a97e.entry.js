import{r as t,h as s,H as e,a as i}from"./p-C3J6Z5OX.js";const a=":host{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host .img-feedback{height:76%}:host(.empty_img){background-color:var(--color-surface-3, rgb(227, 227, 227))}";const h=class{constructor(s){t(this,s);this.imageHasLoading=false;this.objectFit="cover";this.dataTest=null;this.imageLoaded=false;this.loadError=false}componentDidLoad(){var t;this.element.style.width=this.width?this.width:"auto";this.element.style.height=((t=this.height)===null||t===void 0?void 0:t.length)>0?this.height:"auto"}async loadImage(){if(this.src){this.imageHasLoading=true;try{const t=await fetch(this.src);if(t.ok){const s=await t.blob();const e=URL.createObjectURL(s);this.currentSrc=e;this.imageLoaded=true;this.imageHasLoading=false}else{this.loadError=true}}catch(t){this.imageHasLoading=false;this.loadError=true}}}render(){if(!this.imageLoaded&&!this.loadError){this.loadImage()}return s(e,{key:"b7798d3abcb3fe0da3938b890b1abc6479aeccb0",class:{empty_img:!this.imageLoaded}},this.imageLoaded?s("img",{src:this.currentSrc,alt:this.alt,style:{objectFit:this.objectFit,width:"100%",height:"100%",filter:`brightness(${this.brightness})`},"data-test":this.dataTest,draggable:false}):this.imageHasLoading?s("bds-skeleton",{shape:"square",width:"100%",height:"100%"}):s("bds-illustration",{class:"img-feedback",type:"empty-states",name:this.loadError?"broken-image":"image-not-found",alt:this.alt,"data-test":this.dataTest}))}get element(){return i(this)}};h.style=a;export{h as bds_image};
//# sourceMappingURL=p-aec7a97e.entry.js.map