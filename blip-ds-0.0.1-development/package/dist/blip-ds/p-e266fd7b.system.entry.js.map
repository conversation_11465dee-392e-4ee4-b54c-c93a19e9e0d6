{"version": 3, "names": ["selectCss", "SelectChips", "exports", "class_1", "hostRef", "_this", "this", "isOpen", "intoView", "selectedOptions", "validationDanger", "isPressed", "validationMesage", "internalChips", "chips", "newPrefix", "value", "danger", "success", "errorMessage", "disabled", "label", "icon", "duplicated", "canAddNew", "notFoundMessage", "type", "delimiters", "disableSubmit", "helperMessage", "successMessage", "inputName", "placeholder", "optionsPosition", "dataTest", "handleChangeChipsValue", "__awaiter", "resetFilterOptions", "_c", "sent", "refDropdown", "el", "dropElement", "refIconDrop", "iconDropElement", "toggle", "handler", "event", "detail", "selectedOption", "text", "getText", "addChip", "bdsChangeChips", "emit", "data", "bdsChange", "handlerNewOption", "childOptions", "find", "option", "getTextFromOption", "opt", "internalOptions", "internalOption", "titleText", "_b", "_a", "textContent", "trim", "setFocusWrapper", "nativeInput", "focus", "removeFocusWrapper", "blur", "onClickWrapper", "onFocus", "bdsFocus", "onInput", "ev", "input", "target", "bdsSelectChipsInput", "changedInputValue", "keyPressWrapper", "key", "handleDelimiters", "setChip", "length", "removeLastChip", "filterOptions", "prototype", "isOpenChanged", "positionHeightDrop", "name", "setDefaultPlacement", "validatePositionDrop", "handleWindow", "contains", "optionsChanged", "options", "JSON", "parse", "e", "valueChanged", "internalValueChanged", "map", "item", "concat", "validValueChip", "selectOption", "<PERSON><PERSON><PERSON><PERSON>", "validateChips", "getChips", "clear", "add", "setFocus", "removeFocus", "componentWillLoad", "getScrollParent", "componentDidLoad", "classList", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "connectedCallback", "_i", "addEventListener", "Object", "defineProperty", "Array", "from", "shadowRoot", "querySelectorAll", "term", "_f", "_d", "existsChip", "isExistsChip", "apply", "_e", "optionTextLower", "toLowerCase", "termLower", "setAttribute", "includes", "removeAttribute", "optionChip", "some", "chip", "enableCreateOption", "childOptionsEnabled", "validateChip", "handleOnBlur", "bdsBlur", "verifyAndSubstituteDelimiters", "match", "newValue", "replace", "substring", "existTerm", "clearInputValues", "words", "split", "for<PERSON>ach", "word", "trimStart", "handleChange", "exists", "whitespaceValidation", "__spread<PERSON><PERSON>y", "trimmedName", "emailValidation", "slice", "removeChip", "id", "filter", "_chip", "index", "toString", "renderChips", "limit", "h", "color", "close", "onChipClickableClose", "position", "renderIcon", "class", "input__icon", "size", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "undefined", "<PERSON><PERSON>ey", "render", "tabindex", "onBlur", "element_input", "onClick", "input__container__wrapper", "style", "height", "maxHeight", "ref", "input__container__text", "maxlength", "onChange", "onKeyDown", "select__options", "onOptionSelected", "status"], "sources": ["src/components/selects/select.scss?tag=bds-select-chips&encapsulation=shadow", "src/components/selects/select-chips/select-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, Element, h, Prop, Method, Event, EventEmitter, Listen, Watch, State } from '@stencil/core';\nimport { Option, SelectChangeEvent, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\nimport { emailValidation, whitespaceValidation } from '../../../utils/validations';\nimport { InputChipsTypes } from '../../input-chips/input-chips-interface';\n\n@Component({\n  tag: 'bds-select-chips',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class SelectChips {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @State() internalOptions: Option[];\n\n  @Element() el!: HTMLElement;\n\n  @State() isOpen? = false;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() selectedOptions: { label: string; value: any }[] = [];\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  @State() selectedOption: number;\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true }) options?: string | Option[];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * Used for add prefix on new option select.\n   */\n  @Prop({ reflect: true }) newPrefix?: string = '';\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Set maximum length value for the chip content\n   */\n\n  @Prop() maxlength?: number;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() canAddNew?: boolean = true;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() notFoundMessage?: string = 'No results found';\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSelectChipsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('options')\n  protected optionsChanged(): void {\n    if (typeof this.options === 'string') {\n      try {\n        this.internalOptions = JSON.parse(this.options);\n      } catch (e) {}\n    } else {\n      this.internalOptions = this.options;\n    }\n  }\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.handleChangeChipsValue();\n\n    if (this.internalChips.length > 0) {\n      this.selectedOptions = this.internalChips.map((item) => {\n        return {\n          label: item,\n          value: `${this.validValueChip(item, this.childOptions)}`,\n        };\n      });\n    }\n  }\n\n  private validValueChip(value, internalOptions: HTMLBdsSelectOptionElement[]): string {\n    const selectOption = internalOptions?.find((option) => option.textContent == value);\n    return `${selectOption ? selectOption.value : value}`;\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async getChips(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n    this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  async componentDidLoad() {\n    await this.resetFilterOptions();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  async connectedCallback() {\n    for (const option of this.childOptions) {\n      option.addEventListener('optionSelected', this.handler);\n    }\n  }\n\n  private get childOptionsEnabled(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(\n          this.el.shadowRoot.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'),\n        )\n      : Array.from(this.el.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'));\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'))\n      : Array.from(this.el.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'));\n  }\n\n  private handleChangeChipsValue = async () => {\n    await this.resetFilterOptions();\n  };\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n      return;\n    }\n\n    for (const option of this.childOptions) {\n      const isExistsChip = this.existsChip(option.textContent, await this.getChips());\n      const optionTextLower = option.textContent.toLowerCase();\n      const termLower = term.toLowerCase();\n\n      if (isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n\n      if (term && optionTextLower.includes(termLower) && !isExistsChip) {\n        option.removeAttribute('invisible');\n      }\n\n      if (term && !optionTextLower.includes(termLower) && !isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n    }\n  }\n\n  private async resetFilterOptions() {\n    for (const option of this.childOptions) {\n      if (this.existsChip(option.textContent, await this.getChips())) {\n        option.setAttribute('invisible', 'invisible');\n      } else {\n        option.removeAttribute('invisible');\n      }\n    }\n  }\n\n  private refDropdown = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private existsChip(optionChip: string, chips: string[]) {\n    return chips.some((chip) => optionChip === chip);\n  }\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handler = async (event: CustomEvent) => {\n    const {\n      detail: { value },\n    } = event;\n    this.selectedOption = value;\n    const text = this.getText(value);\n    await this.addChip(text);\n\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n    this.toggle();\n  };\n\n  private handlerNewOption = async (text: string) => {\n    await this.addChip(text);\n    this.toggle();\n  };\n\n  private enableCreateOption(): boolean {\n    return !!(this.childOptionsEnabled.length === 0 && this.nativeInput && this.nativeInput.value);\n  }\n\n  private async addChip(chip: string) {\n    await this.setChip(chip);\n    this.nativeInput.value = '';\n  }\n\n  private getText = (value: string) => {\n    const el: HTMLBdsSelectOptionElement = this.childOptions.find((option) => option.value === value);\n    return this.getTextFromOption(el);\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.textContent?.trim() ?? '');\n  };\n\n  private setFocusWrapper = (): void => {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private removeFocusWrapper = (): void => {\n    this.nativeInput.blur();\n  };\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsSelectChipsInput.emit(ev);\n    this.changedInputValue();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        if (this.canAddNew !== false) {\n          this.handleDelimiters();\n          this.setChip(this.value);\n          this.value = '';\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowUp':\n        if (!this.disabled) {\n          this.isOpen = false;\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.handleChangeChipsValue;\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        break;\n    }\n  };\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    this.changedInputValue;\n    const {\n      detail: { value },\n    } = event;\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private changedInputValue = async () => {\n    this.value = this.nativeInput.value;\n\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      await this.resetFilterOptions();\n    }\n\n    if (this.value && this.isOpen === false) {\n      this.isOpen = true;\n    }\n  };\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n            >\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  private generateKey(value: string) {\n    return value.toLowerCase().replace(/ /g, '-');\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n\n    let internalOptions: Option[] = [];\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        try {\n          internalOptions = JSON.parse(this.options);\n        } catch (e) {}\n      } else {\n        internalOptions = this.options;\n      }\n    }\n\n    return (\n      <div class=\"select\" tabindex=\"0\" onFocus={this.setFocusWrapper} onBlur={this.removeFocusWrapper}>\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null} onClick={this.toggle}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                {this.internalChips.length > 0 && (\n                  <span style={{ height: this.height, maxHeight: this.maxHeight }} class=\"inside-input-left\">\n                    {this.renderChips()}\n                  </span>\n                )}\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class={{ input__container__text: true }}\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n        >\n          {internalOptions.map((option) => (\n            <bds-select-option\n              key={this.generateKey(option.value)}\n              onOptionSelected={this.handler}\n              value={option.value}\n              status={option.status}\n            >\n              {option.label}\n            </bds-select-option>\n          ))}\n          <slot />\n          {this.canAddNew === true && this.enableCreateOption() && (\n            <bds-select-option\n              id=\"option-add\"\n              value=\"add\"\n              onClick={() => this.handlerNewOption(this.nativeInput.value)}\n            >\n              {this.newPrefix}\n              {this.nativeInput.value}\n            </bds-select-option>\n          )}\n          {!this.canAddNew && this.enableCreateOption() && (\n            <bds-select-option id=\"no-option\" value=\"add\">\n              {this.notFoundMessage}\n            </bds-select-option>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "s8DAAA,IAAMA,EAAY,qmU,ICWLC,EAAWC,EAAA,8BALxB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,uRAeWA,KAAMC,OAAI,MAEVD,KAAQE,SAAiB,KAEzBF,KAAeG,gBAAoC,GAInDH,KAAgBI,iBAAa,MAI7BJ,KAASK,UAAI,MAKbL,KAAgBM,iBAAI,GAEpBN,KAAaO,cAAa,GAiBVP,KAAKQ,MAAsB,GAK3BR,KAASS,UAAY,GAKrBT,KAAKU,MAAmB,GAKTV,KAAMW,OAAI,MAIVX,KAAOY,QAAa,MAUnCZ,KAAYa,aAAI,GAKhBb,KAAQc,SAAI,MAK7Bd,KAAKe,MAAI,GAKQf,KAAIgB,KAAY,GAKjChB,KAAUiB,WAAa,MAKvBjB,KAASkB,UAAa,KAKtBlB,KAAemB,gBAAY,mBAM3BnB,KAAIoB,KAAoB,OAKxBpB,KAAUqB,WAAI,MAKdrB,KAAasB,cAAG,MAIhBtB,KAAauB,cAAY,GAIRvB,KAAcwB,eAAY,GAI3CxB,KAASyB,UAAY,GAKrBzB,KAAW0B,YAAY,GAKS1B,KAAe2B,gBAA+B,OAY9E3B,KAAQ4B,SAAY,KAwNpB5B,KAAsB6B,uBAAG,kBAAAC,UAAA/B,OAAA,qB,4DAC/B,SAAMC,KAAK+B,sB,OAAXC,EAAAC,O,kBAsCMjC,KAAAkC,YAAc,SAACC,GACrBpC,EAAKqC,YAAcD,CACrB,EAEQnC,KAAAqC,YAAc,SAACF,GACrBpC,EAAKuC,gBAAkBH,CACzB,EAMQnC,KAAMuC,OAAG,WACf,IAAKxC,EAAKe,SAAU,CAClBf,EAAKE,QAAUF,EAAKE,M,CAExB,EAEQD,KAAAwC,QAAU,SAAOC,GAAkB,OAAAX,UAAA/B,OAAA,qB,oEAE7BW,EACR+B,EAAKC,OAAAhC,MACTV,KAAK2C,eAAiBjC,EAChBkC,EAAO5C,KAAK6C,QAAQnC,GAC1B,SAAMV,KAAK8C,QAAQF,I,OAAnBZ,EAAAC,OAEAjC,KAAK+C,eAAeC,KAAK,CAAEC,KAAMjD,KAAKO,cAAeG,MAAOV,KAAK2C,iBACjE3C,KAAKkD,UAAUF,KAAK,CAAEC,KAAMjD,KAAKG,kBACjCH,KAAKuC,S,kBAGCvC,KAAAmD,iBAAmB,SAAOP,GAAY,OAAAd,UAAA/B,OAAA,qB,4DAC5C,SAAMC,KAAK8C,QAAQF,I,OAAnBZ,EAAAC,OACAjC,KAAKuC,S,kBAYCvC,KAAA6C,QAAU,SAACnC,GACjB,IAAMyB,EAAiCpC,EAAKqD,aAAaC,MAAK,SAACC,GAAW,OAAAA,EAAO5C,QAAUA,CAAjB,IAC1E,OAAOX,EAAKwD,kBAAkBpB,EAChC,EAEQnC,KAAAuD,kBAAoB,SAACC,G,QAC3B,GAAIzD,EAAK0D,gBAAiB,CACxB,IAAMC,EAAiB3D,EAAK0D,gBAAgBJ,MAAK,SAACC,GAAW,OAAAA,EAAO5C,QAAS8C,IAAA,MAAAA,SAAA,SAAAA,EAAK9C,MAArB,IAC7D,GAAIgD,EAAgB,CAClB,OAAOA,EAAe3C,K,EAG1B,OAAOyC,IAAA,MAAAA,SAAG,SAAHA,EAAKG,WAAYH,EAAIG,WAAaC,GAAAC,EAAAL,IAAG,MAAHA,SAAA,SAAAA,EAAKM,eAAW,MAAAD,SAAA,SAAAA,EAAEE,UAAU,MAAAH,SAAA,EAAAA,EAAA,EACvE,EAEQ5D,KAAegE,gBAAG,WACxB,GAAIjE,EAAKkE,YAAa,CACpBlE,EAAKkE,YAAYC,O,CAErB,EAEQlE,KAAkBmE,mBAAG,WAC3BpE,EAAKkE,YAAYG,MACnB,EAUQpE,KAAcqE,eAAG,WACvBtE,EAAKuE,UACL,GAAIvE,EAAKkE,YAAa,CACpBlE,EAAKkE,YAAYC,O,CAErB,EAEQlE,KAAOsE,QAAG,WAChBvE,EAAKwE,SAASvB,OACdjD,EAAKM,UAAY,IACnB,EAOQL,KAAAwE,QAAU,SAACC,GACjB,IAAMC,EAAQD,EAAGE,OACjB,GAAID,EAAO,CACT3E,EAAKW,MAAQgE,EAAMhE,OAAS,E,CAE9BX,EAAK6E,oBAAoB5B,KAAKyB,GAC9B1E,EAAK8E,mBACP,EAEQ7E,KAAA8E,gBAAkB,SAACrC,GACzB,OAAQA,EAAMsC,KACZ,IAAK,QACH,GAAIhF,EAAKmB,YAAc,MAAO,CAC5BnB,EAAKiF,mBACLjF,EAAKkF,QAAQlF,EAAKW,OAClBX,EAAKW,MAAQ,GACbX,EAAKgD,eAAeC,KAAK,CAAEC,KAAMlD,EAAKQ,cAAeG,MAAOX,EAAK4C,iBACjE5C,EAAKmD,UAAUF,KAAK,CAAEC,KAAMlD,EAAKI,iB,CAEnC,IAAKJ,EAAKe,SAAU,CAClBf,EAAKE,OAAS,I,CAEhB,MACF,IAAK,YACH,IAAKF,EAAKe,SAAU,CAClBf,EAAKE,OAAS,I,CAEhB,MACF,IAAK,UACH,IAAKF,EAAKe,SAAU,CAClBf,EAAKE,OAAS,K,CAEhB,MACF,IAAK,YACL,IAAK,SACH,IAAKF,EAAKW,QAAU,MAAQX,EAAKW,MAAMwE,QAAU,IAAMnF,EAAKQ,cAAc2E,OAAQ,CAChFnF,EAAKoF,iBAELpF,EAAKgD,eAAeC,KAAK,CAAEC,KAAMlD,EAAKQ,cAAeG,MAAOX,EAAK4C,iBACjE5C,EAAKmD,UAAUF,KAAK,CAAEC,KAAMlD,EAAKI,iB,CAEnC,MAEN,EAkEQH,KAAiB6E,kBAAG,kBAAA/C,UAAA/B,OAAA,qB,4DAC1BC,KAAKU,MAAQV,KAAKiE,YAAYvD,M,IAE1BV,KAAKiE,YAAYvD,MAAjB,YACF,SAAMV,KAAKoF,cAAcpF,KAAKiE,YAAYvD,Q,OAA1CsB,EAAAC,O,mBAEA,SAAMjC,KAAK+B,sB,OAAXC,EAAAC,O,iBAGF,GAAIjC,KAAKU,OAASV,KAAKC,SAAW,MAAO,CACvCD,KAAKC,OAAS,I,kBAsPnB,CArqBWJ,EAAAwF,UAAAC,cAAA,SAAcrF,GACtB,GAAID,KAAKuF,oBAAsB,SAAU,CACvCvF,KAAKsC,gBAAgBkD,KAAOxF,KAAKC,OAAS,WAAa,Y,KAClD,CACLD,KAAKsC,gBAAgBkD,KAAOxF,KAAKC,OAAS,aAAe,U,CAE3D,GAAIA,EACF,GAAID,KAAK2B,iBAAmB,OAAQ,CAClC3B,KAAKyF,oBAAoBzF,KAAK2B,gB,KACzB,CACL3B,KAAK0F,sB,GAKX7F,EAAAwF,UAAAM,aAAA,SAAalB,GACX,IAAKzE,KAAKmC,GAAGyD,SAASnB,EAAGE,QAA6B,CACpD3E,KAAKC,OAAS,K,GAKRJ,EAAAwF,UAAAQ,eAAA,WACR,UAAW7F,KAAK8F,UAAY,SAAU,CACpC,IACE9F,KAAKyD,gBAAkBsC,KAAKC,MAAMhG,KAAK8F,Q,CACvC,MAAOG,GAAG,C,KACP,CACLjG,KAAKyD,gBAAkBzD,KAAK8F,O,GAQtBjG,EAAAwF,UAAAa,aAAA,WACR,GAAIlG,KAAKQ,MAAO,CACd,UAAWR,KAAKQ,QAAU,SAAU,CAClC,IACER,KAAKO,cAAgBwF,KAAKC,MAAMhG,KAAKQ,M,CACrC,MAAAqD,GACA7D,KAAKO,cAAgB,E,MAElB,CACLP,KAAKO,cAAgBP,KAAKQ,K,MAEvB,CACLR,KAAKO,cAAgB,E,GAKfV,EAAAwF,UAAAc,qBAAA,eAAApG,EAAAC,KACRA,KAAK6B,yBAEL,GAAI7B,KAAKO,cAAc2E,OAAS,EAAG,CACjClF,KAAKG,gBAAkBH,KAAKO,cAAc6F,KAAI,SAACC,GAC7C,MAAO,CACLtF,MAAOsF,EACP3F,MAAO,GAAA4F,OAAGvG,EAAKwG,eAAeF,EAAMtG,EAAKqD,eAE7C,G,GAIIvD,EAAAwF,UAAAkB,eAAA,SAAe7F,EAAO+C,GAC5B,IAAM+C,EAAe/C,IAAe,MAAfA,SAAA,SAAAA,EAAiBJ,MAAK,SAACC,GAAW,OAAAA,EAAOQ,aAAepD,CAAtB,IACvD,MAAO,GAAA4F,OAAGE,EAAeA,EAAa9F,MAAQA,E,EAO1Cb,EAAAwF,UAAAoB,QAAN,W,qFACE,SAAOzG,KAAK0G,gB,QAOR7G,EAAAwF,UAAAsB,SAAN,W,qFACE,SAAO3G,KAAKO,c,QAORV,EAAAwF,UAAAuB,MAAN,W,qFACE5G,KAAKO,cAAgB,GACrBP,KAAKU,MAAQ,G,iBAITb,EAAAwF,UAAAwB,IAAN,SAAUnG,G,qFACRV,KAAKgF,mBACL,GAAItE,EAAO,CACTV,KAAKiF,QAAQvE,E,KACR,CACLV,KAAKiF,QAAQjF,KAAKU,M,CAEpBV,KAAKU,MAAQ,G,iBAITb,EAAAwF,UAAAyB,SAAN,W,qFACE9G,KAAKiE,YAAYC,Q,iBAIbrE,EAAAwF,UAAA0B,YAAN,W,qFACE/G,KAAKiE,YAAYG,O,iBAGnBvE,EAAAwF,UAAA2B,kBAAA,WACEhH,KAAKkG,eACLlG,KAAK6F,iBACL7F,KAAKE,SAAW+G,EAAgBjH,KAAKmC,G,EAGjCtC,EAAAwF,UAAA6B,iBAAN,W,4GACE,SAAMlH,KAAK+B,sB,OAAXC,EAAAC,OACA,GAAIjC,KAAK2B,iBAAmB,OAAQ,CAClC3B,KAAKyF,oBAAoBzF,KAAK2B,gB,KACzB,CACL3B,KAAK0F,sB,mBAID7F,EAAAwF,UAAAI,oBAAA,SAAoB/E,GAC1B,GAAIA,GAAS,SAAU,CACrBV,KAAKoC,YAAY+E,UAAUN,IAAI,oCAC/B7G,KAAKsC,gBAAgBkD,KAAO,Y,KACvB,CACLxF,KAAKoC,YAAY+E,UAAUN,IAAI,iCAC/B7G,KAAKsC,gBAAgBkD,KAAO,U,GAIxB3F,EAAAwF,UAAAK,qBAAA,WACN,IAAM0B,EAAgBC,EAAwB,CAC5CC,cAAetH,KAAKmC,GACpBoF,eAAgBvH,KAAKoC,YACrBlC,SAAUF,KAAKE,WAEjBF,KAAKuF,mBAAqB6B,EAAcI,EACxC,GAAIJ,EAAcI,GAAK,SAAU,CAC/BxH,KAAKoC,YAAY+E,UAAUN,IAAI,oCAC/B7G,KAAKsC,gBAAgBkD,KAAO,Y,KACvB,CACLxF,KAAKoC,YAAY+E,UAAUN,IAAI,iCAC/B7G,KAAKsC,gBAAgBkD,KAAO,U,GAI1B3F,EAAAwF,UAAAoC,kBAAN,W,+FACE,IAAAC,EAAA,EAAqB1F,EAAAhC,KAAKoD,aAALsE,EAAA1F,EAAAkD,OAAAwC,IAAmB,CAA7BpE,EAAMtB,EAAA0F,GACfpE,EAAOqE,iBAAiB,iBAAkB3H,KAAKwC,Q,kBAInDoF,OAAAC,eAAYhI,EAAAwF,UAAA,sBAAmB,C,IAA/B,WACE,OAAOrF,KAAK8F,QACRgC,MAAMC,KACJ/H,KAAKmC,GAAG6F,WAAWC,iBAAiB,wEAEtCH,MAAMC,KAAK/H,KAAKmC,GAAG8F,iBAAiB,uE,uCAG1CL,OAAAC,eAAYhI,EAAAwF,UAAA,eAAY,C,IAAxB,WACE,OAAOrF,KAAK8F,QACRgC,MAAMC,KAAK/H,KAAKmC,GAAG6F,WAAWC,iBAAiB,uDAC/CH,MAAMC,KAAK/H,KAAKmC,GAAG8F,iBAAiB,sD,uCAO5BpI,EAAAwF,UAAAD,cAAN,SAAoB8C,G,qIACrBA,EAAD,YACF,SAAMlI,KAAK+B,sB,OAAXoG,EAAAlG,OACA,U,WAGmBD,EAAAhC,KAAKoD,a,sBAALsE,EAAA1F,EAAAkD,QAAiB,YAA3B5B,EAAMtB,EAAA0F,GACMU,EAAApI,KAAKqI,W,GAAW/E,EAAOQ,aAAa,SAAM9D,KAAK2G,Y,OAA9D2B,EAAeF,EAAAG,MAAAvI,KAAIwI,EAAAlC,OAAA,CAAgC6B,EAAAlG,UACnDwG,EAAkBnF,EAAOQ,YAAY4E,cACrCC,EAAYT,EAAKQ,cAEvB,GAAIJ,EAAc,CAChBhF,EAAOsF,aAAa,YAAa,Y,CAGnC,GAAIV,GAAQO,EAAgBI,SAASF,KAAeL,EAAc,CAChEhF,EAAOwF,gBAAgB,Y,CAGzB,GAAIZ,IAASO,EAAgBI,SAASF,KAAeL,EAAc,CACjEhF,EAAOsF,aAAa,YAAa,Y,kBAdhBlB,I,qCAmBT7H,EAAAwF,UAAAtD,mBAAN,W,8HACeC,EAAAhC,KAAKoD,a,sBAALsE,EAAA1F,EAAAkD,QAAiB,YAA3B5B,EAAMtB,EAAA0F,GACXU,EAAApI,KAAKqI,W,GAAW/E,EAAOQ,aAAa,SAAM9D,KAAK2G,Y,OAAnD,GAAIyB,EAAAG,MAAAvI,KAAIwI,EAAAlC,OAAA,CAAgC6B,EAAAlG,UAAwB,CAC9DqB,EAAOsF,aAAa,YAAa,Y,KAC5B,CACLtF,EAAOwF,gBAAgB,Y,kBAJNpB,I,qCAiBf7H,EAAAwF,UAAAgD,WAAA,SAAWU,EAAoBvI,GACrC,OAAOA,EAAMwI,MAAK,SAACC,GAAS,OAAAF,IAAeE,CAAf,G,EA2BtBpJ,EAAAwF,UAAA6D,mBAAA,WACN,SAAUlJ,KAAKmJ,oBAAoBjE,SAAW,GAAKlF,KAAKiE,aAAejE,KAAKiE,YAAYvD,M,EAG5Eb,EAAAwF,UAAAvC,QAAN,SAAcmG,G,4GACpB,SAAMjJ,KAAKiF,QAAQgE,I,OAAnBjH,EAAAC,OACAjC,KAAKiE,YAAYvD,MAAQ,G,kBA4BnBb,EAAAwF,UAAAqB,cAAA,eAAA3G,EAAAC,KACN,GAAIA,KAAKoB,OAAS,QAAS,CACzB,OAAQpB,KAAKO,cAAcyI,MAAK,SAACC,GAAS,OAAClJ,EAAKqJ,aAAaH,EAAnB,G,KACrC,CACL,OAAO,I,GAgBHpJ,EAAAwF,UAAAgE,aAAA,WACNrJ,KAAKsJ,QAAQtG,OACbhD,KAAKK,UAAY,K,EAgDXR,EAAAwF,UAAAkE,8BAAA,SAA8B7I,GACpC,GAAIA,EAAMwE,SAAW,GAAKxE,EAAM,GAAG8I,MAAMxJ,KAAKqB,YAAa,CACzD,MAAO,E,CAGT,IAAIoI,EAAW/I,EAAMgJ,QAAQ,KAAM,KAAKA,QAAQ,UAAW,KAE3D,GAAID,EAAS,GAAGD,MAAMxJ,KAAKqB,YAAa,CACtCoI,EAAWA,EAASE,UAAU,E,CAGhC,OAAOF,C,EAGD5J,EAAAwF,UAAAL,iBAAA,eAAAjF,EAAAC,KACN,IAAMU,EAAQV,KAAKiE,YAAYvD,MAC/BV,KAAKU,MAAQA,EAAQA,EAAMqD,OAAS,GAEpC,GAAIrD,EAAMwE,SAAW,EAAG,OAExB,IAAM0E,EAAYlJ,EAAM8I,MAAMxJ,KAAKqB,YACnC,IAAKuI,EAAW,OAEhB,IAAMH,EAAWzJ,KAAKuJ,8BAA8B7I,GACpD,IAAK+I,EAAU,CACbzJ,KAAK6J,mBACL,M,CAGF,IAAMC,EAAQL,EAASM,MAAM/J,KAAKqB,YAClCyI,EAAME,SAAQ,SAACC,GACblK,EAAKkF,QAAQgF,EAAKC,YACpB,IAEAlK,KAAK6J,kB,EAGOhK,EAAAwF,UAAA8E,aAAN,SAAmB1H,G,4GAGb/B,EACR+B,EAAKC,OAAAhC,MAETV,KAAKU,MAAQA,EAAQA,EAAMqD,OAAS,GAEpC,GAAIrD,EAAMwE,SAAW,EAAG,UAElB0E,EAAYlJ,EAAM8I,MAAMxJ,KAAKqB,YACnC,IAAKuI,EAAW,UAEVH,EAAWzJ,KAAKuJ,8BAA8B7I,GACpD,IAAK+I,EAAU,CACbzJ,KAAK6J,mBACL,S,CAGIC,EAAQL,EAASM,MAAM/J,KAAKqB,YAClCyI,EAAME,SAAQ,SAACC,GACblK,EAAKkF,QAAQgF,EACf,IAEAjK,KAAK6J,mB,iBAiBChK,EAAAwF,UAAAwE,iBAAA,SAAiBnJ,GAAA,GAAAA,SAAA,GAAAA,EAAA,EAAU,CACjCV,KAAKiE,YAAYvD,MAAQA,EACzBV,KAAKU,MAAQA,C,EAGPb,EAAAwF,UAAAJ,QAAA,SAAQO,GACd,IAAKxF,KAAKiB,WAAY,CACpB,IAAMmJ,EAASpK,KAAKO,cAAcyI,MAAK,SAACC,GAAS,OAAAA,EAAKP,gBAAkBlD,EAAKkD,aAA5B,IACjD,GAAI0B,EAAQ,M,CAGd,IAAKC,EAAqB7E,GAAO,CAC/B,M,CAGFxF,KAAKO,cAAa+J,4BAAA,GAAOtK,KAAKO,cAAa,OAAEiF,GAAI,M,EAG3C3F,EAAAwF,UAAA+D,aAAA,SAAa5D,GACnB,IAAM+E,EAAc/E,EAAKzB,OACzB,GAAI/D,KAAKoB,OAAS,SAAWoJ,EAAgBD,GAAc,CACzD,OAAO,K,CAET,OAAO,I,EAGD1K,EAAAwF,UAAAF,eAAA,WACNnF,KAAKO,cAAgBP,KAAKO,cAAckK,MAAM,EAAGzK,KAAKO,cAAc2E,OAAS,E,EAGvErF,EAAAwF,UAAAqF,WAAA,SAAWjI,GAEL,IAAAkI,EACRlI,EAAKC,OAAAiI,GAET3K,KAAKO,cAAgBP,KAAKO,cAAcqK,QAAO,SAACC,EAAOC,GAAU,OAAAA,EAAMC,aAAeJ,CAArB,IACjE3K,KAAK+C,eAAeC,KAAK,CAAEC,KAAMjD,KAAKO,cAAeG,MAAOV,KAAK2C,iBACjE3C,KAAKkD,UAAUF,KAAK,CAAEC,KAAMjD,KAAKG,iB,EAG3BN,EAAAwF,UAAA2F,YAAA,eAAAjL,EAAAC,KACN,IAAKA,KAAKO,cAAc2E,OAAQ,CAC9B,MAAO,E,CAGT,OAAOlF,KAAKO,cAAc6F,KAAI,SAAC6C,EAAM6B,GACnC,IAAMH,EAAKG,EAAMC,WACjB,IAAME,EAAQ,GACd,GAAIhC,EAAK/D,QAAU+F,EAAO,CACxB,OACEC,EACE,sBAAAP,GAAIA,EACJ5F,IAAK4F,EACLQ,MAAM,UACNC,OAAQrL,EAAKe,SACbuK,qBAAsB,SAAC5I,GAAU,OAAA1C,EAAK2K,WAAWjI,EAAhB,GAEhCwG,E,KAGA,CACL,OACEiC,EAAa,eAAAnG,IAAK4F,EAAIW,SAAS,aAAY,eAAerC,GACxDiC,EACE,sBAAAP,GAAIA,EACJ5F,IAAK4F,EACLQ,MAAM,UACNC,OAAQrL,EAAKe,SACbuK,qBAAsB,SAAC5I,GAAU,OAAA1C,EAAK2K,WAAWjI,EAAhB,GAEhC,GAAA6D,OAAG2C,EAAKwB,MAAM,EAAGQ,GAAM,S,CAKlC,G,EAGMpL,EAAAwF,UAAAkG,WAAA,WACN,OACEvL,KAAKgB,MACHkK,EAAA,OACEM,MAAO,CACLC,YAAa,KACb,uBAAwBzL,KAAKe,QAG/BmK,EAAU,YAAAQ,KAAM1L,KAAKe,MAAQ,SAAW,QAASyE,KAAMxF,KAAKgB,KAAMmK,MAAM,Y,EAMxEtL,EAAAwF,UAAAsG,YAAA,WACN,OACE3L,KAAKe,OACHmK,EAAA,SACEM,MAAO,CACLI,wBAAyB,KACzB,mCAAoC5L,KAAKK,YAAcL,KAAKc,WAG9DoK,EAAA,YAAUW,QAAQ,QAAQC,KAAK,QAC5B9L,KAAKe,O,EAORlB,EAAAwF,UAAA0G,cAAA,WACN,IAAM/K,EAAOhB,KAAKW,OAAS,QAAUX,KAAKY,QAAU,YAAc,OAClE,IAAIoL,EAAUhM,KAAKW,OAASX,KAAKa,aAAeb,KAAKY,QAAUZ,KAAKwB,eAAiBxB,KAAKuB,cAE1F,IAAKyK,GAAWhM,KAAKI,iBAAkB4L,EAAUhM,KAAKM,iBAEtD,IAAM2L,EACJjM,KAAKW,QAAUX,KAAKI,iBAChB,wCACAJ,KAAKY,QACH,yCACA,iBAER,GAAIoL,EAAS,CACX,OACEd,EAAA,OAAKM,MAAOS,EAAQC,KAAK,kBACvBhB,EAAK,OAAAM,MAAM,wBACTN,EAAA,YAAUQ,KAAK,UAAUlG,KAAMxE,EAAMmL,MAAM,UAAUhB,MAAM,aAE7DD,EAAA,YAAUM,MAAM,uBAAuBK,QAAQ,SAC5CG,G,CAMT,OAAOI,S,EAGDvM,EAAAwF,UAAAgH,YAAA,SAAY3L,GAClB,OAAOA,EAAMgI,cAAcgB,QAAQ,KAAM,I,EAG3C7J,EAAAwF,UAAAiH,OAAA,eAAAvM,EAAAC,KACE,IAAMK,EAAYL,KAAKK,YAAcL,KAAKc,SAE1C,IAAI2C,EAA4B,GAChC,GAAIzD,KAAK8F,QAAS,CAChB,UAAW9F,KAAK8F,UAAY,SAAU,CACpC,IACErC,EAAkBsC,KAAKC,MAAMhG,KAAK8F,Q,CAClC,MAAOG,GAAG,C,KACP,CACLxC,EAAkBzD,KAAK8F,O,EAI3B,OACEoF,EAAA,OAAAnG,IAAA,2CAAKyG,MAAM,SAASe,SAAS,IAAIjI,QAAStE,KAAKgE,gBAAiBwI,OAAQxM,KAAKmE,oBAC3E+G,EAAK,OAAAnG,IAAA,2CAAAyG,MAAO,CAAEiB,cAAe,MAAM,gBAAiBzM,KAAKc,SAAW,OAAS,KAAM4L,QAAS1M,KAAKuC,QAC/F2I,EAAA,OAAAnG,IAAA,2CACEyG,MAAO,CACL9G,MAAO,KACP,wBAAyB1E,KAAKW,SAAWX,KAAKI,iBAC9C,sBAAuBJ,KAAKW,QAAUX,KAAKI,iBAC3C,uBAAwBJ,KAAKY,QAC7B,wBAAyBZ,KAAKc,SAC9B,iBAAkBd,KAAKe,MACvB,iBAAkBV,GAEpBqM,QAAS1M,KAAKqE,gBAEbrE,KAAKuL,aACNL,EAAK,OAAAnG,IAAA,2CAAAyG,MAAM,oBACRxL,KAAK2L,cACNT,EAAA,OAAAnG,IAAA,2CAAKyG,MAAO,CAAEmB,0BAA2B,OACtC3M,KAAKO,cAAc2E,OAAS,GAC3BgG,EAAA,QAAAnG,IAAA,2CAAM6H,MAAO,CAAEC,OAAQ7M,KAAK6M,OAAQC,UAAW9M,KAAK8M,WAAatB,MAAM,qBACpExL,KAAKgL,eAGVE,EACE,SAAAnG,IAAA,2CAAAgI,IAAK,SAACrI,GAAK,OAAM3E,EAAKkE,YAAcS,CAAzB,EACX8G,MAAO,CAAEwB,uBAAwB,MACjCxH,KAAMxF,KAAKyB,UACXwL,UAAWjN,KAAKiN,UAChBvL,YAAa1B,KAAK0B,YAClB8C,QAASxE,KAAKwE,QACdF,QAAStE,KAAKsE,QACdkI,OAAQ,WAAM,OAAAzM,EAAKsJ,cAAL,EACd6D,SAAU,WAAM,OAAAnN,EAAKoK,YAAL,EAChBzJ,MAAOV,KAAKU,MACZI,SAAUd,KAAKc,SAAQ,YACZd,KAAK4B,SAChBuL,UAAWnN,KAAK8E,oBAItBoG,EAAK,OAAAnG,IAAA,2CAAAyG,MAAM,gBACTN,EAAU,YAAAnG,IAAA,2CAAAgI,IAAK,SAAC5K,GAAO,OAAApC,EAAKsC,YAAYF,EAAjB,EAAsBuJ,KAAK,QAAQP,MAAM,aAEjEnL,KAAKY,SAAWsK,EAAA,YAAAnG,IAAA,2CAAUyG,MAAM,eAAehG,KAAK,QAAQ2G,MAAM,UAAUT,KAAK,eAEnF1L,KAAK+L,iBAERb,EAAA,OAAAnG,IAAA,2CACEgI,IAAK,SAAC5K,GAAO,OAAApC,EAAKmC,YAAYC,EAAjB,EACbqJ,MAAO,CACL4B,gBAAiB,KACjB,wBAAyBpN,KAAKC,SAG/BwD,EAAgB2C,KAAI,SAAC9C,GAAM,OAC1B4H,EAAA,qBACEnG,IAAKhF,EAAKsM,YAAY/I,EAAO5C,OAC7B2M,iBAAkBtN,EAAKyC,QACvB9B,MAAO4C,EAAO5C,MACd4M,OAAQhK,EAAOgK,QAEdhK,EAAOvC,MAPgB,IAU5BmK,EAAQ,QAAAnG,IAAA,6CACP/E,KAAKkB,YAAc,MAAQlB,KAAKkJ,sBAC/BgC,EAAA,qBAAAnG,IAAA,2CACE4F,GAAG,aACHjK,MAAM,MACNgM,QAAS,WAAM,OAAA3M,EAAKoD,iBAAiBpD,EAAKkE,YAAYvD,MAAvC,GAEdV,KAAKS,UACLT,KAAKiE,YAAYvD,QAGpBV,KAAKkB,WAAalB,KAAKkJ,sBACvBgC,EAAA,qBAAAnG,IAAA,2CAAmB4F,GAAG,YAAYjK,MAAM,OACrCV,KAAKmB,kB,0UA/1BI,I", "ignoreList": []}