import{r as t,h as e,H as s,a as o}from"./p-C3J6Z5OX.js";const b=".sc-bds-table-body-h{display:table-row-group;height:64px}.multiple.sc-bds-table-body-h{border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16))}.sc-bds-table-body-h:last-child{border-bottom:none}";const r=class{constructor(e){t(this,e);this.multipleRows=false}componentWillLoad(){const t=this.element.closest("bds-table");if(t&&(t.getAttribute("collapse")==="true"||t.collapse===true)){this.multipleRows=true}}render(){return e(s,{key:"6fd4435dd90fba8dbdf3ec4cf22b128a27869818",class:{host:true,multiple:this.multipleRows}},e("slot",{key:"3b8c873e9c861b5e6b39b46f8f9097d342d2681d"}))}get element(){return o(this)}};r.style=b;export{r as bds_table_body};
//# sourceMappingURL=p-bd391f6c.entry.js.map