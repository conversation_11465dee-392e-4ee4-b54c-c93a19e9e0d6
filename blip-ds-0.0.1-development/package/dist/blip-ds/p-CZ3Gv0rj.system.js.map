{"version": 3, "file": "p-CZ3Gv0rj.system.js", "sources": ["src/components/tabs/tab-group.scss?tag=bds-tab-group&encapsulation=shadow", "src/components/tabs/tab-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n.tab_group {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n\n  &__header {\n    padding: 4px 16px;\n    overflow: hidden;\n\n    &__itens {\n      display: flex;\n      flex-direction: row;\n      width: max-content;\n      gap: 32px;\n      margin: auto;\n\n      &__center {\n        justify-content: center;\n        margin: auto;\n      }\n\n      &__right {\n        justify-content: right;\n        margin: 0 0 0 auto;\n      }\n\n      &__left {\n        justify-content: left;\n        margin: 0 auto 0 0;\n      }\n\n      &__item {\n        cursor: pointer;\n        height: 46px;\n        gap: 4px;\n        width: auto;\n        display: flex;\n        align-items: center;\n        border-bottom: 2px solid transparent;\n        position: relative;\n\n        &__typo{\n          color: $color-content-disable;\n          &__disable {\n            color: $color-content-ghost;\n          }\n          &__error {\n            color: $color-surface-negative;\n          }\n        }\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: -4px;\n          border: 2px solid transparent;\n          border-radius: 4px;\n        }\n\n        &:focus-visible {\n          outline: none;\n\n          &::before {\n            border-color: $color-focus;\n          }\n        }\n\n        &__open {\n          color: $color-content-default;\n          border-color: $color-primary;\n        }\n        &__disable {\n          cursor: no-drop;\n        }\n      }\n    }\n  }\n\n  &__slide {\n    position: relative;\n    overflow: hidden;\n    padding: 0 16px;\n    height: 54px;\n    margin-left: 56px;\n    margin-right: 56px;\n\n    &-button {\n      position: absolute;\n      z-index: 1;\n      background-color: $color-surface-1;\n\n      &[icon='arrow-left'] {\n        left: 0;\n      }\n      &[icon='arrow-right'] {\n        right: 0;\n      }\n    }\n\n    &__itens {\n      position: absolute;\n      left: 56px;\n      width: max-content;\n      height: 48px;\n      display: flex;\n      flex-direction: row;\n      justify-content: center;\n      padding: 4px;\n      gap: 32px;\n      -webkit-transition: left 0.5s;\n      -moz-transition: left 0.5s;\n      transition: left 0.5s;\n    }\n  }\n  &__content {\n    height: 100%;\n  }\n\n  &__scrolled {\n    flex-shrink: 999;\n    overflow: none;\n\n    @include custom-scroll;\n  }\n}\n", "import { Component, h, Host, Element, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { Itens } from './tab-group-interface';\n\n@Component({\n  tag: 'bds-tab-group',\n  styleUrl: 'tab-group.scss',\n  shadow: true,\n})\nexport class BdsTabGroup {\n  private tabItensElement?: HTMLCollectionOf<HTMLBdsTabItemElement> = null;\n  private tabItensSlideElement?: NodeListOf<HTMLElement> = null;\n  private headerElement?: HTMLElement;\n  private headerSlideElement?: HTMLElement;\n  private isSlide?: number;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalItens: Itens[];\n\n  @State() isSlideTabs?: boolean = false;\n\n  @State() alignTab?: 'left' | 'scrolling' | 'right' = 'left';\n\n  @State() tabRefSlide?: number = 0;\n\n  @State() positionLeft?: number = 0;\n\n  @Prop() contentScrollable?: boolean = true;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonPrev is the data-test to button prev.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonNext is the data-test to button next.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * bdsTabChange. Event to return value when Tabs is change.\n   */\n  @Event() bdsTabChange?: EventEmitter;\n  /**\n   * bdsTabDisabled. Event to return value when Tabs disable is change.\n   */\n  @Event() bdsTabDisabled?: EventEmitter;\n\n  componentWillRender() {\n    this.tabItensElement = this.element.getElementsByTagName('bds-tab-item') as HTMLCollectionOf<HTMLBdsTabItemElement>;\n    this.setnumberElement();\n    this.setFirstActive();\n    this.setInternalItens(Array.from(this.tabItensElement));\n    this.getEventsDisable(Array.from(this.tabItensElement));\n  }\n\n  componentDidLoad() {\n    this.tabItensSlideElement = this.element.shadowRoot.querySelectorAll(\n      '.tab_group__header__itens__item',\n    ) as NodeListOf<HTMLElement>;\n  }\n\n  connectedCallback() {\n    this.isSlide = window.setInterval(() => {\n      this.isSlideTabs = this.checkSlideTabs();\n    }, 100);\n  }\n\n  private getEventsDisable = (ItensElement): void => {\n    ItensElement.forEach((element) => {\n      element.addEventListener(\n        'tabDisabled',\n        () => {\n          this.setInternalItens(Array.from(this.tabItensElement));\n        },\n        false,\n      );\n    });\n  };\n\n  disconnectedCallback() {\n    window.clearInterval(this.isSlide);\n  }\n\n  private checkSlideTabs = (): boolean => {\n    if (this.headerElement || this.headerSlideElement) {\n      if (this.headerSlideElement?.offsetWidth > this.headerElement?.offsetWidth) {\n        return true;\n      }\n    }\n  };\n\n  private setFirstActive = () => {\n    const hasOpenDefined = Array.from(this.tabItensElement).filter((obj) => obj.open);\n    if (!hasOpenDefined.length) {\n      this.tabItensElement[0].open = true;\n    }\n  };\n\n  private setnumberElement = () => {\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      this.tabItensElement[i].numberElement = i;\n    }\n  };\n\n  private setInternalItens = (ItensElement) => {\n    const arrayItens = ItensElement.map((item, index) => {\n      return {\n        label: item.label,\n        open: item.open,\n        numberElement: index,\n        badge: item.badge,\n        ...(item.disable !== undefined && { disable: item.disable }),\n        ...(item.error !== undefined && { error: item.error }),\n        ...(item.headerStyle !== undefined && { headerStyle: item.headerStyle }),\n        ...(item.contentStyle !== undefined && { contentStyle: item.contentStyle }),\n        ...(item.icon !== undefined && { icon: item.icon }),\n        ...(item.iconPosition !== undefined && { iconPosition: item.iconPosition }),\n        ...(item.iconTheme !== undefined && { iconTheme: item.iconTheme }),\n        ...(item.badgeShape !== undefined && { badgeShape: item.badgeShape }),\n        ...(item.badgeColor !== undefined && { badgeColor: item.badgeColor }),\n        ...(item.badgeIcon !== undefined && { badgeIcon: item.badgeIcon }),\n        ...(item.badgeAnimation !== undefined && { badgeAnimation: item.badgeAnimation }),\n        ...(item.badgeNumber !== undefined && { badgeNumber: item.badgeNumber }),\n        ...(item.badgePosition !== undefined && { badgePosition: item.badgePosition }),\n        ...(item.dataTest !== undefined && { dataTest: item.dataTest }),\n      };\n    });\n    return (this.internalItens = arrayItens);\n  };\n\n  private handleClick = (numberItem) => {\n    const updateInternalItens = this.internalItens.map((item) => {\n      return {\n        label: item.label,\n        open: false,\n        numberElement: item.numberElement,\n      };\n    });\n    this.internalItens = updateInternalItens;\n    for (let i = 0; i < this.tabItensElement.length; i++) {\n      if (this.tabItensElement[i].numberElement != numberItem) {\n        this.tabItensElement[i].open = false;\n      } else {\n        this.tabItensElement[i].open = true;\n        this.bdsTabChange.emit(this.tabItensElement[i]);\n      }\n    }\n  };\n\n  private refHeaderElement = (el: HTMLElement): void => {\n    this.headerElement = el;\n  };\n\n  private refHeaderSlideElement = (el: HTMLElement): void => {\n    this.headerSlideElement = el;\n  };\n\n  private handleDisabled = (id) => {\n    this.bdsTabDisabled.emit(this.tabItensElement[id]);\n  };\n\n  private nextSlide = () => {\n    const minLeft = this.headerElement?.offsetWidth - this.headerSlideElement?.offsetWidth;\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft - this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition < minLeft ? minLeft : newPosition;\n    this.alignTab = newPosition < minLeft ? 'right' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide + 1 : numberClicks;\n  };\n\n  private prevSlide = () => {\n    const calcNumber = this.headerSlideElement?.offsetWidth / this.headerElement?.offsetWidth;\n    const numberClicks = parseInt(calcNumber.toString());\n    const newPosition = this.positionLeft + this.headerElement?.offsetWidth;\n\n    this.positionLeft = newPosition > 0 ? 0 : newPosition;\n    this.alignTab = newPosition > 0 ? 'left' : 'scrolling';\n\n    this.tabRefSlide = numberClicks <= this.tabRefSlide ? this.tabRefSlide - 1 : numberClicks;\n  };\n\n  private handleKeyDown(event, item) {\n    if (event.key == 'Enter') {\n      item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement);\n    }\n    if (event.key == 'ArrowRight') {\n      this.tabItensSlideElement[item.numberElement + 1].focus();\n    }\n    if (event.key == 'ArrowLeft') {\n      this.tabItensSlideElement[item.numberElement - 1].focus();\n    }\n  }\n\n  private parseInlineStyle(styleString: string): { [key: string]: string } {\n    if (!styleString) return {};\n    \n    return styleString\n      .split(';')\n      .filter(style => style.trim())\n      .reduce((acc, style) => {\n        const [property, value] = style.split(':').map(s => s.trim());\n        if (property && value) {\n          // Convert kebab-case to camelCase for CSS properties\n          const camelProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase());\n          acc[camelProperty] = value;\n        }\n        return acc;\n      }, {});\n  }\n\n  private renderIcon = (Icon, Theme, disable, error) => {\n    return (\n      <bds-icon\n        class={{ \n          tab_group__header__itens__item__typo__disable: disable,\n          tab_group__header__itens__item__typo__error: error \n        }}\n        size=\"x-small\"\n        name={Icon}\n        theme={Theme}\n      ></bds-icon>\n    );\n  };\n\n  private renderBadge = (Shape, Color, Icon, Animation, Number) => {\n    return (\n      <bds-grid justify-content=\"center\">\n        <bds-badge color={Color} icon={Icon} number={Number} shape={Shape} animation={Animation}></bds-badge>\n      </bds-grid>\n    );\n  };\n\n  render(): HTMLElement {\n    const slidePosition = { left: `${this.positionLeft}px` };\n    \n    // Find the currently open tab to get its headerStyle and contentStyle\n    const openTab = this.internalItens?.find(item => item.open);\n    const headerStyle = openTab?.headerStyle ? this.parseInlineStyle(openTab.headerStyle) : {};\n    const contentStyle = openTab?.contentStyle ? this.parseInlineStyle(openTab.contentStyle) : {};\n    \n    return (\n      <Host>\n        <div class={{ tab_group: true }}>\n          {this.isSlideTabs && this.alignTab != 'left' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-left\"\n              size=\"short\"\n              id=\"bds-tabs-button-left\"\n              onClick={() => this.prevSlide()}\n              dataTest={this.dtButtonPrev}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__header: true, tab_group__slide: this.isSlideTabs }} \n            ref={this.refHeaderElement}\n            style={headerStyle}\n          >\n            <div\n              class={{\n                tab_group__header__itens: true,\n                tab_group__slide__itens: this.isSlideTabs,\n                [`tab_group__header__itens__${this.align}`]: !this.isSlideTabs,\n              }}\n              ref={this.refHeaderSlideElement}\n              style={slidePosition}\n            >\n              {this.internalItens &&\n                this.internalItens.map((item, index) => {\n                  const bold = item.open == true ? 'bold' : 'regular';\n                  return (\n                    <div\n                      class={{\n                        tab_group__header__itens__item: true,\n                        tab_group__header__itens__item__open: item.open,\n                        tab_group__header__itens__item__disable: item.disable,\n                      }}\n                      key={index}\n                      tabindex=\"0\"\n                      onClick={() =>\n                        item.disable ? this.handleDisabled(item.numberElement) : this.handleClick(item.numberElement)\n                      }\n                      onKeyDown={(ev) => this.handleKeyDown(ev, item)}\n                    >\n                      {item.iconPosition === 'left' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'left' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                      <bds-typo\n                        class={{ \n                          tab_group__header__itens__item__typo__disable: item.disable,\n                          tab_group__header__itens__item__typo__error: item.error \n                        }}\n                        variant=\"fs-16\"\n                        bold={bold}\n                      >\n                        {item.label}\n                      </bds-typo>\n                      {item.iconPosition === 'right' && item.icon\n                        ? this.renderIcon(item.icon, item.iconTheme, item.disable, item.error)\n                        : ''}\n                      {item.badgePosition === 'right' && item.badge\n                        ? this.renderBadge(\n                            item.badgeShape,\n                            item.badgeColor,\n                            item.badgeIcon,\n                            item.badgeAnimation,\n                            item.badgeNumber,\n                          )\n                        : ''}\n                    </div>\n                  );\n                })}\n            </div>\n          </div>\n          {this.isSlideTabs && this.alignTab != 'right' && (\n            <bds-button-icon\n              class=\"tab_group__slide-button\"\n              icon=\"arrow-right\"\n              size=\"short\"\n              id=\"bds-tabs-button-right\"\n              onClick={() => this.nextSlide()}\n              dataTest={this.dtButtonNext}\n              variant=\"secondary\"\n            ></bds-button-icon>\n          )}\n          <div \n            class={{ tab_group__content: true, tab_group__scrolled: this.contentScrollable }}\n            style={contentStyle}\n          >\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,WAAW,GAAG,utFAAutF;;YCQ9tF,WAAW,4BAAA,MAAA;MALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MAMU,QAAA,IAAe,CAAA,eAAA,GAA6C,IAAI;MAChE,QAAA,IAAoB,CAAA,oBAAA,GAA6B,IAAI;MASpD,QAAA,IAAW,CAAA,WAAA,GAAa,KAAK;MAE7B,QAAA,IAAQ,CAAA,QAAA,GAAoC,MAAM;MAElD,QAAA,IAAW,CAAA,WAAA,GAAY,CAAC;MAExB,QAAA,IAAY,CAAA,YAAA,GAAY,CAAC;MAE1B,QAAA,IAAiB,CAAA,iBAAA,GAAa,IAAI;MAElC,QAAA,IAAK,CAAA,KAAA,GAAgC,QAAQ;MAErD;;;MAGG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;MAEpC;;;MAGG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;MA+B5B,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,YAAY,KAAU;MAChD,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;MAC/B,gBAAA,OAAO,CAAC,gBAAgB,CACtB,aAAa,EACb,MAAK;MACH,oBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;uBACxD,EACD,KAAK,CACN;MACH,aAAC,CAAC;MACJ,SAAC;MAMO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAc;;kBACrC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,kBAAkB,EAAE;sBACjD,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,0CAAE,WAAW,KAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,CAAA,EAAE;MAC1E,oBAAA,OAAO,IAAI;;;MAGjB,SAAC;MAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAK;kBAC5B,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC;MACjF,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;sBAC1B,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI;;MAEvC,SAAC;MAEO,QAAA,IAAgB,CAAA,gBAAA,GAAG,MAAK;MAC9B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;sBACpD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC;;MAE7C,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,YAAY,KAAI;kBAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;MAClD,gBAAA,OACE,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,aAAa,EAAE,KAAK,EACpB,KAAK,EAAE,IAAI,CAAC,KAAK,EACd,GAAC,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAC,GACxD,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAC,GAClD,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EACpE,GAAC,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,EACvE,GAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAC,GAC/C,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,EAAC,GACvE,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAC9D,GAAC,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,EACjE,GAAC,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,EAAC,GACjE,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAC,GAC9D,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,EAC7E,GAAC,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EACpE,GAAC,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,EAC1E,GAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAC9D;MACJ,aAAC,CAAC;MACF,YAAA,QAAQ,IAAI,CAAC,aAAa,GAAG,UAAU;MACzC,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,UAAU,KAAI;kBACnC,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;sBAC1D,OAAO;0BACL,KAAK,EAAE,IAAI,CAAC,KAAK;MACjB,oBAAA,IAAI,EAAE,KAAK;0BACX,aAAa,EAAE,IAAI,CAAC,aAAa;uBAClC;MACH,aAAC,CAAC;MACF,YAAA,IAAI,CAAC,aAAa,GAAG,mBAAmB;MACxC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;sBACpD,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,UAAU,EAAE;0BACvD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK;;2BAC/B;0BACL,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI;MACnC,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;MAGrD,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,EAAe,KAAU;MACnD,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;MACzB,SAAC;MAEO,QAAA,IAAA,CAAA,qBAAqB,GAAG,CAAC,EAAe,KAAU;MACxD,YAAA,IAAI,CAAC,kBAAkB,GAAG,EAAE;MAC9B,SAAC;MAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAE,KAAI;MAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;MACpD,SAAC;MAEO,QAAA,IAAS,CAAA,SAAA,GAAG,MAAK;;kBACvB,MAAM,OAAO,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,KAAG,MAAA,IAAI,CAAC,kBAAkB,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAA;kBACtF,MAAM,UAAU,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,KAAG,MAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAA;kBACzF,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;MACpD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,IAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAA;MAEvE,YAAA,IAAI,CAAC,YAAY,GAAG,WAAW,GAAG,OAAO,GAAG,OAAO,GAAG,WAAW;MACjE,YAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,GAAG,OAAO,GAAG,OAAO,GAAG,WAAW;kBAE7D,IAAI,CAAC,WAAW,GAAG,YAAY,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,YAAY;MAC3F,SAAC;MAEO,QAAA,IAAS,CAAA,SAAA,GAAG,MAAK;;kBACvB,MAAM,UAAU,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,kBAAkB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,WAAW,KAAG,MAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAA;kBACzF,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;MACpD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,IAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,WAAW,CAAA;MAEvE,YAAA,IAAI,CAAC,YAAY,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW;MACrD,YAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,WAAW;kBAEtD,IAAI,CAAC,WAAW,GAAG,YAAY,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,YAAY;MAC3F,SAAC;MA+BO,QAAA,IAAU,CAAA,UAAA,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,KAAI;MACnD,YAAA,QACE,CACE,CAAA,UAAA,EAAA,EAAA,KAAK,EAAE;MACL,oBAAA,6CAA6C,EAAE,OAAO;MACtD,oBAAA,2CAA2C,EAAE;MAC9C,iBAAA,EACD,IAAI,EAAC,SAAS,EACd,IAAI,EAAE,IAAI,EACV,KAAK,EAAE,KAAK,EAAA,CACF;MAEhB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,KAAI;MAC9D,YAAA,QACE,mCAA0B,QAAQ,EAAA,EAChC,CAAW,CAAA,WAAA,EAAA,EAAA,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAA,CAAc,CAC5F;MAEf,SAAC;MAmHF;UA7SC,mBAAmB,GAAA;cACjB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAA4C;cACnH,IAAI,CAAC,gBAAgB,EAAE;cACvB,IAAI,CAAC,cAAc,EAAE;MACrB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;MACvD,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;UAGzD,gBAAgB,GAAA;MACd,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAClE,iCAAiC,CACP;;UAG9B,iBAAiB,GAAA;cACf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,MAAK;MACrC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;eACzC,EAAE,GAAG,CAAC;;UAeT,oBAAoB,GAAA;MAClB,QAAA,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;UAwG5B,aAAa,CAAC,KAAK,EAAE,IAAI,EAAA;MAC/B,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;kBACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;;MAE/F,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,YAAY,EAAE;MAC7B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;;MAE3D,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,WAAW,EAAE;MAC5B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;;;MAIrD,IAAA,gBAAgB,CAAC,WAAmB,EAAA;MAC1C,QAAA,IAAI,CAAC,WAAW;MAAE,YAAA,OAAO,EAAE;MAE3B,QAAA,OAAO;mBACJ,KAAK,CAAC,GAAG;mBACT,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;MAC5B,aAAA,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAI;kBACrB,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;MAC7D,YAAA,IAAI,QAAQ,IAAI,KAAK,EAAE;;sBAErB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;MAC9E,gBAAA,GAAG,CAAC,aAAa,CAAC,GAAG,KAAK;;MAE5B,YAAA,OAAO,GAAG;eACX,EAAE,EAAE,CAAC;;UAyBV,MAAM,GAAA;;cACJ,MAAM,aAAa,GAAG,EAAE,IAAI,EAAE,CAAG,EAAA,IAAI,CAAC,YAAY,CAAI,EAAA,CAAA,EAAE;;MAGxD,QAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;MAC3D,QAAA,MAAM,WAAW,GAAG,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,WAAW,IAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE;MAC1F,QAAA,MAAM,YAAY,GAAG,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,MAAA,GAAA,MAAA,GAAA,OAAO,CAAE,YAAY,IAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;MAE7F,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAA,EAC5B,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM,KAC1C,wEACE,KAAK,EAAC,yBAAyB,EAC/B,IAAI,EAAC,YAAY,EACjB,IAAI,EAAC,OAAO,EACZ,EAAE,EAAC,sBAAsB,EACzB,OAAO,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAC/B,QAAQ,EAAE,IAAI,CAAC,YAAY,EAC3B,OAAO,EAAC,WAAW,EAAA,CACF,CACpB,EACD,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,CAAC,WAAW,EAAE,EACtE,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAC1B,KAAK,EAAE,WAAW,EAAA,EAElB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,wBAAwB,EAAE,IAAI;sBAC9B,uBAAuB,EAAE,IAAI,CAAC,WAAW;sBACzC,CAAC,CAAA,0BAAA,EAA6B,IAAI,CAAC,KAAK,CAAA,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW;MAC/D,aAAA,EACD,GAAG,EAAE,IAAI,CAAC,qBAAqB,EAC/B,KAAK,EAAE,aAAa,EAAA,EAEnB,IAAI,CAAC,aAAa;kBACjB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;MACrC,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,MAAM,GAAG,SAAS;MACnD,gBAAA,QACE,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE;MACL,wBAAA,8BAA8B,EAAE,IAAI;8BACpC,oCAAoC,EAAE,IAAI,CAAC,IAAI;8BAC/C,uCAAuC,EAAE,IAAI,CAAC,OAAO;MACtD,qBAAA,EACD,GAAG,EAAE,KAAK,EACV,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,MACP,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,EAE/F,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,EAAA,EAE9C,IAAI,CAAC,YAAY,KAAK,MAAM,IAAI,IAAI,CAAC;4BAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;4BACnE,EAAE,EACL,IAAI,CAAC,aAAa,KAAK,MAAM,IAAI,IAAI,CAAC;4BACnC,IAAI,CAAC,WAAW,CACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,WAAW;4BAElB,EAAE,EACN,CAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAE;8BACL,6CAA6C,EAAE,IAAI,CAAC,OAAO;8BAC3D,2CAA2C,EAAE,IAAI,CAAC;2BACnD,EACD,OAAO,EAAC,OAAO,EACf,IAAI,EAAE,IAAI,EAET,EAAA,IAAI,CAAC,KAAK,CACF,EACV,IAAI,CAAC,YAAY,KAAK,OAAO,IAAI,IAAI,CAAC;4BACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;4BACnE,EAAE,EACL,IAAI,CAAC,aAAa,KAAK,OAAO,IAAI,IAAI,CAAC;4BACpC,IAAI,CAAC,WAAW,CACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,WAAW;MAEpB,sBAAE,EAAE,CACF;mBAET,CAAC,CACA,CACF,EACL,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,KAC3C,wEACE,KAAK,EAAC,yBAAyB,EAC/B,IAAI,EAAC,aAAa,EAClB,IAAI,EAAC,OAAO,EACZ,EAAE,EAAC,uBAAuB,EAC1B,OAAO,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,EAC/B,QAAQ,EAAE,IAAI,CAAC,YAAY,EAC3B,OAAO,EAAC,WAAW,EAAA,CACF,CACpB,EACD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAChF,KAAK,EAAE,YAAY,EAAA,EAEnB,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,CACF,CACD;;;;;;;;;;;;"}