{"version": 3, "names": ["bannerLinkCss", "BannerLink", "constructor", "hostRef", "this", "target", "dataTest", "_buttonClickHandler", "bdsBannerLink", "emit", "el", "window", "open", "link", "handleKeyDown", "event", "key", "render", "Element", "h", "class", "banner__link", "onClick", "tabindex", "onKeyDown", "bind"], "sources": ["src/components/banner/banner-link/banner-link.scss?tag=bds-banner-link&encapsulation=shadow", "src/components/banner/banner-link/banner-link.tsx"], "sourcesContent": ["@use '../../../globals/colors' as *;\n\n:Host {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  text-decoration: underline;\n  white-space: nowrap;\n  margin-left: 16px;\n  order: 2;\n}\n\n.banner__link{\n  position: relative;\n  \n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n  \n    &:focus-visible {\n      outline: none;\n  \n      &::before {\n        border-color: $color-focus;\n      }\n    }\n}", "import { Component, h, Element, Event, EventEmitter, Prop } from '@stencil/core';\n\nexport type targets = 'blank' | 'self' | 'parent' | 'top' | 'framename';\n\n@Component({\n  tag: 'bds-banner-link',\n  styleUrl: 'banner-link.scss',\n  shadow: true,\n})\nexport class BannerLink {\n  @Element() el: HTMLBdsBannerElement;\n  /**\n   * Set the link pass.\n   */\n  @Prop() link: string;\n\n  /**\n   * Set the link pass.\n   */\n  @Prop() target: targets = 'blank';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the link is clicked.\n   */\n  @Event() bdsBannerLink!: EventEmitter;\n\n  private _buttonClickHandler = () => {\n    this.bdsBannerLink.emit(this.el);\n    window.open(this.link, `_${this.target}`);\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.bdsBannerLink.emit(this.el);\n      window.open(this.link, `_${this.target}`);\n    }\n  }\n\n  render(): HTMLElement {\n    const Element = 'a';\n\n    return (\n      <Element\n        class={{ banner__link: true }}\n        onClick={() => this._buttonClickHandler()}\n        data-test={this.dataTest}\n        tabindex=\"0\"\n        onKeyDown={this.handleKeyDown.bind(this)}\n      >\n        <slot></slot>\n      </Element>\n    );\n  }\n}\n"], "mappings": "yDAAA,MAAMA,EAAgB,sc,MCSTC,EAAU,MALvB,WAAAC,CAAAC,G,qDAeUC,KAAMC,OAAY,QAKlBD,KAAQE,SAAY,KAMpBF,KAAmBG,oBAAG,KAC5BH,KAAKI,cAAcC,KAAKL,KAAKM,IAC7BC,OAAOC,KAAKR,KAAKS,KAAM,IAAIT,KAAKC,SAAS,CAyB5C,CAtBS,aAAAS,CAAcC,GACpB,GAAIA,EAAMC,KAAO,QAAS,CACxBZ,KAAKI,cAAcC,KAAKL,KAAKM,IAC7BC,OAAOC,KAAKR,KAAKS,KAAM,IAAIT,KAAKC,S,EAIpC,MAAAY,GACE,MAAMC,EAAU,IAEhB,OACEC,EAACD,EACC,CAAAF,IAAA,2CAAAI,MAAO,CAAEC,aAAc,MACvBC,QAAS,IAAMlB,KAAKG,sBAAqB,YAC9BH,KAAKE,SAChBiB,SAAS,IACTC,UAAWpB,KAAKU,cAAcW,KAAKrB,OAEnCe,EAAa,QAAAH,IAAA,6C", "ignoreList": []}