{"version": 3, "file": "p-BRHWfLxD.system.js", "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree-group&encapsulation=shadow", "src/components/nav-tree/nav-tree-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, Element, State, Prop, h, EventEmitter, Event, Method } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree-group',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTreeGroup {\n  private itemsElement?: HTMLCollectionOf<HTMLBdsNavTreeElement> = null;\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  /**\n   * Collapse. Used to set mode of iteraction of componente when navigate with menu. You can choose a option single or multiple.\n   */\n  @Prop() collapse?: collapses = 'single';\n\n  @Event() bdsNavTreeGroupCloseAll?: EventEmitter;\n  @Event() bdsNavTreeGroupOpenAll?: EventEmitter;\n\n  componentWillRender() {\n    this.itemsElement = this.element.getElementsByTagName('bds-nav-tree') as HTMLCollectionOf<HTMLBdsNavTreeElement>;\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      this.itemsElement[i].reciveNumber(i);\n    }\n  }\n\n  @Method()\n  async closeAll(actNumber?) {\n    this.bdsNavTreeGroupCloseAll.emit();\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.itemsElement[i].close();\n      } else {\n        this.itemsElement[i].close();\n      }\n    }\n  }\n\n  @Method()\n  async openAll(actNumber?) {\n    this.bdsNavTreeGroupOpenAll.emit();\n    for (let i = 0; i < this.itemsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.itemsElement[i].open();\n      } else {\n        this.itemsElement[i].open();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;MAAA,MAAM,UAAU,GAAG,04KAA04K;;YCSh5K,YAAY,iCAAA,MAAA;MALzB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;MAMU,QAAA,IAAY,CAAA,YAAA,GAA6C,IAAI;MAG5D,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;MACpC,QAAA,IAAY,CAAA,YAAA,GAAI,IAAI;MAC7B;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAe,QAAQ;MA2CxC;UAtCC,mBAAmB,GAAA;cACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAA4C;MAChH,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;kBACjD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;;;UAKxC,MAAM,QAAQ,CAAC,SAAU,EAAA;MACvB,QAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE;MACnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACjD,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE;sBAC/B,IAAI,SAAS,IAAI,CAAC;0BAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;uBAC3C;sBACL,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;;UAMlC,MAAM,OAAO,CAAC,SAAU,EAAA;MACtB,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;MAClC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACjD,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE;sBAC/B,IAAI,SAAS,IAAI,CAAC;0BAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;;uBAC1C;sBACL,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;;;;UAKjC,MAAM,GAAA;cACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;;;;;;"}