import{r as t,c as i,h as e,a as s}from"./p-C3J6Z5OX.js";import{g as o,p as r}from"./p-BNEKIkjk.js";import{w as n,e as a}from"./p-BXYXNVip.js";const l=':host{display:block}.element_input{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%}.element_input input{-webkit-box-shadow:inherit;box-shadow:inherit}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-moz-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input:-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-ms-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.element_input input::-webkit-input-placeholder{color:var(--color-content-ghost, rgb(140, 140, 140));opacity:1}.input{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;padding:8px 4px 8px 12px;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;gap:8px}.input .bds-icon{position:relative;z-index:1}.input--state-primary{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary .input__icon{position:relative}.input--state-primary .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input--state-primary:hover{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-primary.input--pressed{border:1px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235));box-shadow:0 0 0 2px var(--color-info, rgb(128, 227, 235))}.input--state-primary.input--pressed .input__icon .bds-icon{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-primary .input__container__label--pressed bds-typo{color:var(--color-primary, rgb(30, 107, 241))}.input--state-primary .input__container__text{caret-color:var(--color-primary, rgb(30, 107, 241));color:var(--color-content-default, rgb(40, 40, 40))}.input--state-danger{border:1px solid var(--color-delete, rgb(230, 15, 15));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger .input__icon{position:relative}.input--state-danger .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-negative, rgb(138, 0, 0));z-index:0;opacity:50%;border-radius:8px}.input--state-danger:hover{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-danger.input--pressed{border:1px solid var(--color-negative, #e60f0f);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190));box-shadow:0 0 0 2px var(--color-error, rgb(250, 190, 190))}.input--state-danger.input--pressed .input__icon .bds-icon{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__label{color:var(--color-delete, rgb(230, 15, 15))}.input--state-danger .input__container__label--pressed bds-typo{color:var(--color-negative, #e60f0f)}.input--state-danger .input__container__text{caret-color:var(--color-negative, #e60f0f);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success{border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success .input__icon{position:relative}.input--state-success .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-surface-positive, rgb(1, 114, 62));z-index:0;border-radius:8px}.input--state-success:hover{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px}.input--state-success.input--pressed{border:1px solid var(--color-positive, #10603b);-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188));box-shadow:0 0 0 2px var(--color-success, rgb(132, 235, 188))}.input--state-success.input--pressed .input__icon .bds-icon{color:var(--color-positive, #10603b)}.input--state-success .input__container__label{color:var(--color-content-default, rgb(40, 40, 40))}.input--state-success .input__container__label--pressed bds-typo{color:var(--color-positive, #10603b)}.input--state-success .input__container__text{caret-color:var(--color-positive, #10603b);color:var(--color-content-default, rgb(40, 40, 40))}.input--state-disabled{opacity:50%;pointer-events:none;cursor:not-allowed}.input--state-disabled .input__icon{position:relative}.input--state-disabled .input__icon::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));z-index:0;opacity:50%;border-radius:8px}.input .icon-success{color:var(--color-positive, #10603b);margin-left:4px}.input--label{padding:7px 4px 7px 12px}.input__icon{cursor:inherit;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border-radius:8px;padding:2px}.input__container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;width:100%}.input__container__wrapper{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.input__container__wrapper__chips{display:inline;max-height:100px;overflow:auto}.input__container__wrapper__chips::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__wrapper__chips::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__container__label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input__container__text{display:inline-block;margin:0;border:0;padding:0;width:auto;vertical-align:middle;white-space:normal;line-height:inherit;background:none;color:inherit;font-size:inherit;font-family:inherit;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;line-height:150%;width:100%;line-height:22px;resize:none;cursor:inherit}.input__container__text:focus{outline:0}.input__container__text::-webkit-file-upload-button{padding:0;border:0;background:none}.input__container__text:focus{outline:0}.input__container__text[type=checkbox],.input__container__text[type=radio]{width:13px;height:13px}.input__container__text[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box}::-webkit-search-decoration{display:none}.input__container__text[type=reset],.input__container__text[type=button],.input__container__text[type=submit]{overflow:visible}.input__container__text::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.input__container__text::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input__message{display:-ms-flexbox;display:flex;-ms-flex-align:baseline;align-items:baseline;height:20px;margin:3.7px 2.5px;-webkit-transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);transition:0.3s cubic-bezier(0.4, 0, 0.2, 1);color:var(--color-content-ghost, rgb(140, 140, 140));word-break:break-word;height:auto;min-height:20px}.input__message bds-typo{margin-top:0px;-ms-flex-item-align:self-start;align-self:self-start}.input__message__icon{display:-ms-flexbox;display:flex;padding-right:4px;margin-top:0px;padding-top:2px}.input__message--danger .bds-icon{color:var(--color-negative, #e60f0f)}.input__message--danger .input__message__text{color:var(--color-negative, #e60f0f)}.input__message--success .input__message__icon .bds-icon{color:var(--color-positive, #10603b)}.input__message--success .input__message__text{color:var(--color-content-default, rgb(40, 40, 40))}.select{position:relative;outline:none}.select__icon{color:var(--color-content-ghost, rgb(140, 140, 140));display:-ms-flexbox;display:flex}.select__options{background:var(--color-surface-0, rgb(255, 255, 255));width:100%;max-height:200px;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:absolute;left:0;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));overflow-y:auto;z-index:2;pointer-events:none;opacity:0}.select__options::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.select__options::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.select__options--open{opacity:1;pointer-events:auto}.select__options--position-top{bottom:calc(100% + 4px)}.select__options--position-bottom{top:calc(100% + 4px)}.inside-input-left{display:-ms-inline-flexbox;display:inline-flex;gap:8px;-ms-flex-wrap:wrap;flex-wrap:wrap;max-height:200px;overflow-y:auto}.inside-input-left::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.inside-input-left::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.input-chips__chip{margin:2px 4px 2px 0px}.input-chips__chips{-ms-flex:1;flex:1}';const c=class{constructor(e){t(this,e);this.bdsChange=i(this,"bdsChange");this.bdsCancel=i(this,"bdsCancel");this.bdsFocus=i(this,"bdsFocus");this.bdsBlur=i(this,"bdsBlur");this.bdsChangeChips=i(this,"bdsChangeChips");this.bdsSelectChipsInput=i(this,"bdsSelectChipsInput");this.bdsSubmit=i(this,"bdsSubmit");this.isOpen=false;this.intoView=null;this.selectedOptions=[];this.validationDanger=false;this.isPressed=false;this.validationMesage="";this.internalChips=[];this.chips=[];this.newPrefix="";this.value="";this.danger=false;this.success=false;this.errorMessage="";this.disabled=false;this.label="";this.icon="";this.duplicated=false;this.canAddNew=true;this.notFoundMessage="No results found";this.type="text";this.delimiters=/,|;/;this.disableSubmit=false;this.helperMessage="";this.successMessage="";this.inputName="";this.placeholder="";this.optionsPosition="auto";this.dataTest=null;this.handleChangeChipsValue=async()=>{await this.resetFilterOptions()};this.refDropdown=t=>{this.dropElement=t};this.refIconDrop=t=>{this.iconDropElement=t};this.toggle=()=>{if(!this.disabled){this.isOpen=!this.isOpen}};this.handler=async t=>{const{detail:{value:i}}=t;this.selectedOption=i;const e=this.getText(i);await this.addChip(e);this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions});this.toggle()};this.handlerNewOption=async t=>{await this.addChip(t);this.toggle()};this.getText=t=>{const i=this.childOptions.find((i=>i.value===t));return this.getTextFromOption(i)};this.getTextFromOption=t=>{var i,e;if(this.internalOptions){const i=this.internalOptions.find((i=>i.value==(t===null||t===void 0?void 0:t.value)));if(i){return i.label}}return(t===null||t===void 0?void 0:t.titleText)?t.titleText:(e=(i=t===null||t===void 0?void 0:t.textContent)===null||i===void 0?void 0:i.trim())!==null&&e!==void 0?e:""};this.setFocusWrapper=()=>{if(this.nativeInput){this.nativeInput.focus()}};this.removeFocusWrapper=()=>{this.nativeInput.blur()};this.onClickWrapper=()=>{this.onFocus();if(this.nativeInput){this.nativeInput.focus()}};this.onFocus=()=>{this.bdsFocus.emit();this.isPressed=true};this.onInput=t=>{const i=t.target;if(i){this.value=i.value||""}this.bdsSelectChipsInput.emit(t);this.changedInputValue()};this.keyPressWrapper=t=>{switch(t.key){case"Enter":if(this.canAddNew!==false){this.handleDelimiters();this.setChip(this.value);this.value="";this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions})}if(!this.disabled){this.isOpen=true}break;case"ArrowDown":if(!this.disabled){this.isOpen=true}break;case"ArrowUp":if(!this.disabled){this.isOpen=false}break;case"Backspace":case"Delete":if((this.value===null||this.value.length<=0)&&this.internalChips.length){this.removeLastChip();this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions})}break}};this.changedInputValue=async()=>{this.value=this.nativeInput.value;if(this.nativeInput.value){await this.filterOptions(this.nativeInput.value)}else{await this.resetFilterOptions()}if(this.value&&this.isOpen===false){this.isOpen=true}}}isOpenChanged(t){if(this.positionHeightDrop=="bottom"){this.iconDropElement.name=this.isOpen?"arrow-up":"arrow-down"}else{this.iconDropElement.name=this.isOpen?"arrow-down":"arrow-up"}if(t)if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}}handleWindow(t){if(!this.el.contains(t.target)){this.isOpen=false}}optionsChanged(){if(typeof this.options==="string"){try{this.internalOptions=JSON.parse(this.options)}catch(t){}}else{this.internalOptions=this.options}}valueChanged(){if(this.chips){if(typeof this.chips==="string"){try{this.internalChips=JSON.parse(this.chips)}catch(t){this.internalChips=[]}}else{this.internalChips=this.chips}}else{this.internalChips=[]}}internalValueChanged(){this.handleChangeChipsValue();if(this.internalChips.length>0){this.selectedOptions=this.internalChips.map((t=>({label:t,value:`${this.validValueChip(t,this.childOptions)}`})))}}validValueChip(t,i){const e=i===null||i===void 0?void 0:i.find((i=>i.textContent==t));return`${e?e.value:t}`}async isValid(){return this.validateChips()}async getChips(){return this.internalChips}async clear(){this.internalChips=[];this.value=""}async add(t){this.handleDelimiters();if(t){this.setChip(t)}else{this.setChip(this.value)}this.value=""}async setFocus(){this.nativeInput.focus()}async removeFocus(){this.nativeInput.blur()}componentWillLoad(){this.valueChanged();this.optionsChanged();this.intoView=o(this.el)}async componentDidLoad(){await this.resetFilterOptions();if(this.optionsPosition!="auto"){this.setDefaultPlacement(this.optionsPosition)}else{this.validatePositionDrop()}}setDefaultPlacement(t){if(t=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}}validatePositionDrop(){const t=r({actionElement:this.el,changedElement:this.dropElement,intoView:this.intoView});this.positionHeightDrop=t.y;if(t.y=="bottom"){this.dropElement.classList.add("select__options--position-bottom");this.iconDropElement.name="arrow-down"}else{this.dropElement.classList.add("select__options--position-top");this.iconDropElement.name="arrow-up"}}async connectedCallback(){for(const t of this.childOptions){t.addEventListener("optionSelected",this.handler)}}get childOptionsEnabled(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option:not([invisible]):not(#option-add):not(#no-option)")):Array.from(this.el.querySelectorAll("bds-select-option:not([invisible]):not(#option-add):not(#no-option)"))}get childOptions(){return this.options?Array.from(this.el.shadowRoot.querySelectorAll("bds-select-option:not(#option-add):not(#no-option)")):Array.from(this.el.querySelectorAll("bds-select-option:not(#option-add):not(#no-option)"))}async filterOptions(t){if(!t){await this.resetFilterOptions();return}for(const i of this.childOptions){const e=this.existsChip(i.textContent,await this.getChips());const s=i.textContent.toLowerCase();const o=t.toLowerCase();if(e){i.setAttribute("invisible","invisible")}if(t&&s.includes(o)&&!e){i.removeAttribute("invisible")}if(t&&!s.includes(o)&&!e){i.setAttribute("invisible","invisible")}}}async resetFilterOptions(){for(const t of this.childOptions){if(this.existsChip(t.textContent,await this.getChips())){t.setAttribute("invisible","invisible")}else{t.removeAttribute("invisible")}}}existsChip(t,i){return i.some((i=>t===i))}enableCreateOption(){return!!(this.childOptionsEnabled.length===0&&this.nativeInput&&this.nativeInput.value)}async addChip(t){await this.setChip(t);this.nativeInput.value=""}validateChips(){if(this.type==="email"){return!this.internalChips.some((t=>!this.validateChip(t)))}else{return true}}handleOnBlur(){this.bdsBlur.emit();this.isPressed=false}verifyAndSubstituteDelimiters(t){if(t.length===1&&t[0].match(this.delimiters)){return""}let i=t.replace(/;/g,",").replace(/\,+|;+/g,",");if(i[0].match(this.delimiters)){i=i.substring(1)}return i}handleDelimiters(){const t=this.nativeInput.value;this.value=t?t.trim():"";if(t.length===0)return;const i=t.match(this.delimiters);if(!i)return;const e=this.verifyAndSubstituteDelimiters(t);if(!e){this.clearInputValues();return}const s=e.split(this.delimiters);s.forEach((t=>{this.setChip(t.trimStart())}));this.clearInputValues()}async handleChange(t){const{detail:{value:i}}=t;this.value=i?i.trim():"";if(i.length===0)return;const e=i.match(this.delimiters);if(!e)return;const s=this.verifyAndSubstituteDelimiters(i);if(!s){this.clearInputValues();return}const o=s.split(this.delimiters);o.forEach((t=>{this.setChip(t)}));this.clearInputValues()}clearInputValues(t=""){this.nativeInput.value=t;this.value=t}setChip(t){if(!this.duplicated){const i=this.internalChips.some((i=>i.toLowerCase()===t.toLowerCase()));if(i)return}if(!n(t)){return}this.internalChips=[...this.internalChips,t]}validateChip(t){const i=t.trim();if(this.type==="email"&&a(i)){return false}return true}removeLastChip(){this.internalChips=this.internalChips.slice(0,this.internalChips.length-1)}removeChip(t){const{detail:{id:i}}=t;this.internalChips=this.internalChips.filter(((t,e)=>e.toString()!==i));this.bdsChangeChips.emit({data:this.internalChips,value:this.selectedOption});this.bdsChange.emit({data:this.selectedOptions})}renderChips(){if(!this.internalChips.length){return[]}return this.internalChips.map(((t,i)=>{const s=i.toString();const o=30;if(t.length<=o){return e("bds-chip-clickable",{id:s,key:s,color:"outline",close:!this.disabled,onChipClickableClose:t=>this.removeChip(t)},t)}else{return e("bds-tooltip",{key:s,position:"top-center","tooltip-text":t},e("bds-chip-clickable",{id:s,key:s,color:"outline",close:!this.disabled,onChipClickableClose:t=>this.removeChip(t)},`${t.slice(0,o)} ...`))}}))}renderIcon(){return this.icon&&e("div",{class:{input__icon:true,"input__icon--large":!!this.label}},e("bds-icon",{size:this.label?"medium":"small",name:this.icon,color:"inherit"}))}renderLabel(){return this.label&&e("label",{class:{input__container__label:true,"input__container__label--pressed":this.isPressed&&!this.disabled}},e("bds-typo",{variant:"fs-12",bold:"bold"},this.label))}renderMessage(){const t=this.danger?"error":this.success?"checkball":"info";let i=this.danger?this.errorMessage:this.success?this.successMessage:this.helperMessage;if(!i&&this.validationDanger)i=this.validationMesage;const s=this.danger||this.validationDanger?"input__message input__message--danger":this.success?"input__message input__message--success":"input__message";if(i){return e("div",{class:s,part:"input__message"},e("div",{class:"input__message__icon"},e("bds-icon",{size:"x-small",name:t,theme:"outline",color:"inherit"})),e("bds-typo",{class:"input__message__text",variant:"fs-12"},i))}return undefined}generateKey(t){return t.toLowerCase().replace(/ /g,"-")}render(){const t=this.isPressed&&!this.disabled;let i=[];if(this.options){if(typeof this.options==="string"){try{i=JSON.parse(this.options)}catch(t){}}else{i=this.options}}return e("div",{key:"3df3da1874aaa4e0f131d22d4b63bc5b16e8366f",class:"select",tabindex:"0",onFocus:this.setFocusWrapper,onBlur:this.removeFocusWrapper},e("div",{key:"59b1b21a02059b15def9eaa8da5cfd399c0fb36a",class:{element_input:true},"aria-disabled":this.disabled?"true":null,onClick:this.toggle},e("div",{key:"572d01c470e0630380f58c5c0c41bb67311f0106",class:{input:true,"input--state-primary":!this.danger&&!this.validationDanger,"input--state-danger":this.danger||this.validationDanger,"input--state-success":this.success,"input--state-disabled":this.disabled,"input--label":!!this.label,"input--pressed":t},onClick:this.onClickWrapper},this.renderIcon(),e("div",{key:"442ca869cdda603d31835416f8228d5c4c4003a2",class:"input__container"},this.renderLabel(),e("div",{key:"deff52088bcde71354dd5f4f5762c139c7c67ab6",class:{input__container__wrapper:true}},this.internalChips.length>0&&e("span",{key:"b26b3b2394de5dd416ddca962797d500be51a5b4",style:{height:this.height,maxHeight:this.maxHeight},class:"inside-input-left"},this.renderChips()),e("input",{key:"2ce5972966e480523c8d1582acfe9d9d2dcef40d",ref:t=>this.nativeInput=t,class:{input__container__text:true},name:this.inputName,maxlength:this.maxlength,placeholder:this.placeholder,onInput:this.onInput,onFocus:this.onFocus,onBlur:()=>this.handleOnBlur(),onChange:()=>this.handleChange,value:this.value,disabled:this.disabled,"data-test":this.dataTest,onKeyDown:this.keyPressWrapper}))),e("div",{key:"f15ac21dae66265062a748843832d664edd438ef",class:"select__icon"},e("bds-icon",{key:"303c7455fe8fca1ad9739c0326ca4d3b5d15e3fd",ref:t=>this.refIconDrop(t),size:"small",color:"inherit"})),this.success&&e("bds-icon",{key:"0ef632747adf1bd7ad58233cddbd444531805cb6",class:"icon-success",name:"check",theme:"outline",size:"xxx-small"})),this.renderMessage()),e("div",{key:"9d9d432f436c4c055108bc197e2d5a244299df17",ref:t=>this.refDropdown(t),class:{select__options:true,"select__options--open":this.isOpen}},i.map((t=>e("bds-select-option",{key:this.generateKey(t.value),onOptionSelected:this.handler,value:t.value,status:t.status},t.label))),e("slot",{key:"f73ea2c85e54417dde1327fe70447163c5eb4358"}),this.canAddNew===true&&this.enableCreateOption()&&e("bds-select-option",{key:"f0599212b27fd2be1b6194f5f7054f429ce6219d",id:"option-add",value:"add",onClick:()=>this.handlerNewOption(this.nativeInput.value)},this.newPrefix,this.nativeInput.value),!this.canAddNew&&this.enableCreateOption()&&e("bds-select-option",{key:"ccbabb2fb4bfaf044bf19e0280553e6797b81be8",id:"no-option",value:"add"},this.notFoundMessage)))}get el(){return s(this)}static get watchers(){return{isOpen:["isOpenChanged"],options:["optionsChanged"],chips:["valueChanged"],internalChips:["internalValueChanged"]}}};c.style=l;export{c as bds_select_chips};
//# sourceMappingURL=p-63600148.entry.js.map