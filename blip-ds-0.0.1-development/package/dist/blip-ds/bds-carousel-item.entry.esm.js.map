{"version": 3, "file": "bds-carousel-item.entry.esm.js", "sources": ["src/components/carousel/carousel-item.scss?tag=bds-carousel-item&encapsulation=shadow", "src/components/carousel/carousel-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  box-sizing: border-box;\n  width: auto;\n}\n\n.carrosel-item-frame {\n  display: block;\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  position: relative;\n  overflow: hidden;\n\n  ::slotted(*) {\n    position: relative;\n  }\n}\n\n.image-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\nimport { Themes } from '../theme-provider/theme-provider';\n@Component({\n  tag: 'bds-carousel-item',\n  styleUrl: 'carousel-item.scss',\n  shadow: true,\n})\nexport class BdsCarouselItem {\n  /**\n   * Set what theme will be aplyed inside the component.\n   * 'light', 'dark';\n   */\n  @Prop() theme?: Themes = 'light';\n\n  @Prop() bgImage?: string;\n\n  @Prop() bgImageBrightness?: number = 1;\n\n  @Prop() bgColor?: string;\n\n  render(): HTMLElement {\n    return (\n      <Host class=\"carrosel-item\">\n        <bds-theme-provider theme={this.theme} class=\"carrosel-item-frame\" style={{ background: this.bgColor }}>\n          {this.bgImage && (\n            <bds-image\n              class=\"image-bg\"\n              alt=\"Example of a image\"\n              width=\"100%\"\n              height=\"100%\"\n              brightness={this.bgImageBrightness}\n              object-fit=\"cover\"\n              src={this.bgImage}\n            />\n          )}\n          <slot />\n        </bds-theme-provider>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,eAAe,GAAG,0SAA0S;;MCOrT,eAAe,GAAA,MAAA;AAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;;AAGG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,OAAO;AAIxB,QAAA,IAAiB,CAAA,iBAAA,GAAY,CAAC;AAwBvC;IApBC,MAAM,GAAA;QACJ,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACzB,CAAA,CAAA,oBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAoB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAC,qBAAqB,EAAC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,EAAA,EACnG,IAAI,CAAC,OAAO,KACX,kEACE,KAAK,EAAC,UAAU,EAChB,GAAG,EAAC,oBAAoB,EACxB,KAAK,EAAC,MAAM,EACZ,MAAM,EAAC,MAAM,EACb,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAA,YAAA,EACvB,OAAO,EAClB,GAAG,EAAE,IAAI,CAAC,OAAO,GACjB,CACH,EACD,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACW,CAChB;;;;;;;"}