{"version": 3, "names": ["navTreeCss", "NavTree", "constructor", "hostRef", "this", "itemsGroup", "isOpenAftAnimation", "navTreeChild", "numberElement", "collapse", "isOpen", "icon", "secondaryText", "dataTest", "loading", "disable", "handler", "toggle", "reciveNumber", "number", "open", "close", "isOpenChanged", "value", "bdsToogleChange", "emit", "element", "_a", "closeAll", "componentWillLoad", "parentElement", "tagName", "querySelector", "handleKeyDown", "event", "key", "render", "h", "Host", "tabindex", "onKeyDown", "bind", "class", "onClick", "nav_main", "nav_main_active", "text", "size", "name", "color", "theme", "variant", "tag", "bold", "margin", "accordion", "accordion_open"], "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree&encapsulation=shadow", "src/components/nav-tree/nav-tree.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, Element, State, Prop, Method, Event, EventEmitter, Watch, h } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTree {\n  private itemsGroup?: HTMLBdsNavTreeGroupElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  @State() numberElement?: number = null;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * A prop for make the nav open.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * When de open or close of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    if (!this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    if (value) {\n      if (this.itemsGroup.collapse == 'single') {\n        this.itemsGroup?.closeAll(this.numberElement);\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.itemsGroup =\n      this.element.parentElement.tagName == 'BDS-NAV-TREE-GROUP' &&\n      (this.element.parentElement as HTMLBdsNavTreeGroupElement);\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item') === null ? false : true;\n  }\n\n  private handler = (): void => {\n    if (!this.loading && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter' && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              [`nav_main--disable`]: this.disable,\n            }}\n          >\n            <div\n              onClick={this.handler}\n              class={{\n                nav_main: true,\n                nav_main_active: this.isOpen,\n                [`nav_main--loading`]: this.loading,\n                [`nav_main--disable`]: this.disable,\n              }}\n              data-test={this.dataTest}\n              aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n            >\n              {this.loading ? (\n                <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n              ) : this.icon ? (\n                <bds-icon\n                  class={{\n                    [`icon-item`]: true,\n                    [`icon-item-active`]: this.isOpen,\n                  }}\n                  size=\"medium\"\n                  name={this.icon}\n                  color=\"inherit\"\n                  theme=\"outline\"\n                ></bds-icon>\n              ) : (\n                ''\n              )}\n              <div class=\"nav_main_text\">\n                {this.text && (\n                  <bds-typo\n                    class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                    variant=\"fs-14\"\n                    tag=\"span\"\n                    line-height=\"small\"\n                    bold={this.isOpen ? 'bold' : 'semi-bold'}\n                  >\n                    {this.text}\n                  </bds-typo>\n                )}\n                {this.secondaryText && (\n                  <bds-typo\n                    class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                    variant=\"fs-12\"\n                    line-height=\"small\"\n                    tag=\"span\"\n                    margin={false}\n                  >\n                    {this.secondaryText}\n                  </bds-typo>\n                )}\n              </div>\n              <div class=\"nav_main_content\">\n                <slot name=\"header-content\"></slot>\n              </div>\n              {this.navTreeChild && (\n                <bds-icon\n                  name=\"arrow-down\"\n                  class={{\n                    [`nav_main_arrow`]: true,\n                    [`nav_main_arrow_active`]: this.isOpen,\n                    [`nav_main_arrow--loading`]: this.loading,\n                  }}\n                ></bds-icon>\n              )}\n            </div>\n          </div>\n        </div>\n        <div\n          class={{\n            accordion: true,\n            accordion_open: this.isOpen && this.navTreeChild,\n          }}\n        >\n          <div class={{ ['container']: true, [`container--disable`]: this.disable }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAa,i4K,MCSNC,EAAO,MALpB,WAAAC,CAAAC,G,yDAMUC,KAAUC,WAAgC,KAIzCD,KAAkBE,mBAAa,MAC/BF,KAAYG,aAAI,KAChBH,KAAaI,cAAY,KAI1BJ,KAAQK,SAAe,SAISL,KAAMM,OAAa,MAInDN,KAAIO,KAAY,KAQhBP,KAAaQ,cAAY,KAIzBR,KAAQS,SAAY,KAIpBT,KAAOU,QAAa,MAKpBV,KAAOW,QAAa,MA4CpBX,KAAOY,QAAG,KAChB,IAAKZ,KAAKU,UAAYV,KAAKW,QAAS,CAClCX,KAAKM,QAAUN,KAAKM,M,EAmGzB,CA1IC,YAAMO,GACJ,IAAKb,KAAKW,QAAS,CACjBX,KAAKM,QAAUN,KAAKM,M,EAKxB,kBAAMQ,CAAaC,GACjBf,KAAKI,cAAgBW,C,CAIvB,UAAMC,GACJhB,KAAKM,OAAS,I,CAIhB,WAAMW,GACJjB,KAAKM,OAAS,K,CAGN,aAAAY,CAAcC,G,MACtBnB,KAAKoB,gBAAgBC,KAAK,CAAEF,MAAOA,EAAOG,QAAStB,KAAKsB,UACxD,GAAIH,EAAO,CACT,GAAInB,KAAKC,WAAWI,UAAY,SAAU,EACxCkB,EAAAvB,KAAKC,cAAY,MAAAsB,SAAA,SAAAA,EAAAC,SAASxB,KAAKI,c,GAKrC,iBAAAqB,GACEzB,KAAKC,WACHD,KAAKsB,QAAQI,cAAcC,SAAW,sBACrC3B,KAAKsB,QAAQI,cAChB1B,KAAKG,aAAeH,KAAKsB,QAAQM,cAAc,uBAAyB,KAAO,MAAQ,I,CASjF,aAAAC,CAAcC,GACpB,GAAIA,EAAMC,KAAO,UAAY/B,KAAKW,QAAS,CACzCX,KAAKM,QAAUN,KAAKM,M,EAIxB,MAAA0B,GACE,OACEC,EAACC,EAAI,CAAAH,IAAA,4CACHE,EAAA,OAAAF,IAAA,2CAAKI,SAAS,IAAIC,UAAWpC,KAAK6B,cAAcQ,KAAKrC,MAAOsC,MAAM,SAChEL,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACL,CAAC,qBAAsBtC,KAAKW,UAG9BsB,EAAA,OAAAF,IAAA,2CACEQ,QAASvC,KAAKY,QACd0B,MAAO,CACLE,SAAU,KACVC,gBAAiBzC,KAAKM,OACtB,CAAC,qBAAsBN,KAAKU,QAC5B,CAAC,qBAAsBV,KAAKW,SAC7B,YACUX,KAAKS,SAAQ,aACZT,KAAK0C,MAAQ1C,KAAKQ,eAAiB,KAAKR,KAAKQ,kBAExDR,KAAKU,QACJuB,EAAqB,uBAAAU,KAAK,gBACxB3C,KAAKO,KACP0B,EACE,YAAAK,MAAO,CACL,CAAC,aAAc,KACf,CAAC,oBAAqBtC,KAAKM,QAE7BqC,KAAK,SACLC,KAAM5C,KAAKO,KACXsC,MAAM,UACNC,MAAM,YACI,GAIdb,EAAK,OAAAF,IAAA,2CAAAO,MAAM,iBACRtC,KAAK0C,MACJT,EAAA,YAAAF,IAAA,2CACEO,MAAO,CAAE,CAAC,cAAe,KAAM,CAAC,uBAAwBtC,KAAKU,SAC7DqC,QAAQ,QACRC,IAAI,OACQ,sBACZC,KAAMjD,KAAKM,OAAS,OAAS,aAE5BN,KAAK0C,MAGT1C,KAAKQ,eACJyB,EACE,YAAAF,IAAA,2CAAAO,MAAO,CAAE,CAAC,iBAAkB,KAAM,CAAC,0BAA2BtC,KAAKU,SACnEqC,QAAQ,QAAO,cACH,QACZC,IAAI,OACJE,OAAQ,OAEPlD,KAAKQ,gBAIZyB,EAAK,OAAAF,IAAA,2CAAAO,MAAM,oBACTL,EAAA,QAAAF,IAAA,2CAAMa,KAAK,oBAEZ5C,KAAKG,cACJ8B,EAAA,YAAAF,IAAA,2CACEa,KAAK,aACLN,MAAO,CACL,CAAC,kBAAmB,KACpB,CAAC,yBAA0BtC,KAAKM,OAChC,CAAC,2BAA4BN,KAAKU,cAO9CuB,EAAA,OAAAF,IAAA,2CACEO,MAAO,CACLa,UAAW,KACXC,eAAgBpD,KAAKM,QAAUN,KAAKG,eAGtC8B,EAAA,OAAAF,IAAA,2CAAKO,MAAO,CAAE,CAAC,aAAc,KAAM,CAAC,sBAAuBtC,KAAKW,UAC9DsB,EAAA,QAAAF,IAAA,+C", "ignoreList": []}