var __extends=this&&this.__extends||function(){var r=function(n,e){r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)if(Object.prototype.hasOwnProperty.call(n,e))r[e]=n[e]};return r(n,e)};return function(n,e){if(typeof e!=="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");r(n,e);function t(){this.constructor=n}n.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}}();var __assign=this&&this.__assign||function(){__assign=Object.assign||function(r){for(var n,e=1,t=arguments.length;e<t;e++){n=arguments[e];for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i))r[i]=n[i]}return r};return __assign.apply(this,arguments)};var __awaiter=this&&this.__awaiter||function(r,n,e,t){function i(r){return r instanceof e?r:new e((function(n){n(r)}))}return new(e||(e=Promise))((function(e,a){function f(r){try{o(t.next(r))}catch(r){a(r)}}function u(r){try{o(t["throw"](r))}catch(r){a(r)}}function o(r){r.done?e(r.value):i(r.value).then(f,u)}o((t=t.apply(r,n||[])).next())}))};var __generator=this&&this.__generator||function(r,n){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},t,i,a,f;return f={next:u(0),throw:u(1),return:u(2)},typeof Symbol==="function"&&(f[Symbol.iterator]=function(){return this}),f;function u(r){return function(n){return o([r,n])}}function o(u){if(t)throw new TypeError("Generator is already executing.");while(f&&(f=0,u[0]&&(e=0)),e)try{if(t=1,i&&(a=u[0]&2?i["return"]:u[0]?i["throw"]||((a=i["return"])&&a.call(i),0):i.next)&&!(a=a.call(i,u[1])).done)return a;if(i=0,a)u=[u[0]&2,a.value];switch(u[0]){case 0:case 1:a=u;break;case 4:e.label++;return{value:u[1],done:false};case 5:e.label++;i=u[1];u=[0];continue;case 7:u=e.ops.pop();e.trys.pop();continue;default:if(!(a=e.trys,a=a.length>0&&a[a.length-1])&&(u[0]===6||u[0]===2)){e=0;continue}if(u[0]===3&&(!a||u[1]>a[0]&&u[1]<a[3])){e.label=u[1];break}if(u[0]===6&&e.label<a[1]){e.label=a[1];a=u;break}if(a&&e.label<a[2]){e.label=a[2];e.ops.push(u);break}if(a[2])e.ops.pop();e.trys.pop();continue}u=n.call(r,e)}catch(r){u=[6,r];i=0}finally{t=a=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(r,n,e){if(e||arguments.length===2)for(var t=0,i=n.length,a;t<i;t++){if(a||!(t in n)){if(!a)a=Array.prototype.slice.call(n,0,t);a[t]=n[t]}}return r.concat(a||Array.prototype.slice.call(n))};System.register([],(function(r,n){"use strict";return{execute:function(){var e=this;var t=r("N","blip-ds");var i={hydratedSelectorName:"hydrated",lazyLoad:true,slotRelocation:true,updatable:true};var a=r("g",(function(){}));var f="";var u=Object.defineProperty;var o=function(r,n){for(var e in n)u(r,e,{get:n[e],enumerable:true})};var l=function(r){if(r.__stencil__getHostRef){return r.__stencil__getHostRef()}return void 0};var v=r("r",(function(r,n){r.__stencil__getHostRef=function(){return n};n.t=r}));var c=function(r,n){var e={i:0,$hostElement$:r,u:n,o:new Map};{e.l=new Promise((function(r){return e.v=r}))}{e.h=new Promise((function(r){return e.p=r}));r["s-p"]=[];r["s-rc"]=[]}var t=e;r.__stencil__getHostRef=function(){return t};return t};var s=function(r,n){return n in r};var d=function(r,n){return(0,console.error)(r,n)};var h=new Map;var p=function(r,e,t){var i=r.m.replace(/-/g,"_");var a=r._;if(!a){return void 0}var f=h.get(a);if(f){return f[i]}
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/return n.import("./".concat(a,".entry.js").concat("")).then((function(r){{h.set(a,r)}return r[i]}),(function(r){d(r,e.$hostElement$)}))};var y=new Map;var m="{visibility:hidden}.hydrated{visibility:inherit}";var b="slot-fb{display:contents}slot-fb[hidden]{display:none}";var w="http://www.w3.org/1999/xlink";var _=r("w",typeof window!=="undefined"?window:{});var g={i:0,S:"",jmp:function(r){return r()},raf:function(r){return requestAnimationFrame(r)},ael:function(r,n,e,t){return r.addEventListener(n,e,t)},rel:function(r,n,e,t){return r.removeEventListener(n,e,t)},ce:function(r,n){return new CustomEvent(r,n)}};var S=function(){var r;var n=false;try{(r=_.document)==null?void 0:r.addEventListener("e",null,Object.defineProperty({},"passive",{get:function(){n=true}}))}catch(r){}return n}();var $=r("p",(function(r){return Promise.resolve(r)}));var j=function(){try{new CSSStyleSheet;return typeof(new CSSStyleSheet).replaceSync==="function"}catch(r){}return false}();var O=false;var k=[];var C=[];var N=function(r,n){return function(e){r.push(e);if(!O){O=true;if(n&&g.i&4){x(A)}else{g.raf(A)}}}};var E=function(r){for(var n=0;n<r.length;n++){try{r[n](performance.now())}catch(r){d(r)}}r.length=0};var A=function(){E(k);{E(C);if(O=k.length>0){g.raf(A)}}};var x=function(r){return $().then(r)};var M=N(C,true);var T=function(r){return r!=null&&r!==void 0};var P=function(r){r=typeof r;return r==="object"||r==="function"};function R(r){var n,e,t;return(t=(e=(n=r.head)==null?void 0:n.querySelector('meta[name="csp-nonce"]'))==null?void 0:e.getAttribute("content"))!=null?t:void 0}var L=function(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")};var U={};o(U,{err:function(){return D},map:function(){return W},ok:function(){return F},unwrap:function(){return B},unwrapErr:function(){return H}});var F=function(r){return{isOk:true,isErr:false,value:r}};var D=function(r){return{isOk:false,isErr:true,value:r}};function W(r,n){if(r.isOk){var e=n(r.value);if(e instanceof Promise){return e.then((function(r){return F(r)}))}else{return F(e)}}if(r.isErr){var t=r.value;return D(t)}throw"should never get here"}var B=function(r){if(r.isOk){return r.value}else{throw r.value}};var H=function(r){if(r.isErr){return r.value}else{throw r.value}};function z(r){var n=this.attachShadow({mode:"open"});if(j){var e=new CSSStyleSheet;e.replaceSync(f);n.adoptedStyleSheets.push(e)}}var I=function(r){var n=ar(r,"childNodes");if(r.tagName&&r.tagName.includes("-")&&r["s-cr"]&&r.tagName!=="SLOT-FB"){V(n,r.tagName).forEach((function(r){if(r.nodeType===1&&r.tagName==="SLOT-FB"){if(Y(r,K(r),false).length){r.hidden=true}else{r.hidden=false}}}))}var e=0;for(e=0;e<n.length;e++){var t=n[e];if(t.nodeType===1&&ar(t,"childNodes").length){I(t)}}};var G=function(r){var n=[];for(var e=0;e<r.length;e++){var t=r[e]["s-nr"]||void 0;if(t&&t.isConnected){n.push(t)}}return n};function V(r,n,e){var t=0;var i=[];var a;for(;t<r.length;t++){a=r[t];if(a["s-sr"]&&(!n||a["s-hn"]===n)&&(e===void 0||K(a)===e)){i.push(a);if(typeof e!=="undefined")return i}i=__spreadArray(__spreadArray([],i,true),V(a.childNodes,n,e),true)}return i}var Y=function(r,n,e){if(e===void 0){e=true}var t=[];if(e&&r["s-sr"]||!r["s-sr"])t.push(r);var i=r;while(i=i.nextSibling){if(K(i)===n&&(e||!i["s-sr"]))t.push(i)}return t};var q=function(r,n){if(r.nodeType===1){if(r.getAttribute("slot")===null&&n===""){return true}if(r.getAttribute("slot")===n){return true}return false}if(r["s-sn"]===n){return true}return n===""};var J=function(r,n,e,t){if(r["s-ol"]&&r["s-ol"].isConnected){return}var i=document.createTextNode("");i["s-nr"]=r;if(!n["s-cr"]||!n["s-cr"].parentNode)return;var a=n["s-cr"].parentNode;var f=ar(a,"appendChild");{f.call(a,i)}r["s-ol"]=i;r["s-sh"]=n["s-hn"]};var K=function(r){return typeof r["s-sn"]==="string"?r["s-sn"]:r.nodeType===1&&r.getAttribute("slot")||void 0};function Q(r){if(r.assignedElements||r.assignedNodes||!r["s-sr"])return;var n=function(n){return function(r){var e=[];var t=this["s-sn"];if(r==null?void 0:r.flatten){console.error("\n          Flattening is not supported for Stencil non-shadow slots.\n          You can use `.childNodes` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        ")}var i=this["s-cr"].parentElement;var a=i.__childNodes?i.childNodes:G(i.childNodes);a.forEach((function(r){if(t===K(r)){e.push(r)}}));if(n){return e.filter((function(r){return r.nodeType===1}))}return e}.bind(r)};r.assignedElements=n(true);r.assignedNodes=n(false)}function X(r){r.dispatchEvent(new CustomEvent("slotchange",{bubbles:false,cancelable:false,composed:false}))}function Z(r,n){var e;n=n||((e=r["s-ol"])==null?void 0:e.parentElement);if(!n)return{slotNode:null,slotName:""};var t=r["s-sn"]=K(r)||"";var i=ar(n,"childNodes");var a=V(i,n.tagName,t)[0];return{slotNode:a,slotName:t}}var rr=function(r){r.__appendChild=r.appendChild;r.appendChild=function(r){var n=Z(r,this),e=n.slotName,t=n.slotNode;if(t){J(r,t);var i=Y(t,e);var a=i[i.length-1];var f=ar(a,"parentNode");var u=ar(f,"insertBefore")(r,a.nextSibling);X(t);I(this);return u}return this.__appendChild(r)}};var nr=function(r){var n=function(r){__extends(n,r);function n(){return r!==null&&r.apply(this,arguments)||this}n.prototype.item=function(r){return this[r]};return n}(Array);ir("children",r);Object.defineProperty(r,"children",{get:function(){return this.childNodes.filter((function(r){return r.nodeType===1}))}});Object.defineProperty(r,"childElementCount",{get:function(){return this.children.length}});ir("firstChild",r);Object.defineProperty(r,"firstChild",{get:function(){return this.childNodes[0]}});ir("lastChild",r);Object.defineProperty(r,"lastChild",{get:function(){return this.childNodes[this.childNodes.length-1]}});ir("childNodes",r);Object.defineProperty(r,"childNodes",{get:function(){var r=new n;r.push.apply(r,G(this.__childNodes));return r}})};var er=["children","nextElementSibling","previousElementSibling"];var tr=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function ir(r,n){var e;if(er.includes(r)){e=Object.getOwnPropertyDescriptor(Element.prototype,r)}else if(tr.includes(r)){e=Object.getOwnPropertyDescriptor(Node.prototype,r)}if(!e){e=Object.getOwnPropertyDescriptor(n,r)}if(e)Object.defineProperty(n,"__"+r,e)}function ar(r,n){if("__"+n in r){var e=r["__"+n];if(typeof e!=="function")return e;return e.bind(r)}else{if(typeof r[n]!=="function")return r[n];return r[n].bind(r)}}var fr=function(r,n){if(n===void 0){n=""}{return function(){return}}};var ur=function(r,n){{return function(){return}}};var or=r("h",(function(r,n){var e=[];for(var t=2;t<arguments.length;t++){e[t-2]=arguments[t]}var i=null;var a=null;var f=null;var u=false;var o=false;var l=[];var v=function(n){for(var e=0;e<n.length;e++){i=n[e];if(Array.isArray(i)){v(i)}else if(i!=null&&typeof i!=="boolean"){if(u=typeof r!=="function"&&!P(i)){i=String(i)}if(u&&o){l[l.length-1].$+=i}else{l.push(u?lr(null,i):i)}o=u}}};v(e);if(n){if(n.key){a=n.key}if(n.name){f=n.name}{var c=n.className||n.class;if(c){n.class=typeof c!=="object"?c:Object.keys(c).filter((function(r){return c[r]})).join(" ")}}}if(typeof r==="function"){return r(n===null?{}:n,l,sr)}var s=lr(r,null);s.j=n;if(l.length>0){s.O=l}{s.k=a}{s.C=f}return s}));var lr=function(r,n){var e={i:0,N:r,$:n,A:null,O:null};{e.j=null}{e.k=null}{e.C=null}return e};var vr=r("H",{});var cr=function(r){return r&&r.N===vr};var sr={forEach:function(r,n){return r.map(dr).forEach(n)},map:function(r,n){return r.map(dr).map(n).map(hr)}};var dr=function(r){return{vattrs:r.j,vchildren:r.O,vkey:r.k,vname:r.C,vtag:r.N,vtext:r.$}};var hr=function(r){if(typeof r.vtag==="function"){var n=__assign({},r.vattrs);if(r.vkey){n.key=r.vkey}if(r.vname){n.name=r.vname}return or.apply(void 0,__spreadArray([r.vtag,n],r.vchildren||[],false))}var e=lr(r.vtag,r.vtext);e.j=r.vattrs;e.O=r.vchildren;e.k=r.vkey;e.C=r.vname;return e};var pr=function(r){var n=L(r);return new RegExp("(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?".concat(n,"))(").concat(n,"\\b)"),"g")};pr("::slotted");pr(":host");pr(":host-context");var yr=function(r,n,e){if(r!=null&&!P(r)){if(n&4){{return r==="false"?false:r===""||!!r}}if(n&2){return typeof r==="string"?parseFloat(r):typeof r==="number"?r:NaN}if(n&1){return String(r)}return r}return r};var mr=r("a",(function(r){return l(r).$hostElement$}));var br=r("c",(function(r,n,e){var t=mr(r);return{emit:function(r){return wr(t,n,{bubbles:true,composed:true,cancelable:true,detail:r})}}}));var wr=function(r,n,e){var t=g.ce(n,e);r.dispatchEvent(t);return t};var _r=new WeakMap;var gr=function(r,n,e){var t=y.get(r);if(j&&e){t=t||new CSSStyleSheet;if(typeof t==="string"){t=n}else{t.replaceSync(n)}}else{t=n}y.set(r,t)};var Sr=function(r,n,e){var t;var i=jr(n);var a=y.get(i);if(!_.document){return i}r=r.nodeType===11?r:_.document;if(a){if(typeof a==="string"){r=r.head||r;var f=_r.get(r);var u=void 0;if(!f){_r.set(r,f=new Set)}if(!f.has(i)){{u=_.document.createElement("style");u.innerHTML=a;var o=(t=g.M)!=null?t:R(_.document);if(o!=null){u.setAttribute("nonce",o)}if(!(n.i&1)){if(r.nodeName==="HEAD"){var l=r.querySelectorAll("link[rel=preconnect]");var v=l.length>0?l[l.length-1].nextSibling:r.querySelector("style");r.insertBefore(u,(v==null?void 0:v.parentNode)===r?v:null)}else if("host"in r){if(j){var c=new CSSStyleSheet;c.replaceSync(a);r.adoptedStyleSheets=__spreadArray([c],r.adoptedStyleSheets,true)}else{var s=r.querySelector("style");if(s){s.innerHTML=a+s.innerHTML}else{r.prepend(u)}}}else{r.append(u)}}if(n.i&1){r.insertBefore(u,null)}}if(n.i&4){u.innerHTML+=b}if(f){f.add(i)}}}else if(!r.adoptedStyleSheets.includes(a)){r.adoptedStyleSheets=__spreadArray(__spreadArray([],r.adoptedStyleSheets,true),[a],false)}}return i};var $r=function(r){var n=r.u;var e=r.$hostElement$;var t=n.i;var i=fr("attachStyles",n.m);var a=Sr(e.shadowRoot?e.shadowRoot:e.getRootNode(),n);if(t&10){e["s-sc"]=a;e.classList.add(a+"-h")}i()};var jr=function(r,n){return"sc-"+r.m};var Or=function(r,n,e,t,i,a,f){if(e===t){return}var u=s(r,n);var o=n.toLowerCase();if(n==="class"){var l=r.classList;var v=Cr(e);var c=Cr(t);{l.remove.apply(l,v.filter((function(r){return r&&!c.includes(r)})));l.add.apply(l,c.filter((function(r){return r&&!v.includes(r)})))}}else if(n==="style"){{for(var d in e){if(!t||t[d]==null){if(d.includes("-")){r.style.removeProperty(d)}else{r.style[d]=""}}}}for(var d in t){if(!e||t[d]!==e[d]){if(d.includes("-")){r.style.setProperty(d,t[d])}else{r.style[d]=t[d]}}}}else if(n==="key");else if(n==="ref"){if(t){t(r)}}else if(!u&&n[0]==="o"&&n[1]==="n"){if(n[2]==="-"){n=n.slice(3)}else if(s(_,o)){n=o.slice(2)}else{n=o[2]+n.slice(3)}if(e||t){var h=n.endsWith(Nr);n=n.replace(Er,"");if(e){g.rel(r,n,e,h)}if(t){g.ael(r,n,t,h)}}}else{var p=P(t);if((u||p&&t!==null)&&true){try{if(!r.tagName.includes("-")){var y=t==null?"":t;if(n==="list"){u=false}else if(e==null||r[n]!=y){if(typeof r.__lookupSetter__(n)==="function"){r[n]=y}else{r.setAttribute(n,y)}}}else if(r[n]!==t){r[n]=t}}catch(r){}}var m=false;{if(o!==(o=o.replace(/^xlink\:?/,""))){n=o;m=true}}if(t==null||t===false){if(t!==false||r.getAttribute(n)===""){if(m){r.removeAttributeNS(w,n)}else{r.removeAttribute(n)}}}else if((!u||a&4||i)&&!p&&r.nodeType===1){t=t===true?"":t;if(m){r.setAttributeNS(w,n,t)}else{r.setAttribute(n,t)}}}};var kr=/\s/;var Cr=function(r){if(typeof r==="object"&&r&&"baseVal"in r){r=r.baseVal}if(!r||typeof r!=="string"){return[]}return r.split(kr)};var Nr="Capture";var Er=new RegExp(Nr+"$");var Ar=function(r,n,e,t){var i=n.A.nodeType===11&&n.A.host?n.A.host:n.A;var a=r&&r.j||{};var f=n.j||{};{for(var u=0,o=xr(Object.keys(a));u<o.length;u++){var l=o[u];if(!(l in f)){Or(i,l,a[l],void 0,e,n.i)}}}for(var v=0,c=xr(Object.keys(f));v<c.length;v++){var l=c[v];Or(i,l,a[l],f[l],e,n.i)}};function xr(r){return r.includes("ref")?__spreadArray(__spreadArray([],r.filter((function(r){return r!=="ref"})),true),["ref"],false):r}var Mr;var Tr;var Pr;var Rr=false;var Lr=false;var Ur=false;var Fr=false;var Dr=function(r,n,e){var t;var a=n.O[e];var f=0;var u;var o;var l;if(!Rr){Ur=true;if(a.N==="slot"){a.i|=a.O?2:1}}if(a.$!==null){u=a.A=_.document.createTextNode(a.$)}else if(a.i&1){u=a.A=_.document.createTextNode("");{Ar(null,a,Fr)}}else{if(!_.document){throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.")}u=a.A=_.document.createElement(!Rr&&i.slotRelocation&&a.i&2?"slot-fb":a.N);{Ar(null,a,Fr)}if(T(Mr)&&u["s-si"]!==Mr){u.classList.add(u["s-si"]=Mr)}if(a.O){for(f=0;f<a.O.length;++f){o=Dr(r,a,f);if(o){u.appendChild(o)}}}}u["s-hn"]=Pr;{if(a.i&(2|1)){u["s-sr"]=true;u["s-cr"]=Tr;u["s-sn"]=a.C||"";u["s-rf"]=(t=a.j)==null?void 0:t.ref;Q(u);l=r&&r.O&&r.O[e];if(l&&l.N===a.N&&r.A){{Wr(r.A,false)}}{Qr(Tr,u,n.A,r==null?void 0:r.A)}}}return u};var Wr=function(r,n){g.i|=1;var e=Array.from(r.__childNodes||r.childNodes);for(var t=e.length-1;t>=0;t--){var i=e[t];if(i["s-hn"]!==Pr&&i["s-ol"]){Kr(Gr(i).parentNode,i,Gr(i));i["s-ol"].remove();i["s-ol"]=void 0;i["s-sh"]=void 0;Ur=true}if(n){Wr(i,n)}}g.i&=-2};var Br=function(r,n,e,t,i,a){var f=r["s-cr"]&&r["s-cr"].parentNode||r;var u;if(f.shadowRoot&&f.tagName===Pr){f=f.shadowRoot}for(;i<=a;++i){if(t[i]){u=Dr(null,e,i);if(u){t[i].A=u;Kr(f,u,Gr(n))}}}};var Hr=function(r,n,e){for(var t=n;t<=e;++t){var i=r[t];if(i){var a=i.A;Jr(i);if(a){{Lr=true;if(a["s-ol"]){a["s-ol"].remove()}else{Wr(a,true)}}a.remove()}}}};var zr=function(r,n,e,t,i){if(i===void 0){i=false}var a=0;var f=0;var u=0;var o=0;var l=n.length-1;var v=n[0];var c=n[l];var s=t.length-1;var d=t[0];var h=t[s];var p;var y;while(a<=l&&f<=s){if(v==null){v=n[++a]}else if(c==null){c=n[--l]}else if(d==null){d=t[++f]}else if(h==null){h=t[--s]}else if(Ir(v,d,i)){Vr(v,d,i);v=n[++a];d=t[++f]}else if(Ir(c,h,i)){Vr(c,h,i);c=n[--l];h=t[--s]}else if(Ir(v,h,i)){if(v.N==="slot"||h.N==="slot"){Wr(v.A.parentNode,false)}Vr(v,h,i);Kr(r,v.A,c.A.nextSibling);v=n[++a];h=t[--s]}else if(Ir(c,d,i)){if(v.N==="slot"||h.N==="slot"){Wr(c.A.parentNode,false)}Vr(c,d,i);Kr(r,c.A,v.A);c=n[--l];d=t[++f]}else{u=-1;{for(o=a;o<=l;++o){if(n[o]&&n[o].k!==null&&n[o].k===d.k){u=o;break}}}if(u>=0){y=n[u];if(y.N!==d.N){p=Dr(n&&n[f],e,u)}else{Vr(y,d,i);n[u]=void 0;p=y.A}d=t[++f]}else{p=Dr(n&&n[f],e,f);d=t[++f]}if(p){{Kr(Gr(v.A).parentNode,p,Gr(v.A))}}}}if(a>l){Br(r,t[s+1]==null?null:t[s+1].A,e,t,f,s)}else if(f>s){Hr(n,a,l)}};var Ir=function(r,n,e){if(e===void 0){e=false}if(r.N===n.N){if(r.N==="slot"){return r.C===n.C}if(!e){return r.k===n.k}if(e&&!r.k&&n.k){r.k=n.k}return true}return false};var Gr=function(r){return r&&r["s-ol"]||r};var Vr=function(r,n,e){if(e===void 0){e=false}var t=n.A=r.A;var a=r.O;var f=n.O;var u=n.$;var o;if(u===null){{Ar(r,n,Fr)}if(a!==null&&f!==null){zr(t,a,n,f,e)}else if(f!==null){if(r.$!==null){t.textContent=""}Br(t,null,n,f,0,f.length-1)}else if(!e&&i.updatable&&a!==null){Hr(a,0,a.length-1)}}else if(o=t["s-cr"]){o.parentNode.textContent=u}else if(r.$!==u){t.data=u}};var Yr=[];var qr=function(r){var n;var e;var t;var i=r.__childNodes||r.childNodes;for(var a=0,f=i;a<f.length;a++){var u=f[a];if(u["s-sr"]&&(n=u["s-cr"])&&n.parentNode){e=n.parentNode.__childNodes||n.parentNode.childNodes;var o=u["s-sn"];var l=function(){n=e[t];if(!n["s-cn"]&&!n["s-nr"]&&n["s-hn"]!==u["s-hn"]&&true){if(q(n,o)){var r=Yr.find((function(r){return r.T===n}));Lr=true;n["s-sn"]=n["s-sn"]||o;if(r){r.T["s-sh"]=u["s-hn"];r.P=u}else{n["s-sh"]=u["s-hn"];Yr.push({P:u,T:n})}if(n["s-sr"]){Yr.map((function(e){if(q(e.T,n["s-sn"])){r=Yr.find((function(r){return r.T===n}));if(r&&!e.P){e.P=r.P}}}))}}else if(!Yr.some((function(r){return r.T===n}))){Yr.push({T:n})}}};for(t=e.length-1;t>=0;t--){l()}}if(u.nodeType===1){qr(u)}}};var Jr=function(r){{r.j&&r.j.ref&&r.j.ref(null);r.O&&r.O.map(Jr)}};var Kr=function(r,n,e){if(typeof n["s-sn"]==="string"&&!!n["s-sr"]&&!!n["s-cr"]){Qr(n["s-cr"],n,r,n.parentElement)}{return r==null?void 0:r.insertBefore(n,e)}};function Qr(r,n,e,t){var i,a;var f;if(r&&typeof n["s-sn"]==="string"&&!!n["s-sr"]&&r.parentNode&&r.parentNode["s-sc"]&&(f=n["s-si"]||r.parentNode["s-sc"])){var u=n["s-sn"];var o=n["s-hn"];(i=e.classList)==null?void 0:i.add(f+"-s");if(t&&((a=t.classList)==null?void 0:a.contains(f+"-s"))){var l=(t.__childNodes||t.childNodes)[0];var v=false;while(l){if(l["s-sn"]!==u&&l["s-hn"]===o&&!!l["s-sr"]){v=true;break}l=l.nextSibling}if(!v)t.classList.remove(f+"-s")}}}var Xr=function(r,n,e){if(e===void 0){e=false}var t,i,a,f;var u=r.$hostElement$;var o=r.u;var l=r.R||lr(null,null);var v=cr(n);var c=v?n:or(null,null,n);Pr=u.tagName;if(o.L){c.j=c.j||{};o.L.map((function(r){var n=r[0],e=r[1];return c.j[e]=u[n]}))}if(e&&c.j){for(var s=0,d=Object.keys(c.j);s<d.length;s++){var h=d[s];if(u.hasAttribute(h)&&!["key","ref","style","class"].includes(h)){c.j[h]=u[h]}}}c.N=null;c.i|=4;r.R=c;c.A=l.A=u.shadowRoot||u;{Mr=u["s-sc"]}Rr=!!(o.i&1)&&!(o.i&128);{Tr=u["s-cr"];Lr=false}Vr(l,c,e);{g.i|=1;if(Ur){qr(c.A);for(var p=0,y=Yr;p<y.length;p++){var m=y[p];var b=m.T;if(!b["s-ol"]&&_.document){var w=_.document.createTextNode("");w["s-nr"]=b;Kr(b.parentNode,b["s-ol"]=w,b)}}for(var S=0,$=Yr;S<$.length;S++){var m=$[S];var b=m.T;var j=m.P;if(j){var O=j.parentNode;var k=j.nextSibling;{var w=(t=b["s-ol"])==null?void 0:t.previousSibling;while(w){var C=(i=w["s-nr"])!=null?i:null;if(C&&C["s-sn"]===b["s-sn"]&&O===(C.__parentNode||C.parentNode)){C=C.nextSibling;while(C===b||(C==null?void 0:C["s-sr"])){C=C==null?void 0:C.nextSibling}if(!C||!C["s-nr"]){k=C;break}}w=w.previousSibling}}var N=b.__parentNode||b.parentNode;var E=b.__nextSibling||b.nextSibling;if(!k&&O!==N||E!==k){if(b!==k){if(!b["s-hn"]&&b["s-ol"]){b["s-hn"]=b["s-ol"].parentNode.nodeName}Kr(O,b,k);if(b.nodeType===1&&b.tagName!=="SLOT-FB"){b.hidden=(a=b["s-ih"])!=null?a:false}}}b&&typeof j["s-rf"]==="function"&&j["s-rf"](j)}else{if(b.nodeType===1){if(e){b["s-ih"]=(f=b.hidden)!=null?f:false}b.hidden=true}}}}if(Lr){I(c.A)}g.i&=-2;Yr.length=0}Tr=void 0};var Zr=function(r,n){if(n&&!r.U&&n["s-p"]){var e=n["s-p"].push(new Promise((function(t){return r.U=function(){n["s-p"].splice(e-1,1);t()}})))}};var rn=function(r,n){{r.i|=16}if(r.i&4){r.i|=512;return}Zr(r,r.F);var e=function(){return nn(r,n)};return M(e)};var nn=function(r,n){var e=r.$hostElement$;var t=fr("scheduleUpdate",r.u.m);var i=r.t;if(!i){throw new Error("Can't render component <".concat(e.tagName.toLowerCase()," /> with invalid Stencil runtime! Make sure this imported component is compiled with a `externalRuntime: true` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime"))}var a;if(n){{r.i|=256;if(r.D){r.D.map((function(r){var n=r[0],t=r[1];return ln(i,n,t,e)}));r.D=void 0}}a=ln(i,"componentWillLoad",void 0,e)}else{a=ln(i,"componentWillUpdate",void 0,e)}a=en(a,(function(){return ln(i,"componentWillRender",void 0,e)}));t();return en(a,(function(){return an(r,i,n)}))};var en=function(r,n){return tn(r)?r.then(n).catch((function(r){console.error(r);n()})):n()};var tn=function(r){return r instanceof Promise||r&&r.then&&typeof r.then==="function"};var an=function(r,n,t){return __awaiter(e,void 0,void 0,(function(){var e,i,a,f,u,o,l;return __generator(this,(function(v){i=r.$hostElement$;a=fr("update",r.u.m);f=i["s-rc"];if(t){$r(r)}u=fr("render",r.u.m);{fn(r,n,i,t)}if(f){f.map((function(r){return r()}));i["s-rc"]=void 0}u();a();{o=(e=i["s-p"])!=null?e:[];l=function(){return un(r)};if(o.length===0){l()}else{Promise.all(o).then(l);r.i|=4;o.length=0}}return[2]}))}))};var fn=function(r,n,e,t){try{n=n.render();{r.i&=-17}{r.i|=2}{{{Xr(r,n,t)}}}}catch(n){d(n,r.$hostElement$)}return null};var un=function(r){var n=r.u.m;var e=r.$hostElement$;var t=fr("postUpdate",n);var i=r.t;var a=r.F;ln(i,"componentDidRender",void 0,e);if(!(r.i&64)){r.i|=64;{vn(e)}ln(i,"componentDidLoad",void 0,e);t();{r.p(e);if(!a){on()}}}else{ln(i,"componentDidUpdate",void 0,e);t()}{r.v(e)}{if(r.U){r.U();r.U=void 0}if(r.i&512){x((function(){return rn(r,false)}))}r.i&=-517}};var on=function(r){x((function(){return wr(_,"appload",{detail:{namespace:t}})}))};var ln=function(r,n,e,t){if(r&&r[n]){try{return r[n](e)}catch(r){d(r,t)}}return void 0};var vn=function(r){var n;return r.classList.add((n=i.hydratedSelectorName)!=null?n:"hydrated")};var cn=function(r,n){return l(r).o.get(n)};var sn=function(r,n,e,t){var i=l(r);if(!i){throw new Error("Couldn't find host element for \"".concat(t.m,'" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).'))}var a=i.$hostElement$;var f=i.o.get(n);var u=i.i;var o=i.t;e=yr(e,t.W[n][0]);var v=Number.isNaN(f)&&Number.isNaN(e);var c=e!==f&&!v;if((!(u&8)||f===void 0)&&c){i.o.set(n,e);if(o){if(t.B&&u&128){var s=t.B[n];if(s){s.map((function(r){try{o[r](e,f,n)}catch(r){d(r,a)}}))}}if((u&(2|16))===2){if(o.componentShouldUpdate){if(o.componentShouldUpdate(e,f,n)===false){return}}rn(i,false)}}}};var dn=function(r,n,e){var t,a;var f=r.prototype;if(n.W||(n.B||r.watchers)){if(r.watchers&&!n.B){n.B=r.watchers}var u=Object.entries((t=n.W)!=null?t:{});u.map((function(r){var t=r[0],i=r[1][0];if(i&31||e&2&&i&32){var a=Object.getOwnPropertyDescriptor(f,t)||{},u=a.get,o=a.set;if(u)n.W[t][0]|=2048;if(o)n.W[t][0]|=4096;if(e&1||!u){Object.defineProperty(f,t,{get:function(){{if((n.W[t][0]&2048)===0){return cn(this,t)}var r=l(this);var e=r?r.t:f;if(!e)return;return e[t]}},configurable:true,enumerable:true})}Object.defineProperty(f,t,{set:function(r){var a=this;var f=l(this);if(o){var u=i&32?this[t]:f.$hostElement$[t];if(typeof u==="undefined"&&f.o.get(t)){r=f.o.get(t)}else if(!f.o.get(t)&&u){f.o.set(t,u)}o.apply(this,[yr(r,i)]);r=i&32?this[t]:f.$hostElement$[t];sn(this,t,r,n);return}{if((e&1)===0||(n.W[t][0]&4096)===0){sn(this,t,r,n);if(e&1&&!f.t){f.h.then((function(){if(n.W[t][0]&4096&&f.t[t]!==f.o.get(t)){f.t[t]=r}}))}return}var v=function(){var e=f.t[t];if(!f.o.get(t)&&e){f.o.set(t,e)}f.t[t]=yr(r,i);sn(a,t,f.t[t],n)};if(f.t){v()}else{f.h.then((function(){return v()}))}}}})}else if(e&1&&i&64){Object.defineProperty(f,t,{value:function(){var r=[];for(var n=0;n<arguments.length;n++){r[n]=arguments[n]}var e;var i=l(this);return(e=i==null?void 0:i.l)==null?void 0:e.then((function(){var n;return(n=i.t)==null?void 0:n[t].apply(n,r)}))}})}}));if(e&1){var o=new Map;f.attributeChangedCallback=function(r,e,t){var a=this;g.jmp((function(){var u;var v=o.get(r);if(a.hasOwnProperty(v)&&i.lazyLoad){t=a[v];delete a[v]}else if(f.hasOwnProperty(v)&&typeof a[v]==="number"&&a[v]==t){return}else if(v==null){var c=l(a);var s=c==null?void 0:c.i;if(s&&!(s&8)&&s&128&&t!==e){var d=c.t;var h=(u=n.B)==null?void 0:u[r];h==null?void 0:h.forEach((function(n){if(d[n]!=null){d[n].call(d,t,e,r)}}))}return}var p=Object.getOwnPropertyDescriptor(f,v);t=t===null&&typeof a[v]==="boolean"?false:t;if(t!==a[v]&&(!p.get||!!p.set)){a[v]=t}}))};r.observedAttributes=Array.from(new Set(__spreadArray(__spreadArray([],Object.keys((a=n.B)!=null?a:{}),true),u.filter((function(r){var n=r[0],e=r[1];return e[0]&15})).map((function(r){var e=r[0],t=r[1];var i;var a=t[1]||e;o.set(a,e);if(t[0]&512){(i=n.L)==null?void 0:i.push([e,a])}return a})),true)))}}return r};var hn=function(r,n,t,i){return __awaiter(e,void 0,void 0,(function(){var e,i,a,f,u,o,l,v,c,s,h;return __generator(this,(function(m){switch(m.label){case 0:if(!((n.i&32)===0))return[3,6];n.i|=32;i=t._;if(!i)return[3,4];a=p(t,n);if(!(a&&"then"in a))return[3,2];f=ur();return[4,a];case 1:e=m.sent();f();return[3,3];case 2:e=a;m.label=3;case 3:if(!e){throw new Error('Constructor for "'.concat(t.m,"#").concat(n.H,'" was not found'))}if(!e.isProxied){{t.B=e.watchers}dn(e,t,2);e.isProxied=true}u=fr("createInstance",t.m);{n.i|=8}try{new e(n)}catch(n){d(n,r)}{n.i&=-9}{n.i|=128}u();pn(n.t,r);return[3,5];case 4:e=r.constructor;o=r.localName;customElements.whenDefined(o).then((function(){return n.i|=128}));m.label=5;case 5:if(e&&e.style){l=void 0;if(typeof e.style==="string"){l=e.style}v=jr(t);if(!y.has(v)){c=fr("registerStyles",t.m);gr(v,l,!!(t.i&1));c()}}m.label=6;case 6:s=n.F;h=function(){return rn(n,true)};if(s&&s["s-rc"]){s["s-rc"].push(h)}else{h()}return[2]}}))}))};var pn=function(r,n){{ln(r,"connectedCallback",void 0,n)}};var yn=function(r){if((g.i&1)===0){var n=l(r);var e=n.u;var t=fr("connectedCallback",e.m);if(!(n.i&1)){n.i|=1;{if(e.i&(4|8)){mn(r)}}{var i=r;while(i=i.parentNode||i.host){if(i["s-p"]){Zr(n,n.F=i);break}}}if(e.W){Object.entries(e.W).map((function(n){var e=n[0],t=n[1][0];if(t&31&&r.hasOwnProperty(e)){var i=r[e];delete r[e];r[e]=i}}))}{hn(r,n,e)}}else{gn(r,n,e.I);if(n==null?void 0:n.t){pn(n.t,r)}else if(n==null?void 0:n.h){n.h.then((function(){return pn(n.t,r)}))}}t()}};var mn=function(r){if(!_.document){return}var n=r["s-cr"]=_.document.createComment("");n["s-cn"]=true;Kr(r,n,r.firstChild)};var bn=function(r,n){{ln(r,"disconnectedCallback",void 0,n||r)}};var wn=function(r){return __awaiter(e,void 0,void 0,(function(){var n;return __generator(this,(function(e){if((g.i&1)===0){n=l(r);{if(n.G){n.G.map((function(r){return r()}));n.G=void 0}}if(n==null?void 0:n.t){bn(n.t,r)}else if(n==null?void 0:n.h){n.h.then((function(){return bn(n.t,r)}))}}if(_r.has(r)){_r.delete(r)}if(r.shadowRoot&&_r.has(r.shadowRoot)){_r.delete(r.shadowRoot)}return[2]}))}))};var _n=r("b",(function(r,n){if(n===void 0){n={}}var e;if(!_.document){console.warn("Stencil: No document found. Skipping bootstrapping lazy components.");return}var t=fr();var i=[];var a=n.exclude||[];var f=_.customElements;var u=_.document.head;var o=u.querySelector("meta[charset]");var v=_.document.createElement("style");var s=[];var d;var h=true;Object.assign(g,n);g.S=new URL(n.resourcesUrl||"./",_.document.baseURI).href;var p=false;r.map((function(r){r[1].map((function(n){var e;var t={i:n[0],m:n[1],W:n[2],I:n[3]};if(t.i&4){p=true}{t.W=n[2]}{t.I=n[3]}{t.L=[]}{t.B=(e=n[4])!=null?e:{}}var u=t.m;var o=function(r){__extends(n,r);function n(n){var e=r.call(this,n)||this;e.hasRegisteredEventListeners=false;n=e;c(n,t);if(t.i&1){{if(!n.shadowRoot){z.call(n,t)}else{if(n.shadowRoot.mode!=="open"){throw new Error("Unable to re-use existing shadow root for ".concat(t.m,"! Mode is set to ").concat(n.shadowRoot.mode," but Stencil only supports open shadow roots."))}}}}return e}n.prototype.connectedCallback=function(){var r=this;var n=l(this);if(!this.hasRegisteredEventListeners){this.hasRegisteredEventListeners=true;gn(this,n,t.I)}if(d){clearTimeout(d);d=null}if(h){s.push(this)}else{g.jmp((function(){return yn(r)}))}};n.prototype.disconnectedCallback=function(){var r=this;g.jmp((function(){return wn(r)}));g.raf((function(){var n;var e=l(r);var t=s.findIndex((function(n){return n===r}));if(t>-1){s.splice(t,1)}if(((n=e==null?void 0:e.R)==null?void 0:n.A)instanceof Node&&!e.R.A.isConnected){delete e.R.A}}))};n.prototype.componentOnReady=function(){return l(this).h};return n}(HTMLElement);{{nr(o.prototype)}{rr(o.prototype)}}t._=r[0];if(!a.includes(u)&&!f.get(u)){i.push(u);f.define(u,dn(o,t,1))}}))}));if(i.length>0){if(p){v.textContent+=b}{v.textContent+=i.sort()+m}if(v.innerHTML.length){v.setAttribute("data-styles","");var y=(e=g.M)!=null?e:R(_.document);if(y!=null){v.setAttribute("nonce",y)}u.insertBefore(v,o?o.nextSibling:u.firstChild)}}h=false;if(s.length){s.map((function(r){return r.connectedCallback()}))}else{{g.jmp((function(){return d=setTimeout(on,30)}))}}t()}));var gn=function(r,n,e,t){if(e&&_.document){e.map((function(e){var t=e[0],i=e[1],a=e[2];var f=$n(_.document,r,t);var u=Sn(n,a);var o=jn(t);g.ael(f,i,u,o);(n.G=n.G||[]).push((function(){return g.rel(f,i,u,o)}))}))}};var Sn=function(r,n){return function(e){var t;try{{if(r.i&256){(t=r.t)==null?void 0:t[n](e)}else{(r.D=r.D||[]).push([n,e])}}}catch(n){d(n,r.$hostElement$)}}};var $n=function(r,n,e){if(e&8){return _}if(e&16){return r.body}return n};var jn=function(r){return S?{passive:(r&1)!==0,capture:(r&2)!==0}:(r&2)!==0};var On=r("s",(function(r){return g.M=r}))}}}));
//# sourceMappingURL=p-B47mPBRA.system.js.map