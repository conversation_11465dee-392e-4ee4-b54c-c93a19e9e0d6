var __awaiter=this&&this.__awaiter||function(t,o,r,e){function i(t){return t instanceof r?t:new r((function(o){o(t)}))}return new(r||(r=Promise))((function(r,n){function a(t){try{s(e.next(t))}catch(t){n(t)}}function c(t){try{s(e["throw"](t))}catch(t){n(t)}}function s(t){t.done?r(t.value):i(t.value).then(a,c)}s((e=e.apply(t,o||[])).next())}))};var __generator=this&&this.__generator||function(t,o){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},e,i,n,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(t){return function(o){return s([t,o])}}function s(c){if(e)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(r=0)),r)try{if(e=1,i&&(n=c[0]&2?i["return"]:c[0]?i["throw"]||((n=i["return"])&&n.call(i),0):i.next)&&!(n=n.call(i,c[1])).done)return n;if(i=0,n)c=[c[0]&2,n.value];switch(c[0]){case 0:case 1:n=c;break;case 4:r.label++;return{value:c[1],done:false};case 5:r.label++;i=c[1];c=[0];continue;case 7:c=r.ops.pop();r.trys.pop();continue;default:if(!(n=r.trys,n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){r.label=c[1];break}if(c[0]===6&&r.label<n[1]){r.label=n[1];n=c;break}if(n&&r.label<n[2]){r.label=n[2];r.ops.push(c);break}if(n[2])r.ops.pop();r.trys.pop();continue}c=o.call(t,r)}catch(t){c=[6,t];i=0}finally{e=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var o,r,e;return{setters:[function(t){o=t.r;r=t.h;e=t.a}],execute:function(){var i='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';var n=t("bds_accordion_header",function(){function t(t){var r=this;o(this,t);this.accordionElement=null;this.isOpen=false;this.btToggleIsfocus=false;this.numberElement=null;this.accordionTitle=null;this.icon=null;this.avatarName=null;this.avatarThumb=null;this.dataTest=null;this.toggleHeader=function(){var t,o;if(r.isOpen){(t=r.accordionElement)===null||t===void 0?void 0:t.close()}else{(o=r.accordionElement)===null||o===void 0?void 0:o.open()}}}t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=!this.isOpen;return[2]}))}))};t.prototype.open=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=true;return[2]}))}))};t.prototype.close=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=false;return[2]}))}))};t.prototype.componentWillRender=function(){this.accordionElement=this.element.parentElement};t.prototype.handleKeyDown=function(t){var o,r;if(t.key=="Enter"){if(this.isOpen){(o=this.accordionElement)===null||o===void 0?void 0:o.close()}else{(r=this.accordionElement)===null||r===void 0?void 0:r.open()}}};t.prototype.render=function(){return r("div",{key:"187061591a0438fad6432f12708fb0b1d6ae2f2a",onClick:this.toggleHeader,class:{accordion_header:true},"data-test":this.dataTest},this.avatarName||this.avatarThumb?r("bds-avatar",{name:this.avatarName,thumbnail:this.avatarThumb,size:"extra-small"}):this.icon&&r("bds-icon",{size:"x-large",name:this.icon,color:"inherit"}),this.accordionTitle&&r("bds-typo",{key:"7bdadadb86dc1cc7e5aa85a28e9e6b70b39eed24",bold:"bold",variant:"fs-16","line-height":"double"},this.accordionTitle),r("slot",{key:"94a857b65dbdc8ebe1621e4436d8c0414444fd95"}),r("bds-icon",{key:"9733311a10ebe3849f53cb13614d52dc56b2b7ab",class:{accButton:true,accButton__isopen:this.isOpen,accButton__isfocus:this.btToggleIsfocus},size:"x-large",name:"arrow-down",color:"inherit",tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)}))};Object.defineProperty(t.prototype,"element",{get:function(){return e(this)},enumerable:false,configurable:true});return t}());n.style=i}}}));
//# sourceMappingURL=p-57aed733.system.entry.js.map