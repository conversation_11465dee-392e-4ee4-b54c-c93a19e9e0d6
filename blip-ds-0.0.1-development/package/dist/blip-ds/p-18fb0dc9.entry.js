import{r as t,c as e,h as i,H as s,a as o}from"./p-C3J6Z5OX.js";const r=':host{display:block;width:100%}.tab_group{width:100%;height:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative}.tab_group__header{padding:4px 16px;overflow:hidden}.tab_group__header__itens{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;width:-webkit-max-content;width:-moz-max-content;width:max-content;gap:32px;margin:auto}.tab_group__header__itens__center{-ms-flex-pack:center;justify-content:center;margin:auto}.tab_group__header__itens__right{-ms-flex-pack:right;justify-content:right;margin:0 0 0 auto}.tab_group__header__itens__left{-ms-flex-pack:left;justify-content:left;margin:0 auto 0 0}.tab_group__header__itens__item{cursor:pointer;height:46px;gap:4px;width:auto;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;border-bottom:2px solid transparent;position:relative}.tab_group__header__itens__item__typo{color:var(--color-content-disable, rgb(89, 89, 89))}.tab_group__header__itens__item__typo__disable{color:var(--color-content-ghost, rgb(140, 140, 140))}.tab_group__header__itens__item__typo__error{color:var(--color-surface-negative, rgb(138, 0, 0))}.tab_group__header__itens__item::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.tab_group__header__itens__item:focus-visible{outline:none}.tab_group__header__itens__item:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.tab_group__header__itens__item__open{color:var(--color-content-default, rgb(40, 40, 40));border-color:var(--color-primary, rgb(30, 107, 241))}.tab_group__header__itens__item__disable{cursor:no-drop}.tab_group__slide{position:relative;overflow:hidden;padding:0 16px;height:54px;margin-left:56px;margin-right:56px}.tab_group__slide-button{position:absolute;z-index:1;background-color:var(--color-surface-1, rgb(246, 246, 246))}.tab_group__slide-button[icon=arrow-left]{left:0}.tab_group__slide-button[icon=arrow-right]{right:0}.tab_group__slide__itens{position:absolute;left:56px;width:-webkit-max-content;width:-moz-max-content;width:max-content;height:48px;display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:center;justify-content:center;padding:4px;gap:32px;-webkit-transition:left 0.5s;-moz-transition:left 0.5s;transition:left 0.5s}.tab_group__content{height:100%}.tab_group__scrolled{-ms-flex-negative:999;flex-shrink:999;overflow:none}.tab_group__scrolled::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.tab_group__scrolled::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}';const n=class{constructor(s){t(this,s);this.bdsTabChange=e(this,"bdsTabChange");this.bdsTabDisabled=e(this,"bdsTabDisabled");this.tabItensElement=null;this.tabItensSlideElement=null;this.isSlideTabs=false;this.alignTab="left";this.tabRefSlide=0;this.positionLeft=0;this.contentScrollable=true;this.align="center";this.dtButtonPrev=null;this.dtButtonNext=null;this.getEventsDisable=t=>{t.forEach((t=>{t.addEventListener("tabDisabled",(()=>{this.setInternalItens(Array.from(this.tabItensElement))}),false)}))};this.checkSlideTabs=()=>{var t,e;if(this.headerElement||this.headerSlideElement){if(((t=this.headerSlideElement)===null||t===void 0?void 0:t.offsetWidth)>((e=this.headerElement)===null||e===void 0?void 0:e.offsetWidth)){return true}}};this.setFirstActive=()=>{const t=Array.from(this.tabItensElement).filter((t=>t.open));if(!t.length){this.tabItensElement[0].open=true}};this.setnumberElement=()=>{for(let t=0;t<this.tabItensElement.length;t++){this.tabItensElement[t].numberElement=t}};this.setInternalItens=t=>{const e=t.map(((t,e)=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({label:t.label,open:t.open,numberElement:e,badge:t.badge},t.disable!==undefined&&{disable:t.disable}),t.error!==undefined&&{error:t.error}),t.headerStyle!==undefined&&{headerStyle:t.headerStyle}),t.contentStyle!==undefined&&{contentStyle:t.contentStyle}),t.icon!==undefined&&{icon:t.icon}),t.iconPosition!==undefined&&{iconPosition:t.iconPosition}),t.iconTheme!==undefined&&{iconTheme:t.iconTheme}),t.badgeShape!==undefined&&{badgeShape:t.badgeShape}),t.badgeColor!==undefined&&{badgeColor:t.badgeColor}),t.badgeIcon!==undefined&&{badgeIcon:t.badgeIcon}),t.badgeAnimation!==undefined&&{badgeAnimation:t.badgeAnimation}),t.badgeNumber!==undefined&&{badgeNumber:t.badgeNumber}),t.badgePosition!==undefined&&{badgePosition:t.badgePosition}),t.dataTest!==undefined&&{dataTest:t.dataTest})));return this.internalItens=e};this.handleClick=t=>{const e=this.internalItens.map((t=>({label:t.label,open:false,numberElement:t.numberElement})));this.internalItens=e;for(let e=0;e<this.tabItensElement.length;e++){if(this.tabItensElement[e].numberElement!=t){this.tabItensElement[e].open=false}else{this.tabItensElement[e].open=true;this.bdsTabChange.emit(this.tabItensElement[e])}}};this.refHeaderElement=t=>{this.headerElement=t};this.refHeaderSlideElement=t=>{this.headerSlideElement=t};this.handleDisabled=t=>{this.bdsTabDisabled.emit(this.tabItensElement[t])};this.nextSlide=()=>{var t,e,i,s,o;const r=((t=this.headerElement)===null||t===void 0?void 0:t.offsetWidth)-((e=this.headerSlideElement)===null||e===void 0?void 0:e.offsetWidth);const n=((i=this.headerSlideElement)===null||i===void 0?void 0:i.offsetWidth)/((s=this.headerElement)===null||s===void 0?void 0:s.offsetWidth);const a=parseInt(n.toString());const _=this.positionLeft-((o=this.headerElement)===null||o===void 0?void 0:o.offsetWidth);this.positionLeft=_<r?r:_;this.alignTab=_<r?"right":"scrolling";this.tabRefSlide=a<=this.tabRefSlide?this.tabRefSlide+1:a};this.prevSlide=()=>{var t,e,i;const s=((t=this.headerSlideElement)===null||t===void 0?void 0:t.offsetWidth)/((e=this.headerElement)===null||e===void 0?void 0:e.offsetWidth);const o=parseInt(s.toString());const r=this.positionLeft+((i=this.headerElement)===null||i===void 0?void 0:i.offsetWidth);this.positionLeft=r>0?0:r;this.alignTab=r>0?"left":"scrolling";this.tabRefSlide=o<=this.tabRefSlide?this.tabRefSlide-1:o};this.renderIcon=(t,e,s,o)=>i("bds-icon",{class:{tab_group__header__itens__item__typo__disable:s,tab_group__header__itens__item__typo__error:o},size:"x-small",name:t,theme:e});this.renderBadge=(t,e,s,o,r)=>i("bds-grid",{"justify-content":"center"},i("bds-badge",{color:e,icon:s,number:r,shape:t,animation:o}))}componentWillRender(){this.tabItensElement=this.element.getElementsByTagName("bds-tab-item");this.setnumberElement();this.setFirstActive();this.setInternalItens(Array.from(this.tabItensElement));this.getEventsDisable(Array.from(this.tabItensElement))}componentDidLoad(){this.tabItensSlideElement=this.element.shadowRoot.querySelectorAll(".tab_group__header__itens__item")}connectedCallback(){this.isSlide=window.setInterval((()=>{this.isSlideTabs=this.checkSlideTabs()}),100)}disconnectedCallback(){window.clearInterval(this.isSlide)}handleKeyDown(t,e){if(t.key=="Enter"){e.disable?this.handleDisabled(e.numberElement):this.handleClick(e.numberElement)}if(t.key=="ArrowRight"){this.tabItensSlideElement[e.numberElement+1].focus()}if(t.key=="ArrowLeft"){this.tabItensSlideElement[e.numberElement-1].focus()}}parseInlineStyle(t){if(!t)return{};return t.split(";").filter((t=>t.trim())).reduce(((t,e)=>{const[i,s]=e.split(":").map((t=>t.trim()));if(i&&s){const e=i.replace(/-([a-z])/g,(t=>t[1].toUpperCase()));t[e]=s}return t}),{})}render(){var t;const e={left:`${this.positionLeft}px`};const o=(t=this.internalItens)===null||t===void 0?void 0:t.find((t=>t.open));const r=(o===null||o===void 0?void 0:o.headerStyle)?this.parseInlineStyle(o.headerStyle):{};const n=(o===null||o===void 0?void 0:o.contentStyle)?this.parseInlineStyle(o.contentStyle):{};return i(s,{key:"413f1ecafde31333a22d2ba54a35194170453230"},i("div",{key:"d2a8ebe711dc8ee0e2b5a8f65044689089ecfafb",class:{tab_group:true}},this.isSlideTabs&&this.alignTab!="left"&&i("bds-button-icon",{key:"5ef00fd2515df1d10565d7c5029472a762a73fc3",class:"tab_group__slide-button",icon:"arrow-left",size:"short",id:"bds-tabs-button-left",onClick:()=>this.prevSlide(),dataTest:this.dtButtonPrev,variant:"secondary"}),i("div",{key:"01d649f752b869ed63f5132eb832622b7b5bea8c",class:{tab_group__header:true,tab_group__slide:this.isSlideTabs},ref:this.refHeaderElement,style:r},i("div",{key:"8afbfb17c968ffd80a9be0a376e6319e0f774e08",class:{tab_group__header__itens:true,tab_group__slide__itens:this.isSlideTabs,[`tab_group__header__itens__${this.align}`]:!this.isSlideTabs},ref:this.refHeaderSlideElement,style:e},this.internalItens&&this.internalItens.map(((t,e)=>{const s=t.open==true?"bold":"regular";return i("div",{class:{tab_group__header__itens__item:true,tab_group__header__itens__item__open:t.open,tab_group__header__itens__item__disable:t.disable},key:e,tabindex:"0",onClick:()=>t.disable?this.handleDisabled(t.numberElement):this.handleClick(t.numberElement),onKeyDown:e=>this.handleKeyDown(e,t)},t.iconPosition==="left"&&t.icon?this.renderIcon(t.icon,t.iconTheme,t.disable,t.error):"",t.badgePosition==="left"&&t.badge?this.renderBadge(t.badgeShape,t.badgeColor,t.badgeIcon,t.badgeAnimation,t.badgeNumber):"",i("bds-typo",{class:{tab_group__header__itens__item__typo__disable:t.disable,tab_group__header__itens__item__typo__error:t.error},variant:"fs-16",bold:s},t.label),t.iconPosition==="right"&&t.icon?this.renderIcon(t.icon,t.iconTheme,t.disable,t.error):"",t.badgePosition==="right"&&t.badge?this.renderBadge(t.badgeShape,t.badgeColor,t.badgeIcon,t.badgeAnimation,t.badgeNumber):"")})))),this.isSlideTabs&&this.alignTab!="right"&&i("bds-button-icon",{key:"19944db7058ee43b5d3b215e946a5fbb976ec608",class:"tab_group__slide-button",icon:"arrow-right",size:"short",id:"bds-tabs-button-right",onClick:()=>this.nextSlide(),dataTest:this.dtButtonNext,variant:"secondary"}),i("div",{key:"2810b14cb861ce76027c4b60e42bd50dca8beb0b",class:{tab_group__content:true,tab_group__scrolled:this.contentScrollable},style:n},i("slot",{key:"e42d74ff44c8a0a37aa76195e3a8c3fa8d56870b"}))))}get element(){return o(this)}};n.style=r;export{n as bds_tab_group};
//# sourceMappingURL=p-18fb0dc9.entry.js.map