var __awaiter=this&&this.__awaiter||function(t,e,n,i){function s(t){return t instanceof n?t:new n((function(e){e(t)}))}return new(n||(n=Promise))((function(n,r){function a(t){try{c(i.next(t))}catch(t){r(t)}}function u(t){try{c(i["throw"](t))}catch(t){r(t)}}function c(t){t.done?n(t.value):s(t.value).then(a,u)}c((i=i.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var n={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},i,s,r,a;return a={next:u(0),throw:u(1),return:u(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function u(t){return function(e){return c([t,e])}}function c(u){if(i)throw new TypeError("Generator is already executing.");while(a&&(a=0,u[0]&&(n=0)),n)try{if(i=1,s&&(r=u[0]&2?s["return"]:u[0]?s["throw"]||((r=s["return"])&&r.call(s),0):s.next)&&!(r=r.call(s,u[1])).done)return r;if(s=0,r)u=[u[0]&2,r.value];switch(u[0]){case 0:case 1:r=u;break;case 4:n.label++;return{value:u[1],done:false};case 5:n.label++;s=u[1];u=[0];continue;case 7:u=n.ops.pop();n.trys.pop();continue;default:if(!(r=n.trys,r=r.length>0&&r[r.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!r||u[1]>r[0]&&u[1]<r[3])){n.label=u[1];break}if(u[0]===6&&n.label<r[1]){n.label=r[1];r=u;break}if(r&&n.label<r[2]){n.label=r[2];n.ops.push(u);break}if(r[2])n.ops.pop();n.trys.pop();continue}u=e.call(t,n)}catch(t){u=[6,t];s=0}finally{i=r=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,n,i,s;return{setters:[function(t){e=t.r;n=t.c;i=t.h;s=t.H}],execute:function(){var r=":host{display:none}:host(.is-open){display:block;height:100%}.tab_item{height:100%}.tab_item_content{display:none;height:100%}.tab_item_content--open{display:block}";var a=t("bds_tab_item",function(){function t(t){e(this,t);this.tabDisabled=n(this,"tabDisabled");this.numberElement=null;this.label=null;this.icon=null;this.iconPosition="left";this.iconTheme="outline";this.badge=false;this.badgeShape="circle";this.badgeColor="system";this.badgeIcon=null;this.badgeAnimation=false;this.badgePosition="left";this.badgeNumber=null;this.disable=false;this.error=false;this.headerStyle=null;this.contentStyle=null;this.open=false;this.dataTest=null}t.prototype.reciveNumber=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.numberElement=t;return[2]}))}))};t.prototype.disableChanged=function(){this.tabDisabled.emit({item:this.numberElement,disable:this.disable})};t.prototype.render=function(){var t,e;return i(s,{key:"aed24d3a7e507e47baf6ab2ee66b93bf7780ddf1",class:(t={},t["is-open"]=this.disable===true?false:this.open,t)},i("div",{key:"8f66f58d7d3537c1748ba8318831ac65d56946d5",class:{tab_item:true},"data-test":this.dataTest},i("div",{key:"a9bbc47a9c74f0784a52f7def97f3b4b7f1a4768",class:(e={tab_item_content:true},e["tab_item_content--open"]=this.open,e)},i("slot",{key:"c6385c83ab5dbcb7a90868224b3b1584055c7eee"}))))};Object.defineProperty(t,"watchers",{get:function(){return{disable:["disableChanged"]}},enumerable:false,configurable:true});return t}());a.style=r}}}));
//# sourceMappingURL=p-c36b2f5a.system.entry.js.map