var __awaiter=this&&this.__awaiter||function(t,o,r,n){function e(t){return t instanceof r?t:new r((function(o){o(t)}))}return new(r||(r=Promise))((function(r,i){function a(t){try{_(n.next(t))}catch(t){i(t)}}function b(t){try{_(n["throw"](t))}catch(t){i(t)}}function _(t){t.done?r(t.value):e(t.value).then(a,b)}_((n=n.apply(t,o||[])).next())}))};var __generator=this&&this.__generator||function(t,o){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,e,i,a;return a={next:b(0),throw:b(1),return:b(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function b(t){return function(o){return _([t,o])}}function _(b){if(n)throw new TypeError("Generator is already executing.");while(a&&(a=0,b[0]&&(r=0)),r)try{if(n=1,e&&(i=b[0]&2?e["return"]:b[0]?e["throw"]||((i=e["return"])&&i.call(e),0):e.next)&&!(i=i.call(e,b[1])).done)return i;if(e=0,i)b=[b[0]&2,i.value];switch(b[0]){case 0:case 1:i=b;break;case 4:r.label++;return{value:b[1],done:false};case 5:r.label++;e=b[1];b=[0];continue;case 7:b=r.ops.pop();r.trys.pop();continue;default:if(!(i=r.trys,i=i.length>0&&i[i.length-1])&&(b[0]===6||b[0]===2)){r=0;continue}if(b[0]===3&&(!i||b[1]>i[0]&&b[1]<i[3])){r.label=b[1];break}if(b[0]===6&&r.label<i[1]){r.label=i[1];i=b;break}if(i&&r.label<i[2]){r.label=i[2];r.ops.push(b);break}if(i[2])r.ops.pop();r.trys.pop();continue}b=o.call(t,r)}catch(t){b=[6,t];e=0}finally{n=i=0}if(b[0]&5)throw b[1];return{value:b[0]?b[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var o,r,n,e,i;return{setters:[function(t){o=t.r;r=t.c;n=t.h;e=t.H;i=t.a}],execute:function(){var a=':host{height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:relative;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}:host(.block){width:100%;display:-ms-flexbox;display:flex}:host(.group){width:auto}:host:focus-visible{outline:none}.button{border:none;margin:0;padding:0;width:auto;overflow:visible;outline:none;background:transparent;color:inherit;font:inherit;line-height:normal;-webkit-font-smoothing:inherit;-moz-osx-font-smoothing:inherit;-webkit-appearance:none;cursor:pointer;display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;border-radius:8px;border-style:solid;border-left-width:1px;border-top-width:1px;border-right-width:1px;border-bottom-width:1px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;gap:4px;padding:0 16px}.button::-moz-focus-inner{border:0;padding:0}.button__size--short{height:32px}.button__size--standard{height:40px}.button__size--medium{height:40px}.button__size--large{height:56px}.button__only-icon--medium{padding:8px;gap:0}.button__only-icon--large{padding:8px 16px;gap:0}.button__only-icon--short{padding:0px;width:32px;gap:0}.button--block,.button--group{width:100%}.button--full-width{width:100%}.button__justify-content--center{-ms-flex-pack:center;justify-content:center}.button__justify-content--space-between{-ms-flex-pack:justify;justify-content:space-between}.button__group-content{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;gap:4px}.button bds-loading-spinner{max-height:100%;position:absolute}.button *{pointer-events:none}.button__color--primary.button__variant--solid,.button__color--primary.button__variant--primary{background-color:var(--color-surface-primary, rgb(30, 107, 241));border-color:transparent}.button__color--primary.button__variant--solid .typo_buttom,.button__color--primary.button__variant--solid .icon_buttom,.button__color--primary.button__variant--primary .typo_buttom,.button__color--primary.button__variant--primary .icon_buttom{color:var(--color-content-bright, rgb(255, 255, 255));z-index:1}.button__color--primary.button__variant--solid--disabled,.button__color--primary.button__variant--primary--disabled{opacity:50%;pointer-events:none}.button__color--primary.button__variant--solid:hover::before,.button__color--primary.button__variant--primary:hover::before{background-color:var(--color-surface-primary, rgb(30, 107, 241));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;-webkit-filter:brightness(0.88);filter:brightness(0.88)}.button__color--primary.button__variant--solid:active::before,.button__color--primary.button__variant--solid--active::before,.button__color--primary.button__variant--primary:active::before,.button__color--primary.button__variant--primary--active::before{-webkit-filter:brightness(0.76);filter:brightness(0.76)}.button__color--primary.button__variant--outline,.button__color--primary.button__variant--tertiary{background-color:transparent;border-color:var(--color-primary, rgb(30, 107, 241))}.button__color--primary.button__variant--outline .typo_buttom,.button__color--primary.button__variant--outline .icon_buttom,.button__color--primary.button__variant--tertiary .typo_buttom,.button__color--primary.button__variant--tertiary .icon_buttom{color:var(--color-primary, rgb(30, 107, 241));z-index:1}.button__color--primary.button__variant--outline--disabled,.button__color--primary.button__variant--tertiary--disabled{opacity:50%;pointer-events:none}.button__color--primary.button__variant--outline:hover::before,.button__color--primary.button__variant--tertiary:hover::before{background-color:var(--color-surface-primary, rgb(30, 107, 241));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--primary.button__variant--outline:active::before,.button__color--primary.button__variant--outline--active::before,.button__color--primary.button__variant--tertiary:active::before,.button__color--primary.button__variant--tertiary--active::before{opacity:0.24}.button__color--primary.button__variant--text,.button__color--primary.button__variant--secondary{background-color:transparent;border-color:transparent}.button__color--primary.button__variant--text .typo_buttom,.button__color--primary.button__variant--text .icon_buttom,.button__color--primary.button__variant--secondary .typo_buttom,.button__color--primary.button__variant--secondary .icon_buttom{color:var(--color-primary, rgb(30, 107, 241));z-index:1}.button__color--primary.button__variant--text--disabled,.button__color--primary.button__variant--secondary--disabled{opacity:50%;pointer-events:none}.button__color--primary.button__variant--text:hover::before,.button__color--primary.button__variant--secondary:hover::before{background-color:var(--color-primary, rgb(30, 107, 241));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--primary.button__variant--text:active::before,.button__color--primary.button__variant--text--active::before,.button__color--primary.button__variant--secondary:active::before,.button__color--primary.button__variant--secondary--active::before{opacity:0.24}.button__color--content.button__variant--solid,.button__color--content.button__variant--primary{background-color:var(--color-content-default, rgb(40, 40, 40));border-color:transparent}.button__color--content.button__variant--solid .typo_buttom,.button__color--content.button__variant--solid .icon_buttom,.button__color--content.button__variant--primary .typo_buttom,.button__color--content.button__variant--primary .icon_buttom{color:var(--color-surface-0, rgb(255, 255, 255));z-index:1}.button__color--content.button__variant--solid--disabled,.button__color--content.button__variant--primary--disabled{opacity:50%;pointer-events:none}.button__color--content.button__variant--solid:hover::before,.button__color--content.button__variant--primary:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;-webkit-filter:brightness(0.88);filter:brightness(0.88)}.button__color--content.button__variant--solid:active::before,.button__color--content.button__variant--solid--active::before,.button__color--content.button__variant--primary:active::before,.button__color--content.button__variant--primary--active::before{-webkit-filter:brightness(0.76);filter:brightness(0.76)}.button__color--content.button__variant--outline,.button__color--content.button__variant--tertiary{background-color:transparent;border-color:var(--color-content-default, rgb(40, 40, 40))}.button__color--content.button__variant--outline .typo_buttom,.button__color--content.button__variant--outline .icon_buttom,.button__color--content.button__variant--tertiary .typo_buttom,.button__color--content.button__variant--tertiary .icon_buttom{color:var(--color-content-default, rgb(40, 40, 40));z-index:1}.button__color--content.button__variant--outline--disabled,.button__color--content.button__variant--tertiary--disabled{opacity:50%;pointer-events:none}.button__color--content.button__variant--outline:hover::before,.button__color--content.button__variant--tertiary:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--content.button__variant--outline:active::before,.button__color--content.button__variant--outline--active::before,.button__color--content.button__variant--tertiary:active::before,.button__color--content.button__variant--tertiary--active::before{opacity:0.24}.button__color--content.button__variant--text,.button__color--content.button__variant--secondary{background-color:transparent;border-color:transparent}.button__color--content.button__variant--text .typo_buttom,.button__color--content.button__variant--text .icon_buttom,.button__color--content.button__variant--secondary .typo_buttom,.button__color--content.button__variant--secondary .icon_buttom{color:var(--color-content-default, rgb(40, 40, 40));z-index:1}.button__color--content.button__variant--text--disabled,.button__color--content.button__variant--secondary--disabled{opacity:50%;pointer-events:none}.button__color--content.button__variant--text:hover::before,.button__color--content.button__variant--secondary:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--content.button__variant--text:active::before,.button__color--content.button__variant--text--active::before,.button__color--content.button__variant--secondary:active::before,.button__color--content.button__variant--secondary--active::before{opacity:0.24}.button__color--negative.button__variant--solid,.button__color--negative.button__variant--primary,.button__color--negative.button__variant--delete{background-color:var(--color-surface-negative, rgb(138, 0, 0));border-color:transparent}.button__color--negative.button__variant--solid .typo_buttom,.button__color--negative.button__variant--solid .icon_buttom,.button__color--negative.button__variant--primary .typo_buttom,.button__color--negative.button__variant--primary .icon_buttom,.button__color--negative.button__variant--delete .typo_buttom,.button__color--negative.button__variant--delete .icon_buttom{color:var(--color-content-bright, rgb(255, 255, 255));z-index:1}.button__color--negative.button__variant--solid--disabled,.button__color--negative.button__variant--primary--disabled,.button__color--negative.button__variant--delete--disabled{opacity:50%;pointer-events:none}.button__color--negative.button__variant--solid:hover::before,.button__color--negative.button__variant--primary:hover::before,.button__color--negative.button__variant--delete:hover::before{background-color:var(--color-surface-negative, rgb(138, 0, 0));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;-webkit-filter:brightness(0.88);filter:brightness(0.88)}.button__color--negative.button__variant--solid:active::before,.button__color--negative.button__variant--solid--active::before,.button__color--negative.button__variant--primary:active::before,.button__color--negative.button__variant--primary--active::before,.button__color--negative.button__variant--delete:active::before,.button__color--negative.button__variant--delete--active::before{-webkit-filter:brightness(0.76);filter:brightness(0.76)}.button__color--negative.button__variant--outline,.button__color--negative.button__variant--tertiary,.button__color--negative.button__variant--delete{background-color:transparent;border-color:var(--color-negative, #e60f0f)}.button__color--negative.button__variant--outline .typo_buttom,.button__color--negative.button__variant--outline .icon_buttom,.button__color--negative.button__variant--tertiary .typo_buttom,.button__color--negative.button__variant--tertiary .icon_buttom,.button__color--negative.button__variant--delete .typo_buttom,.button__color--negative.button__variant--delete .icon_buttom{color:var(--color-negative, #e60f0f);z-index:1}.button__color--negative.button__variant--outline--disabled,.button__color--negative.button__variant--tertiary--disabled,.button__color--negative.button__variant--delete--disabled{opacity:50%;pointer-events:none}.button__color--negative.button__variant--outline:hover::before,.button__color--negative.button__variant--tertiary:hover::before,.button__color--negative.button__variant--delete:hover::before{background-color:var(--color-surface-negative, rgb(138, 0, 0));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--negative.button__variant--outline:active::before,.button__color--negative.button__variant--outline--active::before,.button__color--negative.button__variant--tertiary:active::before,.button__color--negative.button__variant--tertiary--active::before,.button__color--negative.button__variant--delete:active::before,.button__color--negative.button__variant--delete--active::before{opacity:0.24}.button__color--negative.button__variant--text,.button__color--negative.button__variant--secondary,.button__color--negative.button__variant--delete{background-color:transparent;border-color:transparent}.button__color--negative.button__variant--text .typo_buttom,.button__color--negative.button__variant--text .icon_buttom,.button__color--negative.button__variant--secondary .typo_buttom,.button__color--negative.button__variant--secondary .icon_buttom,.button__color--negative.button__variant--delete .typo_buttom,.button__color--negative.button__variant--delete .icon_buttom{color:var(--color-negative, #e60f0f);z-index:1}.button__color--negative.button__variant--text--disabled,.button__color--negative.button__variant--secondary--disabled,.button__color--negative.button__variant--delete--disabled{opacity:50%;pointer-events:none}.button__color--negative.button__variant--text:hover::before,.button__color--negative.button__variant--secondary:hover::before,.button__color--negative.button__variant--delete:hover::before{background-color:var(--color-negative, #e60f0f);-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--negative.button__variant--text:active::before,.button__color--negative.button__variant--text--active::before,.button__color--negative.button__variant--secondary:active::before,.button__color--negative.button__variant--secondary--active::before,.button__color--negative.button__variant--delete:active::before,.button__color--negative.button__variant--delete--active::before{opacity:0.24}.button__color--positive.button__variant--solid,.button__color--positive.button__variant--primary{background-color:var(--color-surface-positive, rgb(1, 114, 62));border-color:transparent}.button__color--positive.button__variant--solid .typo_buttom,.button__color--positive.button__variant--solid .icon_buttom,.button__color--positive.button__variant--primary .typo_buttom,.button__color--positive.button__variant--primary .icon_buttom{color:var(--color-content-bright, rgb(255, 255, 255));z-index:1}.button__color--positive.button__variant--solid--disabled,.button__color--positive.button__variant--primary--disabled{opacity:50%;pointer-events:none}.button__color--positive.button__variant--solid:hover::before,.button__color--positive.button__variant--primary:hover::before{background-color:var(--color-surface-positive, rgb(1, 114, 62));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;-webkit-filter:brightness(0.88);filter:brightness(0.88)}.button__color--positive.button__variant--solid:active::before,.button__color--positive.button__variant--solid--active::before,.button__color--positive.button__variant--primary:active::before,.button__color--positive.button__variant--primary--active::before{-webkit-filter:brightness(0.76);filter:brightness(0.76)}.button__color--positive.button__variant--outline,.button__color--positive.button__variant--tertiary{background-color:transparent;border-color:var(--color-positive, #10603b)}.button__color--positive.button__variant--outline .typo_buttom,.button__color--positive.button__variant--outline .icon_buttom,.button__color--positive.button__variant--tertiary .typo_buttom,.button__color--positive.button__variant--tertiary .icon_buttom{color:var(--color-positive, #10603b);z-index:1}.button__color--positive.button__variant--outline--disabled,.button__color--positive.button__variant--tertiary--disabled{opacity:50%;pointer-events:none}.button__color--positive.button__variant--outline:hover::before,.button__color--positive.button__variant--tertiary:hover::before{background-color:var(--color-surface-positive, rgb(1, 114, 62));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--positive.button__variant--outline:active::before,.button__color--positive.button__variant--outline--active::before,.button__color--positive.button__variant--tertiary:active::before,.button__color--positive.button__variant--tertiary--active::before{opacity:0.24}.button__color--positive.button__variant--text,.button__color--positive.button__variant--secondary{background-color:transparent;border-color:transparent}.button__color--positive.button__variant--text .typo_buttom,.button__color--positive.button__variant--text .icon_buttom,.button__color--positive.button__variant--secondary .typo_buttom,.button__color--positive.button__variant--secondary .icon_buttom{color:var(--color-positive, #10603b);z-index:1}.button__color--positive.button__variant--text--disabled,.button__color--positive.button__variant--secondary--disabled{opacity:50%;pointer-events:none}.button__color--positive.button__variant--text:hover::before,.button__color--positive.button__variant--secondary:hover::before{background-color:var(--color-positive, #10603b);-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button__color--positive.button__variant--text:active::before,.button__color--positive.button__variant--text--active::before,.button__color--positive.button__variant--secondary:active::before,.button__color--positive.button__variant--secondary--active::before{opacity:0.24}.button.button__variant--secondary{background-color:transparent;border-color:transparent}.button.button__variant--secondary .typo_buttom,.button.button__variant--secondary .icon_buttom{color:var(--color-content-default, rgb(40, 40, 40));z-index:1}.button.button__variant--secondary--disabled{opacity:50%;pointer-events:none}.button.button__variant--secondary:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button.button__variant--secondary:active::before,.button.button__variant--secondary--active::before{opacity:0.24}.button.button__variant--tertiary{background-color:transparent;border-color:var(--color-content-default, rgb(40, 40, 40))}.button.button__variant--tertiary .typo_buttom,.button.button__variant--tertiary .icon_buttom{color:var(--color-content-default, rgb(40, 40, 40));z-index:1}.button.button__variant--tertiary--disabled{opacity:50%;pointer-events:none}.button.button__variant--tertiary:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));-webkit-transition:background-color 0.3s ease;transition:background-color 0.3s ease;opacity:0.08}.button.button__variant--tertiary:active::before,.button.button__variant--tertiary--active::before{opacity:0.24}.button__group{width:100%}.button__position--row--first{border-top-left-radius:10px;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:0;border-left-width:1px;border-top-width:1px;border-bottom-width:1px;border-right-width:1px}.button__position--row--middle{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;border-top-width:1px;border-bottom-width:1px;border-left-width:0;border-right-width:1px}.button__position--row--last{border-top-left-radius:0;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:10px;border-right-width:1px;border-top-width:1px;border-bottom-width:1px;border-left-width:0px}.button__position--column--first{border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:0;border-left-width:1px;border-top-width:1px;border-bottom-width:1px;border-right-width:1px}.button__position--column--middle{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;border-top-width:0px;border-bottom-width:1px;border-left-width:1px;border-right-width:1px}.button__position--column--last{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:10px;border-right-width:1px;border-top-width:0px;border-bottom-width:1px;border-left-width:1px}.button__arrow{color:inherit;background-color:inherit;height:24px;margin-left:2px}.button__content{height:20px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;z-index:1}.button::before{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:transparent;z-index:0;border-radius:8px}.button__position--row--first::before{border-top-left-radius:10px;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:0}.button__position--row--middle::before{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0}.button__position--row--last::before{border-top-left-radius:0;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:10px}.button__position--column--first::before{border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:0}.button__position--column--middle::before{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0}.button__position--column--last::before{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:10px}.button .hide{cursor:not-allowed;opacity:0}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}.disabled{pointer-events:none}';var b=t("bds_button",function(){function t(t){o(this,t);this.bdsClick=r(this,"bdsClick");this.group=false;this.block=false;this.fullWidth=false;this.justifyContent="center";this.groupIcon=false;this.disabled=false;this.color="primary";this.size="medium";this.variant="solid";this.icon=null;this.iconLeft=null;this.iconRight=null;this.arrow=false;this.type="button";this.iconTheme="outline";this.typeIcon="icon";this.bdsLoading=false;this.bdsLoadingVariant="primary";this.bdsLoadingColor="main";this.dataTest=null}t.prototype.isActive=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.active=t;return[2]}))}))};t.prototype.setPosition=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.position=t;this.position?this.group=true:false;return[2]}))}))};t.prototype.setDirection=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.direction=t;return[2]}))}))};t.prototype.setSize=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.size=t;return[2]}))}))};t.prototype.setColor=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.color=t;return[2]}))}))};t.prototype.setVariant=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.variant=t;return[2]}))}))};t.prototype.componentDidRender=function(){this.logSlotText();this.buttonLegacy()};t.prototype.buttonLegacy=function(){this.variant==="facebook"?this.setVariant("outline"):this.setVariant(this.variant);this.size==="tall"?this.setSize("large"):this.size==="standard"?this.setSize("medium"):this.setSize(this.size)};t.prototype.logSlotText=function(){var t=this.el.shadowRoot.querySelector("slot");var o=this.el.shadowRoot.querySelector("button");if(t){var r=t.assignedNodes();var n="";r.forEach((function(t){if(t.nodeType===Node.TEXT_NODE){n+=t.textContent}}));if(n===""&&this.size==="medium"){o.classList.add("button__only-icon--medium")}if(n===""&&this.size==="large"){o.classList.add("button__only-icon--large")}if(n===""&&this.size==="short"){o.classList.add("button__only-icon--short")}}};t.prototype.renderLoadingSpinner=function(){if(this.variant==="solid"){if(["primary","positive","negative"].includes(this.color)){this.loadingColor="light"}else if(this.color==="content"){this.loadingColor="content"}}else if(this.variant==="outline"||this.variant==="text"){this.loadingColor=this.color==="positive"?"positive":this.color==="negative"?"negative":"main"}return n("bds-loading-spinner",{size:"extra-small",color:this.loadingColor})};t.prototype.handleClick=function(t){if(!this.disabled){this.bdsClick.emit(t);var o=this.el.closest("form");if(o){t.preventDefault();var r=document.createElement("button");r.type=this.type;r.style.display="none";o.appendChild(r);r.click();r.remove()}}};t.prototype.render=function(){var t;var o=this;return n(e,{key:"14e854be99707cebafdfc4bc53387046224e0d4b",class:{host:true,block:this.block||this.fullWidth,group:this.group}},n("div",{key:"0e51d7804bef62a92c817c19bfdcf3dedf61827a",tabindex:"0",onKeyDown:function(t){return o.handleClick(t)},class:"focus"}),n("button",{key:"9f3c8621fe71c9708fba62f52533a4b0dbf2db13",onClick:function(t){return o.handleClick(t)},disabled:this.disabled,tabindex:"-1","aria-disabled":this.disabled?"true":"false","aria-live":"assertive",type:this.type,class:(t={button:true,"button--block":this.block,"button--full-width":this.fullWidth,"button--group":this.group},t["button__justify-content--".concat(this.justifyContent)]=true,t["button__position--".concat(this.direction,"--").concat(this.position)]=true,t["button--active"]=this.active,t["button__variant--".concat(this.variant==="delete"?"solid":this.variant)]=true,t["button__".concat(this.variant==="delete"?"solid":this.variant)]=true,t["button__color--".concat(this.variant==="delete"?"negative":this.color)]=true,t["button__variant--".concat(this.variant,"--disabled")]=this.disabled,t["button__size--".concat(this.size)]=true,t),part:"button","data-test":this.dataTest},this.bdsLoading?this.renderLoadingSpinner():"",this.groupIcon&&(this.iconLeft||this.icon)?n("div",{class:"button__group-content"},n("bds-icon",{class:{icon_buttom:true,hide:this.bdsLoading},name:this.icon?this.icon:this.iconLeft,theme:this.iconTheme,type:this.typeIcon,color:"inherit",size:"medium"}),n("bds-typo",{class:{typo_buttom:true,button__content:true,hide:this.bdsLoading},variant:"fs-14",lineHeight:"simple",bold:"bold"},n("slot",null))):[this.iconLeft||this.icon?n("bds-icon",{class:{icon_buttom:true,hide:this.bdsLoading},name:this.icon?this.icon:this.iconLeft,theme:this.iconTheme,type:this.typeIcon,color:"inherit",size:"medium"}):null,n("bds-typo",{class:{typo_buttom:true,button__content:true,hide:this.bdsLoading},variant:"fs-14",lineHeight:"simple",bold:"bold"},n("slot",null))],this.iconRight||this.arrow?n("bds-icon",{class:{icon_buttom:true,hide:this.bdsLoading},name:this.arrow?"arrow-right":this.iconRight,color:"inherit",theme:this.iconTheme,type:this.typeIcon}):""))};Object.defineProperty(t.prototype,"el",{get:function(){return i(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{bdsLoading:["renderLoadingSpinner"]}},enumerable:false,configurable:true});return t}());b.style=a}}}));
//# sourceMappingURL=p-6de40292.system.entry.js.map