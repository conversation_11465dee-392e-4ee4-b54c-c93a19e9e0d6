System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,l,s,c;return{setters:[function(e){t=e.r;l=e.h;s=e.H;c=e.a}],execute:function(){var i='.sc-bds-table-cell-h{display:table-cell;padding:0 8px;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:14px;vertical-align:middle}.cell.sc-bds-table-cell{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;min-height:48px;margin:8px 0;color:var(--color-content-default, rgb(40, 40, 40));font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap}.dense_cell.sc-bds-table-cell{margin:0}.cell_custom.sc-bds-table-cell{gap:8px}.cell_action.sc-bds-table-cell{-ms-flex-direction:row;flex-direction:row;gap:8px}.justify--left.sc-bds-table-cell{-ms-flex-pack:start;justify-content:flex-start}.justify--center.sc-bds-table-cell{-ms-flex-pack:center;justify-content:center}.justify--right.sc-bds-table-cell{-ms-flex-pack:end;justify-content:flex-end}.sc-bds-table-cell-h:first-child{padding-left:16px}.sc-bds-table-cell-h:last-child{padding-right:16px}';var a=e("bds_table_cell",function(){function e(e){t(this,e);this.isDense=false;this.type="text";this.sortable=false;this.justifyContent="left"}e.prototype.renderContent=function(){var e,t,s,c;return this.type==="custom"?l("div",{class:(e={cell:true,cell_custom:true,dense_cell:true},e["justify--".concat(this.justifyContent)]=true,e)},l("slot",null)):this.type==="text"?l("div",{class:(t={cell:true,dense_cell:true},t["justify--".concat(this.justifyContent)]=true,t)},l("bds-typo",{variant:"fs-14",bold:this.sortable?"bold":"regular"},l("slot",null))):this.type==="action"?l("div",{class:(s={cell:true,cell_action:true,dense_cell:true},s["justify--".concat(this.justifyContent)]=true,s)},l("slot",null)):this.type==="collapse"?l("td",{colSpan:2,class:(c={cell:true,cell_action:true,dense_cell:true},c["justify--".concat(this.justifyContent)]=true,c)},l("slot",null)):l("slot",null)};e.prototype.componentWillLoad=function(){var e=this.element.closest("bds-table");if(e&&e.getAttribute("dense-table")==="true"){this.isDense=true}};e.prototype.render=function(){return l(s,{key:"308fc20da6c74c8808a4eca46cc3768985a73419"},this.renderContent())};Object.defineProperty(e.prototype,"element",{get:function(){return c(this)},enumerable:false,configurable:true});return e}());a.style=i}}}));
//# sourceMappingURL=p-a9c0f081.system.entry.js.map