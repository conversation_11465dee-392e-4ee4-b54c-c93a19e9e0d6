{"version": 3, "names": ["tableCss", "Table", "render", "h", "Host", "key", "class", "scrollable", "this", "denseTable"], "sources": ["src/components/table/table/table.scss?tag=bds-table&encapsulation=scoped", "src/components/table/table/table.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  width: 100%;\n  border-radius: 8px;\n  background-color: $color-surface-1;\n  border: 1px solid $color-border-3;\n}\n.bds-table {\n  display: table;\n  width: 100%;\n  border-spacing: 0px;\n  box-sizing: border-box;\n  border-collapse: collapse;\n}\n\n:host(.scrollable) {\n  overflow-x: auto;\n}\n\n:host(.dense-table) {\n  height: fit-content;\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table',\n  styleUrl: 'table.scss',\n  scoped: true,\n})\nexport class Table {\n  /**\n   * Specifies whether the table is scrollable or not.\n   */\n  @Prop({ mutable: true, reflect: true }) scrollable?: boolean;\n  /**\n   * Specifies whether the table is scrollable or not.\n   */\n  @Prop({ mutable: true, reflect: true }) collapse?: boolean;\n\n  /**\n   * Determines if the table has a higher content density, typically resulting in more compact rows and cells.\n   */\n  @Prop({ mutable: true, reflect: true }) denseTable?: boolean;\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ scrollable: this.scrollable, 'dense-table': this.denseTable }}>\n        <div class=\"bds-table\">\n          <slot />\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAW,sc,MCOJC,EAAK,M,yBAehB,MAAAC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CAACC,MAAO,CAAEC,WAAYC,KAAKD,WAAY,cAAeC,KAAKC,aAC9DN,EAAK,OAAAE,IAAA,2CAAAC,MAAM,aACTH,EAAQ,QAAAE,IAAA,8C", "ignoreList": []}