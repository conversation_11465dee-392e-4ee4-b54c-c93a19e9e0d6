{"version": 3, "file": "p-BwL15WLC.system.js", "sources": ["src/components/autocomplete/autocomplete.scss?tag=bds-autocomplete&encapsulation=shadow", "src/components/autocomplete/autocomplete.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 250px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n  .input__container__text:placeholder-shown {\n    color: $color-content-ghost;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n    &__text::placeholder {\n      color: $color-content-ghost;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n  &__text::placeholder {\n    color: $color-content-ghost;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 8px;\n\n  .inside-input-left {\n    display: inline;\n  }\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n  flex-shrink: 99999;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n\n:host {\n  display: block;\n}\n\n.select {\n  position: relative;\n  outline: none;\n  overflow: hidden;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n\n    bds-icon {\n      margin-left: 10px;\n    }\n  }\n\n  .icon-hidden {\n    visibility: hidden;\n  }\n\n  &__options {\n    display: grid;\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    ::slotted(*) {\n      display: flex;\n      flex-flow: column;\n    }\n\n    .selection-title {\n      order: -2;\n      width: 100%;\n      padding: 8px 16px;\n      box-sizing: border-box;\n    }\n\n    .select-all {\n      order: -3;\n      padding: 8px 8px 8px 12px;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n    }\n\n    .content-divisor {\n      display: block;\n      width: 100%;\n      height: 1px;\n      background-color: $color-surface-1;\n\n      .divisor {\n        display: block;\n        margin: 0 16px;\n        height: 1px;\n        background-color: $color-border-2;\n      }\n    }\n\n    .load-spinner {\n      background-color: $color-surface-1;\n      height: 200px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n\n    .options-checked {\n      order: -1;\n    }\n  }\n}\n", "import { Component, h, Host, State, Prop, EventEmitter, Event, Watch, Element, Listen, Method } from '@stencil/core';\nimport {\n  AutocompleteOption,\n  AutocompleteChangeEventDetail,\n  AutocompleteSelectedChangeEventDetail,\n  AutocompleteOptionsPositionType,\n  AutocompleteMultiSelectedChangeEventDetail,\n} from './autocomplete-select-interface';\nimport { SelectOptionsPositionType } from '../selects/select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type SelectionType = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-autocomplete',\n  styleUrl: 'autocomplete.scss',\n  shadow: true,\n})\nexport class BdsAutocomplete {\n  private checkAllInput?: HTMLBdsCheckboxElement;\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() intoView?: HTMLElement = null;\n\n  @State() isPressed? = false;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  @State() textMultiselect? = '';\n\n  @State() placeholderState?: string = this.placeholder;\n\n  @State() internalOptions: AutocompleteOption[];\n\n  @State() cloneOptions: AutocompleteOption[];\n\n  @State() checkedOptions: AutocompleteOption[];\n\n  @State() isFocused?: boolean = false;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | AutocompleteOption[];\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null;\n\n  /**\n   * the item selected.\n   */\n  @Prop({ mutable: true }) selected?: HTMLBdsSelectOptionElement | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Search only the title property\n   */\n  @Prop({ reflect: true }) searchOnlyTitle? = true;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop() optionsPosition?: AutocompleteOptionsPositionType = 'auto';\n\n  /**\n   * If true, the X icon will appear only when component is focused.\n   */\n  @Prop() clearIconOnFocus?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Is Loading, is the prop to enable that the component is loading.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Multiselect, Prop to enable multi selections.\n   */\n  @Prop() selectionType?: SelectionType = 'single';\n\n  /**\n   * Selection Title, Prop to enable title to select.\n   */\n  @Prop() selectionTitle?: string = '';\n\n    /**\n   * Selection Title, Prop to enable title to select.\n   */\n    @Prop() selectedAll?: boolean = true;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsSelectedChange!: EventEmitter<AutocompleteSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsMultiselectedChange!: EventEmitter<AutocompleteMultiSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('selected')\n  itemSelectedChanged(): void {\n    this.bdsSelectedChange.emit(this.selected);\n  }\n\n  @Watch('value')\n  protected valueChanged(): void {\n    this.bdsChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n    this.selected = this.childOptionSelected;\n    this.text = this.getText();\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('checkedOptions')\n  protected changeCheckedOptions() {\n    this.placeholderState =\n      this.selectionType === 'multiple'\n        ? this.checkedOptions?.length === 0 || this.checkedOptions === null\n          ? this.placeholder\n          : ''\n        : this.placeholder;\n    this.getTextMultiselect(this.checkedOptions);\n    this.bdsMultiselectedChange.emit({ value: this.checkedOptions });\n  }\n\n  @Watch('options')\n  parseOptions() {\n    if (this.options) {\n      this.resetFilterOptions();\n      try {\n        this.internalOptions = typeof this.options === 'string' ? JSON.parse(this.options) : this.options;\n      } catch (e) {\n        this.internalOptions = [];\n      }\n    }\n  }\n\n  @Watch('selectionType')\n  protected changeSelectionType() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.intoView = getScrollParent(this.el);\n    this.options && this.parseOptions();\n  }\n\n  componentDidLoad() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n\n    this.text = this.getText();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: AutocompleteOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private refCheckAllInput = (input: HTMLBdsCheckboxElement): void => {\n    this.checkAllInput = input;\n  };\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  private onFocus = (): void => {\n    this.isFocused = true;\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  private onFocusout = (): void => {\n    if (!this.isOpen) {\n      this.nativeInput.value = this.getText();\n    }\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n    if (!this.isOpen) {\n      this.isFocused = false;\n      this.nativeInput.value = this.getText();\n      if (this.selectionType == 'multiple') this.cleanInputSelection();\n    }\n    if (this.selectionType == 'multiple' && this.checkedOptions?.length > 0)\n      this.getTextMultiselect(this.checkedOptions);\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.toggle();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.innerText ?? '');\n  };\n\n  private getText = (): string => {\n    const opt = this.childOptions.find((option) => option.value == this.value);\n    return this.getTextFromOption(opt);\n  };\n\n  private getTextMultiselect = (data): void => {\n    const valueInput = data?.length > 0 && `${data?.length} selecionados`;\n    this.textMultiselect = valueInput;\n  };\n\n  private handlerMultiselect = (): void => {\n    this.updateListChecked(this.childOptions);\n    this.nativeInput.value = '';\n    this.value = undefined;\n    this.resetFilterOptions();\n    if (this.childOptions.length != this.checkedOptions.length) {\n      setTimeout(() => {\n        this.checkAllInput.checked = false;\n      }, 10);\n    }\n  };\n\n  private handleCheckAll = (event: CustomEvent): void => {\n    const {\n      detail: { checked },\n    } = event;\n    for (const option of this.childOptions) {\n      if (checked) {\n        option.toMark();\n      } else {\n        option.markOff();\n      }\n    }\n    setTimeout(() => {\n      this.updateListChecked(this.childOptions);\n    }, 10);\n  };\n\n  private updateListChecked = (data: HTMLBdsSelectOptionElement[]): void => {\n    for (const option of data) {\n      option.checked ? option.classList.add('option-checked') : option.classList.remove('option-checked');\n    }\n    const defaultCheckedOptions = Array.from(data).filter((item) => item.checked == true);\n    const value = defaultCheckedOptions.map((term) => ({\n      value: term.value,\n      label: term.textContent,\n      checked: term.checked,\n    }));\n    this.checkedOptions = value;\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private cleanInputSelection = async () => {\n    if (!this.disabled) {\n      this.value = '';\n      this.nativeInput.value = '';\n      this.isOpen = false;\n      this.bdsCancel.emit({ value: '' });\n      await this.resetFilterOptions();\n    }\n  };\n\n  @Method()\n  async cleanMultipleSelection() {\n    if (this.selectionType === 'multiple' && this.checkedOptions?.length > 0) {\n      for (const option of this.childOptions) {\n        option.checked = false;\n        option.classList.remove('option-checked');\n      }\n      this.checkedOptions = [];\n      this.checkAllInput.checked = false;\n      this.nativeInput.value = '';\n      this.value = undefined;\n      this.resetFilterOptions();\n    } else {\n      this.cleanInputSelection();\n    }\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      this.value = '';\n      if (this.isOpen) {\n        await this.resetFilterOptions();\n      } else {\n        this.setTimeoutFilter();\n      }\n    }\n\n    if (this.isOpen === false) {\n      this.value = this.getSelectedValue();\n      this.setTimeoutFilter();\n    }\n  };\n\n  private setTimeoutFilter(): void {\n    setTimeout(() => {\n      this.resetFilterOptions();\n    }, 500);\n  }\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n    }\n\n    for (const option of this.childOptions) {\n      const optionTextLowercase = this.searchOnlyTitle\n        ? this.getTextFromOption(option).toLowerCase()\n        : option.textContent.toLowerCase();\n\n      const termLower = term.toLowerCase();\n\n      optionTextLowercase.includes(termLower)\n        ? option.removeAttribute('invisible')\n        : option.setAttribute('invisible', 'invisible');\n    }\n  }\n\n  private async resetFilterOptions() {\n    const childOptions = this.childOptions;\n    for (const option of childOptions) {\n      option.removeAttribute('invisible');\n    }\n  }\n\n  private getSelectedValue() {\n    return this.childOptionSelected?.value;\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            select: true,\n            'input--state-primary': !this.danger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': this.isPressed,\n          }}\n          onClick={this.onClickWrapper}\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\" tabindex=\"0\" onFocusout={this.onFocusout}>\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              {this.textMultiselect?.length > 0 && (\n                <bds-typo variant=\"fs-14\" class=\"inside-input-left\">\n                  {this.textMultiselect}\n                </bds-typo>\n              )}\n              <input\n                class={{ input__container__text: true }}\n                ref={(input) => (this.nativeInput = input)}\n                disabled={this.disabled}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.changedInputValue}\n                placeholder={this.placeholderState}\n                type=\"text\"\n                value={this.text}\n                data-test={this.dataTest}\n                onKeyDown={this.keyPressWrapper.bind(this)}\n              />\n            </div>\n          </div>\n          <div class=\"select__icon\">\n            <bds-icon\n              size=\"small\"\n              name=\"error\"\n              theme=\"solid\"\n              onClick={this.cleanInputSelection}\n              class={{\n                'icon-hidden': (this.clearIconOnFocus && (!this.isFocused || !this.isOpen)) || !this.value,\n              }}\n            ></bds-icon>\n            <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n          </div>\n        </div>\n        {this.renderMessage()}\n        {this.loading ? (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            <bds-loading-spinner class=\"load-spinner\" size=\"small\"></bds-loading-spinner>\n          </div>\n        ) : (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            {this.selectionTitle && this.selectionType == 'multiple' && (\n              <bds-typo class=\"selection-title\" variant=\"fs-10\" bold=\"bold\">\n                {this.selectionTitle}\n              </bds-typo>\n            )}\n            {this.selectionType == 'multiple' && this.selectedAll && (\n              <bds-checkbox\n                ref={this.refCheckAllInput}\n                refer={`refer-multiselect`}\n                label={`Selecionar Todos`}\n                name=\"chack-all\"\n                class=\"select-all\"\n                onBdsChange={(ev) => this.handleCheckAll(ev)}\n              ></bds-checkbox>\n            )}\n            {this.checkedOptions?.length > 0 && (\n              <span class=\"content-divisor\">\n                <span class=\"divisor\"></span>\n              </span>\n            )}\n            {this.internalOptions ? (\n              this.internalOptions.map((option, idx) => (\n                <bds-select-option\n                  onOptionSelected={this.handler}\n                  onOptionChecked={this.handlerMultiselect}\n                  selected={this.value === option.value}\n                  value={option.value}\n                  key={idx}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                  type-option={this.selectionType == 'multiple' ? 'checkbox' : 'default'}\n                >\n                  {option.label}\n                </bds-select-option>\n              ))\n            ) : (\n              <slot />\n            )}\n          </div>\n        )}\n      </Host>\n    );\n  }\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;MAAA,MAAM,eAAe,GAAG,w3cAAw3c;;YCkBn4c,eAAe,+BAAA,MAAA;MAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;MAcE;;MAEG;MACM,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;MAE7B,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;MAElB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MAEf,QAAA,IAAI,CAAA,IAAA,GAAI,EAAE;MAEV,QAAA,IAAe,CAAA,eAAA,GAAI,EAAE;MAErB,QAAA,IAAA,CAAA,gBAAgB,GAAY,IAAI,CAAC,WAAW;MAQ5C,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;MAEpC;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;MAE3C;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;MAoB/B;;MAEG;MACsB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MAExC;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MACjE;;MAEG;MACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAE1C;;MAEG;MACsB,QAAA,IAAe,CAAA,eAAA,GAAI,IAAI;MAEhD;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;MAEnB;;MAEG;MACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;MAE3C;;MAEG;MACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;MAEjC;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;MACnC;;MAEG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;MAClC;;MAEG;MACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;MACrD;;MAEG;MACK,QAAA,IAAe,CAAA,eAAA,GAAqC,MAAM;MAElE;;MAEG;MACK,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;MAE1C;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;MAEhC;;MAEG;MACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MAEjC;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAmB,QAAQ;MAEhD;;MAEG;MACK,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;MAElC;;MAEC;MACO,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;MAuK9B,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAI;MACxC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;MACvB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;MAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;MAC3B,SAAC;MAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAA6B,KAAU;MACjE,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;MAC5B,SAAC;MAcO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;MAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACrB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;MACtB,SAAC;MAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAW;MAC9B,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;sBAChB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE;;MAE3C,SAAC;MAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;;MAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;MACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;MACtB,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;MAChB,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;sBACtB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE;MACvC,gBAAA,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU;0BAAE,IAAI,CAAC,mBAAmB,EAAE;;MAElE,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC;MACrE,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;MAChD,SAAC;MAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;kBAClC,IAAI,CAAC,OAAO,EAAE;kBACd,IAAI,CAAC,MAAM,EAAE;MACb,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAE5B,SAAC;MAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;MAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;MAE9B,SAAC;MAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,GAA+B,KAAY;;MACtE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;MACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;sBACxF,IAAI,cAAc,EAAE;0BAClB,OAAO,cAAc,CAAC,KAAK;;;MAG/B,YAAA,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,IAAG,GAAG,CAAC,SAAS,IAAI,CAAA,EAAA,GAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;MAChE,SAAC;MAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAa;kBAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;MAC1E,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;MACpC,SAAC;MAEO,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,IAAI,KAAU;kBAC1C,MAAM,UAAU,GAAG,CAAA,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,MAAM,IAAG,CAAC,IAAI,GAAG,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,aAAA,CAAe;MACrE,YAAA,IAAI,CAAC,eAAe,GAAG,UAAU;MACnC,SAAC;MAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAW;MACtC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;MACzC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;MAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;kBACtB,IAAI,CAAC,kBAAkB,EAAE;MACzB,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;sBAC1D,UAAU,CAAC,MAAK;MACd,oBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK;uBACnC,EAAE,EAAE,CAAC;;MAEV,SAAC;MAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAkB,KAAU;kBACpD,MAAM,EACJ,MAAM,EAAE,EAAE,OAAO,EAAE,GACpB,GAAG,KAAK;MACT,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;sBACtC,IAAI,OAAO,EAAE;0BACX,MAAM,CAAC,MAAM,EAAE;;2BACV;0BACL,MAAM,CAAC,OAAO,EAAE;;;kBAGpB,UAAU,CAAC,MAAK;MACd,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;mBAC1C,EAAE,EAAE,CAAC;MACR,SAAC;MAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,IAAkC,KAAU;MACvE,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE;sBACzB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;;kBAErG,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;kBACrF,MAAM,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;sBACjD,KAAK,EAAE,IAAI,CAAC,KAAK;sBACjB,KAAK,EAAE,IAAI,CAAC,WAAW;sBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;MACtB,aAAA,CAAC,CAAC;MACH,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;MAC7B,SAAC;MAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAkB,KAAU;kBAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;MACT,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;kBAClB,IAAI,CAAC,MAAM,EAAE;MACf,SAAC;MA2BO,QAAA,IAAmB,CAAA,mBAAA,GAAG,YAAW;MACvC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;MACf,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;MAC3B,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;sBACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;MAClC,gBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;MAEnC,SAAC;MAmBO,QAAA,IAAA,CAAA,iBAAiB,GAAG,OAAO,EAAc,KAAI;MACnD,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;kBAClD,IAAI,KAAK,EAAE;sBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;MAEhC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;MACtB,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;sBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;uBAC3C;MACL,gBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;MACf,gBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;MACf,oBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;2BAC1B;0BACL,IAAI,CAAC,gBAAgB,EAAE;;;MAI3B,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;MACzB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE;sBACpC,IAAI,CAAC,gBAAgB,EAAE;;MAE3B,SAAC;MAqNF;MAliBW,IAAA,aAAa,CAAC,MAAe,EAAA;MACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;MACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;mBAC9D;MACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;MAErE,QAAA,IAAI,MAAM;MACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;uBACzC;sBACL,IAAI,CAAC,oBAAoB,EAAE;;;UAKjC,mBAAmB,GAAA;cACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;UAIlC,YAAY,GAAA;MACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;MACvF,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;kBACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;MAE/C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB;MACxC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;;MAI5B,IAAA,YAAY,CAAC,EAAS,EAAA;MACpB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,MAA0B,CAAC,EAAE;MACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;UAKb,oBAAoB,GAAA;;MAC5B,QAAA,IAAI,CAAC,gBAAgB;kBACnB,IAAI,CAAC,aAAa,KAAK;MACrB,kBAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,MAAK,CAAC,IAAI,IAAI,CAAC,cAAc,KAAK;4BAC3D,IAAI,CAAC;MACP,sBAAE;MACJ,kBAAE,IAAI,CAAC,WAAW;MACtB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;MAC5C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;;UAIlE,YAAY,GAAA;MACV,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;kBAChB,IAAI,CAAC,kBAAkB,EAAE;MACzB,YAAA,IAAI;sBACF,IAAI,CAAC,eAAe,GAAG,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO;;kBACjG,OAAO,CAAC,EAAE;MACV,gBAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;;;UAMrB,mBAAmB,GAAA;MAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,gBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;MACrC,oBAAA,MAAM,CAAC,UAAU,GAAG,UAAU;0BAC9B,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC;;2BAC5D;MACL,oBAAA,MAAM,CAAC,UAAU,GAAG,SAAS;0BAC7B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;0BAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;;;UAM/D,iBAAiB,GAAA;cACf,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;MACxC,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;;UAGrC,gBAAgB,GAAA;MACd,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,gBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;MACrC,oBAAA,MAAM,CAAC,UAAU,GAAG,UAAU;0BAC9B,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC;;2BAC5D;MACL,oBAAA,MAAM,CAAC,UAAU,GAAG,SAAS;0BAC7B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;0BAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;;MAK7D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;MAC1B,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;mBACzC;kBACL,IAAI,CAAC,oBAAoB,EAAE;;;MAIvB,IAAA,mBAAmB,CAAC,KAAsC,EAAA;MAChE,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;kBACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;UAIlC,oBAAoB,GAAA;cAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;kBAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;kBACtB,cAAc,EAAE,IAAI,CAAC,WAAW;kBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;MACxB,SAAA,CAAC;MACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;MACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;kBAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;MAgB1C,IAAA,IAAY,YAAY,GAAA;cACtB,OAAO,IAAI,CAAC;MACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;MACrE,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;;MAG/D,IAAA,IAAY,mBAAmB,GAAA;cAC7B,OAAO,IAAI,CAAC;oBACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ;oBACrG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC;;MA8GzF,IAAA,eAAe,CAAC,KAAK,EAAA;;MAC3B,QAAA,QAAQ,KAAK,CAAC,GAAG;MACf,YAAA,KAAK,OAAO;sBACV,IAAI,CAAC,MAAM,EAAE;sBACb;MACF,YAAA,KAAK,WAAW;MACd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;MAEpB,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;MAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,WAA0C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;0BACxF;;MAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,iBAAgD,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;sBAC7E;MACF,YAAA,KAAK,SAAS;MACZ,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;MAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,eAA8C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;0BAC5F;;MAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,gBAA+C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;sBAC5E;;;MAeN,IAAA,MAAM,sBAAsB,GAAA;;MAC1B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,EAAE;MACxE,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,gBAAA,MAAM,CAAC,OAAO,GAAG,KAAK;MACtB,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;;MAE3C,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE;MACxB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK;MAClC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;MAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;kBACtB,IAAI,CAAC,kBAAkB,EAAE;;mBACpB;kBACL,IAAI,CAAC,mBAAmB,EAAE;;;;UA2BtB,gBAAgB,GAAA;cACtB,UAAU,CAAC,MAAK;kBACd,IAAI,CAAC,kBAAkB,EAAE;eAC1B,EAAE,GAAG,CAAC;;UAGD,MAAM,aAAa,CAAC,IAAY,EAAA;cACtC,IAAI,CAAC,IAAI,EAAE;MACT,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;MAGjC,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;MACtC,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC;wBAC7B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,WAAW;MAC5C,kBAAE,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE;MAEpC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE;MAEpC,YAAA,mBAAmB,CAAC,QAAQ,CAAC,SAAS;MACpC,kBAAE,MAAM,CAAC,eAAe,CAAC,WAAW;wBAClC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;;MAI7C,IAAA,MAAM,kBAAkB,GAAA;MAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;MACtC,QAAA,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE;MACjC,YAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;;UAI/B,gBAAgB,GAAA;;cACtB,OAAO,MAAA,IAAI,CAAC,mBAAmB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK;;UAGhC,UAAU,GAAA;MAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,WAAW,EAAE,IAAI;MACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;UAIG,WAAW,GAAA;MACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,uBAAuB,EAAE,IAAI;sBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;mBACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;UAIG,aAAa,GAAA;cACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;MACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;MAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;MAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;cAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;MAClB,cAAE;oBACA,IAAI,CAAC;MACL,kBAAE;wBACA,gBAAgB;cAExB,IAAI,OAAO,EAAE;MACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;MAIV,QAAA,OAAO,SAAS;;UAGlB,MAAM,GAAA;;MACJ,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,KAAK,EAAE,IAAI;MACX,gBAAA,MAAM,EAAE,IAAI;MACZ,gBAAA,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM;MACpC,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;sBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;sBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;MACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;sBAC5B,gBAAgB,EAAE,IAAI,CAAC,SAAS;mBACjC,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAA,EAE3B,IAAI,CAAC,UAAU,EAAE,EAClB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,kBAAkB,EAAC,QAAQ,EAAC,GAAG,EAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAA,EACnE,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC5C,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,0CAAE,MAAM,IAAG,CAAC,KAC/B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,mBAAmB,IAChD,IAAI,CAAC,eAAe,CACZ,CACZ,EACD,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAClC,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,IAAI,CAAC,IAAI,eACL,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAC1C,CACE,CACF,EACN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,OAAO,EACb,OAAO,EAAE,IAAI,CAAC,mBAAmB,EACjC,KAAK,EAAE;sBACL,aAAa,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;MAC3F,aAAA,EACS,CAAA,EACZ,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAY,CAAA,CACjF,CACF,EACL,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,OAAO,IACX,CAAA,CAAA,KAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI;sBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;MACrC,aAAA,EAAA,EAED,CAAA,CAAA,qBAAA,EAAA,EAAqB,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAA,CAAuB,CACzE,KAEN,CAAA,CAAA,KAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI;sBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;MACrC,aAAA,EAAA,EAEA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,KACtD,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EAC1D,IAAI,CAAC,cAAc,CACX,CACZ,EACA,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,KACnD,oBACE,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAC1B,KAAK,EAAE,CAAmB,iBAAA,CAAA,EAC1B,KAAK,EAAE,CAAkB,gBAAA,CAAA,EACzB,IAAI,EAAC,WAAW,EAChB,KAAK,EAAC,YAAY,EAClB,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EAAA,CAC9B,CACjB,EACA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,KAC9B,CAAM,CAAA,MAAA,EAAA,EAAA,KAAK,EAAC,iBAAiB,EAAA,EAC3B,CAAA,CAAA,MAAA,EAAA,EAAM,KAAK,EAAC,SAAS,EAAA,CAAQ,CACxB,CACR,EACA,IAAI,CAAC,eAAe,IACnB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,MACnC,CAAA,CAAA,mBAAA,EAAA,EACE,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAC9B,eAAe,EAAE,IAAI,CAAC,kBAAkB,EACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,EACrC,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,MAAM,CAAC,UAAU,EAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EACR,aAAA,EAAA,IAAI,CAAC,aAAa,IAAI,UAAU,GAAG,UAAU,GAAG,SAAS,EAAA,EAErE,MAAM,CAAC,KAAK,CACK,CACrB,CAAC,KAEF,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACT,CACG,CACP,CACI;;;;;;;;;;;;;;;;;;;;"}