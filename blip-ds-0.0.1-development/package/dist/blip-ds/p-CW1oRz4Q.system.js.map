{"version": 3, "file": "p-CW1oRz4Q.system.js", "sources": ["src/components/stepper/stepper.scss?tag=bds-stepper&encapsulation=shadow", "src/components/stepper/stepper.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  width: 100%;\n  border-radius: 8px;\n  box-sizing: border-box;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n\n  ::slotted(bds-step:last-child) {\n    flex: inherit;\n  }\n}\n\n::slotted(.stepper__container__divisor) {\n  flex: 1 1 auto;\n  align-self: center;\n  height: 1.5px;\n  background: $color-content-disable;\n  margin: 0px 8px;\n  min-width: 24px;\n}\n\n::slotted(.stepper__container__divisor--completed) {\n  border-top: 2px solid $color-primary;\n}\n", "import { Component, ComponentInterface, h, Element, Method, Host } from '@stencil/core';\n@Component({\n  tag: 'bds-stepper',\n  styleUrl: 'stepper.scss',\n  shadow: true,\n})\nexport class BdsStepper implements ComponentInterface {\n  @Element() el: HTMLBdsStepperElement;\n\n  connectedCallback() {\n    this.childOptions.forEach((option, index) => {\n      option.index = index;\n      if (index === this.childOptions.length - 1) {\n        option.last = true;\n      }\n    });\n  }\n\n  componentDidLoad() {\n    this.renderLine();\n  }\n\n  /**\n   * Set the active step\n   *\n   * @param index The index of the step to be set as active\n   * @returns void\n   */\n  @Method()\n  public async setActiveStep(index: number): Promise<void> {\n    this.resetActiveSteps();\n    this.childOptions[index].active = true;\n  }\n\n  /**\n   * Set the completed step\n   *\n   * @param index The index of the step to be set as completed\n   * @returns void\n   */\n  @Method()\n  public async setCompletedStep(index: number): Promise<void> {\n    this.childOptions[index].completed = true;\n  }\n\n  /**\n   * Returns the active step\n   *\n   * @returns HTMLBdsStepElement\n   */\n  @Method()\n  public async getActiveStep(): Promise<number> {\n    return this.childOptions.find((step) => step.active === true).index;\n  }\n\n  /**\n   * Reset all active steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetActiveSteps() {\n    for (const step of this.childOptions) {\n      step.active = false;\n    }\n  }\n\n  /**\n   * Reset all completed steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetCompletedSteps() {\n    for (const step of this.childOptions) {\n      step.completed = false;\n    }\n  }\n\n  private get childOptions(): HTMLBdsStepElement[] {\n    return Array.from(this.el.querySelectorAll('bds-step'));\n  }\n\n  private renderLine() {\n    const line = document.createElement('div');\n    line.classList.add('stepper__container__divisor');\n\n    Array.from(this.childOptions).forEach((item, idx) => {\n      if (this.childOptions.length - 1 != idx) {\n        item.insertAdjacentHTML('afterend', line.outerHTML);\n      }\n    });\n  }\n\n  render() {\n    return (\n      <Host class=\"stepper__container\">\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;MAAA,MAAM,UAAU,GAAG,ukBAAukB;;YCM7kB,UAAU,0BAAA,MAAA;;;;UAGrB,iBAAiB,GAAA;cACf,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;MAC1C,YAAA,MAAM,CAAC,KAAK,GAAG,KAAK;kBACpB,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;MAC1C,gBAAA,MAAM,CAAC,IAAI,GAAG,IAAI;;MAEtB,SAAC,CAAC;;UAGJ,gBAAgB,GAAA;cACd,IAAI,CAAC,UAAU,EAAE;;MAGnB;;;;;MAKG;UAEI,MAAM,aAAa,CAAC,KAAa,EAAA;cACtC,IAAI,CAAC,gBAAgB,EAAE;cACvB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI;;MAGxC;;;;;MAKG;UAEI,MAAM,gBAAgB,CAAC,KAAa,EAAA;cACzC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,IAAI;;MAG3C;;;;MAIG;MAEI,IAAA,MAAM,aAAa,GAAA;MACxB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;;MAGrE;;;;MAIG;MAEI,IAAA,MAAM,gBAAgB,GAAA;MAC3B,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;MACpC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;MAIvB;;;;MAIG;MAEI,IAAA,MAAM,mBAAmB,GAAA;MAC9B,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;MACpC,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;MAI1B,IAAA,IAAY,YAAY,GAAA;MACtB,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;;UAGjD,UAAU,GAAA;cAChB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;MAC1C,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC;MAEjD,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,KAAI;kBAClD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE;sBACvC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC;;MAEvD,SAAC,CAAC;;UAGJ,MAAM,GAAA;cACJ,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,oBAAoB,EAAA,EAC9B,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;;;;;"}