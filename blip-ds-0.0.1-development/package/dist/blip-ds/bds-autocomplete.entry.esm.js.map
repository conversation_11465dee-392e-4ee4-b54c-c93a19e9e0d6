{"version": 3, "file": "bds-autocomplete.entry.esm.js", "sources": ["src/components/autocomplete/autocomplete.scss?tag=bds-autocomplete&encapsulation=shadow", "src/components/autocomplete/autocomplete.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 250px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n  .input__container__text:placeholder-shown {\n    color: $color-content-ghost;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n    &__text::placeholder {\n      color: $color-content-ghost;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n  &__text::placeholder {\n    color: $color-content-ghost;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 8px;\n\n  .inside-input-left {\n    display: inline;\n  }\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n  flex-shrink: 99999;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n\n:host {\n  display: block;\n}\n\n.select {\n  position: relative;\n  outline: none;\n  overflow: hidden;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n\n    bds-icon {\n      margin-left: 10px;\n    }\n  }\n\n  .icon-hidden {\n    visibility: hidden;\n  }\n\n  &__options {\n    display: grid;\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    ::slotted(*) {\n      display: flex;\n      flex-flow: column;\n    }\n\n    .selection-title {\n      order: -2;\n      width: 100%;\n      padding: 8px 16px;\n      box-sizing: border-box;\n    }\n\n    .select-all {\n      order: -3;\n      padding: 8px 8px 8px 12px;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n    }\n\n    .content-divisor {\n      display: block;\n      width: 100%;\n      height: 1px;\n      background-color: $color-surface-1;\n\n      .divisor {\n        display: block;\n        margin: 0 16px;\n        height: 1px;\n        background-color: $color-border-2;\n      }\n    }\n\n    .load-spinner {\n      background-color: $color-surface-1;\n      height: 200px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n\n    .options-checked {\n      order: -1;\n    }\n  }\n}\n", "import { Component, h, Host, State, Prop, EventEmitter, Event, Watch, Element, Listen, Method } from '@stencil/core';\nimport {\n  AutocompleteOption,\n  AutocompleteChangeEventDetail,\n  AutocompleteSelectedChangeEventDetail,\n  AutocompleteOptionsPositionType,\n  AutocompleteMultiSelectedChangeEventDetail,\n} from './autocomplete-select-interface';\nimport { SelectOptionsPositionType } from '../selects/select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type SelectionType = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-autocomplete',\n  styleUrl: 'autocomplete.scss',\n  shadow: true,\n})\nexport class BdsAutocomplete {\n  private checkAllInput?: HTMLBdsCheckboxElement;\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() intoView?: HTMLElement = null;\n\n  @State() isPressed? = false;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  @State() textMultiselect? = '';\n\n  @State() placeholderState?: string = this.placeholder;\n\n  @State() internalOptions: AutocompleteOption[];\n\n  @State() cloneOptions: AutocompleteOption[];\n\n  @State() checkedOptions: AutocompleteOption[];\n\n  @State() isFocused?: boolean = false;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | AutocompleteOption[];\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null;\n\n  /**\n   * the item selected.\n   */\n  @Prop({ mutable: true }) selected?: HTMLBdsSelectOptionElement | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Search only the title property\n   */\n  @Prop({ reflect: true }) searchOnlyTitle? = true;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop() optionsPosition?: AutocompleteOptionsPositionType = 'auto';\n\n  /**\n   * If true, the X icon will appear only when component is focused.\n   */\n  @Prop() clearIconOnFocus?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Is Loading, is the prop to enable that the component is loading.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Multiselect, Prop to enable multi selections.\n   */\n  @Prop() selectionType?: SelectionType = 'single';\n\n  /**\n   * Selection Title, Prop to enable title to select.\n   */\n  @Prop() selectionTitle?: string = '';\n\n    /**\n   * Selection Title, Prop to enable title to select.\n   */\n    @Prop() selectedAll?: boolean = true;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsSelectedChange!: EventEmitter<AutocompleteSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsMultiselectedChange!: EventEmitter<AutocompleteMultiSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('selected')\n  itemSelectedChanged(): void {\n    this.bdsSelectedChange.emit(this.selected);\n  }\n\n  @Watch('value')\n  protected valueChanged(): void {\n    this.bdsChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n    this.selected = this.childOptionSelected;\n    this.text = this.getText();\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('checkedOptions')\n  protected changeCheckedOptions() {\n    this.placeholderState =\n      this.selectionType === 'multiple'\n        ? this.checkedOptions?.length === 0 || this.checkedOptions === null\n          ? this.placeholder\n          : ''\n        : this.placeholder;\n    this.getTextMultiselect(this.checkedOptions);\n    this.bdsMultiselectedChange.emit({ value: this.checkedOptions });\n  }\n\n  @Watch('options')\n  parseOptions() {\n    if (this.options) {\n      this.resetFilterOptions();\n      try {\n        this.internalOptions = typeof this.options === 'string' ? JSON.parse(this.options) : this.options;\n      } catch (e) {\n        this.internalOptions = [];\n      }\n    }\n  }\n\n  @Watch('selectionType')\n  protected changeSelectionType() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.intoView = getScrollParent(this.el);\n    this.options && this.parseOptions();\n  }\n\n  componentDidLoad() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n\n    this.text = this.getText();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: AutocompleteOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private refCheckAllInput = (input: HTMLBdsCheckboxElement): void => {\n    this.checkAllInput = input;\n  };\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  private onFocus = (): void => {\n    this.isFocused = true;\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  private onFocusout = (): void => {\n    if (!this.isOpen) {\n      this.nativeInput.value = this.getText();\n    }\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n    if (!this.isOpen) {\n      this.isFocused = false;\n      this.nativeInput.value = this.getText();\n      if (this.selectionType == 'multiple') this.cleanInputSelection();\n    }\n    if (this.selectionType == 'multiple' && this.checkedOptions?.length > 0)\n      this.getTextMultiselect(this.checkedOptions);\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.toggle();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.innerText ?? '');\n  };\n\n  private getText = (): string => {\n    const opt = this.childOptions.find((option) => option.value == this.value);\n    return this.getTextFromOption(opt);\n  };\n\n  private getTextMultiselect = (data): void => {\n    const valueInput = data?.length > 0 && `${data?.length} selecionados`;\n    this.textMultiselect = valueInput;\n  };\n\n  private handlerMultiselect = (): void => {\n    this.updateListChecked(this.childOptions);\n    this.nativeInput.value = '';\n    this.value = undefined;\n    this.resetFilterOptions();\n    if (this.childOptions.length != this.checkedOptions.length) {\n      setTimeout(() => {\n        this.checkAllInput.checked = false;\n      }, 10);\n    }\n  };\n\n  private handleCheckAll = (event: CustomEvent): void => {\n    const {\n      detail: { checked },\n    } = event;\n    for (const option of this.childOptions) {\n      if (checked) {\n        option.toMark();\n      } else {\n        option.markOff();\n      }\n    }\n    setTimeout(() => {\n      this.updateListChecked(this.childOptions);\n    }, 10);\n  };\n\n  private updateListChecked = (data: HTMLBdsSelectOptionElement[]): void => {\n    for (const option of data) {\n      option.checked ? option.classList.add('option-checked') : option.classList.remove('option-checked');\n    }\n    const defaultCheckedOptions = Array.from(data).filter((item) => item.checked == true);\n    const value = defaultCheckedOptions.map((term) => ({\n      value: term.value,\n      label: term.textContent,\n      checked: term.checked,\n    }));\n    this.checkedOptions = value;\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private cleanInputSelection = async () => {\n    if (!this.disabled) {\n      this.value = '';\n      this.nativeInput.value = '';\n      this.isOpen = false;\n      this.bdsCancel.emit({ value: '' });\n      await this.resetFilterOptions();\n    }\n  };\n\n  @Method()\n  async cleanMultipleSelection() {\n    if (this.selectionType === 'multiple' && this.checkedOptions?.length > 0) {\n      for (const option of this.childOptions) {\n        option.checked = false;\n        option.classList.remove('option-checked');\n      }\n      this.checkedOptions = [];\n      this.checkAllInput.checked = false;\n      this.nativeInput.value = '';\n      this.value = undefined;\n      this.resetFilterOptions();\n    } else {\n      this.cleanInputSelection();\n    }\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      this.value = '';\n      if (this.isOpen) {\n        await this.resetFilterOptions();\n      } else {\n        this.setTimeoutFilter();\n      }\n    }\n\n    if (this.isOpen === false) {\n      this.value = this.getSelectedValue();\n      this.setTimeoutFilter();\n    }\n  };\n\n  private setTimeoutFilter(): void {\n    setTimeout(() => {\n      this.resetFilterOptions();\n    }, 500);\n  }\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n    }\n\n    for (const option of this.childOptions) {\n      const optionTextLowercase = this.searchOnlyTitle\n        ? this.getTextFromOption(option).toLowerCase()\n        : option.textContent.toLowerCase();\n\n      const termLower = term.toLowerCase();\n\n      optionTextLowercase.includes(termLower)\n        ? option.removeAttribute('invisible')\n        : option.setAttribute('invisible', 'invisible');\n    }\n  }\n\n  private async resetFilterOptions() {\n    const childOptions = this.childOptions;\n    for (const option of childOptions) {\n      option.removeAttribute('invisible');\n    }\n  }\n\n  private getSelectedValue() {\n    return this.childOptionSelected?.value;\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            select: true,\n            'input--state-primary': !this.danger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': this.isPressed,\n          }}\n          onClick={this.onClickWrapper}\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\" tabindex=\"0\" onFocusout={this.onFocusout}>\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              {this.textMultiselect?.length > 0 && (\n                <bds-typo variant=\"fs-14\" class=\"inside-input-left\">\n                  {this.textMultiselect}\n                </bds-typo>\n              )}\n              <input\n                class={{ input__container__text: true }}\n                ref={(input) => (this.nativeInput = input)}\n                disabled={this.disabled}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.changedInputValue}\n                placeholder={this.placeholderState}\n                type=\"text\"\n                value={this.text}\n                data-test={this.dataTest}\n                onKeyDown={this.keyPressWrapper.bind(this)}\n              />\n            </div>\n          </div>\n          <div class=\"select__icon\">\n            <bds-icon\n              size=\"small\"\n              name=\"error\"\n              theme=\"solid\"\n              onClick={this.cleanInputSelection}\n              class={{\n                'icon-hidden': (this.clearIconOnFocus && (!this.isFocused || !this.isOpen)) || !this.value,\n              }}\n            ></bds-icon>\n            <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n          </div>\n        </div>\n        {this.renderMessage()}\n        {this.loading ? (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            <bds-loading-spinner class=\"load-spinner\" size=\"small\"></bds-loading-spinner>\n          </div>\n        ) : (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            {this.selectionTitle && this.selectionType == 'multiple' && (\n              <bds-typo class=\"selection-title\" variant=\"fs-10\" bold=\"bold\">\n                {this.selectionTitle}\n              </bds-typo>\n            )}\n            {this.selectionType == 'multiple' && this.selectedAll && (\n              <bds-checkbox\n                ref={this.refCheckAllInput}\n                refer={`refer-multiselect`}\n                label={`Selecionar Todos`}\n                name=\"chack-all\"\n                class=\"select-all\"\n                onBdsChange={(ev) => this.handleCheckAll(ev)}\n              ></bds-checkbox>\n            )}\n            {this.checkedOptions?.length > 0 && (\n              <span class=\"content-divisor\">\n                <span class=\"divisor\"></span>\n              </span>\n            )}\n            {this.internalOptions ? (\n              this.internalOptions.map((option, idx) => (\n                <bds-select-option\n                  onOptionSelected={this.handler}\n                  onOptionChecked={this.handlerMultiselect}\n                  selected={this.value === option.value}\n                  value={option.value}\n                  key={idx}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                  type-option={this.selectionType == 'multiple' ? 'checkbox' : 'default'}\n                >\n                  {option.label}\n                </bds-select-option>\n              ))\n            ) : (\n              <slot />\n            )}\n          </div>\n        )}\n      </Host>\n    );\n  }\n}"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe,GAAG,w3cAAw3c;;MCkBn4c,eAAe,GAAA,MAAA;AAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;AAcE;;AAEG;AACM,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;AAE7B,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAElB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAEf,QAAA,IAAI,CAAA,IAAA,GAAI,EAAE;AAEV,QAAA,IAAe,CAAA,eAAA,GAAI,EAAE;AAErB,QAAA,IAAA,CAAA,gBAAgB,GAAY,IAAI,CAAC,WAAW;AAQ5C,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AAEpC;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAE3C;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAoB/B;;AAEG;AACsB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAExC;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAE1C;;AAEG;AACsB,QAAA,IAAe,CAAA,eAAA,GAAI,IAAI;AAEhD;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;AAEnB;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AACnC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AAClC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAqC,MAAM;AAElE;;AAEG;AACK,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAE1C;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAEhC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjC;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAmB,QAAQ;AAEhD;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AAElC;;AAEC;AACO,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;AAuK9B,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAI;AACxC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;AAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B,SAAC;AAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,KAA6B,KAAU;AACjE,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC5B,SAAC;AAcO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,SAAC;AAEO,QAAA,IAAU,CAAA,UAAA,GAAG,MAAW;AAC9B,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE;;AAE3C,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;;AAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;gBACtB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE;AACvC,gBAAA,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU;oBAAE,IAAI,CAAC,mBAAmB,EAAE;;AAElE,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC;AACrE,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;AAChD,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAE9B,SAAC;AAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,GAA+B,KAAY;;AACtE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;gBACxF,IAAI,cAAc,EAAE;oBAClB,OAAO,cAAc,CAAC,KAAK;;;AAG/B,YAAA,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,IAAG,GAAG,CAAC,SAAS,IAAI,CAAA,EAAA,GAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AAChE,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAa;YAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;AAC1E,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;AACpC,SAAC;AAEO,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,IAAI,KAAU;YAC1C,MAAM,UAAU,GAAG,CAAA,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAA,MAAA,GAAJ,IAAI,CAAE,MAAM,IAAG,CAAC,IAAI,GAAG,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,aAAA,CAAe;AACrE,YAAA,IAAI,CAAC,eAAe,GAAG,UAAU;AACnC,SAAC;AAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAW;AACtC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;AACzC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;AAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;YACtB,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;gBAC1D,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK;iBACnC,EAAE,EAAE,CAAC;;AAEV,SAAC;AAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAkB,KAAU;YACpD,MAAM,EACJ,MAAM,EAAE,EAAE,OAAO,EAAE,GACpB,GAAG,KAAK;AACT,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBACtC,IAAI,OAAO,EAAE;oBACX,MAAM,CAAC,MAAM,EAAE;;qBACV;oBACL,MAAM,CAAC,OAAO,EAAE;;;YAGpB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;aAC1C,EAAE,EAAE,CAAC;AACR,SAAC;AAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,IAAkC,KAAU;AACvE,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE;gBACzB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;;YAErG,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;YACrF,MAAM,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;gBACjD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,WAAW;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC7B,SAAC;AAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAkB,KAAU;YAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;AACT,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;YAClB,IAAI,CAAC,MAAM,EAAE;AACf,SAAC;AA2BO,QAAA,IAAmB,CAAA,mBAAA,GAAG,YAAW;AACvC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACf,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;AAC3B,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;gBACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AAClC,gBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;AAEnC,SAAC;AAmBO,QAAA,IAAA,CAAA,iBAAiB,GAAG,OAAO,EAAc,KAAI;AACnD,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;AAEhC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;AACtB,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;iBAC3C;AACL,gBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACf,gBAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,oBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;qBAC1B;oBACL,IAAI,CAAC,gBAAgB,EAAE;;;AAI3B,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACzB,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBACpC,IAAI,CAAC,gBAAgB,EAAE;;AAE3B,SAAC;AAqNF;AAliBW,IAAA,aAAa,CAAC,MAAe,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;AACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;aAC9D;AACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;AAErE,QAAA,IAAI,MAAM;AACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;iBACzC;gBACL,IAAI,CAAC,oBAAoB,EAAE;;;IAKjC,mBAAmB,GAAA;QACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;IAIlC,YAAY,GAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;AACvF,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;AAE/C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB;AACxC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;;AAI5B,IAAA,YAAY,CAAC,EAAS,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,MAA0B,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;IAKb,oBAAoB,GAAA;;AAC5B,QAAA,IAAI,CAAC,gBAAgB;YACnB,IAAI,CAAC,aAAa,KAAK;AACrB,kBAAE,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,MAAK,CAAC,IAAI,IAAI,CAAC,cAAc,KAAK;sBAC3D,IAAI,CAAC;AACP,sBAAE;AACJ,kBAAE,IAAI,CAAC,WAAW;AACtB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC;AAC5C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;;IAIlE,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,IAAI;gBACF,IAAI,CAAC,eAAe,GAAG,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO;;YACjG,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;;;IAMrB,mBAAmB,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,gBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;AACrC,oBAAA,MAAM,CAAC,UAAU,GAAG,UAAU;oBAC9B,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC;;qBAC5D;AACL,oBAAA,MAAM,CAAC,UAAU,GAAG,SAAS;oBAC7B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;oBAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;;;IAM/D,iBAAiB,GAAA;QACf,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;;IAGrC,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,gBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;AACrC,oBAAA,MAAM,CAAC,UAAU,GAAG,UAAU;oBAC9B,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC;;qBAC5D;AACL,oBAAA,MAAM,CAAC,UAAU,GAAG,SAAS;oBAC7B,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;oBAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;;AAK7D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;AAC1B,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;aACzC;YACL,IAAI,CAAC,oBAAoB,EAAE;;;AAIvB,IAAA,mBAAmB,CAAC,KAAsC,EAAA;AAChE,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;IAIlC,oBAAoB,GAAA;QAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;YAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;YACtB,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;AACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;YAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;AAgB1C,IAAA,IAAY,YAAY,GAAA;QACtB,OAAO,IAAI,CAAC;AACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;AACrE,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;;AAG/D,IAAA,IAAY,mBAAmB,GAAA;QAC7B,OAAO,IAAI,CAAC;cACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ;cACrG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC;;AA8GzF,IAAA,eAAe,CAAC,KAAK,EAAA;;AAC3B,QAAA,QAAQ,KAAK,CAAC,GAAG;AACf,YAAA,KAAK,OAAO;gBACV,IAAI,CAAC,MAAM,EAAE;gBACb;AACF,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;AAEpB,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,WAA0C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;oBACxF;;AAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,iBAAgD,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;gBAC7E;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,eAA8C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;oBAC5F;;AAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,gBAA+C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;gBAC5E;;;AAeN,IAAA,MAAM,sBAAsB,GAAA;;AAC1B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,EAAE;AACxE,YAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,gBAAA,MAAM,CAAC,OAAO,GAAG,KAAK;AACtB,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;;AAE3C,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK;AAClC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;AAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;YACtB,IAAI,CAAC,kBAAkB,EAAE;;aACpB;YACL,IAAI,CAAC,mBAAmB,EAAE;;;;IA2BtB,gBAAgB,GAAA;QACtB,UAAU,CAAC,MAAK;YACd,IAAI,CAAC,kBAAkB,EAAE;SAC1B,EAAE,GAAG,CAAC;;IAGD,MAAM,aAAa,CAAC,IAAY,EAAA;QACtC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;AAGjC,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC;kBAC7B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,WAAW;AAC5C,kBAAE,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE;AAEpC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE;AAEpC,YAAA,mBAAmB,CAAC,QAAQ,CAAC,SAAS;AACpC,kBAAE,MAAM,CAAC,eAAe,CAAC,WAAW;kBAClC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;;AAI7C,IAAA,MAAM,kBAAkB,GAAA;AAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,QAAA,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE;AACjC,YAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;;IAI/B,gBAAgB,GAAA;;QACtB,OAAO,MAAA,IAAI,CAAC,mBAAmB,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,KAAK;;IAGhC,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;IAGlB,MAAM,GAAA;;AACJ,QAAA,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,IAAI;AACZ,gBAAA,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM;AACpC,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;gBAC5B,gBAAgB,EAAE,IAAI,CAAC,SAAS;aACjC,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAA,EAE3B,IAAI,CAAC,UAAU,EAAE,EAClB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,kBAAkB,EAAC,QAAQ,EAAC,GAAG,EAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAA,EACnE,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC5C,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,0CAAE,MAAM,IAAG,CAAC,KAC/B,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,mBAAmB,IAChD,IAAI,CAAC,eAAe,CACZ,CACZ,EACD,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAClC,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,IAAI,CAAC,IAAI,eACL,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAC1C,CACE,CACF,EACN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvB,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,KAAK,EAAC,OAAO,EACb,OAAO,EAAE,IAAI,CAAC,mBAAmB,EACjC,KAAK,EAAE;gBACL,aAAa,EAAE,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;AAC3F,aAAA,EACS,CAAA,EACZ,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAY,CAAA,CACjF,CACF,EACL,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,OAAO,IACX,CAAA,CAAA,KAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;gBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;AACrC,aAAA,EAAA,EAED,CAAA,CAAA,qBAAA,EAAA,EAAqB,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAA,CAAuB,CACzE,KAEN,CAAA,CAAA,KAAA,EAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;gBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;AACrC,aAAA,EAAA,EAEA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,KACtD,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EAC1D,IAAI,CAAC,cAAc,CACX,CACZ,EACA,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,KACnD,oBACE,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAC1B,KAAK,EAAE,CAAmB,iBAAA,CAAA,EAC1B,KAAK,EAAE,CAAkB,gBAAA,CAAA,EACzB,IAAI,EAAC,WAAW,EAChB,KAAK,EAAC,YAAY,EAClB,WAAW,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EAAA,CAC9B,CACjB,EACA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,MAAM,IAAG,CAAC,KAC9B,CAAM,CAAA,MAAA,EAAA,EAAA,KAAK,EAAC,iBAAiB,EAAA,EAC3B,CAAA,CAAA,MAAA,EAAA,EAAM,KAAK,EAAC,SAAS,EAAA,CAAQ,CACxB,CACR,EACA,IAAI,CAAC,eAAe,IACnB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,MACnC,CAAA,CAAA,mBAAA,EAAA,EACE,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAC9B,eAAe,EAAE,IAAI,CAAC,kBAAkB,EACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,EACrC,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,MAAM,CAAC,UAAU,EAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EACR,aAAA,EAAA,IAAI,CAAC,aAAa,IAAI,UAAU,GAAG,UAAU,GAAG,SAAS,EAAA,EAErE,MAAM,CAAC,KAAK,CACK,CACrB,CAAC,KAEF,CAAA,CAAA,MAAA,EAAA,IAAA,CAAQ,CACT,CACG,CACP,CACI;;;;;;;;;;;;;;;;"}