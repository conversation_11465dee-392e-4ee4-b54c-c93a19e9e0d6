var __awaiter=this&&this.__awaiter||function(t,e,n,r){function i(t){return t instanceof n?t:new n((function(e){e(t)}))}return new(n||(n=Promise))((function(n,o){function a(t){try{s(r.next(t))}catch(t){o(t)}}function c(t){try{s(r["throw"](t))}catch(t){o(t)}}function s(t){t.done?n(t.value):i(t.value).then(a,c)}s((r=r.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(t){return function(e){return s([t,e])}}function s(c){if(r)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(n=0)),n)try{if(r=1,i&&(o=c[0]&2?i["return"]:c[0]?i["throw"]||((o=i["return"])&&o.call(i),0):i.next)&&!(o=o.call(i,c[1])).done)return o;if(i=0,o)c=[c[0]&2,o.value];switch(c[0]){case 0:case 1:o=c;break;case 4:n.label++;return{value:c[1],done:false};case 5:n.label++;i=c[1];c=[0];continue;case 7:c=n.ops.pop();n.trys.pop();continue;default:if(!(o=n.trys,o=o.length>0&&o[o.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!o||c[1]>o[0]&&c[1]<o[3])){n.label=c[1];break}if(c[0]===6&&n.label<o[1]){n.label=o[1];o=c;break}if(o&&n.label<o[2]){n.label=o[2];n.ops.push(c);break}if(o[2])n.ops.pop();n.trys.pop();continue}c=e.call(t,n)}catch(t){c=[6,t];i=0}finally{r=o=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,n,r,i;return{setters:[function(t){e=t.r;n=t.c;r=t.h;i=t.H}],execute:function(){var o=".bds-tab{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;-webkit-box-sizing:content-box;box-sizing:content-box;min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;max-width:270px;height:46px;max-height:48px;cursor:pointer;text-align:center;color:var(--color-content-disable, rgb(89, 89, 89));border-bottom:2px solid transparent}.bds-tab:not(:last-child){margin-right:32px}.bds-tab:hover{color:var(--color-content-default, rgb(40, 40, 40))}.bds-tab--selected{-webkit-animation-name:selectFade;animation-name:selectFade;-webkit-animation-duration:0.75s;animation-duration:0.75s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}.bds-tab__text{min-width:90px;max-width:270px}@-webkit-keyframes selectFade{from{border-bottom:2px solid transparent;color:var(--color-content-default, rgb(40, 40, 40))}to{border-bottom:2px solid var(--color-brand, rgb(0, 150, 250));color:var(--color-content-default, rgb(40, 40, 40))}}@keyframes selectFade{from{border-bottom:2px solid transparent;color:var(--color-content-default, rgb(40, 40, 40))}to{border-bottom:2px solid var(--color-brand, rgb(0, 150, 250));color:var(--color-content-default, rgb(40, 40, 40))}}@media (max-width: 599px){.bds-tab{min-width:110px;text-overflow:ellipsis}}";var a=t("bds_tab",function(){function t(t){e(this,t);this.bdsTabChange=n(this,"bdsTabChange");this.active=false;this.isActive=false}t.prototype.handleTabChange=function(t){this.isActive=t.detail==this.group};t.prototype.onClick=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.bdsTabChange.emit(this.group);return[2]}))}))};t.prototype.render=function(){var t;var e=this.isActive?"bold":"regular";return r(i,{key:"8f7ec6e0536a51a222c2b26e491542b0d921b650",class:(t={"bds-tab":true},t["bds-tab--selected"]=this.isActive,t),onClick:this.onClick.bind(this)},r("div",{key:"4e1dc9282dc14c436959119d51d36f9bc6bcb6be",class:"bds-tab__text"},r("bds-typo",{key:"a555ee8e27ae9711ae9d6081d98ee3f1fc546f7f",variant:"fs-16",bold:e},this.label)))};return t}());a.style=o}}}));
//# sourceMappingURL=p-94c12be2.system.entry.js.map