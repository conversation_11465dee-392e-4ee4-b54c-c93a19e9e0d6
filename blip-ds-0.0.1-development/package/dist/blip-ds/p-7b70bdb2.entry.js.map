{"version": 3, "names": ["carouselItemCss", "BdsCarouselItem", "constructor", "hostRef", "this", "theme", "bgImageBrightness", "render", "h", "Host", "key", "class", "style", "background", "bgColor", "bgImage", "alt", "width", "height", "brightness", "src"], "sources": ["src/components/carousel/carousel-item.scss?tag=bds-carousel-item&encapsulation=shadow", "src/components/carousel/carousel-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  box-sizing: border-box;\n  width: auto;\n}\n\n.carrosel-item-frame {\n  display: block;\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  position: relative;\n  overflow: hidden;\n\n  ::slotted(*) {\n    position: relative;\n  }\n}\n\n.image-bg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\nimport { Themes } from '../theme-provider/theme-provider';\n@Component({\n  tag: 'bds-carousel-item',\n  styleUrl: 'carousel-item.scss',\n  shadow: true,\n})\nexport class BdsCarouselItem {\n  /**\n   * Set what theme will be aplyed inside the component.\n   * 'light', 'dark';\n   */\n  @Prop() theme?: Themes = 'light';\n\n  @Prop() bgImage?: string;\n\n  @Prop() bgImageBrightness?: number = 1;\n\n  @Prop() bgColor?: string;\n\n  render(): HTMLElement {\n    return (\n      <Host class=\"carrosel-item\">\n        <bds-theme-provider theme={this.theme} class=\"carrosel-item-frame\" style={{ background: this.bgColor }}>\n          {this.bgImage && (\n            <bds-image\n              class=\"image-bg\"\n              alt=\"Example of a image\"\n              width=\"100%\"\n              height=\"100%\"\n              brightness={this.bgImageBrightness}\n              object-fit=\"cover\"\n              src={this.bgImage}\n            />\n          )}\n          <slot />\n        </bds-theme-provider>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAkB,2S,MCOXC,EAAe,MAL5B,WAAAC,CAAAC,G,UAUUC,KAAKC,MAAY,QAIjBD,KAAiBE,kBAAY,CAwBtC,CApBC,MAAAC,GACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAM,iBACVH,EAAA,sBAAAE,IAAA,2CAAoBL,MAAOD,KAAKC,MAAOM,MAAM,sBAAsBC,MAAO,CAAEC,WAAYT,KAAKU,UAC1FV,KAAKW,SACJP,EAAA,aAAAE,IAAA,2CACEC,MAAM,WACNK,IAAI,qBACJC,MAAM,OACNC,OAAO,OACPC,WAAYf,KAAKE,kBAAiB,aACvB,QACXc,IAAKhB,KAAKW,UAGdP,EAAQ,QAAAE,IAAA,8C", "ignoreList": []}