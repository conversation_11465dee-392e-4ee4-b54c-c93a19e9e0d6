var __awaiter=this&&this.__awaiter||function(t,e,i,n){function r(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,o){function a(t){try{c(n.next(t))}catch(t){o(t)}}function s(t){try{c(n["throw"](t))}catch(t){o(t)}}function c(t){t.done?i(t.value):r(t.value).then(a,s)}c((n=n.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var i={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,r,o,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(t){return function(e){return c([t,e])}}function c(s){if(n)throw new TypeError("Generator is already executing.");while(a&&(a=0,s[0]&&(i=0)),i)try{if(n=1,r&&(o=s[0]&2?r["return"]:s[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;if(r=0,o)s=[s[0]&2,o.value];switch(s[0]){case 0:case 1:o=s;break;case 4:i.label++;return{value:s[1],done:false};case 5:i.label++;r=s[1];s=[0];continue;case 7:s=i.ops.pop();i.trys.pop();continue;default:if(!(o=i.trys,o=o.length>0&&o[o.length-1])&&(s[0]===6||s[0]===2)){i=0;continue}if(s[0]===3&&(!o||s[1]>o[0]&&s[1]<o[3])){i.label=s[1];break}if(s[0]===6&&i.label<o[1]){i.label=o[1];o=s;break}if(o&&i.label<o[2]){i.label=o[2];i.ops.push(s);break}if(o[2])i.ops.pop();i.trys.pop();continue}s=e.call(t,i)}catch(t){s=[6,t];r=0}finally{n=o=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,n;return{setters:[function(t){e=t.r;i=t.c;n=t.h}],execute:function(){var r=".alert__dialog{opacity:0;visibility:hidden;background-color:rgba(0, 0, 0, 0.7);width:100%;height:100%;position:fixed;top:0;left:0;-webkit-transition:opacity 0.3s ease-in-out;transition:opacity 0.3s ease-in-out;z-index:80000}.alert__dialog .alert{position:relative;margin:48px auto 0;overflow:hidden;max-width:424px;border-radius:8px;background:var(--color-surface-1, rgb(246, 246, 246));-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16))}.alert__dialog--open{opacity:1;visibility:visible}.alert__dialog--fixed{position:fixed}.alert__dialog--contain{position:absolute}";var o=t("bds_alert",function(){function t(t){var n=this;e(this,t);this.bdsAlertChanged=i(this,"bdsAlertChanged");this.open=false;this.dataTest=null;this.position="fixed";this.listener=function(t){if(t.key=="Enter"||t.key=="Escape"){n.toggle()}}}t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.open=!this.open;if(this.open){this.bdsAlertChanged.emit({alertStatus:"opened"})}else{this.bdsAlertChanged.emit({alertStatus:"closed"})}return[2]}))}))};t.prototype.isOpenChanged=function(){if(this.open){document.addEventListener("keydown",this.listener,false)}else document.removeEventListener("keydown",this.listener,false)};t.prototype.render=function(){var t;return n("div",{key:"ac8c0b4e12840f60698d573b221ec689645bdd11",class:(t={alert__dialog:true,"alert__dialog--open":this.open},t["alert__dialog--".concat(this.position)]=true,t)},n("div",{key:"1130ed827571cfad2827e36f73cb43436d3a48f6",class:"alert","data-test":this.dataTest},n("slot",{key:"828e303a6465c71579e6ff2082c7a899669d43d8"})))};Object.defineProperty(t,"watchers",{get:function(){return{open:["isOpenChanged"]}},enumerable:false,configurable:true});return t}());o.style=r}}}));
//# sourceMappingURL=p-ab1d55b4.system.entry.js.map