import{r as t,c as s,h as e,H as i,a as n}from"./p-C3J6Z5OX.js";const a=".bds-tabs{width:100%;display:-ms-flexbox;display:flex;z-index:1100;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-negative:0;flex-shrink:0;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;height:48px;padding:0 10px 0 10px}.bds-tabs--center{-ms-flex-pack:center;justify-content:center}.bds-tabs--left{-ms-flex-pack:start;justify-content:flex-start}.bds-tabs--right{-ms-flex-pack:end;justify-content:flex-end}.bds-tabs .bds-tabs__header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;overflow:hidden;-ms-flex-align:stretch;align-items:stretch;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.bds-tabs .bds-tabs__header-button-container{padding:0px;min-width:40px}";const o=class{constructor(e){t(this,e);this.scrollButtonClick=s(this,"scrollButtonClick");this.bdsTabInit=s(this,"bdsTabInit");this.SCROLL_BEHAVIOR="smooth";this.align="center";this.handleHeaderResize=()=>{if(this.tabsHeaderChildElement.offsetWidth<this.tabsHeaderChildElement.scrollWidth){this.updateButtonsVisibility(true)}else{this.updateButtonsVisibility(false)}};this.updateButtonsVisibility=t=>{this.setLeftButtonVisibility(t);this.setRightButtonVisibility(t)};this.handleScrollButtonClick=t=>{this.scrollButtonClick.emit({direction:t})}}onScrollButtonClick(t){var s;t.preventDefault();const e={behavior:this.SCROLL_BEHAVIOR,top:0,left:t.detail.distance};(s=e.left)!==null&&s!==void 0?s:e.left=this.getDistance(e,t);this.tabsHeaderChildElement.scrollTo(e)}onSelectedTab(t){this.handleButtonOverlay(t.detail)}componentDidLoad(){this.getChildElements();this.attachEvents();this.setLeftButtonVisibility(false);this.setRightButtonVisibility(true);this.handleActiveTab()}handleActiveTab(){const t=Array.from(this.tabsHeaderChildElement.getElementsByTagName("bds-tab"));const s=t.find((t=>t.active));if(s){this.bdsTabInit.emit(s.group)}else{const[s]=t;this.bdsTabInit.emit(s.group)}}getChildElements(){this.tabsHeaderChildElement=this.el.querySelector(".bds-tabs__header");this.leftButtonChildElement=this.el.querySelector("#bds-tabs-button-left");this.rightButtonChildElement=this.el.querySelector("#bds-tabs-button-right")}attachEvents(){window.onresize=this.handleHeaderResize;this.tabsHeaderChildElement.onscroll=()=>this.updateButtonsVisibility(this.tabsHeaderChildElement.scrollWidth>this.tabsHeaderChildElement.clientWidth)}setRightButtonVisibility(t){if(t&&this.tabsHeaderChildElement.scrollWidth>Math.ceil(this.tabsHeaderChildElement.scrollLeft+this.tabsHeaderChildElement.clientWidth)){this.rightButtonChildElement.style.display="block"}else{this.rightButtonChildElement.style.display="none"}}setLeftButtonVisibility(t){this.leftButtonChildElement.style.display=this.tabsHeaderChildElement.scrollLeft>0&&t?"block":"none"}handleButtonOverlay(t){const s=Array.from(this.tabsHeaderChildElement.getElementsByTagName("bds-tab")).find((s=>s.group==t));const e=[this.leftButtonChildElement,this.rightButtonChildElement];e.forEach((t=>{if(this.isButtonOverlappingTab(t,s)){const e=this.getAdjutScrollDistance(t,s);this.scrollButtonClick.emit({distance:e})}}))}isButtonOverlappingTab(t,s){const e=s.getBoundingClientRect();const i=t.getBoundingClientRect();return this.elementIsOverlapping(i,e)}elementIsOverlapping(t,s){const e=t.x;const i=t.x+t.width;const n=s.x;const a=s.x+s.width;return e>=n&&e<=a||i>=n&&i<=a}getAdjutScrollDistance(t,s){const e=t.id=="bds-tabs-button-left"?"left":"right";const i=s.clientWidth+parseInt(getComputedStyle(s).marginRight)-t.offsetWidth;if(e=="right"){return s.parentElement.scrollLeft+i}else{return s.parentElement.scrollLeft-i}}getDistance(t,s){return s.detail.direction=="right"?t.left=this.tabsHeaderChildElement.scrollLeft+this.tabsHeaderChildElement.clientWidth:t.left=this.tabsHeaderChildElement.scrollLeft-this.tabsHeaderChildElement.clientWidth}render(){return e(i,{key:"e344015c075a0f42260d9dec090e7ce9b37ce308",class:{"bds-tabs":true,[`bds-tabs--${this.align}`]:true}},e("div",{key:"10e0dc4985640367dd72e8c12d33fa062c59b25f",class:"bds-tabs__header-button-container"},e("bds-button-icon",{key:"7563383118dc3374923b284e979e90ac4c426e7f",class:"bds-tabs__header-button",icon:"arrow-left",size:"short",id:"bds-tabs-button-left",onClick:()=>this.handleScrollButtonClick("left"),variant:"secondary"})),e("div",{key:"5a929ca2b47aebb6b3390d63d24fb1382a8a2e50",class:"bds-tabs__header"},e("slot",{key:"4096ffc7f4a6ea5d9927d1674ee21915d64bad8f"})),e("div",{key:"ea79f43c91c548369f4d313e64243100560a1c2a",class:"bds-tabs__header-button-container"},e("bds-button-icon",{key:"1cfe8731e4b3844ba6ff060d255fa72c262a3e16",class:"bds-tabs__header-button",icon:"arrow-right",size:"short",id:"bds-tabs-button-right",onClick:()=>this.handleScrollButtonClick("right"),variant:"secondary"})))}get el(){return n(this)}};o.style=a;export{o as bds_tabs};
//# sourceMappingURL=p-474e14fb.entry.js.map