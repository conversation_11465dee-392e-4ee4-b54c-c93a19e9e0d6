{"version": 3, "file": "p-DZ0t5BHt.system.js", "sources": ["src/components/expansion-panel/expansion-panel-header/expansion-panel-header.scss?tag=bds-expansion-panel-header&encapsulation=shadow", "src/components/expansion-panel/expansion-panel-header/expansion-panel-header.tsx"], "sourcesContent": [":host {\n  display: flex;\n  align-items: center;\n}\n\n.header {\n  width: 70px;\n  padding-right: 6px;\n}", "import { Component, Host, h, ComponentInterface, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel-header',\n  styleUrl: 'expansion-panel-header.scss',\n  shadow: true,\n})\nexport class ExpansionPanelHeader implements ComponentInterface {\n  @Prop() text?: string;\n\n  render() {\n    return (\n      <Host>\n        <div class=\"header\">\n          <slot />\n        </div>\n        <bds-typo tag=\"p\" variant=\"fs-12\">\n          {this.text}\n        </bds-typo>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;MAAA,MAAM,uBAAuB,GAAG,uHAAuH;;YCO1I,oBAAoB,yCAAA,MAAA;;;;UAG/B,MAAM,GAAA;MACJ,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,QAAQ,EAAA,EACjB,CAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACJ,EACN,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,GAAG,EAAC,GAAG,EAAC,OAAO,EAAC,OAAO,EAC9B,EAAA,IAAI,CAAC,IAAI,CACD,CACN;;;;;;;;;;;"}