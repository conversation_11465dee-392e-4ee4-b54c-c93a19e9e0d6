{"version": 3, "names": ["en_US", "itemsPerPage", "of", "items", "pages", "pt_BR", "es_MX", "paginationCss", "Pagination", "constructor", "hostRef", "this", "value", "startedPage", "paginationNumbers", "intoView", "optionsPosition", "pageCounter", "language", "dtButtonInitial", "dtButtonPrev", "dtSelectNumber", "dtButtonNext", "dtButtonEnd", "nextPage", "event", "el", "preventDefault", "updateItemRange", "previewPage", "firstPage", "lastPage", "openOptions", "openSelect", "onBlur", "componentWillLoad", "countPage", "getScrollParent", "processItemsPage", "itemValue", "itemsPage", "itemSelected", "countItem", "pagesChanged", "valueChanged", "bdsPaginationChange", "emit", "JSON", "parse", "replace", "error", "numberItems", "Math", "ceil", "length", "i", "push", "optionSelected", "index", "bdsItemsPerPageChange", "startItem", "endItem", "min", "currentLanguage", "render", "h", "Host", "key", "class", "full_width", "gap", "variant", "_a", "map", "onClick", "onBdsClick", "ev", "size", "icon", "dataTest"], "sources": ["src/components/pagination/languages.tsx", "src/components/pagination/pagination.scss?tag=bds-pagination&encapsulation=shadow", "src/components/pagination/pagination.tsx"], "sourcesContent": ["export const en_US = \n    {\n        itemsPerPage: 'Items per page',\n        of: 'of',\n        items: 'items',\n        pages: 'pages'\n    }\n;\n\nexport const pt_BR = \n    {\n        itemsPerPage: 'Itens por página',\n        of: 'de',\n        items: 'itens',\n        pages: 'páginas'\n    }\n;\n\nexport const es_MX = \n    {\n        itemsPerPage: 'Itens por página',\n        of: 'de',\n        items: 'itens',\n        pages: 'páginas'\n    }\n;", "@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n\n  .actions {\n    &_select {\n      width: 74px;\n    }\n  }\n}\n\n:host(.full_width) {\n  width: 100%;\n}\n\n@media screen and (max-width: 905px) {\n  .items_per_page {\n    display: none;\n  }\n  .actions {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n@media screen and (max-width: 600px) {\n  .actions {\n    &--text {\n      display: none;\n    }\n  }\n}\n", "import { Component, Host, h, Element, Prop, State, Event, EventEmitter, Watch } from '@stencil/core';\nimport { getScrollParent } from '../../utils/position-element';\nimport { pt_BR, en_US, es_MX } from './languages';\n\nexport type PaginationOptionsPositionType = 'auto' | 'top' | 'bottom';\n@Component({\n  tag: 'bds-pagination',\n  styleUrl: 'pagination.scss',\n  shadow: true,\n})\nexport class Pagination {\n  // Elemento HTML nativo onde o componente será renderizado\n  @Element() private el!: HTMLElement;\n\n  /**\n   * Estado que armazena o valor selecionado no seletor de página.\n   * Inicialmente, é configurado com a página inicial (startedPage).\n   */\n  @State() value: number = this.startedPage;\n\n  // Estado que armazena o valor selecionado no seletor de itens por página\n  @State() itemValue: number;\n\n  /**\n   * Estado que controla se o seletor de opções de página está aberto ou fechado.\n   */\n  @State() openSelect: boolean;\n\n  /**\n   * Estado que armazena o número de páginas, gerado com base no total de itens e itens por página.\n   */\n  @State() paginationNumbers = [];\n\n  // Estado que armazena o número de itens por página selecionado\n  @State() itemsPerPage: number;\n\n  // Estado que guarda o elemento pai com rolagem (se houver)\n  @State() intoView?: HTMLElement = null;\n\n  /**\n   * Propriedade para receber o número total de páginas, baseado no total de itens e itens por página.\n   */\n  @Prop({ mutable: true, reflect: true }) pages?: number;\n\n  /**\n   * Propriedade que define a página inicial ao renderizar o componente.\n   */\n  @Prop() startedPage?: number;\n\n  /**\n   * Define a posição do menu de opções. Pode ser 'bottom' ou 'top'.\n   * Padrão é 'auto', que ajusta automaticamente a posição.\n   */\n  @Prop() optionsPosition?: PaginationOptionsPositionType = 'auto';\n\n  // Propriedade que controla se o contador de páginas será exibido\n  @Prop() pageCounter?: boolean = false;\n\n  // Propriedade para receber as opções de itens por página (por exemplo, [10, 20, 30])\n  @Prop({ mutable: true, reflect: true }) itemsPage?: any;\n\n  // Propriedade que define o número total de itens que serão paginados\n  @Prop() numberItems?: number;\n\n  // Propriedade para definir o idioma do componente (opcional)\n  @Prop() language?: string = 'pt_BR';\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão inicial.\n   * dtButtonInitial é o data-test para o botão inicial.\n   */\n  @Prop() dtButtonInitial?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de página anterior.\n   * dtButtonPrev é o data-test para o botão anterior.\n   */\n  @Prop() dtButtonPrev?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar o seletor de número de páginas.\n   * dtSelectNumber é o data-test para o seletor de número de páginas.\n   */\n  @Prop() dtSelectNumber?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão de próxima página.\n   * dtButtonNext é o data-test para o botão próximo.\n   */\n  @Prop() dtButtonNext?: string = null;\n\n  /**\n   * Propriedade de teste para especificamente testar a ação do botão final.\n   * dtButtonEnd é o data-test para o botão final.\n   */\n  @Prop() dtButtonEnd?: string = null;\n\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsPaginationChange: EventEmitter;\n  /**\n   * Evento emitido quando o valor da página atual é alterado.\n   * Pode ser escutado para realizar ações específicas ao mudar de página.\n   */\n  @Event() bdsItemsPerPageChange: EventEmitter;\n\n  // Variável que armazena o número do primeiro item sendo exibido na página atual\n  startItem: number;\n\n  // Variável que armazena o número do último item sendo exibido na página atual\n  endItem: number;\n\n  componentWillLoad() {\n    this.countPage();\n    this.intoView = getScrollParent(this.el);\n    this.processItemsPage();\n    if (this.pageCounter) {\n      this.itemValue = this.itemsPage[0];\n    }\n    this.itemSelected(this.itemValue);\n    this.countItem();\n  }\n\n  @Watch('pages')\n  @Watch('startedPage')\n  pagesChanged(): void {\n    this.countPage();\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsPaginationChange.emit(this.value);\n  }\n\n  processItemsPage() {\n    if (typeof this.itemsPage === 'string') {\n      try {\n        this.itemsPage = JSON.parse(this.itemsPage.replace(/'/g, '\"'));\n      } catch (error) {\n        this.itemsPage = [];\n      }\n    }\n  }\n\n  countItem() {\n    if (this.pageCounter) {\n      const pages = this.numberItems / this.itemValue;\n      this.pages = Math.ceil(pages);\n    }\n  }\n\n  countPage() {\n    if (this.paginationNumbers.length !== 0) {\n      this.paginationNumbers = [];\n    }\n    if (this.paginationNumbers.length === 0) {\n      for (let i = 1; i <= this.pages; i++) {\n        this.paginationNumbers.push(i);\n      }\n      if (this.startedPage && this.startedPage < this.pages) {\n        this.value = this.startedPage;\n      } else {\n        this.value = this.paginationNumbers[0];\n      }\n    }\n  }\n\n  nextPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.value + 1;\n      this.updateItemRange();\n    }\n  };\n\n  previewPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.value - 1;\n      this.updateItemRange();\n    }\n  };\n\n  firstPage = (event: Event) => {\n    const el = this.value;\n    if (el > 1) {\n      event.preventDefault();\n      this.value = this.paginationNumbers[0];\n      this.updateItemRange();\n    }\n  };\n\n  lastPage = (event: Event) => {\n    const el = this.value;\n    if (el < this.pages) {\n      event.preventDefault();\n      this.value = this.pages;\n      this.updateItemRange();\n    }\n  };\n\n  openOptions = () => {\n    this.openSelect = !this.openSelect;\n  };\n\n  onBlur = () => {\n    this.openSelect = false;\n  };\n\n  optionSelected(index) {\n    this.value = index;\n    this.openOptions();\n    this.updateItemRange();\n  }\n\n  @Watch('itemValue')\n  itemSelected(index) {\n    this.itemValue = index;\n    this.itemsPerPage = index;\n    this.openOptions();\n    this.countItem();\n    this.updateItemRange();\n    this.bdsItemsPerPageChange.emit(this.itemsPerPage);\n  }\n\n  updateItemRange() {\n    this.startItem = (this.value - 1) * this.itemsPerPage + 1;\n    this.endItem = Math.min(this.value * this.itemsPerPage, this.numberItems);\n  }\n\n  get currentLanguage() {\n    switch (this.language) {\n      case 'en_US':\n        return en_US;\n      case 'es_MX':\n        return es_MX;\n      default:\n        return pt_BR;\n    }\n  }\n\n  render() {\n    const { currentLanguage } = this;\n    return (\n      <Host class={{ full_width: this.pageCounter }}>\n        <bds-grid justify-content=\"space-between\">\n          {this.itemsPerPage && this.itemsPage && (\n            <bds-grid gap=\"1\" align-items=\"center\" class=\"items_per_page\">\n              <bds-typo variant=\"fs-14\">{currentLanguage.itemsPerPage}:</bds-typo>\n              <bds-select class=\"actions_select\" value={this.itemValue} options-position={this.optionsPosition}>\n                {this.itemsPage?.map((el, index) => (\n                  <bds-select-option key={index} value={el} onClick={() => this.itemSelected(el)}>\n                    {el}\n                  </bds-select-option>\n                ))}\n              </bds-select>\n              <bds-typo variant=\"fs-14\" no-wrap=\"true\">\n                {this.startItem}-{this.endItem} {currentLanguage.of} {this.numberItems}\n              </bds-typo>\n            </bds-grid>\n          )}\n\n          <bds-grid gap=\"1\" align-items=\"center\" class=\"actions\">\n            <bds-button-icon\n              onBdsClick={(ev) => this.firstPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-first\"\n              dataTest={this.dtButtonInitial}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.previewPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-left\"\n              dataTest={this.dtButtonPrev}\n            ></bds-button-icon>\n\n            <bds-select class=\"actions_select\" value={this.value} options-position={this.optionsPosition}>\n              {this.paginationNumbers.map((el, index) => (\n                <bds-select-option key={index} value={el} onClick={() => this.optionSelected(el)}>\n                  {el}\n                </bds-select-option>\n              ))}\n            </bds-select>\n            {this.pageCounter && (\n              <bds-typo class=\"actions--text\" variant=\"fs-14\" no-wrap=\"true\">\n                {currentLanguage.of} {this.pages} {currentLanguage.pages}\n              </bds-typo>\n            )}\n            <bds-button-icon\n              onBdsClick={(ev) => this.nextPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-right\"\n              dataTest={this.dtButtonNext}\n            ></bds-button-icon>\n            <bds-button-icon\n              onBdsClick={(ev) => this.lastPage(ev)}\n              size=\"short\"\n              variant=\"secondary\"\n              icon=\"arrow-last\"\n              dataTest={this.dtButtonEnd}\n            ></bds-button-icon>\n          </bds-grid>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "+FAAO,MAAMA,EACT,CACIC,aAAc,iBACdC,GAAI,KACJC,MAAO,QACPC,MAAO,SAIR,MAAMC,EACT,CACIJ,aAAc,mBACdC,GAAI,KACJC,MAAO,QACPC,MAAO,WAIR,MAAME,EACT,CACIL,aAAc,mBACdC,GAAI,KACJC,MAAO,QACPC,MAAO,WCvBf,MAAMG,EAAgB,2R,MCUTC,EAAU,MALvB,WAAAC,CAAAC,G,4HAaWC,KAAAC,MAAgBD,KAAKE,YAarBF,KAAiBG,kBAAG,GAMpBH,KAAQI,SAAiB,KAgB1BJ,KAAeK,gBAAmC,OAGlDL,KAAWM,YAAa,MASxBN,KAAQO,SAAY,QAMpBP,KAAeQ,gBAAY,KAM3BR,KAAYS,aAAY,KAMxBT,KAAcU,eAAY,KAM1BV,KAAYW,aAAY,KAMxBX,KAAWY,YAAY,KA0E/BZ,KAAAa,SAAYC,IACV,MAAMC,EAAKf,KAAKC,MAChB,GAAIc,EAAKf,KAAKP,MAAO,CACnBqB,EAAME,iBACNhB,KAAKC,MAAQD,KAAKC,MAAQ,EAC1BD,KAAKiB,iB,GAITjB,KAAAkB,YAAeJ,IACb,MAAMC,EAAKf,KAAKC,MAChB,GAAIc,EAAK,EAAG,CACVD,EAAME,iBACNhB,KAAKC,MAAQD,KAAKC,MAAQ,EAC1BD,KAAKiB,iB,GAITjB,KAAAmB,UAAaL,IACX,MAAMC,EAAKf,KAAKC,MAChB,GAAIc,EAAK,EAAG,CACVD,EAAME,iBACNhB,KAAKC,MAAQD,KAAKG,kBAAkB,GACpCH,KAAKiB,iB,GAITjB,KAAAoB,SAAYN,IACV,MAAMC,EAAKf,KAAKC,MAChB,GAAIc,EAAKf,KAAKP,MAAO,CACnBqB,EAAME,iBACNhB,KAAKC,MAAQD,KAAKP,MAClBO,KAAKiB,iB,GAITjB,KAAWqB,YAAG,KACZrB,KAAKsB,YAActB,KAAKsB,UAAU,EAGpCtB,KAAMuB,OAAG,KACPvB,KAAKsB,WAAa,KAAK,CAuG1B,CAvMC,iBAAAE,GACExB,KAAKyB,YACLzB,KAAKI,SAAWsB,EAAgB1B,KAAKe,IACrCf,KAAK2B,mBACL,GAAI3B,KAAKM,YAAa,CACpBN,KAAK4B,UAAY5B,KAAK6B,UAAU,E,CAElC7B,KAAK8B,aAAa9B,KAAK4B,WACvB5B,KAAK+B,W,CAKP,YAAAC,GACEhC,KAAKyB,W,CAIP,YAAAQ,GACEjC,KAAKkC,oBAAoBC,KAAKnC,KAAKC,M,CAGrC,gBAAA0B,GACE,UAAW3B,KAAK6B,YAAc,SAAU,CACtC,IACE7B,KAAK6B,UAAYO,KAAKC,MAAMrC,KAAK6B,UAAUS,QAAQ,KAAM,K,CACzD,MAAOC,GACPvC,KAAK6B,UAAY,E,GAKvB,SAAAE,GACE,GAAI/B,KAAKM,YAAa,CACpB,MAAMb,EAAQO,KAAKwC,YAAcxC,KAAK4B,UACtC5B,KAAKP,MAAQgD,KAAKC,KAAKjD,E,EAI3B,SAAAgC,GACE,GAAIzB,KAAKG,kBAAkBwC,SAAW,EAAG,CACvC3C,KAAKG,kBAAoB,E,CAE3B,GAAIH,KAAKG,kBAAkBwC,SAAW,EAAG,CACvC,IAAK,IAAIC,EAAI,EAAGA,GAAK5C,KAAKP,MAAOmD,IAAK,CACpC5C,KAAKG,kBAAkB0C,KAAKD,E,CAE9B,GAAI5C,KAAKE,aAAeF,KAAKE,YAAcF,KAAKP,MAAO,CACrDO,KAAKC,MAAQD,KAAKE,W,KACb,CACLF,KAAKC,MAAQD,KAAKG,kBAAkB,E,GAiD1C,cAAA2C,CAAeC,GACb/C,KAAKC,MAAQ8C,EACb/C,KAAKqB,cACLrB,KAAKiB,iB,CAIP,YAAAa,CAAaiB,GACX/C,KAAK4B,UAAYmB,EACjB/C,KAAKV,aAAeyD,EACpB/C,KAAKqB,cACLrB,KAAK+B,YACL/B,KAAKiB,kBACLjB,KAAKgD,sBAAsBb,KAAKnC,KAAKV,a,CAGvC,eAAA2B,GACEjB,KAAKiD,WAAajD,KAAKC,MAAQ,GAAKD,KAAKV,aAAe,EACxDU,KAAKkD,QAAUT,KAAKU,IAAInD,KAAKC,MAAQD,KAAKV,aAAcU,KAAKwC,Y,CAG/D,mBAAIY,GACF,OAAQpD,KAAKO,UACX,IAAK,QACH,OAAOlB,EACT,IAAK,QACH,OAAOM,EACT,QACE,OAAOD,E,CAIb,MAAA2D,G,MACE,MAAMD,gBAAEA,GAAoBpD,KAC5B,OACEsD,EAACC,EAAI,CAAAC,IAAA,2CAACC,MAAO,CAAEC,WAAY1D,KAAKM,cAC9BgD,EAAA,YAAAE,IAAA,6DAA0B,iBACvBxD,KAAKV,cAAgBU,KAAK6B,WACzByB,EAAU,YAAAE,IAAA,2CAAAG,IAAI,IAAG,cAAa,SAASF,MAAM,kBAC3CH,EAAU,YAAAE,IAAA,2CAAAI,QAAQ,SAASR,EAAgB9D,aAAyB,KACpEgE,EAAY,cAAAE,IAAA,2CAAAC,MAAM,iBAAiBxD,MAAOD,KAAK4B,UAAS,mBAAoB5B,KAAKK,kBAC9EwD,EAAA7D,KAAK6B,aAAS,MAAAgC,SAAA,SAAAA,EAAEC,KAAI,CAAC/C,EAAIgC,IACxBO,EAAA,qBAAmBE,IAAKT,EAAO9C,MAAOc,EAAIgD,QAAS,IAAM/D,KAAK8B,aAAaf,IACxEA,MAIPuC,EAAA,YAAAE,IAAA,2CAAUI,QAAQ,QAAO,UAAS,QAC/B5D,KAAKiD,UAAS,IAAGjD,KAAKkD,QAAO,IAAGE,EAAgB7D,GAAE,IAAGS,KAAKwC,cAKjEc,EAAU,YAAAE,IAAA,2CAAAG,IAAI,IAAG,cAAa,SAASF,MAAM,WAC3CH,EAAA,mBAAAE,IAAA,2CACEQ,WAAaC,GAAOjE,KAAKmB,UAAU8C,GACnCC,KAAK,QACLN,QAAQ,YACRO,KAAK,cACLC,SAAUpE,KAAKQ,kBAEjB8C,EAAA,mBAAAE,IAAA,2CACEQ,WAAaC,GAAOjE,KAAKkB,YAAY+C,GACrCC,KAAK,QACLN,QAAQ,YACRO,KAAK,aACLC,SAAUpE,KAAKS,eAGjB6C,EAAY,cAAAE,IAAA,2CAAAC,MAAM,iBAAiBxD,MAAOD,KAAKC,MAAK,mBAAoBD,KAAKK,iBAC1EL,KAAKG,kBAAkB2D,KAAI,CAAC/C,EAAIgC,IAC/BO,EAAA,qBAAmBE,IAAKT,EAAO9C,MAAOc,EAAIgD,QAAS,IAAM/D,KAAK8C,eAAe/B,IAC1EA,MAINf,KAAKM,aACJgD,EAAU,YAAAE,IAAA,2CAAAC,MAAM,gBAAgBG,QAAQ,QAAO,UAAS,QACrDR,EAAgB7D,GAAE,IAAGS,KAAKP,MAAK,IAAG2D,EAAgB3D,OAGvD6D,EAAA,mBAAAE,IAAA,2CACEQ,WAAaC,GAAOjE,KAAKa,SAASoD,GAClCC,KAAK,QACLN,QAAQ,YACRO,KAAK,cACLC,SAAUpE,KAAKW,eAEjB2C,EAAA,mBAAAE,IAAA,2CACEQ,WAAaC,GAAOjE,KAAKoB,SAAS6C,GAClCC,KAAK,QACLN,QAAQ,YACRO,KAAK,aACLC,SAAUpE,KAAKY,gB", "ignoreList": []}