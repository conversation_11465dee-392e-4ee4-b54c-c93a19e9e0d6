{"version": 3, "file": "bds-image.entry.esm.js", "sources": ["src/components/image/image.scss?tag=bds-image&encapsulation=shadow", "src/components/image/image.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n\n  .img-feedback {\n    height: 76%;\n  }\n}\n\n:host(.empty_img) {\n  background-color: $color-surface-3;\n}\n", "import { Element, Component, Prop, Method, State, h, Host } from '@stencil/core';\n\nexport type ObjectFitValue = 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';\n\n@Component({\n  tag: 'bds-image',\n  styleUrl: 'image.scss',\n  shadow: true,\n})\nexport class Image {\n  private imageHasLoading: boolean = false;\n\n  @Element() element: HTMLElement;\n  /**\n   * URL of the main image.\n   */\n  @Prop({ reflect: true, mutable: true }) src?: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Width of the image.\n   */\n  @Prop() width?: string;\n\n  /**\n   * Height of the image.\n   */\n  @Prop() height?: string;\n\n  /**\n   * Specifies the object-fit style for the image. Can be: 'fill', 'contain', 'cover', 'none', 'scale-down'.\n   */\n  @Prop() objectFit?: ObjectFitValue = 'cover';\n\n  /**\n   * Brightness of the image.\n   */\n  @Prop() brightness?: number;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Indicates whether the main image has been successfully loaded.\n   */\n  @State() imageLoaded = false;\n\n  /**\n   * Indicates whether there was an error during image loading.\n   */\n  @State() loadError = false;\n\n  /**\n   * The current source URL of the image to be rendered.\n   */\n  @State() currentSrc: string;\n\n  componentDidLoad() {\n    this.element.style.width = this.width ? this.width : 'auto';\n    this.element.style.height = this.height?.length > 0 ? this.height : 'auto';\n  }\n\n  @Method()\n  async loadImage(): Promise<void> {\n    if (this.src) {\n      this.imageHasLoading = true;\n      try {\n        const response = await fetch(this.src);\n        if (response.ok) {\n          const blob = await response.blob();\n          const objectURL = URL.createObjectURL(blob);\n          this.currentSrc = objectURL;\n          this.imageLoaded = true;\n          this.imageHasLoading = false;\n        } else {\n          this.loadError = true;\n        }\n      } catch {\n        this.imageHasLoading = false;\n        this.loadError = true;\n      }\n    }\n  }\n\n  render(): JSX.Element {\n    if (!this.imageLoaded && !this.loadError) {\n      // Se a imagem ainda não foi carregada, chame o método loadImage\n      this.loadImage();\n    }\n    return (\n      <Host class={{ empty_img: !this.imageLoaded }}>\n        {this.imageLoaded ? (\n          <img\n            src={this.currentSrc}\n            alt={this.alt}\n            style={{\n              objectFit: this.objectFit,\n              width: '100%',\n              height: '100%',\n              filter: `brightness(${this.brightness})`,\n            }}\n            data-test={this.dataTest}\n            draggable={false}\n          />\n        ) : this.imageHasLoading ? (\n          <bds-skeleton shape=\"square\" width=\"100%\" height=\"100%\"></bds-skeleton>\n        ) : (\n          <bds-illustration\n            class=\"img-feedback\"\n            type=\"empty-states\"\n            name={this.loadError ? 'broken-image' : 'image-not-found'}\n            alt={this.alt}\n            data-test={this.dataTest}\n          ></bds-illustration>\n        )}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,QAAQ,GAAG,yPAAyP;;MCS7P,KAAK,GAAA,MAAA;AALlB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAMU,QAAA,IAAe,CAAA,eAAA,GAAY,KAAK;AAuBxC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAoB,OAAO;AAO5C;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAEhC;;AAEG;AACM,QAAA,IAAW,CAAA,WAAA,GAAG,KAAK;AAE5B;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AAoE3B;IA7DC,gBAAgB,GAAA;;QACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM;QAC3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAE,MAAM,IAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;;AAI5E,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;AACZ,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,YAAA,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACtC,gBAAA,IAAI,QAAQ,CAAC,EAAE,EAAE;AACf,oBAAA,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;oBAClC,MAAM,SAAS,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC;AAC3C,oBAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,oBAAA,IAAI,CAAC,eAAe,GAAG,KAAK;;qBACvB;AACL,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;;YAEvB,OAAA,EAAA,EAAM;AACN,gBAAA,IAAI,CAAC,eAAe,GAAG,KAAK;AAC5B,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;;;IAK3B,MAAM,GAAA;QACJ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;;YAExC,IAAI,CAAC,SAAS,EAAE;;QAElB,QACE,CAAC,CAAA,IAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAA,EAC1C,IAAI,CAAC,WAAW,IACf,CAAA,CAAA,KAAA,EAAA,EACE,GAAG,EAAE,IAAI,CAAC,UAAU,EACpB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,KAAK,EAAE;gBACL,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,MAAM,EAAE,CAAA,WAAA,EAAc,IAAI,CAAC,UAAU,CAAG,CAAA,CAAA;AACzC,aAAA,EAAA,WAAA,EACU,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,KAAK,EAChB,CAAA,IACA,IAAI,CAAC,eAAe,IACtB,CAAc,CAAA,cAAA,EAAA,EAAA,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAgB,CAAA,KAEvE,CAAA,CAAA,kBAAA,EAAA,EACE,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,cAAc,EACnB,IAAI,EAAE,IAAI,CAAC,SAAS,GAAG,cAAc,GAAG,iBAAiB,EACzD,GAAG,EAAE,IAAI,CAAC,GAAG,EACF,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,CACN,CACrB,CACI;;;;;;;;"}