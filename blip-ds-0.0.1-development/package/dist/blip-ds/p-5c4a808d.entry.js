import{r as t,c as o,h as i,a as s}from"./p-C3J6Z5OX.js";const a=':host .show,:host .hide{display:-ms-flexbox;display:flex}:host .show{opacity:1}:host .show--top-right,:host .show--bottom-right{-webkit-animation:toastAnimationFadeInFromRight 1s;animation:toastAnimationFadeInFromRight 1s}:host .show--top-left,:host .show--bottom-left{-webkit-animation:toastAnimationFadeInFromLeft 1s;animation:toastAnimationFadeInFromLeft 1s}:host .hide{-webkit-transition:all 1s;transition:all 1s;-webkit-animation:toastAnimationFadeOut 0.5s;animation:toastAnimationFadeOut 0.5s}.toast{display:none;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:8px;-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));color:var(--color-content-default, rgb(40, 40, 40));opacity:0;margin-top:16px;overflow:hidden;gap:16px}.toast--action--icon{min-width:440px;max-width:440px;padding:8px 16px}.toast--action--icon bds-icon-button{height:32px}@media (max-width: 780px){.toast--action--icon{min-width:220px;width:95%;margin:16px auto 0px auto}}.toast--action--button{min-width:440px;max-width:456px;padding:8px 16px}@media (max-width: 780px){.toast--action--button{min-width:220px;width:95%;margin:16px auto 0px auto}}.toast--system{background:var(--color-system, rgb(178, 223, 253))}.toast--error{background:var(--color-error, rgb(250, 190, 190))}.toast--success{background:var(--color-success, rgb(132, 235, 188))}.toast--warning{background:var(--color-warning, rgb(253, 233, 155))}.toast--undo{background-color:var(--color-system, rgb(178, 223, 253))}.toast--redo{background-color:var(--color-system, rgb(178, 223, 253))}.toast--notification{background-color:var(--color-surface-1, rgb(246, 246, 246))}.toast__icon{position:relative;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:8px 0}.toast__ballon{display:-ms-flexbox;display:flex;position:absolute;top:-8px;left:-12px;color:var(--color-system, rgb(178, 223, 253));width:72px}.toast__content{position:relative;height:100%;width:100%;-ms-flex-align:start;align-items:flex-start;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;padding:8px 0}.toast__action{display:-ms-flexbox;display:flex;-ms-flex-align:start;align-items:flex-start}.toast__action bds-button-icon,.toast__action bds-button{position:relative}.toast__action bds-button-icon::before,.toast__action bds-button::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.toast__action bds-button-icon:focus-visible,.toast__action bds-button:focus-visible{outline:none}.toast__action bds-button-icon:focus-visible::before,.toast__action bds-button:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.toast__action__button{white-space:nowrap}@-webkit-keyframes toastAnimationFadeInFromRight{0%{opacity:0;right:-200px}50%{opacity:0.9;right:1px}100%{opacity:1}}@keyframes toastAnimationFadeInFromRight{0%{opacity:0;right:-200px}50%{opacity:0.9;right:1px}100%{opacity:1}}@-webkit-keyframes toastAnimationFadeInFromLeft{0%{opacity:0;left:-200px}50%{opacity:0.9;left:1px}100%{opacity:1}}@keyframes toastAnimationFadeInFromLeft{0%{opacity:0;left:-200px}50%{opacity:0.9;left:1px}100%{opacity:1}}@-webkit-keyframes toastAnimationFadeOut{0%{opacity:1}30%{max-height:60px}80%{opacity:0;max-height:30px}100%{max-height:0px}}@keyframes toastAnimationFadeOut{0%{opacity:1}30%{max-height:60px}80%{opacity:0;max-height:30px}100%{max-height:0px}}';const e=class{constructor(i){t(this,i);this.toastButtonClick=o(this,"toastButtonClick");this.icon=null;this.actionType="button";this.variant="system";this.duration=0;this.buttonAction="close";this.show=false;this.hide=false;this.position="bottom-left";this.dtButtonAction=null;this.dtButtonClose=null;this._buttonClickHandler=()=>{if(this.buttonAction==="close")this.close();else{this.toastButtonClick.emit(this.el);this.close()}};this.mapIconName={system:"bell",error:"error",success:"like",warning:"attention",undo:"undo",redo:"redo",notification:"notification"}}_keyPressHandler(t){if(t.key==="Enter"||t.key===" "){t.preventDefault();if(this.buttonAction==="close")this.close();else{this.toastButtonClick.emit(this.el);this.close()}}}async create({actionType:t,buttonAction:o,buttonText:i,icon:s,toastText:a,toastTitle:e,variant:n,duration:r}){let c=document.querySelector(`bds-toast-container.${n==="notification"?"top-right":"bottom-left"}`);if(c){c.appendChild(this.el);c.classList.add(n==="notification"?"top-right":"bottom-left")}else{c=document.createElement("bds-toast-container");c.classList.add(n==="notification"?"top-right":"bottom-left");document.body.appendChild(c);c.appendChild(this.el)}this.el.actionType=t||"button";this.el.buttonAction=o||"close";this.el.buttonText=i;this.el.toastText=a;this.el.toastTitle=e;this.el.variant=n||"system";this.el.duration=r*1e3||0;this.el.position=n==="notification"?"top-right":"bottom-left";this.el.icon=s!==null&&s!==void 0?s:this.mapIconName[this.variant];this.el.show=true;if(this.el.duration>0){setTimeout((()=>{this.el.hide=true;setTimeout((()=>{this.el.remove()}),400)}),this.el.duration)}}async close(){if(this.el.shadowRoot){this.el.shadowRoot.querySelector("div").classList.remove("show");this.el.shadowRoot.querySelector("div").classList.add("hide")}else{this.el.querySelector("div").classList.remove("show");this.el.querySelector("div").classList.add("hide")}setTimeout((()=>{this.el.remove()}),400)}render(){return i("div",{key:"3585d014a0d856a1649d48485f6ef1155ab05b6c",class:{toast:true,[`toast--${this.variant}`]:true,[`toast--action--${this.actionType}`]:true,[`show show--${this.position}`]:this.show,hide:this.hide}},this.variant==="notification"&&i("bds-icon",{key:"ca41b6f8f9e28bc4950fe073a30f504b6e25f52e",class:"toast__ballon",theme:"solid",name:"blip-chat",size:"brand"}),this.icon&&i("bds-icon",{key:"1b2f4cc9d69c0fc0d6a3cd60a0027cac55a30446",class:"toast__icon",theme:"outline",size:"medium",name:this.icon}),i("div",{key:"de5c75c32cb5396a3ceab4b0c613da089af34896",class:"toast__content"},this.toastTitle&&i("bds-typo",{key:"aa9efe355172812f38c0b6a4fc360af2dcd75e68",variant:"fs-14",bold:"bold"},this.toastTitle),this.toastText&&i("bds-typo",{key:"8ff8995c947c670f97264d19836c9acba5deeb13",variant:"fs-14",innerHTML:this.toastText})),i("div",{key:"6d66d73091f4799f1d3e1b5962f307d908ce5227",class:{toast__action:true,[`toast__action__${this.actionType}`]:true}},this.actionType==="button"?i("bds-button",{onKeyDown:this._keyPressHandler.bind(this),tabindex:"0",onClick:()=>this._buttonClickHandler(),variant:"secondary",size:"standard",dataTest:this.dtButtonAction},this.buttonText):i("bds-button-icon",{onClick:()=>this._buttonClickHandler(),size:"short",onKeyDown:this._keyPressHandler.bind(this),tabindex:"0",variant:"secondary",icon:"close",dataTest:this.dtButtonClose})))}get el(){return s(this)}};e.style=a;export{e as bds_toast};
//# sourceMappingURL=p-5c4a808d.entry.js.map