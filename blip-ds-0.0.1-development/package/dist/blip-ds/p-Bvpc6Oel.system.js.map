{"version": 3, "file": "p-Bvpc6Oel.system.js", "sources": ["src/components/card/card-subtitle/card-subtitle.tsx"], "sourcesContent": ["import { Component, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-card-subtitle',\n  shadow: true,\n})\nexport class CardSubtitle {\n  /**\n   *Set the card subtitle.\n   */\n  @Prop() text?: string;\n  render() {\n    return (\n      <bds-typo variant=\"fs-12\" tag=\"p\" bold=\"regular\" margin={false}>\n        {this.text}\n      </bds-typo>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;kBAMa,YAAY,gCAAA,MAAA;;;;gBAKvB,MAAM,GAAA;YACJ,QAAA,QACE,iEAAU,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,SAAS,EAAC,MAAM,EAAE,KAAK,EAAA,EAC3D,IAAI,CAAC,IAAI,CACD;;;;;;;;;;"}