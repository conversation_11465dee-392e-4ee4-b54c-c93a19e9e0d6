{"version": 3, "names": ["tabPanelCss", "TabPanel", "exports", "class_1", "hostRef", "this", "isActive", "prototype", "handleTabChange", "event", "detail", "group", "render", "h", "Host", "key", "class", "_a"], "sources": ["src/components/tabs/tab (depreciated)/tab-panel/tab-panel.scss?tag=bds-tab-panel", "src/components/tabs/tab (depreciated)/tab-panel/tab-panel.tsx"], "sourcesContent": ["@use '../../../../globals/helpers' as *;\n\n.bds-tab-panel {\n  display: none;\n  font-family: $font-family;\n  font-size: $fs-16;\n  font-style: normal;\n  font-weight: normal;\n\n  &--selected {\n    display: block;\n  }\n}\n", "import { Component, ComponentInterface, h, Host, Listen, Prop, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab-panel',\n  styleUrl: 'tab-panel.scss',\n})\nexport class TabPanel implements ComponentInterface {\n  /**\n   * Specifies the TabPanel group. Used to link it to the Tab.\n   */\n  @Prop() group!: string;\n\n  /**\n   * State to control if a tab panel is current active\n   */\n  @State() isActive = false;\n\n  @Listen('bdsTabChange', { target: 'body' })\n  @Listen('bdsTabInit', { target: 'body' })\n  handleTabChange(event: CustomEvent) {\n    this.isActive = event.detail == this.group;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tab-panel': true,\n          ['bds-tab-panel--selected']: this.isActive,\n        }}\n      >\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAc,2M,ICMPC,EAAQC,EAAA,2BAJrB,SAAAC,EAAAC,G,UAaWC,KAAQC,SAAG,KAoBrB,CAhBCH,EAAAI,UAAAC,gBAAA,SAAgBC,GACdJ,KAAKC,SAAWG,EAAMC,QAAUL,KAAKM,K,EAGvCR,EAAAI,UAAAK,OAAA,W,MACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,OAAKC,EAAA,CACH,gBAAiB,MACjBA,EAAC,2BAA4BZ,KAAKC,S,IAGpCO,EAAQ,QAAAE,IAAA,6C,WAzBK,I", "ignoreList": []}