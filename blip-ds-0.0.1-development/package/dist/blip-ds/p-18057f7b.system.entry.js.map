{"version": 3, "names": ["accordionCss", "Accordion", "exports", "class_1", "hostRef", "this", "accGroup", "accheaders", "accBodies", "isOpen", "numberElement", "condition", "startOpen", "divisor", "prototype", "toggle", "bdsToggle", "emit", "value", "open", "_a", "_b", "close", "notStart", "reciveNumber", "number", "isOpenChanged", "collapse", "closeAll", "_c", "bdsAccordionOpen", "_d", "_e", "bdsAccordionClose", "divisorChanged", "newValue", "accordionBody", "element", "querySelector", "componentWillLoad", "parentElement", "tagName", "render", "h", "key", "class"], "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion&encapsulation=shadow", "src/components/accordion/accordion.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, Element, Event, EventEmitter, h, Method, Prop, State, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class Accordion {\n  private accGroup?: HTMLBdsAccordionGroupElement = null;\n  private accheaders?: HTMLBdsAccordionHeaderElement = null;\n  private accBodies?: HTMLBdsAccordionBodyElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n  @State() numberElement?: number = null;\n  @State() condition?: boolean = false;\n\n  @Event() bdsToggle?: EventEmitter;\n  @Event() bdsAccordionOpen?: EventEmitter;\n  @Event() bdsAccordionClose?: EventEmitter;\n\n  @Prop({ reflect: true }) startOpen?: boolean = false;\n  @Prop({ reflect: true }) divisor?: boolean = true;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n    this.bdsToggle.emit({ value: this.isOpen });\n  }\n\n  @Method()\n  async open() {\n    this.accheaders?.open();\n    this.accBodies?.open();\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.accheaders?.close();\n    this.accBodies?.close();\n    this.isOpen = false;\n  }\n\n  @Method()\n  async notStart() {\n    this.startOpen = false;\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(value): void {\n    if (value) {\n      if (this.accGroup.collapse == 'single' && this.condition === false) {\n        this.accGroup?.closeAll(this.numberElement);\n      }\n      this.accheaders?.open();\n      this.accBodies?.open();\n      this.bdsAccordionOpen.emit();\n    } else {\n      this.accheaders?.close();\n      this.accBodies?.close();\n      this.bdsAccordionClose.emit();\n    }\n    this.condition = false;\n  }\n\n  @Watch('divisor')\n  divisorChanged(newValue: boolean): void {\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(newValue);\n    }\n  }\n\n  componentWillLoad() {\n    this.accGroup =\n      this.element.parentElement.tagName == 'BDS-ACCORDION-GROUP' &&\n      (this.element.parentElement as HTMLBdsAccordionGroupElement);\n    this.accheaders = this.element.querySelector('bds-accordion-header') as HTMLBdsAccordionHeaderElement;\n    this.accBodies = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n\n    // Passar a prop divisor para o AccordionBody\n    const accordionBody = this.element.querySelector('bds-accordion-body') as HTMLBdsAccordionBodyElement;\n    if (accordionBody) {\n      (accordionBody as any).divisor(this.divisor);\n    }\n\n    if (this.startOpen === true) {\n      this.condition = true;\n      this.isOpen = true;\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"accordion_group\">\n        <slot></slot>\n      </div>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAe,ylE,ICORC,EAASC,EAAA,2BALtB,SAAAC,EAAAC,G,iJAMUC,KAAQC,SAAkC,KAC1CD,KAAUE,WAAmC,KAC7CF,KAASG,UAAiC,KAIzCH,KAAMI,OAAa,MACnBJ,KAAaK,cAAY,KACzBL,KAASM,UAAa,MAMNN,KAASO,UAAa,MACtBP,KAAOQ,QAAa,IAmF9C,CAhFOV,EAAAW,UAAAC,OAAN,W,qFACEV,KAAKI,QAAUJ,KAAKI,OACpBJ,KAAKW,UAAUC,KAAK,CAAEC,MAAOb,KAAKI,S,iBAI9BN,EAAAW,UAAAK,KAAN,W,8FACEC,EAAAf,KAAKE,cAAY,MAAAa,SAAA,SAAAA,EAAAD,QACjBE,EAAAhB,KAAKG,aAAW,MAAAa,SAAA,SAAAA,EAAAF,OAChBd,KAAKI,OAAS,K,iBAIVN,EAAAW,UAAAQ,MAAN,W,8FACEF,EAAAf,KAAKE,cAAY,MAAAa,SAAA,SAAAA,EAAAE,SACjBD,EAAAhB,KAAKG,aAAW,MAAAa,SAAA,SAAAA,EAAAC,QAChBjB,KAAKI,OAAS,M,iBAIVN,EAAAW,UAAAS,SAAN,W,qFACElB,KAAKO,UAAY,M,iBAIbT,EAAAW,UAAAU,aAAN,SAAmBC,G,qFACjBpB,KAAKK,cAAgBe,E,iBAIvBtB,EAAAW,UAAAY,cAAA,SAAcR,G,cACZ,GAAIA,EAAO,CACT,GAAIb,KAAKC,SAASqB,UAAY,UAAYtB,KAAKM,YAAc,MAAO,EAClES,EAAAf,KAAKC,YAAU,MAAAc,SAAA,SAAAA,EAAAQ,SAASvB,KAAKK,c,EAE/BW,EAAAhB,KAAKE,cAAY,MAAAc,SAAA,SAAAA,EAAAF,QACjBU,EAAAxB,KAAKG,aAAW,MAAAqB,SAAA,SAAAA,EAAAV,OAChBd,KAAKyB,iBAAiBb,M,KACjB,EACLc,EAAA1B,KAAKE,cAAY,MAAAwB,SAAA,SAAAA,EAAAT,SACjBU,EAAA3B,KAAKG,aAAW,MAAAwB,SAAA,SAAAA,EAAAV,QAChBjB,KAAK4B,kBAAkBhB,M,CAEzBZ,KAAKM,UAAY,K,EAInBR,EAAAW,UAAAoB,eAAA,SAAeC,GACb,IAAMC,EAAgB/B,KAAKgC,QAAQC,cAAc,sBACjD,GAAIF,EAAe,CAChBA,EAAsBvB,QAAQsB,E,GAInChC,EAAAW,UAAAyB,kBAAA,WACElC,KAAKC,SACHD,KAAKgC,QAAQG,cAAcC,SAAW,uBACrCpC,KAAKgC,QAAQG,cAChBnC,KAAKE,WAAaF,KAAKgC,QAAQC,cAAc,wBAC7CjC,KAAKG,UAAYH,KAAKgC,QAAQC,cAAc,sBAG5C,IAAMF,EAAgB/B,KAAKgC,QAAQC,cAAc,sBACjD,GAAIF,EAAe,CAChBA,EAAsBvB,QAAQR,KAAKQ,Q,CAGtC,GAAIR,KAAKO,YAAc,KAAM,CAC3BP,KAAKM,UAAY,KACjBN,KAAKI,OAAS,I,GAIlBN,EAAAW,UAAA4B,OAAA,WACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,mBACTF,EAAa,QAAAC,IAAA,6C,iRA/FC,I", "ignoreList": []}