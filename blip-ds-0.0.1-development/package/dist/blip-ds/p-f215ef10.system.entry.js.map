{"version": 3, "names": ["alertHeaderCss", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "class_1", "hostRef", "this", "variant", "icon", "prototype", "render", "h", "key", "class", "_a", "alert__header", "concat", "theme", "size", "name", "bold"], "sources": ["src/components/alert/alert-header/alert-header.scss?tag=bds-alert-header&encapsulation=shadow", "src/components/alert/alert-header/alert-header.tsx"], "sourcesContent": ["@use '../../../globals/colors' as *;\n\n.alert__header {\n  width: 100%;\n  min-height: 64px;\n  padding: 12px 16px;\n  box-sizing: border-box;\n  display: inline-flex;\n  align-items: center;\n\n  bds-icon {\n    min-width: 32px;\n  }\n\n  bds-typo {\n    margin-left: 8px;\n    color: $color-content-din;\n  }\n\n  &--system {\n    background: $color-system;\n\n    bds-typo {\n      color: $color-content-default;\n    }\n\n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--error {\n    background: $color-error;\n    bds-typo {\n      color: $color-content-default;\n    }\n    \n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--warning {\n    background: $color-warning;\n    bds-typo {\n      color: $color-content-default;\n    }\n    \n    .color-icon {\n      color: $color-content-default;\n    }\n  }\n\n  &--delete {\n    background: $color-delete;\n\n    bds-typo {\n      color: $color-content-bright;\n    }\n\n    .color-icon {\n      color: $color-content-bright;\n    }\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\nexport type AlertHeaderVariannt = 'system' | 'error' | 'warning' | 'delete';\n\n@Component({\n  tag: 'bds-alert-header',\n  styleUrl: 'alert-header.scss',\n  shadow: true,\n})\nexport class AlertHeader implements ComponentInterface {\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'system', 'error', 'warning', 'delete';\n   */\n  @Prop() variant?: AlertHeaderVariannt = 'system';\n\n  /**\n   * used for add icon the header. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n\n  render() {\n    return (\n      <div\n        class={{\n          alert__header: true,\n          [`alert__header--${this.variant}`]: true,\n        }}\n      >\n        {this.icon && <bds-icon class=\"color-icon\" theme=\"outline\" size=\"x-large\" name={this.icon}></bds-icon>}\n        <bds-typo variant=\"fs-16\" bold=\"bold\">\n          <slot />\n        </bds-typo>\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAiB,6xC,ICSVC,EAAWC,EAAA,8BALxB,SAAAC,EAAAC,G,UAUUC,KAAOC,QAAyB,SAKfD,KAAIE,KAAY,IAiB1C,CAfCJ,EAAAK,UAAAC,OAAA,W,MACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,OAAKC,EAAA,CACHC,cAAe,MACfD,EAAC,kBAAAE,OAAkBV,KAAKC,UAAY,K,IAGrCD,KAAKE,MAAQG,EAAA,YAAAC,IAAA,2CAAUC,MAAM,aAAaI,MAAM,UAAUC,KAAK,UAAUC,KAAMb,KAAKE,OACrFG,EAAA,YAAAC,IAAA,2CAAUL,QAAQ,QAAQa,KAAK,QAC7BT,EAAQ,QAAAC,IAAA,8C,WAtBM,I", "ignoreList": []}