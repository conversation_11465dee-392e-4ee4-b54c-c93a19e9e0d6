{"version": 3, "file": "bds-loading-page.entry.esm.js", "sources": ["src/assets/svg/message-ballon.svg", "src/components/loading-page/loading-page.scss?tag=bds-loading-page&encapsulation=shadow", "src/components/loading-page/loading-page.tsx"], "sourcesContent": ["<svg width=\"116\" height=\"128\" viewBox=\"0 0 116 128\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path\nd=\"M19.7641 7.96205C29.4515 2.08609 43.2568 0 56.1909 0C64.9661 0 73.6114 0.906179 80.7859 2.43227C98.0762 6.04915 110.71 17.6474 114.205 35.2845C114.205 35.2846 114.205 35.2845 114.205 35.2845C117.703 52.9247 116.117 74.3786 105.319 89.8806C105.322 89.8764 105.325 89.8722 105.328 89.868L103.064 88.3265L105.311 89.8926C105.314 89.8886 105.316 89.8846 105.319 89.8806C101.187 95.9459 95.3332 101.27 90 106.121C88.9159 107.108 87.8533 108.074 86.8311 109.023L86.8205 109.033L86.8098 109.043C80.2757 114.983 73.451 120.918 66.7959 126.686C66.8247 126.658 66.8526 126.631 66.8794 126.604L64.9424 124.667L66.7365 126.737C66.7563 126.72 66.7761 126.703 66.7959 126.686C66.1841 127.27 65.1126 128 63.6075 128H63.3108C60.2104 128 58.0499 125.48 58.0499 122.739V110.034C58.5749 110.046 59.0928 110.052 59.6025 110.052C59.2332 110.052 58.8641 109.919 58.5673 109.647C58.235 109.342 58.0499 108.911 58.0499 108.499V110.034C51.349 109.878 43.4663 108.724 36.0206 106.467C28.0402 104.048 20.2674 100.273 15.0588 94.8251C15.0614 94.8279 15.0641 94.8305 15.0667 94.8332L17.0315 92.9245L15.0507 94.8167C15.0534 94.8195 15.0561 94.8223 15.0588 94.8251C4.29919 83.7432 0.509836 67.8161 0.0540137 53.4132C0.0540929 53.4153 0.0541721 53.4175 0.0542514 53.4197L2.79174 53.3202L0.053807 53.4066C0.0538757 53.4088 0.0539448 53.411 0.0540137 53.4132C-0.551046 36.7377 3.80243 17.797 19.7478 7.97204L19.756 7.96702L19.7641 7.96205ZM22.6136 12.6413C9.19796 20.9131 4.94914 37.268 5.52923 53.2206L5.52947 53.2272L5.52968 53.2337C5.96467 67.0085 9.59063 81.3335 18.9964 91.0158L19.0044 91.0241L19.0123 91.0324C23.2969 95.5178 30.0567 98.9345 37.6098 101.224C45.1184 103.5 53.1199 104.573 59.6025 104.573C61.4796 104.573 63.5285 106.025 63.5285 108.499V122.268C70.0787 116.59 76.741 110.792 83.1141 104.998C84.2257 103.966 85.3483 102.944 86.4682 101.923C91.7975 97.0679 97.0658 92.2681 100.799 86.7846L100.808 86.7723L100.816 86.76C110.481 72.8935 112.156 53.118 108.831 36.3499C105.801 21.0592 95 11.0014 79.6599 7.79391L79.6505 7.79192C72.8828 6.35199 64.6201 5.4786 56.1909 5.4786C43.6161 5.4786 31.0226 7.54442 22.6136 12.6413Z\"\nfill-rule=\"evenodd\"\nfill=\"currentColor\"\n/>\n</svg>", "@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  position: fixed;\n  top: 0;\n  left: 0;\n  height: 100vh;\n  width: 100vw;\n  z-index: 999;\n}\n\n.loading-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: $color-surface-4;\n  opacity: 0.65;\n  mix-blend-mode: multiply;\n}\n\n.page_loading {\n  animation: growUp 0.8s ease-in-out infinite;\n  width: 116px;\n  height: 128px;\n  color: $color-surface-1;\n}\n\n@keyframes growUp {\n  from {\n    opacity: 0;\n  }\n  10% {\n    opacity: 1;\n  }\n  60% {\n    opacity: 1;\n  }\n  to {\n    transform: scale(1.6);\n    opacity: 0;\n  }\n}\n", "import { Component, Host, Prop, State, h } from '@stencil/core';\nimport messageBallon from '../../assets/svg/message-ballon.svg';\n\n@Component({\n  tag: 'bds-loading-page',\n  styleUrl: 'loading-page.scss',\n  shadow: true,\n})\nexport class BdsLoading {\n  @State() private svgContent?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setSvgContent();\n  }\n\n  /**Function to transform the svg in a div element. */\n  formatSvg = (svgContent: string) => {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    return div.innerHTML;\n  };\n\n  setSvgContent = () => {\n    const innerHTML = messageBallon;\n\n    const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));\n    this.svgContent = this.formatSvg(svg);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div class=\"loading-container\" data-test={this.dataTest}>\n          <div class={{ page_loading: true }} innerHTML={this.svgContent}></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["messageBallon"], "mappings": ";;AAAA,MAAM,gBAAgB,GAAG,o+FAAo+F;;ACA7/F,MAAM,cAAc,GAAG,6wBAA6wB;;MCQvxB,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAQE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;;AAOhC,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,UAAkB,KAAI;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACzC,YAAA,GAAG,CAAC,SAAS,GAAG,UAAU;AAC1B,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB;AAEpC,YAAA,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC;AAC/B,YAAA,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAChC,OAAO,GAAG,CAAC,SAAS;AACtB,SAAC;AAED,QAAA,IAAa,CAAA,aAAA,GAAG,MAAK;YACnB,MAAM,SAAS,GAAGA,gBAAa;AAE/B,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AACvC,SAAC;AAWF;IA/BC,iBAAiB,GAAA;QACf,IAAI,CAAC,aAAa,EAAE;;IAqBtB,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,mBAAmB,EAAY,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EACrD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,GAAQ,CAClE,CACD;;;;;;;"}