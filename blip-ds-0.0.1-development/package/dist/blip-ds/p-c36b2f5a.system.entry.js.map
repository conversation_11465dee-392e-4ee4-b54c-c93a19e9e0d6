{"version": 3, "names": ["tabItemCss", "BdsTabItem", "exports", "class_1", "hostRef", "this", "numberElement", "label", "icon", "iconPosition", "iconTheme", "badge", "badgeShape", "badgeColor", "badgeIcon", "badgeAnimation", "badgePosition", "badgeNumber", "disable", "error", "headerStyle", "contentStyle", "open", "dataTest", "prototype", "reciveNumber", "number", "disable<PERSON><PERSON>ed", "tabDisabled", "emit", "item", "render", "h", "Host", "key", "class", "_a", "tab_item", "_b", "tab_item_content"], "sources": ["src/components/tabs/tab-item/tab-item.scss?tag=bds-tab-item&encapsulation=shadow", "src/components/tabs/tab-item/tab-item.tsx"], "sourcesContent": [":host {\n  display: none;\n}\n\n:host(.is-open) {\n  display: block;\n  height: 100%;\n}\n\n.tab_item {\n  height: 100%;\n  &_content {\n    display: none;\n    height: 100%;\n    &--open {\n      display: block;\n    }\n  }\n}\n", "import { Component, h, Host, Prop, Method, Watch, Event, EventEmitter } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab-item',\n  styleUrl: 'tab-item.scss',\n  shadow: true,\n})\nexport class BdsTabItem {\n  /**\n   * Use to set number of tabItem.\n   */\n  @Prop({ mutable: true, reflect: true }) public numberElement?: number = null;\n  /**\n   * The text to be shown at the Tab item.\n   */\n  @Prop() label?: string = null;\n  /**\n   * The icon to be shown at the Tab item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * The position of the icon at the Tab item ('left', 'right').\n   */\n  @Prop() iconPosition?: string = 'left';\n  /**\n   * The theme of the icon at the Tab item ('solid', 'outline', 'emoji', 'logos').\n   */\n  @Prop() iconTheme?: string = 'outline';\n  /**\n   * The shape of the badge to be shown at the Tab item ('circle', 'square', 'triangle', 'triangle-reverse', 'polygon').\n   */\n  @Prop() badge?: boolean = false;\n  /**\n   * The shape of the badge to be shown at the Tab item ('circle', 'square', 'triangle', 'triangle-reverse', 'polygon').\n   */\n  @Prop() badgeShape?: string = 'circle';\n  /**\n   * The color of the badge to be shown at the Tab item.\n   */\n  @Prop() badgeColor?: string = 'system';\n  /**\n   * The icon to be shown inside the badge at the Tab item ('system', 'danger', 'warning', 'success', 'neutral')\n   */\n  @Prop() badgeIcon?: string = null;\n  /**\n   * The animation of the badge to be shown at the Tab item.\n   */\n  @Prop() badgeAnimation?: boolean = false;\n  /**\n   * The animation of the badge to be shown at the Tab item.\n   */\n  @Prop() badgePosition?: string = 'left';\n  /**\n   * The number to be shown inside the badge at the Tab item.\n   */\n  @Prop() badgeNumber?: number = null;\n  /**\n   * Prop for disable the especific tab.\n   */\n  @Prop({ mutable: true, reflect: true }) disable?: boolean = false;\n  /**\n   * Prop to indicate an error state for the tab.\n   */\n  @Prop() error?: boolean = false;\n  /**\n   * Inline styles to be applied to the tab group header element.\n   */\n  @Prop() headerStyle?: string = null;\n  /**\n   * Inline styles to be applied to the tab group content element.\n   */\n  @Prop() contentStyle?: string = null;\n  /**\n   * Used to open/close the Tab item.\n   */\n  @Prop({ mutable: true, reflect: true }) public open?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n  @Event() tabDisabled: EventEmitter;\n\n  @Watch('disable')\n  disableChanged(): void {\n    this.tabDisabled.emit({ item: this.numberElement, disable: this.disable });\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ [`is-open`]: this.disable === true ? false : this.open }}>\n        <div class={{ tab_item: true }} data-test={this.dataTest}>\n          <div class={{ tab_item_content: true, [`tab_item_content--open`]: this.open }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAa,uK,ICONC,EAAUC,EAAA,0BALvB,SAAAC,EAAAC,G,iDASiDC,KAAaC,cAAY,KAIhED,KAAKE,MAAY,KAIjBF,KAAIG,KAAY,KAIhBH,KAAYI,aAAY,OAIxBJ,KAASK,UAAY,UAIrBL,KAAKM,MAAa,MAIlBN,KAAUO,WAAY,SAItBP,KAAUQ,WAAY,SAItBR,KAASS,UAAY,KAIrBT,KAAcU,eAAa,MAI3BV,KAAaW,cAAY,OAIzBX,KAAWY,YAAY,KAISZ,KAAOa,QAAa,MAIpDb,KAAKc,MAAa,MAIlBd,KAAWe,YAAY,KAIvBf,KAAYgB,aAAY,KAIehB,KAAIiB,KAAa,MAIxDjB,KAAQkB,SAAY,IAwB7B,CArBOpB,EAAAqB,UAAAC,aAAN,SAAmBC,G,qFACjBrB,KAAKC,cAAgBoB,E,iBAKvBvB,EAAAqB,UAAAG,eAAA,WACEtB,KAAKuB,YAAYC,KAAK,CAAEC,KAAMzB,KAAKC,cAAeY,QAASb,KAAKa,S,EAGlEf,EAAAqB,UAAAO,OAAA,W,QACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,OAAKC,EAAA,GAAIA,EAAC,WAAY/B,KAAKa,UAAY,KAAO,MAAQb,KAAKiB,KAAIc,IACnEJ,EAAK,OAAAE,IAAA,2CAAAC,MAAO,CAAEE,SAAU,MAAM,YAAahC,KAAKkB,UAC9CS,EAAA,OAAAE,IAAA,2CAAKC,OAAKG,EAAA,CAAIC,iBAAkB,MAAMD,EAAC,0BAA2BjC,KAAKiB,KAAIgB,IACzEN,EAAA,QAAAE,IAAA,+C,uIA1FW,I", "ignoreList": []}