System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var t,s,a,i;return{setters:[function(e){t=e.r;s=e.h;a=e.H;i=e.a}],execute:function(){var l='.sc-bds-table-th-h{display:table-cell;padding:0px 8px}.th_cell.sc-bds-table-th{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:64px;gap:8px;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;-webkit-box-sizing:border-box;box-sizing:border-box}.th_cell--sortable-true.sc-bds-table-th:hover,.th_cell--sortable-false.sc-bds-table-th:hover{cursor:pointer}.justify--left.sc-bds-table-th{-ms-flex-pack:start;justify-content:flex-start}.justify--center.sc-bds-table-th{-ms-flex-pack:center;justify-content:center}.justify--right.sc-bds-table-th{-ms-flex-pack:end;justify-content:flex-end}.dense-th.sc-bds-table-th{min-height:48px;height:auto}.sc-bds-table-th-h:first-child{padding-left:16px}.sc-bds-table-th-h:last-child{padding-right:16px}';var c=e("bds_table_th",function(){function e(e){t(this,e);this.isDense=false;this.sortable=false;this.arrow="";this.justifyContent="left"}e.prototype.componentWillLoad=function(){var e=this.element.closest("bds-table");if(e&&(e.getAttribute("dense-table")==="true"||e.denseTable===true)){this.isDense=true}};e.prototype.render=function(){var e;return s(a,{key:"09da6cc9fbdb19a0d8b92febe0c692ceb1ebc5d4"},s("div",{key:"241b707372284075fd46bc1c16f036e2a87eaef9",class:(e={th_cell:true},e["th_cell--sortable-".concat(this.sortable)]=true,e["dense-th"]=this.isDense,e["justify--".concat(this.justifyContent)]=true,e)},s("bds-typo",{key:"f28cbf94ace5351291094fe1e20e5813ade61341",bold:this.sortable?"bold":"semi-bold",variant:"fs-14"},s("slot",{key:"c802902e4344d0ae247eb8f3187f0a207ce0bcee"})),this.sortable?s("bds-icon",{size:"small",name:this.arrow==="asc"?"arrow-down":this.arrow==="dsc"?"arrow-up":""}):""))};Object.defineProperty(e.prototype,"element",{get:function(){return i(this)},enumerable:false,configurable:true});return e}());c.style=l}}}));
//# sourceMappingURL=p-fa6c6c41.system.entry.js.map