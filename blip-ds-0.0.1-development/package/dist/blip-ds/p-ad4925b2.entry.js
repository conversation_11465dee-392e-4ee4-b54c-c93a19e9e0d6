import{r as o,h as t}from"./p-C3J6Z5OX.js";const e=".modal__close__button-icon{opacity:0;visibility:hidden;color:var(--color-content-default, rgb(40, 40, 40));display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;padding-bottom:16px}.modal__close__button-icon--active{opacity:1;visibility:visible}";const i=class{constructor(t){o(this,t);this.active=true}render(){return t("div",{key:"550543d8c7adc0f28847af57b247be54335b177e",class:{"modal__close__button-icon":true,"modal__close__button-icon--active":this.active}},t("bds-icon",{key:"e466606576851be0f31cd5459048b2337aa44f15",size:"medium",name:"close"}))}};i.style=e;export{i as bds_modal_close_button};
//# sourceMappingURL=p-ad4925b2.entry.js.map