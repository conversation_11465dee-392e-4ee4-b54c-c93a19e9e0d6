{"version": 3, "names": ["tableCellCss", "TableCell", "exports", "class_1", "hostRef", "this", "isDense", "type", "sortable", "justifyContent", "prototype", "renderContent", "h", "class", "_a", "cell", "cell_custom", "dense_cell", "concat", "_b", "variant", "bold", "_c", "cell_action", "colSpan", "_d", "componentWillLoad", "bdsTable", "element", "closest", "getAttribute", "render", "Host", "key"], "sources": ["src/components/table/table-cell/table-cell.scss?tag=bds-table-cell&encapsulation=scoped", "src/components/table/table-cell/table-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0 8px;\n  font-family: $font-family;\n  font-size: 14px;\n  vertical-align: middle;\n}\n.cell {\n  display: flex;\n  align-items: center;\n  min-height: 48px;\n  margin: 8px 0;\n  color: $color-content-default;\n  font-family: $font-family;\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.dense_cell {\n  margin: 0;\n}\n\n.cell_custom {\n  gap: 8px;\n}\n\n.cell_action {\n  flex-direction: row;\n  gap: 8px;\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type IconType = 'text' | 'custom' | 'emoji' | 'collapse';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n\n@Component({\n  tag: 'bds-table-cell',\n  styleUrl: 'table-cell.scss',\n  scoped: true,\n})\nexport class TableCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() type?: string = 'text';\n  @Prop() sortable = false;\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  renderContent(): HTMLElement {\n    return this.type === 'custom' ? (\n      <div class={{ cell:true, cell_custom:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'text' ? (\n      <div class={{ cell:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <bds-typo variant=\"fs-14\" bold={this.sortable ? 'bold' : 'regular'}>\n          <slot />\n        </bds-typo>\n      </div>\n    ) : this.type === 'action' ? (\n      <div class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </div>\n    ) : this.type === 'collapse' ? (\n      <td colSpan={2} class={{ cell:true, cell_action:true, dense_cell:true, [`justify--${this.justifyContent}`]:true }}>\n        <slot />\n      </td>\n    ) : (\n      <slot />\n    );\n  }\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && bdsTable.getAttribute('dense-table') === 'true') {\n      this.isDense = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return <Host>{this.renderContent()}</Host>;\n  }\n}\n"], "mappings": "0JAAA,IAAMA,EAAe,g/B,ICWRC,EAASC,EAAA,4BALtB,SAAAC,EAAAC,G,UAOWC,KAAOC,QAAG,MACXD,KAAIE,KAAY,OAChBF,KAAQG,SAAG,MACXH,KAAcI,eAAmB,MAoC1C,CAlCCN,EAAAO,UAAAC,cAAA,W,YACE,OAAON,KAAKE,OAAS,SACnBK,EAAA,OAAKC,OAAKC,EAAA,CAAIC,KAAK,KAAMC,YAAY,KAAMC,WAAW,MAAMH,EAAC,YAAAI,OAAYb,KAAKI,iBAAkB,KAAIK,IAClGF,EAAA,cAEAP,KAAKE,OAAS,OAChBK,EAAA,OAAKC,OAAKM,EAAA,CAAIJ,KAAK,KAAME,WAAW,MAAME,EAAC,YAAAD,OAAYb,KAAKI,iBAAkB,KAAIU,IAChFP,EAAA,YAAUQ,QAAQ,QAAQC,KAAMhB,KAAKG,SAAW,OAAS,WACvDI,EAAA,eAGFP,KAAKE,OAAS,SAChBK,EAAK,OAAAC,OAAKS,EAAA,CAAIP,KAAK,KAAMQ,YAAY,KAAMN,WAAW,MAAMK,EAAC,YAAAJ,OAAYb,KAAKI,iBAAkB,KAAIa,IAClGV,EAAA,cAEAP,KAAKE,OAAS,WAChBK,EAAA,MAAIY,QAAS,EAAGX,OAAKY,EAAA,CAAIV,KAAK,KAAMQ,YAAY,KAAMN,WAAW,MAAMQ,EAAC,YAAAP,OAAYb,KAAKI,iBAAkB,KAAIgB,IAC7Gb,EAAQ,cAGVA,EAAA,Y,EAIJT,EAAAO,UAAAgB,kBAAA,WACE,IAAMC,EAAWtB,KAAKuB,QAAQC,QAAQ,aACtC,GAAIF,GAAYA,EAASG,aAAa,iBAAmB,OAAQ,CAC/DzB,KAAKC,QAAU,I,GAInBH,EAAAO,UAAAqB,OAAA,WACE,OAAOnB,EAACoB,EAAM,CAAAC,IAAA,4CAAA5B,KAAKM,gB,4HAvCD,I", "ignoreList": []}