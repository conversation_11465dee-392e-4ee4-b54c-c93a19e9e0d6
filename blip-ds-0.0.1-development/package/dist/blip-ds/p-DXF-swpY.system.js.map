{"version": 3, "file": "p-DXF-swpY.system.js", "sources": ["src/components/counter-text/counter-text-interface.ts", "src/components/counter-text/counter-text.scss?tag=bds-counter-text", "src/components/counter-text/counter-text.tsx"], "sourcesContent": ["export enum CounterTextState {\n  Default = 'default',\n  Warning = 'warning',\n  Delete = 'delete',\n}\n\nexport type CounterTextRule = {\n  max: number;\n  min: number;\n};\n", "@use '../../globals/helpers' as *;\n\n.counter-text {\n  background: $color-surface-2;\n  color: $color-content-disable;\n  box-sizing: content-box;\n  width: fit-content;\n  border-radius: 11px;\n  padding: 0 8px;\n\n  @include no-select;\n\n  &--active {\n    background: $color-system;\n    color: $color-content-din;\n  }\n\n  &--warning {\n    background: $color-warning;\n    color: $color-content-din;\n  }\n\n  &--delete {\n    background: $color-delete;\n    color: $color-content-bright;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\nimport { CounterTextRule, CounterTextState } from './counter-text-interface';\n\n@Component({\n  tag: 'bds-counter-text',\n  styleUrl: 'counter-text.scss',\n})\nexport class CounterText {\n  @Prop({ mutable: true }) length!: number;\n  @Prop() max?: number;\n  @Prop({ mutable: true }) active? = false;\n\n  @Prop({ mutable: true }) warning?: CounterTextRule = { max: 20, min: 2 };\n  @Prop({ mutable: true }) delete?: CounterTextRule = { max: 1, min: 0 };\n\n  getState(): string {\n    const actualLength = this.getActualLength();\n\n    if (actualLength >= this.warning.min && actualLength <= this.warning.max) {\n      return CounterTextState.Warning;\n    }\n\n    if (actualLength <= this.delete.max) {\n      return CounterTextState.Delete;\n    }\n\n    return CounterTextState.Default;\n  }\n\n  getActualLength(): number {\n    return this.max - this.length;\n  }\n\n  render(): HTMLElement {\n    const state = this.getState();\n    const actualLength = this.getActualLength();\n\n    return (\n      <div\n        class={{\n          'counter-text': true,\n          'counter-text--active': this.active,\n          [`counter-text--${state}`]: true,\n        }}\n      >\n        <bds-typo variant=\"fs-10\">{actualLength}</bds-typo>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;YAAA,IAAY,gBAIX;YAJD,CAAA,UAAY,gBAAgB,EAAA;YAC1B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;YACnB,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;YACnB,IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;YACnB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,GAI3B,EAAA,CAAA,CAAA;;YCJD,MAAM,cAAc,GAAG,gwBAAgwB;;kBCO1wB,WAAW,+BAAA,MAAA;YAJxB,IAAA,WAAA,CAAA,OAAA,EAAA;;YAO2B,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;YAEf,QAAA,IAAO,CAAA,OAAA,GAAqB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE;YAC/C,QAAA,IAAM,CAAA,MAAA,GAAqB,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;YAoCvE;gBAlCC,QAAQ,GAAA;YACN,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;YAE3C,QAAA,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;wBACxE,OAAO,gBAAgB,CAAC,OAAO;;oBAGjC,IAAI,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;wBACnC,OAAO,gBAAgB,CAAC,MAAM;;oBAGhC,OAAO,gBAAgB,CAAC,OAAO;;gBAGjC,eAAe,GAAA;YACb,QAAA,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;;gBAG/B,MAAM,GAAA;YACJ,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC7B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;oBAE3C,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;YACL,gBAAA,cAAc,EAAE,IAAI;4BACpB,sBAAsB,EAAE,IAAI,CAAC,MAAM;YACnC,gBAAA,CAAC,CAAiB,cAAA,EAAA,KAAK,CAAE,CAAA,GAAG,IAAI;YACjC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,OAAO,EAAA,EAAE,YAAY,CAAY,CAC/C;;;;;;;;;;;"}