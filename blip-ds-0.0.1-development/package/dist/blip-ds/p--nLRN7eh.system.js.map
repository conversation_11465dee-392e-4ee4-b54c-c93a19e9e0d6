{"version": 3, "file": "p--nLRN7eh.system.js", "sources": ["src/components/tabs/tab (depreciated)/tab-panel/tab-panel.scss?tag=bds-tab-panel", "src/components/tabs/tab (depreciated)/tab-panel/tab-panel.tsx"], "sourcesContent": ["@use '../../../../globals/helpers' as *;\n\n.bds-tab-panel {\n  display: none;\n  font-family: $font-family;\n  font-size: $fs-16;\n  font-style: normal;\n  font-weight: normal;\n\n  &--selected {\n    display: block;\n  }\n}\n", "import { Component, ComponentInterface, h, Host, Listen, Prop, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab-panel',\n  styleUrl: 'tab-panel.scss',\n})\nexport class TabPanel implements ComponentInterface {\n  /**\n   * Specifies the TabPanel group. Used to link it to the Tab.\n   */\n  @Prop() group!: string;\n\n  /**\n   * State to control if a tab panel is current active\n   */\n  @State() isActive = false;\n\n  @Listen('bdsTabChange', { target: 'body' })\n  @Listen('bdsTabInit', { target: 'body' })\n  handleTabChange(event: CustomEvent) {\n    this.isActive = event.detail == this.group;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tab-panel': true,\n          ['bds-tab-panel--selected']: this.isActive,\n        }}\n      >\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;MAAA,MAAM,WAAW,GAAG,oNAAoN;;YCM3N,QAAQ,4BAAA,MAAA;MAJrB,IAAA,WAAA,CAAA,OAAA,EAAA;;MAUE;;MAEG;MACM,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;MAoB1B;MAhBC,IAAA,eAAe,CAAC,KAAkB,EAAA;cAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK;;UAG5C,MAAM,GAAA;cACJ,QACE,CAAA,CAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI;MACrB,gBAAA,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ;MAC3C,aAAA,EAAA,EAED,CAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;;;;"}