{"version": 3, "file": "bds-icon.entry.esm.js", "sources": ["src/components/icon/utils.ts", "src/components/icon/icon.scss?tag=bds-icon&encapsulation=shadow", "src/components/icon/icon.tsx"], "sourcesContent": ["import { IconTheme } from './icon-interface';\n\nconst clearPathsAndFillColor = (svg: Element, color: string): void => {\n  const paths = svg.getElementsByTagName('path');\n\n  for (let i = 0; i < paths.length; i++) {\n    paths[i].setAttribute('fill', color);\n  }\n\n  svg.setAttribute('fill', color);\n};\n\nexport const formatSvg = (svgContent: string | null, color: string | null, emoji = false): string => {\n  if (svgContent) {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    svgElm.setAttribute('fill', 'currentColor');\n\n    if (!emoji) {\n      clearPathsAndFillColor(svgElm, color || 'currentColor');\n    }\n    return div.innerHTML;\n  }\n\n  return '';\n};\n\nexport const getIconName = (name: string, theme: IconTheme) => {\n  return `asset-icon-${name}-${theme}`;\n};\n\nexport const getEmojiName = (name: string) => {\n  return `asset-emoji-${name}`;\n};\n\nexport const getLogoName = (name: string) => {\n  return `asset-logo-${name}`;\n};\n", "@use '../../globals/helpers' as *;\n\n@mixin size($size) {\n  width: $size;\n  min-width: $size;\n  height: $size;\n  max-height: $size;\n  min-height: $size;\n}\n\n$icon-brand: 64px;\n$icon-xxx-large: 40px;\n$icon-xx-large: 36px;\n$icon-x-large: 32px;\n$icon-large: 28px;\n$icon-medium: 24px;\n$icon-small: 20px;\n$icon-x-small: 16px;\n$icon-xx-small: 12px;\n\n:host {\n  width: 1em;\n  height: 1em;\n  color: $color-content-default;\n\n  fill: currentColor;\n\n  box-sizing: content-box !important;\n}\n\n.icon-inner {\n  max-width: 100%;\n  height: 100%;\n}\n\n.emoji-inner {\n  max-width: 100%;\n  height: 100%;\n  display: flex;\n}\n\n:host(.bds-icon) {\n  display: inline-block;\n  svg {\n    fill: currentColor;\n    width: 100%;\n    min-width: 100%;\n  }\n}\n\n:host(.bds-icon__size--brand) {\n  @include size($icon-brand);\n}\n\n:host(.bds-icon__size--xxx-large) {\n  @include size($icon-xxx-large);\n}\n\n:host(.bds-icon__size--xx-large) {\n  @include size($icon-xx-large);\n}\n\n:host(.bds-icon__size--x-large) {\n  @include size($icon-x-large);\n}\n\n:host(.bds-icon__size--large) {\n  @include size($icon-large);\n}\n\n:host(.bds-icon__size--medium) {\n  @include size($icon-medium);\n}\n\n:host(.bds-icon__size--small) {\n  @include size($icon-small);\n}\n\n:host(.bds-icon__size--x-small) {\n  @include size($icon-x-small);\n}\n\n:host(.bds-icon__size--xx-small) {\n  @include size($icon-xx-small);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Build, Component, Element, Host, Prop, State, Watch, h } from '@stencil/core';\nimport icons from 'blip-tokens/build/json/assets_icons.json';\nimport emojis from 'blip-tokens/build/json/assets_emojis.json';\nimport logo from 'blip-tokens/build/json/assets_logos.json';\nimport { IconSize, IconTheme, IconType } from './icon-interface';\nimport { formatSvg, getIconName, getEmojiName, getLogoName } from './utils';\n\n@Component({\n  tag: 'bds-icon',\n  assetsDirs: ['svg'],\n  styleUrl: 'icon.scss',\n  shadow: true,\n})\nexport class Icon {\n  private io?: IntersectionObserver;\n\n  @Element() el!: HTMLElement;\n\n  @State() private svgContent?: string;\n  @State() private isVisible = false;\n\n  /**\n   * Specifies the color to use.Specifies a color to use. The default is svg.\n   */\n  @Prop() color?: string;\n\n  /**\n   * Specifies the label to use for accessibility. Defaults to the icon name.\n   */\n  @Prop({ mutable: true, reflect: true }) ariaLabel: string;\n\n  /**\n   * Specifies whether the icon should horizontally flip when `dir` is `\"rtl\"`.\n   */\n  @Prop() flipRtl?: boolean;\n\n  /**\n   * Specifies which icon to use from the built-in set of icons.\n   */\n  @Prop() name?: string;\n\n  /**\n   * Specifies the exact `src` of an SVG file to use.\n   */\n  @Prop() src?: string;\n\n  /**\n   * A combination of both `name` and `src`. If a `src` url is detected\n   * it will set the `src` property. Otherwise it assumes it's a built-in named\n   * SVG and set the `name` property.\n   */\n  @Prop() icon?: any;\n\n  /**\n   * Icon size. Entered as one of the icon size design tokens. Can be one of:\n   * \"xxx-small\", \"xx-small\", \"x-small\", \"small\", \"medium\", \"large\", \"x-large\", \"xx-large\", \"xxx-large\", \"brand\".\n   */\n  @Prop() size?: IconSize = 'medium';\n\n  /**\n   * If enabled, ion-icon will be loaded lazily when it's visible in the viewport.\n   * Default, `false`.\n   */\n  @Prop() lazy = false;\n\n  /**\n   * Specifies the theme to use outline or solid icons. Defaults to outline.\n   */\n  @Prop({ reflect: true }) theme: IconTheme = 'outline';\n\n  /**\n   * Specifies the type of icon. If type is set to emoji, it will be able to set only emoji names on the name property.\n   */\n  @Prop() type: IconType = 'icon';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    // purposely do not return the promise here because loading\n    // the svg file should not hold up loading the app\n    // only load the svg if it's visible\n    this.waitUntilVisible(this.el, () => {\n      this.isVisible = true;\n      this.loadIcon();\n    });\n  }\n\n  disconnectedCallback(): void {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n\n  private waitUntilVisible(el: HTMLElement, cb: () => void): void {\n    if (Build.isBrowser && this.lazy && typeof window !== 'undefined' && (window as any).IntersectionObserver) {\n      const io = (this.io = new (window as any).IntersectionObserver((data: IntersectionObserverEntry[]) => {\n        if (data[0].isIntersecting) {\n          io.disconnect();\n          this.io = undefined;\n          cb();\n        }\n      }));\n\n      io.observe(el);\n    } else {\n      // browser doesn't support IntersectionObserver\n      // so just fallback to always show it\n      cb();\n    }\n  }\n\n  @Watch('name')\n  @Watch('src')\n  @Watch('icon')\n  @Watch('theme')\n  loadIcon(): void {\n    if (!this.name) return;\n\n    if (Build.isBrowser && this.isVisible) {\n      this.setSvgContent();\n    }\n\n    if (!this.ariaLabel) {\n      const label = this.name;\n      if (label) {\n        this.ariaLabel = label;\n      }\n    }\n  }\n\n  setSvgContent = () => {\n    let svg;\n    try {\n      if (this.type === 'icon') {\n        const key = getIconName(this.name, this.theme);\n        svg = atob(icons[key]);\n        this.svgContent = formatSvg(svg, this.color);\n      } else if (this.type === 'emoji') {\n        const key = getEmojiName(this.name);\n        svg = atob(emojis[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      } else if (this.type === 'logo') {\n        const key = getLogoName(this.name);\n        svg = atob(logo[key]);\n        this.svgContent = formatSvg(svg, this.color, true);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn('[Warning]: Failed to setSvgContent to', this.name);\n    }\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-icon': true,\n          [`bds-icon__size--${this.size}`]: true,\n        }}\n      >\n        {this.svgContent ? (\n          <div\n            class={{\n              'icon-inner': this.type === 'icon',\n              'emoji-inner': this.type === 'emoji',\n              'logo-inner': this.type === 'logo',\n            }}\n            innerHTML={this.svgContent}\n            data-test={this.dataTest}\n          ></div>\n        ) : (\n          <div class=\"icon-inner\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,sBAAsB,GAAG,CAAC,GAAY,EAAE,KAAa,KAAU;IACnE,MAAM,KAAK,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAE9C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;;AAGtC,IAAA,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;AACjC,CAAC;AAEM,MAAM,SAAS,GAAG,CAAC,UAAyB,EAAE,KAAoB,EAAE,KAAK,GAAG,KAAK,KAAY;IAClG,IAAI,UAAU,EAAE;QACd,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACzC,QAAA,GAAG,CAAC,SAAS,GAAG,UAAU;AAE1B,QAAA,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB;AAEpC,QAAA,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC;AAC/B,QAAA,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;AAChC,QAAA,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,sBAAsB,CAAC,MAAM,EAAE,KAAK,IAAI,cAAc,CAAC;;QAEzD,OAAO,GAAG,CAAC,SAAS;;AAGtB,IAAA,OAAO,EAAE;AACX,CAAC;AAEM,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,KAAgB,KAAI;AAC5D,IAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAI,CAAA,EAAA,KAAK,EAAE;AACtC,CAAC;AAEM,MAAM,YAAY,GAAG,CAAC,IAAY,KAAI;IAC3C,OAAO,CAAA,YAAA,EAAe,IAAI,CAAA,CAAE;AAC9B,CAAC;AAEM,MAAM,WAAW,GAAG,CAAC,IAAY,KAAI;IAC1C,OAAO,CAAA,WAAA,EAAc,IAAI,CAAA,CAAE;AAC7B,CAAC;;AC1CD,MAAM,OAAO,GAAG,yxCAAyxC;;MCc5xC,IAAI,GAAA,MAAA;AANjB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAYmB,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AAkClC;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAc,QAAQ;AAElC;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAG,KAAK;AAEpB;;AAEG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAc,SAAS;AAErD;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAa,MAAM;AAE/B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAwDhC,QAAA,IAAa,CAAA,aAAA,GAAG,MAAK;AACnB,YAAA,IAAI,GAAG;AACP,YAAA,IAAI;AACF,gBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AACxB,oBAAA,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;oBAC9C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;;AACvC,qBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;oBAChC,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACvB,oBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;;AAC7C,qBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;oBAC/B,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAClC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,oBAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;;;YAEpD,OAAO,GAAG,EAAE;;gBAEZ,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,IAAI,CAAC;;AAEpE,SAAC;AA2BF;IArGC,iBAAiB,GAAA;;;;QAIf,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAK;AAClC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;YACrB,IAAI,CAAC,QAAQ,EAAE;AACjB,SAAC,CAAC;;IAGJ,oBAAoB,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,EAAE,EAAE;AACX,YAAA,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,EAAE,GAAG,SAAS;;;IAIf,gBAAgB,CAAC,EAAe,EAAE,EAAc,EAAA;AACtD,QAAA,IAAuB,IAAI,CAAC,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,oBAAoB,EAAE;AACzG,YAAA,MAAM,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,IAAK,MAAc,CAAC,oBAAoB,CAAC,CAAC,IAAiC,KAAI;AACnG,gBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE;oBAC1B,EAAE,CAAC,UAAU,EAAE;AACf,oBAAA,IAAI,CAAC,EAAE,GAAG,SAAS;AACnB,oBAAA,EAAE,EAAE;;aAEP,CAAC,CAAC;AAEH,YAAA,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;;aACT;;;AAGL,YAAA,EAAE,EAAE;;;IAQR,QAAQ,GAAA;QACN,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE;QAEhB,IAAuB,IAAI,CAAC,SAAS,EAAE;YACrC,IAAI,CAAC,aAAa,EAAE;;AAGtB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI;YACvB,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;;IA2B5B,MAAM,GAAA;AACJ,QAAA,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,IAAI,EAAC,KAAK,EACV,KAAK,EAAE;AACL,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACvC,aAAA,EAEA,EAAA,IAAI,CAAC,UAAU,IACd,CACE,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE;AACL,gBAAA,YAAY,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;AAClC,gBAAA,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;AACpC,gBAAA,YAAY,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;AACnC,aAAA,EACD,SAAS,EAAE,IAAI,CAAC,UAAU,EACf,WAAA,EAAA,IAAI,CAAC,QAAQ,EACnB,CAAA,KAEP,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,YAAY,EAAY,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAQ,CAAA,CACzD,CACI;;;;;;;;;;;;;;;"}