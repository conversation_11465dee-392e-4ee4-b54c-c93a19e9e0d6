{"version": 3, "names": ["modalCloseButtonCss", "BdsModalCloseButton", "exports", "class_1", "hostRef", "this", "active", "prototype", "render", "h", "key", "class", "size", "name"], "sources": ["src/components/modal/modal-close-button/modal-close-button.scss?tag=bds-modal-close-button&encapsulation=shadow", "src/components/modal/modal-close-button/modal-close-button.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.modal__close__button-icon {\n  opacity: 0;\n  visibility: hidden;\n  color: $color-content-default;\n  display: flex;\n  justify-content: flex-end;\n  padding-bottom: 16px;\n\n  &--active {\n    opacity: 1;\n    visibility: visible;\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-close-button',\n  styleUrl: 'modal-close-button.scss',\n  shadow: true,\n})\nexport class BdsModalCloseButton implements ComponentInterface {\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public active?: boolean = true;\n\n  render() {\n    return (\n      <div\n        class={{\n          'modal__close__button-icon': true,\n          'modal__close__button-icon--active': this.active,\n        }}\n      >\n        <bds-icon size=\"medium\" name=\"close\"></bds-icon>\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAsB,8Q,ICOfC,EAAmBC,EAAA,oCALhC,SAAAC,EAAAC,G,UAaSC,KAAMC,OAAa,IAc3B,CAZCH,EAAAI,UAAAC,OAAA,WACE,OACEC,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACL,4BAA6B,KAC7B,oCAAqCN,KAAKC,SAG5CG,EAAU,YAAAC,IAAA,2CAAAE,KAAK,SAASC,KAAK,U,WAlBL,I", "ignoreList": []}