import{r as s,c as t,h as e,H as i,a}from"./p-C3J6Z5OX.js";const o=":host{display:-ms-flexbox;display:flex;position:relative}:host:focus-visible{outline:none}.card{display:-ms-flexbox;display:flex;width:100%}.card_hover:hover{-webkit-transform:scale(1.02);transform:scale(1.02);-webkit-transition:all 0.3s;transition:all 0.3s;cursor:pointer}.card_hover_selectable:hover{-webkit-box-shadow:0px 0px 0px 2px rgba(30, 107, 241, 0.08);box-shadow:0px 0px 0px 2px rgba(30, 107, 241, 0.08)}.card_hover_selectable:active{-webkit-box-shadow:0px 0px 0px 2px rgba(30, 107, 241, 0.24);box-shadow:0px 0px 0px 2px rgba(30, 107, 241, 0.24)}.focus:focus-visible{display:-ms-flexbox;display:flex;position:absolute;border:2px solid var(--color-focus, rgb(194, 38, 251));border-radius:4px;width:100%;height:100%;top:-4px;left:-4px;padding-right:4px;padding-bottom:4px;outline:none}";const r=class{constructor(e){s(this,e);this.bdsClick=t(this,"bdsClick");this.height=null;this.width="fit-content";this.clickable=false;this.bgColor="surface-1";this.selectable=false;this.borderColor=null;this.dataTest=null;this.isHovered=false;this.isPressed=false;this.elevation="primary"}componentDidLoad(){this.cardElement=this.element.shadowRoot.querySelector(".card");if(this.cardElement&&(this.clickable===true||this.selectable===true)){this.cardElement.addEventListener("mouseenter",(()=>{this.isHovered=true}));this.cardElement.addEventListener("mouseleave",(()=>{this.isHovered=false}));this.cardElement.addEventListener("mousedown",(()=>{this.isPressed=true;this.bdsClick.emit()}));document.addEventListener("mouseup",(()=>{this.isPressed=false}));this.cardElement.addEventListener("keydown",(s=>{if(s.key==="Enter"){this.isPressed=true;this.bdsClick.emit()}}));this.cardElement.addEventListener("keyup",(s=>{if(s.key==="Enter"){this.isPressed=false}}))}}componentDidUpdate(){if(this.isPressed){this.elevation="static"}else if(this.isHovered){this.elevation="secondary"}}handleKeyDown(s){if(s.key=="Enter"){this.isPressed=true;this.bdsClick.emit(s)}}render(){const s={width:this.width};return e(i,{key:"9a1a837ae9e47555d7f1e2a6300449c492d70a96",style:s},e("bds-paper",{key:"ea250b930c8b09c55ad4137f8a21fdc9c46a351e",border:this.clickable?false:true,elevation:this.elevation,class:{card:true,card_hover:this.clickable,card_hover_selectable:this.isHovered&&this.selectable?true:false,card_hover_pressed:this.isHovered&&this.selectable?true:false},height:this.height,width:this.width,bgColor:this.bgColor,"data-test":this.dataTest,"border-color":this.borderColor?this.borderColor:"border-1"},e("div",{key:"2f5d2ef71dd309ada7c9bd9c7298851ba33ccc24",tabindex:"0",class:"focus",onKeyDown:this.handleKeyDown.bind(this)}),e("bds-grid",{key:"3e4271c1d8b65e08ff6db22674cdb2095dae2e02",xxs:"12",direction:"column",gap:"2",padding:"2"},e("slot",{key:"6618f26114e5aaba2df45f66380d89b3d24f6453"}))))}get element(){return a(this)}};r.style=o;export{r as bds_card};
//# sourceMappingURL=p-e3d1edaf.entry.js.map