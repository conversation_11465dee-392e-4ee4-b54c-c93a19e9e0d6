import{r as e,c as t,h as s,H as a,a as i}from"./p-C3J6Z5OX.js";const r=class{constructor(s){e(this,s);this.bdsRadioGroupChange=t(this,"bdsRadioGroupChange");this.radioGroupElement=null;this.chagedOptions=(e,t)=>{if(t.detail.checked==true){this.value=e}}}valueChanged(e){this.setSelectedRadio(e);this.bdsRadioGroupChange.emit({value:e})}componentWillRender(){this.radioGroupElement=this.element.getElementsByTagName("bds-radio");for(let e=0;e<this.radioGroupElement.length;e++){this.radioGroupElement[e].addEventListener("bdsChange",(t=>this.chagedOptions(this.radioGroupElement[e].value,t)))}}setSelectedRadio(e){const t=this.radioGroupElement;for(let s=0;s<t.length;s++){const a=t[s].value;t[s].checked=false;if(t[s].checked==false&&e==a){t[s].checked=true}}}render(){return s(a,{key:"e3125c6fb59d1ae2d14b403c0987a1c38d3ed4e9"},s("slot",{key:"760a98c72db9700f888e85436e4f07a89bc7209b"}))}get element(){return i(this)}static get watchers(){return{value:["valueChanged"]}}};export{r as bds_radio_group};
//# sourceMappingURL=p-bc2d1329.entry.js.map