{"version": 3, "names": ["iconButtonCss", "IconButton", "exports", "class_1", "hostRef", "_this", "this", "disabled", "size", "variant", "iconTheme", "icon", "dataTest", "mapSize", "tall", "standard", "short", "mapVariantStyle", "primary", "secondary", "tertiary", "delete", "ghost", "handleClick", "ev", "bdsClick", "emit", "prototype", "render", "state", "h", "onClick", "class", "_a", "concat", "tabindex", "name", "theme", "color"], "sources": ["src/components/icon-button/icon-button.scss?tag=bds-button-icon&encapsulation=shadow", "src/components/icon-button/icon-button.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$icon-button-border-radius: 8px;\n$icon-button-padding: 8px;\n\n@mixin size-buttom($size) {\n  width: $size;\n  height: $size;\n}\n\n@mixin disable-button($color: null, $border: null) {\n  opacity: 50%;\n  pointer-events: none;\n  cursor: not-allowed;\n\n  @if ($color) {\n    color: $color;\n  }\n\n  @if ($border) {\n    border: $border;\n  }\n\n  &:hover,\n  &:active {\n    @if ($color) {\n      color: $color;\n    }\n\n    @if ($border) {\n      border: $border;\n    }\n  }\n}\n\n@mixin icon-button-primary {\n  background: $color-surface-primary;\n\n  .bds-icon {\n    color: $color-content-bright;\n  }\n\n  &--disabled {\n    @include disable-button($color-disabled-bg, $color-disabled-text);\n  }\n}\n\n@mixin icon-button-secondary {\n  color: $color-content-default;\n  background: transparent;\n\n  .bds-icon {\n    color: $color-content-default;\n  }\n\n  &--disabled {\n    @include disable-button(transparent, $color-disabled-text);\n  }\n}\n\n@mixin icon-button-secondary-white {\n  background: transparent;\n  color: $color-neutral-light-snow;\n\n  &:hover,\n  &:focus {\n    background: rgba($color-neutral-light-snow, 0.3);\n    color: $color-neutral-light-snow;\n  }\n\n  &:active {\n    background: rgba($color-neutral-light-snow, 0.4);\n    color: $color-neutral-light-snow;\n  }\n\n  &--disabled {\n    cursor: not-allowed;\n    color: $color-disabled-text;\n    background: $color-disabled-bg;\n  }\n}\n\n@mixin icon-button-delete {\n  background: $color-delete;\n\n  .bds-icon {\n    color: $color-content-bright;\n  }\n\n  &--disabled {\n    @include disable-button($color-disabled-wrong);\n  }\n}\n\n@mixin icon-button-tertiary {\n  background: transparent;\n  border: 1px solid $color-border-1;\n\n  .bds-icon {\n    color: $color-content-default;\n  }\n\n  &--disabled {\n    @include disable-button($color-disabled-text, 1px solid $color-disabled-text);\n  }\n}\n\n@mixin icon-button-ghost {\n  background: transparent;\n  border: 1px solid $color-border-1;\n\n  .bds-icon {\n    color: $color-content-default;\n  }\n\n  &--disabled {\n    @include disable-button($color-disabled-text, 1px solid $color-disabled-text);\n  }\n}\n\n.icon__button {\n  @include reset-button();\n  cursor: pointer;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &::before {\n      border-color: $color-focus;\n    }\n  }\n\n  -webkit-transition: all 0.5s;\n  -moz-transition: all 0.5s;\n  transition: all 0.5s;\n\n  border-radius: $icon-button-border-radius;\n  padding: $icon-button-padding;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: transparent;\n    z-index: 0;\n    border-radius: $icon-button-border-radius;\n  }\n  &:hover {\n    &::after {\n      background-color: $color-hover;\n    }\n  }\n\n  &:active {\n    &::after {\n      background-color: $color-pressed;\n    }\n  }\n\n  & .icon__button {\n    position: relative;\n    z-index: 1;\n  }\n\n  & * {\n    pointer-events: none;\n  }\n\n  &--primary {\n    @include icon-button-primary();\n  }\n\n  &--secondary {\n    @include icon-button-secondary();\n  }\n\n  &--tertiary {\n    @include icon-button-tertiary();\n  }\n\n  &--ghost {\n    @include icon-button-ghost();\n  }\n\n  &--secondary-white {\n    @include icon-button-secondary-white();\n  }\n\n  &--delete {\n    @include icon-button-delete();\n  }\n\n  &.size-tall {\n    @include size-buttom(56px);\n  }\n  &.size-standard {\n    @include size-buttom(48px);\n  }\n  &.size-short {\n    @include size-buttom(40px);\n  }\n}\n", "import { Component, h, Prop, Event, EventEmitter } from '@stencil/core';\nimport { IconSize } from '../icon/icon-interface';\n\nexport type IconButtonSize = 'tall' | 'standard' | 'short';\nexport type IconButtonVariant = 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'secondary--white' | 'delete';\nexport type IconSizeMap = { [key in string]: IconSize };\nexport type IconButtonVariantMap = { [key in IconButtonVariant]: string };\nexport type ButtonIconTheme = 'outline' | 'solid';\n\n@Component({\n  tag: 'bds-button-icon',\n  styleUrl: 'icon-button.scss',\n  shadow: true,\n})\nexport class IconButton {\n  /**\n   * \tIf true, the base button will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: IconButtonSize = 'standard';\n\n  /**\n   * Variant. Entered as one of the variant. Can be one of:\n   * 'primary', 'secondary', 'ghost', 'dashed';\n   */\n  @Prop() variant?: IconButtonVariant = 'primary';\n\n  /**\n   * The theme of the icon. Can be one of:\n   * 'outline', 'solid';\n   */\n  @Prop({ reflect: true }) iconTheme: ButtonIconTheme = 'outline';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Event buttom onClick.\n   */\n  @Event() bdsClick: EventEmitter;\n\n  private mapSize: IconSizeMap = {\n    tall: 'xxx-large',\n    standard: 'x-large',\n    short: 'medium',\n  };\n\n  private mapVariantStyle: IconButtonVariantMap = {\n    primary: 'icon__button--primary',\n    secondary: 'icon__button--secondary',\n    tertiary: 'icon__button--tertiary',\n    delete: 'icon__button--delete',\n    ghost: 'icon__button--ghost',\n    'secondary--white': 'icon__button--secondary-white',\n  };\n\n  private handleClick = (ev) => {\n    if (!this.disabled) {\n      this.bdsClick.emit(ev);\n    }\n  };\n\n  render(): HTMLElement {\n    if (!this.icon) return null;\n\n    const size: IconSize = this.mapSize[this.size];\n    const state: string = this.mapVariantStyle[this.variant];\n\n    return (\n      <button\n        onClick={(ev) => this.handleClick(ev)}\n        disabled={this.disabled}\n        class={{\n          ['icon__button']: true,\n          [state]: true,\n          [`${state}--disabled`]: this.disabled,\n          [`size-${this.size}`]: true,\n        }}\n        data-test={this.dataTest}\n        tabindex=\"0\"\n      >\n        <bds-icon name={this.icon} size={size} theme={this.iconTheme} color=\"inherit\"></bds-icon>\n      </button>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAgB,8oH,ICcTC,EAAUC,EAAA,6BALvB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,2CASUA,KAAQC,SAAa,MAMrBD,KAAIE,KAAoB,WAMxBF,KAAOG,QAAuB,UAMbH,KAASI,UAAoB,UAK7BJ,KAAIK,KAAY,KAKjCL,KAAQM,SAAY,KAOpBN,KAAAO,QAAuB,CAC7BC,KAAM,YACNC,SAAU,UACVC,MAAO,UAGDV,KAAAW,gBAAwC,CAC9CC,QAAS,wBACTC,UAAW,0BACXC,SAAU,yBACVC,OAAQ,uBACRC,MAAO,sBACP,mBAAoB,iCAGdhB,KAAAiB,YAAc,SAACC,GACrB,IAAKnB,EAAKE,SAAU,CAClBF,EAAKoB,SAASC,KAAKF,E,CAEvB,CAyBD,CAvBCrB,EAAAwB,UAAAC,OAAA,W,MAAA,IAAAvB,EAAAC,KACE,IAAKA,KAAKK,KAAM,OAAO,KAEvB,IAAMH,EAAiBF,KAAKO,QAAQP,KAAKE,MACzC,IAAMqB,EAAgBvB,KAAKW,gBAAgBX,KAAKG,SAEhD,OACEqB,EAAA,UACEC,QAAS,SAACP,GAAO,OAAAnB,EAAKkB,YAAYC,EAAjB,EACjBjB,SAAUD,KAAKC,SACfyB,OAAKC,EAAA,GACHA,EAAC,gBAAiB,KAClBA,EAACJ,GAAQ,KACTI,EAAC,GAAAC,OAAGL,EAAK,eAAevB,KAAKC,SAC7B0B,EAAC,QAAAC,OAAQ5B,KAAKE,OAAS,K,GACxB,YACUF,KAAKM,SAChBuB,SAAS,KAETL,EAAU,YAAAM,KAAM9B,KAAKK,KAAMH,KAAMA,EAAM6B,MAAO/B,KAAKI,UAAW4B,MAAM,Y,WA/ErD,I", "ignoreList": []}