import{r as e,h as t,H as s,a as r}from"./p-C3J6Z5OX.js";const i=":host{width:100%;border-radius:8px;-webkit-box-sizing:border-box;box-sizing:border-box;padding:16px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host ::slotted(bds-step:last-child){-ms-flex:inherit;flex:inherit}::slotted(.stepper__container__divisor){-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-item-align:center;align-self:center;height:1.5px;background:var(--color-content-disable, rgb(89, 89, 89));margin:0px 8px;min-width:24px}::slotted(.stepper__container__divisor--completed){border-top:2px solid var(--color-primary, rgb(30, 107, 241))}";const o=class{constructor(t){e(this,t)}connectedCallback(){this.childOptions.forEach(((e,t)=>{e.index=t;if(t===this.childOptions.length-1){e.last=true}}))}componentDidLoad(){this.renderLine()}async setActiveStep(e){this.resetActiveSteps();this.childOptions[e].active=true}async setCompletedStep(e){this.childOptions[e].completed=true}async getActiveStep(){return this.childOptions.find((e=>e.active===true)).index}async resetActiveSteps(){for(const e of this.childOptions){e.active=false}}async resetCompletedSteps(){for(const e of this.childOptions){e.completed=false}}get childOptions(){return Array.from(this.el.querySelectorAll("bds-step"))}renderLine(){const e=document.createElement("div");e.classList.add("stepper__container__divisor");Array.from(this.childOptions).forEach(((t,s)=>{if(this.childOptions.length-1!=s){t.insertAdjacentHTML("afterend",e.outerHTML)}}))}render(){return t(s,{key:"a793010b385c44a42b19bcb31ec1a6f828ac32e6",class:"stepper__container"},t("slot",{key:"2d9955120102d625dba629acfa47380c3eed2017"}))}get el(){return r(this)}};o.style=i;export{o as bds_stepper};
//# sourceMappingURL=p-0ef8de7f.entry.js.map