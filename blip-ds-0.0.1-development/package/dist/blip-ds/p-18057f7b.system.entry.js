var __awaiter=this&&this.__awaiter||function(t,o,i,r){function n(t){return t instanceof i?t:new i((function(o){o(t)}))}return new(i||(i=Promise))((function(i,e){function a(t){try{s(r.next(t))}catch(t){e(t)}}function c(t){try{s(r["throw"](t))}catch(t){e(t)}}function s(t){t.done?i(t.value):n(t.value).then(a,c)}s((r=r.apply(t,o||[])).next())}))};var __generator=this&&this.__generator||function(t,o){var i={label:0,sent:function(){if(e[0]&1)throw e[1];return e[1]},trys:[],ops:[]},r,n,e,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(t){return function(o){return s([t,o])}}function s(c){if(r)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(i=0)),i)try{if(r=1,n&&(e=c[0]&2?n["return"]:c[0]?n["throw"]||((e=n["return"])&&e.call(n),0):n.next)&&!(e=e.call(n,c[1])).done)return e;if(n=0,e)c=[c[0]&2,e.value];switch(c[0]){case 0:case 1:e=c;break;case 4:i.label++;return{value:c[1],done:false};case 5:i.label++;n=c[1];c=[0];continue;case 7:c=i.ops.pop();i.trys.pop();continue;default:if(!(e=i.trys,e=e.length>0&&e[e.length-1])&&(c[0]===6||c[0]===2)){i=0;continue}if(c[0]===3&&(!e||c[1]>e[0]&&c[1]<e[3])){i.label=c[1];break}if(c[0]===6&&i.label<e[1]){i.label=e[1];e=c;break}if(e&&i.label<e[2]){i.label=e[2];i.ops.push(c);break}if(e[2])i.ops.pop();i.trys.pop();continue}c=o.call(t,i)}catch(t){c=[6,t];n=0}finally{r=e=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var o,i,r,n;return{setters:[function(t){o=t.r;i=t.c;r=t.h;n=t.a}],execute:function(){var e='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';var a=t("bds_accordion",function(){function t(t){o(this,t);this.bdsToggle=i(this,"bdsToggle");this.bdsAccordionOpen=i(this,"bdsAccordionOpen");this.bdsAccordionClose=i(this,"bdsAccordionClose");this.accGroup=null;this.accheaders=null;this.accBodies=null;this.isOpen=false;this.numberElement=null;this.condition=false;this.startOpen=false;this.divisor=true}t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.isOpen=!this.isOpen;this.bdsToggle.emit({value:this.isOpen});return[2]}))}))};t.prototype.open=function(){return __awaiter(this,void 0,void 0,(function(){var t,o;return __generator(this,(function(i){(t=this.accheaders)===null||t===void 0?void 0:t.open();(o=this.accBodies)===null||o===void 0?void 0:o.open();this.isOpen=true;return[2]}))}))};t.prototype.close=function(){return __awaiter(this,void 0,void 0,(function(){var t,o;return __generator(this,(function(i){(t=this.accheaders)===null||t===void 0?void 0:t.close();(o=this.accBodies)===null||o===void 0?void 0:o.close();this.isOpen=false;return[2]}))}))};t.prototype.notStart=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.startOpen=false;return[2]}))}))};t.prototype.reciveNumber=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(o){this.numberElement=t;return[2]}))}))};t.prototype.isOpenChanged=function(t){var o,i,r,n,e;if(t){if(this.accGroup.collapse=="single"&&this.condition===false){(o=this.accGroup)===null||o===void 0?void 0:o.closeAll(this.numberElement)}(i=this.accheaders)===null||i===void 0?void 0:i.open();(r=this.accBodies)===null||r===void 0?void 0:r.open();this.bdsAccordionOpen.emit()}else{(n=this.accheaders)===null||n===void 0?void 0:n.close();(e=this.accBodies)===null||e===void 0?void 0:e.close();this.bdsAccordionClose.emit()}this.condition=false};t.prototype.divisorChanged=function(t){var o=this.element.querySelector("bds-accordion-body");if(o){o.divisor(t)}};t.prototype.componentWillLoad=function(){this.accGroup=this.element.parentElement.tagName=="BDS-ACCORDION-GROUP"&&this.element.parentElement;this.accheaders=this.element.querySelector("bds-accordion-header");this.accBodies=this.element.querySelector("bds-accordion-body");var t=this.element.querySelector("bds-accordion-body");if(t){t.divisor(this.divisor)}if(this.startOpen===true){this.condition=true;this.isOpen=true}};t.prototype.render=function(){return r("div",{key:"5026d0a595341208663ef3020c3930364ee50195",class:"accordion_group"},r("slot",{key:"b2f75fc28ba9752f1bcb935daaa9df818063e31d"}))};Object.defineProperty(t.prototype,"element",{get:function(){return n(this)},enumerable:false,configurable:true});Object.defineProperty(t,"watchers",{get:function(){return{isOpen:["isOpenChanged"],divisor:["divisorChanged"]}},enumerable:false,configurable:true});return t}());a.style=e}}}));
//# sourceMappingURL=p-18057f7b.system.entry.js.map