import{r as t,c as e,h as i,H as s}from"./p-C3J6Z5OX.js";const r=':host{position:relative;display:-ms-flexbox;display:flex;width:100%;height:32px}.track-bg{position:absolute;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;inset:0 8px;pointer-events:none}.track-bg .progress-bar{position:absolute;height:4px;border-radius:1rem;z-index:2}.track-bg .progress-bar-liner{background-color:var(--color-primary, rgb(30, 107, 241))}.track-bg .progress-bar-tooltip{position:absolute;top:-6px;right:-0.5rem}.track-bg .progress-bar-thumb{position:relative;width:1rem;height:1rem;border-radius:1rem;background-color:var(--color-primary, rgb(30, 107, 241));z-index:0}.track-bg .progress-bar-thumb::before{content:"";position:absolute;inset:0;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));border-radius:1rem;-webkit-transition:all 0.3s ease-in-out;transition:all 0.3s ease-in-out}.track-bg .progress-bar-hover .progress-bar-thumb::before{-webkit-transform:scale(2);transform:scale(2)}.track-bg::before{content:"";position:absolute;inset:0;height:4px;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16;border-radius:1rem}.track-bg .step{position:relative;width:2px;height:8px;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;background-color:var(--color-content-disable, rgb(89, 89, 89));border-bottom-left-radius:1rem;border-bottom-right-radius:1rem}.track-bg .step .label-step{margin-top:1rem}.element-min{position:relative;height:4px;background-color:var(--color-primary, rgb(30, 107, 241));border-top-left-radius:1rem;border-bottom-left-radius:1rem}.element-max{position:relative;height:4px;border-top-right-radius:1rem;border-bottom-right-radius:1rem}.input_slide{-webkit-appearance:none;-moz-appearance:none;appearance:none;margin:0;background:transparent;cursor:pointer;width:100%;height:4px;position:relative;border-radius:1rem;background:transparent;color:-internal-light-dark(transparent, transparent)}.input_slide.has_min{border-top-left-radius:0;border-bottom-left-radius:0;margin-left:0}.input_slide.has_max{border-top-right-radius:0;border-bottom-right-radius:0}.input_slide:hover .input_slide::-webkit-slider-thumb,.input_slide:hover .input_slide::-moz-range-thumb{-webkit-appearance:none}.input_slide::-webkit-slider-thumb,.input_slide::-moz-range-thumb{-webkit-appearance:none;position:relative;height:16px;width:16px;border-radius:50%;border:none}.group_slide{position:relative;width:100%}.group_slide .input_slide{width:inherit;position:absolute}.group_slide .input_slide_start{left:0}.group_slide .input_slide_end{right:0}.group_slide .input_slide::-webkit-slider-thumb,.group_slide .input_slide::-moz-range-thumb{-webkit-appearance:none}';const o=class{constructor(i){t(this,i);this.bdsChange=e(this,"bdsChange");var s,r;this.inputValue=(r=(s=this.value)===null||s===void 0?void 0:s.toString())!==null&&r!==void 0?r:this.min?this.min.toString():"0";this.value=this.min?this.min:0;this.markers=false;this.label=false;this.type="fill";this.dataTest=null;this.refInputSlide=t=>{this.inputSlide=t};this.refBdsTooltip=t=>{this.bdsTooltip=t};this.refProgressBar=t=>{this.progressBar=t};this.valuePercent=t=>{const e=t;const i=e.min?parseInt(e.min):0;const s=parseInt(e.max);const r=parseInt(e.value);const o=(r-i)*100/(s-i);return o};this.onInputSlide=t=>{const e=t.target;this.progressBar.style.width=`${this.valuePercent(e)}%`;const i=this.emiterChange(parseInt(e.value));this.inputValue=this.stepArray.length>0?i.name:e.value;this.bdsChange.emit(i)};this.onInputMouseEnter=()=>{this.bdsTooltip.visible();this.progressBar.classList.add(`progress-bar-hover`)};this.onInputMouseLeave=()=>{this.bdsTooltip.invisible();this.progressBar.classList.remove(`progress-bar-hover`)};this.emiterChange=t=>{if(this.internalOptions){return this.stepArray[t]}else{return this.stepArray.find((e=>parseInt(e.name)===t))}}}componentWillLoad(){if(this.dataMarkers){if(typeof this.dataMarkers==="string"){this.internalOptions=JSON.parse(this.dataMarkers);this.stepArray=this.internalOptions}else{this.internalOptions=this.dataMarkers;this.stepArray=this.internalOptions}}else{this.stepArray=this.arrayToSteps((this.max-this.min)/this.step,Number.isInteger((this.max-this.min)/this.step))}}componentDidLoad(){this.progressBar.style.width=`${this.valuePercent(this.inputSlide)}%`}componentDidRender(){if(this.internalOptions){this.inputSlide.min="0";this.inputSlide.max=`${this.internalOptions.length-1}`;this.inputSlide.step="1"}else{this.inputSlide.min=this.min?`${this.min}`:"";this.inputSlide.max=this.max?`${this.max}`:"";this.inputSlide.step=this.step?`${this.step}`:""}}componentDidUpdate(){this.progressBar.style.width=`${this.valuePercent(this.inputSlide)}%`;const t=this.emiterChange(parseInt(this.inputSlide.value));this.inputValue=this.stepArray.length>0?t.name:this.inputSlide.value}arrayToSteps(t,e){const i=e?t+1:t;const s=[];for(let t=0;t<i;t++){s.push(t)}return s.map((t=>({value:t,name:t*this.step+this.min})))}render(){return i(s,{key:"29451c00acb28b35da8b8cc8cb632cf933b664a8"},i("input",{key:"e72694e41261abfa6094ec05bca761a5b90e3117",ref:this.refInputSlide,type:"range",class:{input_slide:true},value:this.value,onInput:this.onInputSlide,onMouseEnter:this.onInputMouseEnter,onMouseLeave:this.onInputMouseLeave,"data-test":this.dataTest}),i("div",{key:"66c0346da07f072e40e4d6e7ee819473b66d1e84",class:"track-bg"},i("div",{key:"30a91ac84bac7d436b36f7c88c2465158e4812cb",class:{[`progress-bar`]:true,[`progress-bar-liner`]:this.type!=="no-linear"},ref:this.refProgressBar},i("bds-tooltip",{key:"97cf038eb25945c3dcdf8e1d08495f2bf6ce0377",ref:this.refBdsTooltip,class:{[`progress-bar-tooltip`]:true},position:"top-center","tooltip-text":this.inputValue},i("div",{key:"b21a646fcf26179271064c7256ccb1caaa2b5e38",class:{[`progress-bar-thumb`]:true}}))),this.markers&&this.stepArray.map(((t,e)=>i("div",{key:e,class:`step`},this.label&&i("bds-typo",{class:"label-step",variant:"fs-10"},`${t.name}`))))))}};o.style=r;export{o as bds_slider};
//# sourceMappingURL=p-4501dcb7.entry.js.map