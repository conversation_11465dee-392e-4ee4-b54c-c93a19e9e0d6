{"version": 3, "names": ["breadcrumbCss", "Breadcrumb", "exports", "class_1", "hostRef", "this", "items", "parsedItems", "isDropdownOpen", "prototype", "parseItems", "newValue", "JSON", "parse", "error", "componentWillLoad", "toggleDropdown", "render", "_this", "length", "h", "visibleItems", "label", "href", "direction", "map", "item", "index", "class", "breadcrumb__item", "position", "slot", "padding", "gap", "slice", "subItem", "idx", "concat", "name", "theme", "size", "variant", "color", "onClick", "margin", "bold"], "sources": ["src/components/breadcrumb/breadcrumb.scss?tag=bds-breadcrumb&encapsulation=shadow", "src/components/breadcrumb/breadcrumb.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n\n  * {\n    color: $color-content-default;\n  }\n}\n\n.button--icon {\n  transform: rotate(180deg);\n  color: $color-content-ghost;\n}\n\n.breadcrumb__button {\n  &--0 {\n    padding-left: 0;\n\n    .button--icon {\n      display: none;\n    }\n  }\n\n  &--1 {\n    padding-left: 8px;\n  }\n\n  &--2 {\n    padding-left: 16px;\n  }\n\n  &--3 {\n    padding-left: 24px;\n  }\n\n  &--4 {\n    padding-left: 32px;\n  }\n}\n\n.breadcrumb__link--text {\n  color: $color-content-disable;\n}\n\n.breadcrumb__link {\n  text-decoration: none;\n}", "import { Component, h, Prop, State, Watch } from '@stencil/core';\n\n@Component({\n  tag: 'bds-breadcrumb',\n  styleUrl: 'breadcrumb.scss',\n  shadow: true,\n})\nexport class Breadcrumb {\n  @Prop() items: string | Array<{ label: string; href?: string }> = [];\n\n  @State() parsedItems: Array<{ label: string; href?: string }> = [];\n\n  @State() isDropdownOpen: boolean = false;\n\n  @Watch('items')\n  parseItems(newValue: string | Array<{ label: string; href?: string }>) {\n    if (typeof newValue === 'string') {\n      try {\n        this.parsedItems = JSON.parse(newValue);\n      } catch (error) {\n        this.parsedItems = [];\n      }\n    } else {\n      this.parsedItems = newValue;\n    }\n  }\n\n  componentWillLoad() {\n    this.parseItems(this.items);\n  }\n\n  toggleDropdown() {\n    this.isDropdownOpen = !this.isDropdownOpen;\n  }\n\n  render() {\n    if (!this.parsedItems || this.parsedItems.length === 0) {\n      return <p>Sem itens para exibir no Breadcrumb.</p>;\n    }\n\n    const visibleItems =\n      this.parsedItems.length <= 3\n        ? this.parsedItems\n        : [\n            this.parsedItems[0],\n            { label: '...', href: null },\n            this.parsedItems[this.parsedItems.length - 1],\n          ];\n\n    return (\n      <nav aria-label=\"breadcrumb\">\n        <bds-grid direction=\"row\" align-items=\"center\">\n          {visibleItems.map((item, index) => (\n            <bds-grid\n              class={{\n                breadcrumb__item: true,\n                'breadcrumb__item--active': index === visibleItems.length - 1,\n              }}\n              aria-current={index === visibleItems.length - 1 ? 'page' : null}\n            >\n              {item.label === '...' ? (\n                <bds-dropdown active-mode=\"click\" position=\"auto\">\n                  <bds-grid slot=\"dropdown-content\">\n                    <bds-grid direction=\"column\" padding=\"1\" gap=\"half\">\n                      {this.parsedItems.slice(1, -1).map((subItem, idx) => (\n                        <bds-grid class={`breadcrumb__button--${idx}`}>\n                          {subItem.href ? (\n                            <a\n                              href={subItem.href}\n                              class={`breadcrumb__link breadcrumb__button--${idx}`}\n                            >\n                              <bds-grid align-items=\"center\" gap=\"half\">\n                                <bds-icon\n                                  name=\"reply\"\n                                  theme=\"outline\"\n                                  class=\"button--icon\"\n                                  size=\"x-small\"\n                                ></bds-icon>\n                                <bds-button\n                                  variant=\"text\"\n                                  color=\"content\"\n                                  size=\"short\"\n                                >\n                                  {subItem.label}\n                                </bds-button>\n                              </bds-grid>\n                            </a>\n                          ) : (\n                            <span>{subItem.label}</span>\n                          )}\n                        </bds-grid>\n                      ))}\n                    </bds-grid>\n                  </bds-grid>\n                  <bds-grid slot=\"dropdown-activator\" align-items=\"center\">\n                    <bds-button\n                      variant=\"text\"\n                      color=\"content\"\n                      size=\"short\"\n                      onClick={() => this.toggleDropdown()}\n                      icon-left=\"more-options-horizontal\"\n                    ></bds-button>\n                    <bds-icon name=\"arrow-right\" size=\"x-small\"></bds-icon>\n                  </bds-grid>\n                </bds-dropdown>\n              ) : item.href ? (\n                <bds-grid direction=\"row\">\n                  <bds-typo\n                    variant=\"fs-12\"\n                    margin={false}\n                    class=\"breadcrumb__link--text\"\n                  >\n                    <a href={item.href} class=\"breadcrumb__link\">\n                      {item.label}\n                    </a>\n                  </bds-typo>\n                  <bds-icon name=\"arrow-right\" size=\"x-small\"></bds-icon>\n                </bds-grid>\n              ) : (\n                <bds-grid direction=\"row\">\n                  <bds-typo variant=\"fs-12\" bold=\"semi-bold\" margin={false}>\n                    {item.label}\n                  </bds-typo>\n                </bds-grid>\n              )}\n            </bds-grid>\n          ))}\n        </bds-grid>\n      </nav>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAgB,8jB,ICOTC,EAAUC,EAAA,4BALvB,SAAAC,EAAAC,G,UAMUC,KAAKC,MAAqD,GAEzDD,KAAWE,YAA4C,GAEvDF,KAAcG,eAAY,KAuHpC,CApHCL,EAAAM,UAAAC,WAAA,SAAWC,GACT,UAAWA,IAAa,SAAU,CAChC,IACEN,KAAKE,YAAcK,KAAKC,MAAMF,E,CAC9B,MAAOG,GACPT,KAAKE,YAAc,E,MAEhB,CACLF,KAAKE,YAAcI,C,GAIvBR,EAAAM,UAAAM,kBAAA,WACEV,KAAKK,WAAWL,KAAKC,M,EAGvBH,EAAAM,UAAAO,eAAA,WACEX,KAAKG,gBAAkBH,KAAKG,c,EAG9BL,EAAAM,UAAAQ,OAAA,eAAAC,EAAAb,KACE,IAAKA,KAAKE,aAAeF,KAAKE,YAAYY,SAAW,EAAG,CACtD,OAAOC,EAAA,gD,CAGT,IAAMC,EACJhB,KAAKE,YAAYY,QAAU,EACvBd,KAAKE,YACL,CACEF,KAAKE,YAAY,GACjB,CAAEe,MAAO,MAAOC,KAAM,MACtBlB,KAAKE,YAAYF,KAAKE,YAAYY,OAAS,IAGnD,OACEC,EAAA,oBAAgB,cACdA,EAAU,YAAAI,UAAU,MAAK,cAAa,UACnCH,EAAaI,KAAI,SAACC,EAAMC,GAAK,OAC5BP,EAAA,YACEQ,MAAO,CACLC,iBAAkB,KAClB,2BAA4BF,IAAUN,EAAaF,OAAS,GAC7D,eACaQ,IAAUN,EAAaF,OAAS,EAAI,OAAS,MAE1DO,EAAKJ,QAAU,MACdF,EAA0B,sCAAQU,SAAS,QACzCV,EAAU,YAAAW,KAAK,oBACbX,EAAU,YAAAI,UAAU,SAASQ,QAAQ,IAAIC,IAAI,QAC1Cf,EAAKX,YAAY2B,MAAM,GAAG,GAAIT,KAAI,SAACU,EAASC,GAAG,OAC9ChB,EAAA,YAAUQ,MAAO,uBAAAS,OAAuBD,IACrCD,EAAQZ,KACPH,EAAA,KACEG,KAAMY,EAAQZ,KACdK,MAAO,wCAAAS,OAAwCD,IAE/ChB,EAAA,0BAAsB,SAASa,IAAI,QACjCb,EAAA,YACEkB,KAAK,QACLC,MAAM,UACNX,MAAM,eACNY,KAAK,YAEPpB,EAAA,cACEqB,QAAQ,OACRC,MAAM,UACNF,KAAK,SAEJL,EAAQb,SAKfF,EAAO,YAAAe,EAAQb,OAxB2B,MA8BpDF,EAAA,YAAUW,KAAK,qBAAoB,cAAa,UAC9CX,EACE,cAAAqB,QAAQ,OACRC,MAAM,UACNF,KAAK,QACLG,QAAS,WAAM,OAAAzB,EAAKF,gBAAL,EACL,wCAEZI,EAAU,YAAAkB,KAAK,cAAcE,KAAK,cAGpCd,EAAKH,KACPH,EAAA,YAAUI,UAAU,OAClBJ,EACE,YAAAqB,QAAQ,QACRG,OAAQ,MACRhB,MAAM,0BAENR,EAAA,KAAGG,KAAMG,EAAKH,KAAMK,MAAM,oBACvBF,EAAKJ,QAGVF,EAAA,YAAUkB,KAAK,cAAcE,KAAK,aAGpCpB,EAAU,YAAAI,UAAU,OAClBJ,EAAU,YAAAqB,QAAQ,QAAQI,KAAK,YAAYD,OAAQ,OAChDlB,EAAKJ,QArEc,K,iIA7CjB,I", "ignoreList": []}