{"version": 3, "names": ["expansionPanelHeaderCss", "ExpansionPanelHeader", "render", "h", "Host", "key", "class", "tag", "variant", "this", "text"], "sources": ["src/components/expansion-panel/expansion-panel-header/expansion-panel-header.scss?tag=bds-expansion-panel-header&encapsulation=shadow", "src/components/expansion-panel/expansion-panel-header/expansion-panel-header.tsx"], "sourcesContent": [":host {\n  display: flex;\n  align-items: center;\n}\n\n.header {\n  width: 70px;\n  padding-right: 6px;\n}", "import { Component, Host, h, ComponentInterface, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel-header',\n  styleUrl: 'expansion-panel-header.scss',\n  shadow: true,\n})\nexport class ExpansionPanelHeader implements ComponentInterface {\n  @Prop() text?: string;\n\n  render() {\n    return (\n      <Host>\n        <div class=\"header\">\n          <slot />\n        </div>\n        <bds-typo tag=\"p\" variant=\"fs-12\">\n          {this.text}\n        </bds-typo>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAA0B,wH,MCOnBC,EAAoB,M,yBAG/B,MAAAC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,UACTH,EAAA,QAAAE,IAAA,8CAEFF,EAAA,YAAAE,IAAA,2CAAUE,IAAI,IAAIC,QAAQ,SACvBC,KAAKC,M", "ignoreList": []}