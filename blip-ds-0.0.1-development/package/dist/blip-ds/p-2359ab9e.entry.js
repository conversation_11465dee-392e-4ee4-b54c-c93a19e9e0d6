import{r as t,c as s,h as e,H as i}from"./p-C3J6Z5OX.js";const a=":host{display:none}:host(.is-open){display:block;height:100%}.tab_item{height:100%}.tab_item_content{display:none;height:100%}.tab_item_content--open{display:block}";const l=class{constructor(e){t(this,e);this.tabDisabled=s(this,"tabDisabled");this.numberElement=null;this.label=null;this.icon=null;this.iconPosition="left";this.iconTheme="outline";this.badge=false;this.badgeShape="circle";this.badgeColor="system";this.badgeIcon=null;this.badgeAnimation=false;this.badgePosition="left";this.badgeNumber=null;this.disable=false;this.error=false;this.headerStyle=null;this.contentStyle=null;this.open=false;this.dataTest=null}async reciveNumber(t){this.numberElement=t}disableChanged(){this.tabDisabled.emit({item:this.numberElement,disable:this.disable})}render(){return e(i,{key:"aed24d3a7e507e47baf6ab2ee66b93bf7780ddf1",class:{[`is-open`]:this.disable===true?false:this.open}},e("div",{key:"8f66f58d7d3537c1748ba8318831ac65d56946d5",class:{tab_item:true},"data-test":this.dataTest},e("div",{key:"a9bbc47a9c74f0784a52f7def97f3b4b7f1a4768",class:{tab_item_content:true,[`tab_item_content--open`]:this.open}},e("slot",{key:"c6385c83ab5dbcb7a90868224b3b1584055c7eee"}))))}static get watchers(){return{disable:["disableChanged"]}}};l.style=a;export{l as bds_tab_item};
//# sourceMappingURL=p-2359ab9e.entry.js.map