{"version": 3, "file": "bds-radio-group.entry.esm.js", "sources": ["src/components/radio-button/radio-group.tsx"], "sourcesContent": ["import { Component, h, Host, Element, Prop, Watch, Event, EventEmitter, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-radio-group',\n  scoped: true,\n})\nexport class RadioGroup implements ComponentInterface {\n  private radioGroupElement?: HTMLCollectionOf<HTMLBdsRadioElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n  /**\n   * Emitted when the value has changed due to a click event.\n   */\n  @Event() bdsRadioGroupChange: EventEmitter;\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n\n    this.bdsRadioGroupChange.emit({ value });\n  }\n\n  componentWillRender() {\n    this.radioGroupElement = this.element.getElementsByTagName('bds-radio') as HTMLCollectionOf<HTMLBdsRadioElement>;\n    for (let i = 0; i < this.radioGroupElement.length; i++) {\n      this.radioGroupElement[i].addEventListener('bdsChange', (event: CustomEvent) =>\n        this.chagedOptions(this.radioGroupElement[i].value, event),\n      );\n    }\n  }\n\n  private chagedOptions = (value: string, event: CustomEvent): void => {\n    if (event.detail.checked == true) {\n      this.value = value;\n    }\n  };\n\n  private setSelectedRadio(value: string) {\n    const radios = this.radioGroupElement;\n    for (let i = 0; i < radios.length; i++) {\n      const getValue = radios[i].value;\n      radios[i].checked = false;\n      if (radios[i].checked == false && value == getValue) {\n        radios[i].checked = true;\n      }\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;MAMa,UAAU,GAAA,MAAA;AAJvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAKU,QAAA,IAAiB,CAAA,iBAAA,GAA2C,IAAI;QA6BhE,IAAA,CAAA,aAAa,GAAG,CAAC,KAAa,EAAE,KAAkB,KAAU;YAClE,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;AAChC,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;AAEtB,SAAC;AAoBF;AAvCC,IAAA,YAAY,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAE5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;;IAG1C,mBAAmB,GAAA;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAA0C;AAChH,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,KAAkB,KACzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAC3D;;;AAUG,IAAA,gBAAgB,CAAC,KAAa,EAAA;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB;AACrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;AAChC,YAAA,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK;AACzB,YAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE;AACnD,gBAAA,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI;;;;IAK9B,MAAM,GAAA;QACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;;;;"}