{"version": 3, "names": ["listCss", "List", "exports", "class_1", "hostRef", "_this", "this", "itemListElement", "typeList", "chagedOptions", "event", "detail", "checked", "value", "setSelectedCheckbox", "setSelectedSwitch", "onClickActionsButtons", "bdsClickActionsButtons", "emit", "prototype", "componentWillLoad", "data", "dataChanged", "componentWillRender", "updateData", "setitemListElement", "componentDidRender", "internalDataChanged", "valueChanged", "setSelectedRadio", "element", "shadowRoot", "querySelectorAll", "getElementsByTagName", "i", "length", "addEventListener", "internalData", "JSON", "parse", "itemList", "itens", "Array", "from", "radios", "filter", "item", "construct", "text", "_a", "secondaryText", "_b", "avatar<PERSON><PERSON>", "_c", "avatar<PERSON><PERSON><PERSON><PERSON>", "_d", "_e", "bdsListRadioChange", "checkboxs", "result", "map", "term", "bdsListCheckboxChange", "Switch", "bdsListSwitchChange", "render", "h", "Host", "key", "class", "list", "idx", "icon", "chips", "actionsButtons", "onBdsChecked", "ev", "onBdsClickActionButtom", "dataTest"], "sources": ["src/components/list/list.scss?tag=bds-list&encapsulation=shadow", "src/components/list/list.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { Data } from './list-interface';\n\nexport type TypeList = 'checkbox' | 'radio' | 'switch' | 'default';\n\n@Component({\n  tag: 'bds-list',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class List {\n  private itemListElement?: HTMLCollectionOf<HTMLBdsListItemElement> | NodeListOf<HTMLBdsListItemElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalData: Data[];\n  /**\n   * Typelist. Used to .\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n\n  /**\n   * The Data of the list\n   * Should be passed this way:\n   * data='[{\"value\": \"01\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"true\",\"icon\": \"settings-builder\"}, {\"value\": \"02\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"false\",\"icon\": \"settings-builder\",}]'\n   * Data can also be passed as child by using bds-list-item component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true, reflect: true }) data?: string | Data[];\n\n  /**\n   * Emitted when the value checkboxes has changed because of a click event.\n   */\n  @Event() bdsListCheckboxChange!: EventEmitter;\n  /**\n   * Emitted when the value radios has changed because of a click event.\n   */\n  @Event() bdsListRadioChange!: EventEmitter;\n  /**\n   * Emitted when the value switches has changed because of a click event.\n   */\n  @Event() bdsListSwitchChange!: EventEmitter;\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionsButtons!: EventEmitter;\n\n  componentWillLoad() {\n    this.data && this.dataChanged();\n  }\n\n  componentWillRender() {\n    this.data && this.updateData();\n    if (!this.data) {\n      this.setitemListElement();\n    }\n  }\n  componentDidRender() {\n    if (this.data) {\n      this.internalDataChanged();\n    }\n  }\n\n  @Watch('data')\n  dataChanged() {\n    this.updateData();\n  }\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n  }\n\n  @Watch('internalData')\n  internalDataChanged() {\n    this.itemListElement = this.element.shadowRoot.querySelectorAll('bds-list-item');\n  }\n\n  private setitemListElement() {\n    this.itemListElement = this.element.getElementsByTagName(\n      'bds-list-item',\n    ) as HTMLCollectionOf<HTMLBdsListItemElement>;\n\n    for (let i = 0; i < this.itemListElement.length; i++) {\n      this.itemListElement[i].typeList = this.typeList;\n      this.itemListElement[i].addEventListener('bdsChecked', (event: CustomEvent) => this.chagedOptions(event));\n    }\n  }\n\n  private updateData() {\n    if (this.data) {\n      if (typeof this.data === 'string') {\n        this.internalData = JSON.parse(this.data);\n      } else {\n        this.internalData = this.data;\n      }\n    }\n  }\n\n  private chagedOptions = (event: CustomEvent): void => {\n    const { detail } = event;\n    if (detail.typeList == 'radio') {\n      if (detail.checked == true) {\n        this.value = detail;\n      }\n    }\n    if (detail.typeList == 'checkbox') {\n      this.setSelectedCheckbox();\n    }\n    if (detail.typeList == 'switch') {\n      this.setSelectedSwitch();\n    }\n  };\n\n  private setSelectedRadio(itemList) {\n    const itens = Array.from(this.itemListElement);\n    const radios = itens.filter((item) => item.typeList == 'radio');\n    for (let i = 0; i < radios.length; i++) {\n      if (radios[i].value != itemList.value) {\n        radios[i].checked = false;\n      } else {\n        const construct = {\n          value: radios[i].value,\n          text: radios[i]?.text,\n          secondaryText: radios[i]?.secondaryText,\n          avatarName: radios[i]?.avatarName,\n          avatarThumbnail: radios[i]?.avatarThumbnail,\n          typeList: radios[i]?.typeList,\n        };\n        this.bdsListRadioChange.emit(construct);\n      }\n    }\n  }\n\n  private setSelectedCheckbox() {\n    const checkboxs = this.itemListElement;\n    const itens = Array.from(checkboxs).filter((item) => item.typeList == 'checkbox');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListCheckboxChange.emit(result);\n  }\n\n  private setSelectedSwitch() {\n    const Switch = this.itemListElement;\n    const itens = Array.from(Switch).filter((item) => item.typeList == 'switch');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListSwitchChange.emit(result);\n  }\n\n  private onClickActionsButtons = (event: CustomEvent): void => {\n    const { detail } = event;\n    this.bdsClickActionsButtons.emit(detail);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            list: true,\n          }}\n        >\n          {this.internalData ? (\n            this.internalData.map((item, idx) => (\n              <bds-list-item\n                key={idx}\n                value={item.value}\n                text={item.text}\n                type-list={this.typeList ? this.typeList : item.typeList}\n                secondary-text={item.secondaryText}\n                avatar-name={item.avatarName}\n                avatar-thumbnail={item.avatarThumbnail}\n                checked={item.checked}\n                icon={item.icon}\n                chips={item.chips}\n                actionsButtons={item.actionsButtons}\n                onBdsChecked={(ev) => this.chagedOptions(ev)}\n                onBdsClickActionButtom={(ev) => this.onClickActionsButtons(ev)}\n                dataTest={item.dataTest}\n              ></bds-list-item>\n            ))\n          ) : (\n            <slot></slot>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kKAAA,IAAMA,EAAU,g1E,ICUHC,EAAIC,EAAA,sBALjB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,8OAMUA,KAAeC,gBAAmF,KAQlGD,KAAQE,SAAc,KAmFtBF,KAAAG,cAAgB,SAACC,GACf,IAAAC,EAAWD,EAAKC,OACxB,GAAIA,EAAOH,UAAY,QAAS,CAC9B,GAAIG,EAAOC,SAAW,KAAM,CAC1BP,EAAKQ,MAAQF,C,EAGjB,GAAIA,EAAOH,UAAY,WAAY,CACjCH,EAAKS,qB,CAEP,GAAIH,EAAOH,UAAY,SAAU,CAC/BH,EAAKU,mB,CAET,EAsDQT,KAAAU,sBAAwB,SAACN,GACvB,IAAAC,EAAWD,EAAKC,OACxBN,EAAKY,uBAAuBC,KAAKP,EACnC,CAoCD,CA9JCR,EAAAgB,UAAAC,kBAAA,WACEd,KAAKe,MAAQf,KAAKgB,a,EAGpBnB,EAAAgB,UAAAI,oBAAA,WACEjB,KAAKe,MAAQf,KAAKkB,aAClB,IAAKlB,KAAKe,KAAM,CACdf,KAAKmB,oB,GAGTtB,EAAAgB,UAAAO,mBAAA,WACE,GAAIpB,KAAKe,KAAM,CACbf,KAAKqB,qB,GAKTxB,EAAAgB,UAAAG,YAAA,WACEhB,KAAKkB,Y,EAIPrB,EAAAgB,UAAAS,aAAA,SAAaf,GACXP,KAAKuB,iBAAiBhB,E,EAIxBV,EAAAgB,UAAAQ,oBAAA,WACErB,KAAKC,gBAAkBD,KAAKwB,QAAQC,WAAWC,iBAAiB,gB,EAG1D7B,EAAAgB,UAAAM,mBAAA,eAAApB,EAAAC,KACNA,KAAKC,gBAAkBD,KAAKwB,QAAQG,qBAClC,iBAGF,IAAK,IAAIC,EAAI,EAAGA,EAAI5B,KAAKC,gBAAgB4B,OAAQD,IAAK,CACpD5B,KAAKC,gBAAgB2B,GAAG1B,SAAWF,KAAKE,SACxCF,KAAKC,gBAAgB2B,GAAGE,iBAAiB,cAAc,SAAC1B,GAAuB,OAAAL,EAAKI,cAAcC,EAAnB,G,GAI3EP,EAAAgB,UAAAK,WAAA,WACN,GAAIlB,KAAKe,KAAM,CACb,UAAWf,KAAKe,OAAS,SAAU,CACjCf,KAAK+B,aAAeC,KAAKC,MAAMjC,KAAKe,K,KAC/B,CACLf,KAAK+B,aAAe/B,KAAKe,I,IAoBvBlB,EAAAgB,UAAAU,iBAAA,SAAiBW,G,cACvB,IAAMC,EAAQC,MAAMC,KAAKrC,KAAKC,iBAC9B,IAAMqC,EAASH,EAAMI,QAAO,SAACC,GAAS,OAAAA,EAAKtC,UAAY,OAAjB,IACtC,IAAK,IAAI0B,EAAI,EAAGA,EAAIU,EAAOT,OAAQD,IAAK,CACtC,GAAIU,EAAOV,GAAGrB,OAAS2B,EAAS3B,MAAO,CACrC+B,EAAOV,GAAGtB,QAAU,K,KACf,CACL,IAAMmC,EAAY,CAChBlC,MAAO+B,EAAOV,GAAGrB,MACjBmC,MAAMC,EAAAL,EAAOV,MAAE,MAAAe,SAAA,SAAAA,EAAED,KACjBE,eAAeC,EAAAP,EAAOV,MAAE,MAAAiB,SAAA,SAAAA,EAAED,cAC1BE,YAAYC,EAAAT,EAAOV,MAAE,MAAAmB,SAAA,SAAAA,EAAED,WACvBE,iBAAiBC,EAAAX,EAAOV,MAAE,MAAAqB,SAAA,SAAAA,EAAED,gBAC5B9C,UAAUgD,EAAAZ,EAAOV,MAAE,MAAAsB,SAAA,SAAAA,EAAEhD,UAEvBF,KAAKmD,mBAAmBvC,KAAK6B,E,IAK3B5C,EAAAgB,UAAAL,oBAAA,WACN,IAAM4C,EAAYpD,KAAKC,gBACvB,IAAMkC,EAAQC,MAAMC,KAAKe,GAAWb,QAAO,SAACC,GAAS,OAAAA,EAAKtC,UAAY,UAAjB,IACrD,IAAMmD,EAASlB,EACZI,QAAO,SAACC,GAAS,OAAAA,EAAKlC,OAAL,IACjBgD,KAAI,SAACC,GAAI,OACRhD,MAAOgD,EAAKhD,MACZmC,KAAMa,IAAI,MAAJA,SAAA,SAAAA,EAAMb,KACZE,cAAeW,IAAI,MAAJA,SAAA,SAAAA,EAAMX,cACrBE,WAAYS,IAAI,MAAJA,SAAA,SAAAA,EAAMT,WAClBE,gBAAiBO,IAAI,MAAJA,SAAA,SAAAA,EAAMP,gBACvB9C,SAAUqD,IAAI,MAAJA,SAAA,SAAAA,EAAMrD,SANR,IAQZF,KAAKwD,sBAAsB5C,KAAKyC,E,EAG1BxD,EAAAgB,UAAAJ,kBAAA,WACN,IAAMgD,EAASzD,KAAKC,gBACpB,IAAMkC,EAAQC,MAAMC,KAAKoB,GAAQlB,QAAO,SAACC,GAAS,OAAAA,EAAKtC,UAAY,QAAjB,IAClD,IAAMmD,EAASlB,EACZI,QAAO,SAACC,GAAS,OAAAA,EAAKlC,OAAL,IACjBgD,KAAI,SAACC,GAAI,OACRhD,MAAOgD,EAAKhD,MACZmC,KAAMa,IAAI,MAAJA,SAAA,SAAAA,EAAMb,KACZE,cAAeW,IAAI,MAAJA,SAAA,SAAAA,EAAMX,cACrBE,WAAYS,IAAI,MAAJA,SAAA,SAAAA,EAAMT,WAClBE,gBAAiBO,IAAI,MAAJA,SAAA,SAAAA,EAAMP,gBACvB9C,SAAUqD,IAAI,MAAJA,SAAA,SAAAA,EAAMrD,SANR,IAQZF,KAAK0D,oBAAoB9C,KAAKyC,E,EAQhCxD,EAAAgB,UAAA8C,OAAA,eAAA5D,EAAAC,KACE,OACE4D,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,MAAO,CACLC,KAAM,OAGPhE,KAAK+B,aACJ/B,KAAK+B,aAAauB,KAAI,SAACd,EAAMyB,GAAG,OAC9BL,EAAA,iBACEE,IAAKG,EACL1D,MAAOiC,EAAKjC,MACZmC,KAAMF,EAAKE,KACA,YAAA3C,EAAKG,SAAWH,EAAKG,SAAWsC,EAAKtC,SAAQ,iBACxCsC,EAAKI,cACR,cAAAJ,EAAKM,WACA,mBAAAN,EAAKQ,gBACvB1C,QAASkC,EAAKlC,QACd4D,KAAM1B,EAAK0B,KACXC,MAAO3B,EAAK2B,MACZC,eAAgB5B,EAAK4B,eACrBC,aAAc,SAACC,GAAO,OAAAvE,EAAKI,cAAcmE,EAAnB,EACtBC,uBAAwB,SAACD,GAAO,OAAAvE,EAAKW,sBAAsB4D,EAA3B,EAChCE,SAAUhC,EAAKgC,UAfa,IAmBhCZ,EAAa,c,8SAhMR,I", "ignoreList": []}