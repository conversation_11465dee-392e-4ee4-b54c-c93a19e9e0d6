{"version": 3, "names": ["RadioGroup", "exports", "class_1", "hostRef", "_this", "this", "radioGroupElement", "chagedOptions", "value", "event", "detail", "checked", "prototype", "valueChanged", "setSelectedRadio", "bdsRadioGroupChange", "emit", "componentWillRender", "element", "getElementsByTagName", "i", "this_1", "addEventListener", "length", "radios", "getValue", "render", "h", "Host", "key"], "sources": ["src/components/radio-button/radio-group.tsx"], "sourcesContent": ["import { Component, h, Host, Element, Prop, Watch, Event, EventEmitter, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-radio-group',\n  scoped: true,\n})\nexport class RadioGroup implements ComponentInterface {\n  private radioGroupElement?: HTMLCollectionOf<HTMLBdsRadioElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n  /**\n   * Emitted when the value has changed due to a click event.\n   */\n  @Event() bdsRadioGroupChange: EventEmitter;\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n\n    this.bdsRadioGroupChange.emit({ value });\n  }\n\n  componentWillRender() {\n    this.radioGroupElement = this.element.getElementsByTagName('bds-radio') as HTMLCollectionOf<HTMLBdsRadioElement>;\n    for (let i = 0; i < this.radioGroupElement.length; i++) {\n      this.radioGroupElement[i].addEventListener('bdsChange', (event: CustomEvent) =>\n        this.chagedOptions(this.radioGroupElement[i].value, event),\n      );\n    }\n  }\n\n  private chagedOptions = (value: string, event: CustomEvent): void => {\n    if (event.detail.checked == true) {\n      this.value = value;\n    }\n  };\n\n  private setSelectedRadio(value: string) {\n    const radios = this.radioGroupElement;\n    for (let i = 0; i < radios.length; i++) {\n      const getValue = radios[i].value;\n      radios[i].checked = false;\n      if (radios[i].checked == false && value == getValue) {\n        radios[i].checked = true;\n      }\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "mappings": "sKAMaA,EAAUC,EAAA,6BAJvB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,iEAKUA,KAAiBC,kBAA2C,KA6B5DD,KAAAE,cAAgB,SAACC,EAAeC,GACtC,GAAIA,EAAMC,OAAOC,SAAW,KAAM,CAChCP,EAAKI,MAAQA,C,CAEjB,CAoBD,CAvCCN,EAAAU,UAAAC,aAAA,SAAaL,GACXH,KAAKS,iBAAiBN,GAEtBH,KAAKU,oBAAoBC,KAAK,CAAER,MAAKA,G,EAGvCN,EAAAU,UAAAK,oBAAA,eAAAb,EAAAC,KACEA,KAAKC,kBAAoBD,KAAKa,QAAQC,qBAAqB,a,eAClDC,GACPC,EAAKf,kBAAkBc,GAAGE,iBAAiB,aAAa,SAACb,GACvD,OAAAL,EAAKG,cAAcH,EAAKE,kBAAkBc,GAAGZ,MAAOC,EAApD,G,aAFJ,IAAK,IAAIW,EAAI,EAAGA,EAAIf,KAAKC,kBAAkBiB,OAAQH,IAAG,C,EAA7CA,E,GAaHlB,EAAAU,UAAAE,iBAAA,SAAiBN,GACvB,IAAMgB,EAASnB,KAAKC,kBACpB,IAAK,IAAIc,EAAI,EAAGA,EAAII,EAAOD,OAAQH,IAAK,CACtC,IAAMK,EAAWD,EAAOJ,GAAGZ,MAC3BgB,EAAOJ,GAAGT,QAAU,MACpB,GAAIa,EAAOJ,GAAGT,SAAW,OAASH,GAASiB,EAAU,CACnDD,EAAOJ,GAAGT,QAAU,I,IAK1BT,EAAAU,UAAAc,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAa,QAAAE,IAAA,6C,oPAlDE,G", "ignoreList": []}