{"version": 3, "names": ["illustrationCss", "BdsIllustration", "constructor", "hostRef", "this", "type", "dataTest", "setIllustrationContent", "tokensVersion", "packageJson", "dependencies", "replace", "apiUrl", "name", "fetch", "then", "response", "json", "data", "IllustrationContent", "componentWillLoad", "render", "h", "Host", "key", "role", "class", "draggable", "src", "alt", "skeletonCss", "Skeleton", "shape", "height", "width", "style", "display", "position", "overflow", "borderRadius", "xxs", "skeleton"], "sources": ["src/components/illustration/illustration.scss?tag=bds-illustration&encapsulation=shadow", "src/components/illustration/illustration.tsx", "src/components/skeleton/skeleton.scss?tag=bds-skeleton&encapsulation=shadow", "src/components/skeleton/skeleton.tsx"], "sourcesContent": [":host {\n  .illustration {\n    display: flex;\n    height: 100%;\n    width: auto;\n  }\n  \n}\n\n:host(.bds-illustration) {\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n", "import { Component, h, Host, Prop, State } from '@stencil/core';\nimport { IllustrationType } from './illustration-interface';\nimport packageJson from '../../../package.json';\n\n@Component({\n  tag: 'bds-illustration',\n  assetsDirs: ['svg'],\n  styleUrl: 'illustration.scss',\n  shadow: true,\n})\nexport class BdsIllustration {\n  @State() private IllustrationContent?: string;\n\n  /**\n   * Specifies the type to use. Can be: 'default'.\n   */\n  @Prop() type: IllustrationType = 'default';\n  /**\n   * Specifies the name of illustration. Verify the names on illustration tokens.\n   */\n  @Prop() name: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setIllustrationContent();\n  }\n\n  /**Function to map the svg and call the \"formatSvg\" function */\n  setIllustrationContent = () => {\n    const tokensVersion = packageJson.dependencies['blip-tokens'].replace('^', '');\n    const apiUrl = `https://cdn.jsdelivr.net/npm/blip-tokens@${tokensVersion}/build/json/illustrations/${this.type}/${this.name}.json`;\n    fetch(apiUrl).then((response) =>\n      response.json().then((data) => {\n        this.IllustrationContent = data[`asset-${this.type}-${this.name}-svg`];\n      }),\n    );\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-illustration': true,\n        }}\n      >\n        {this.IllustrationContent ? (\n          <img\n            draggable={false}\n            src={`data:image/svg+xml;base64,${this.IllustrationContent}`}\n            alt={this.alt}\n            data-test={this.dataTest}\n          />\n        ) : (\n          <div class=\"default\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n.skeleton {\n  min-width: 8px;\n  min-height: 8px;\n  background-color: $color-content-default;\n  opacity: 0.16;\n  overflow: hidden;\n\n  &_shape {\n    &--circle {\n      border-radius: 50%;\n    }\n    &--square {\n      border-radius: 8px;\n    }\n  }\n}\n\n.animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    rgba(246, 246, 246, 0) 0%,\n    rgba(246, 246, 246, 0.56) 50%,\n    rgba(246, 246, 246, 0) 100%\n  );\n  mix-blend-mode: overlay;\n\n  animation: 2.5s ease-out infinite shine;\n}\n\n@keyframes shine {\n  0% {\n    transform: translateX(-100%);\n  }\n\n  20% {\n    transform: translateX(100%);\n  }\n\n  100% {\n    transform: translateX(100%);\n  }\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\n\nexport type Shape = 'circle' | 'square';\n\n@Component({\n  tag: 'bds-skeleton',\n  styleUrl: 'skeleton.scss',\n  shadow: true,\n})\nexport class Skeleton {\n  @Prop() shape?: Shape = 'square';\n  @Prop() height?: string = '50px';\n  @Prop() width?: string = '100%';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host\n        style={{\n          display: 'flex',\n          position: 'relative',\n          overflow: 'hidden',\n          width: this.width,\n          height: this.height,\n          borderRadius: this.shape === 'circle' ? '50%' : '8px',\n        }}\n      >\n        <bds-grid xxs=\"12\" class={{ skeleton: true, [`skeleton_shape--${this.shape}`]: true }}></bds-grid>\n        <div\n          style={{\n            display: 'flex',\n            width: '100%',\n            height: '100%',\n            position: 'absolute',\n            borderRadius: this.shape === 'circle' ? '50%' : '8px',\n            overflow: 'hidden',\n          }}\n          data-test={this.dataTest}\n        >\n          <div class=\"animation\"></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "2GAAA,MAAMA,EAAkB,mI,MCUXC,EAAe,MAN5B,WAAAC,CAAAC,G,UAYUC,KAAIC,KAAqB,UAczBD,KAAQE,SAAY,KAO5BF,KAAsBG,uBAAG,KACvB,MAAMC,EAAgBC,EAAYC,aAAa,eAAeC,QAAQ,IAAK,IAC3E,MAAMC,EAAS,4CAA4CJ,8BAA0CJ,KAAKC,QAAQD,KAAKS,YACvHC,MAAMF,GAAQG,MAAMC,GAClBA,EAASC,OAAOF,MAAMG,IACpBd,KAAKe,oBAAsBD,EAAK,SAASd,KAAKC,QAAQD,KAAKS,WAAW,KAEzE,CAwBJ,CApCC,iBAAAO,GACEhB,KAAKG,wB,CAcP,MAAAc,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,KAAK,MACLC,MAAO,CACL,mBAAoB,OAGrBtB,KAAKe,oBACJG,EAAA,OACEK,UAAW,MACXC,IAAK,6BAA6BxB,KAAKe,sBACvCU,IAAKzB,KAAKyB,IAAG,YACFzB,KAAKE,WAGlBgB,EAAA,OAAKI,MAAM,UAAS,YAAYtB,KAAKE,W,mDC/D/C,MAAMwB,EAAc,+kC,MCSPC,EAAQ,MALrB,WAAA7B,CAAAC,G,UAMUC,KAAK4B,MAAW,SAChB5B,KAAM6B,OAAY,OAClB7B,KAAK8B,MAAY,OAKjB9B,KAAQE,SAAY,IA+B7B,CA7BC,MAAAe,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHW,MAAO,CACLC,QAAS,OACTC,SAAU,WACVC,SAAU,SACVJ,MAAO9B,KAAK8B,MACZD,OAAQ7B,KAAK6B,OACbM,aAAcnC,KAAK4B,QAAU,SAAW,MAAQ,QAGlDV,EAAU,YAAAE,IAAA,2CAAAgB,IAAI,KAAKd,MAAO,CAAEe,SAAU,KAAM,CAAC,mBAAmBrC,KAAK4B,SAAU,QAC/EV,EAAA,OAAAE,IAAA,2CACEW,MAAO,CACLC,QAAS,OACTF,MAAO,OACPD,OAAQ,OACRI,SAAU,WACVE,aAAcnC,KAAK4B,QAAU,SAAW,MAAQ,MAChDM,SAAU,UAED,YAAAlC,KAAKE,UAEhBgB,EAAA,OAAAE,IAAA,2CAAKE,MAAM,e", "ignoreList": []}