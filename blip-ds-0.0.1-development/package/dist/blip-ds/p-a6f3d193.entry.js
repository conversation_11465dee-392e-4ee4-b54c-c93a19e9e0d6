import{r as t,h as o,H as s}from"./p-C3J6Z5OX.js";const e=".sc-bds-toast-container-h{position:fixed;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;z-index:110000;width:456px}.bottom-right.sc-bds-toast-container-h{bottom:48px;right:48px}.bottom-left.sc-bds-toast-container-h{bottom:48px;left:48px}.top-right.sc-bds-toast-container-h{top:24px;right:24px}.top-left.sc-bds-toast-container-h{top:48px;left:48px}@media (max-width: 780px){.sc-bds-toast-container-h{right:0px;left:0px;width:100%}.top-left.sc-bds-toast-container-h,.top-right.sc-bds-toast-container-h{top:20px}.bottom-left.sc-bds-toast-container-h,.bottom-right.sc-bds-toast-container-h{bottom:20px}}";const a=class{constructor(o){t(this,o)}render(){return o(s,{key:"e6db8f555f65022023eb3c740137d3b37448091a"},o("slot",{key:"a939272a4b760ce9f5810be1fde85567d56e2caf"}))}};a.style=e;export{a as bds_toast_container};
//# sourceMappingURL=p-a6f3d193.entry.js.map