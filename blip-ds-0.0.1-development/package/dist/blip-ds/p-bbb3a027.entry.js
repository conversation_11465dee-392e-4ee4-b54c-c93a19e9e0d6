import{r as t,c as s,h as i,H as e,a as o}from"./p-C3J6Z5OX.js";const h=":host{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}";const n=class{constructor(i){t(this,i);this.buttonSelected=s(this,"buttonSelected");this.activeIndexes=new Set;this.size="medium";this.direction="row";this.color="primary";this.multiple=false}componentDidLoad(){this.buttons=this.el.getElementsByTagName("bds-button");this.setupButtons()}componentDidUpdate(){this.setupButtons()}handlePropChanges(){this.setupButtons()}setupButtons(){for(let t=0;t<this.buttons.length;t++){const s=this.buttons[t];s.setAttribute("data-index",t.toString());s.addEventListener("click",(()=>this.selectButton(t)));s.setVariant("outline");this.updateButtonPosition(t);this.updateButtonDirection(t);this.updateButtonSize(t);this.updateButtonColor(t)}}async activateButton(t){if(t>=0&&t<this.buttons.length){this.selectButton(t)}}selectButton(t){if(this.multiple){if(this.activeIndexes.has(t)){this.activeIndexes.delete(t)}else{this.activeIndexes.add(t)}}else{if(this.activeIndexes.has(t)){this.activeIndexes.clear()}else{this.activeIndexes.clear();this.activeIndexes.add(t)}}this.updateButtonStates(t)}updateButtonStates(t){for(let s=0;s<this.buttons.length;s++){const i=this.buttons[s];if(this.activeIndexes.has(s)){i.isActive(true);i.setVariant("solid");i.classList.add("active")}else{i.isActive(false);i.setVariant("outline");i.classList.remove("active")}if(s===t){this.buttonSelected.emit(i.id)}}}updateButtonPosition(t){const s=this.buttons[t];if(t===0){s.setPosition("first")}else if(t===this.buttons.length-1){s.setPosition("last")}else{s.setPosition("middle")}}updateButtonDirection(t){const s=this.buttons[t];this.direction==="row"?s.setDirection("row"):s.setDirection("column")}updateButtonSize(t){const s=this.buttons[t];this.size==="medium"?s.setSize("medium"):s.setSize("large")}updateButtonColor(t){const s=this.buttons[t];s.setColor(this.color)}render(){return i(e,{key:"dc12bbfd11ecdfc7a39bb3474bbef396d8182d3f",class:"button_group"},i("bds-grid",{key:"b2a0f033711ac0dd8c0bf6c773fba095d8d91be8",direction:this.direction},i("slot",{key:"877672a01e95c7fb6dfbeecafabd7ce93ebc0dc0"})))}get el(){return o(this)}static get watchers(){return{size:["handlePropChanges"],direction:["handlePropChanges"],color:["handlePropChanges"],multiple:["handlePropChanges"]}}};n.style=h;export{n as bds_button_group};
//# sourceMappingURL=p-bbb3a027.entry.js.map