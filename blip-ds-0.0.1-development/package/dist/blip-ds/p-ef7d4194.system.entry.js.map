{"version": 3, "names": ["accordionCss", "AccordionGroup", "exports", "class_1", "hostRef", "this", "accordionsElement", "collapse", "divisor", "prototype", "closeAll", "actNumber", "bdsAccordionCloseAll", "emit", "i", "length", "close", "openAll", "bdsAccordionOpenAll", "open", "divisorChanged", "newValue", "componentWillRender", "element", "getElementsByTagName", "reciveNumber", "render", "h", "key", "class"], "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion-group&encapsulation=shadow", "src/components/accordion/accordion-group.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, h, Element, Prop, EventEmitter, Event, Method, Watch } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-accordion-group',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionGroup {\n  private accordionsElement?: HTMLCollectionOf<HTMLBdsAccordionElement> = null;\n\n  @Element() private element: HTMLElement;\n  @Prop() collapse?: collapses = 'single';\n  @Prop() divisor?: boolean = true;\n\n  @Event() bdsAccordionCloseAll?: EventEmitter;\n  @Event() bdsAccordionOpenAll?: EventEmitter;\n\n  @Method()\n  async closeAll(actNumber?) {\n    this.bdsAccordionCloseAll.emit();\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.accordionsElement[i].close();\n      } else {\n        this.accordionsElement[i].close();\n      }\n    }\n  }\n\n  @Method()\n  async openAll(actNumber?) {\n    this.bdsAccordionOpenAll.emit();\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      if (this.collapse != 'multiple') {\n        if (actNumber != i) this.accordionsElement[i].open();\n      } else {\n        this.accordionsElement[i].open();\n      }\n    }\n  }\n\n  @Watch('divisor')\n  divisorChanged(newValue: boolean): void {\n    if (this.accordionsElement) {\n      for (let i = 0; i < this.accordionsElement.length; i++) {\n        this.accordionsElement[i].divisor = newValue;  // Atualiza divisor nos filhos\n      }\n    }\n  }\n\n  componentWillRender() {\n    this.accordionsElement = this.element.getElementsByTagName(\n      'bds-accordion',\n    ) as HTMLCollectionOf<HTMLBdsAccordionElement>;\n    for (let i = 0; i < this.accordionsElement.length; i++) {\n      this.accordionsElement[i].reciveNumber(i);\n      this.accordionsElement[i].divisor = this.divisor;\n    }\n  }\n\n  render() {\n    return (\n      <div class=\"accordion_group\">\n        <slot></slot>\n      </div>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAe,ylE,ICSRC,EAAcC,EAAA,iCAL3B,SAAAC,EAAAC,G,0HAMUC,KAAiBC,kBAA+C,KAGhED,KAAQE,SAAe,SACvBF,KAAOG,QAAa,IAuD7B,CAjDOL,EAAAM,UAAAC,SAAN,SAAeC,G,2FACbN,KAAKO,qBAAqBC,OAC1B,IAASC,EAAI,EAAGA,EAAIT,KAAKC,kBAAkBS,OAAQD,IAAK,CACtD,GAAIT,KAAKE,UAAY,WAAY,CAC/B,GAAII,GAAaG,EAAGT,KAAKC,kBAAkBQ,GAAGE,O,KACzC,CACLX,KAAKC,kBAAkBQ,GAAGE,O,mBAM1Bb,EAAAM,UAAAQ,QAAN,SAAcN,G,2FACZN,KAAKa,oBAAoBL,OACzB,IAASC,EAAI,EAAGA,EAAIT,KAAKC,kBAAkBS,OAAQD,IAAK,CACtD,GAAIT,KAAKE,UAAY,WAAY,CAC/B,GAAII,GAAaG,EAAGT,KAAKC,kBAAkBQ,GAAGK,M,KACzC,CACLd,KAAKC,kBAAkBQ,GAAGK,M,mBAMhChB,EAAAM,UAAAW,eAAA,SAAeC,GACb,GAAIhB,KAAKC,kBAAmB,CAC1B,IAAK,IAAIQ,EAAI,EAAGA,EAAIT,KAAKC,kBAAkBS,OAAQD,IAAK,CACtDT,KAAKC,kBAAkBQ,GAAGN,QAAUa,C,IAK1ClB,EAAAM,UAAAa,oBAAA,WACEjB,KAAKC,kBAAoBD,KAAKkB,QAAQC,qBACpC,iBAEF,IAAK,IAAIV,EAAI,EAAGA,EAAIT,KAAKC,kBAAkBS,OAAQD,IAAK,CACtDT,KAAKC,kBAAkBQ,GAAGW,aAAaX,GACvCT,KAAKC,kBAAkBQ,GAAGN,QAAUH,KAAKG,O,GAI7CL,EAAAM,UAAAiB,OAAA,WACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,mBACTF,EAAa,QAAAC,IAAA,6C,wPAxDM,I", "ignoreList": []}