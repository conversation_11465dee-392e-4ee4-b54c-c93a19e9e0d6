{"version": 3, "names": ["expansionPanelCss", "ExpansionPanel", "exports", "class_1", "prototype", "render", "h", "Host", "key"], "sources": ["src/components/expansion-panel/expansion-panel.scss?tag=bds-expansion-panel&encapsulation=shadow", "src/components/expansion-panel/expansion-panel.tsx"], "sourcesContent": ["* {\n  -webkit-transition: all 0.5s;\n  -moz-transition: all 0.5s;\n  transition: all 0.5s;\n}\n\n:host {\n  display: block;\n}\n\n", "import { Component, Host, h, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel',\n  styleUrl: 'expansion-panel.scss',\n  shadow: true,\n})\nexport class ExpansionPanel implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAoB,kG,ICObC,EAAcC,EAAA,iC,wBACzBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAa,QAAAE,IAAA,6C,WAJM,I", "ignoreList": []}