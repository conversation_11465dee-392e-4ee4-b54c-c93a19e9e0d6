{"version": 3, "file": "p-KsAJij7V.system.js", "sources": ["src/utils/position-element.ts"], "sourcesContent": ["export interface Position {\n  top: number;\n  left: number;\n}\n\nexport type reference = 'top' | 'bottom' | 'left' | 'right';\n\nexport interface BreakPostion {\n  x: reference | string;\n  y: reference | string;\n}\n\nexport const getScrollParent = (node: HTMLElement) => {\n  if (node === null) {\n    return null;\n  }\n\n  if (node.classList.contains('element_scrolled') || node?.tagName === 'BODY') {\n    return node;\n  } else {\n    return getScrollParent(node.offsetParent as HTMLElement);\n  }\n};\n\nexport function getParentsUntil(element: HTMLElement, stopSelector: string): HTMLElement[] {\n  const parents: HTMLElement[] = [element];\n\n  while (element && !element.matches(stopSelector)) {\n    element = element.parentElement;\n    parents.push(element);\n  }\n\n  return parents;\n}\n\nexport function positionElement({\n  actionElement,\n  changedElement,\n  intoView,\n}: {\n  actionElement: HTMLElement;\n  changedElement: HTMLElement;\n  intoView: HTMLElement;\n}): Position {\n  const body = intoView ? intoView : document.body;\n  const parentElement: HTMLElement = body.offsetParent as HTMLElement;\n  const contentScrolled = !!body.classList.contains('element_scrolled');\n\n  const positionTop = contentScrolled\n    ? actionElement.offsetTop - body.scrollTop + parentElement.offsetTop\n    : actionElement.offsetTop - window.scrollY;\n\n  const positionLeft = contentScrolled ? actionElement.offsetLeft + parentElement.offsetLeft : actionElement.offsetLeft;\n\n  const changedpositionTop =\n    changedElement?.offsetHeight > window.innerHeight - positionTop\n      ? positionTop - changedElement?.offsetHeight - 16\n      : positionTop + actionElement?.offsetHeight + 16;\n  const changedpositionLeft =\n    changedElement?.offsetWidth > window.innerWidth - positionLeft\n      ? positionLeft + actionElement?.offsetWidth - changedElement?.offsetWidth\n      : positionLeft;\n\n  const limitedHeightScreen = window.innerHeight - changedElement?.offsetHeight;\n  const limitedWidthScreen = window.innerWidth - changedElement?.offsetWidth;\n\n  const result = {\n    top:\n      changedpositionTop < 8\n        ? 8\n        : changedpositionTop > limitedHeightScreen\n          ? limitedHeightScreen - 8\n          : changedpositionTop,\n    left:\n      changedpositionLeft < 0 ? 0 : changedpositionLeft > limitedWidthScreen ? limitedWidthScreen : changedpositionLeft,\n  };\n\n  return result;\n}\n\nexport function positionAbsoluteElement({\n  actionElement,\n  changedElement,\n  intoView,\n}: {\n  actionElement: HTMLElement;\n  changedElement: HTMLElement;\n  intoView: HTMLElement;\n}): BreakPostion {\n  const body = intoView ? intoView : document.body;\n  const numberHeignt = body.offsetHeight < changedElement.offsetHeight ? window.screen.height : body.offsetHeight;\n  const numberWidth = body.offsetWidth < changedElement.offsetWidth ? window.screen.width : body.offsetWidth;\n  const heightTop = numberHeignt - actionElement.offsetTop;\n  const widthLeft = numberWidth - actionElement.offsetLeft;\n\n  const result = {\n    y: heightTop < changedElement.offsetHeight + actionElement.offsetHeight ? 'top' : 'bottom',\n    x: widthLeft < changedElement.offsetWidth ? 'right' : 'left',\n  };\n\n  return result;\n}\n\nexport const getItems = (itenslenght: number) => {\n  const items = [];\n  let item = 1;\n\n  while (item <= itenslenght) {\n    const newItem = {\n      id: item,\n      label: `Frame - ${item}`,\n    };\n    items.push(newItem);\n    item++;\n  }\n  return items;\n};\n\nexport const getHighestItem = (items) => {\n  var maxoffsetHeight = Math.max.apply(\n    null,\n    items.map((a) => a.offsetHeight),\n  );\n  var output = items.filter((a) => a.offsetHeight == maxoffsetHeight).map((a) => a.offsetHeight);\n  return output;\n};\n\nexport const gapChanged = (gap: string) => {\n  let spaceGap;\n  switch (gap) {\n    case 'none':\n      spaceGap = 0;\n      break;\n    case 'half':\n      spaceGap = 4;\n      break;\n    case '1':\n      spaceGap = 8;\n      break;\n    case '2':\n      spaceGap = 16;\n      break;\n    case '3':\n      spaceGap = 24;\n      break;\n    case '4':\n      spaceGap = 32;\n      break;\n    case '5':\n      spaceGap = 40;\n      break;\n    case '6':\n      spaceGap = 48;\n      break;\n    case '7':\n      spaceGap = 56;\n      break;\n    case '8':\n      spaceGap = 64;\n      break;\n    case '9':\n      spaceGap = 72;\n      break;\n    case '10':\n      spaceGap = 80;\n      break;\n    case '11':\n      spaceGap = 88;\n      break;\n    case '12':\n      spaceGap = 96;\n      break;\n    default:\n      spaceGap = 0;\n  }\n  return spaceGap;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAYa,kBAAA,eAAe,gBAAG,CAAC,IAAiB,KAAI;YACnD,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,QAAA,OAAO,IAAI;;gBAGb,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,OAAO,MAAK,MAAM,EAAE;YAC3E,QAAA,OAAO,IAAI;;qBACN;YACL,QAAA,OAAO,eAAe,CAAC,IAAI,CAAC,YAA2B,CAAC;;YAE5D;YAEgB,SAAA,eAAe,CAAC,OAAoB,EAAE,YAAoB,EAAA;YACxE,IAAA,MAAM,OAAO,GAAkB,CAAC,OAAO,CAAC;gBAExC,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAChD,QAAA,OAAO,GAAG,OAAO,CAAC,aAAa;YAC/B,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;YAGvB,IAAA,OAAO,OAAO;YAChB;YAEM,SAAU,eAAe,CAAC,EAC9B,aAAa,EACb,cAAc,EACd,QAAQ,GAKT,EAAA;YACC,IAAA,MAAM,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI;YAChD,IAAA,MAAM,aAAa,GAAgB,IAAI,CAAC,YAA2B;YACnE,IAAA,MAAM,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAErE,MAAM,WAAW,GAAG;sBAChB,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;sBACzD,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO;YAE5C,IAAA,MAAM,YAAY,GAAG,eAAe,GAAG,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU;YAErH,IAAA,MAAM,kBAAkB,GACtB,CAAA,cAAc,aAAd,cAAc,KAAA,MAAA,GAAA,MAAA,GAAd,cAAc,CAAE,YAAY,IAAG,MAAM,CAAC,WAAW,GAAG;YAClD,UAAE,WAAW,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,YAAY,CAAA,GAAG;YAC/C,UAAE,WAAW,IAAG,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,MAAA,GAAA,MAAA,GAAA,aAAa,CAAE,YAAY,CAAA,GAAG,EAAE;YACpD,IAAA,MAAM,mBAAmB,GACvB,CAAA,cAAc,aAAd,cAAc,KAAA,MAAA,GAAA,MAAA,GAAd,cAAc,CAAE,WAAW,IAAG,MAAM,CAAC,UAAU,GAAG;YAChD,UAAE,YAAY,IAAG,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,WAAW,CAAA,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,uBAAd,cAAc,CAAE,WAAW;sBACvE,YAAY;YAElB,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,YAAY,CAAA;YAC7E,IAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,WAAW,CAAA;YAE1E,IAAA,MAAM,MAAM,GAAG;oBACb,GAAG,EACD,kBAAkB,GAAG;YACnB,cAAE;0BACA,kBAAkB,GAAG;8BACnB,mBAAmB,GAAG;YACxB,kBAAE,kBAAkB;oBAC1B,IAAI,EACF,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,mBAAmB;iBACpH;YAED,IAAA,OAAO,MAAM;YACf;YAEM,SAAU,uBAAuB,CAAC,EACtC,aAAa,EACb,cAAc,EACd,QAAQ,GAKT,EAAA;YACC,IAAA,MAAM,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI;gBAChD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;gBAC/G,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;YAC1G,IAAA,MAAM,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC,SAAS;YACxD,IAAA,MAAM,SAAS,GAAG,WAAW,GAAG,aAAa,CAAC,UAAU;YAExD,IAAA,MAAM,MAAM,GAAG;YACb,QAAA,CAAC,EAAE,SAAS,GAAG,cAAc,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,GAAG,KAAK,GAAG,QAAQ;YAC1F,QAAA,CAAC,EAAE,SAAS,GAAG,cAAc,CAAC,WAAW,GAAG,OAAO,GAAG,MAAM;iBAC7D;YAED,IAAA,OAAO,MAAM;YACf;AAEa,kBAAA,QAAQ,gBAAG,CAAC,WAAmB,KAAI;gBAC9C,MAAM,KAAK,GAAG,EAAE;gBAChB,IAAI,IAAI,GAAG,CAAC;YAEZ,IAAA,OAAO,IAAI,IAAI,WAAW,EAAE;YAC1B,QAAA,MAAM,OAAO,GAAG;YACd,YAAA,EAAE,EAAE,IAAI;wBACR,KAAK,EAAE,CAAW,QAAA,EAAA,IAAI,CAAE,CAAA;qBACzB;YACD,QAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;YACnB,QAAA,IAAI,EAAE;;YAER,IAAA,OAAO,KAAK;YACd;AAEa,kBAAA,cAAc,gBAAG,CAAC,KAAK,KAAI;gBACtC,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAClC,IAAI,EACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CACjC;YACD,IAAA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,IAAI,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC;YAC9F,IAAA,OAAO,MAAM;YACf;AAEa,kBAAA,UAAU,gBAAG,CAAC,GAAW,KAAI;YACxC,IAAA,IAAI,QAAQ;gBACZ,QAAQ,GAAG;YACT,QAAA,KAAK,MAAM;wBACT,QAAQ,GAAG,CAAC;wBACZ;YACF,QAAA,KAAK,MAAM;wBACT,QAAQ,GAAG,CAAC;wBACZ;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,CAAC;wBACZ;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,GAAG;wBACN,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,IAAI;wBACP,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,IAAI;wBACP,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA,KAAK,IAAI;wBACP,QAAQ,GAAG,EAAE;wBACb;YACF,QAAA;wBACE,QAAQ,GAAG,CAAC;;YAEhB,IAAA,OAAO,QAAQ;YACjB;;;;;;;;"}