var __awaiter=this&&this.__awaiter||function(t,e,i,n){function r(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,s){function a(t){try{u(n.next(t))}catch(t){s(t)}}function c(t){try{u(n["throw"](t))}catch(t){s(t)}}function u(t){t.done?i(t.value):r(t.value).then(a,c)}u((n=n.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var i={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,r,s,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol==="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(t){return function(e){return u([t,e])}}function u(c){if(n)throw new TypeError("Generator is already executing.");while(a&&(a=0,c[0]&&(i=0)),i)try{if(n=1,r&&(s=c[0]&2?r["return"]:c[0]?r["throw"]||((s=r["return"])&&s.call(r),0):r.next)&&!(s=s.call(r,c[1])).done)return s;if(r=0,s)c=[c[0]&2,s.value];switch(c[0]){case 0:case 1:s=c;break;case 4:i.label++;return{value:c[1],done:false};case 5:i.label++;r=c[1];c=[0];continue;case 7:c=i.ops.pop();i.trys.pop();continue;default:if(!(s=i.trys,s=s.length>0&&s[s.length-1])&&(c[0]===6||c[0]===2)){i=0;continue}if(c[0]===3&&(!s||c[1]>s[0]&&c[1]<s[3])){i.label=c[1];break}if(c[0]===6&&i.label<s[1]){i.label=s[1];s=c;break}if(s&&i.label<s[2]){i.label=s[2];i.ops.push(c);break}if(s[2])i.ops.pop();i.trys.pop();continue}c=e.call(t,i)}catch(t){c=[6,t];r=0}finally{n=s=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,n,r;return{setters:[function(t){e=t.r;i=t.h;n=t.H;r=t.a}],execute:function(){var s=":host{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host .img-feedback{height:76%}:host(.empty_img){background-color:var(--color-surface-3, rgb(227, 227, 227))}";var a=t("bds_image",function(){function t(t){e(this,t);this.imageHasLoading=false;this.objectFit="cover";this.dataTest=null;this.imageLoaded=false;this.loadError=false}t.prototype.componentDidLoad=function(){var t;this.element.style.width=this.width?this.width:"auto";this.element.style.height=((t=this.height)===null||t===void 0?void 0:t.length)>0?this.height:"auto"};t.prototype.loadImage=function(){return __awaiter(this,void 0,void 0,(function(){var t,e,i,n;return __generator(this,(function(r){switch(r.label){case 0:if(!this.src)return[3,7];this.imageHasLoading=true;r.label=1;case 1:r.trys.push([1,6,,7]);return[4,fetch(this.src)];case 2:t=r.sent();if(!t.ok)return[3,4];return[4,t.blob()];case 3:e=r.sent();i=URL.createObjectURL(e);this.currentSrc=i;this.imageLoaded=true;this.imageHasLoading=false;return[3,5];case 4:this.loadError=true;r.label=5;case 5:return[3,7];case 6:n=r.sent();this.imageHasLoading=false;this.loadError=true;return[3,7];case 7:return[2]}}))}))};t.prototype.render=function(){if(!this.imageLoaded&&!this.loadError){this.loadImage()}return i(n,{key:"b7798d3abcb3fe0da3938b890b1abc6479aeccb0",class:{empty_img:!this.imageLoaded}},this.imageLoaded?i("img",{src:this.currentSrc,alt:this.alt,style:{objectFit:this.objectFit,width:"100%",height:"100%",filter:"brightness(".concat(this.brightness,")")},"data-test":this.dataTest,draggable:false}):this.imageHasLoading?i("bds-skeleton",{shape:"square",width:"100%",height:"100%"}):i("bds-illustration",{class:"img-feedback",type:"empty-states",name:this.loadError?"broken-image":"image-not-found",alt:this.alt,"data-test":this.dataTest}))};Object.defineProperty(t.prototype,"element",{get:function(){return r(this)},enumerable:false,configurable:true});return t}());a.style=s}}}));
//# sourceMappingURL=p-8ad476d0.system.entry.js.map