import{r as o,h as r}from"./p-C3J6Z5OX.js";const e='.accordion_header{display:-ms-flexbox;display:flex;grid-auto-flow:column;gap:24px;-ms-flex-pack:start;justify-content:start;-ms-flex-align:center;align-items:center;padding:24px;padding-right:56px;position:relative;color:var(--color-content-default, rgb(40, 40, 40));cursor:pointer}.accordion_header::before{content:"";position:absolute;inset:0;z-index:0}.accordion_header slot{display:-ms-flexbox;display:flex;width:100%;-ms-flex-negative:99999;flex-shrink:99999}.accordion_header *{position:relative;z-index:1}.accordion_header:hover::before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.accordion_header .accButton{position:absolute;right:24px;top:calc(50% - 16px);border-radius:8px;contain:inherit;-webkit-transition:height 0.5s, all 0.3s;-moz-transition:height 0.5s, all 0.3s;transition:height 0.5s, all 0.3s;z-index:1}.accordion_header .accButton__isopen{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion_header .accButton::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.accordion_header .accButton:focus-visible{outline:none}.accordion_header .accButton:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.accordion_header .accButton:hover{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_header .accButton:active{background-color:var(--color-surface-1, rgb(246, 246, 246))}.accordion_body{height:0;overflow:hidden;border-bottom:none;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.accordion_body::-webkit-scrollbar{width:16px;background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04));border-radius:10px}.accordion_body::-webkit-scrollbar-thumb{border-radius:10px;border:4px solid transparent;border-radius:10px;background-clip:content-box;background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body_isOpen{overflow:overlay}.accordion_body_divisor{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}.accordion_body .container{padding:8px 24px 48px;position:relative;color:var(--color-content-default, rgb(40, 40, 40))}';const t=class{constructor(r){o(this,r);this.container=null;this.isOpen=false;this.isOpenAftAnimation=false;this.numberElement=null;this.hasDivisor=true;this.dataTest=null;this.refContainer=o=>{this.container=o}}async toggle(){this.isOpen=!this.isOpen}async open(){this.isOpen=true}async close(){this.isOpen=false}async divisor(o){this.hasDivisor=o}isOpenChanged(){var o;this.heightContainer=this.isOpen?((o=this.container)===null||o===void 0?void 0:o.offsetHeight)||0:0;if(this.isOpen){setTimeout((()=>{this.isOpenAftAnimation=true}),500)}else{this.isOpenAftAnimation=false}}render(){return r("div",{key:"5976bbf52d123b3534d352a00dabe2df406dad25",class:{accordion_body:true,accordion_body_divisor:this.hasDivisor,accordion_body_isOpen:this.isOpenAftAnimation},style:{height:`${this.heightContainer}px`},"data-test":this.dataTest},r("div",{key:"c1038dc51ecd25fbfd5a18b11c52fb901b504f56",class:"container",ref:o=>this.refContainer(o)},r("slot",{key:"ba113dcabd2c5ed50dd672284a61702b1741f877"})))}static get watchers(){return{isOpen:["isOpenChanged"]}}};t.style=e;export{t as bds_accordion_body};
//# sourceMappingURL=p-9c5aff0c.entry.js.map