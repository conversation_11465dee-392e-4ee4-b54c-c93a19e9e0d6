{"version": 3, "names": ["CounterTextState", "counterTextCss", "CounterText", "exports", "class_1", "hostRef", "this", "active", "warning", "max", "min", "delete", "prototype", "getState", "actualLength", "getActualLength", "Warning", "Delete", "<PERSON><PERSON><PERSON>", "length", "render", "state", "h", "key", "class", "_a", "concat", "variant"], "sources": ["src/components/counter-text/counter-text-interface.ts", "src/components/counter-text/counter-text.scss?tag=bds-counter-text", "src/components/counter-text/counter-text.tsx"], "sourcesContent": ["export enum CounterTextState {\n  Default = 'default',\n  Warning = 'warning',\n  Delete = 'delete',\n}\n\nexport type CounterTextRule = {\n  max: number;\n  min: number;\n};\n", "@use '../../globals/helpers' as *;\n\n.counter-text {\n  background: $color-surface-2;\n  color: $color-content-disable;\n  box-sizing: content-box;\n  width: fit-content;\n  border-radius: 11px;\n  padding: 0 8px;\n\n  @include no-select;\n\n  &--active {\n    background: $color-system;\n    color: $color-content-din;\n  }\n\n  &--warning {\n    background: $color-warning;\n    color: $color-content-din;\n  }\n\n  &--delete {\n    background: $color-delete;\n    color: $color-content-bright;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\nimport { CounterTextRule, CounterTextState } from './counter-text-interface';\n\n@Component({\n  tag: 'bds-counter-text',\n  styleUrl: 'counter-text.scss',\n})\nexport class CounterText {\n  @Prop({ mutable: true }) length!: number;\n  @Prop() max?: number;\n  @Prop({ mutable: true }) active? = false;\n\n  @Prop({ mutable: true }) warning?: CounterTextRule = { max: 20, min: 2 };\n  @Prop({ mutable: true }) delete?: CounterTextRule = { max: 1, min: 0 };\n\n  getState(): string {\n    const actualLength = this.getActualLength();\n\n    if (actualLength >= this.warning.min && actualLength <= this.warning.max) {\n      return CounterTextState.Warning;\n    }\n\n    if (actualLength <= this.delete.max) {\n      return CounterTextState.Delete;\n    }\n\n    return CounterTextState.Default;\n  }\n\n  getActualLength(): number {\n    return this.max - this.length;\n  }\n\n  render(): HTMLElement {\n    const state = this.getState();\n    const actualLength = this.getActualLength();\n\n    return (\n      <div\n        class={{\n          'counter-text': true,\n          'counter-text--active': this.active,\n          [`counter-text--${state}`]: true,\n        }}\n      >\n        <bds-typo variant=\"fs-10\">{actualLength}</bds-typo>\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAYA,GAAZ,SAAYA,GACVA,EAAA,qBACAA,EAAA,qBACAA,EAAA,kBACD,EAJD,CAAYA,MAIX,KCJD,IAAMC,EAAiB,iwB,ICOVC,EAAWC,EAAA,8BAJxB,SAAAC,EAAAC,G,UAO2BC,KAAMC,OAAI,MAEVD,KAAOE,QAAqB,CAAEC,IAAK,GAAIC,IAAK,GAC5CJ,KAAMK,OAAqB,CAAEF,IAAK,EAAGC,IAAK,EAoCpE,CAlCCN,EAAAQ,UAAAC,SAAA,WACE,IAAMC,EAAeR,KAAKS,kBAE1B,GAAID,GAAgBR,KAAKE,QAAQE,KAAOI,GAAgBR,KAAKE,QAAQC,IAAK,CACxE,OAAOT,EAAiBgB,O,CAG1B,GAAIF,GAAgBR,KAAKK,OAAOF,IAAK,CACnC,OAAOT,EAAiBiB,M,CAG1B,OAAOjB,EAAiBkB,O,EAG1Bd,EAAAQ,UAAAG,gBAAA,WACE,OAAOT,KAAKG,IAAMH,KAAKa,M,EAGzBf,EAAAQ,UAAAQ,OAAA,W,MACE,IAAMC,EAAQf,KAAKO,WACnB,IAAMC,EAAeR,KAAKS,kBAE1B,OACEO,EACE,OAAAC,IAAA,2CAAAC,OAAKC,EAAA,CACH,eAAgB,KAChB,uBAAwBnB,KAAKC,QAC7BkB,EAAC,iBAAAC,OAAiBL,IAAU,K,IAG9BC,EAAU,YAAAC,IAAA,2CAAAI,QAAQ,SAASb,G,WAtCX,I", "ignoreList": []}