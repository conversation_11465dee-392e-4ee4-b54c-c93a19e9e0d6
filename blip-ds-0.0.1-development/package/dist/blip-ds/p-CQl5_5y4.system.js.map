{"version": 3, "file": "p-CQl5_5y4.system.js", "sources": ["src/components/expansion-panel/expansion-panel.scss?tag=bds-expansion-panel&encapsulation=shadow", "src/components/expansion-panel/expansion-panel.tsx"], "sourcesContent": ["* {\n  -webkit-transition: all 0.5s;\n  -moz-transition: all 0.5s;\n  transition: all 0.5s;\n}\n\n:host {\n  display: block;\n}\n\n", "import { Component, Host, h, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-expansion-panel',\n  styleUrl: 'expansion-panel.scss',\n  shadow: true,\n})\nexport class ExpansionPanel implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;MAAA,MAAM,iBAAiB,GAAG,iGAAiG;;YCO9G,cAAc,kCAAA,MAAA;;;;UACzB,MAAM,GAAA;cACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;;;;;"}