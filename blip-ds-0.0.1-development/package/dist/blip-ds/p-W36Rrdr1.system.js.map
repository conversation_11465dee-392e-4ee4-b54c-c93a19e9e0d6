{"version": 3, "file": "p-W36Rrdr1.system.js", "sources": ["src/components/selects/select.scss?tag=bds-select&encapsulation=shadow", "src/components/selects/select/select.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Watch, Element, Listen } from '@stencil/core';\nimport { Option, SelectChangeEventDetail, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\n@Component({\n  tag: 'bds-select',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class Select {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalOptions: Option[];\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | Option[];\n\n  /**\n   * the value of the select.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  @Prop({ mutable: true }) value?: any | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEventDetail>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsChange.emit({ value: this.value });\n\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n\n    this.text = this.getText(this.value);\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true, capture: true })\n  handleWindow(ev: Event) {\n    const path = ev.composedPath();\n    if (!path.find((element: HTMLElement) => element == this.el)) {\n      this.isOpen = false;\n    }\n  }\n\n  componentWillLoad() {\n    this.options && this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  componentWillRender() {\n    this.options && this.updateOptions();\n    this.getValueSelected();\n  }\n\n  componentDidLoad() {\n    this.getValueSelected();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  @Watch('options')\n  optionsChanged() {\n    this.updateOptions();\n  }\n\n  private getValueSelected() {\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n      option.addEventListener('optionSelected', this.handler);\n    }\n    this.text = this.getText(this.value);\n  }\n\n  private updateOptions() {\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        this.internalOptions = JSON.parse(this.options);\n      } else {\n        this.internalOptions = this.options;\n      }\n    }\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private refNativeInput = (el: any): void => {\n    this.nativeInput = el;\n  };\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.isOpen = true;\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getText = (value): string => {\n    const opt = this.childOptions.find((option) => option.value == value);\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.titleText ? internalOption.titleText : internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt?.titleText : (opt?.innerText ?? '');\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n\n    return (\n      <div class=\"select\">\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n            part=\"input-container\"\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                <input\n                  ref={this.refNativeInput}\n                  class={{ input__container__text: true }}\n                  onFocus={this.onFocus}\n                  onBlur={this.onBlur}\n                  value={this.text}\n                  disabled={this.disabled}\n                  placeholder={this.placeholder}\n                  readonly\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper.bind(this)}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n          role=\"application\"\n        >\n          {this.internalOptions ? (\n            this.internalOptions.map((option, idx) =>\n              option.icon || option.titleText ? (\n                <bds-select-option\n                  key={idx}\n                  value={option.value}\n                  title-text={option.titleText}\n                  slot-align={option.slotAlign}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                >\n                  {option.icon && (\n                    <bds-icon slot=\"input-left\" name={option.icon} size=\"medium\" color={option.iconColor}></bds-icon>\n                  )}\n                  {option.label}\n                </bds-select-option>\n              ) : (\n                <bds-select-option key={idx} value={option.value} bulkOption={option.bulkOption} status={option.status}>\n                  {option.label}\n                </bds-select-option>\n              ),\n            )\n          ) : (\n            <slot />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;MAAA,MAAM,SAAS,GAAG,snUAAsnU;;YCQ3nU,MAAM,yBAAA,MAAA;MALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;MAaW,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;MAE7B,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MAEf,QAAA,IAAI,CAAA,IAAA,GAAI,EAAE;MAEnB;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;MAC3C;;MAEG;MACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;MAE3B;;MAEG;MACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;MAkB/B;;MAEG;MACsB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;MACxC;;MAEG;MACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;MACjE;;MAEG;MACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;MAsB1C;;MAEG;MACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;MAEnB;;MAEG;MACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;MAE3C;;MAEG;MACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;MAEjC;;MAEG;MACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;MAEnC;;MAEG;MACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;MAClC;;MAEG;MACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;MACrD;;MAEG;MACqC,QAAA,IAAe,CAAA,eAAA,GAA+B,MAAM;MAE5F;;MAEG;MACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;;MAqHxB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAO,KAAU;MACzC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;MACvB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAI;MACxC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;MACvB,SAAC;MAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;MAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;MAC3B,SAAC;MAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;kBAClC,IAAI,CAAC,OAAO,EAAE;MACd,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;MAClB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;MACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;MAE5B,SAAC;MAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;MAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;MACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;MACvB,SAAC;MAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;MAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;MACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;MACxB,SAAC;MAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;MAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;MAE9B,SAAC;MAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAK,KAAY;;MAClC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;MACrE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;MACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;sBACxF,IAAI,cAAc,EAAE;MAClB,oBAAA,OAAO,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,KAAK;;;MAGrF,YAAA,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAE,SAAS,IAAG,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,IAAI,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,MAAA,GAAA,MAAA,GAAH,GAAG,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;MACjE,SAAC;MAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAkB,KAAU;kBAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;MACT,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;kBAClB,IAAI,CAAC,MAAM,EAAE;MACf,SAAC;MAyKF;MAhVW,IAAA,aAAa,CAAC,MAAe,EAAA;MACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;MACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;mBAC9D;MACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;MAErE,QAAA,IAAI,MAAM;MACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;uBACzC;sBACL,IAAI,CAAC,oBAAoB,EAAE;;;UAKjC,YAAY,GAAA;MACV,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;MAE1C,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;kBACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;cAG/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;MAItC,IAAA,YAAY,CAAC,EAAS,EAAA;MACpB,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,EAAE;MAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAoB,KAAK,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE;MAC5D,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;UAIvB,iBAAiB,GAAA;MACf,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;cACrC,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;;UAG1C,mBAAmB,GAAA;MACjB,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE;cACpC,IAAI,CAAC,gBAAgB,EAAE;;UAGzB,gBAAgB,GAAA;cACd,IAAI,CAAC,gBAAgB,EAAE;MACvB,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;MAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;mBACzC;kBACL,IAAI,CAAC,oBAAoB,EAAE;;;MAIvB,IAAA,mBAAmB,CAAC,KAAgC,EAAA;MAC1D,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;kBACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;UAIlC,oBAAoB,GAAA;cAC1B,MAAM,aAAa,GAAG,uBAAuB,CAAC;kBAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;kBACtB,cAAc,EAAE,IAAI,CAAC,WAAW;kBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;MACxB,SAAA,CAAC;MACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;MACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;kBAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;MAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;mBACnC;kBACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;MAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;UAK1C,cAAc,GAAA;cACZ,IAAI,CAAC,aAAa,EAAE;;UAGd,gBAAgB,GAAA;MACtB,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;kBACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;kBAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;cAEzD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;UAG9B,aAAa,GAAA;MACnB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;MAChB,YAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;sBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;uBAC1C;MACL,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO;;;;MAKzC,IAAA,IAAY,YAAY,GAAA;cACtB,OAAO,IAAI,CAAC;MACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;MACrE,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;;MAG/D,IAAA,IAAY,mBAAmB,GAAA;cAC7B,OAAO,IAAI,CAAC;oBACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ;oBACrG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC;;MA2DzF,IAAA,eAAe,CAAC,KAAK,EAAA;;MAC3B,QAAA,QAAQ,KAAK,CAAC,GAAG;MACf,YAAA,KAAK,OAAO;sBACV,IAAI,CAAC,MAAM,EAAE;sBACb;MACF,YAAA,KAAK,WAAW;MACd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;MAClB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;MAEpB,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;MAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,WAA0C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;0BACxF;;MAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,iBAAgD,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;sBAC7E;MACF,YAAA,KAAK,SAAS;MACZ,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;MAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,eAA8C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;0BAC5F;;MAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,gBAA+C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;sBAC5E;;;UAIE,UAAU,GAAA;MAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACP,CAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,WAAW,EAAE,IAAI;MACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MACnC,aAAA,EAAA,EAED,CAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;UAIG,WAAW,GAAA;MACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACR,CAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;MACL,gBAAA,uBAAuB,EAAE,IAAI;sBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;mBACrE,EAAA,EAED,CAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;UAIG,aAAa,GAAA;cACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;MACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;MAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;MAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;cAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;MAClB,cAAE;oBACA,IAAI,CAAC;MACL,kBAAE;wBACA,gBAAgB;cAExB,IAAI,OAAO,EAAE;MACX,YAAA,QACE,WAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvC,CAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/B,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACN,CAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;MAIV,QAAA,OAAO,SAAS;;UAGlB,MAAM,GAAA;cACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;cAElD,QACE,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,QAAQ,EAAA,EACjB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAiB,eAAA,EAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAC/E,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;MACL,gBAAA,KAAK,EAAE,IAAI;sBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;MAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;sBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;sBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;MACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;MAC5B,gBAAA,gBAAgB,EAAE,SAAS;mBAC5B,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClB,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnB,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC7C,CAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,KAAK,EAAE,IAAI,CAAC,IAAI,EAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,QAAQ,EACG,IAAA,EAAA,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CACnC,CACL,CACF,EACN,CAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvB,CAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAA,CAAY,CACjF,EACL,IAAI,CAAC,OAAO,IAAI,CAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACN,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;MACL,gBAAA,eAAe,EAAE,IAAI;sBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;MACrC,aAAA,EACD,IAAI,EAAC,aAAa,EAAA,EAEjB,IAAI,CAAC,eAAe,IACnB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,KACnC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,IAC7B,CAAA,CAAA,mBAAA,EAAA,EACE,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,MAAM,CAAC,KAAK,EAAA,YAAA,EACP,MAAM,CAAC,SAAS,EAChB,YAAA,EAAA,MAAM,CAAC,SAAS,EAC5B,UAAU,EAAE,MAAM,CAAC,UAAU,EAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAA,EAEpB,MAAM,CAAC,IAAI,KACV,CAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,YAAY,EAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC,QAAQ,EAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAA,CAAa,CAClG,EACA,MAAM,CAAC,KAAK,CACK,KAEpB,CAAmB,CAAA,mBAAA,EAAA,EAAA,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EACnG,EAAA,MAAM,CAAC,KAAK,CACK,CACrB,CACF,KAED,eAAQ,CACT,CACG,CACF;;;;;;;;;;;;;;;;;"}