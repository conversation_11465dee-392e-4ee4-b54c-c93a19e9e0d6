{"version": 3, "names": ["cardCss", "Card", "constructor", "hostRef", "this", "height", "width", "clickable", "bgColor", "selectable", "borderColor", "dataTest", "isHovered", "isPressed", "elevation", "componentDidLoad", "cardElement", "element", "shadowRoot", "querySelector", "addEventListener", "bdsClick", "emit", "document", "event", "key", "componentDidUpdate", "handleKeyDown", "render", "styleHost", "h", "Host", "style", "border", "class", "card", "card_hover", "card_hover_selectable", "card_hover_pressed", "tabindex", "onKeyDown", "bind", "xxs", "direction", "gap", "padding"], "sources": ["src/components/card/card.scss?tag=bds-card&encapsulation=shadow", "src/components/card/card.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n    display: flex;\n    position: relative;\n  \n    &:focus-visible {\n      outline: none;\n    }\n  }\n  \n  .card {\n    display: flex;\n    width: 100%;\n  }\n  \n  .card_hover:hover {\n    transform: scale(1.02);\n    transition: all .3s;\n    cursor: pointer;\n}\n\n.card_hover_selectable:hover {\n  box-shadow: 0px 0px 0px 2px rgba(30, 107, 241, 0.08);\n}\n\n.card_hover_selectable:active {\n  box-shadow: 0px 0px 0px 2px rgba(30, 107, 241, 0.24);\n}\n\n  .focus:focus-visible {\n    display: flex;\n    position: absolute;\n    border: 2px solid $color-focus;\n    border-radius: 4px;\n    width: 100%;\n    height: 100%;\n    top: -4px;\n    left: -4px;\n    padding-right: 4px;\n    padding-bottom: 4px;\n    outline: none;\n  }\n  ", "import { Component, ComponentInterface, Host, h, Prop, Event, Element, State, EventEmitter } from '@stencil/core';\nimport { PaperBackground, BorderColor } from '../paper/paper-interface';\n\nexport type elevationType = 'primary' | 'secondary' | 'static' | 'none';\n\n@Component({\n  tag: 'bds-card',\n  styleUrl: 'card.scss',\n  shadow: true,\n})\nexport class Card implements ComponentInterface {\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string = null;\n\n  /**\n   * Prop for set the width of the component.\n   */\n  @Prop() width?: string = 'fit-content';\n  /**\n   * If the prop is true, the component will be clickable.\n   */\n  @Prop() clickable?: boolean = false;\n  /**\n   * Prop for set the background color.\n   */\n  @Prop() bgColor?: PaperBackground = 'surface-1';\n  \n  /**\n   * Prop for set the background color.\n   */\n   @Prop() selectable?: boolean = false;\n  /**\n   * Prop for set the border color.\n   */\n  @Prop({ mutable: true }) borderColor?: BorderColor = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @State() isHovered = false;\n  @State() isPressed = false;\n  @State() elevation: elevationType = 'primary';\n\n  /**\n   * This event will be dispatch when click on the component.\n   */\n  @Event() bdsClick: EventEmitter;\n\n  @Element() element: HTMLElement;\n\n  private cardElement: HTMLElement;\n\n  componentDidLoad() {\n    this.cardElement = this.element.shadowRoot.querySelector('.card');\n\n    if (this.cardElement && (this.clickable === true || this.selectable === true)) {\n      this.cardElement.addEventListener('mouseenter', () => {\n        this.isHovered = true;\n      });\n\n      this.cardElement.addEventListener('mouseleave', () => {\n        this.isHovered = false;\n      });\n\n      this.cardElement.addEventListener('mousedown', () => {\n        this.isPressed = true;\n        this.bdsClick.emit();\n      });\n\n      document.addEventListener('mouseup', () => {\n        this.isPressed = false;\n      });\n\n      this.cardElement.addEventListener('keydown', (event: KeyboardEvent) => {\n        if (event.key === 'Enter') {\n          this.isPressed = true;\n          this.bdsClick.emit();\n        }\n      });\n\n      this.cardElement.addEventListener('keyup', (event: KeyboardEvent) => {\n        if (event.key === 'Enter') {\n          this.isPressed = false;\n        }\n      });\n    }\n  }\n\n  componentDidUpdate() {\n    if (this.isPressed) {\n      this.elevation = 'static';\n    } else if (this.isHovered) {\n      this.elevation = 'secondary';\n    }\n  }\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.isPressed = true;\n      this.bdsClick.emit(event);\n    }\n  }\n\n  render() {\n    const styleHost = {\n      width: this.width,\n    };\n\n    return (\n      <Host style={styleHost}>\n        <bds-paper\n          border={this.clickable ? false : true}\n          elevation={this.elevation}\n          class={{\n            card: true,\n            card_hover: this.clickable,\n            card_hover_selectable: this.isHovered && this.selectable ? true : false,\n            card_hover_pressed: this.isHovered && this.selectable ? true : false\n          }}\n          height={this.height}\n          width={this.width}\n          bgColor={this.bgColor}\n          data-test={this.dataTest}\n          border-color={this.borderColor ? this.borderColor : 'border-1'}\n        >\n          <div tabindex=\"0\" class=\"focus\" onKeyDown={this.handleKeyDown.bind(this)}></div>\n          <bds-grid xxs=\"12\" direction=\"column\" gap=\"2\" padding=\"2\">\n            <slot></slot>\n          </bds-grid>\n        </bds-paper>\n      </Host>\n    );\n  }\n}\n"], "mappings": "2DAAA,MAAMA,EAAU,4xB,MCUHC,EAAI,MALjB,WAAAC,CAAAC,G,2CASUC,KAAMC,OAAY,KAKlBD,KAAKE,MAAY,cAIjBF,KAASG,UAAa,MAItBH,KAAOI,QAAqB,YAK3BJ,KAAUK,WAAa,MAIPL,KAAWM,YAAiB,KAI7CN,KAAQO,SAAY,KAEnBP,KAASQ,UAAG,MACZR,KAASS,UAAG,MACZT,KAASU,UAAkB,SA4FrC,CAjFC,gBAAAC,GACEX,KAAKY,YAAcZ,KAAKa,QAAQC,WAAWC,cAAc,SAEzD,GAAIf,KAAKY,cAAgBZ,KAAKG,YAAc,MAAQH,KAAKK,aAAe,MAAO,CAC7EL,KAAKY,YAAYI,iBAAiB,cAAc,KAC9ChB,KAAKQ,UAAY,IAAI,IAGvBR,KAAKY,YAAYI,iBAAiB,cAAc,KAC9ChB,KAAKQ,UAAY,KAAK,IAGxBR,KAAKY,YAAYI,iBAAiB,aAAa,KAC7ChB,KAAKS,UAAY,KACjBT,KAAKiB,SAASC,MAAM,IAGtBC,SAASH,iBAAiB,WAAW,KACnChB,KAAKS,UAAY,KAAK,IAGxBT,KAAKY,YAAYI,iBAAiB,WAAYI,IAC5C,GAAIA,EAAMC,MAAQ,QAAS,CACzBrB,KAAKS,UAAY,KACjBT,KAAKiB,SAASC,M,KAIlBlB,KAAKY,YAAYI,iBAAiB,SAAUI,IAC1C,GAAIA,EAAMC,MAAQ,QAAS,CACzBrB,KAAKS,UAAY,K,MAMzB,kBAAAa,GACE,GAAItB,KAAKS,UAAW,CAClBT,KAAKU,UAAY,Q,MACZ,GAAIV,KAAKQ,UAAW,CACzBR,KAAKU,UAAY,W,EAIb,aAAAa,CAAcH,GACpB,GAAIA,EAAMC,KAAO,QAAS,CACxBrB,KAAKS,UAAY,KACjBT,KAAKiB,SAASC,KAAKE,E,EAIvB,MAAAI,GACE,MAAMC,EAAY,CAChBvB,MAAOF,KAAKE,OAGd,OACEwB,EAACC,EAAK,CAAAN,IAAA,2CAAAO,MAAOH,GACXC,EACE,aAAAL,IAAA,2CAAAQ,OAAQ7B,KAAKG,UAAY,MAAQ,KACjCO,UAAWV,KAAKU,UAChBoB,MAAO,CACLC,KAAM,KACNC,WAAYhC,KAAKG,UACjB8B,sBAAuBjC,KAAKQ,WAAaR,KAAKK,WAAa,KAAO,MAClE6B,mBAAoBlC,KAAKQ,WAAaR,KAAKK,WAAa,KAAO,OAEjEJ,OAAQD,KAAKC,OACbC,MAAOF,KAAKE,MACZE,QAASJ,KAAKI,QACH,YAAAJ,KAAKO,SACF,eAAAP,KAAKM,YAAcN,KAAKM,YAAc,YAEpDoB,EAAA,OAAAL,IAAA,2CAAKc,SAAS,IAAIL,MAAM,QAAQM,UAAWpC,KAAKuB,cAAcc,KAAKrC,QACnE0B,EAAA,YAAAL,IAAA,2CAAUiB,IAAI,KAAKC,UAAU,SAASC,IAAI,IAAIC,QAAQ,KACpDf,EAAA,QAAAL,IAAA,+C", "ignoreList": []}