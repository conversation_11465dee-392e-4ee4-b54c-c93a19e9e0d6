var __awaiter=this&&this.__awaiter||function(t,e,i,r){function n(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,a){function s(t){try{c(r.next(t))}catch(t){a(t)}}function o(t){try{c(r["throw"](t))}catch(t){a(t)}}function c(t){t.done?i(t.value):n(t.value).then(s,o)}c((r=r.apply(t,e||[])).next())}))};var __generator=this&&this.__generator||function(t,e){var i={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,n,a,s;return s={next:o(0),throw:o(1),return:o(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function o(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s&&(s=0,o[0]&&(i=0)),i)try{if(r=1,n&&(a=o[0]&2?n["return"]:o[0]?n["throw"]||((a=n["return"])&&a.call(n),0):n.next)&&!(a=a.call(n,o[1])).done)return a;if(n=0,a)o=[o[0]&2,a.value];switch(o[0]){case 0:case 1:a=o;break;case 4:i.label++;return{value:o[1],done:false};case 5:i.label++;n=o[1];o=[0];continue;case 7:o=i.ops.pop();i.trys.pop();continue;default:if(!(a=i.trys,a=a.length>0&&a[a.length-1])&&(o[0]===6||o[0]===2)){i=0;continue}if(o[0]===3&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(o[0]===6&&i.label<a[1]){i.label=a[1];a=o;break}if(a&&i.label<a[2]){i.label=a[2];i.ops.push(o);break}if(a[2])i.ops.pop();i.trys.pop();continue}o=e.call(t,i)}catch(t){o=[6,t];n=0}finally{r=a=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(t,e,i){if(i||arguments.length===2)for(var r=0,n=e.length,a;r<n;r++){if(a||!(r in e)){if(!a)a=Array.prototype.slice.call(e,0,r);a[r]=e[r]}}return t.concat(a||Array.prototype.slice.call(e))};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var e,i,r,n,a;return{setters:[function(t){e=t.r;i=t.c;r=t.h;n=t.H;a=t.a}],execute:function(){var s=':host{display:block;width:100%}:host .table{display:grid;font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;color:var(--color-content-default, rgb(40, 40, 40));width:100%;border:1px solid var(--color-border-3, rgba(0, 0, 0, 0.06));border-radius:8px;overflow-x:auto;background-color:var(--color-surface-1, rgb(246, 246, 246))}:host .table .thead{border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));padding:0 16px}:host .table .thead .header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;text-align:left;-ms-flex-align:center;align-items:center;height:64px;gap:16px}:host .table .thead .header .header-title{height:64px;width:100%;display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:start;justify-content:flex-start;gap:8px}:host .table .thead .header .header-title .title-click{cursor:pointer}:host .table .body-row{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;-ms-flex-align:center;align-items:center;height:64px;padding:0 16px;gap:16px;border-bottom:1px solid var(--color-border-2, rgba(0, 0, 0, 0.16))}:host .table .body-row .body-item{height:48px;width:100%;gap:8px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:start;justify-content:flex-start}:host .table .body-row:last-child{border-bottom:none}';var o=t("bds_data_table",function(){function t(t){e(this,t);this.bdsTableClick=i(this,"bdsTableClick");this.bdsTableDelete=i(this,"bdsTableDelete");this.bdsTableChange=i(this,"bdsTableChange");this.newTable=[];this.headerData=[];this.tableData=[];this.avatar=false;this.chips=false;this.sorting=false}t.prototype.componentWillLoad=function(){this.getDataFromProprety()};t.prototype.getDataFromProprety=function(){this.headerData=JSON.parse(this.column);this.tableData=JSON.parse(this.options)};t.prototype.renderArrow=function(t){if(t){return r("bds-icon",{name:"arrow-up",size:"small"})}else{return null}};t.prototype.deleteItem=function(t){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(i){e=this.tableData.filter((function(e,i){return i===t&&e}));this.bdsTableDelete.emit(e[0]);this.tableData.splice(t,1);this.tableData=__spreadArray([],this.tableData,true);this.bdsTableChange.emit(this.tableData);return[2]}))}))};t.prototype.clickButton=function(t,e,i){this.bdsTableClick.emit({item:t,index:e,nameButton:i})};t.prototype.orderColumn=function(t){this.headerActive=t;this.sortAscending=this.sortAscending?false:true;if(this.sortAscending===false){this.tableData.sort((function(e,i){return e[t]>i[t]?1:-1}))}else{this.tableData.sort((function(e,i){return e[t]>i[t]?-1:1}))}};t.prototype.render=function(){var t=this;return r(n,{key:"ab0f80f5cbd63bdd7cb30bce36b5c3a4b2bcfc57"},r("table",{key:"30a9e88b6eb99cfb85255f1a644ad7d239156226",class:"table"},r("thead",{key:"39ddb051120f8b1109a11f79ef678521634af5d4",class:"thead"},r("tr",{key:"005042e306fcd6b2a44e24be0d10667da5d8a218",class:"header"},this.headerData.map((function(e,i){return r("th",{class:"header-title",key:i},t.sorting?r("bds-typo",{class:"title-click",onClick:function(){return t.orderColumn(e.value)},variant:"fs-14",bold:t.headerActive==="".concat(e.value)?"bold":"semi-bold"},e.heading):r("bds-typo",{variant:"fs-14",bold:"semi-bold"},e.heading),t.sortAscending===true&&t.sorting===true&&t.headerActive==="".concat(e.value)?r("bds-icon",{class:"header-icon",name:"arrow-up",size:"small"}):t.sortAscending===false&&t.sorting===true&&t.headerActive==="".concat(e.value)?r("bds-icon",{name:"arrow-down",size:"small"}):"")})))),r("tbody",{key:"dee098be4fb6d74fc062714f25cc2f3564b8e216"},this.tableData.map((function(e,i){return r("tr",{class:"body-row",key:i},t.headerData.map((function(n,a){return r("td",{class:"body-item",key:a},t.actionArea&&n.editAction?r("bds-button-icon",{onClick:function(){return t.clickButton(e,i,n.editAction)},variant:"secondary",icon:e["".concat(n.editAction)],size:"short"}):"",t.actionArea&&n.deleteAction?r("bds-button-icon",{onClick:function(){return t.clickButton(e,i,n.deleteAction)},variant:"secondary",icon:e["".concat(n.deleteAction)],size:"short"}):"",t.actionArea&&n.customAction?r("bds-button-icon",{onClick:function(){return t.clickButton(e,i,n.customAction)},variant:"secondary",icon:e["".concat(n.customAction)],size:"short"}):"",t.chips&&n.chips?r("bds-chip-tag",{color:e["".concat(n.chips)]?e["".concat(n.chips)]:"default"},e["".concat(n.value)]):"",t.avatar&&n.img?r("bds-avatar",{size:"extra-small",thumbnail:e["".concat(n.img)],name:e["".concat(n.value)]}):"",n.chips?"":r("bds-typo",{variant:"fs-14",bold:t.headerActive==="".concat(n.value)?"bold":"regular"},e["".concat(n.value)]))})))})))))};Object.defineProperty(t.prototype,"el",{get:function(){return a(this)},enumerable:false,configurable:true});return t}());o.style=s}}}));
//# sourceMappingURL=p-7570aa79.system.entry.js.map