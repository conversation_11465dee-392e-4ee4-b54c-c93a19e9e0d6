var __awaiter=this&&this.__awaiter||function(t,o,e,i){function a(t){return t instanceof e?t:new e((function(o){o(t)}))}return new(e||(e=Promise))((function(e,n){function s(t){try{d(i.next(t))}catch(t){n(t)}}function r(t){try{d(i["throw"](t))}catch(t){n(t)}}function d(t){t.done?e(t.value):a(t.value).then(s,r)}d((i=i.apply(t,o||[])).next())}))};var __generator=this&&this.__generator||function(t,o){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,a,n,s;return s={next:r(0),throw:r(1),return:r(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function r(t){return function(o){return d([t,o])}}function d(r){if(i)throw new TypeError("Generator is already executing.");while(s&&(s=0,r[0]&&(e=0)),e)try{if(i=1,a&&(n=r[0]&2?a["return"]:r[0]?a["throw"]||((n=a["return"])&&n.call(a),0):a.next)&&!(n=n.call(a,r[1])).done)return n;if(a=0,n)r=[r[0]&2,n.value];switch(r[0]){case 0:case 1:n=r;break;case 4:e.label++;return{value:r[1],done:false};case 5:e.label++;a=r[1];r=[0];continue;case 7:r=e.ops.pop();e.trys.pop();continue;default:if(!(n=e.trys,n=n.length>0&&n[n.length-1])&&(r[0]===6||r[0]===2)){e=0;continue}if(r[0]===3&&(!n||r[1]>n[0]&&r[1]<n[3])){e.label=r[1];break}if(r[0]===6&&e.label<n[1]){e.label=n[1];n=r;break}if(n&&e.label<n[2]){e.label=n[2];e.ops.push(r);break}if(n[2])e.ops.pop();e.trys.pop();continue}r=o.call(t,e)}catch(t){r=[6,t];a=0}finally{i=n=0}if(r[0]&5)throw r[1];return{value:r[0]?r[1]:void 0,done:true}}};System.register(["./p-B47mPBRA.system.js"],(function(t){"use strict";var o,e,i;return{setters:[function(t){o=t.r;e=t.c;i=t.h}],execute:function(){var a='.modal__dialog{opacity:0;visibility:hidden;width:100%;height:100%;position:fixed;top:0;left:0;-webkit-transition:opacity 0.3s ease-in-out;transition:opacity 0.3s ease-in-out;z-index:80000;display:none}.modal__dialog .outzone{position:absolute;inset:0;background-color:var(--color-content-din, rgb(0, 0, 0));opacity:0.7}.modal__dialog--dynamic{overflow-y:auto;padding-top:40px;padding-bottom:40px;height:-webkit-fill-available}.modal__dialog .modal{position:relative;margin:auto;width:592px;height:368px;border-radius:8px;background:var(--color-surface-1, rgb(246, 246, 246));-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));padding:32px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between}.modal__dialog .modal--dynamic{height:auto;width:auto;max-width:1000px}.modal__dialog .modal .close-button{position:relative;color:var(--color-content-default, rgb(40, 40, 40));-ms-flex-item-align:end;align-self:flex-end;margin-bottom:16px;cursor:pointer}.modal__dialog .modal .close-button::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.modal__dialog .modal .close-button:focus-visible{outline:none}.modal__dialog .modal .close-button:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.modal__dialog .modal .slot--dynamic{-ms-flex:1 1 auto;flex:1 1 auto}.modal__dialog--open{opacity:1;visibility:visible;display:-ms-flexbox;display:flex}';var n=t("bds_modal",function(){function t(t){var i=this;o(this,t);this.bdsModalChanged=e(this,"bdsModalChanged");this.open=false;this.closeButton=true;this.size="fixed";this.outzoneClose=true;this.enterClose=true;this.dtOutzone=null;this.dtButtonClose=null;this.listener=function(t){if(i.enterClose&&(t.key=="Enter"||t.key=="Escape")){i.toggle()}};this.handleMouseClick=function(){i.open=false};this.onClickOutzone=function(){if(i.outzoneClose){i.open=false}}}t.prototype.toggle=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){this.open=!this.open;return[2]}))}))};t.prototype.isOpenChanged=function(){if(this.open){document.addEventListener("keydown",this.listener,false);this.bdsModalChanged.emit({modalStatus:"opened"})}else{document.removeEventListener("keydown",this.listener,false);this.bdsModalChanged.emit({modalStatus:"closed"})}};t.prototype.render=function(){var t,o,e;var a=this;return i("div",{key:"3a82682d2fa331315a99d2ed594e58437d76d8b7",class:(t={modal__dialog:true,"modal__dialog--open":this.open},t["modal__dialog--".concat(this.size)]=true,t)},i("div",{key:"94dd90b69a47fa57c2033efeaf356d413922c09f",class:{outzone:true},onClick:function(){return a.onClickOutzone()},"data-test":this.dtOutzone}),i("div",{key:"4f8c5fc5915a1ccc96bfbe6d567cdfa8ddd5aa13",class:(o={modal:true},o["modal--".concat(this.size)]=true,o)},this.closeButton&&i("bds-icon",{key:"c1c5d0e581f5e12b701d883e499c9b1d01382ac5",size:"medium",class:"close-button",name:"close",tabindex:"0",onClick:this.handleMouseClick,dataTest:this.dtButtonClose}),this.size=="fixed"&&i("slot",{key:"f8dbec96027b15fe74507b081c9181306c980a62"}),this.size!=="fixed"&&i("div",{key:"62c76b08122932e90f7d09e6b0e81d3ea2d474a9",class:(e={slot:true},e["slot--".concat(this.size)]=true,e)},i("slot",{key:"0b3a9d7a888423f052d5b03dd7c92c05d77d4310"}))))};Object.defineProperty(t,"watchers",{get:function(){return{open:["isOpenChanged"]}},enumerable:false,configurable:true});return t}());n.style=a}}}));
//# sourceMappingURL=p-d287376b.system.entry.js.map