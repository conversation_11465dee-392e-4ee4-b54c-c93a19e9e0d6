{"version": 3, "names": ["badgeCss", "Badge", "exports", "class_1", "hostRef", "this", "type", "color", "shape", "icon", "animation", "dataTest", "prototype", "componentWillLoad", "number", "numberChanged", "newNumber", "render", "h", "Host", "key", "class", "_a", "chip_badge", "chip_size", "concat", "_b", "status", "_c", "_d", "trim", "size", "name", "_e", "variant", "bold", "margin", "_f", "empty"], "sources": ["src/components/badge/badge.scss?tag=bds-badge&encapsulation=shadow", "src/components/badge/badge.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n@keyframes pulse {\n  0% {\n    scale: 100%;\n    opacity: 1;\n  }\n  20% {\n    scale: 140%;\n    opacity: 0;\n  }\n  21% {\n    scale: 100%;\n    opacity: 1;\n  }\n  40% {\n    scale: 140%;\n    opacity: 0;\n  }\n  41% {\n    scale: 140%;\n    opacity: 0;\n  }\n  100% {\n    scale: 140%;\n    opacity: 0;\n  }\n}\n\n.color {\n  &--system {\n    background-color: $color-system;\n    color: $color-system;\n  }\n\n  &--danger {\n    background-color: $color-error;\n    color: $color-error;\n  }\n\n  &--warning {\n    background-color: $color-warning;\n    color: $color-warning;\n  }\n\n  &--success {\n    background-color: $color-success;\n    color: $color-success;\n  }\n\n  &--neutral {\n    background-color: $color-surface-3;\n    color: $color-surface-3;\n  }\n}\n\n:host {\n  display: inline-flex;\n}\n\n.chip_size {\n  min-width: 24px;\n}\n\n.chip_badge {\n  .status {\n    width: 8px;\n    height: 8px;\n\n    &--circle {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n      border-radius: 4px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    &--circle-true::before {\n      content: '';\n      width: 8px;\n      height: 8px;\n      position: absolute;\n      border: 1px solid;\n      border-radius: 8px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 0;\n      height: 0;\n      border-left: 4px solid transparent;\n      border-right: 4px solid transparent;\n\n      border-bottom: 8px solid;\n    }\n    &--triangle-reverse {\n      width: 0;\n      height: 0;\n      border-left: 4px solid transparent;\n      border-right: 4px solid transparent;\n\n      border-top: 8px solid;\n    }\n    &--polygon {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n      rotate: 45deg;\n    }\n    &--square {\n      width: 0;\n      height: 0;\n      border: 4px solid;\n    }\n  }\n\n  .icon {\n    position: relative;\n    bds-icon {\n      position: absolute;\n    }\n    .bds-icon {\n      color: $color-content-default;\n    }\n\n    &--circle {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        border-radius: 16px;\n      }\n      .trim-true::before {\n        content: '';\n        width: 24px;\n        height: 24px;\n        left: -2px;\n        top: -2px;\n        position: absolute;\n        border: 2px solid;\n        border-radius: 16px;\n        animation: pulse 2s ease-out infinite;\n      }\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: end;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0, 100% 100%, 0 100%);\n      }\n    }\n\n    &--triangle-reverse {\n      display: flex;\n      justify-content: center;\n      align-items: start;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 100%, 0 0, 100% 0);\n      }\n    }\n    &--polygon {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);\n      }\n    }\n    &--square {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n      }\n    }\n  }\n\n  .number {\n    display: flex;\n    height: 24px;\n    padding: 0 8px;\n    border-radius: 16px;\n    justify-content: center;\n    align-items: center;\n    position: relative;\n\n    &--true::before {\n      content: '';\n      width: 100%;\n      height: 24px;\n      left: -2px;\n      top: -2px;\n      position: absolute;\n      border: 2px solid;\n      border-radius: 16px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    .number_text {\n      color: $color-content-default;\n    }\n  }\n\n  .empty {\n    display: flex;\n    min-height: 24px;\n    min-width: 24px;\n    position: relative;\n\n    &--true::before {\n      content: '';\n      width: 100%;\n      height: 24px;\n      left: -2px;\n      top: -2px;\n      position: absolute;\n      border: 2px solid;\n      border-radius: 16px;\n      animation: pulse 2s ease-out infinite;\n    }\n\n    &--circle {\n      border-radius: 50%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        border-radius: 16px;\n      }\n      .trim-true::before {\n        content: '';\n        width: 24px;\n        height: 24px;\n        left: -2px;\n        top: -2px;\n        position: absolute;\n        border: 2px solid;\n        border-radius: 16px;\n        animation: pulse 2s ease-out infinite;\n      }\n    }\n\n    &--triangle {\n      display: flex;\n      justify-content: center;\n      align-items: end;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0, 100% 100%, 0 100%);\n      }\n    }\n\n    &--triangle-reverse {\n      display: flex;\n      justify-content: center;\n      align-items: start;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 100%, 0 0, 100% 0);\n      }\n    }\n    &--polygon {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);\n      }\n    }\n    &--square {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .trim {\n        width: 24px;\n        height: 24px;\n      }\n    }\n  }\n}\n", "import { Component, h, Host, Prop, State, Watch } from '@stencil/core';\n\nexport type Shape = 'circle' | 'triangle' | 'triangle-reverse' | 'polygon' | 'square';\n\nexport type Color = 'system' | 'danger' | 'warning' | 'success' | 'neutral';\n\nexport type Type = 'status' | 'icon' | 'number' | 'empty';\n\n@Component({\n  tag: 'bds-badge',\n  styleUrl: 'badge.scss',\n  shadow: true,\n})\nexport class Badge {\n  /**\n   * State for keep the value of the type.\n   */\n  @State() type?: Type = 'status';\n  /**\n   * Set the color of the component.\n   */\n  @Prop() color?: string = 'system';\n  /**\n   * Set the shape of the component.\n   */\n  @Prop() shape?: Shape = 'circle';\n  /**\n   * Set witch icon will be render inside the component.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Set the text in shape circle. Is just alow numbers, but if the number pass 999 a symbol '+' will be render.\n   */\n  @Prop() number?: number;\n  /**\n   * If true, actived the pulse animation.\n   */\n  @Prop() animation?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    if (this.icon === null && this.number) {\n      this.type = 'number';\n    } else if (!this.number && this.icon) {\n      this.type = 'icon';\n    } else if (this.number && this.icon) {\n      this.type = 'number';\n    } else if (this.number === 0) {\n      this.type = 'empty';\n    }\n  }\n\n  @Watch('number')\n  numberChanged(newNumber: number) {\n    if (newNumber === 0) {\n      this.type = 'empty';\n    } else if (this.icon === null && newNumber !== null) {\n      this.type = 'number';\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_badge: true,\n            chip_size: this.number !== 0 ? true : false,\n            [`chip_badge--${this.shape}`]: true,\n            [`chip_badge--${this.color}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.type === 'status' && (\n            <div\n              class={{\n                status: true,\n                [`status--${this.shape}`]: true,\n                [`color--${this.color}`]: true,\n                [`status--circle-${this.animation}`]: true,\n              }}\n            ></div>\n          )}\n          {this.type === 'icon' && (\n            <div class={{ icon: true, [`icon--${this.shape}`]: true }}>\n              <div class={{ [`color--${this.color}`]: true, trim: true, [`trim-${this.animation}`]: true }}></div>\n              <bds-icon size=\"xxx-small\" name={this.icon}></bds-icon>\n            </div>\n          )}\n          {this.type === 'number' && (\n            <div\n              class={{\n                number: true,\n                [`color--${this.color}`]: true,\n                [`number--${this.animation}`]: true,\n              }}\n            >\n              <bds-typo class=\"number_text\" variant=\"fs-12\" bold=\"bold\" margin={false}>\n                {this.number >= 999 ? '999+' : this.number}\n              </bds-typo>\n            </div>\n          )}\n          {this.type === 'empty' && (\n            <div\n              class={{\n                empty: true,\n                [`color--${this.color}`]: true,\n                [`empty--${this.shape}`]: true,\n                [`empty--${this.animation}`]: true,\n              }}\n            ></div>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kJAAA,IAAMA,EAAW,6hM,ICaJC,EAAKC,EAAA,uBALlB,SAAAC,EAAAC,G,UASWC,KAAIC,KAAU,SAIfD,KAAKE,MAAY,SAIjBF,KAAKG,MAAW,SAIhBH,KAAII,KAAY,KAQhBJ,KAASK,UAAa,MAKtBL,KAAQM,SAAY,IA8E7B,CA5ECR,EAAAS,UAAAC,kBAAA,WACE,GAAIR,KAAKI,OAAS,MAAQJ,KAAKS,OAAQ,CACrCT,KAAKC,KAAO,Q,MACP,IAAKD,KAAKS,QAAUT,KAAKI,KAAM,CACpCJ,KAAKC,KAAO,M,MACP,GAAID,KAAKS,QAAUT,KAAKI,KAAM,CACnCJ,KAAKC,KAAO,Q,MACP,GAAID,KAAKS,SAAW,EAAG,CAC5BT,KAAKC,KAAO,O,GAKhBH,EAAAS,UAAAG,cAAA,SAAcC,GACZ,GAAIA,IAAc,EAAG,CACnBX,KAAKC,KAAO,O,MACP,GAAID,KAAKI,OAAS,MAAQO,IAAc,KAAM,CACnDX,KAAKC,KAAO,Q,GAIhBH,EAAAS,UAAAK,OAAA,W,gBACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,OAAKC,EAAA,CACHC,WAAY,KACZC,UAAWnB,KAAKS,SAAW,EAAI,KAAO,OACtCQ,EAAC,eAAAG,OAAepB,KAAKG,QAAU,KAC/Bc,EAAC,eAAAG,OAAepB,KAAKE,QAAU,K,GAEtB,YAAAF,KAAKM,UAEfN,KAAKC,OAAS,UACbY,EAAA,OAAAE,IAAA,2CACEC,OAAKK,EAAA,CACHC,OAAQ,MACRD,EAAC,WAAAD,OAAWpB,KAAKG,QAAU,KAC3BkB,EAAC,UAAAD,OAAUpB,KAAKE,QAAU,KAC1BmB,EAAC,kBAAAD,OAAkBpB,KAAKK,YAAc,K,KAI3CL,KAAKC,OAAS,QACbY,EAAA,OAAAE,IAAA,2CAAKC,OAAKO,EAAA,CAAInB,KAAM,MAAMmB,EAAC,SAAAH,OAASpB,KAAKG,QAAU,KAAIoB,IACrDV,EAAK,OAAAE,IAAA,2CAAAC,OAAKQ,EAAA,GAAIA,EAAC,UAAAJ,OAAUpB,KAAKE,QAAU,KAAMsB,EAAAC,KAAM,KAAMD,EAAC,QAAAJ,OAAQpB,KAAKK,YAAc,KAAImB,KAC1FX,EAAU,YAAAE,IAAA,2CAAAW,KAAK,YAAYC,KAAM3B,KAAKI,QAGzCJ,KAAKC,OAAS,UACbY,EAAA,OAAAE,IAAA,2CACEC,OAAKY,EAAA,CACHnB,OAAQ,MACRmB,EAAC,UAAAR,OAAUpB,KAAKE,QAAU,KAC1B0B,EAAC,WAAAR,OAAWpB,KAAKK,YAAc,K,IAGjCQ,EAAA,YAAAE,IAAA,2CAAUC,MAAM,cAAca,QAAQ,QAAQC,KAAK,OAAOC,OAAQ,OAC/D/B,KAAKS,QAAU,IAAM,OAAST,KAAKS,SAIzCT,KAAKC,OAAS,SACbY,EAAA,OAAAE,IAAA,2CACEC,OAAKgB,EAAA,CACHC,MAAO,MACPD,EAAC,UAAAZ,OAAUpB,KAAKE,QAAU,KAC1B8B,EAAC,UAAAZ,OAAUpB,KAAKG,QAAU,KAC1B6B,EAAC,UAAAZ,OAAUpB,KAAKK,YAAc,K,2IAnG5B,I", "ignoreList": []}