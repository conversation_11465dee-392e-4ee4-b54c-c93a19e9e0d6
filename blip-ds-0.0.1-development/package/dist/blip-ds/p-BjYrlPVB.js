const e=[{conclude:"Concluir",from:"De",reset:"Redefinir",setTheDate:"Definir a data",to:"Até"}];const a=[{january:"Janeiro",february:"Fevereiro",march:"Mar<PERSON>o",april:"<PERSON>bri<PERSON>",may:"<PERSON><PERSON>",june:"<PERSON><PERSON>",july:"<PERSON><PERSON>",august:"Agosto",september:"Setembro",october:"Outubro",november:"Novembro",december:"Dezembro"}];const t=[{sunday:"Domingo",monday:"Segunda",tuesday:"Ter<PERSON>",wednesday:"Quarta",thursday:"Quinta",friday:"<PERSON><PERSON>",saturday:"Sábado"}];const r=[{dateFormatIsIncorrect:"Formato da data esta incorreto",betweenPeriodOf:"Por favor selecione uma data entre o período de",endDateIsEmpty:"Selecione a data final"}];const n=[{conclude:"Concluir",from:"En",reset:"Reiniciar",setTheDate:"Establecer la fecha",to:"<PERSON><PERSON>"}];const o=[{january:"Enero",february:"Febrero",march:"Marzo",april:"Abril",may:"Puede",june:"Junio",july:"Julio",august:"Agosto",september:"Septiembre",october:"Octubre",november:"Noviembre",december:"Diciembre"}];const s=[{sunday:"Domingo",monday:"Segundo",tuesday:"Martes",wednesday:"Cuatro",thursday:"Quinto",friday:"Viernes",saturday:"Sábado"}];const c=[{dateFormatIsIncorrect:"El formato de fecha es incorrecto",betweenPeriodOf:"Seleccione una fecha entre el período de",endDateIsEmpty:"Seleccione la fecha de finalización"}];const u=[{conclude:"Conclude",from:"From",reset:"Reset",setTheDate:"Set the date",to:"To"}];const d=[{january:"January",february:"February",march:"March",april:"April",may:"May",june:"June",july:"July",august:"August",september:"September",october:"October",november:"November",december:"December"}];const l=[{sunday:"Sunday",monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday"}];const y=[{dateFormatIsIncorrect:"Date format is incorrect",betweenPeriodOf:"Please select a date between the period of",endDateIsEmpty:"Select the end date"}];const b=(a,t)=>{let r;switch(a){case"pt_BR":r=e.map((e=>e[t]));break;case"es_ES":r=n.map((e=>e[t]));break;case"en_US":r=u.map((e=>e[t]));break;default:r=e.map((e=>e[t]))}return r};const i=(e,t)=>{let r;switch(e){case"pt_BR":r=a.map((e=>e[t]));break;case"es_ES":r=o.map((e=>e[t]));break;case"en_US":r=d.map((e=>e[t]));break;default:r=a.map((e=>e[t]))}return r};const m=(e,a)=>{let r;switch(e){case"pt_BR":r=t.map((e=>e[a]));break;case"es_ES":r=s.map((e=>e[a]));break;case"en_US":r=l.map((e=>e[a]));break;default:r=t.map((e=>e[a]))}return r};const h=(e,a)=>{let t;switch(e){case"pt_BR":t=r.map((e=>e[a]));break;case"es_ES":t=c.map((e=>e[a]));break;case"en_US":t=y.map((e=>e[a]));break;default:t=r.map((e=>e[a]))}return t};const f=new Date;const p=+(new Date).getFullYear();const S=+(new Date).getMonth();const v=e=>{const a={Sunday:m(e,"sunday")[0],Monday:m(e,"monday")[0],Tuesday:m(e,"tuesday")[0],Wednesday:m(e,"wednesday")[0],Thursday:m(e,"thursday")[0],Friday:m(e,"friday")[0],Saturday:m(e,"saturday")[0]};return a};const w=e=>[{value:0,label:i(e,"january")},{value:1,label:i(e,"february")},{value:2,label:i(e,"march")},{value:3,label:i(e,"april")},{value:4,label:i(e,"may")},{value:5,label:i(e,"june")},{value:6,label:i(e,"july")},{value:7,label:i(e,"august")},{value:8,label:i(e,"september")},{value:9,label:i(e,"october")},{value:10,label:i(e,"november")},{value:11,label:i(e,"december")}];const $=`${f.getDate().toString().padStart(2,"0")}/${(f.getMonth()+1).toString().padStart(2,"0")}/${f.getFullYear()}`;const D=`${f.getDate().toString().padStart(2,"0")}/${(f.getMonth()+1).toString().padStart(2,"0")}/${f.getFullYear()+100}`;const F=(e,a,t)=>{const r=[];let n=a<e-4?e-4:a;const o=t>e+6?e+6:t;while(n<=o){const e={value:n,label:n.toString()};r.push(e);n++}return r};const j=(e,a,t,r)=>{let n=[];if(e==a.year&&e==t.year){n=r.slice(a.month,t.month+1);return n}if(e==a.year){n=r.slice(a.month);return n}if(e==t.year){n=r.slice(0,t.month+1);return n}return r};const k=(e=p,a=S)=>{const t=new Date(e,a,1);const r=[];while(t.getMonth()===a){const e=new Date(t);const a={date:e.getDate(),month:e.getMonth(),year:e.getFullYear(),day:e.getDay()};r.push(a);t.setDate(t.getDate()+1)}return r};const g=(e=p,a=S)=>{const t={year:a-1<0?e-1:e,month:a-1<0?11:a-1};const r={year:a+1>11?e+1:e,month:a+1>11?0:a+1};const n={year:r.month+1>11?e+1:e,month:r.month+1>11?0:r.month+1};const o={year:t.year,month:t.month,days:k(t.year,t.month)};const s={year:e,month:a,days:k(e,a)};const c={year:r.year,month:r.month,days:k(r.year,r.month)};const u={year:n.year,month:n.month,days:k(n.year,n.month)};const d=[];d.push(o);d.push(s);d.push(c);d.push(u);return d};const _=e=>{const a=`${e.year}${e.month.toString().padStart(2,"0")}${e.date.toString().padStart(2,"0")}`;return a};const E=e=>{const a=`${e.getFullYear()}${e.getMonth().toString().padStart(2,"0")}${e.getDate().toString().padStart(2,"0")}`;return a};const T=e=>{const a=e.split("/");const t=new Date(parseFloat(a[2]),parseFloat(a[1])-1,parseFloat(a[0]));const r={date:t.getDate(),month:t.getMonth(),year:t.getFullYear(),day:t.getDay()};return r};const I=e=>{const a=e.split("/");return`${parseFloat(a[2])}-${parseFloat(a[1]).toString().padStart(2,"0")}-${parseFloat(a[0]).toString().padStart(2,"0")}`};const J=e=>{const a=e.split("-");return`${a[2]}/${a[1]}/${a[0]}`};const M=e=>`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}`;export{f as T,D as a,T as b,I as c,$ as d,M as e,_ as f,b as g,E as h,g as i,F as j,j as k,w as l,h as m,J as t,v as w};
//# sourceMappingURL=p-BjYrlPVB.js.map