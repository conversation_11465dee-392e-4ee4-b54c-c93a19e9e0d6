{"version": 3, "file": "p-JCCPPXk5.system.js", "sources": ["src/components/radio-button/radio-group.tsx"], "sourcesContent": ["import { Component, h, Host, Element, Prop, Watch, Event, EventEmitter, ComponentInterface } from '@stencil/core';\n\n@Component({\n  tag: 'bds-radio-group',\n  scoped: true,\n})\nexport class RadioGroup implements ComponentInterface {\n  private radioGroupElement?: HTMLCollectionOf<HTMLBdsRadioElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n  /**\n   * Emitted when the value has changed due to a click event.\n   */\n  @Event() bdsRadioGroupChange: EventEmitter;\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n\n    this.bdsRadioGroupChange.emit({ value });\n  }\n\n  componentWillRender() {\n    this.radioGroupElement = this.element.getElementsByTagName('bds-radio') as HTMLCollectionOf<HTMLBdsRadioElement>;\n    for (let i = 0; i < this.radioGroupElement.length; i++) {\n      this.radioGroupElement[i].addEventListener('bdsChange', (event: CustomEvent) =>\n        this.chagedOptions(this.radioGroupElement[i].value, event),\n      );\n    }\n  }\n\n  private chagedOptions = (value: string, event: CustomEvent): void => {\n    if (event.detail.checked == true) {\n      this.value = value;\n    }\n  };\n\n  private setSelectedRadio(value: string) {\n    const radios = this.radioGroupElement;\n    for (let i = 0; i < radios.length; i++) {\n      const getValue = radios[i].value;\n      radios[i].checked = false;\n      if (radios[i].checked == false && value == getValue) {\n        radios[i].checked = true;\n      }\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;kBAMa,UAAU,8BAAA,MAAA;YAJvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;YAKU,QAAA,IAAiB,CAAA,iBAAA,GAA2C,IAAI;oBA6BhE,IAAA,CAAA,aAAa,GAAG,CAAC,KAAa,EAAE,KAAkB,KAAU;wBAClE,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;YAChC,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;YAEtB,SAAC;YAoBF;YAvCC,IAAA,YAAY,CAAC,KAAa,EAAA;YACxB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBAE5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;;gBAG1C,mBAAmB,GAAA;oBACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAA0C;YAChH,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtD,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,KAAkB,KACzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAC3D;;;YAUG,IAAA,gBAAgB,CAAC,KAAa,EAAA;YACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB;YACrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;YAChC,YAAA,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK;YACzB,YAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE;YACnD,gBAAA,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI;;;;gBAK9B,MAAM,GAAA;oBACJ,QACE,EAAC,IAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACH,CAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;;;;;;;;"}