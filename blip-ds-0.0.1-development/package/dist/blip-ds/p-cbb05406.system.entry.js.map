{"version": 3, "names": ["stepper<PERSON>s", "BdsStepper", "exports", "class_1", "prototype", "connectedCallback", "_this", "this", "childOptions", "for<PERSON>ach", "option", "index", "length", "last", "componentDidLoad", "renderLine", "setActiveStep", "resetActiveSteps", "active", "setCompletedStep", "completed", "getActiveStep", "find", "step", "_i", "_a", "resetCompletedSteps", "Object", "defineProperty", "Array", "from", "el", "querySelectorAll", "line", "document", "createElement", "classList", "add", "item", "idx", "insertAdjacentHTML", "outerHTML", "render", "h", "Host", "key", "class"], "sources": ["src/components/stepper/stepper.scss?tag=bds-stepper&encapsulation=shadow", "src/components/stepper/stepper.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  width: 100%;\n  border-radius: 8px;\n  box-sizing: border-box;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n\n  ::slotted(bds-step:last-child) {\n    flex: inherit;\n  }\n}\n\n::slotted(.stepper__container__divisor) {\n  flex: 1 1 auto;\n  align-self: center;\n  height: 1.5px;\n  background: $color-content-disable;\n  margin: 0px 8px;\n  min-width: 24px;\n}\n\n::slotted(.stepper__container__divisor--completed) {\n  border-top: 2px solid $color-primary;\n}\n", "import { Component, ComponentInterface, h, Element, Method, Host } from '@stencil/core';\n@Component({\n  tag: 'bds-stepper',\n  styleUrl: 'stepper.scss',\n  shadow: true,\n})\nexport class BdsStepper implements ComponentInterface {\n  @Element() el: HTMLBdsStepperElement;\n\n  connectedCallback() {\n    this.childOptions.forEach((option, index) => {\n      option.index = index;\n      if (index === this.childOptions.length - 1) {\n        option.last = true;\n      }\n    });\n  }\n\n  componentDidLoad() {\n    this.renderLine();\n  }\n\n  /**\n   * Set the active step\n   *\n   * @param index The index of the step to be set as active\n   * @returns void\n   */\n  @Method()\n  public async setActiveStep(index: number): Promise<void> {\n    this.resetActiveSteps();\n    this.childOptions[index].active = true;\n  }\n\n  /**\n   * Set the completed step\n   *\n   * @param index The index of the step to be set as completed\n   * @returns void\n   */\n  @Method()\n  public async setCompletedStep(index: number): Promise<void> {\n    this.childOptions[index].completed = true;\n  }\n\n  /**\n   * Returns the active step\n   *\n   * @returns HTMLBdsStepElement\n   */\n  @Method()\n  public async getActiveStep(): Promise<number> {\n    return this.childOptions.find((step) => step.active === true).index;\n  }\n\n  /**\n   * Reset all active steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetActiveSteps() {\n    for (const step of this.childOptions) {\n      step.active = false;\n    }\n  }\n\n  /**\n   * Reset all completed steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetCompletedSteps() {\n    for (const step of this.childOptions) {\n      step.completed = false;\n    }\n  }\n\n  private get childOptions(): HTMLBdsStepElement[] {\n    return Array.from(this.el.querySelectorAll('bds-step'));\n  }\n\n  private renderLine() {\n    const line = document.createElement('div');\n    line.classList.add('stepper__container__divisor');\n\n    Array.from(this.childOptions).forEach((item, idx) => {\n      if (this.childOptions.length - 1 != idx) {\n        item.insertAdjacentHTML('afterend', line.outerHTML);\n      }\n    });\n  }\n\n  render() {\n    return (\n      <Host class=\"stepper__container\">\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAa,wkB,ICMNC,EAAUC,EAAA,yB,wBAGrBC,EAAAC,UAAAC,kBAAA,eAAAC,EAAAC,KACEA,KAAKC,aAAaC,SAAQ,SAACC,EAAQC,GACjCD,EAAOC,MAAQA,EACf,GAAIA,IAAUL,EAAKE,aAAaI,OAAS,EAAG,CAC1CF,EAAOG,KAAO,I,CAElB,G,EAGFV,EAAAC,UAAAU,iBAAA,WACEP,KAAKQ,Y,EAUMZ,EAAAC,UAAAY,cAAN,SAAoBL,G,qFACzBJ,KAAKU,mBACLV,KAAKC,aAAaG,GAAOO,OAAS,K,iBAUvBf,EAAAC,UAAAe,iBAAN,SAAuBR,G,qFAC5BJ,KAAKC,aAAaG,GAAOS,UAAY,K,iBAS1BjB,EAAAC,UAAAiB,cAAN,W,qFACL,SAAOd,KAAKC,aAAac,MAAK,SAACC,GAAS,OAAAA,EAAKL,SAAW,IAAhB,IAAsBP,M,QASnDR,EAAAC,UAAAa,iBAAN,W,+FACL,IAAAO,EAAA,EAAmBC,EAAAlB,KAAKC,aAALgB,EAAAC,EAAAb,OAAAY,IAAmB,CAA3BD,EAAIE,EAAAD,GACbD,EAAKL,OAAS,K,kBAULf,EAAAC,UAAAsB,oBAAN,W,+FACL,IAAAF,EAAA,EAAmBC,EAAAlB,KAAKC,aAALgB,EAAAC,EAAAb,OAAAY,IAAmB,CAA3BD,EAAIE,EAAAD,GACbD,EAAKH,UAAY,K,kBAIrBO,OAAAC,eAAYzB,EAAAC,UAAA,eAAY,C,IAAxB,WACE,OAAOyB,MAAMC,KAAKvB,KAAKwB,GAAGC,iBAAiB,Y,uCAGrC7B,EAAAC,UAAAW,WAAA,eAAAT,EAAAC,KACN,IAAM0B,EAAOC,SAASC,cAAc,OACpCF,EAAKG,UAAUC,IAAI,+BAEnBR,MAAMC,KAAKvB,KAAKC,cAAcC,SAAQ,SAAC6B,EAAMC,GAC3C,GAAIjC,EAAKE,aAAaI,OAAS,GAAK2B,EAAK,CACvCD,EAAKE,mBAAmB,WAAYP,EAAKQ,U,CAE7C,G,EAGFtC,EAAAC,UAAAsC,OAAA,WACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAM,sBACVH,EAAQ,QAAAE,IAAA,6C,uHA3FO,I", "ignoreList": []}