var __awaiter=this&&this.__awaiter||function(e,i,r,t){function a(e){return e instanceof r?e:new r((function(i){i(e)}))}return new(r||(r=Promise))((function(r,o){function n(e){try{s(t.next(e))}catch(e){o(e)}}function l(e){try{s(t["throw"](e))}catch(e){o(e)}}function s(e){e.done?r(e.value):a(e.value).then(n,l)}s((t=t.apply(e,i||[])).next())}))};var __generator=this&&this.__generator||function(e,i){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},t,a,o,n;return n={next:l(0),throw:l(1),return:l(2)},typeof Symbol==="function"&&(n[Symbol.iterator]=function(){return this}),n;function l(e){return function(i){return s([e,i])}}function s(l){if(t)throw new TypeError("Generator is already executing.");while(n&&(n=0,l[0]&&(r=0)),r)try{if(t=1,a&&(o=l[0]&2?a["return"]:l[0]?a["throw"]||((o=a["return"])&&o.call(a),0):a.next)&&!(o=o.call(a,l[1])).done)return o;if(a=0,o)l=[l[0]&2,o.value];switch(l[0]){case 0:case 1:o=l;break;case 4:r.label++;return{value:l[1],done:false};case 5:r.label++;a=l[1];l=[0];continue;case 7:l=r.ops.pop();r.trys.pop();continue;default:if(!(o=r.trys,o=o.length>0&&o[o.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!o||l[1]>o[0]&&l[1]<o[3])){r.label=l[1];break}if(l[0]===6&&r.label<o[1]){r.label=o[1];o=l;break}if(o&&r.label<o[2]){r.label=o[2];r.ops.push(l);break}if(o[2])r.ops.pop();r.trys.pop();continue}l=i.call(e,r)}catch(e){l=[6,e];a=0}finally{t=o=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:true}}};var __spreadArray=this&&this.__spreadArray||function(e,i,r){if(r||arguments.length===2)for(var t=0,a=i.length,o;t<a;t++){if(o||!(t in i)){if(!o)o=Array.prototype.slice.call(i,0,t);o[t]=i[t]}}return e.concat(o||Array.prototype.slice.call(i))};System.register(["./p-B47mPBRA.system.js"],(function(e){"use strict";var i,r,t,a;return{setters:[function(e){i=e.r;r=e.c;t=e.h;a=e.a}],execute:function(){var o=[{uploaded:"Arquivos enviados",dropHere:"Solte aqui para anexar o arquivo",dropOrClick:"Arraste e solte seus arquivos aqui ou clique para fazer upload do arquivo",formatError:"Ocorreu um erro ao anexar o arquivo, tente novamente ou selecione outro arquivo"}];var n=[{uploaded:"Archivos subidos",dropHere:"Soltar aquí para adjuntar archivo",dropOrClick:"Arrastre y suelte sus archivos aquí o haga clic para cargar el archivo",formatError:"Se produjo un error al adjuntar el archivo, inténtelo nuevamente o seleccione otro archivo"}];var l=[{uploaded:"Files uploaded",dropHere:"Drop here to attach file",dropOrClick:"Drag and drop your files here or click to upload file",formatError:"There was an error attaching the file, please try again or select another file"}];var s=function(e,i){var r;switch(e){case"pt_BR":r=o.map((function(e){return e[i]}));break;case"es_ES":r=n.map((function(e){return e[i]}));break;case"en_US":r=l.map((function(e){return e[i]}));break;default:r=o.map((function(e){return e[i]}))}return r};var c="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzg0IiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMzg0IDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZyBjbGlwLXBhdGg9InVybCgjY2xpcDBfMzc4OF8yMTU2NDApIj4KPGxpbmUgeDE9Ii0xMC45NzY3IiB5MT0iNzQuMzg0MyIgeDI9IjIyLjc3NzgiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjIyLjc3NzciIHkxPSI3NC4zODQzIiB4Mj0iNTYuNTMyMiIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iNTYuNTMyIiB5MT0iNzQuMzg0MyIgeDI9IjkwLjI4NjYiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjkwLjI4NjkiIHkxPSI3NC4zODQzIiB4Mj0iMTI0LjA0MiIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMTI0LjA0MSIgeTE9Ijc0LjM4NDMiIHgyPSIxNTcuNzk2IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjxsaW5lIHgxPSIxNTcuNzk2IiB5MT0iNzQuMzg0MyIgeDI9IjE5MS41NTEiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjE5MS41NTEiIHkxPSI3NC4zODQzIiB4Mj0iMjI1LjMwNSIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMjI1LjMwNSIgeTE9Ijc0LjM4NDMiIHgyPSIyNTkuMDYiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjI1OS4wNiIgeTE9Ijc0LjM4NDMiIHgyPSIyOTIuODE0IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjxsaW5lIHgxPSIyOTIuODE0IiB5MT0iNzQuMzg0MyIgeDI9IjMyNi41NjkiIHkyPSI1LjE3NzE5IiBzdHJva2U9IiNFN0VERjQiLz4KPGxpbmUgeDE9IjMyNi41NjkiIHkxPSI3NC4zODQzIiB4Mj0iMzYwLjMyMyIgeTI9IjUuMTc3MTkiIHN0cm9rZT0iI0U3RURGNCIvPgo8bGluZSB4MT0iMzYwLjMyNCIgeTE9Ijc0LjM4NDMiIHgyPSIzOTQuMDc4IiB5Mj0iNS4xNzcxOSIgc3Ryb2tlPSIjRTdFREY0Ii8+CjwvZz4KPGRlZnM+CjxjbGlwUGF0aCBpZD0iY2xpcDBfMzc4OF8yMTU2NDAiPgo8cmVjdCB3aWR0aD0iMzg0IiBoZWlnaHQ9IjgwIiBmaWxsPSJ3aGl0ZSIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo=";var d='.upload{min-width:400px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;gap:16px}.upload .upload-header{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;-ms-flex-align:center;align-items:center;gap:8px;color:var(--color-content-default, rgb(40, 40, 40))}.upload .upload-header_text{color:var(--color-content-default, rgb(40, 40, 40));display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.upload__edit--label{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-radius:8px;cursor:pointer;font-weight:normal;-webkit-box-sizing:border-box;box-sizing:border-box;padding:23px 16px;position:relative}.upload__edit--label::before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.upload__edit--label:focus-visible{outline:none}.upload__edit--label:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.upload__edit--label .upload__img--visible{display:-ms-flexbox;display:flex;width:100%;height:100%;border-radius:8px;position:absolute;background-color:var(--color-surface-2, rgb(237, 237, 237));z-index:1}.upload__edit--label .text-box{display:-ms-flexbox;display:flex;padding:8px;width:100%;text-align:center;z-index:2}.upload__edit--label .text-box .text{color:var(--color-content-default, rgb(40, 40, 40));width:100%;-ms-flex-wrap:wrap;flex-wrap:wrap}.upload__edit--label .text-box--hover{background-color:var(--color-surface-2, rgb(237, 237, 237))}.upload__edit--label .text-box--hover .text{color:var(--color-primary, rgb(30, 107, 241))}.upload__edit--label:hover{border:2px solid var(--color-primary, rgb(30, 107, 241));-webkit-box-sizing:border-box;box-sizing:border-box;padding:22px 16px;cursor:pointer;-webkit-text-decoration:underline var(--color-primary, rgb(30, 107, 241));text-decoration:underline var(--color-primary, rgb(30, 107, 241));color:var(--color-brand, rgb(0, 150, 250))}.upload__edit--label:hover .text{color:var(--color-primary, rgb(30, 107, 241))}.upload__edit--hover{background-size:cover;border:1px dashed var(--color-surface-4, rgb(20, 20, 20));color:var(--color-primary, rgb(30, 107, 241));font-weight:bold;border-radius:8px}.upload__img--invisible{display:none}.list-preview{border-top:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));max-height:200px;overflow-y:auto}.upload__preview{-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;padding:16px 0}.upload__preview .preview{display:-ms-flexbox;display:flex;padding:0 16px;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;gap:8px}.upload__preview .preview-text{font-family:"Nunito Sans", "Carbona", "Tahoma", "Helvetica", "Arial", sans-serif;font-size:0.875rem;font-weight:700;margin:0;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:100%;color:var(--color-content-default, rgb(40, 40, 40))}.upload__preview .preview-icon{color:var(--color-content-default, rgb(40, 40, 40))}.upload__preview .preview-icon:hover{cursor:pointer}.preview-length{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:end;padding-top:16px;text-align:end}.upload__edit input{position:absolute;left:0;top:0;right:0;bottom:0;opacity:0;width:0;height:100%}';var u=e("bds_upload",function(){function e(e){var t=this;i(this,e);this.bdsUploadDelete=r(this,"bdsUploadDelete");this.bdsUploadChange=r(this,"bdsUploadChange");this.files=[];this.haveFiles=false;this.hover=false;this.size=[];this.internalAccepts=[];this.formatError=false;this.language="pt_BR";this.dataAccept=[];this.dtInputFiles=null;this.dtLabelAddFile=null;this.dtButtonDelete=null;this.validationFiles=function(e,i){var r=".".concat(e.name.split(".").pop());var a=t.internalAccepts.includes(r);if(a){t.formatError=false;return}else{t.formatError=true;t.deleteFile(i);return}};this.handleDrop=function(e){t.haveFiles=true;var i=e.dataTransfer;var r=i.files;t.handleFiles(r)};this.handleFiles=function(e){if(!t.multiple){t.files=[e[0]]}else{t.files=__spreadArray(__spreadArray([],t.files,true),e,true)}t.bdsUploadChange.emit({value:t.files})};this.refInputElement=function(e){t.inputElement=e}}e.prototype.dataAcceptChanged=function(){if(this.dataAccept){if(typeof this.dataAccept==="string"){try{this.internalAccepts=JSON.parse(this.dataAccept)}catch(e){this.internalAccepts=[]}}else{this.internalAccepts=this.dataAccept}}else{this.internalAccepts=[]}};e.prototype.filesChanged=function(){if(this.files.length>0){for(var e=0;e<this.files.length;e++){if(this.internalAccepts.length>0){this.validationFiles(this.files[e],e)}}}};e.prototype.formatErrorChanged=function(e){var i=this;if(e){this.error=s(this.language,"formatError");setTimeout((function(){return i.error=null}),5e3)}};e.prototype.componentWillLoad=function(){this.dataAcceptChanged()};e.prototype.componentDidLoad=function(){var e=this;["dragenter","dragover","dragleave","drop"].forEach((function(i){e.dropArea.shadowRoot.addEventListener(i,e.preventDefaults,false);e.dropArea.shadowRoot.addEventListener(i,(function(){return e.hoverFile(true)}),false)}));["dragenter","dragover"].forEach((function(i){e.dropArea.shadowRoot.addEventListener(i,(function(){return e.preventDefaults}),false);e.dropArea.shadowRoot.addEventListener(i,(function(){return e.hoverFile(true)}),false)}));["dragleave","drop"].forEach((function(i){e.dropArea.shadowRoot.addEventListener(i,(function(){return e.preventDefaults}),false);e.dropArea.shadowRoot.addEventListener(i,(function(){return e.hoverFile(false)}),false)}));this.dropArea.shadowRoot.addEventListener("drop",this.handleDrop,false)};e.prototype.preventDefaults=function(e){e.preventDefault();e.stopPropagation()};e.prototype.hoverFile=function(e){this.hover=e};e.prototype.onUploadClick=function(e){if(e.length>0){if(!this.multiple){this.files=[e[0]]}else{this.files=__spreadArray(__spreadArray([],this.files,true),e,true)}this.haveFiles=true;this.getSize()}else{return false}this.bdsUploadChange.emit({value:this.files})};e.prototype.getSize=function(){var e=this;this.files.map((function(i){var r=i.size;e.size.push(r)}))};e.prototype.deleteFile=function(e){return __awaiter(this,void 0,void 0,(function(){var i;return __generator(this,(function(r){i=this.files.filter((function(i,r){return r==e&&i}));this.bdsUploadDelete.emit({value:i});this.files.splice(e,1);this.files=__spreadArray([],this.files,true);if(this.files.length===0){this.haveFiles=false}else{this.haveFiles=true}this.bdsUploadChange.emit({value:this.files});return[2]}))}))};e.prototype.deleteAllFiles=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){this.bdsUploadDelete.emit({value:this.files});this.files=[];if(this.files.length===0){this.haveFiles=false}else{this.haveFiles=true}this.bdsUploadChange.emit({value:this.files});return[2]}))}))};e.prototype.handleKeyDown=function(e){if(e.key=="Enter"){this.inputElement.click()}};e.prototype.render=function(){var e=this;return t("div",{key:"1f75f1bf649088e0893fe64f27a9ef37de84b78a",class:"upload"},t("div",{key:"b8fc280093d1fd6bdc46583c71a56a4f5a27f9e4",class:"upload-header"},t("bds-icon",{key:"145546937a1399ef5c121b2a1c0bd2545667e987",class:"upload-header_icon",size:"xxx-large",name:"upload"}),t("div",{key:"9fff2b1c4d1c5b6a50c18c5ea8fd8a9840552263",class:"upload-header_text"},t("bds-typo",{key:"726cf65caa77d4f372918868d050c8b69e29c038",variant:"fs-16",bold:"bold","aria-label":this.titleName},this.titleName),t("bds-typo",{key:"2065f3a6ad9157bc014ddb053b0fe73b0b7497b7",variant:"fs-14",bold:"regular","aria-label":this.subtitle},this.subtitle))),this.error?t("bds-banner",{context:"inside",variant:"error","aria-label":this.error},this.error):"",this.haveFiles?t("div",null,t("div",{class:"list-preview"},this.files.map((function(i,r){return t("div",{class:"upload__preview",key:r,id:"drop-area"},t("div",{class:"preview",id:"preview"},t("bds-icon",{size:"x-small",name:"attach"}),t("p",{class:"preview-text",id:"preview-text","aria-label":i.name},i.name),t("bds-button-icon",{class:"preview-icon",size:"short",icon:"trash",variant:"secondary",onClick:function(){return e.deleteFile(r)},"aria-label":"delete ".concat(i.name),"data-test":"".concat(e.dtButtonDelete,"-").concat(r)})))}))),this.multiple?t("bds-typo",{variant:"fs-14",italic:true,class:"preview-length","aria-label":s(this.language,"uploaded")},this.files.length>1?"".concat(this.files.length," ").concat(s(this.language,"uploaded")):""):""):"",t("div",{key:"d4e4954f155c51071cf3e884a73dd840b0912a8c",class:{upload__edit:true}},t("label",{key:"27ba7a823d4d48d726706f0ab2edecd1e77a4ebf",class:{"upload__edit--label":true,"upload__edit--hover":this.hover},id:"file-label",htmlFor:"file","data-test":this.dtLabelAddFile,tabindex:"0",onKeyDown:this.handleKeyDown.bind(this)},t("div",{key:"c3bd4bb2ab9c02c22f12cec3e6de4fa708bcd060",class:{"text-box":true,"text-box--hover":this.hover},id:"file-text_box"},this.hover?t("bds-typo",{class:"text",variant:"fs-14",bold:"regular","aria-label":s(this.language,"dropHere")},s(this.language,"dropHere")):t("bds-typo",{class:"text",variant:"fs-14",bold:"regular","aria-label":s(this.language,"dropOrClick")},s(this.language,"dropOrClick"))),t("img",{key:"a38b8c18d54da95ac8f75e036f5b17035ea0cbb0",class:{"upload__img--invisible":true,"upload__img--visible":this.hover},src:c})),t("input",{key:"e7ebf4a4a9180540ee0608ab06278ce07f5db04b",ref:this.refInputElement,type:"file",name:"files[]",id:"file",class:"upload__input",multiple:this.multiple,accept:this.internalAccepts.length>0?this.internalAccepts.toString():this.accept,onChange:function(i){return e.onUploadClick(i.target.files)},"data-test":this.dtInputFiles})))};Object.defineProperty(e.prototype,"dropArea",{get:function(){return a(this)},enumerable:false,configurable:true});Object.defineProperty(e,"watchers",{get:function(){return{dataAccept:["dataAcceptChanged"],files:["filesChanged"],formatError:["formatErrorChanged"]}},enumerable:false,configurable:true});return e}());u.style=d}}}));
//# sourceMappingURL=p-e41ee6da.system.entry.js.map