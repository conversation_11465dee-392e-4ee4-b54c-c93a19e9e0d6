{"version": 3, "names": ["accordionCss", "Accordi<PERSON><PERSON><PERSON><PERSON>", "constructor", "hostRef", "this", "accordion<PERSON><PERSON>", "isOpen", "btToggleIsfocus", "numberElement", "accordion<PERSON><PERSON>le", "icon", "avatar<PERSON><PERSON>", "avatar<PERSON><PERSON><PERSON>", "dataTest", "toggle<PERSON><PERSON><PERSON>", "_a", "close", "_b", "open", "toggle", "componentWillRender", "element", "parentElement", "handleKeyDown", "event", "key", "render", "h", "onClick", "class", "accordion_header", "name", "thumbnail", "size", "color", "bold", "variant", "acc<PERSON><PERSON><PERSON>", "accButton__isopen", "accButton__isfocus", "tabindex", "onKeyDown", "bind"], "sources": ["src/components/accordion/accordion.scss?tag=bds-accordion-header&encapsulation=shadow", "src/components/accordion/accordion-header.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.accordion_header {\n  display: flex;\n  grid-auto-flow: column;\n  gap: 24px;\n  justify-content: start;\n  align-items: center;\n  padding: 24px;\n  padding-right: 56px;\n  position: relative;\n  color: $color-content-default;\n  cursor: pointer;\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n  }\n\n  slot {\n    display: flex;\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  & * {\n    position: relative;\n    z-index: 1;\n  }\n\n  &:hover {\n    &::before {\n      background-color: $color-content-default;\n      opacity: 0.08;\n    }\n  }\n\n  & .accButton {\n    position: absolute;\n    right: 24px;\n    top: calc(50% - 16px);\n    border-radius: 8px;\n    contain: inherit;\n    -webkit-transition:\n      height 0.5s,\n      all 0.3s;\n    -moz-transition:\n      height 0.5s,\n      all 0.3s;\n    transition:\n      height 0.5s,\n      all 0.3s;\n    z-index: 1;\n\n    &__isopen {\n      transform: rotate(180deg);\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n\n    &:hover {\n      background-color: $color-surface-1;\n    }\n    &:active {\n      background-color: $color-surface-1;\n    }\n  }\n}\n\n.accordion_body {\n  height: 0;\n  overflow: hidden;\n  border-bottom: none;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n  @include custom-scroll;\n\n  &_isOpen {\n    overflow: overlay;\n  }\n\n  &_divisor {\n    border-bottom: 1px solid $color-border-1;\n  }\n\n  & .container {\n    padding: 8px 24px 48px;\n    position: relative;\n    color: $color-content-default;\n  }\n}\n", "import { Component, State, h, Prop, Element, Method } from '@stencil/core';\n\n@Component({\n  tag: 'bds-accordion-header',\n  styleUrl: 'accordion.scss',\n  shadow: true,\n})\nexport class AccordionHeader {\n  private accordionElement?: HTMLBdsAccordionElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpen?: boolean = false;\n\n  @State() btToggleIsfocus?: boolean = false;\n\n  @State() numberElement?: number = null;\n\n  /**\n   * Accordion Title. Used to add title in header accordion.\n   */\n  @Prop() accordionTitle?: string = null;\n\n  /**\n   * Icon. Used to add icon in header accordion.\n   */\n  @Prop() icon?: string = null;\n\n  /**\n   * Avatar Name. Used to add avatar in header accordion.\n   */\n  @Prop() avatarName?: string = null;\n\n  /**\n   * Avatar Thumb. Used to add avatar in header accordion.\n   */\n  @Prop() avatarThumb?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n\n  componentWillRender() {\n    this.accordionElement = this.element.parentElement as HTMLBdsAccordionElement;\n  }\n\n  private toggleHeader = (): void => {\n    if (this.isOpen) {\n      this.accordionElement?.close();\n    } else {\n      this.accordionElement?.open();\n    }\n  };\n\n  handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      if (this.isOpen) {\n        this.accordionElement?.close();\n      } else {\n        this.accordionElement?.open();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <div onClick={this.toggleHeader} class={{ accordion_header: true }} data-test={this.dataTest}>\n        {this.avatarName || this.avatarThumb ? (\n          <bds-avatar name={this.avatarName} thumbnail={this.avatarThumb} size=\"extra-small\"></bds-avatar>\n        ) : (\n          this.icon && <bds-icon size=\"x-large\" name={this.icon} color=\"inherit\"></bds-icon>\n        )}\n        {this.accordionTitle && (\n          <bds-typo bold=\"bold\" variant=\"fs-16\" line-height=\"double\">\n            {this.accordionTitle}\n          </bds-typo>\n        )}\n        <slot></slot>\n        <bds-icon\n          class={{\n            accButton: true,\n            accButton__isopen: this.isOpen,\n            accButton__isfocus: this.btToggleIsfocus,\n          }}\n          size=\"x-large\"\n          name=\"arrow-down\"\n          color=\"inherit\"\n          tabindex=\"0\"\n          onKeyDown={this.handleKeyDown.bind(this)}\n        ></bds-icon>\n      </div>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAe,ylE,MCORC,EAAe,MAL5B,WAAAC,CAAAC,G,UAMUC,KAAgBC,iBAA6B,KAI5CD,KAAME,OAAa,MAEnBF,KAAeG,gBAAa,MAE5BH,KAAaI,cAAY,KAK1BJ,KAAcK,eAAY,KAK1BL,KAAIM,KAAY,KAKhBN,KAAUO,WAAY,KAKtBP,KAAWQ,YAAY,KAKvBR,KAAQS,SAAY,KAqBpBT,KAAYU,aAAG,K,QACrB,GAAIV,KAAKE,OAAQ,EACfS,EAAAX,KAAKC,oBAAkB,MAAAU,SAAA,SAAAA,EAAAC,O,KAClB,EACLC,EAAAb,KAAKC,oBAAkB,MAAAY,SAAA,SAAAA,EAAAC,M,EA2C5B,CAjEC,YAAMC,GACJf,KAAKE,QAAUF,KAAKE,M,CAItB,UAAMY,GACJd,KAAKE,OAAS,I,CAIhB,WAAMU,GACJZ,KAAKE,OAAS,K,CAGhB,mBAAAc,GACEhB,KAAKC,iBAAmBD,KAAKiB,QAAQC,a,CAWvC,aAAAC,CAAcC,G,QACZ,GAAIA,EAAMC,KAAO,QAAS,CACxB,GAAIrB,KAAKE,OAAQ,EACfS,EAAAX,KAAKC,oBAAkB,MAAAU,SAAA,SAAAA,EAAAC,O,KAClB,EACLC,EAAAb,KAAKC,oBAAkB,MAAAY,SAAA,SAAAA,EAAAC,M,GAK7B,MAAAQ,GACE,OACEC,EAAK,OAAAF,IAAA,2CAAAG,QAASxB,KAAKU,aAAce,MAAO,CAAEC,iBAAkB,MAAmB,YAAA1B,KAAKS,UACjFT,KAAKO,YAAcP,KAAKQ,YACvBe,EAAA,cAAYI,KAAM3B,KAAKO,WAAYqB,UAAW5B,KAAKQ,YAAaqB,KAAK,gBAErE7B,KAAKM,MAAQiB,EAAA,YAAUM,KAAK,UAAUF,KAAM3B,KAAKM,KAAMwB,MAAM,YAE9D9B,KAAKK,gBACJkB,EAAU,YAAAF,IAAA,2CAAAU,KAAK,OAAOC,QAAQ,QAAO,cAAa,UAC/ChC,KAAKK,gBAGVkB,EAAa,QAAAF,IAAA,6CACbE,EAAA,YAAAF,IAAA,2CACEI,MAAO,CACLQ,UAAW,KACXC,kBAAmBlC,KAAKE,OACxBiC,mBAAoBnC,KAAKG,iBAE3B0B,KAAK,UACLF,KAAK,aACLG,MAAM,UACNM,SAAS,IACTC,UAAWrC,KAAKmB,cAAcmB,KAAKtC,Q", "ignoreList": []}