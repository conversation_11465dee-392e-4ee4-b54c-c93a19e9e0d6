{"version": 3, "names": ["tabsCss", "Tabs", "constructor", "hostRef", "this", "SCROLL_BEHAVIOR", "align", "handleHeaderResize", "tabsHeaderChildElement", "offsetWidth", "scrollWidth", "updateButtonsVisibility", "isScrollable", "setLeftButtonVisibility", "setRightButtonVisibility", "handleScrollButtonClick", "direction", "scrollButtonClick", "emit", "onScrollButtonClick", "event", "preventDefault", "options", "behavior", "top", "left", "detail", "distance", "_a", "getDistance", "scrollTo", "onSelectedTab", "handleButtonOverlay", "componentDidLoad", "getChildElements", "attachEvents", "handleActiveTab", "tabs", "Array", "from", "getElementsByTagName", "activeTab", "find", "tab", "active", "bdsTabInit", "group", "firstTab", "el", "querySelector", "leftButtonChildElement", "rightButtonChildElement", "window", "onresize", "onscroll", "clientWidth", "Math", "ceil", "scrollLeft", "style", "display", "header", "buttons", "for<PERSON>ach", "button", "isButtonOverlappingTab", "getAdjutScrollDistance", "tabRect", "getBoundingClientRect", "buttonRect", "elementIsOverlapping", "element", "overlaidElement", "elementStart", "x", "elementEnd", "width", "comparatorStart", "comparatorEnd", "id", "distanceDifference", "parseInt", "getComputedStyle", "marginRight", "parentElement", "render", "h", "Host", "key", "class", "icon", "size", "onClick", "variant"], "sources": ["src/components/tabs/tab (depreciated)/tabs.scss?tag=bds-tabs", "src/components/tabs/tab (depreciated)/tabs.tsx"], "sourcesContent": [".bds-tabs {\n  width: 100%;\n  display: flex;\n  z-index: 1100;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  flex-direction: row;\n  align-items: center;\n  height: 48px;\n  padding: 0 10px 0 10px;\n\n  &--center {\n    justify-content: center;\n  }\n\n  &--left {\n    justify-content: flex-start;\n  }\n\n  &--right {\n    justify-content: flex-end;\n  }\n\n  .bds-tabs__header {\n    display: flex;\n    flex-direction: row;\n    overflow: hidden;\n    align-items: stretch;\n    width: fit-content;\n  }\n\n  .bds-tabs__header-button-container {\n    padding: 0px;\n    min-width: 40px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { ScrollDirection, Display, Overflow } from './tabs-interface';\nimport { Component, Element, h, Host, Event, EventEmitter, Listen, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tabs',\n  styleUrl: 'tabs.scss',\n})\nexport class Tabs {\n  tabsHeaderChildElement: HTMLElement;\n  leftButtonChildElement: HTMLElement;\n  rightButtonChildElement: HTMLElement;\n\n  readonly SCROLL_BEHAVIOR = 'smooth';\n\n  @Element() el!: HTMLElement;\n\n  @Event() scrollButtonClick: EventEmitter<Overflow>;\n\n  @Event() bdsTabInit: EventEmitter;\n\n  @Prop() align: 'left' | 'center' | 'right' = 'center';\n\n  @Listen('scrollButtonClick')\n  onScrollButtonClick(event: CustomEvent<Overflow>) {\n    event.preventDefault();\n\n    const options: ScrollToOptions = {\n      behavior: this.SCROLL_BEHAVIOR,\n      top: 0,\n      left: event.detail.distance,\n    };\n    options.left ??= this.getDistance(options, event);\n    this.tabsHeaderChildElement.scrollTo(options);\n  }\n\n  @Listen('bdsTabChange', { target: 'body' })\n  onSelectedTab(event: CustomEvent) {\n    this.handleButtonOverlay(event.detail);\n  }\n\n  componentDidLoad() {\n    this.getChildElements();\n    this.attachEvents();\n    this.setLeftButtonVisibility(false);\n    this.setRightButtonVisibility(true);\n    this.handleActiveTab();\n  }\n\n  private handleActiveTab() {\n    const tabs = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab'));\n    const activeTab = tabs.find((tab) => tab.active);\n    if (activeTab) {\n      this.bdsTabInit.emit(activeTab.group);\n    } else {\n      const [firstTab] = tabs;\n      this.bdsTabInit.emit(firstTab.group);\n    }\n  }\n\n  private getChildElements() {\n    this.tabsHeaderChildElement = this.el.querySelector('.bds-tabs__header');\n    this.leftButtonChildElement = this.el.querySelector('#bds-tabs-button-left');\n    this.rightButtonChildElement = this.el.querySelector('#bds-tabs-button-right');\n  }\n\n  private attachEvents() {\n    window.onresize = this.handleHeaderResize;\n    this.tabsHeaderChildElement.onscroll = () =>\n      this.updateButtonsVisibility(this.tabsHeaderChildElement.scrollWidth > this.tabsHeaderChildElement.clientWidth);\n  }\n\n  private handleHeaderResize = () => {\n    if (this.tabsHeaderChildElement.offsetWidth < this.tabsHeaderChildElement.scrollWidth) {\n      this.updateButtonsVisibility(true);\n    } else {\n      this.updateButtonsVisibility(false);\n    }\n  };\n\n  private updateButtonsVisibility = (isScrollable: boolean) => {\n    this.setLeftButtonVisibility(isScrollable);\n    this.setRightButtonVisibility(isScrollable);\n  };\n\n  private handleScrollButtonClick = (direction: ScrollDirection) => {\n    this.scrollButtonClick.emit({ direction });\n  };\n\n  private setRightButtonVisibility(isScrollable: boolean) {\n    if (\n      isScrollable &&\n      this.tabsHeaderChildElement.scrollWidth >\n        Math.ceil(this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n    ) {\n      this.rightButtonChildElement.style.display = Display.BLOCK;\n    } else {\n      this.rightButtonChildElement.style.display = Display.NONE;\n    }\n  }\n\n  private setLeftButtonVisibility(isScrollable: boolean) {\n    this.leftButtonChildElement.style.display =\n      this.tabsHeaderChildElement.scrollLeft > 0 && isScrollable ? Display.BLOCK : Display.NONE;\n  }\n\n  private handleButtonOverlay(group: string) {\n    const tab = Array.from(this.tabsHeaderChildElement.getElementsByTagName('bds-tab')).find((header) => {\n      return header.group == group;\n    });\n\n    const buttons = [this.leftButtonChildElement, this.rightButtonChildElement];\n    buttons.forEach((button) => {\n      if (this.isButtonOverlappingTab(button, tab)) {\n        const distance = this.getAdjutScrollDistance(button, tab);\n        this.scrollButtonClick.emit({ distance: distance });\n      }\n    });\n  }\n\n  private isButtonOverlappingTab(button: HTMLElement, tab: HTMLElement) {\n    const tabRect = tab.getBoundingClientRect();\n    const buttonRect = button.getBoundingClientRect();\n\n    return this.elementIsOverlapping(buttonRect, tabRect);\n  }\n\n  private elementIsOverlapping(element: DOMRect, overlaidElement: DOMRect): boolean {\n    const elementStart = element.x;\n    const elementEnd = element.x + element.width;\n\n    const comparatorStart = overlaidElement.x;\n    const comparatorEnd = overlaidElement.x + overlaidElement.width;\n\n    return (\n      (elementStart >= comparatorStart && elementStart <= comparatorEnd) ||\n      (elementEnd >= comparatorStart && elementEnd <= comparatorEnd)\n    );\n  }\n\n  private getAdjutScrollDistance(button: HTMLElement, tab: HTMLElement) {\n    const direction = button.id == 'bds-tabs-button-left' ? ScrollDirection.LEFT : ScrollDirection.RIGHT;\n\n    const distanceDifference = tab.clientWidth + parseInt(getComputedStyle(tab).marginRight) - button.offsetWidth;\n\n    if (direction == ScrollDirection.RIGHT) {\n      return tab.parentElement.scrollLeft + distanceDifference;\n    } else {\n      return tab.parentElement.scrollLeft - distanceDifference;\n    }\n  }\n\n  private getDistance(options: ScrollToOptions, event: CustomEvent<Overflow>): number {\n    return event.detail.direction == ScrollDirection.RIGHT\n      ? (options.left = this.tabsHeaderChildElement.scrollLeft + this.tabsHeaderChildElement.clientWidth)\n      : (options.left = this.tabsHeaderChildElement.scrollLeft - this.tabsHeaderChildElement.clientWidth);\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host\n        class={{\n          'bds-tabs': true,\n          [`bds-tabs--${this.align}`]: true,\n        }}\n      >\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-left\"\n            size=\"short\"\n            id=\"bds-tabs-button-left\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.LEFT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n\n        <div class=\"bds-tabs__header\">\n          <slot />\n        </div>\n\n        <div class=\"bds-tabs__header-button-container\">\n          <bds-button-icon\n            class=\"bds-tabs__header-button\"\n            icon=\"arrow-right\"\n            size=\"short\"\n            id=\"bds-tabs-button-right\"\n            onClick={() => this.handleScrollButtonClick(ScrollDirection.RIGHT)}\n            variant=\"secondary\"\n          ></bds-button-icon>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAU,uvB,MCQHC,EAAI,MAJjB,WAAAC,CAAAC,G,kGASWC,KAAeC,gBAAG,SAQnBD,KAAKE,MAAgC,SAmDrCF,KAAkBG,mBAAG,KAC3B,GAAIH,KAAKI,uBAAuBC,YAAcL,KAAKI,uBAAuBE,YAAa,CACrFN,KAAKO,wBAAwB,K,KACxB,CACLP,KAAKO,wBAAwB,M,GAIzBP,KAAAO,wBAA2BC,IACjCR,KAAKS,wBAAwBD,GAC7BR,KAAKU,yBAAyBF,EAAa,EAGrCR,KAAAW,wBAA2BC,IACjCZ,KAAKa,kBAAkBC,KAAK,CAAEF,aAAY,CA4G7C,CA1KC,mBAAAG,CAAoBC,G,MAClBA,EAAMC,iBAEN,MAAMC,EAA2B,CAC/BC,SAAUnB,KAAKC,gBACfmB,IAAK,EACLC,KAAML,EAAMM,OAAOC,WAErBC,EAAAN,EAAQG,QAAR,MAAAG,SAAA,EAAAA,EAAAN,EAAQG,KAASrB,KAAKyB,YAAYP,EAASF,GAC3ChB,KAAKI,uBAAuBsB,SAASR,E,CAIvC,aAAAS,CAAcX,GACZhB,KAAK4B,oBAAoBZ,EAAMM,O,CAGjC,gBAAAO,GACE7B,KAAK8B,mBACL9B,KAAK+B,eACL/B,KAAKS,wBAAwB,OAC7BT,KAAKU,yBAAyB,MAC9BV,KAAKgC,iB,CAGC,eAAAA,GACN,MAAMC,EAAOC,MAAMC,KAAKnC,KAAKI,uBAAuBgC,qBAAqB,YACzE,MAAMC,EAAYJ,EAAKK,MAAMC,GAAQA,EAAIC,SACzC,GAAIH,EAAW,CACbrC,KAAKyC,WAAW3B,KAAKuB,EAAUK,M,KAC1B,CACL,MAAOC,GAAYV,EACnBjC,KAAKyC,WAAW3B,KAAK6B,EAASD,M,EAI1B,gBAAAZ,GACN9B,KAAKI,uBAAyBJ,KAAK4C,GAAGC,cAAc,qBACpD7C,KAAK8C,uBAAyB9C,KAAK4C,GAAGC,cAAc,yBACpD7C,KAAK+C,wBAA0B/C,KAAK4C,GAAGC,cAAc,yB,CAG/C,YAAAd,GACNiB,OAAOC,SAAWjD,KAAKG,mBACvBH,KAAKI,uBAAuB8C,SAAW,IACrClD,KAAKO,wBAAwBP,KAAKI,uBAAuBE,YAAcN,KAAKI,uBAAuB+C,Y,CAoB/F,wBAAAzC,CAAyBF,GAC/B,GACEA,GACAR,KAAKI,uBAAuBE,YAC1B8C,KAAKC,KAAKrD,KAAKI,uBAAuBkD,WAAatD,KAAKI,uBAAuB+C,aACjF,CACAnD,KAAK+C,wBAAwBQ,MAAMC,QAAO,O,KACrC,CACLxD,KAAK+C,wBAAwBQ,MAAMC,QAAO,M,EAItC,uBAAA/C,CAAwBD,GAC9BR,KAAK8C,uBAAuBS,MAAMC,QAChCxD,KAAKI,uBAAuBkD,WAAa,GAAK9C,EAA6B,c,CAGvE,mBAAAoB,CAAoBc,GAC1B,MAAMH,EAAML,MAAMC,KAAKnC,KAAKI,uBAAuBgC,qBAAqB,YAAYE,MAAMmB,GACjFA,EAAOf,OAASA,IAGzB,MAAMgB,EAAU,CAAC1D,KAAK8C,uBAAwB9C,KAAK+C,yBACnDW,EAAQC,SAASC,IACf,GAAI5D,KAAK6D,uBAAuBD,EAAQrB,GAAM,CAC5C,MAAMhB,EAAWvB,KAAK8D,uBAAuBF,EAAQrB,GACrDvC,KAAKa,kBAAkBC,KAAK,CAAES,SAAUA,G,KAKtC,sBAAAsC,CAAuBD,EAAqBrB,GAClD,MAAMwB,EAAUxB,EAAIyB,wBACpB,MAAMC,EAAaL,EAAOI,wBAE1B,OAAOhE,KAAKkE,qBAAqBD,EAAYF,E,CAGvC,oBAAAG,CAAqBC,EAAkBC,GAC7C,MAAMC,EAAeF,EAAQG,EAC7B,MAAMC,EAAaJ,EAAQG,EAAIH,EAAQK,MAEvC,MAAMC,EAAkBL,EAAgBE,EACxC,MAAMI,EAAgBN,EAAgBE,EAAIF,EAAgBI,MAE1D,OACGH,GAAgBI,GAAmBJ,GAAgBK,GACnDH,GAAcE,GAAmBF,GAAcG,C,CAI5C,sBAAAZ,CAAuBF,EAAqBrB,GAClD,MAAM3B,EAAYgD,EAAOe,IAAM,uBAAwB,OAAuB,QAE9E,MAAMC,EAAqBrC,EAAIY,YAAc0B,SAASC,iBAAiBvC,GAAKwC,aAAenB,EAAOvD,YAElG,GAAIO,GAAkC,QAAE,CACtC,OAAO2B,EAAIyC,cAAc1B,WAAasB,C,KACjC,CACL,OAAOrC,EAAIyC,cAAc1B,WAAasB,C,EAIlC,WAAAnD,CAAYP,EAA0BF,GAC5C,OAAOA,EAAMM,OAAOV,WAAkC,QACjDM,EAAQG,KAAOrB,KAAKI,uBAAuBkD,WAAatD,KAAKI,uBAAuB+C,YACpFjC,EAAQG,KAAOrB,KAAKI,uBAAuBkD,WAAatD,KAAKI,uBAAuB+C,W,CAG3F,MAAA8B,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACL,WAAY,KACZ,CAAC,aAAarF,KAAKE,SAAU,OAG/BgF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,qCACTH,EAAA,mBAAAE,IAAA,2CACEC,MAAM,0BACNC,KAAK,aACLC,KAAK,QACLZ,GAAG,uBACHa,QAAS,IAAMxF,KAAKW,wBAAuB,QAC3C8E,QAAQ,eAIZP,EAAK,OAAAE,IAAA,2CAAAC,MAAM,oBACTH,EAAA,QAAAE,IAAA,8CAGFF,EAAK,OAAAE,IAAA,2CAAAC,MAAM,qCACTH,EAAA,mBAAAE,IAAA,2CACEC,MAAM,0BACNC,KAAK,cACLC,KAAK,QACLZ,GAAG,wBACHa,QAAS,IAAMxF,KAAKW,wBAAuB,SAC3C8E,QAAQ,e", "ignoreList": []}