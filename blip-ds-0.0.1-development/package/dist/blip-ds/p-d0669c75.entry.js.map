{"version": 3, "names": ["autocompleteCss", "BdsAutocomplete", "constructor", "hostRef", "this", "intoView", "isPressed", "isOpen", "text", "textMultiselect", "placeholderState", "placeholder", "isFocused", "validationDanger", "validationMesage", "danger", "success", "disabled", "searchOnlyTitle", "label", "icon", "helperMessage", "errorMessage", "successMessage", "optionsPosition", "clearIconOnFocus", "dataTest", "loading", "selectionType", "selectionTitle", "<PERSON><PERSON><PERSON>", "refDropdown", "el", "dropElement", "refIconDrop", "iconDropElement", "refCheckAllInput", "input", "checkAllInput", "onFocus", "bdsFocus", "emit", "onFocusout", "nativeInput", "value", "getText", "onBlur", "bdsBlur", "cleanInputSelection", "_a", "checkedOptions", "length", "getTextMultiselect", "onClickWrapper", "toggle", "focus", "getTextFromOption", "opt", "internalOptions", "internalOption", "find", "option", "titleText", "innerText", "childOptions", "data", "valueInput", "handlerMultiselect", "updateListChecked", "undefined", "resetFilterOptions", "setTimeout", "checked", "handleCheckAll", "event", "detail", "toMark", "<PERSON><PERSON><PERSON>", "classList", "add", "remove", "defaultCheckedOptions", "Array", "from", "filter", "item", "map", "term", "textContent", "handler", "async", "bdsCancel", "changedInputValue", "ev", "target", "bdsInput", "filterOptions", "setTimeoutFilter", "getSelectedValue", "isOpenChanged", "positionHeightDrop", "name", "setDefaultPlacement", "validatePositionDrop", "itemSelectedChanged", "bdsSelectedChange", "selected", "valueChanged", "bdsChange", "toString", "childOptionSelected", "handleWindow", "contains", "changeCheckedOptions", "bdsMultiselectedChange", "parseOptions", "options", "JSON", "parse", "e", "changeSelectionType", "typeOption", "addEventListener", "componentWillLoad", "getScrollParent", "componentDidLoad", "positionValue", "positionAbsoluteElement", "actionElement", "changedElement", "y", "shadowRoot", "querySelectorAll", "keyPressWrapper", "key", "nextS<PERSON>ling", "_b", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "previousSibling", "_d", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanMultipleSelection", "optionTextLowercase", "toLowerCase", "termLower", "includes", "removeAttribute", "setAttribute", "renderIcon", "h", "class", "input__icon", "size", "color", "renderLabel", "input__container__label", "variant", "bold", "renderMessage", "message", "styles", "part", "theme", "render", "Host", "select", "onClick", "tabindex", "input__container__wrapper", "input__container__text", "ref", "onInput", "type", "onKeyDown", "bind", "select__options", "refer", "onBdsChange", "idx", "onOptionSelected", "onOptionChecked", "bulkOption", "status"], "sources": ["src/components/autocomplete/autocomplete.scss?tag=bds-autocomplete&encapsulation=shadow", "src/components/autocomplete/autocomplete.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 250px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n  .input__container__text:placeholder-shown {\n    color: $color-content-ghost;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n    &__text::placeholder {\n      color: $color-content-ghost;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n  &__text::placeholder {\n    color: $color-content-ghost;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 8px;\n\n  .inside-input-left {\n    display: inline;\n  }\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n  flex-shrink: 99999;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n\n:host {\n  display: block;\n}\n\n.select {\n  position: relative;\n  outline: none;\n  overflow: hidden;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n\n    bds-icon {\n      margin-left: 10px;\n    }\n  }\n\n  .icon-hidden {\n    visibility: hidden;\n  }\n\n  &__options {\n    display: grid;\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    ::slotted(*) {\n      display: flex;\n      flex-flow: column;\n    }\n\n    .selection-title {\n      order: -2;\n      width: 100%;\n      padding: 8px 16px;\n      box-sizing: border-box;\n    }\n\n    .select-all {\n      order: -3;\n      padding: 8px 8px 8px 12px;\n      box-sizing: border-box;\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      outline: none;\n      flex-direction: row;\n    }\n\n    .content-divisor {\n      display: block;\n      width: 100%;\n      height: 1px;\n      background-color: $color-surface-1;\n\n      .divisor {\n        display: block;\n        margin: 0 16px;\n        height: 1px;\n        background-color: $color-border-2;\n      }\n    }\n\n    .load-spinner {\n      background-color: $color-surface-1;\n      height: 200px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n\n    .options-checked {\n      order: -1;\n    }\n  }\n}\n", "import { Component, h, Host, State, Prop, EventEmitter, Event, Watch, Element, Listen, Method } from '@stencil/core';\nimport {\n  AutocompleteOption,\n  AutocompleteChangeEventDetail,\n  AutocompleteSelectedChangeEventDetail,\n  AutocompleteOptionsPositionType,\n  AutocompleteMultiSelectedChangeEventDetail,\n} from './autocomplete-select-interface';\nimport { SelectOptionsPositionType } from '../selects/select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../utils/position-element';\n\nexport type SelectionType = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-autocomplete',\n  styleUrl: 'autocomplete.scss',\n  shadow: true,\n})\nexport class BdsAutocomplete {\n  private checkAllInput?: HTMLBdsCheckboxElement;\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() intoView?: HTMLElement = null;\n\n  @State() isPressed? = false;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  @State() textMultiselect? = '';\n\n  @State() placeholderState?: string = this.placeholder;\n\n  @State() internalOptions: AutocompleteOption[];\n\n  @State() cloneOptions: AutocompleteOption[];\n\n  @State() checkedOptions: AutocompleteOption[];\n\n  @State() isFocused?: boolean = false;\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | AutocompleteOption[];\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null;\n\n  /**\n   * the item selected.\n   */\n  @Prop({ mutable: true }) selected?: HTMLBdsSelectOptionElement | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Search only the title property\n   */\n  @Prop({ reflect: true }) searchOnlyTitle? = true;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop() optionsPosition?: AutocompleteOptionsPositionType = 'auto';\n\n  /**\n   * If true, the X icon will appear only when component is focused.\n   */\n  @Prop() clearIconOnFocus?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Is Loading, is the prop to enable that the component is loading.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Multiselect, Prop to enable multi selections.\n   */\n  @Prop() selectionType?: SelectionType = 'single';\n\n  /**\n   * Selection Title, Prop to enable title to select.\n   */\n  @Prop() selectionTitle?: string = '';\n\n    /**\n   * Selection Title, Prop to enable title to select.\n   */\n    @Prop() selectedAll?: boolean = true;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsSelectedChange!: EventEmitter<AutocompleteSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the selected value has changed.\n   */\n  @Event() bdsMultiselectedChange!: EventEmitter<AutocompleteMultiSelectedChangeEventDetail>;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<AutocompleteChangeEventDetail>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('selected')\n  itemSelectedChanged(): void {\n    this.bdsSelectedChange.emit(this.selected);\n  }\n\n  @Watch('value')\n  protected valueChanged(): void {\n    this.bdsChange.emit({ value: this.value == null ? this.value : this.value.toString() });\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n    this.selected = this.childOptionSelected;\n    this.text = this.getText();\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('checkedOptions')\n  protected changeCheckedOptions() {\n    this.placeholderState =\n      this.selectionType === 'multiple'\n        ? this.checkedOptions?.length === 0 || this.checkedOptions === null\n          ? this.placeholder\n          : ''\n        : this.placeholder;\n    this.getTextMultiselect(this.checkedOptions);\n    this.bdsMultiselectedChange.emit({ value: this.checkedOptions });\n  }\n\n  @Watch('options')\n  parseOptions() {\n    if (this.options) {\n      this.resetFilterOptions();\n      try {\n        this.internalOptions = typeof this.options === 'string' ? JSON.parse(this.options) : this.options;\n      } catch (e) {\n        this.internalOptions = [];\n      }\n    }\n  }\n\n  @Watch('selectionType')\n  protected changeSelectionType() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.intoView = getScrollParent(this.el);\n    this.options && this.parseOptions();\n  }\n\n  componentDidLoad() {\n    if (!this.options) {\n      for (const option of this.childOptions) {\n        if (this.selectionType === 'multiple') {\n          option.typeOption = 'checkbox';\n          option.addEventListener('optionChecked', this.handlerMultiselect);\n        } else {\n          option.typeOption = 'default';\n          option.selected = this.value === option.value;\n          option.addEventListener('optionSelected', this.handler);\n        }\n      }\n    }\n\n    this.text = this.getText();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: AutocompleteOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private refCheckAllInput = (input: HTMLBdsCheckboxElement): void => {\n    this.checkAllInput = input;\n  };\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  private onFocus = (): void => {\n    this.isFocused = true;\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  private onFocusout = (): void => {\n    if (!this.isOpen) {\n      this.nativeInput.value = this.getText();\n    }\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n    if (!this.isOpen) {\n      this.isFocused = false;\n      this.nativeInput.value = this.getText();\n      if (this.selectionType == 'multiple') this.cleanInputSelection();\n    }\n    if (this.selectionType == 'multiple' && this.checkedOptions?.length > 0)\n      this.getTextMultiselect(this.checkedOptions);\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.toggle();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.innerText ?? '');\n  };\n\n  private getText = (): string => {\n    const opt = this.childOptions.find((option) => option.value == this.value);\n    return this.getTextFromOption(opt);\n  };\n\n  private getTextMultiselect = (data): void => {\n    const valueInput = data?.length > 0 && `${data?.length} selecionados`;\n    this.textMultiselect = valueInput;\n  };\n\n  private handlerMultiselect = (): void => {\n    this.updateListChecked(this.childOptions);\n    this.nativeInput.value = '';\n    this.value = undefined;\n    this.resetFilterOptions();\n    if (this.childOptions.length != this.checkedOptions.length) {\n      setTimeout(() => {\n        this.checkAllInput.checked = false;\n      }, 10);\n    }\n  };\n\n  private handleCheckAll = (event: CustomEvent): void => {\n    const {\n      detail: { checked },\n    } = event;\n    for (const option of this.childOptions) {\n      if (checked) {\n        option.toMark();\n      } else {\n        option.markOff();\n      }\n    }\n    setTimeout(() => {\n      this.updateListChecked(this.childOptions);\n    }, 10);\n  };\n\n  private updateListChecked = (data: HTMLBdsSelectOptionElement[]): void => {\n    for (const option of data) {\n      option.checked ? option.classList.add('option-checked') : option.classList.remove('option-checked');\n    }\n    const defaultCheckedOptions = Array.from(data).filter((item) => item.checked == true);\n    const value = defaultCheckedOptions.map((term) => ({\n      value: term.value,\n      label: term.textContent,\n      checked: term.checked,\n    }));\n    this.checkedOptions = value;\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private cleanInputSelection = async () => {\n    if (!this.disabled) {\n      this.value = '';\n      this.nativeInput.value = '';\n      this.isOpen = false;\n      this.bdsCancel.emit({ value: '' });\n      await this.resetFilterOptions();\n    }\n  };\n\n  @Method()\n  async cleanMultipleSelection() {\n    if (this.selectionType === 'multiple' && this.checkedOptions?.length > 0) {\n      for (const option of this.childOptions) {\n        option.checked = false;\n        option.classList.remove('option-checked');\n      }\n      this.checkedOptions = [];\n      this.checkAllInput.checked = false;\n      this.nativeInput.value = '';\n      this.value = undefined;\n      this.resetFilterOptions();\n    } else {\n      this.cleanInputSelection();\n    }\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      this.value = '';\n      if (this.isOpen) {\n        await this.resetFilterOptions();\n      } else {\n        this.setTimeoutFilter();\n      }\n    }\n\n    if (this.isOpen === false) {\n      this.value = this.getSelectedValue();\n      this.setTimeoutFilter();\n    }\n  };\n\n  private setTimeoutFilter(): void {\n    setTimeout(() => {\n      this.resetFilterOptions();\n    }, 500);\n  }\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n    }\n\n    for (const option of this.childOptions) {\n      const optionTextLowercase = this.searchOnlyTitle\n        ? this.getTextFromOption(option).toLowerCase()\n        : option.textContent.toLowerCase();\n\n      const termLower = term.toLowerCase();\n\n      optionTextLowercase.includes(termLower)\n        ? option.removeAttribute('invisible')\n        : option.setAttribute('invisible', 'invisible');\n    }\n  }\n\n  private async resetFilterOptions() {\n    const childOptions = this.childOptions;\n    for (const option of childOptions) {\n      option.removeAttribute('invisible');\n    }\n  }\n\n  private getSelectedValue() {\n    return this.childOptionSelected?.value;\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            select: true,\n            'input--state-primary': !this.danger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': this.isPressed,\n          }}\n          onClick={this.onClickWrapper}\n        >\n          {this.renderIcon()}\n          <div class=\"input__container\" tabindex=\"0\" onFocusout={this.onFocusout}>\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: true }}>\n              {this.textMultiselect?.length > 0 && (\n                <bds-typo variant=\"fs-14\" class=\"inside-input-left\">\n                  {this.textMultiselect}\n                </bds-typo>\n              )}\n              <input\n                class={{ input__container__text: true }}\n                ref={(input) => (this.nativeInput = input)}\n                disabled={this.disabled}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.changedInputValue}\n                placeholder={this.placeholderState}\n                type=\"text\"\n                value={this.text}\n                data-test={this.dataTest}\n                onKeyDown={this.keyPressWrapper.bind(this)}\n              />\n            </div>\n          </div>\n          <div class=\"select__icon\">\n            <bds-icon\n              size=\"small\"\n              name=\"error\"\n              theme=\"solid\"\n              onClick={this.cleanInputSelection}\n              class={{\n                'icon-hidden': (this.clearIconOnFocus && (!this.isFocused || !this.isOpen)) || !this.value,\n              }}\n            ></bds-icon>\n            <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n          </div>\n        </div>\n        {this.renderMessage()}\n        {this.loading ? (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            <bds-loading-spinner class=\"load-spinner\" size=\"small\"></bds-loading-spinner>\n          </div>\n        ) : (\n          <div\n            ref={(el) => this.refDropdown(el)}\n            class={{\n              select__options: true,\n              'select__options--open': this.isOpen,\n            }}\n          >\n            {this.selectionTitle && this.selectionType == 'multiple' && (\n              <bds-typo class=\"selection-title\" variant=\"fs-10\" bold=\"bold\">\n                {this.selectionTitle}\n              </bds-typo>\n            )}\n            {this.selectionType == 'multiple' && this.selectedAll && (\n              <bds-checkbox\n                ref={this.refCheckAllInput}\n                refer={`refer-multiselect`}\n                label={`Selecionar Todos`}\n                name=\"chack-all\"\n                class=\"select-all\"\n                onBdsChange={(ev) => this.handleCheckAll(ev)}\n              ></bds-checkbox>\n            )}\n            {this.checkedOptions?.length > 0 && (\n              <span class=\"content-divisor\">\n                <span class=\"divisor\"></span>\n              </span>\n            )}\n            {this.internalOptions ? (\n              this.internalOptions.map((option, idx) => (\n                <bds-select-option\n                  onOptionSelected={this.handler}\n                  onOptionChecked={this.handlerMultiselect}\n                  selected={this.value === option.value}\n                  value={option.value}\n                  key={idx}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                  type-option={this.selectionType == 'multiple' ? 'checkbox' : 'default'}\n                >\n                  {option.label}\n                </bds-select-option>\n              ))\n            ) : (\n              <slot />\n            )}\n          </div>\n        )}\n      </Host>\n    );\n  }\n}"], "mappings": "2GAAA,MAAMA,EAAkB,61c,MCkBXC,EAAe,MAL5B,WAAAC,CAAAC,G,iSAiBWC,KAAQC,SAAiB,KAEzBD,KAASE,UAAI,MAEbF,KAAMG,OAAI,MAEVH,KAAII,KAAI,GAERJ,KAAeK,gBAAI,GAEnBL,KAAAM,iBAA4BN,KAAKO,YAQjCP,KAASQ,UAAa,MAKtBR,KAAgBS,iBAAa,MAK7BT,KAAgBU,iBAAI,GAuBJV,KAAMW,OAAI,MAKKX,KAAOY,QAAa,MAInCZ,KAAQa,SAAI,MAKZb,KAAec,gBAAI,KAKpCd,KAAKe,MAAI,GAKQf,KAAIgB,KAAY,GAKjChB,KAAWO,YAAY,GAKvBP,KAAaiB,cAAY,GAIzBjB,KAAYkB,aAAY,GAIPlB,KAAcmB,eAAY,GAI3CnB,KAAeoB,gBAAqC,OAKpDpB,KAAgBqB,iBAAa,MAK7BrB,KAAQsB,SAAY,KAKpBtB,KAAOuB,QAAa,MAKpBvB,KAAawB,cAAmB,SAKhCxB,KAAcyB,eAAY,GAKxBzB,KAAW0B,YAAa,KAuK1B1B,KAAA2B,YAAeC,IACrB5B,KAAK6B,YAAcD,CAAE,EAGf5B,KAAA8B,YAAeF,IACrB5B,KAAK+B,gBAAkBH,CAAE,EAGnB5B,KAAAgC,iBAAoBC,IAC1BjC,KAAKkC,cAAgBD,CAAK,EAepBjC,KAAOmC,QAAG,KAChBnC,KAAKQ,UAAY,KACjBR,KAAKE,UAAY,KACjBF,KAAKoC,SAASC,MAAM,EAGdrC,KAAUsC,WAAG,KACnB,IAAKtC,KAAKG,OAAQ,CAChBH,KAAKuC,YAAYC,MAAQxC,KAAKyC,S,GAI1BzC,KAAM0C,OAAG,K,MACf1C,KAAK2C,QAAQN,OACbrC,KAAKE,UAAY,MACjB,IAAKF,KAAKG,OAAQ,CAChBH,KAAKQ,UAAY,MACjBR,KAAKuC,YAAYC,MAAQxC,KAAKyC,UAC9B,GAAIzC,KAAKwB,eAAiB,WAAYxB,KAAK4C,qB,CAE7C,GAAI5C,KAAKwB,eAAiB,cAAcqB,EAAA7C,KAAK8C,kBAAgB,MAAAD,SAAA,SAAAA,EAAAE,QAAS,EACpE/C,KAAKgD,mBAAmBhD,KAAK8C,eAAe,EAGxC9C,KAAciD,eAAG,KACvBjD,KAAKmC,UACLnC,KAAKkD,SACL,GAAIlD,KAAKuC,YAAa,CACpBvC,KAAKuC,YAAYY,O,GAIbnD,KAAMkD,OAAG,KACf,IAAKlD,KAAKa,SAAU,CAClBb,KAAKG,QAAUH,KAAKG,M,GAIhBH,KAAAoD,kBAAqBC,I,MAC3B,GAAIrD,KAAKsD,gBAAiB,CACxB,MAAMC,EAAiBvD,KAAKsD,gBAAgBE,MAAMC,GAAWA,EAAOjB,QAASa,IAAA,MAAAA,SAAA,SAAAA,EAAKb,SAClF,GAAIe,EAAgB,CAClB,OAAOA,EAAexC,K,EAG1B,OAAOsC,IAAA,MAAAA,SAAA,SAAAA,EAAKK,WAAYL,EAAIK,WAAab,EAAAQ,IAAA,MAAAA,SAAA,SAAAA,EAAKM,aAAa,MAAAd,SAAA,EAAAA,EAAA,EAAG,EAGxD7C,KAAOyC,QAAG,KAChB,MAAMY,EAAMrD,KAAK4D,aAAaJ,MAAMC,GAAWA,EAAOjB,OAASxC,KAAKwC,QACpE,OAAOxC,KAAKoD,kBAAkBC,EAAI,EAG5BrD,KAAAgD,mBAAsBa,IAC5B,MAAMC,GAAaD,IAAI,MAAJA,SAAI,SAAJA,EAAMd,QAAS,GAAK,GAAGc,IAAA,MAAAA,SAAI,SAAJA,EAAMd,sBAChD/C,KAAKK,gBAAkByD,CAAU,EAG3B9D,KAAkB+D,mBAAG,KAC3B/D,KAAKgE,kBAAkBhE,KAAK4D,cAC5B5D,KAAKuC,YAAYC,MAAQ,GACzBxC,KAAKwC,MAAQyB,UACbjE,KAAKkE,qBACL,GAAIlE,KAAK4D,aAAab,QAAU/C,KAAK8C,eAAeC,OAAQ,CAC1DoB,YAAW,KACTnE,KAAKkC,cAAckC,QAAU,KAAK,GACjC,G,GAICpE,KAAAqE,eAAkBC,IACxB,MACEC,QAAQH,QAAEA,IACRE,EACJ,IAAK,MAAMb,KAAUzD,KAAK4D,aAAc,CACtC,GAAIQ,EAAS,CACXX,EAAOe,Q,KACF,CACLf,EAAOgB,S,EAGXN,YAAW,KACTnE,KAAKgE,kBAAkBhE,KAAK4D,aAAa,GACxC,GAAG,EAGA5D,KAAAgE,kBAAqBH,IAC3B,IAAK,MAAMJ,KAAUI,EAAM,CACzBJ,EAAOW,QAAUX,EAAOiB,UAAUC,IAAI,kBAAoBlB,EAAOiB,UAAUE,OAAO,iB,CAEpF,MAAMC,EAAwBC,MAAMC,KAAKlB,GAAMmB,QAAQC,GAASA,EAAKb,SAAW,OAChF,MAAM5B,EAAQqC,EAAsBK,KAAKC,IAAI,CAC3C3C,MAAO2C,EAAK3C,MACZzB,MAAOoE,EAAKC,YACZhB,QAASe,EAAKf,YAEhBpE,KAAK8C,eAAiBN,CAAK,EAGrBxC,KAAAqF,QAAWf,IACjB,MACEC,QAAQ/B,MAAEA,IACR8B,EACJtE,KAAKwC,MAAQA,EACbxC,KAAKkD,QAAQ,EA4BPlD,KAAmB4C,oBAAG0C,UAC5B,IAAKtF,KAAKa,SAAU,CAClBb,KAAKwC,MAAQ,GACbxC,KAAKuC,YAAYC,MAAQ,GACzBxC,KAAKG,OAAS,MACdH,KAAKuF,UAAUlD,KAAK,CAAEG,MAAO,WACvBxC,KAAKkE,oB,GAqBPlE,KAAAwF,kBAAoBF,MAAOG,IACjC,MAAMxD,EAAQwD,EAAGC,OACjB,GAAIzD,EAAO,CACTjC,KAAKwC,MAAQP,EAAMO,OAAS,E,CAE9BxC,KAAK2F,SAAStD,KAAKoD,GACnB,GAAIzF,KAAKuC,YAAYC,MAAO,OACpBxC,KAAK4F,cAAc5F,KAAKuC,YAAYC,M,KACrC,CACLxC,KAAKwC,MAAQ,GACb,GAAIxC,KAAKG,OAAQ,OACTH,KAAKkE,oB,KACN,CACLlE,KAAK6F,kB,EAIT,GAAI7F,KAAKG,SAAW,MAAO,CACzBH,KAAKwC,MAAQxC,KAAK8F,mBAClB9F,KAAK6F,kB,EAuNV,CAliBW,aAAAE,CAAc5F,GACtB,GAAIH,KAAKgG,oBAAsB,SAAU,CACvChG,KAAK+B,gBAAgBkE,KAAOjG,KAAKG,OAAS,WAAa,Y,KAClD,CACLH,KAAK+B,gBAAgBkE,KAAOjG,KAAKG,OAAS,aAAe,U,CAE3D,GAAIA,EACF,GAAIH,KAAKoB,iBAAmB,OAAQ,CAClCpB,KAAKkG,oBAAoBlG,KAAKoB,gB,KACzB,CACLpB,KAAKmG,sB,EAKX,mBAAAC,GACEpG,KAAKqG,kBAAkBhE,KAAKrC,KAAKsG,S,CAIzB,YAAAC,GACRvG,KAAKwG,UAAUnE,KAAK,CAAEG,MAAOxC,KAAKwC,OAAS,KAAOxC,KAAKwC,MAAQxC,KAAKwC,MAAMiE,aAC1E,IAAK,MAAMhD,KAAUzD,KAAK4D,aAAc,CACtCH,EAAO6C,SAAWtG,KAAKwC,QAAUiB,EAAOjB,K,CAE1CxC,KAAKsG,SAAWtG,KAAK0G,oBACrB1G,KAAKI,KAAOJ,KAAKyC,S,CAInB,YAAAkE,CAAalB,GACX,IAAKzF,KAAK4B,GAAGgF,SAASnB,EAAGC,QAA6B,CACpD1F,KAAKG,OAAS,K,EAKR,oBAAA0G,G,MACR7G,KAAKM,iBACHN,KAAKwB,gBAAkB,aACnBqB,EAAA7C,KAAK8C,kBAAc,MAAAD,SAAA,SAAAA,EAAEE,UAAW,GAAK/C,KAAK8C,iBAAmB,KAC3D9C,KAAKO,YACL,GACFP,KAAKO,YACXP,KAAKgD,mBAAmBhD,KAAK8C,gBAC7B9C,KAAK8G,uBAAuBzE,KAAK,CAAEG,MAAOxC,KAAK8C,gB,CAIjD,YAAAiE,GACE,GAAI/G,KAAKgH,QAAS,CAChBhH,KAAKkE,qBACL,IACElE,KAAKsD,uBAAyBtD,KAAKgH,UAAY,SAAWC,KAAKC,MAAMlH,KAAKgH,SAAWhH,KAAKgH,O,CAC1F,MAAOG,GACPnH,KAAKsD,gBAAkB,E,GAMnB,mBAAA8D,GACR,IAAKpH,KAAKgH,QAAS,CACjB,IAAK,MAAMvD,KAAUzD,KAAK4D,aAAc,CACtC,GAAI5D,KAAKwB,gBAAkB,WAAY,CACrCiC,EAAO4D,WAAa,WACpB5D,EAAO6D,iBAAiB,gBAAiBtH,KAAK+D,mB,KACzC,CACLN,EAAO4D,WAAa,UACpB5D,EAAO6C,SAAWtG,KAAKwC,QAAUiB,EAAOjB,MACxCiB,EAAO6D,iBAAiB,iBAAkBtH,KAAKqF,Q,IAMvD,iBAAAkC,GACEvH,KAAKC,SAAWuH,EAAgBxH,KAAK4B,IACrC5B,KAAKgH,SAAWhH,KAAK+G,c,CAGvB,gBAAAU,GACE,IAAKzH,KAAKgH,QAAS,CACjB,IAAK,MAAMvD,KAAUzD,KAAK4D,aAAc,CACtC,GAAI5D,KAAKwB,gBAAkB,WAAY,CACrCiC,EAAO4D,WAAa,WACpB5D,EAAO6D,iBAAiB,gBAAiBtH,KAAK+D,mB,KACzC,CACLN,EAAO4D,WAAa,UACpB5D,EAAO6C,SAAWtG,KAAKwC,QAAUiB,EAAOjB,MACxCiB,EAAO6D,iBAAiB,iBAAkBtH,KAAKqF,Q,GAKrDrF,KAAKI,KAAOJ,KAAKyC,UACjB,GAAIzC,KAAKoB,iBAAmB,OAAQ,CAClCpB,KAAKkG,oBAAoBlG,KAAKoB,gB,KACzB,CACLpB,KAAKmG,sB,EAID,mBAAAD,CAAoB1D,GAC1B,GAAIA,GAAS,SAAU,CACrBxC,KAAK6B,YAAY6C,UAAUC,IAAI,oCAC/B3E,KAAK+B,gBAAgBkE,KAAO,Y,KACvB,CACLjG,KAAK6B,YAAY6C,UAAUC,IAAI,iCAC/B3E,KAAK+B,gBAAgBkE,KAAO,U,EAIxB,oBAAAE,GACN,MAAMuB,EAAgBC,EAAwB,CAC5CC,cAAe5H,KAAK4B,GACpBiG,eAAgB7H,KAAK6B,YACrB5B,SAAUD,KAAKC,WAEjBD,KAAKgG,mBAAqB0B,EAAcI,EACxC,GAAIJ,EAAcI,GAAK,SAAU,CAC/B9H,KAAK6B,YAAY6C,UAAUC,IAAI,oCAC/B3E,KAAK+B,gBAAgBkE,KAAO,Y,KACvB,CACLjG,KAAK6B,YAAY6C,UAAUC,IAAI,iCAC/B3E,KAAK+B,gBAAgBkE,KAAO,U,EAgBhC,gBAAYrC,GACV,OAAO5D,KAAKgH,QACRlC,MAAMC,KAAK/E,KAAK4B,GAAGmG,WAAWC,iBAAiB,sBAC/ClD,MAAMC,KAAK/E,KAAK4B,GAAGoG,iBAAiB,qB,CAG1C,uBAAYtB,GACV,OAAO1G,KAAKgH,QACRlC,MAAMC,KAAK/E,KAAK4B,GAAGmG,WAAWC,iBAAiB,sBAAsBxE,MAAMC,GAAWA,EAAO6C,WAC7FxB,MAAMC,KAAK/E,KAAK4B,GAAGoG,iBAAiB,sBAAsBxE,MAAMC,GAAWA,EAAO6C,U,CA8GhF,eAAA2B,CAAgB3D,G,YACtB,OAAQA,EAAM4D,KACZ,IAAK,QACHlI,KAAKkD,SACL,MACF,IAAK,YACH,IAAKlD,KAAKa,SAAU,CAClBb,KAAKG,OAAS,I,CAEhB,GAAIH,KAAK0G,oBAAqB,CAC5B1G,KAAKwC,OAASK,EAAA7C,KAAK0G,oBAAoByB,eAA4C,MAAAtF,SAAA,SAAAA,EAAAL,MACnF,M,CAEFxC,KAAKwC,OAAS4F,EAAApI,KAAK4B,GAAGyG,qBAAkD,MAAAD,SAAA,SAAAA,EAAA5F,MACxE,MACF,IAAK,UACH,GAAIxC,KAAK0G,oBAAqB,CAC5B1G,KAAKwC,OAAS8F,EAAAtI,KAAK0G,oBAAoB6B,mBAAgD,MAAAD,SAAA,SAAAA,EAAA9F,MACvF,M,CAEFxC,KAAKwC,OAASgG,EAAAxI,KAAK4B,GAAG6G,oBAAiD,MAAAD,SAAA,SAAAA,EAAAhG,MACvE,M,CAeN,4BAAMkG,G,MACJ,GAAI1I,KAAKwB,gBAAkB,cAAcqB,EAAA7C,KAAK8C,kBAAgB,MAAAD,SAAA,SAAAA,EAAAE,QAAS,EAAG,CACxE,IAAK,MAAMU,KAAUzD,KAAK4D,aAAc,CACtCH,EAAOW,QAAU,MACjBX,EAAOiB,UAAUE,OAAO,iB,CAE1B5E,KAAK8C,eAAiB,GACtB9C,KAAKkC,cAAckC,QAAU,MAC7BpE,KAAKuC,YAAYC,MAAQ,GACzBxC,KAAKwC,MAAQyB,UACbjE,KAAKkE,oB,KACA,CACLlE,KAAK4C,qB,EA2BD,gBAAAiD,GACN1B,YAAW,KACTnE,KAAKkE,oBAAoB,GACxB,I,CAGG,mBAAM0B,CAAcT,GAC1B,IAAKA,EAAM,OACHnF,KAAKkE,oB,CAGb,IAAK,MAAMT,KAAUzD,KAAK4D,aAAc,CACtC,MAAM+E,EAAsB3I,KAAKc,gBAC7Bd,KAAKoD,kBAAkBK,GAAQmF,cAC/BnF,EAAO2B,YAAYwD,cAEvB,MAAMC,EAAY1D,EAAKyD,cAEvBD,EAAoBG,SAASD,GACzBpF,EAAOsF,gBAAgB,aACvBtF,EAAOuF,aAAa,YAAa,Y,EAIjC,wBAAM9E,GACZ,MAAMN,EAAe5D,KAAK4D,aAC1B,IAAK,MAAMH,KAAUG,EAAc,CACjCH,EAAOsF,gBAAgB,Y,EAInB,gBAAAjD,G,MACN,OAAOjD,EAAA7C,KAAK0G,uBAAmB,MAAA7D,SAAA,SAAAA,EAAEL,K,CAG3B,UAAAyG,GACN,OACEjJ,KAAKgB,MACHkI,EAAA,OACEC,MAAO,CACLC,YAAa,KACb,uBAAwBpJ,KAAKe,QAG/BmI,EAAU,YAAAG,KAAMrJ,KAAKe,MAAQ,SAAW,QAASkF,KAAMjG,KAAKgB,KAAMsI,MAAM,Y,CAMxE,WAAAC,GACN,OACEvJ,KAAKe,OACHmI,EAAA,SACEC,MAAO,CACLK,wBAAyB,KACzB,mCAAoCxJ,KAAKE,YAAcF,KAAKa,WAG9DqI,EAAA,YAAUO,QAAQ,QAAQC,KAAK,QAC5B1J,KAAKe,O,CAOR,aAAA4I,GACN,MAAM3I,EAAOhB,KAAKW,OAAS,QAAUX,KAAKY,QAAU,YAAc,OAClE,IAAIgJ,EAAU5J,KAAKW,OAASX,KAAKkB,aAAelB,KAAKY,QAAUZ,KAAKmB,eAAiBnB,KAAKiB,cAE1F,IAAK2I,GAAW5J,KAAKS,iBAAkBmJ,EAAU5J,KAAKU,iBAEtD,MAAMmJ,EACJ7J,KAAKW,QAAUX,KAAKS,iBAChB,wCACAT,KAAKY,QACH,yCACA,iBAER,GAAIgJ,EAAS,CACX,OACEV,EAAA,OAAKC,MAAOU,EAAQC,KAAK,kBACvBZ,EAAK,OAAAC,MAAM,wBACTD,EAAA,YAAUG,KAAK,UAAUpD,KAAMjF,EAAM+I,MAAM,UAAUT,MAAM,aAE7DJ,EAAA,YAAUC,MAAM,uBAAuBM,QAAQ,SAC5CG,G,CAMT,OAAO3F,S,CAGT,MAAA+F,G,QACE,OACEd,EAACe,EAAI,CAAA/B,IAAA,2DAAgBlI,KAAKa,SAAW,OAAS,MAC5CqI,EAAA,OAAAhB,IAAA,2CACEiB,MAAO,CACLlH,MAAO,KACPiI,OAAQ,KACR,wBAAyBlK,KAAKW,OAC9B,sBAAuBX,KAAKW,QAAUX,KAAKS,iBAC3C,uBAAwBT,KAAKY,QAC7B,wBAAyBZ,KAAKa,SAC9B,iBAAkBb,KAAKe,MACvB,iBAAkBf,KAAKE,WAEzBiK,QAASnK,KAAKiD,gBAEbjD,KAAKiJ,aACNC,EAAA,OAAAhB,IAAA,2CAAKiB,MAAM,mBAAmBiB,SAAS,IAAI9H,WAAYtC,KAAKsC,YACzDtC,KAAKuJ,cACNL,EAAA,OAAAhB,IAAA,2CAAKiB,MAAO,CAAEkB,0BAA2B,SACtCxH,EAAA7C,KAAKK,mBAAe,MAAAwC,SAAA,SAAAA,EAAEE,QAAS,GAC9BmG,EAAA,YAAAhB,IAAA,2CAAUuB,QAAQ,QAAQN,MAAM,qBAC7BnJ,KAAKK,iBAGV6I,EAAA,SAAAhB,IAAA,2CACEiB,MAAO,CAAEmB,uBAAwB,MACjCC,IAAMtI,GAAWjC,KAAKuC,YAAcN,EACpCpB,SAAUb,KAAKa,SACf6B,OAAQ1C,KAAK0C,OACbP,QAASnC,KAAKmC,QACdqI,QAASxK,KAAKwF,kBACdjF,YAAaP,KAAKM,iBAClBmK,KAAK,OACLjI,MAAOxC,KAAKI,KAAI,YACLJ,KAAKsB,SAChBoJ,UAAW1K,KAAKiI,gBAAgB0C,KAAK3K,UAI3CkJ,EAAK,OAAAhB,IAAA,2CAAAiB,MAAM,gBACTD,EAAA,YAAAhB,IAAA,2CACEmB,KAAK,QACLpD,KAAK,QACL8D,MAAM,QACNI,QAASnK,KAAK4C,oBACduG,MAAO,CACL,cAAgBnJ,KAAKqB,oBAAsBrB,KAAKQ,YAAcR,KAAKG,UAAaH,KAAKwC,SAGzF0G,EAAU,YAAAhB,IAAA,2CAAAqC,IAAM3I,GAAO5B,KAAK8B,YAAYF,GAAKyH,KAAK,QAAQC,MAAM,cAGnEtJ,KAAK2J,gBACL3J,KAAKuB,QACJ2H,EAAA,OACEqB,IAAM3I,GAAO5B,KAAK2B,YAAYC,GAC9BuH,MAAO,CACLyB,gBAAiB,KACjB,wBAAyB5K,KAAKG,SAGhC+I,EAAA,uBAAqBC,MAAM,eAAeE,KAAK,WAGjDH,EAAA,OACEqB,IAAM3I,GAAO5B,KAAK2B,YAAYC,GAC9BuH,MAAO,CACLyB,gBAAiB,KACjB,wBAAyB5K,KAAKG,SAG/BH,KAAKyB,gBAAkBzB,KAAKwB,eAAiB,YAC5C0H,EAAA,YAAUC,MAAM,kBAAkBM,QAAQ,QAAQC,KAAK,QACpD1J,KAAKyB,gBAGTzB,KAAKwB,eAAiB,YAAcxB,KAAK0B,aACxCwH,EAAA,gBACEqB,IAAKvK,KAAKgC,iBACV6I,MAAO,oBACP9J,MAAO,mBACPkF,KAAK,YACLkD,MAAM,aACN2B,YAAcrF,GAAOzF,KAAKqE,eAAeoB,OAG5C2C,EAAApI,KAAK8C,kBAAgB,MAAAsF,SAAA,SAAAA,EAAArF,QAAS,GAC7BmG,EAAM,QAAAC,MAAM,mBACVD,EAAA,QAAMC,MAAM,aAGfnJ,KAAKsD,gBACJtD,KAAKsD,gBAAgB4B,KAAI,CAACzB,EAAQsH,IAChC7B,EAAA,qBACE8B,iBAAkBhL,KAAKqF,QACvB4F,gBAAiBjL,KAAK+D,mBACtBuC,SAAUtG,KAAKwC,QAAUiB,EAAOjB,MAChCA,MAAOiB,EAAOjB,MACd0F,IAAK6C,EACLG,WAAYzH,EAAOyH,WACnBC,OAAQ1H,EAAO0H,OACF,cAAAnL,KAAKwB,eAAiB,WAAa,WAAa,WAE5DiC,EAAO1C,SAIZmI,EAAA,c", "ignoreList": []}