{"version": 3, "names": ["buttonGroupCss", "ButtonGroup", "constructor", "hostRef", "this", "activeIndexes", "Set", "size", "direction", "color", "multiple", "componentDidLoad", "buttons", "el", "getElementsByTagName", "setupButtons", "componentDidUpdate", "handlePropChanges", "i", "length", "button", "setAttribute", "toString", "addEventListener", "selectButton", "set<PERSON><PERSON><PERSON>", "updateButtonPosition", "updateButtonDirection", "updateButtonSize", "updateButtonColor", "activateButton", "index", "has", "delete", "add", "clear", "updateButtonStates", "clickedIndex", "isActive", "classList", "remove", "buttonSelected", "emit", "id", "setPosition", "setDirection", "setSize", "setColor", "render", "h", "Host", "key", "class"], "sources": ["src/components/button/button-group.scss?tag=bds-button-group&encapsulation=shadow", "src/components/button/button-group.tsx"], "sourcesContent": [":host {\n    width: fit-content;\n}", "import { Component, h, Element, State, Event, EventEmitter, Prop, Host, Watch, Method } from '@stencil/core';\nimport { direction } from '../grid/grid-interface';\nimport { ButtonSize } from './button';\n\ninterface HTMLBdsButtonElement extends HTMLElement {\n  setVariant(variant: string): void;\n  setColor(color: string): void;\n  setSize(size: string): void;\n  setDirection(direction: string): void;\n  isActive(active: boolean): void;\n  setPosition(position: string): void;\n}\n\n@Component({\n  tag: 'bds-button-group',\n  styleUrl: 'button-group.scss',\n  shadow: true,\n})\nexport class ButtonGroup {\n  @Element() el!: HTMLElement;\n\n  @State() activeIndexes: Set<number> = new Set();\n\n  /**\n   * Size of the buttons. Can be one of:\n   * 'medium', 'large'.\n   */\n  @Prop({ mutable: true }) size?: ButtonSize = 'medium';\n\n  /**\n   * Direction of the button group layout. Can be one of:\n   * 'row', 'column'.\n   */\n  @Prop({ mutable: true }) direction?: direction = 'row';\n\n  /**\n   * Color scheme for the buttons. Default is 'primary'.\n   */\n  @Prop({ mutable: true }) color?: string = 'primary';\n\n  /**\n   * Allows multiple buttons to be selected simultaneously if true.\n   */\n  @Prop({ mutable: true }) multiple? = false;\n\n  @Event() buttonSelected: EventEmitter;\n\n  private buttons: HTMLCollectionOf<HTMLBdsButtonElement>;\n\n  componentDidLoad() {\n    this.buttons = this.el.getElementsByTagName('bds-button') as HTMLCollectionOf<HTMLBdsButtonElement>;\n    this.setupButtons();\n  }\n\n  componentDidUpdate() {\n    this.setupButtons();\n  }\n\n  @Watch('size')\n  @Watch('direction')\n  @Watch('color')\n  @Watch('multiple')\n  handlePropChanges() {\n    // Re-setup buttons when props change\n    this.setupButtons();\n  }\n\n  setupButtons() {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      button.setAttribute('data-index', i.toString());\n      button.addEventListener('click', () => this.selectButton(i));\n      button.setVariant('outline');\n      this.updateButtonPosition(i);\n      this.updateButtonDirection(i);\n      this.updateButtonSize(i);\n      this.updateButtonColor(i);\n    }\n  }\n\n  @Method()\n  async activateButton(index: number) {\n    if (index >= 0 && index < this.buttons.length) {\n      this.selectButton(index);\n    }\n  }\n\n  selectButton(index: number) {\n    if (this.multiple) {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.delete(index);\n      } else {\n        this.activeIndexes.add(index);\n      }\n    } else {\n      if (this.activeIndexes.has(index)) {\n        this.activeIndexes.clear();\n      } else {\n        this.activeIndexes.clear();\n        this.activeIndexes.add(index);\n      }\n    }\n    this.updateButtonStates(index);\n  }\n\n  updateButtonStates(clickedIndex: number) {\n    for (let i = 0; i < this.buttons.length; i++) {\n      const button = this.buttons[i];\n      if (this.activeIndexes.has(i)) {\n        button.isActive(true);\n        button.setVariant('solid');\n        button.classList.add('active');\n      } else {\n        button.isActive(false);\n        button.setVariant('outline');\n        button.classList.remove('active');\n      }\n      if (i === clickedIndex) {\n        this.buttonSelected.emit(button.id);\n      }\n    }\n  }\n\n  updateButtonPosition(index: number) {\n    const button = this.buttons[index];\n    if (index === 0) {\n      button.setPosition('first');\n    } else if (index === this.buttons.length - 1) {\n      button.setPosition('last');\n    } else {\n      button.setPosition('middle');\n    }\n  }\n\n  updateButtonDirection(index: number) {\n    const button = this.buttons[index];\n    this.direction === 'row' ? button.setDirection('row') : button.setDirection('column');\n  }\n\n  updateButtonSize(index: number) {\n    const button = this.buttons[index];\n    this.size === 'medium' ? button.setSize('medium') : button.setSize('large');\n  }\n\n  updateButtonColor(index: number) {\n    const button = this.buttons[index];\n    button.setColor(this.color);\n  }\n\n  render() {\n    return (\n      <Host class=\"button_group\">\n        <bds-grid direction={this.direction}>\n          <slot></slot>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAiB,4E,MCkBVC,EAAW,MALxB,WAAAC,CAAAC,G,uDAQWC,KAAAC,cAA6B,IAAIC,IAMjBF,KAAIG,KAAgB,SAMpBH,KAASI,UAAe,MAKxBJ,KAAKK,MAAY,UAKjBL,KAAQM,SAAI,KAmHtC,CA7GC,gBAAAC,GACEP,KAAKQ,QAAUR,KAAKS,GAAGC,qBAAqB,cAC5CV,KAAKW,c,CAGP,kBAAAC,GACEZ,KAAKW,c,CAOP,iBAAAE,GAEEb,KAAKW,c,CAGP,YAAAA,GACE,IAAK,IAAIG,EAAI,EAAGA,EAAId,KAAKQ,QAAQO,OAAQD,IAAK,CAC5C,MAAME,EAAShB,KAAKQ,QAAQM,GAC5BE,EAAOC,aAAa,aAAcH,EAAEI,YACpCF,EAAOG,iBAAiB,SAAS,IAAMnB,KAAKoB,aAAaN,KACzDE,EAAOK,WAAW,WAClBrB,KAAKsB,qBAAqBR,GAC1Bd,KAAKuB,sBAAsBT,GAC3Bd,KAAKwB,iBAAiBV,GACtBd,KAAKyB,kBAAkBX,E,EAK3B,oBAAMY,CAAeC,GACnB,GAAIA,GAAS,GAAKA,EAAQ3B,KAAKQ,QAAQO,OAAQ,CAC7Cf,KAAKoB,aAAaO,E,EAItB,YAAAP,CAAaO,GACX,GAAI3B,KAAKM,SAAU,CACjB,GAAIN,KAAKC,cAAc2B,IAAID,GAAQ,CACjC3B,KAAKC,cAAc4B,OAAOF,E,KACrB,CACL3B,KAAKC,cAAc6B,IAAIH,E,MAEpB,CACL,GAAI3B,KAAKC,cAAc2B,IAAID,GAAQ,CACjC3B,KAAKC,cAAc8B,O,KACd,CACL/B,KAAKC,cAAc8B,QACnB/B,KAAKC,cAAc6B,IAAIH,E,EAG3B3B,KAAKgC,mBAAmBL,E,CAG1B,kBAAAK,CAAmBC,GACjB,IAAK,IAAInB,EAAI,EAAGA,EAAId,KAAKQ,QAAQO,OAAQD,IAAK,CAC5C,MAAME,EAAShB,KAAKQ,QAAQM,GAC5B,GAAId,KAAKC,cAAc2B,IAAId,GAAI,CAC7BE,EAAOkB,SAAS,MAChBlB,EAAOK,WAAW,SAClBL,EAAOmB,UAAUL,IAAI,S,KAChB,CACLd,EAAOkB,SAAS,OAChBlB,EAAOK,WAAW,WAClBL,EAAOmB,UAAUC,OAAO,S,CAE1B,GAAItB,IAAMmB,EAAc,CACtBjC,KAAKqC,eAAeC,KAAKtB,EAAOuB,G,GAKtC,oBAAAjB,CAAqBK,GACnB,MAAMX,EAAShB,KAAKQ,QAAQmB,GAC5B,GAAIA,IAAU,EAAG,CACfX,EAAOwB,YAAY,Q,MACd,GAAIb,IAAU3B,KAAKQ,QAAQO,OAAS,EAAG,CAC5CC,EAAOwB,YAAY,O,KACd,CACLxB,EAAOwB,YAAY,S,EAIvB,qBAAAjB,CAAsBI,GACpB,MAAMX,EAAShB,KAAKQ,QAAQmB,GAC5B3B,KAAKI,YAAc,MAAQY,EAAOyB,aAAa,OAASzB,EAAOyB,aAAa,S,CAG9E,gBAAAjB,CAAiBG,GACf,MAAMX,EAAShB,KAAKQ,QAAQmB,GAC5B3B,KAAKG,OAAS,SAAWa,EAAO0B,QAAQ,UAAY1B,EAAO0B,QAAQ,Q,CAGrE,iBAAAjB,CAAkBE,GAChB,MAAMX,EAAShB,KAAKQ,QAAQmB,GAC5BX,EAAO2B,SAAS3C,KAAKK,M,CAGvB,MAAAuC,GACE,OACEC,EAACC,EAAK,CAAAC,IAAA,2CAAAC,MAAM,gBACVH,EAAA,YAAAE,IAAA,2CAAU3C,UAAWJ,KAAKI,WACxByC,EAAa,QAAAE,IAAA,8C", "ignoreList": []}