{"version": 3, "names": ["gridCss", "Grid", "render", "h", "Host", "key", "class", "host", "this", "direction", "justifyContent", "container", "containerFluid", "flexWrap", "alignItems", "xxs", "xs", "sm", "md", "lg", "xg", "gap", "xxsOffset", "xsOffset", "smOffset", "mdOffset", "lgOffset", "xgOffset", "padding", "margin", "bgColor", "style", "height", "paperCss", "Paper", "constructor", "hostRef", "hasBorder", "elevation", "dataTest", "border", "width", "borderColor", "componentWillLoad", "testComponentCss", "TestComponent", "variant", "bold", "theme", "themeProviderCss", "ThemeProvider", "typoCss", "<PERSON><PERSON>", "lineHeight", "italic", "noWrap", "paragraph", "tag", "Element", "typo", "part"], "sources": ["src/components/grid/grid.scss?tag=bds-grid&encapsulation=shadow", "src/components/grid/grid.tsx", "src/components/paper/paper.scss?tag=bds-paper&encapsulation=shadow", "src/components/paper/paper.tsx", "src/components/test-component/test-component.scss?tag=bds-test-component", "src/components/test-component/test-component.tsx", "src/components/theme-provider/theme-provider.scss?tag=bds-theme-provider&encapsulation=shadow", "src/components/theme-provider/theme-provider.tsx", "src/components/typo/typo.scss?tag=bds-typo&encapsulation=shadow", "src/components/typo/typo.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$colors: (\n  \"color-brand\": $color-brand,\n  \"color-primary\": $color-primary,\n  \"color-secondary\": $color-secondary,\n  \"color-surface-0\": $color-surface-0,\n  \"color-surface-1\": $color-surface-1,\n  \"color-surface-2\": $color-surface-2,\n  \"color-surface-3\": $color-surface-3,\n  \"color-surface-4\": $color-surface-4,\n  \"color-surface-positive\": $color-surface-positive,\n  \"color-surface-negative\": $color-surface-negative,\n  \"color-surface-primary\": $color-surface-primary,\n  \"color-content-default\": $color-content-default,\n  \"color-content-disable\": $color-content-disable,\n  \"color-content-ghost\": $color-content-ghost,\n  \"color-content-bright\": $color-content-bright,\n  \"color-content-din\": $color-content-din,\n  \"color-border-1\": $color-border-1,\n  \"color-border-2\": $color-border-2,\n  \"color-border-3\": $color-border-3,\n  \"color-info\": $color-info,\n  \"color-system\": $color-system,\n  \"color-focus\": $color-focus,\n  \"color-success\": $color-success,\n  \"color-warning\": $color-warning,\n  \"color-error\": $color-error,\n  \"color-delete\": $color-delete,\n  \"color-shadow-0\": $color-shadow-0,\n  \"color-shadow-1\": $color-shadow-1,\n  \"color-hover\": $color-hover,\n  \"color-pressed\": $color-pressed,\n  \"color-positive\": $color-positive,\n  \"color-negative\": $color-negative\n);\n\n// Gera as classes de fundo para cada cor\n@each $name, $value in $colors {\n  :host(.#{$name}) {\n    background-color: #{$value};\n  }\n}\n\n:host {\n  display: flex;\n  box-sizing: border-box;\n}\n\n//Container and offset\n:host(.container) {\n  width: 100%;\n}\n:host(.xxsoffset--auto) {\n  margin-left: auto !important;\n}\n:host(.xxsoffset--1) {\n  margin-left: 8.33% !important;\n}\n:host(.xxsoffset--2) {\n  margin-left: 16.66% !important;\n}\n:host(.xxsoffset--3) {\n  margin-left: 24.99% !important;\n}\n:host(.xxsoffset--4) {\n  margin-left: 33.32% !important;\n}\n:host(.xxsoffset--5) {\n  margin-left: 41.65% !important;\n}\n:host(.xxsoffset--6) {\n  margin-left: 50% !important;\n}\n:host(.xxsoffset--7) {\n  margin-left: 58.33% !important;\n}\n:host(.xxsoffset--8) {\n  margin-left: 66.66% !important;\n}\n:host(.xxsoffset--9) {\n  margin-left: 74.99% !important;\n}\n:host(.xxsoffset--10) {\n  margin-left: 83.32% !important;\n}\n:host(.xxsoffset--11) {\n  margin-left: 91.65% !important;\n}\n:host(.xxsoffset--12) {\n  margin-left: 100% !important;\n}\n\n@media (max-width: 599px) {\n  :host(.container) {\n    width: 100%;\n  }\n  :host(.xxsoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.xxsoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.xxsoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.xxsoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.xxsoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.xxsoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.xxsoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.xxsoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.xxsoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.xxsoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.xxsoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.xxsoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.xxsoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n@media (min-width: 600px) {\n  :host(.container) {\n    width: 100%;\n  }\n  :host(.xxsoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.xsoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.xsoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.xsoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.xsoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.xsoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.xsoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.xsoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.xsoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.xsoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.xsoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.xsoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.xsoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n@media (min-width: 905px) {\n  :host(.container) {\n    max-width: 848px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n  :host(.smoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.smoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.smoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.smoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.smoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.smoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.smoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.smoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.smoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.smoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.smoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.smoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.smoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n@media (min-width: 993px) {\n  :host(.container) {\n    max-width: 944px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n  :host(.mdoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.mdoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.mdoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.mdoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.mdoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.mdoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.mdoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.mdoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.mdoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.mdoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.mdoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.mdoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.mdoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n@media (min-width: 1601px) {\n  :host(.container) {\n    max-width: 1328px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n  :host(.lgoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.lgoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.lgoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.lgoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.lgoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.lgoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.lgoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.lgoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.lgoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.lgoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.lgoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.lgoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.lgoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n@media (min-width: 1921px) {\n  :host(.container) {\n    max-width: 1424px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n  :host(.xgoffset--auto) {\n    margin-left: auto !important;\n  }\n  :host(.xgoffset--1) {\n    margin-left: 8.33% !important;\n  }\n  :host(.xgoffset--2) {\n    margin-left: 16.66% !important;\n  }\n  :host(.xgoffset--3) {\n    margin-left: 24.99% !important;\n  }\n  :host(.xgoffset--4) {\n    margin-left: 33.32% !important;\n  }\n  :host(.xgoffset--5) {\n    margin-left: 41.65% !important;\n  }\n  :host(.xgoffset--6) {\n    margin-left: 50% !important;\n  }\n  :host(.xgoffset--7) {\n    margin-left: 58.33% !important;\n  }\n  :host(.xgoffset--8) {\n    margin-left: 66.66% !important;\n  }\n  :host(.xgoffset--9) {\n    margin-left: 74.99% !important;\n  }\n  :host(.xgoffset--10) {\n    margin-left: 83.32% !important;\n  }\n  :host(.xgoffset--11) {\n    margin-left: 91.65% !important;\n  }\n  :host(.xgoffset--12) {\n    margin-left: 100% !important;\n  }\n}\n\n@media (min-width: 600px) {\n  :host(.container-fluid) {\n    max-width: 100%;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n@media (min-width: 905px) {\n  :host(.container-fluid) {\n    max-width: 848px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n@media (min-width: 993px) {\n  :host(.container-fluid) {\n    max-width: 944px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n@media (min-width: 1280px) {\n  :host(.container-fluid) {\n    max-width: 1232px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n@media (min-width: 1440px) {\n  :host(.container-fluid) {\n    max-width: 1328px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n@media (min-width: 1920px) {\n  :host(.container-fluid) {\n    max-width: 1424px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n}\n\n// Flex wrap\n:host(.flex_wrap--wrap) {\n  flex-wrap: wrap;\n}\n:host(.flex_wrap--wrap-reverse) {\n  flex-wrap: wrap-reverse;\n}\n\n//Flex direction\n:host(.direction--row) {\n  flex-direction: row;\n}\n:host(.direction--column) {\n  flex-direction: column;\n}\n:host(.direction--row-reverse) {\n  flex-direction: row-reverse;\n}\n:host(.direction--column-reverse) {\n  flex-direction: column-reverse;\n}\n\n//justify-content\n:host(.justify_content--center) {\n  justify-content: center;\n}\n:host(.justify_content--flex-start) {\n  justify-content: flex-start;\n}\n:host(.justify_content--flex-end) {\n  justify-content: flex-end;\n}\n:host(.justify_content--space-between) {\n  justify-content: space-between;\n}\n:host(.justify_content--space-around) {\n  justify-content: space-around;\n}\n:host(.justify_content--space-evenly) {\n  justify-content: space-evenly;\n}\n:host(.justify_content--stretch) {\n  justify-content: stretch;\n}\n\n// Align Items\n:host(.align_items--flex-start) {\n  align-items: flex-start;\n}\n:host(.align_items--flex-end) {\n  align-items: flex-end;\n}\n:host(.align_items--center) {\n  align-items: center;\n}\n:host(.align_items--stretch) {\n  align-items: stretch;\n}\n:host(.align_items--baseline) {\n  align-items: baseline;\n}\n\n// Gap\n:host(.gap--none) {\n  gap: 0;\n}\n:host(.gap--half) {\n  gap: 4px;\n}\n:host(.gap--1) {\n  gap: 8px;\n}\n:host(.gap--2) {\n  gap: 16px;\n}\n:host(.gap--3) {\n  gap: 24px;\n}\n:host(.gap--4) {\n  gap: 32px;\n}\n:host(.gap--5) {\n  gap: 40px;\n}\n:host(.gap--6) {\n  gap: 48px;\n}\n:host(.gap--7) {\n  gap: 56px;\n}\n:host(.gap--8) {\n  gap: 64px;\n}\n:host(.gap--9) {\n  gap: 72px;\n}\n:host(.gap--10) {\n  gap: 80px;\n}\n:host(.gap--11) {\n  gap: 88px;\n}\n:host(.gap--12) {\n  gap: 96px;\n}\n\n// Breakpoint xxs\n:host(.xxs--auto) {\n  flex: 1 0 auto;\n  width: auto;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--1) {\n  flex: 0 0 auto;\n  width: 8.33%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--2) {\n  flex: 0 0 auto;\n  width: 16.66%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--3) {\n  flex: 0 0 auto;\n  width: 24.99%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--4) {\n  flex: 0 0 auto;\n  width: 33.32%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--5) {\n  flex: 0 0 auto;\n  width: 41.65%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--6) {\n  flex: 0 0 auto;\n  width: 50%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--7) {\n  flex: 0 0 auto;\n  width: 58.33%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--8) {\n  flex: 0 0 auto;\n  width: 66.66%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--9) {\n  flex: 0 0 auto;\n  width: 74.99%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--10) {\n  flex: 0 0 auto;\n  width: 83.32%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--11) {\n  flex: 0 0 auto;\n  width: 91.65%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.xxs--12) {\n  flex: 0 0 auto;\n  width: 100%;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n\n// Breakpoint xs\n@media (min-width: 600px) {\n  :host(.xs--auto) {\n    flex: 1 0 auto;\n    width: auto;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--1) {\n    flex: 0 0 auto;\n    width: 8.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--2) {\n    flex: 0 0 auto;\n    width: 16.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--3) {\n    flex: 0 0 auto;\n    width: 24.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--4) {\n    flex: 0 0 auto;\n    width: 33.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--5) {\n    flex: 0 0 auto;\n    width: 41.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--6) {\n    flex: 0 0 auto;\n    width: 50%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--7) {\n    flex: 0 0 auto;\n    width: 58.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--8) {\n    flex: 0 0 auto;\n    width: 66.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--9) {\n    flex: 0 0 auto;\n    width: 74.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--10) {\n    flex: 0 0 auto;\n    width: 83.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--11) {\n    flex: 0 0 auto;\n    width: 91.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xs--12) {\n    flex: 0 0 auto;\n    width: 100%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n}\n\n// Breakpoint sm\n@media (min-width: 905px) {\n  :host(.sm--auto) {\n    flex: 1 0 auto;\n    width: auto;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--1) {\n    flex: 0 0 auto;\n    width: 8.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--2) {\n    flex: 0 0 auto;\n    width: 16.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--3) {\n    flex: 0 0 auto;\n    width: 24.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--4) {\n    flex: 0 0 auto;\n    width: 33.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--5) {\n    flex: 0 0 auto;\n    width: 41.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--6) {\n    flex: 0 0 auto;\n    width: 50%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--7) {\n    flex: 0 0 auto;\n    width: 58.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--8) {\n    flex: 0 0 auto;\n    width: 66.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--9) {\n    flex: 0 0 auto;\n    width: 74.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--10) {\n    flex: 0 0 auto;\n    width: 83.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--11) {\n    flex: 0 0 auto;\n    width: 91.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.sm--12) {\n    flex: 0 0 auto;\n    width: 100%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n}\n\n// Breakpoint md\n@media (min-width: 993px) {\n  :host(.md--auto) {\n    flex: 1 0 auto;\n    width: auto;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--1) {\n    flex: 0 0 auto;\n    width: 8.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--2) {\n    flex: 0 0 auto;\n    width: 16.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--3) {\n    flex: 0 0 auto;\n    width: 24.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--4) {\n    flex: 0 0 auto;\n    width: 33.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--5) {\n    flex: 0 0 auto;\n    width: 41.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--6) {\n    flex: 0 0 auto;\n    width: 50%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--7) {\n    flex: 0 0 auto;\n    width: 58.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--8) {\n    flex: 0 0 auto;\n    width: 66.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--9) {\n    flex: 0 0 auto;\n    width: 74.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--10) {\n    flex: 0 0 auto;\n    width: 83.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--11) {\n    flex: 0 0 auto;\n    width: 91.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.md--12) {\n    flex: 0 0 auto;\n    width: 100%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n}\n\n// Breakpoint lg\n@media (min-width: 1601px) {\n  :host(.lg--auto) {\n    flex: 1 0 auto;\n    width: auto;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--1) {\n    flex: 0 0 auto;\n    width: 8.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--2) {\n    flex: 0 0 auto;\n    width: 16.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--3) {\n    flex: 0 0 auto;\n    width: 24.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--4) {\n    flex: 0 0 auto;\n    width: 33.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--5) {\n    flex: 0 0 auto;\n    width: 41.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--6) {\n    flex: 0 0 auto;\n    width: 50%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--7) {\n    flex: 0 0 auto;\n    width: 58.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--8) {\n    flex: 0 0 auto;\n    width: 66.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--9) {\n    flex: 0 0 auto;\n    width: 74.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--10) {\n    flex: 0 0 auto;\n    width: 83.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--11) {\n    flex: 0 0 auto;\n    width: 91.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.lg--12) {\n    flex: 0 0 auto;\n    width: 100%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n}\n\n// Breakpoint xg\n@media (min-width: 1921px) {\n  :host(.xg--auto) {\n    flex: 1 0 auto;\n    width: auto;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--1) {\n    flex: 0 0 auto;\n    width: 8.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--2) {\n    flex: 0 0 auto;\n    width: 16.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--3) {\n    flex: 0 0 auto;\n    width: 24.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--4) {\n    flex: 0 0 auto;\n    width: 33.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--5) {\n    flex: 0 0 auto;\n    width: 41.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--6) {\n    flex: 0 0 auto;\n    width: 50%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--7) {\n    flex: 0 0 auto;\n    width: 58.33%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--8) {\n    flex: 0 0 auto;\n    width: 66.66%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--9) {\n    flex: 0 0 auto;\n    width: 74.99%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--10) {\n    flex: 0 0 auto;\n    width: 83.32%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--11) {\n    flex: 0 0 auto;\n    width: 91.65%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n  :host(.xg--12) {\n    flex: 0 0 auto;\n    width: 100%;\n    padding-left: 8px !important;\n    padding-right: 8px !important;\n  }\n}\n\n// Breakpoint lg in fluid\n@media (min-width: 1280px) {\n  :host(.lg--auto .container-fluid) {\n    flex: 1 0 auto;\n    width: auto;\n  }\n  :host(.lg--1.container-fluid) {\n    flex: 0 0 auto;\n    width: 8.33%;\n  }\n  :host(.lg--2.container-fluid) {\n    flex: 0 0 auto;\n    width: 16.66%;\n  }\n  :host(.lg--3.container-fluid) {\n    flex: 0 0 auto;\n    width: 24.99%;\n  }\n  :host(.lg--4.container-fluid) {\n    flex: 0 0 auto;\n    width: 33.32%;\n  }\n  :host(.lg--5.container-fluid) {\n    flex: 0 0 auto;\n    width: 41.65%;\n  }\n  :host(.lg--6.container-fluid) {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  :host(.lg--7.container-fluid) {\n    flex: 0 0 auto;\n    width: 58.33%;\n  }\n  :host(.lg--8.container-fluid) {\n    flex: 0 0 auto;\n    width: 66.66%;\n  }\n  :host(.lg--9.container-fluid) {\n    flex: 0 0 auto;\n    width: 74.99%;\n  }\n  :host(.lg--10.container-fluid) {\n    flex: 0 0 auto;\n    width: 83.32%;\n  }\n  :host(.lg--11.container-fluid) {\n    flex: 0 0 auto;\n    width: 91.65%;\n  }\n  :host(.lg--12.container-fluid) {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n}\n\n// Breakpoint xg in fluid\n@media (min-width: 1440px) {\n  :host(.xg--auto .container-fluid) {\n    flex: 1 0 auto;\n    width: auto;\n  }\n  :host(.xg--1.container-fluid) {\n    flex: 0 0 auto;\n    width: 8.33%;\n  }\n  :host(.xg--2.container-fluid) {\n    flex: 0 0 auto;\n    width: 16.66%;\n  }\n  :host(.xg--3.container-fluid) {\n    flex: 0 0 auto;\n    width: 24.99%;\n  }\n  :host(.xg--4.container-fluid) {\n    flex: 0 0 auto;\n    width: 33.32%;\n  }\n  :host(.xg--5.container-fluid) {\n    flex: 0 0 auto;\n    width: 41.65%;\n  }\n  :host(.xg--6.container-fluid) {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  :host(.xg--7.container-fluid) {\n    flex: 0 0 auto;\n    width: 58.33%;\n  }\n  :host(.xg--8.container-fluid) {\n    flex: 0 0 auto;\n    width: 66.66%;\n  }\n  :host(.xg--9.container-fluid) {\n    flex: 0 0 auto;\n    width: 74.99%;\n  }\n  :host(.xg--10.container-fluid) {\n    flex: 0 0 auto;\n    width: 83.32%;\n  }\n  :host(.xg--11.container-fluid) {\n    flex: 0 0 auto;\n    width: 91.65%;\n  }\n  :host(.xg--12.container-fluid) {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n}\n\n// Breakpoint xxg in fluid\n@media (min-width: 1920px) {\n  :host(.xxg--auto .container-fluid) {\n    flex: 1 0 auto;\n    width: auto;\n  }\n  :host(.xxg--1.container-fluid) {\n    flex: 0 0 auto;\n    width: 8.33%;\n  }\n  :host(.xxg--2.container-fluid) {\n    flex: 0 0 auto;\n    width: 16.66%;\n  }\n  :host(.xxg--3.container-fluid) {\n    flex: 0 0 auto;\n    width: 24.99%;\n  }\n  :host(.xxg--4.container-fluid) {\n    flex: 0 0 auto;\n    width: 33.32%;\n  }\n  :host(.xxg--5.container-fluid) {\n    flex: 0 0 auto;\n    width: 41.65%;\n  }\n  :host(.xxg--6.container-fluid) {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  :host(.xxg--7.container-fluid) {\n    flex: 0 0 auto;\n    width: 58.33%;\n  }\n  :host(.xxg--8.container-fluid) {\n    flex: 0 0 auto;\n    width: 66.66%;\n  }\n  :host(.xxg--9.container-fluid) {\n    flex: 0 0 auto;\n    width: 74.99%;\n  }\n  :host(.xxg--10.container-fluid) {\n    flex: 0 0 auto;\n    width: 83.32%;\n  }\n  :host(.xxg--11.container-fluid) {\n    flex: 0 0 auto;\n    width: 91.65%;\n  }\n  :host(.xxg--12.container-fluid) {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n}\n\n// Padding\n:host(.padding--none) {\n  padding: 0 !important;\n}\n:host(.padding--l-none) {\n  padding-left: 0 !important;\n}\n:host(.padding--r-none) {\n  padding-right: 0 !important;\n}\n:host(.padding--t-none) {\n  padding-top: 0 !important;\n}\n:host(.padding--b-none) {\n  padding-bottom: 0 !important;\n}\n:host(.padding--x-none) {\n  padding-left: 0 !important;\n  padding-right: 0 !important;\n}\n:host(.padding--y-none) {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n:host(.padding--half) {\n  padding: 4px !important;\n}\n:host(.padding--l-half) {\n  padding-left: 4px !important;\n}\n:host(.padding--r-half) {\n  padding-right: 4px !important;\n}\n:host(.padding--t-half) {\n  padding-top: 4px !important;\n}\n:host(.padding--b-half) {\n  padding-bottom: 4px !important;\n}\n:host(.padding--x-half) {\n  padding-left: 4px !important;\n  padding-right: 4px !important;\n}\n:host(.padding--y-half) {\n  padding-top: 4px !important;\n  padding-bottom: 4px !important;\n}\n\n:host(.padding--1) {\n  padding: 8px !important;\n}\n:host(.padding--l-1) {\n  padding-left: 8px !important;\n}\n:host(.padding--r-1) {\n  padding-right: 8px !important;\n}\n:host(.padding--t-1) {\n  padding-top: 8px !important;\n}\n:host(.padding--b-1) {\n  padding-bottom: 8px !important;\n}\n:host(.padding--x-1) {\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n:host(.padding--y-1) {\n  padding-top: 8px !important;\n  padding-bottom: 8px !important;\n}\n\n:host(.padding--2) {\n  padding: 16px !important;\n}\n:host(.padding--l-2) {\n  padding-left: 16px !important;\n}\n:host(.padding--r-2) {\n  padding-right: 16px !important;\n}\n:host(.padding--t-2) {\n  padding-top: 16px !important;\n}\n:host(.padding--b-2) {\n  padding-bottom: 16px !important;\n}\n:host(.padding--x-2) {\n  padding-left: 16px !important;\n  padding-right: 16px !important;\n}\n:host(.padding--y-2) {\n  padding-top: 16px !important;\n  padding-bottom: 16px !important;\n}\n\n:host(.padding--3) {\n  padding: 24px !important;\n}\n:host(.padding--l-3) {\n  padding-left: 24px !important;\n}\n:host(.padding--r-3) {\n  padding-right: 24px !important;\n}\n:host(.padding--t-3) {\n  padding-top: 24px !important;\n}\n:host(.padding--b-3) {\n  padding-bottom: 24px !important;\n}\n:host(.padding--x-3) {\n  padding-left: 24px !important;\n  padding-right: 24px !important;\n}\n:host(.padding--y-3) {\n  padding-top: 24px !important;\n  padding-bottom: 24px !important;\n}\n\n:host(.padding--4) {\n  padding: 32px !important;\n}\n:host(.padding--l-4) {\n  padding-left: 32px !important;\n}\n:host(.padding--r-4) {\n  padding-right: 32px !important;\n}\n:host(.padding--t-4) {\n  padding-top: 32px !important;\n}\n:host(.padding--b-4) {\n  padding-bottom: 32px !important;\n}\n:host(.padding--x-4) {\n  padding-left: 32px !important;\n  padding-right: 32px !important;\n}\n:host(.padding--y-4) {\n  padding-top: 32px !important;\n  padding-bottom: 32px !important;\n}\n:host(.padding--5) {\n  padding: 40px !important;\n}\n:host(.padding--l-5) {\n  padding-left: 40px !important;\n}\n:host(.padding--r-5) {\n  padding-right: 40px !important;\n}\n:host(.padding--t-5) {\n  padding-top: 40px !important;\n}\n:host(.padding--b-5) {\n  padding-bottom: 40px !important;\n}\n:host(.padding--x-5) {\n  padding-left: 40px !important;\n  padding-right: 40px !important;\n}\n:host(.padding--y-5) {\n  padding-top: 40px !important;\n  padding-bottom: 40px !important;\n}\n\n:host(.padding--6) {\n  padding: 48px !important;\n}\n:host(.padding--l-6) {\n  padding-left: 48px !important;\n}\n:host(.padding--r-6) {\n  padding-right: 48px !important;\n}\n:host(.padding--t-6) {\n  padding-top: 48px !important;\n}\n:host(.padding--b-6) {\n  padding-bottom: 48px !important;\n}\n:host(.padding--x-6) {\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n}\n:host(.padding--y-6) {\n  padding-top: 48px !important;\n  padding-bottom: 48px !important;\n}\n\n:host(.padding--7) {\n  padding: 56px !important;\n}\n:host(.padding--l-7) {\n  padding-left: 56px !important;\n}\n:host(.padding--r-7) {\n  padding-right: 56px !important;\n}\n:host(.padding--t-7) {\n  padding-top: 56px !important;\n}\n:host(.padding--b-7) {\n  padding-bottom: 56px !important;\n}\n:host(.padding--x-7) {\n  padding-left: 56px !important;\n  padding-right: 56px !important;\n}\n:host(.padding--y-7) {\n  padding-top: 56px !important;\n  padding-bottom: 56px !important;\n}\n\n:host(.padding--8) {\n  padding: 64px !important;\n}\n:host(.padding--l-8) {\n  padding-left: 64px !important;\n}\n:host(.padding--r-8) {\n  padding-right: 64px !important;\n}\n:host(.padding--t-8) {\n  padding-top: 64px !important;\n}\n:host(.padding--b-8) {\n  padding-bottom: 64px !important;\n}\n:host(.padding--x-8) {\n  padding-left: 64px !important;\n  padding-right: 64px !important;\n}\n:host(.padding--y-8) {\n  padding-top: 64px !important;\n  padding-bottom: 64px !important;\n}\n\n:host(.padding--9) {\n  padding: 72px !important;\n}\n:host(.padding--l-9) {\n  padding-left: 72px !important;\n}\n:host(.padding--r-9) {\n  padding-right: 72px !important;\n}\n:host(.padding--t-9) {\n  padding-top: 72px !important;\n}\n:host(.padding--b-9) {\n  padding-bottom: 72px !important;\n}\n:host(.padding--x-9) {\n  padding-left: 72px !important;\n  padding-right: 72px !important;\n}\n:host(.padding--y-9) {\n  padding-top: 72px !important;\n  padding-bottom: 72px !important;\n}\n\n:host(.padding--10) {\n  padding: 80px !important;\n}\n:host(.padding--l-10) {\n  padding-left: 80px !important;\n}\n:host(.padding--r-10) {\n  padding-right: 80px !important;\n}\n:host(.padding--t-10) {\n  padding-top: 80px !important;\n}\n:host(.padding--b-10) {\n  padding-bottom: 80px !important;\n}\n:host(.padding--x-10) {\n  padding-left: 80px !important;\n  padding-right: 80px !important;\n}\n:host(.padding--y-10) {\n  padding-top: 80px !important;\n  padding-bottom: 80px !important;\n}\n\n:host(.padding--11) {\n  padding: 88px !important;\n}\n:host(.padding--l-11) {\n  padding-left: 88px !important;\n}\n:host(.padding--r-11) {\n  padding-right: 88px !important;\n}\n:host(.padding--t-11) {\n  padding-top: 88px !important;\n}\n:host(.padding--b-11) {\n  padding-bottom: 88px !important;\n}\n:host(.padding--x-11) {\n  padding-left: 88px !important;\n  padding-right: 88px !important;\n}\n:host(.padding--y-11) {\n  padding-top: 88px !important;\n  padding-bottom: 88px !important;\n}\n\n:host(.padding--12) {\n  padding: 96px !important;\n}\n:host(.padding--l-12) {\n  padding-left: 96px !important;\n}\n:host(.padding--r-12) {\n  padding-right: 96px !important;\n}\n:host(.padding--t-12) {\n  padding-top: 96px !important;\n}\n:host(.padding--b-12) {\n  padding-bottom: 96px !important;\n}\n:host(.padding--x-12) {\n  padding-left: 96px !important;\n  padding-right: 96px !important;\n}\n:host(.padding--y-12) {\n  padding-top: 96px !important;\n  padding-bottom: 96px !important;\n}\n\n// Margin\n:host(.margin--auto) {\n  margin: auto !important;\n}\n:host(.margin--l-auto) {\n  margin-left: auto !important;\n}\n:host(.margin--r-auto) {\n  margin-right: auto !important;\n}\n:host(.margin--t-auto) {\n  margin-top: auto !important;\n}\n:host(.margin--b-auto) {\n  margin-bottom: auto !important;\n}\n:host(.margin--x-auto) {\n  margin-left: auto !important;\n  margin-right: auto !important;\n}\n:host(.margin--y-auto) {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n:host(.margin--none) {\n  margin: 0 !important;\n}\n:host(.margin--l-none) {\n  margin-left: 0 !important;\n}\n:host(.margin--r-none) {\n  margin-right: 0 !important;\n}\n:host(.margin--t-none) {\n  margin-top: 0 !important;\n}\n:host(.margin--b-none) {\n  margin-bottom: 0 !important;\n}\n:host(.margin--x-none) {\n  margin-left: 0 !important;\n  margin-right: 0 !important;\n}\n:host(.margin--y-none) {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n:host(.margin--half) {\n  margin: 4px !important;\n}\n:host(.margin--l-half) {\n  margin-left: 4px !important;\n}\n:host(.margin--r-half) {\n  margin-right: 4px !important;\n}\n:host(.margin--t-half) {\n  margin-top: 4px !important;\n}\n:host(.margin--b-half) {\n  margin-bottom: 4px !important;\n}\n:host(.margin--x-half) {\n  margin-left: 4px !important;\n  margin-right: 4px !important;\n}\n:host(.margin--y-half) {\n  margin-top: 4px !important;\n  margin-bottom: 4px !important;\n}\n\n:host(.margin--1) {\n  margin: 8px !important;\n}\n:host(.margin--l-1) {\n  margin-left: 8px !important;\n}\n:host(.margin--r-1) {\n  margin-right: 8px !important;\n}\n:host(.margin--t-1) {\n  margin-top: 8px !important;\n}\n:host(.margin--b-1) {\n  margin-bottom: 8px !important;\n}\n:host(.margin--x-1) {\n  margin-left: 8px !important;\n  margin-right: 8px !important;\n}\n:host(.margin--y-1) {\n  margin-top: 8px !important;\n  margin-bottom: 8px !important;\n}\n\n:host(.margin--2) {\n  margin: 16px !important;\n}\n:host(.margin--l-2) {\n  margin-left: 16px !important;\n}\n:host(.margin--r-2) {\n  margin-right: 16px !important;\n}\n:host(.margin--t-2) {\n  margin-top: 16px !important;\n}\n:host(.margin--b-2) {\n  margin-bottom: 16px !important;\n}\n:host(.margin--x-2) {\n  margin-left: 16px !important;\n  margin-right: 16px !important;\n}\n:host(.margin--y-2) {\n  margin-top: 16px !important;\n  margin-bottom: 16px !important;\n}\n\n:host(.margin--3) {\n  margin: 24px !important;\n}\n:host(.margin--l-3) {\n  margin-left: 24px !important;\n}\n:host(.margin--r-3) {\n  margin-right: 24px !important;\n}\n:host(.margin--t-3) {\n  margin-top: 24px !important;\n}\n:host(.margin--b-3) {\n  margin-bottom: 24px !important;\n}\n:host(.margin--x-3) {\n  margin-left: 24px !important;\n  margin-right: 24px !important;\n}\n:host(.margin--y-3) {\n  margin-top: 24px !important;\n  margin-bottom: 24px !important;\n}\n\n:host(.margin--4) {\n  margin: 32px !important;\n}\n:host(.margin--l-4) {\n  margin-left: 32px !important;\n}\n:host(.margin--r-4) {\n  margin-right: 32px !important;\n}\n:host(.margin--t-4) {\n  margin-top: 32px !important;\n}\n:host(.margin--b-4) {\n  margin-bottom: 32px !important;\n}\n:host(.margin--x-4) {\n  margin-left: 32px !important;\n  margin-right: 32px !important;\n}\n:host(.margin--y-4) {\n  margin-top: 32px !important;\n  margin-bottom: 32px !important;\n}\n\n:host(.margin--5) {\n  margin: 40px !important;\n}\n:host(.margin--l-5) {\n  margin-left: 40px !important;\n}\n:host(.margin--r-5) {\n  margin-right: 40px !important;\n}\n:host(.margin--t-5) {\n  margin-top: 40px !important;\n}\n:host(.margin--b-5) {\n  margin-bottom: 40px !important;\n}\n:host(.margin--x-5) {\n  margin-left: 40px !important;\n  margin-right: 40px !important;\n}\n:host(.margin--y-5) {\n  margin-top: 40px !important;\n  margin-bottom: 40px !important;\n}\n\n:host(.margin--6) {\n  margin: 48px !important;\n}\n:host(.margin--l-6) {\n  margin-left: 48px !important;\n}\n:host(.margin--r-6) {\n  margin-right: 48px !important;\n}\n:host(.margin--t-6) {\n  margin-top: 48px !important;\n}\n:host(.margin--b-6) {\n  margin-bottom: 48px !important;\n}\n:host(.margin--x-6) {\n  margin-left: 48px !important;\n  margin-right: 48px !important;\n}\n:host(.margin--y-6) {\n  margin-top: 48px !important;\n  margin-bottom: 48px !important;\n}\n\n:host(.margin--7) {\n  margin: 56px !important;\n}\n:host(.margin--l-7) {\n  margin-left: 56px !important;\n}\n:host(.margin--r-7) {\n  margin-right: 56px !important;\n}\n:host(.margin--t-7) {\n  margin-top: 56px !important;\n}\n:host(.margin--b-7) {\n  margin-bottom: 56px !important;\n}\n:host(.margin--x-7) {\n  margin-left: 56px !important;\n  margin-right: 56px !important;\n}\n:host(.margin--y-7) {\n  margin-top: 56px !important;\n  margin-bottom: 56px !important;\n}\n\n:host(.margin--8) {\n  margin: 64px !important;\n}\n:host(.margin--l-8) {\n  margin-left: 64px !important;\n}\n:host(.margin--r-8) {\n  margin-right: 64px !important;\n}\n:host(.margin--t-8) {\n  margin-top: 64px !important;\n}\n:host(.margin--b-8) {\n  margin-bottom: 64px !important;\n}\n:host(.margin--x-8) {\n  margin-left: 64px !important;\n  margin-right: 64px !important;\n}\n:host(.margin--y-8) {\n  margin-top: 64px !important;\n  margin-bottom: 64px !important;\n}\n\n:host(.margin--9) {\n  margin: 72px !important;\n}\n:host(.margin--l-9) {\n  margin-left: 72px !important;\n}\n:host(.margin--r-9) {\n  margin-right: 72px !important;\n}\n:host(.margin--t-9) {\n  margin-top: 72px !important;\n}\n:host(.margin--b-9) {\n  margin-bottom: 72px !important;\n}\n:host(.margin--x-9) {\n  margin-left: 72px !important;\n  margin-right: 72px !important;\n}\n:host(.margin--y-9) {\n  margin-top: 72px !important;\n  margin-bottom: 72px !important;\n}\n\n:host(.margin--10) {\n  margin: 80px !important;\n}\n:host(.margin--l-10) {\n  margin-left: 80px !important;\n}\n:host(.margin--r-10) {\n  margin-right: 80px !important;\n}\n:host(.margin--t-10) {\n  margin-top: 80px !important;\n}\n:host(.margin--b-10) {\n  margin-bottom: 80px !important;\n}\n:host(.margin--x-10) {\n  margin-left: 80px !important;\n  margin-right: 80px !important;\n}\n:host(.margin--y-10) {\n  margin-top: 80px !important;\n  margin-bottom: 80px !important;\n}\n\n:host(.margin--11) {\n  margin: 88px !important;\n}\n:host(.margin--l-11) {\n  margin-left: 88px !important;\n}\n:host(.margin--r-11) {\n  margin-right: 88px !important;\n}\n:host(.margin--t-11) {\n  margin-top: 88px !important;\n}\n:host(.margin--b-11) {\n  margin-bottom: 88px !important;\n}\n:host(.margin--x-11) {\n  margin-left: 88px !important;\n  margin-right: 88px !important;\n}\n:host(.margin--y-11) {\n  margin-top: 88px !important;\n  margin-bottom: 88px !important;\n}\n\n:host(.margin--12) {\n  margin: 96px !important;\n}\n:host(.margin--l-12) {\n  margin-left: 96px !important;\n}\n:host(.margin--r-12) {\n  margin-right: 96px !important;\n}\n:host(.margin--t-12) {\n  margin-top: 96px !important;\n}\n:host(.margin--b-12) {\n  margin-bottom: 96px !important;\n}\n:host(.margin--x-12) {\n  margin-left: 96px !important;\n  margin-right: 96px !important;\n}\n:host(.margin--y-12) {\n  margin-top: 96px !important;\n  margin-bottom: 96px !important;\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\nimport { direction, justifyContent, flexWrap, alignItems, breakpoint, gap, padding, margin } from './grid-interface';\nimport { Color } from './color-grid-interface';\n@Component({\n  tag: 'bds-grid',\n  styleUrl: 'grid.scss',\n  shadow: true,\n})\nexport class Grid {\n  @Prop() height?: string;\n  @Prop() direction?: direction;\n  @Prop() justifyContent?: justifyContent;\n  @Prop() flexWrap?: flexWrap;\n  @Prop() alignItems?: alignItems;\n  @Prop() container?: boolean;\n  @Prop() containerFluid?: boolean;\n  @Prop() xxs?: breakpoint;\n  @Prop() xs?: breakpoint;\n  @Prop() sm?: breakpoint;\n  @Prop() md?: breakpoint;\n  @Prop() lg?: breakpoint;\n  @Prop() xg?: breakpoint;\n  @Prop() xxsOffset?: breakpoint;\n  @Prop() xsOffset?: breakpoint;\n  @Prop() smOffset?: breakpoint;\n  @Prop() mdOffset?: breakpoint;\n  @Prop() lgOffset?: breakpoint;\n  @Prop() xgOffset?: breakpoint;\n  @Prop() gap?: gap;\n  @Prop() padding?: padding;\n  @Prop() margin?: margin;\n  @Prop() bgColor?: Color;\n  render() {\n    return (\n      <Host\n        class={{\n          host: true,\n          [`direction--${this.direction}`]: true,\n          [`justify_content--${this.justifyContent}`]: true,\n          [`${this.container === true ? 'container' : ''}`]: true,\n          [`${this.containerFluid === true ? 'container-fluid' : ''}`]: true,\n          [`flex_wrap--${this.flexWrap}`]: true,\n          [`align_items--${this.alignItems}`]: true,\n          [`xxs--${this.xxs}`]: true,\n          [`xs--${this.xs}`]: true,\n          [`sm--${this.sm}`]: true,\n          [`md--${this.md}`]: true,\n          [`lg--${this.lg}`]: true,\n          [`xg--${this.xg}`]: true,\n          [`gap--${this.gap}`]: true,\n          [`xxsoffset--${this.xxsOffset}`]: true,\n          [`xsoffset--${this.xsOffset}`]: true,\n          [`smoffset--${this.smOffset}`]: true,\n          [`mdoffset--${this.mdOffset}`]: true,\n          [`lgoffset--${this.lgOffset}`]: true,\n          [`xgoffset--${this.xgOffset}`]: true,\n          [`padding--${this.padding}`]: true,\n          [`margin--${this.margin}`]: true,\n          [this.bgColor || '']: true,\n        }}\n        style={{ height: this.height  }}\n      >\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n", "@use '../../global-style' as *;\n@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  border-radius: 16px;\n}\n\n:host(.border) {\n  border: 1px solid $color-border-1;\n  margin: -1px;\n}\n\n:host(.bg-surface-0) {\n  background-color: $color-surface-0;\n}\n\n:host(.bg-surface-1) {\n  background-color: $color-surface-1;\n}\n\n:host(.bg-surface-2) {\n  background-color: $color-surface-2;\n}\n\n:host(.bg-surface-3) {\n  background-color: $color-surface-3;\n}\n\n:host(.bg-surface-4) {\n  background-color: $color-surface-4;\n}\n\n:host(.border-1) {\n  border-color: $color-border-1;\n}\n\n:host(.border-2) {\n  border-color: $color-border-2;\n}\n\n:host(.border-3) {\n  border-color: $color-border-3;\n}\n\n:host(.border-primary) {\n  border-color: $color-primary;\n}\n\n:host(.border-secondary) {\n  border-color: $color-secondary;\n}\n:host(.border-positive) {\n  border-color: $color-positive;\n}\n:host(.border-negative) {\n  border-color: $color-negative;\n}\n:host(.border-warning) {\n  border-color: $color-warning;\n}\n:host(.border-error) {\n  border-color: $color-error;\n}\n:host(.border-success) {\n  border-color: $color-success;\n}\n:host(.border-delete) {\n  border-color: $color-delete;\n}\n\n:host(.paper__elevation--none) {\n  box-shadow: none;\n}\n\n:host(.paper__elevation--static) {\n  box-shadow: $shadow-1;\n}\n\n:host(.paper__elevation--primary) {\n  box-shadow: $shadow-2;\n}\n\n:host(.paper__elevation--secondary) {\n  box-shadow: $shadow-3;\n}\n\n.paper__display {\n  display: contents;\n}\n", "import { Component, ComponentInterface, Host, h, Prop, State } from '@stencil/core';\nimport { PaperElevation, PaperBackground, BorderColor } from './paper-interface';\n\n@Component({\n  tag: 'bds-paper',\n  styleUrl: 'paper.scss',\n  shadow: true,\n})\nexport class Paper implements ComponentInterface {\n  @State() hasBorder = true;\n  @State() constElevation: string;\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'static', 'primary', 'secondary';\n   */\n  @Prop({ mutable: true, reflect: true }) elevation?: PaperElevation = 'static';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Prop for set the border of the component.\n   */\n  @Prop() border?: boolean = false;\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string = null;\n\n  /**\n   * Prop for set the width of the component.\n   */\n  @Prop() width?: string = null;\n\n  /**\n   * Prop for set the background color.\n   */\n  @Prop() bgColor?: PaperBackground = 'surface-1';\n\n  /**\n   * Prop for set the border color.\n   */\n  @Prop() borderColor?: BorderColor = null;\n\n  componentWillLoad() {\n    this.border === true ? (this.hasBorder = false) : (this.hasBorder = true);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`paper__elevation--${this.elevation}`]: this.hasBorder,\n          border: this.border,\n          [`bg-${this.bgColor}`]: true,\n          [`border-${this.borderColor}`]: true,\n        }}\n        style={{ height: `${this.height}`, width: `${this.width}` }}\n      >\n        <div class=\"paper__display\" data-test={this.dataTest}>\n          <slot></slot>\n        </div>\n      </Host>\n    );\n  }\n}\n", null, "import { Component, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-test-component',\n  styleUrl: 'test-component.scss',\n})\nexport class TestComponent {\n  render() {\n    return (\n      <bds-grid xxs=\"12\" padding=\"x-2\" flex-wrap=\"wrap\">\n        <bds-grid xxs=\"12\" margin=\"t-2\">\n          <div class=\"titulo\">\n            <bds-typo variant=\"fs-40\" bold=\"bold\">\n              Titulo de teste fora de temas\n            </bds-typo>\n          </div>\n        </bds-grid>\n        <bds-grid xxs=\"6\" padding=\"r-1\">\n          <bds-theme-provider theme=\"light\">\n            <bds-paper elevation=\"none\" border>\n              <bds-grid padding=\"2\">{/* Inserir Componente aqui! */}</bds-grid>\n            </bds-paper>\n          </bds-theme-provider>\n        </bds-grid>\n        <bds-grid xxs=\"6\" padding=\"l-1\">\n          <bds-theme-provider theme=\"dark\">\n            <bds-paper elevation=\"none\" border>\n              <bds-grid padding=\"2\">{/* Inserir Componente aqui! */}</bds-grid>\n            </bds-paper>\n          </bds-theme-provider>\n        </bds-grid>\n      </bds-grid>\n    );\n  }\n}\n", "@use '../../globals/theme/theme-light.scss' as *;\n@use '../../globals/theme/theme-dark.scss' as *;\n@use '../../globals/colors' as *;\n\n:host(.theme--light) {\n  width: 100%;\n  height: 100%;\n\n  --color-brand: #{$color-light-brand};\n  --color-primary: #{$color-light-primary};\n  --color-secondary: #{$color-light-secondary};\n  --color-surface-0: #{$color-light-surface-0};\n  --color-surface-1: #{$color-light-surface-1};\n  --color-surface-2: #{$color-light-surface-2};\n  --color-surface-3: #{$color-light-surface-3};\n  --color-surface-4: #{$color-light-surface-4};\n  --color-surface-positive: #{$color-light-surface-positive};\n  --color-surface-negative: #{$color-light-surface-negative};\n  --color-surface-primary: #{$color-light-surface-primary};\n  --color-content-default: #{$color-light-content-default};\n  --color-content-disable: #{$color-light-content-disable};\n  --color-content-ghost: #{$color-light-content-ghost};\n  --color-content-bright: #{$color-light-content-bright};\n  --color-content-din: #{$color-light-content-din};\n  --color-border-1: #{$color-light-border-1};\n  --color-border-2: #{$color-light-border-2};\n  --color-border-3: #{$color-light-border-3};\n  --color-positive: #{$color-light-positive};\n  --color-negative: #{$color-light-negative};\n  --color-info: #{$color-light-info};\n  --color-system: #{$color-light-system};\n  --color-focus: #{$color-light-focus};\n  --color-success: #{$color-light-success};\n  --color-warning: #{$color-light-warning};\n  --color-error: #{$color-light-error};\n  --color-delete: #{$color-light-delete};\n  --color-extended-blue: #{$color-light-extended-blue};\n  --color-extended-blue-bright: #{$color-light-extended-blue-bright};\n  --color-extended-ocean: #{$color-light-extended-ocean};\n  --color-extended-ocean-bright: #{$color-light-extended-ocean-bright};\n  --color-extended-green: #{$color-light-extended-green};\n  --color-extended-green-bright: #{$color-light-extended-green-bright};\n  --color-extended-yellow: #{$color-light-extended-yellow};\n  --color-extended-yellow-bright: #{$color-light-extended-yellow-bright};\n  --color-extended-orange: #{$color-light-extended-orange};\n  --color-extended-orange-bright: #{$color-light-extended-orange-bright};\n  --color-extended-red: #{$color-light-extended-red};\n  --color-extended-red-bright: #{$color-light-extended-red-bright};\n  --color-extended-pink: #{$color-light-extended-pink};\n  --color-extended-pink-bright: #{$color-light-extended-pink-bright};\n  --color-extended-gray: #{$color-light-extended-gray};\n  --color-extended-gray-bright: #{$color-light-extended-gray-bright};\n  --color-hover: #{$color-light-hover};\n  --color-pressed: #{$color-light-pressed};\n  --color-shadow-0: rgba(0, 0, 0, 0.04);\n  --color-shadow-1: rgba(0, 0, 0, 0.16);\n}\n\n:host(.theme--dark) {\n  width: 100%;\n  height: 100%;\n\n  --color-brand: #{$color-dark-brand};\n  --color-primary: #{$color-dark-primary};\n  --color-secondary: #{$color-dark-secondary};\n  --color-surface-0: #{$color-dark-surface-0};\n  --color-surface-1: #{$color-dark-surface-1};\n  --color-surface-2: #{$color-dark-surface-2};\n  --color-surface-3: #{$color-dark-surface-3};\n  --color-surface-4: #{$color-dark-surface-4};\n  --color-surface-positive: #{$color-dark-surface-positive};\n  --color-surface-negative: #{$color-dark-surface-negative};\n  --color-surface-primary: #{$color-dark-surface-primary};\n  --color-content-default: #{$color-dark-content-default};\n  --color-content-disable: #{$color-dark-content-disable};\n  --color-content-ghost: #{$color-dark-content-ghost};\n  --color-content-bright: #{$color-dark-content-bright};\n  --color-content-din: #{$color-dark-content-din};\n  --color-border-1: #{$color-dark-border-1};\n  --color-border-2: #{$color-dark-border-2};\n  --color-border-3: #{$color-dark-border-3};\n  --color-positive: #{$color-dark-positive};\n  --color-negative: #{$color-dark-negative};\n  --color-info: #{$color-dark-info};\n  --color-system: #{$color-dark-system};\n  --color-focus: #{$color-dark-focus};\n  --color-success: #{$color-dark-success};\n  --color-warning: #{$color-dark-warning};\n  --color-error: #{$color-dark-error};\n  --color-delete: #{$color-dark-delete};\n  --color-extended-blue: #{$color-dark-extended-blue};\n  --color-extended-blue-bright: #{$color-dark-extended-blue-bright};\n  --color-extended-ocean: #{$color-dark-extended-ocean};\n  --color-extended-ocean-bright: #{$color-dark-extended-ocean-bright};\n  --color-extended-green: #{$color-dark-extended-green};\n  --color-extended-green-bright: #{$color-dark-extended-green-bright};\n  --color-extended-yellow: #{$color-dark-extended-yellow};\n  --color-extended-yellow-bright: #{$color-dark-extended-yellow-bright};\n  --color-extended-orange: #{$color-dark-extended-orange};\n  --color-extended-orange-bright: #{$color-dark-extended-orange-bright};\n  --color-extended-red: #{$color-dark-extended-red};\n  --color-extended-red-bright: #{$color-dark-extended-red-bright};\n  --color-extended-pink: #{$color-dark-extended-pink};\n  --color-extended-pink-bright: #{$color-dark-extended-pink-bright};\n  --color-extended-gray: #{$color-dark-extended-gray};\n  --color-extended-gray-bright: #{$color-dark-extended-gray-bright};\n  --color-hover: #{$color-dark-hover};\n  --color-pressed: #{$color-dark-pressed};\n  --color-shadow-0: #{$color-shadow-0};\n  --color-shadow-1: #{$color-shadow-1};\n}\n\n:host(.theme--high-contrast) {\n  width: 100%;\n  height: 100%;\n\n  --color-brand: #0096fa;\n  --color-primary: #1e6bf1;\n  --color-secondary: #292929;\n  --color-surface-1: #ffffff;\n  --color-surface-2: #f5f5f5;\n  --color-surface-3: #e0e0e0;\n  --color-surface-4: #141414;\n  --color-content-default: #292929;\n  --color-content-disable: #666666;\n  --color-content-ghost: #949494;\n  --color-content-bright: #ffffff;\n  --color-content-din: #000000;\n  --color-border-1: #616161;\n  --color-info: #80e3eb;\n  --color-system: #99d5fd;\n  --color-focus: #c226fb;\n  --color-success: #84ebbc;\n  --color-warning: #fde99b;\n  --color-error: #f99f9f;\n  --color-delete: #e60f0f;\n  --color-extended-blue: #1968f0;\n  --color-extended-ocean: #00d3e4;\n  --color-extended-green: #35de90;\n  --color-extended-yellow: #fbcf23;\n  --color-extended-orange: #f06305;\n  --color-extended-red: #e60f0f;\n  --color-extended-pink: #fb4bc1;\n  --color-extended-gray: #666666;\n  --color-hover: rgba(0, 0, 0, 0.08);\n  --color-pressed: rgba(0, 0, 0, 0.16);\n  --color-shadow-1: rgba(0, 0, 0, 0.16);\n  --color-positive: #10603b;\n  --color-negative: #e60f0f;\n}\n", "import { Component, Host, h, Prop } from '@stencil/core';\n\nexport type Themes = 'light' | 'dark' | 'high-contrast';\n\n@Component({\n  tag: 'bds-theme-provider',\n  styleUrl: 'theme-provider.scss',\n  shadow: true,\n})\nexport class ThemeProvider {\n  /**\n   * Set what theme will be aplyed inside the component.\n   * 'light', 'dark';\n   */\n  @Prop() theme?: Themes = 'light';\n\n  render() {\n    return (\n      <Host class={{ theme: true, [`theme--${this.theme}`]: true }}>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n:host {\n  color: $color-content-default;\n}\n\n.typo {\n  margin: 0;\n  font-family: $font-family;\n  font-style: normal;\n  font-weight: normal;\n  margin: 0;\n  margin-block-start: 0;\n  margin-block-end: 0;\n  margin-inline-start: 0;\n  margin-inline-end: 0;\n  padding: 0;\n  border: 0;\n\n  &--italic {\n    font-style: italic;\n  }\n\n  &--no-wrap {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  &--paragraph {\n    margin-bottom: $paragraph-margin;\n  }\n\n  &__variant {\n    &--fs-10 {\n      font-size: $fs-10;\n      line-height: $line-hight-plus;\n    }\n\n    &--fs-12 {\n      font-size: $fs-12;\n      line-height: $line-hight-plus;\n    }\n\n    &--fs-14 {\n      font-size: $fs-14;\n      line-height: $line-hight-plus;\n    }\n\n    &--fs-16 {\n      font-size: $fs-16;\n      line-height: $line-hight-plus;\n    }\n\n    &--fs-20 {\n      font-size: $fs-20;\n      line-height: $line-hight-simple;\n    }\n\n    &--fs-24 {\n      font-size: $fs-24;\n      line-height: $line-hight-simple;\n    }\n\n    &--fs-32 {\n      font-size: $fs-32;\n      line-height: $line-hight-simple;\n    }\n\n    &--fs-40 {\n      font-size: $fs-40;\n      line-height: $line-hight-simple;\n    }\n  }\n\n  &__margin {\n    &--fs-20 {\n      margin-bottom: $title-margin-large;\n    }\n\n    &--fs-24 {\n      margin-bottom: $title-margin-large;\n    }\n\n    &--fs-32 {\n      margin-bottom: $title-margin-large;\n    }\n\n    &--fs-40 {\n      margin-bottom: $title-margin-medium;\n    }\n  }\n\n  &__line-height {\n    &--none {\n      line-height: $line-hight-none;\n    }\n\n    &--small {\n      line-height: $line-hight-small;\n    }\n\n    &--simple {\n      line-height: $line-hight-simple;\n    }\n\n    &--plus {\n      line-height: $line-hight-plus;\n    }\n\n    &--double {\n      line-height: $line-hight-double;\n    }\n  }\n\n  &__bold {\n    &--regular {\n      font-weight: $font-weight-regular;\n    }\n\n    &--semi-bold {\n      font-weight: $font-weight-semi-bold;\n    }\n\n    &--bold {\n      font-weight: $font-weight-bold;\n    }\n\n    &--extra-bold {\n      font-weight: $font-weight-extra-bold;\n    }\n  }\n}\n", "import { Component, h, Prop } from '@stencil/core';\n\nexport type FontSize = 'fs-10' | 'fs-12' | 'fs-14' | 'fs-16' | 'fs-20' | 'fs-24' | 'fs-32' | 'fs-40';\n\nexport type FontLineHeight = 'none' | 'small' | 'simple' | 'plus' | 'double';\n\nexport type Bold = 'regular' | 'semi-bold' | 'bold' | 'extra-bold';\n\nexport type Tag = 'p' | 'h1' | 'h2' | 'h3' | 'h4' | 'span';\n\n@Component({\n  tag: 'bds-typo',\n  styleUrl: 'typo.scss',\n  shadow: true,\n})\nexport class Typo {\n  /**\n   * Variant. Entered as one of the font size variant. Can be one of:\n   * 'fs-10' ,'fs-12' ,'fs-14' ,'fs-16' ,'fs-20' ,'fs-24' ,'fs-32' ,'fs-40';\n   */\n  @Prop() variant?: FontSize = 'fs-16';\n\n  /**\n   * Line Height. Entered as one of the line hieght. Can be one of:\n   * 'none', 'small', 'simple', 'plus', 'double'\n   */\n  @Prop() lineHeight?: FontLineHeight = null;\n\n  /**\n   * Bold. Entered as one of the bold. Can be one of:\n   * 'regular', 'semi-bold', 'bold', 'extra-bold';\n   */\n  @Prop() bold?: Bold = null;\n\n  /**\n   * Added font style italic\n   */\n  @Prop() italic?: boolean = false;\n\n  /**\n   * Added style no wrap\n   */\n  @Prop() noWrap?: boolean = false;\n\n  /**\n   * Tranform text in paragraph\n   */\n  @Prop() paragraph?: boolean = false;\n\n  /**\n   * If true, adds default margin values\n   */\n  @Prop() margin?: boolean = true;\n\n  /**\n   * Define element tag, must be used for acessibilty\n   */\n  @Prop() tag?: Tag = 'p';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render(): HTMLElement {\n    const Element = this.tag;\n\n    return (\n      <Element\n        class={{\n          typo: true,\n          [`typo__variant--${this.variant}`]: true,\n          [`typo__margin--${this.variant}`]: this.margin,\n          'typo--no-wrap': this.noWrap,\n          'typo--paragraph': this.paragraph,\n          'typo--italic': this.italic,\n          [`typo__line-height--${this.lineHeight}`]: !!this.lineHeight,\n          [`typo__bold--${this.bold}`]: !!this.bold,\n        }}\n        part=\"bds-typo__text\"\n        data-test={this.dataTest}\n      >\n        <slot></slot>\n      </Element>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAU,g5iC,MCQHC,EAAI,M,yBAwBf,MAAAC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACLC,KAAM,KACN,CAAC,cAAcC,KAAKC,aAAc,KAClC,CAAC,oBAAoBD,KAAKE,kBAAmB,KAC7C,CAAC,GAAGF,KAAKG,YAAc,KAAO,YAAc,MAAO,KACnD,CAAC,GAAGH,KAAKI,iBAAmB,KAAO,kBAAoB,MAAO,KAC9D,CAAC,cAAcJ,KAAKK,YAAa,KACjC,CAAC,gBAAgBL,KAAKM,cAAe,KACrC,CAAC,QAAQN,KAAKO,OAAQ,KACtB,CAAC,OAAOP,KAAKQ,MAAO,KACpB,CAAC,OAAOR,KAAKS,MAAO,KACpB,CAAC,OAAOT,KAAKU,MAAO,KACpB,CAAC,OAAOV,KAAKW,MAAO,KACpB,CAAC,OAAOX,KAAKY,MAAO,KACpB,CAAC,QAAQZ,KAAKa,OAAQ,KACtB,CAAC,cAAcb,KAAKc,aAAc,KAClC,CAAC,aAAad,KAAKe,YAAa,KAChC,CAAC,aAAaf,KAAKgB,YAAa,KAChC,CAAC,aAAahB,KAAKiB,YAAa,KAChC,CAAC,aAAajB,KAAKkB,YAAa,KAChC,CAAC,aAAalB,KAAKmB,YAAa,KAChC,CAAC,YAAYnB,KAAKoB,WAAY,KAC9B,CAAC,WAAWpB,KAAKqB,UAAW,KAC5B,CAACrB,KAAKsB,SAAW,IAAK,MAExBC,MAAO,CAAEC,OAAQxB,KAAKwB,SAEtB7B,EAAa,QAAAE,IAAA,6C,aC9DrB,MAAM4B,EAAW,+oF,MCQJC,EAAK,MALlB,WAAAC,CAAAC,G,UAMW5B,KAAS6B,UAAG,KAMmB7B,KAAS8B,UAAoB,SAK7D9B,KAAQ+B,SAAY,KAKpB/B,KAAMgC,OAAa,MAInBhC,KAAMwB,OAAY,KAKlBxB,KAAKiC,MAAY,KAKjBjC,KAAOsB,QAAqB,YAK5BtB,KAAWkC,YAAiB,IAuBrC,CArBC,iBAAAC,GACEnC,KAAKgC,SAAW,KAAQhC,KAAK6B,UAAY,MAAU7B,KAAK6B,UAAY,I,CAGtE,MAAAnC,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACL,CAAC,qBAAqBE,KAAK8B,aAAc9B,KAAK6B,UAC9CG,OAAQhC,KAAKgC,OACb,CAAC,MAAMhC,KAAKsB,WAAY,KACxB,CAAC,UAAUtB,KAAKkC,eAAgB,MAElCX,MAAO,CAAEC,OAAQ,GAAGxB,KAAKwB,SAAUS,MAAO,GAAGjC,KAAKiC,UAElDtC,EAAA,OAAAE,IAAA,2CAAKC,MAAM,iBAA4B,YAAAE,KAAK+B,UAC1CpC,EAAa,QAAAE,IAAA,8C,aC9DvB,MAAMuC,EAAmB,G,MCMZC,EAAa,M,yBACxB,MAAA3C,GACE,OACEC,EAAU,YAAAE,IAAA,2CAAAU,IAAI,KAAKa,QAAQ,MAAK,YAAW,QACzCzB,EAAA,YAAAE,IAAA,2CAAUU,IAAI,KAAKc,OAAO,OACxB1B,EAAK,OAAAE,IAAA,2CAAAC,MAAM,UACTH,EAAU,YAAAE,IAAA,2CAAAyC,QAAQ,QAAQC,KAAK,QAEpB,mCAGf5C,EAAA,YAAAE,IAAA,2CAAUU,IAAI,IAAIa,QAAQ,OACxBzB,EAAoB,sBAAAE,IAAA,2CAAA2C,MAAM,SACxB7C,EAAA,aAAAE,IAAA,2CAAWiC,UAAU,OAAOE,OAAM,MAChCrC,EAAA,YAAAE,IAAA,2CAAUuB,QAAQ,SAIxBzB,EAAA,YAAAE,IAAA,2CAAUU,IAAI,IAAIa,QAAQ,OACxBzB,EAAoB,sBAAAE,IAAA,2CAAA2C,MAAM,QACxB7C,EAAA,aAAAE,IAAA,2CAAWiC,UAAU,OAAOE,OAAM,MAChCrC,EAAU,YAAAE,IAAA,2CAAAuB,QAAQ,S,aC3BhC,MAAMqB,EAAmB,+uJ,MCSZC,EAAa,MAL1B,WAAAf,CAAAC,G,UAUU5B,KAAKwC,MAAY,OAS1B,CAPC,MAAA9C,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,2CAACC,MAAO,CAAE0C,MAAO,KAAM,CAAC,UAAUxC,KAAKwC,SAAU,OACpD7C,EAAa,QAAAE,IAAA,6C,aCnBrB,MAAM8C,EAAU,m+C,MCeHC,EAAI,MALjB,WAAAjB,CAAAC,G,UAUU5B,KAAOsC,QAAc,QAMrBtC,KAAU6C,WAAoB,KAM9B7C,KAAIuC,KAAU,KAKdvC,KAAM8C,OAAa,MAKnB9C,KAAM+C,OAAa,MAKnB/C,KAASgD,UAAa,MAKtBhD,KAAMqB,OAAa,KAKnBrB,KAAGiD,IAAS,IAKZjD,KAAQ+B,SAAY,IAwB7B,CAtBC,MAAArC,GACE,MAAMwD,EAAUlD,KAAKiD,IAErB,OACEtD,EAACuD,EAAO,CAAArD,IAAA,2CACNC,MAAO,CACLqD,KAAM,KACN,CAAC,kBAAkBnD,KAAKsC,WAAY,KACpC,CAAC,iBAAiBtC,KAAKsC,WAAYtC,KAAKqB,OACxC,gBAAiBrB,KAAK+C,OACtB,kBAAmB/C,KAAKgD,UACxB,eAAgBhD,KAAK8C,OACrB,CAAC,sBAAsB9C,KAAK6C,gBAAiB7C,KAAK6C,WAClD,CAAC,eAAe7C,KAAKuC,UAAWvC,KAAKuC,MAEvCa,KAAK,iBACM,YAAApD,KAAK+B,UAEhBpC,EAAa,QAAAE,IAAA,6C", "ignoreList": []}