import{r as t,h as e}from"./p-C3J6Z5OX.js";var r;(function(t){t["Default"]="default";t["Warning"]="warning";t["Delete"]="delete"})(r||(r={}));const n=".counter-text{background:var(--color-surface-2, rgb(237, 237, 237));color:var(--color-content-disable, rgb(89, 89, 89));-webkit-box-sizing:content-box;box-sizing:content-box;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;border-radius:11px;padding:0 8px;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-o-user-select:none;-ms-user-select:none;user-select:none}.counter-text--active{background:var(--color-system, rgb(178, 223, 253));color:var(--color-content-din, rgb(0, 0, 0))}.counter-text--warning{background:var(--color-warning, rgb(253, 233, 155));color:var(--color-content-din, rgb(0, 0, 0))}.counter-text--delete{background:var(--color-delete, rgb(230, 15, 15));color:var(--color-content-bright, rgb(255, 255, 255))}";const o=class{constructor(e){t(this,e);this.active=false;this.warning={max:20,min:2};this.delete={max:1,min:0}}getState(){const t=this.getActualLength();if(t>=this.warning.min&&t<=this.warning.max){return r.Warning}if(t<=this.delete.max){return r.Delete}return r.Default}getActualLength(){return this.max-this.length}render(){const t=this.getState();const r=this.getActualLength();return e("div",{key:"eb0ee66e83bb3d4d0755d58b9887bafedffe962f",class:{"counter-text":true,"counter-text--active":this.active,[`counter-text--${t}`]:true}},e("bds-typo",{key:"8e07b48242309ee2d5047f21bede2f0f68f06771",variant:"fs-10"},r))}};o.style=n;export{o as bds_counter_text};
//# sourceMappingURL=p-9976bac3.entry.js.map