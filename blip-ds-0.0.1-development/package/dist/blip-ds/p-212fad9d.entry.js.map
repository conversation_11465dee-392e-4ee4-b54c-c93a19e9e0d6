{"version": 3, "names": ["menuExibitionCss", "BdsMenuExibition", "constructor", "hostRef", "this", "avatar<PERSON><PERSON>", "avatar<PERSON><PERSON><PERSON><PERSON>", "avatarSize", "value", "subtitle", "description", "disabled", "render", "has<PERSON><PERSON><PERSON>", "h", "key", "class", "menuexibition", "name", "thumbnail", "size", "variant", "tag"], "sources": ["src/components/menu/menu-exibition/menu-exibition.scss?tag=bds-menu-exibition&encapsulation=shadow", "src/components/menu/menu-exibition/menu-exibition.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuexibition {\n  display: flex;\n  align-items: center;\n  padding: 16px;\n\n  &__disabled {\n    opacity: 0.5;\n    cursor: no-drop;\n  }\n\n  & .avatar-item {\n    display: block;\n    margin-right: 8px;\n  }\n\n  & .content-item {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n    }\n\n    & .subtitle-item {\n      color: $color-content-disable;\n    }\n\n    & .description-item {\n      color: $color-content-default;\n    }\n  }\n}\n", "import { Component, h, Prop } from '@stencil/core';\n\nexport type avatarSize = 'extra-small' | 'small' | 'standard';\n\n@Component({\n  tag: 'bds-menu-exibition',\n  styleUrl: 'menu-exibition.scss',\n  shadow: true,\n})\nexport class BdsMenuExibition {\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * AvatarSize. Used to set avatar size.\n   */\n  @Prop() avatarSize?: avatarSize = 'standard';\n  /**\n   * Value. Used to insert a title in the display item.\n   */\n  @Prop() value?: string = null;\n  /**\n   * Subtitle. Used to insert a subtitle in the display item.\n   */\n  @Prop() subtitle?: string = null;\n  /**\n   * Description. Used to insert a subtitle in the display item.\n   */\n  @Prop() description?: string = null;\n  /**\n   * Disabled. Used to declare that the item will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  render() {\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <div\n        class={{\n          menuexibition: true,\n          [`menuexibition__disabled`]: this.disabled,\n        }}\n      >\n        {hasAvatar && (\n          <bds-avatar\n            class=\"avatar-item\"\n            name={this.avatarName}\n            thumbnail={this.avatarThumbnail}\n            size={this.avatarSize}\n          ></bds-avatar>\n        )}\n        <div class=\"content-item\">\n          {this.value && (\n            <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\">\n              {this.value}\n            </bds-typo>\n          )}\n          {this.subtitle && (\n            <bds-typo class=\"subtitle-item\" variant=\"fs-10\" tag=\"span\">\n              {this.subtitle}\n            </bds-typo>\n          )}\n          {this.description && (\n            <bds-typo class=\"description-item\" variant=\"fs-10\" tag=\"span\">\n              {this.description}\n            </bds-typo>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "2CAAA,MAAMA,EAAmB,inB,MCSZC,EAAgB,MAL7B,WAAAC,CAAAC,G,UASUC,KAAUC,WAAY,KAItBD,KAAeE,gBAAY,KAI3BF,KAAUG,WAAgB,WAI1BH,KAAKI,MAAY,KAIjBJ,KAAQK,SAAY,KAIpBL,KAAWM,YAAY,KAIvBN,KAAQO,SAAa,KAuC9B,CArCC,MAAAC,GACE,MAAMC,EAAYT,KAAKC,YAAcD,KAAKE,gBAC1C,OACEQ,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACLC,cAAe,KACf,CAAC,2BAA4Bb,KAAKO,WAGnCE,GACCC,EAAA,cAAAC,IAAA,2CACEC,MAAM,cACNE,KAAMd,KAAKC,WACXc,UAAWf,KAAKE,gBAChBc,KAAMhB,KAAKG,aAGfO,EAAK,OAAAC,IAAA,2CAAAC,MAAM,gBACRZ,KAAKI,OACJM,EAAA,YAAAC,IAAA,2CAAUC,MAAM,aAAaK,QAAQ,QAAQC,IAAI,QAC9ClB,KAAKI,OAGTJ,KAAKK,UACJK,EAAA,YAAAC,IAAA,2CAAUC,MAAM,gBAAgBK,QAAQ,QAAQC,IAAI,QACjDlB,KAAKK,UAGTL,KAAKM,aACJI,EAAA,YAAAC,IAAA,2CAAUC,MAAM,mBAAmBK,QAAQ,QAAQC,IAAI,QACpDlB,KAAKM,c", "ignoreList": []}