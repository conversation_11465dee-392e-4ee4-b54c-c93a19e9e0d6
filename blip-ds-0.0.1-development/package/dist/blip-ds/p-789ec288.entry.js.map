{"version": 3, "names": ["ptTerms", "bold", "italic", "strike", "underline", "link", "code", "align_left", "align_center", "align_right", "unordered_list", "ordered_list", "quote", "h1", "h2", "h3", "h4", "h5", "h6", "clear_formatting", "expand", "esTerms", "enTerms", "termTranslate", "lang", "string", "translate", "map", "term", "richTextCss", "RichText", "constructor", "hostRef", "this", "buttonsListElement", "buttonsEditElements", "editor", "dropDownLink", "buttomBoldActive", "buttomItalicActive", "buttomStrikeActive", "buttomUnderlineActive", "buttomCodeActive", "buttomLinkActive", "buttomLinkValidDisabled", "buttomAlignLeftActive", "buttomAlignCenterActive", "buttomAlignRightActive", "buttomUnorderedListActive", "buttomOrderedListActive", "buttomQuoteActive", "buttomH1Active", "buttomH2Active", "buttomH3Active", "buttomH4Active", "buttomH5Active", "buttomH6Active", "buttomAccordionActive", "headerHeight", "hasSelectionRange", "selectedLinesList", "treeElementsEditor", "styleSectorActive", "styleOnHover", "whenSelectionLink", "linkButtonInput", "insideComponent", "language", "weightButton", "italicButton", "strikeThroughButton", "underlineButton", "linkButton", "codeButton", "alignmentButtons", "listButtons", "quoteButton", "headingButtons", "unstyledButton", "height", "maxHeight", "positionBar", "dataTest", "refButtonsListElement", "el", "refeditorElement", "refDropDownLinkElement", "refInputSetLink", "inputSetLink", "clearToolbar", "setheader<PERSON>eight", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "removeAllRanges", "addRange", "onBlur", "classList", "remove", "bdsBlur", "emit", "onFocus", "add", "bdsFocus", "onKeydown", "event", "key", "startNode", "startContainer", "blockElement", "nodeType", "Node", "TEXT_NODE", "parentElement", "contains", "tagName", "innerText", "length", "preventDefault", "textContent", "innerHTML", "setCursorToEnd", "ctrl<PERSON>ey", "metaKey", "stopPropagation", "componentDidLoad", "trim", "getElementsByTagName", "<PERSON><PERSON><PERSON><PERSON>", "style", "buttomsHeaderChanged", "setTimeout", "buttomAccordionActiveChanged", "updateToolbarState", "commonAncestor", "commonAncestorContainer", "getParentsUntil", "value", "allbuttonsEditElementsWidth", "buttonsListWidth", "offsetWidth", "buttonAccordion", "querySelector", "diferrence", "numberOfColumns", "Math", "ceil", "allbuttonsEditElements", "Array", "from", "slice", "floor", "for<PERSON>ach", "element", "treeElementsEditorChanged", "tagList", "toLowerCase", "tagVerifyName", "tag", "includes", "getLine", "find", "textAlign", "onInput", "ev", "bdsRichTextInput", "bdsRichTextChange", "currentNode", "ELEMENT_NODE", "divElement", "pElement", "document", "createElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "querySelectorAll", "div", "p", "replaceWith", "onFocusEditorBar", "<PERSON><PERSON><PERSON>", "target", "NextButton", "nextElement<PERSON><PERSON>ling", "ElementToFocus", "shadowRoot", "focus", "createRange", "sel", "selectNodeContents", "collapse", "wrapSelection", "detail", "KeyboardEvent", "anchorNode", "isTagApplied", "content", "collapsedCursor", "appliedTag", "parent", "isFullSelection", "toString", "isAtEndOfTag", "endOffset", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "createDocumentFragment", "placeholder", "createTextNode", "append<PERSON><PERSON><PERSON>", "newRange", "setStartAfter", "setEndAfter", "insertNode", "collapsed", "extractContents", "wrapper", "setAttribute", "children", "setStart", "setEnd", "setStartBefore", "<PERSON><PERSON><PERSON><PERSON>", "wrapSelectionLine", "enableLinesReturn", "endNode", "endContainer", "selectedLines", "Set", "nextS<PERSON>ling", "_a", "endElement", "allAreSameTag", "every", "line", "returnSelected", "newElement", "lastNode", "_b", "_c", "alignText", "alignment", "currentAlignment", "createHeading", "type", "firstItemList", "firstParent", "previousElementSibling", "lastParent", "item", "insertAdjacentElement", "createList", "lastItemList", "verifyList", "parentListElements", "parentList", "addSelectionLink", "<PERSON><PERSON><PERSON>", "addLinkInput", "input", "createLinkKeyDown", "createLink", "setClose", "firstItem", "lastItem", "firstItemValue", "lastItemValue", "handlePaste", "clipboardData", "plainText", "getData", "deleteContents", "fragment", "split", "clearFormatting", "render", "h", "Host", "class", "tabindex", "onMouseEnter", "onMouseLeave", "ref", "contentEditable", "onMouseUp", "onKeyUp", "onKeyDown", "onPaste", "bind", "position", "variant", "color", "size", "onBdsClick", "activeMode", "slot", "padding", "alignItems", "gap", "onBdsInput", "flexShrink", "open", "disabled", "id"], "sources": ["src/components/rict-text/languages/pt_BR.tsx", "src/components/rict-text/languages/es_ES.tsx", "src/components/rict-text/languages/en_US.tsx", "src/components/rict-text/languages/index.ts", "src/components/rict-text/rich-text.scss?tag=bds-rich-text", "src/components/rict-text/rich-text.tsx"], "sourcesContent": ["export const ptTerms = [\n  {\n    bold: 'Negrito',\n    italic: 'It<PERSON>lico',\n    strike: '<PERSON><PERSON><PERSON>',\n    underline: 'Sublin<PERSON>o',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: '<PERSON><PERSON><PERSON> à esquerda',\n    align_center: '<PERSON><PERSON>ar ao centro',\n    align_right: 'Alinhar à direita',\n    unordered_list: 'Lista não ordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Citação',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpar formatação',\n    expand: 'Expandir',\n  },\n];\n", "export const esTerms = [\n  {\n    bold: 'Negrita',\n    italic: '<PERSON>urs<PERSON>',\n    strike: 'Tacha<PERSON>',\n    underline: 'Subrayado',\n    link: 'Link',\n    code: '<PERSON><PERSON><PERSON>',\n    align_left: 'Alinear a la izquierda',\n    align_center: 'Alinear al centro',\n    align_right: 'Alinear a la derecha',\n    unordered_list: 'Lista desordenada',\n    ordered_list: 'Lista ordenada',\n    quote: 'Cita',\n    h1: 'Título 1',\n    h2: 'Título 2',\n    h3: 'Título 3',\n    h4: 'Título 4',\n    h5: 'Título 5',\n    h6: 'Título 6',\n    clear_formatting: 'Limpiar formato',\n    expand: 'Expandir',\n  },\n];\n", "export const enTerms = [\n  {\n    bold: 'Bold',\n    italic: 'Italic',\n    strike: 'Strikethrough',\n    underline: 'Underline',\n    link: 'Link',\n    code: 'Code',\n    align_left: 'Align left',\n    align_center: 'Align center',\n    align_right: 'Align right',\n    unordered_list: 'Unordered list',\n    ordered_list: 'Ordered list',\n    quote: 'Quote',\n    h1: 'Heading 1',\n    h2: 'Heading 2',\n    h3: 'Heading 3',\n    h4: 'Heading 4',\n    h5: 'Heading 5',\n    h6: 'Heading 6',\n    clear_formatting: 'Clear formatting',\n    expand: 'Expand',\n  },\n];\n", "import { ptTerms } from './pt_BR';\nimport { esTerms } from './es_ES';\nimport { enTerms } from './en_US';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n\nexport const termTranslate = (lang: languages, string: string): string => {\n  let translate;\n  switch (lang) {\n    case 'pt_BR':\n      translate = ptTerms.map((term) => term[string]);\n      break;\n    case 'es_ES':\n      translate = esTerms.map((term) => term[string]);\n      break;\n    case 'en_US':\n      translate = enTerms.map((term) => term[string]);\n      break;\n    default:\n      translate = ptTerms.map((term) => term[string]);\n  }\n  return translate;\n};\n", "@use '../../globals/helpers' as *;\n\n.rich-text {\n  box-sizing: border-box;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 8px;\n  gap: 8px;\n  border: 1px solid $color-border-1;\n  border-radius: 16px;\n  background-color: $color-surface-1;\n\n  &-top {\n    .format-buttons {\n      order: 1;\n    }\n    .preview {\n      order: 2;\n    }\n  }\n\n  &-bottom {\n    .format-buttons {\n      order: 2;\n    }\n    .preview {\n      order: 1;\n    }\n  }\n\n  &.active {\n    border-color: $color-primary;\n    box-shadow: 0 0 0 2px $color-info;\n  }\n\n  .format-buttons {\n    display: none !important;\n\n    &-active {\n      display: flex !important;\n      position: relative;\n      background-color: $color-surface-0;\n      border: 1px solid $color-border-1;\n      border-radius: 16px;\n      padding: 8px;\n    }\n\n    .style-onhover {\n      position: absolute;\n      background-color: $color-surface-1;\n      border-radius: 32px;\n      bottom: -32px;\n      right: 0;\n      opacity: 0;\n      -webkit-transition: opacity ease-in-out 0.5s;\n      -moz-transition: opacity ease-in-out 0.5s;\n      transition: opacity ease-in-out 0.5s;\n      pointer-events: none;\n\n      &.active {\n        opacity: 1;\n      }\n    }\n\n    .accordion-header {\n      width: 100%;\n      position: relative;\n      padding-right: 40px;\n\n      .buttons-list {\n        column-gap: 8px;\n\n        .editor-bar {\n          width: 0;\n          margin-right: -8px;\n        }\n\n        & bds-tooltip {\n          -webkit-transition: height ease-in-out 0.25s;\n          -moz-transition: height ease-in-out 0.25s;\n          transition: height ease-in-out 0.25s;\n          height: 0px;\n\n          & > bds-button,\n          & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n            height: 0;\n            opacity: 0;\n            display: block;\n            overflow: hidden;\n            -webkit-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            -moz-transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n            transition:\n              height ease-in-out 0.25s,\n              opacity 0.5s ease-in-out 0.25s;\n          }\n\n          &.active {\n            height: 32px;\n            & > bds-button,\n            & > bds-dropdown > div[slot='dropdown-activator'] > bds-button {\n              overflow: inherit;\n              height: 32px;\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .arrow-down {\n        position: absolute;\n        right: 0;\n        top: 0;\n        display: none;\n        &.active {\n          display: block;\n        }\n      }\n    }\n  }\n  .preview {\n    box-sizing: border-box;\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    padding: 8px;\n    -webkit-transition: height ease-in-out 0.25s;\n    -moz-transition: height ease-in-out 0.25s;\n    transition: height ease-in-out 0.25s;\n\n    .editor-uai-design-system {\n      min-height: 48px;\n      height: 100%;\n      background-color: transparent;\n      font-size: 1rem;\n      line-height: 1.5;\n      overflow-y: auto;\n      outline: none;\n      font-family: $font-family;\n      font-style: normal;\n      font-weight: normal;\n      color: $color-content-default;\n      @include custom-scroll;\n\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5,\n      h6,\n      ul,\n      ol,\n      blockquote {\n        margin: 0 0 8px 0;\n      }\n\n      h1 {\n        font-size: 32px;\n        font-weight: 600;\n      }\n      h2 {\n        font-size: 28px;\n        font-weight: 600;\n      }\n      h3 {\n        font-size: 24px;\n        font-weight: 600;\n      }\n      h4 {\n        font-size: 20px;\n        font-weight: 600;\n      }\n      h5 {\n        font-size: 16px;\n        font-weight: 600;\n      }\n      h6 {\n        font-size: 12px;\n        font-weight: 600;\n      }\n\n      a {\n        text-decoration: none;\n        color: $color-primary;\n      }\n\n      blockquote {\n        padding: 4px 16px 4px 32px;\n        font-size: 14px;\n        position: relative;\n        display: inline-block;\n\n        &::before,\n        &::after {\n          content: '\"';\n          position: absolute;\n          font-size: 24px;\n          color: $color-content-ghost;\n        }\n\n        &::before {\n          left: 8px;\n          top: -6px;\n        }\n\n        &::after {\n          right: 0px;\n          bottom: 0px;\n        }\n      }\n      code {\n        font-family: monospace;\n        font-size: 12px;\n        background-color: $color-surface-2;\n        padding: 4px;\n        border-radius: 4px;\n      }\n    }\n  }\n}\n\n/* Editor */\n", "import { Element, Component, h, Host, Prop, State, Watch, Event, EventEmitter } from '@stencil/core';\nimport { getParentsUntil } from '../../utils/position-element';\nimport { languages } from './rich-text-interface';\nimport { termTranslate } from './languages';\n\nexport type positionBar = 'top' | 'bottom';\n\n@Component({\n  tag: 'bds-rich-text',\n  styleUrl: 'rich-text.scss',\n  shadow: false,\n})\nexport class RichText {\n  private buttonsListElement?: HTMLElement = null;\n  private buttonsEditElements?: HTMLCollectionOf<HTMLBdsTooltipElement> = null;\n  private editor?: HTMLElement = null;\n  private dropDownLink?: HTMLBdsDropdownElement = null;\n  private inputSetLink?: HTMLBdsInputElement;\n\n  @Element() el: HTMLElement;\n  @State() buttomBoldActive?: boolean = false;\n  @State() buttomItalicActive?: boolean = false;\n  @State() buttomStrikeActive?: boolean = false;\n  @State() buttomUnderlineActive?: boolean = false;\n  @State() buttomCodeActive?: boolean = false;\n  @State() buttomLinkActive?: boolean = false;\n  @State() buttomLinkValidDisabled?: boolean = true;\n  @State() buttomAlignLeftActive?: boolean = false;\n  @State() buttomAlignCenterActive?: boolean = false;\n  @State() buttomAlignRightActive?: boolean = false;\n  @State() buttomUnorderedListActive?: boolean = false;\n  @State() buttomOrderedListActive?: boolean = false;\n  @State() buttomQuoteActive?: boolean = false;\n  @State() buttomH1Active?: boolean = false;\n  @State() buttomH2Active?: boolean = false;\n  @State() buttomH3Active?: boolean = false;\n  @State() buttomH4Active?: boolean = false;\n  @State() buttomH5Active?: boolean = false;\n  @State() buttomH6Active?: boolean = false;\n  @State() buttomAccordionActive?: boolean = false;\n  @State() headerHeight?: string = '32px';\n  @State() hasSelectionRange?: boolean = false;\n  @State() selectedLinesList?: { element: HTMLElement }[] = null;\n  @State() treeElementsEditor?: HTMLElement[] = null;\n  @State() styleSectorActive?: string = null;\n  @State() styleOnHover?: string = 'teste';\n  @State() whenSelectionLink?: Range = null;\n  @State() linkButtonInput?: string = null;\n  @State() insideComponent?: boolean = false;\n  /**\n   * Set the language for fixed texts.\n   */\n  @Prop() language?: languages = 'pt_BR';\n  /**\n   * weightButton to define if component has Bold Control.\n   */\n  @Prop() weightButton?: boolean = true;\n  /**\n   * italicButton to define if component has Italic Control.\n   */\n  @Prop() italicButton?: boolean = true;\n  /**\n   * strikeThroughbutton to define if component has Strike Control.\n   */\n  @Prop() strikeThroughButton?: boolean = true;\n  /**\n   * underlineButton to define if component has Underline Control.\n   */\n  @Prop() underlineButton?: boolean = true;\n  /**\n   * linkButton to define if component has Link Control.\n   */\n  @Prop() linkButton?: boolean = true;\n  /**\n   * codeButton to define if component has Code Control.\n   */\n  @Prop() codeButton?: boolean = true;\n  /**\n   * alignmentButtons to define if component has TextAlign Control.\n   */\n  @Prop() alignmentButtons?: boolean = true;\n  /**\n   * listButtons to define if component has List Control.\n   */\n  @Prop() listButtons?: boolean = true;\n  /**\n   * quoteButton to define if component has Quote Control.\n   */\n  @Prop() quoteButton?: boolean = true;\n  /**\n   * headingButtons to define if component has Heading Control.\n   */\n  @Prop() headingButtons?: boolean = true;\n  /**\n   * unstyledButton to define if component has Unstyled Control.\n   */\n  @Prop() unstyledButton?: boolean = true;\n  /**\n   * height is the prop to define height of component.\n   */\n  @Prop() height?: string = null;\n  /**\n   * maxHeight is the prop to define max height of component.\n   */\n  @Prop() maxHeight?: string = null;\n  /**\n   * positionBar is the prop to define max height of component.\n   */\n  @Prop() positionBar?: positionBar = 'top';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event({ bubbles: true, composed: true }) bdsRichTextChange!: EventEmitter;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsRichTextInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Event input onblur.\n   */\n  @Event() bdsBlur: EventEmitter;\n\n  /**\n   * Event input focus.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  componentDidLoad() {\n    if (this.editor.innerHTML.trim() === '') {\n      this.editor.innerHTML = '<p class=\"line\"><br></p>';\n    }\n    if (\n      this.weightButton ||\n      this.italicButton ||\n      this.strikeThroughButton ||\n      this.underlineButton ||\n      this.linkButton ||\n      this.codeButton ||\n      this.alignmentButtons ||\n      this.listButtons ||\n      this.quoteButton ||\n      this.headingButtons ||\n      this.unstyledButton\n    ) {\n      this.buttonsEditElements = this.buttonsListElement.getElementsByTagName(\n        'bds-tooltip',\n      ) as HTMLCollectionOf<HTMLBdsTooltipElement>;\n      this.accordionHeader(false);\n      this.editor.parentElement.style.height = `calc(100% - 56px)`;\n    } else {\n      this.editor.parentElement.style.height = `100%`;\n    }\n  }\n\n  @Watch('weightButton')\n  @Watch('italicButton')\n  @Watch('strikeThroughButton')\n  @Watch('underlineButton')\n  @Watch('linkButton')\n  @Watch('codeButton')\n  @Watch('alignmentButtons')\n  @Watch('listButtons')\n  @Watch('quoteButton')\n  @Watch('headingButtons')\n  @Watch('unstyledButton')\n  protected buttomsHeaderChanged(): void {\n    setTimeout(() => this.accordionHeader(this.buttomAccordionActive), 500);\n  }\n\n  @Watch('buttomAccordionActive')\n  protected buttomAccordionActiveChanged(): void {\n    this.accordionHeader(this.buttomAccordionActive);\n  }\n\n  private updateToolbarState() {\n    const selection = window.getSelection();\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    this.treeElementsEditor = getParentsUntil(parentElement, '.editor-uai-design-system');\n  }\n\n  // Coloca o cursor no final do editor\n  private accordionHeader(value: boolean) {\n    const allbuttonsEditElementsWidth = this.buttonsEditElements.length * 40;\n    const buttonsListWidth = this.buttonsListElement.offsetWidth;\n    const buttonAccordion = this.el.querySelector('#buttonAccordion') as HTMLBdsButtonElement;\n    if (buttonsListWidth < allbuttonsEditElementsWidth) {\n      buttonAccordion.classList.add('active');\n    } else {\n      buttonAccordion.classList.remove('active');\n    }\n    const diferrence = (buttonsListWidth * this.buttonsEditElements.length) / allbuttonsEditElementsWidth;\n    const numberOfColumns = Math.ceil(allbuttonsEditElementsWidth / buttonsListWidth);\n    const allbuttonsEditElements = Array.from(this.buttonsEditElements);\n    allbuttonsEditElements.slice(0, Math.floor(diferrence)).forEach((element) => {\n      element.classList.add('active');\n    });\n    if (value) {\n      allbuttonsEditElements.forEach((element) => {\n        element.classList.add('active');\n        this.editor.parentElement.style.height = `calc(100% - ${numberOfColumns * 32 + 24}px)`;\n      });\n    } else {\n      allbuttonsEditElements.slice(Math.floor(diferrence)).forEach((element) => {\n        element.classList.remove('active');\n        this.editor.parentElement.style.height = `calc(100% - 56px)`;\n      });\n    }\n  }\n\n  @Watch('treeElementsEditor')\n  protected treeElementsEditorChanged(value): void {\n    const tagList = value.map((element) => element?.tagName.toLowerCase());\n    const tagVerifyName = (tag) => tagList.includes(tag);\n    const getLine = value.find((el) => el?.classList.contains('line'));\n    this.buttomBoldActive = tagVerifyName('b');\n    this.buttomItalicActive = tagVerifyName('i');\n    this.buttomStrikeActive = tagVerifyName('strike');\n    this.buttomUnderlineActive = tagVerifyName('u');\n    this.buttomLinkActive = tagVerifyName('a');\n    this.buttomCodeActive = tagVerifyName('code');\n    this.buttomAlignLeftActive = getLine?.style.textAlign === 'left';\n    this.buttomAlignCenterActive = getLine?.style.textAlign === 'center';\n    this.buttomAlignRightActive = getLine?.style.textAlign === 'right';\n    this.buttomUnorderedListActive = tagList[0] === 'ul';\n    this.buttomOrderedListActive = tagList[0] === 'ol';\n    this.buttomQuoteActive = tagVerifyName('blockquote');\n    this.buttomH1Active = tagVerifyName('h1');\n    this.buttomH2Active = tagVerifyName('h2');\n    this.buttomH3Active = tagVerifyName('h3');\n    this.buttomH4Active = tagVerifyName('h4');\n    this.buttomH5Active = tagVerifyName('h5');\n    this.buttomH6Active = tagVerifyName('h6');\n  }\n\n  private refButtonsListElement = (el: HTMLElement): void => {\n    this.buttonsListElement = el;\n  };\n  private refeditorElement = (el: HTMLElement): void => {\n    this.editor = el;\n  };\n  private refDropDownLinkElement = (el: HTMLBdsDropdownElement): void => {\n    this.dropDownLink = el;\n  };\n\n  private refInputSetLink = (el: HTMLBdsInputElement): void => {\n    this.inputSetLink = el;\n  };\n\n  private clearToolbar = () => {\n    this.buttomBoldActive = false;\n    this.buttomItalicActive = false;\n    this.buttomStrikeActive = false;\n    this.buttomUnderlineActive = false;\n    this.buttomLinkActive = false;\n    this.buttomCodeActive = false;\n    this.buttomAlignLeftActive = false;\n    this.buttomAlignCenterActive = false;\n    this.buttomAlignRightActive = false;\n    this.buttomUnorderedListActive = false;\n    this.buttomOrderedListActive = false;\n    this.buttomQuoteActive = false;\n    this.buttomH1Active = false;\n    this.buttomH2Active = false;\n    this.buttomH3Active = false;\n    this.buttomH4Active = false;\n    this.buttomH5Active = false;\n    this.buttomH6Active = false;\n  };\n\n  private setheaderHeight = () => {\n    this.buttomAccordionActive = !this.buttomAccordionActive;\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    const range = selection.getRangeAt(0);\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  };\n\n  private onBlur = () => {\n    this.el.classList.remove('active');\n    if (this.insideComponent === false) {\n      this.clearToolbar();\n    }\n    this.bdsBlur.emit();\n  };\n\n  private onFocus = () => {\n    this.el.classList.add('active');\n    this.bdsFocus.emit();\n  };\n\n  // Função para ajustar parágrafos durante a edição\n  private onInput(ev: InputEvent) {\n    ev.preventDefault();\n    this.bdsRichTextInput.emit(ev);\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const currentNode = range.startContainer;\n\n    // Se o nó atual é uma `div`, converta-o em um `p`\n    if (currentNode.nodeType === Node.ELEMENT_NODE && (currentNode as HTMLElement).tagName === 'DIV') {\n      const divElement = currentNode as HTMLElement;\n\n      const pElement = document.createElement('p');\n      pElement.classList.add('line');\n      pElement.innerHTML = divElement.innerHTML;\n\n      divElement.parentNode.replaceChild(pElement, divElement);\n    }\n\n    // Garante que novas linhas (Enter) criem <p> ao invés de <div>\n    this.editor.querySelectorAll('div').forEach((div) => {\n      const p = document.createElement('p');\n      p.classList.add('line');\n      p.innerHTML = div.innerHTML;\n      div.replaceWith(p);\n    });\n  }\n\n  private onKeydown = (event: KeyboardEvent) => {\n    if (event.key === 'Backspace') {\n      const selection = window.getSelection();\n      if (!selection || selection.rangeCount === 0) return;\n\n      const range = selection.getRangeAt(0);\n      const startNode = range.startContainer;\n\n      // Encontra o elemento de bloco que contém o cursor\n      let blockElement = startNode.nodeType === Node.TEXT_NODE ? startNode.parentElement : (startNode as HTMLElement);\n\n      while (blockElement && !blockElement.classList.contains('line') && blockElement !== this.editor) {\n        blockElement = blockElement.parentElement;\n      }\n\n      // Se o elemento atual for um <blockquote> e estiver vazio, removê-lo\n      if (\n        blockElement &&\n        blockElement.tagName === 'BLOCKQUOTE' &&\n        blockElement.classList.contains('line') &&\n        blockElement.innerText.length <= 1\n      ) {\n        event.preventDefault(); // Impede a exclusão padrão\n        blockElement.remove(); // Remove apenas o blockquote vazio\n      }\n    }\n    if (this.editor.textContent.length === 0 && event.key === 'Backspace') {\n      event.preventDefault();\n      this.editor.innerHTML = `<p class=\"line\"><br></p>`;\n      this.setCursorToEnd();\n    }\n    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {\n      event.preventDefault(); // Impede o Ctrl+Z\n      event.stopPropagation(); // Evita que afete outros elementos\n    }\n  };\n\n  // Controle a navegação do componente\n  private onFocusEditorBar(ev: FocusEvent) {\n    const editorBar = ev.target as HTMLElement;\n    const NextButton = editorBar.nextElementSibling.querySelector('bds-button');\n    const ElementToFocus = NextButton.shadowRoot.querySelector('.focus') as HTMLElement;\n    ElementToFocus.focus();\n    this.buttomAccordionActive = true;\n  }\n\n  // Coloca o cursor no final do editor\n  private setCursorToEnd() {\n    const range = document.createRange();\n    const sel = window.getSelection();\n    range.selectNodeContents(this.editor);\n    range.collapse(false);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n\n  private tagName(tag: string, tagList: HTMLElement[]): boolean {\n    const value = tagList.map((element) => element?.tagName.toLowerCase());\n    return value.includes(tag);\n  }\n\n  private wrapSelection(ev: CustomEvent, tag: string, link?: string) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n\n    const tagList = getParentsUntil(parentElement, '.line');\n    const isTagApplied = this.tagName(tag, tagList);\n\n    // Se a seleção estiver vazia, cria um espaço invisível para edição\n    let content: DocumentFragment;\n    let collapsedCursor = false;\n\n    // Se a tag já está aplicada e o usuário quer remover\n    if (isTagApplied) {\n      const appliedTag = tagList.find((el) => el.tagName.toLowerCase() === tag);\n      if (appliedTag) {\n        const parent = appliedTag.parentElement;\n        const isFullSelection = range.toString().trim() === appliedTag.textContent.trim();\n        const isAtEndOfTag = range.endOffset === appliedTag.textContent.length;\n\n        if (isFullSelection && parent) {\n          // Remove a tag se toda a seleção corresponde ao conteúdo da tag\n          while (appliedTag.firstChild) {\n            parent.insertBefore(appliedTag.firstChild, appliedTag);\n          }\n          parent.removeChild(appliedTag);\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        } else if (isAtEndOfTag) {\n          // Se o cursor está no final da tag, move para fora dela\n          content = document.createDocumentFragment();\n          const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n          content.appendChild(placeholder);\n          collapsedCursor = true;\n          const newRange = document.createRange();\n          newRange.setStartAfter(appliedTag); // Define o início depois do elemento\n          newRange.setEndAfter(appliedTag);\n          newRange.insertNode(content);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n          this.updateToolbarState();\n        } else {\n          // Se o cursor está no final da tag, move para fora dela\n          selection.removeAllRanges();\n          selection.addRange(range);\n          this.updateToolbarState();\n        }\n      }\n      return;\n    }\n\n    if (range.collapsed) {\n      content = document.createDocumentFragment();\n      const placeholder = document.createTextNode('\\u200B'); // Zero-width space\n      content.appendChild(placeholder);\n      collapsedCursor = true;\n    } else {\n      content = range.extractContents();\n    }\n\n    // Remove tags desnecessárias dentro da seleção\n    content.querySelectorAll('*').forEach((element) => {\n      while (element.firstChild) {\n        element.parentNode.insertBefore(element.firstChild, element);\n      }\n      element.remove();\n    });\n\n    // Cria a nova tag e aplica o conteúdo extraído\n    const wrapper = document.createElement(tag);\n    if (tag === 'a' && link) {\n      wrapper.setAttribute('href', link);\n    }\n    wrapper.appendChild(content);\n    range.insertNode(wrapper);\n\n    // Remove tags vazias no editor\n    this.editor.querySelectorAll('*').forEach((el) => {\n      if (!el.textContent.trim() && el.children.length === 0) {\n        el.remove();\n      }\n    });\n\n    // Se o cursor estava no meio da edição, mantém a seleção original\n    const newRange = document.createRange();\n    if (collapsedCursor) {\n      newRange.setStart(wrapper, 0);\n      newRange.setEnd(wrapper, 1);\n    } else {\n      newRange.setStartBefore(wrapper.firstChild || wrapper);\n      newRange.setEndAfter(wrapper.lastChild || wrapper);\n    }\n    selection.removeAllRanges();\n\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    // Emite o evento para atualizar o estado\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  private wrapSelectionLine(tag: string, enableLinesReturn: boolean = false) {\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Verifica se todas as linhas já possuem a tag escolhida\n    const allAreSameTag = [...selectedLines].every((line) =>\n      tag === 'li' ? false : line.tagName.toLowerCase() === tag,\n    );\n\n    const returnSelected: HTMLElement[] = [...selectedLines].map((el) => {\n      const newElement = document.createElement(allAreSameTag ? 'p' : tag);\n      newElement.classList.add('line');\n      newElement.innerHTML = el.innerHTML;\n      el.replaceWith(newElement);\n      return newElement;\n    });\n\n    if (enableLinesReturn) {\n      this.selectedLinesList = returnSelected.map((element) => ({ element }));\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    const newRange = document.createRange();\n    let lastNode = returnSelected[0].lastChild;\n\n    // Se não houver filhos, cria um nó de texto para evitar erro\n    if (!lastNode) {\n      lastNode = document.createTextNode('');\n      returnSelected[0].appendChild(lastNode);\n    }\n\n    // Se o último nó for outro elemento, busca um nó de texto dentro dele\n    while (lastNode && lastNode.nodeType !== Node.TEXT_NODE) {\n      lastNode = lastNode.lastChild || lastNode;\n    }\n\n    // Define o range no final do último nó de texto\n    newRange.setStart(lastNode, lastNode.textContent?.length || 0);\n    newRange.setEnd(lastNode, lastNode.textContent?.length || 0);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para aplicar alinhamento ao texto selecionado\n  private alignText(ev: CustomEvent, alignment: 'left' | 'center' | 'right' | 'justify') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    let blockElement = range.startContainer as HTMLElement;\n\n    // Percorre os elementos até encontrar um bloco válido que tenha a classe \"line\"\n    while (blockElement && blockElement !== this.editor) {\n      if (blockElement.nodeType === Node.ELEMENT_NODE && blockElement.classList.contains('line')) {\n        break;\n      }\n      blockElement = blockElement.parentElement;\n    }\n\n    // Se encontrou um elemento de bloco com a classe \"line\"\n    if (blockElement && blockElement !== this.editor) {\n      // Verifica se o alinhamento já está aplicado\n      const currentAlignment = (blockElement as HTMLElement).style.textAlign;\n      if (currentAlignment === alignment) {\n        // Se já estiver alinhado, remove o alinhamento\n        (blockElement as HTMLElement).style.textAlign = '';\n      } else {\n        // Caso contrário, aplica o alinhamento\n        (blockElement as HTMLElement).style.textAlign = alignment;\n      }\n    }\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n\n    this.updateToolbarState();\n\n    this.bdsRichTextChange.emit({ value: this.editor.innerHTML });\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createHeading(ev: CustomEvent, type: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine(type, true);\n    const firstItemList = this.selectedLinesList[0]?.element;\n    const firstParent = firstItemList.parentElement.previousElementSibling;\n    const lastParent = firstItemList.parentElement.nextElementSibling;\n    const parent = firstItemList.parentElement;\n    if (parent.tagName.toLowerCase() === 'ul') {\n      this.selectedLinesList.forEach((item) => {\n        if (firstParent) {\n          firstParent.insertAdjacentElement('afterend', item.element);\n        } else if (lastParent) {\n          lastParent.insertAdjacentElement('beforebegin', item.element);\n        } else {\n          this.editor.insertAdjacentElement('afterbegin', item.element);\n        }\n      });\n      if (Array.from(parent.getElementsByTagName('li')).length == 0) {\n        parent.remove();\n      }\n    }\n  }\n\n  // Função para criar/remover lista (ordenada ou não ordenada)\n  private createList(ev: CustomEvent, type: 'ol' | 'ul') {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n    if (!this.editor.contains(selection.anchorNode)) return;\n    this.wrapSelectionLine('li', true);\n    const firstItemList = this.selectedLinesList[0].element;\n    const lastItemList = this.selectedLinesList[this.selectedLinesList.length - 1]?.element;\n    const wrapper = document.createElement(type);\n    const parent = firstItemList.parentElement;\n\n    if (!this.verifyList(firstItemList, lastItemList)) {\n      parent.insertBefore(wrapper, firstItemList);\n      this.selectedLinesList.forEach((item) => {\n        wrapper.appendChild(item.element);\n      });\n    } else {\n      const parentListElements = parent.getElementsByTagName('li');\n      const parentList = Array.from(parentListElements).map((element) => ({ element }));\n\n      if (parentList.length == this.selectedLinesList.length) {\n        if (type !== parent.tagName.toLowerCase()) {\n          wrapper.innerHTML = parent.innerHTML;\n          parent.parentNode.replaceChild(wrapper, parent);\n        } else {\n          this.selectedLinesList.forEach((item) => {\n            const tagList = parent.parentElement.tagName.toLowerCase() === 'li' ? 'li' : 'p';\n            const newElement = document.createElement(tagList);\n            newElement.classList.add('line');\n            newElement.innerHTML = item.element.innerHTML;\n            if (parent.parentElement.tagName.toLowerCase() === 'li') {\n              parent.parentElement.insertAdjacentElement('afterend', newElement);\n            } else {\n              parent.previousElementSibling.insertAdjacentElement('afterend', newElement);\n            }\n            parent.removeChild(item.element);\n          });\n          parent.remove();\n        }\n      } else {\n        // parent.insertBefore(wrapper, firstItemList);\n        firstItemList.previousElementSibling.insertAdjacentElement('beforeend', wrapper);\n        this.selectedLinesList.forEach((item) => {\n          wrapper.appendChild(item.element);\n        });\n      }\n    }\n  }\n\n  private addSelectionLink(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      this.dropDownLink.setOpen();\n    }\n    this.editor.focus();\n    const selection = window.getSelection();\n    this.whenSelectionLink = selection.getRangeAt(0);\n    const ElementToFocus = this.inputSetLink.shadowRoot.querySelector('.input__container__text') as HTMLInputElement;\n    ElementToFocus.focus();\n  }\n\n  private addLinkInput(ev: InputEvent) {\n    ev.preventDefault();\n    const input = ev.target as HTMLInputElement | null;\n    this.linkButtonInput = input.value;\n    if (this.linkButtonInput.length > 0) {\n      this.buttomLinkValidDisabled = false;\n    } else {\n      this.buttomLinkValidDisabled = true;\n    }\n  }\n\n  private createLinkKeyDown(ev: KeyboardEvent) {\n    if (ev.key == 'Enter') {\n      this.createLink(ev);\n    }\n  }\n\n  private createLink(ev) {\n    ev.preventDefault();\n    const selection = window.getSelection();\n    selection.removeAllRanges();\n    selection.addRange(this.whenSelectionLink);\n    this.wrapSelection(ev, 'a', this.linkButtonInput);\n    if (this.dropDownLink) {\n      this.dropDownLink.setClose();\n    }\n  }\n\n  private verifyList(firstItem: HTMLElement, lastItem: HTMLElement) {\n    const firstItemValue =\n      firstItem.parentElement.tagName.toLowerCase() === 'ul' || firstItem.parentElement.tagName.toLowerCase() === 'ol';\n    const lastItemValue =\n      lastItem.parentElement.tagName.toLowerCase() === 'ul' || lastItem.parentElement.tagName.toLowerCase() === 'ol';\n    return firstItemValue && lastItemValue;\n  }\n\n  // Função para limpar o HTML ao colar conteúdo\n  private handlePaste(event: ClipboardEvent) {\n    event.preventDefault(); // Bloqueia a colagem padrão\n    event.stopPropagation(); // Evita que afete outros elementos\n\n    const clipboardData = event.clipboardData || (window as any).clipboardData;\n    const plainText = clipboardData.getData('text/plain'); // Obtém apenas texto puro\n\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const commonAncestor = range.commonAncestorContainer;\n    const parentElement =\n      commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : (commonAncestor as HTMLElement);\n    if (parentElement.classList.contains('line')) {\n      parentElement.remove();\n    }\n\n    range.deleteContents(); // Remove qualquer seleção existente\n\n    // Converte cada linha do texto colado em <p class=\"line\">\n    const fragment = document.createDocumentFragment();\n    plainText.split('\\n').forEach((line) => {\n      if (line.trim()) {\n        const p = document.createElement('p');\n        p.classList.add('line');\n        p.textContent = line.trim();\n        fragment.appendChild(p);\n      }\n    });\n\n    // Insere o conteúdo processado no local do cursor\n    range.insertNode(fragment);\n\n    // Ajusta o cursor para o final do texto inserido\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  // Função para restaurar a formatação do texto selecionado\n  private clearFormatting(ev: CustomEvent) {\n    const detail = ev.detail;\n    if (detail instanceof KeyboardEvent && detail.key === 'Enter') {\n      detail.preventDefault();\n      detail.stopPropagation();\n    }\n    const selection = window.getSelection();\n    if (!selection || selection.rangeCount === 0) return;\n\n    const range = selection.getRangeAt(0);\n    const startNode = range.startContainer as HTMLElement;\n    const endNode = range.endContainer as HTMLElement;\n\n    // Obtém todas as linhas afetadas pela seleção\n    const selectedLines = new Set<HTMLElement>();\n\n    let currentNode = startNode.parentElement;\n    while (currentNode && currentNode !== endNode.parentElement) {\n      let element = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : (currentNode as HTMLElement);\n      if (element && element.classList.contains('line')) {\n        selectedLines.add(element);\n      }\n      currentNode = (currentNode.nextSibling as HTMLElement) || (currentNode.parentElement?.nextSibling as HTMLElement);\n    }\n\n    // Adiciona a última linha caso tenha sido ignorada\n    let endElement = endNode.nodeType === Node.TEXT_NODE ? endNode.parentElement : (endNode as HTMLElement);\n    while (endElement && !endElement.classList.contains('line') && endElement !== this.editor) {\n      endElement = endElement.parentElement;\n    }\n    if (endElement && endElement.classList.contains('line')) {\n      selectedLines.add(endElement);\n    }\n\n    // Remove a formatação de cada linha\n    selectedLines.forEach((line) => {\n      line.innerHTML = line.textContent; // Remove todas as tags HTML\n      line.style.textAlign = ''; // Remove o alinhamento\n    });\n\n    this.wrapSelectionLine('p', true);\n\n    // Limpa a seleção para evitar comportamento inesperado\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n\n  render() {\n    return (\n      <Host\n        class={{\n          [`rich-text`]: true,\n          [`rich-text-${this.positionBar}`]: true,\n        }}\n        style={{ height: this.height, maxHeight: this.maxHeight }}\n        tabindex=\"0\"\n        onMouseEnter={() => (this.insideComponent = true)}\n        onMouseLeave={() => (this.insideComponent = false)}\n      >\n        <div class=\"preview\">\n          <div\n            data-test={this.dataTest}\n            ref={(el) => this.refeditorElement(el)}\n            contentEditable=\"true\"\n            class=\"editor-uai-design-system\"\n            tabindex=\"0\"\n            onBlur={this.onBlur}\n            onFocus={this.onFocus}\n            onMouseUp={() => this.updateToolbarState()}\n            onKeyUp={() => this.updateToolbarState()}\n            onKeyDown={(ev) => this.onKeydown(ev)}\n            onInput={(ev) => this.onInput(ev)}\n            onPaste={this.handlePaste.bind(this)}\n          ></div>\n        </div>\n\n        <bds-grid\n          class={{\n            [`format-buttons`]: true,\n            [`format-buttons-active`]:\n              this.weightButton ||\n              this.italicButton ||\n              this.strikeThroughButton ||\n              this.underlineButton ||\n              this.linkButton ||\n              this.codeButton ||\n              this.alignmentButtons ||\n              this.listButtons ||\n              this.quoteButton ||\n              this.headingButtons ||\n              this.unstyledButton,\n          }}\n        >\n          <div class=\"accordion-header\">\n            <bds-grid ref={(el) => this.refButtonsListElement(el)} class=\"buttons-list\" flex-wrap=\"wrap\">\n              <div onFocus={(ev) => this.onFocusEditorBar(ev)} tabindex=\"1\" class=\"editor-bar\"></div>\n              {this.weightButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'bold')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomBoldActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'b')}\n                    icon-left=\"text-style-bold\"\n                    aria-label={`${termTranslate(this.language, 'bold')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.italicButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'italic')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomItalicActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'i')}\n                    icon-left=\"text-style-italic\"\n                    aria-label={`${termTranslate(this.language, 'italic')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.strikeThroughButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'strike')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomStrikeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'strike')}\n                    icon-left=\"text-style-strikethrough\"\n                    aria-label={`${termTranslate(this.language, 'strike')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.underlineButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'underline')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnderlineActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'u')}\n                    icon-left=\"text-style-underline\"\n                    aria-label={`${termTranslate(this.language, 'underline')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.linkButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'link')}`} position=\"top-center\">\n                  {this.buttomLinkActive ? (\n                    <bds-button\n                      variant=\"solid\"\n                      color=\"content\"\n                      size=\"short\"\n                      onBdsClick={(ev) => this.wrapSelection(ev, 'a')}\n                      icon-left=\"link\"\n                      aria-label={`${termTranslate(this.language, 'link')}`}\n                    ></bds-button>\n                  ) : (\n                    <bds-dropdown\n                      ref={(el) => this.refDropDownLinkElement(el)}\n                      activeMode=\"click\"\n                      position=\"bottom-left\"\n                    >\n                      <div slot=\"dropdown-activator\">\n                        <bds-button\n                          slot=\"dropdown-activator\"\n                          variant=\"text\"\n                          color=\"content\"\n                          size=\"short\"\n                          onBdsClick={(ev) => this.addSelectionLink(ev)}\n                          icon-left=\"link\"\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </div>\n                      <bds-grid padding=\"half\" alignItems=\"center\" gap=\"half\" slot=\"dropdown-content\">\n                        <bds-input\n                          ref={this.refInputSetLink}\n                          onBdsInput={(ev) => this.addLinkInput(ev.detail)}\n                          style={{ flexShrink: '99999' }}\n                          placeholder=\"adcione o link aqui\"\n                          onKeyDown={(ev) => this.createLinkKeyDown(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                        ></bds-input>\n                        <bds-button\n                          disabled={this.buttomLinkValidDisabled}\n                          icon-left=\"check\"\n                          onBdsClick={(ev) => this.createLink(ev)}\n                          tabindex={this.dropDownLink?.open ? '1' : '-1'}\n                          aria-label={`${termTranslate(this.language, 'link')}`}\n                        ></bds-button>\n                      </bds-grid>\n                    </bds-dropdown>\n                  )}\n                </bds-tooltip>\n              )}\n              {this.codeButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'code')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomCodeActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.wrapSelection(ev, 'code')}\n                    icon-left=\"code\"\n                    aria-label={`${termTranslate(this.language, 'code')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_left')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignLeftActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'left')}\n                    icon-left=\"align-left\"\n                    aria-label={`${termTranslate(this.language, 'align_left')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_center')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignCenterActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'center')}\n                    icon-left=\"align-center\"\n                    aria-label={`${termTranslate(this.language, 'align_center')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.alignmentButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'align_right')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomAlignRightActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.alignText(ev, 'right')}\n                    icon-left=\"align-right\"\n                    aria-label={`${termTranslate(this.language, 'align_right')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'unordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomUnorderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ul')}\n                    icon-left=\"unordered-list\"\n                    aria-label={`${termTranslate(this.language, 'unordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.listButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'ordered_list')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomOrderedListActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createList(ev, 'ol')}\n                    icon-left=\"ordered-list\"\n                    aria-label={`${termTranslate(this.language, 'ordered_list')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.quoteButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'quote')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomQuoteActive ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'blockquote')}\n                    icon-left=\"quote\"\n                    aria-label={`${termTranslate(this.language, 'quote')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h1')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH1Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h1')}\n                    icon-left=\"h-1\"\n                    aria-label={`${termTranslate(this.language, 'h1')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h2')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH2Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h2')}\n                    icon-left=\"h-2\"\n                    aria-label={`${termTranslate(this.language, 'h2')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h3')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH3Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h3')}\n                    icon-left=\"h-3\"\n                    aria-label={`${termTranslate(this.language, 'h3')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h4')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH4Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h4')}\n                    icon-left=\"h-4\"\n                    aria-label={`${termTranslate(this.language, 'h4')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h5')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH5Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h5')}\n                    icon-left=\"h-5\"\n                    aria-label={`${termTranslate(this.language, 'h5')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.headingButtons && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'h6')}`} position=\"top-center\">\n                  <bds-button\n                    variant={this.buttomH6Active ? 'solid' : 'text'}\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.createHeading(ev, 'h6')}\n                    icon-left=\"h-6\"\n                    aria-label={`${termTranslate(this.language, 'h6')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n              {this.unstyledButton && (\n                <bds-tooltip tooltip-text={`${termTranslate(this.language, 'clear_formatting')}`} position=\"top-center\">\n                  <bds-button\n                    variant=\"text\"\n                    color=\"content\"\n                    size=\"short\"\n                    onBdsClick={(ev) => this.clearFormatting(ev)}\n                    icon-left=\"unstyled\"\n                    aria-label={`${termTranslate(this.language, 'clear_formatting')}`}\n                  ></bds-button>\n                </bds-tooltip>\n              )}\n            </bds-grid>\n            <bds-button\n              id=\"buttonAccordion\"\n              variant={this.buttomAccordionActive ? 'solid' : 'text'}\n              class=\"arrow-down\"\n              color=\"content\"\n              size=\"short\"\n              onBdsClick={() => this.setheaderHeight()}\n              icon-left={\n                this.positionBar == 'top'\n                  ? this.buttomAccordionActive\n                    ? 'arrow-up'\n                    : 'arrow-down'\n                  : this.buttomAccordionActive\n                    ? 'arrow-down'\n                    : 'arrow-up'\n              }\n            ></bds-button>\n          </div>\n        </bds-grid>\n      </Host>\n    );\n  }\n}\n"], "mappings": "oGAAO,MAAMA,EAAU,CACrB,CACEC,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,UAAW,aACXC,KAAM,OACNC,KAAM,SACNC,WAAY,qBACZC,aAAc,oBACdC,YAAa,oBACbC,eAAgB,qBAChBC,aAAc,iBACdC,MAAO,UACPC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,iBAAkB,oBAClBC,OAAQ,aCrBL,MAAMC,EAAU,CACrB,CACEpB,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,UAAW,YACXC,KAAM,OACNC,KAAM,SACNC,WAAY,yBACZC,aAAc,oBACdC,YAAa,uBACbC,eAAgB,oBAChBC,aAAc,iBACdC,MAAO,OACPC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,iBAAkB,kBAClBC,OAAQ,aCrBL,MAAME,EAAU,CACrB,CACErB,KAAM,OACNC,OAAQ,SACRC,OAAQ,gBACRC,UAAW,YACXC,KAAM,OACNC,KAAM,OACNC,WAAY,aACZC,aAAc,eACdC,YAAa,cACbC,eAAgB,iBAChBC,aAAc,eACdC,MAAO,QACPC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,GAAI,YACJC,iBAAkB,mBAClBC,OAAQ,WCfL,MAAMG,EAAgB,CAACC,EAAiBC,KAC7C,IAAIC,EACJ,OAAQF,GACN,IAAK,QACHE,EAAY1B,EAAQ2B,KAAKC,GAASA,EAAKH,KACvC,MACF,IAAK,QACHC,EAAYL,EAAQM,KAAKC,GAASA,EAAKH,KACvC,MACF,IAAK,QACHC,EAAYJ,EAAQK,KAAKC,GAASA,EAAKH,KACvC,MACF,QACEC,EAAY1B,EAAQ2B,KAAKC,GAASA,EAAKH,KAE3C,OAAOC,CAAS,ECrBlB,MAAMG,EAAc,qnL,MCYPC,EAAQ,MALrB,WAAAC,CAAAC,G,8KAMUC,KAAkBC,mBAAiB,KACnCD,KAAmBE,oBAA6C,KAChEF,KAAMG,OAAiB,KACvBH,KAAYI,aAA4B,KAIvCJ,KAAgBK,iBAAa,MAC7BL,KAAkBM,mBAAa,MAC/BN,KAAkBO,mBAAa,MAC/BP,KAAqBQ,sBAAa,MAClCR,KAAgBS,iBAAa,MAC7BT,KAAgBU,iBAAa,MAC7BV,KAAuBW,wBAAa,KACpCX,KAAqBY,sBAAa,MAClCZ,KAAuBa,wBAAa,MACpCb,KAAsBc,uBAAa,MACnCd,KAAyBe,0BAAa,MACtCf,KAAuBgB,wBAAa,MACpChB,KAAiBiB,kBAAa,MAC9BjB,KAAckB,eAAa,MAC3BlB,KAAcmB,eAAa,MAC3BnB,KAAcoB,eAAa,MAC3BpB,KAAcqB,eAAa,MAC3BrB,KAAcsB,eAAa,MAC3BtB,KAAcuB,eAAa,MAC3BvB,KAAqBwB,sBAAa,MAClCxB,KAAYyB,aAAY,OACxBzB,KAAiB0B,kBAAa,MAC9B1B,KAAiB2B,kBAAgC,KACjD3B,KAAkB4B,mBAAmB,KACrC5B,KAAiB6B,kBAAY,KAC7B7B,KAAY8B,aAAY,QACxB9B,KAAiB+B,kBAAW,KAC5B/B,KAAegC,gBAAY,KAC3BhC,KAAeiC,gBAAa,MAI7BjC,KAAQkC,SAAe,QAIvBlC,KAAYmC,aAAa,KAIzBnC,KAAYoC,aAAa,KAIzBpC,KAAmBqC,oBAAa,KAIhCrC,KAAesC,gBAAa,KAI5BtC,KAAUuC,WAAa,KAIvBvC,KAAUwC,WAAa,KAIvBxC,KAAgByC,iBAAa,KAI7BzC,KAAW0C,YAAa,KAIxB1C,KAAW2C,YAAa,KAIxB3C,KAAc4C,eAAa,KAI3B5C,KAAc6C,eAAa,KAI3B7C,KAAM8C,OAAY,KAIlB9C,KAAS+C,UAAY,KAIrB/C,KAAWgD,YAAiB,MAK5BhD,KAAQiD,SAAY,KAoIpBjD,KAAAkD,sBAAyBC,IAC/BnD,KAAKC,mBAAqBkD,CAAE,EAEtBnD,KAAAoD,iBAAoBD,IAC1BnD,KAAKG,OAASgD,CAAE,EAEVnD,KAAAqD,uBAA0BF,IAChCnD,KAAKI,aAAe+C,CAAE,EAGhBnD,KAAAsD,gBAAmBH,IACzBnD,KAAKuD,aAAeJ,CAAE,EAGhBnD,KAAYwD,aAAG,KACrBxD,KAAKK,iBAAmB,MACxBL,KAAKM,mBAAqB,MAC1BN,KAAKO,mBAAqB,MAC1BP,KAAKQ,sBAAwB,MAC7BR,KAAKU,iBAAmB,MACxBV,KAAKS,iBAAmB,MACxBT,KAAKY,sBAAwB,MAC7BZ,KAAKa,wBAA0B,MAC/Bb,KAAKc,uBAAyB,MAC9Bd,KAAKe,0BAA4B,MACjCf,KAAKgB,wBAA0B,MAC/BhB,KAAKiB,kBAAoB,MACzBjB,KAAKkB,eAAiB,MACtBlB,KAAKmB,eAAiB,MACtBnB,KAAKoB,eAAiB,MACtBpB,KAAKqB,eAAiB,MACtBrB,KAAKsB,eAAiB,MACtBtB,KAAKuB,eAAiB,KAAK,EAGrBvB,KAAeyD,gBAAG,KACxBzD,KAAKwB,uBAAyBxB,KAAKwB,sBACnC,MAAMkC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,MAAMC,EAAQJ,EAAUK,WAAW,GAEnCL,EAAUM,kBACVN,EAAUO,SAASH,EAAM,EAGnB9D,KAAMkE,OAAG,KACflE,KAAKmD,GAAGgB,UAAUC,OAAO,UACzB,GAAIpE,KAAKiC,kBAAoB,MAAO,CAClCjC,KAAKwD,c,CAEPxD,KAAKqE,QAAQC,MAAM,EAGbtE,KAAOuE,QAAG,KAChBvE,KAAKmD,GAAGgB,UAAUK,IAAI,UACtBxE,KAAKyE,SAASH,MAAM,EAoCdtE,KAAA0E,UAAaC,IACnB,GAAIA,EAAMC,MAAQ,YAAa,CAC7B,MAAMlB,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,MAAMC,EAAQJ,EAAUK,WAAW,GACnC,MAAMc,EAAYf,EAAMgB,eAGxB,IAAIC,EAAeF,EAAUG,WAAaC,KAAKC,UAAYL,EAAUM,cAAiBN,EAEtF,MAAOE,IAAiBA,EAAaZ,UAAUiB,SAAS,SAAWL,IAAiB/E,KAAKG,OAAQ,CAC/F4E,EAAeA,EAAaI,a,CAI9B,GACEJ,GACAA,EAAaM,UAAY,cACzBN,EAAaZ,UAAUiB,SAAS,SAChCL,EAAaO,UAAUC,QAAU,EACjC,CACAZ,EAAMa,iBACNT,EAAaX,Q,EAGjB,GAAIpE,KAAKG,OAAOsF,YAAYF,SAAW,GAAKZ,EAAMC,MAAQ,YAAa,CACrED,EAAMa,iBACNxF,KAAKG,OAAOuF,UAAY,2BACxB1F,KAAK2F,gB,CAEP,IAAKhB,EAAMiB,SAAWjB,EAAMkB,UAAYlB,EAAMC,MAAQ,IAAK,CACzDD,EAAMa,iBACNb,EAAMmB,iB,EAgzBX,CA1hCC,gBAAAC,GACE,GAAI/F,KAAKG,OAAOuF,UAAUM,SAAW,GAAI,CACvChG,KAAKG,OAAOuF,UAAY,0B,CAE1B,GACE1F,KAAKmC,cACLnC,KAAKoC,cACLpC,KAAKqC,qBACLrC,KAAKsC,iBACLtC,KAAKuC,YACLvC,KAAKwC,YACLxC,KAAKyC,kBACLzC,KAAK0C,aACL1C,KAAK2C,aACL3C,KAAK4C,gBACL5C,KAAK6C,eACL,CACA7C,KAAKE,oBAAsBF,KAAKC,mBAAmBgG,qBACjD,eAEFjG,KAAKkG,gBAAgB,OACrBlG,KAAKG,OAAOgF,cAAcgB,MAAMrD,OAAS,mB,KACpC,CACL9C,KAAKG,OAAOgF,cAAcgB,MAAMrD,OAAS,M,EAenC,oBAAAsD,GACRC,YAAW,IAAMrG,KAAKkG,gBAAgBlG,KAAKwB,wBAAwB,I,CAI3D,4BAAA8E,GACRtG,KAAKkG,gBAAgBlG,KAAKwB,sB,CAGpB,kBAAA+E,GACN,MAAM7C,EAAYC,OAAOC,eACzB,MAAME,EAAQJ,EAAUK,WAAW,GACnC,MAAMyC,EAAiB1C,EAAM2C,wBAC7B,MAAMtB,EACJqB,EAAexB,WAAaC,KAAKC,UAAYsB,EAAerB,cAAiBqB,EAC/ExG,KAAK4B,mBAAqB8E,EAAgBvB,EAAe,4B,CAInD,eAAAe,CAAgBS,GACtB,MAAMC,EAA8B5G,KAAKE,oBAAoBqF,OAAS,GACtE,MAAMsB,EAAmB7G,KAAKC,mBAAmB6G,YACjD,MAAMC,EAAkB/G,KAAKmD,GAAG6D,cAAc,oBAC9C,GAAIH,EAAmBD,EAA6B,CAClDG,EAAgB5C,UAAUK,IAAI,S,KACzB,CACLuC,EAAgB5C,UAAUC,OAAO,S,CAEnC,MAAM6C,EAAcJ,EAAmB7G,KAAKE,oBAAoBqF,OAAUqB,EAC1E,MAAMM,EAAkBC,KAAKC,KAAKR,EAA8BC,GAChE,MAAMQ,EAAyBC,MAAMC,KAAKvH,KAAKE,qBAC/CmH,EAAuBG,MAAM,EAAGL,KAAKM,MAAMR,IAAaS,SAASC,IAC/DA,EAAQxD,UAAUK,IAAI,SAAS,IAEjC,GAAImC,EAAO,CACTU,EAAuBK,SAASC,IAC9BA,EAAQxD,UAAUK,IAAI,UACtBxE,KAAKG,OAAOgF,cAAcgB,MAAMrD,OAAS,eAAeoE,EAAkB,GAAK,OAAO,G,KAEnF,CACLG,EAAuBG,MAAML,KAAKM,MAAMR,IAAaS,SAASC,IAC5DA,EAAQxD,UAAUC,OAAO,UACzBpE,KAAKG,OAAOgF,cAAcgB,MAAMrD,OAAS,mBAAmB,G,EAMxD,yBAAA8E,CAA0BjB,GAClC,MAAMkB,EAAUlB,EAAMjH,KAAKiI,GAAYA,IAAA,MAAAA,SAAO,SAAPA,EAAStC,QAAQyC,gBACxD,MAAMC,EAAiBC,GAAQH,EAAQI,SAASD,GAChD,MAAME,EAAUvB,EAAMwB,MAAMhF,GAAOA,IAAE,MAAFA,SAAE,SAAFA,EAAIgB,UAAUiB,SAAS,UAC1DpF,KAAKK,iBAAmB0H,EAAc,KACtC/H,KAAKM,mBAAqByH,EAAc,KACxC/H,KAAKO,mBAAqBwH,EAAc,UACxC/H,KAAKQ,sBAAwBuH,EAAc,KAC3C/H,KAAKU,iBAAmBqH,EAAc,KACtC/H,KAAKS,iBAAmBsH,EAAc,QACtC/H,KAAKY,uBAAwBsH,IAAA,MAAAA,SAAA,SAAAA,EAAS/B,MAAMiC,aAAc,OAC1DpI,KAAKa,yBAA0BqH,IAAA,MAAAA,SAAA,SAAAA,EAAS/B,MAAMiC,aAAc,SAC5DpI,KAAKc,wBAAyBoH,IAAA,MAAAA,SAAA,SAAAA,EAAS/B,MAAMiC,aAAc,QAC3DpI,KAAKe,0BAA4B8G,EAAQ,KAAO,KAChD7H,KAAKgB,wBAA0B6G,EAAQ,KAAO,KAC9C7H,KAAKiB,kBAAoB8G,EAAc,cACvC/H,KAAKkB,eAAiB6G,EAAc,MACpC/H,KAAKmB,eAAiB4G,EAAc,MACpC/H,KAAKoB,eAAiB2G,EAAc,MACpC/H,KAAKqB,eAAiB0G,EAAc,MACpC/H,KAAKsB,eAAiByG,EAAc,MACpC/H,KAAKuB,eAAiBwG,EAAc,K,CA8D9B,OAAAM,CAAQC,GACdA,EAAG9C,iBACHxF,KAAKuI,iBAAiBjE,KAAKgE,GAE3BtI,KAAKwI,kBAAkBlE,KAAK,CAAEqC,MAAO3G,KAAKG,OAAOuF,YAEjD,MAAMhC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,MAAMC,EAAQJ,EAAUK,WAAW,GACnC,MAAM0E,EAAc3E,EAAMgB,eAG1B,GAAI2D,EAAYzD,WAAaC,KAAKyD,cAAiBD,EAA4BpD,UAAY,MAAO,CAChG,MAAMsD,EAAaF,EAEnB,MAAMG,EAAWC,SAASC,cAAc,KACxCF,EAASzE,UAAUK,IAAI,QACvBoE,EAASlD,UAAYiD,EAAWjD,UAEhCiD,EAAWI,WAAWC,aAAaJ,EAAUD,E,CAI/C3I,KAAKG,OAAO8I,iBAAiB,OAAOvB,SAASwB,IAC3C,MAAMC,EAAIN,SAASC,cAAc,KACjCK,EAAEhF,UAAUK,IAAI,QAChB2E,EAAEzD,UAAYwD,EAAIxD,UAClBwD,EAAIE,YAAYD,EAAE,G,CA0Cd,gBAAAE,CAAiBf,GACvB,MAAMgB,EAAYhB,EAAGiB,OACrB,MAAMC,EAAaF,EAAUG,mBAAmBzC,cAAc,cAC9D,MAAM0C,EAAiBF,EAAWG,WAAW3C,cAAc,UAC3D0C,EAAeE,QACf5J,KAAKwB,sBAAwB,I,CAIvB,cAAAmE,GACN,MAAM7B,EAAQ+E,SAASgB,cACvB,MAAMC,EAAMnG,OAAOC,eACnBE,EAAMiG,mBAAmB/J,KAAKG,QAC9B2D,EAAMkG,SAAS,OACfF,EAAI9F,kBACJ8F,EAAI7F,SAASH,E,CAGP,OAAAuB,CAAQ2C,EAAaH,GAC3B,MAAMlB,EAAQkB,EAAQnI,KAAKiI,GAAYA,IAAA,MAAAA,SAAO,SAAPA,EAAStC,QAAQyC,gBACxD,OAAOnB,EAAMsB,SAASD,E,CAGhB,aAAAiC,CAAc3B,EAAiBN,EAAa5J,GAClD,MAAM8L,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7DsF,EAAO1E,iBACP0E,EAAOpE,iB,CAGT,MAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU0G,YAAa,OAEjD,MAAMtG,EAAQJ,EAAUK,WAAW,GACnC,MAAMyC,EAAiB1C,EAAM2C,wBAC7B,MAAMtB,EACJqB,EAAexB,WAAaC,KAAKC,UAAYsB,EAAerB,cAAiBqB,EAE/E,MAAMqB,EAAUnB,EAAgBvB,EAAe,SAC/C,MAAMkF,EAAerK,KAAKqF,QAAQ2C,EAAKH,GAGvC,IAAIyC,EACJ,IAAIC,EAAkB,MAGtB,GAAIF,EAAc,CAChB,MAAMG,EAAa3C,EAAQM,MAAMhF,GAAOA,EAAGkC,QAAQyC,gBAAkBE,IACrE,GAAIwC,EAAY,CACd,MAAMC,EAASD,EAAWrF,cAC1B,MAAMuF,EAAkB5G,EAAM6G,WAAW3E,SAAWwE,EAAW/E,YAAYO,OAC3E,MAAM4E,EAAe9G,EAAM+G,YAAcL,EAAW/E,YAAYF,OAEhE,GAAImF,GAAmBD,EAAQ,CAE7B,MAAOD,EAAWM,WAAY,CAC5BL,EAAOM,aAAaP,EAAWM,WAAYN,E,CAE7CC,EAAOO,YAAYR,GACnB9G,EAAUM,kBACVN,EAAUO,SAASH,GACnB9D,KAAKuG,oB,MACA,GAAIqE,EAAc,CAEvBN,EAAUzB,SAASoC,yBACnB,MAAMC,EAAcrC,SAASsC,eAAe,KAC5Cb,EAAQc,YAAYF,GACpBX,EAAkB,KAClB,MAAMc,EAAWxC,SAASgB,cAC1BwB,EAASC,cAAcd,GACvBa,EAASE,YAAYf,GACrBa,EAASG,WAAWlB,GACpB5G,EAAUM,kBACVN,EAAUO,SAASoH,GACnBrL,KAAKuG,oB,KACA,CAEL7C,EAAUM,kBACVN,EAAUO,SAASH,GACnB9D,KAAKuG,oB,EAGT,M,CAGF,GAAIzC,EAAM2H,UAAW,CACnBnB,EAAUzB,SAASoC,yBACnB,MAAMC,EAAcrC,SAASsC,eAAe,KAC5Cb,EAAQc,YAAYF,GACpBX,EAAkB,I,KACb,CACLD,EAAUxG,EAAM4H,iB,CAIlBpB,EAAQrB,iBAAiB,KAAKvB,SAASC,IACrC,MAAOA,EAAQmD,WAAY,CACzBnD,EAAQoB,WAAWgC,aAAapD,EAAQmD,WAAYnD,E,CAEtDA,EAAQvD,QAAQ,IAIlB,MAAMuH,EAAU9C,SAASC,cAAcd,GACvC,GAAIA,IAAQ,KAAO5J,EAAM,CACvBuN,EAAQC,aAAa,OAAQxN,E,CAE/BuN,EAAQP,YAAYd,GACpBxG,EAAM0H,WAAWG,GAGjB3L,KAAKG,OAAO8I,iBAAiB,KAAKvB,SAASvE,IACzC,IAAKA,EAAGsC,YAAYO,QAAU7C,EAAG0I,SAAStG,SAAW,EAAG,CACtDpC,EAAGiB,Q,KAKP,MAAMiH,EAAWxC,SAASgB,cAC1B,GAAIU,EAAiB,CACnBc,EAASS,SAASH,EAAS,GAC3BN,EAASU,OAAOJ,EAAS,E,KACpB,CACLN,EAASW,eAAeL,EAAQb,YAAca,GAC9CN,EAASE,YAAYI,EAAQM,WAAaN,E,CAE5CjI,EAAUM,kBAEVN,EAAUO,SAASoH,GAEnBrL,KAAKuG,qBAGLvG,KAAKwI,kBAAkBlE,KAAK,CAAEqC,MAAO3G,KAAKG,OAAOuF,W,CAG3C,iBAAAwG,CAAkBlE,EAAamE,EAA6B,O,UAClE,MAAMzI,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU0G,YAAa,OAEjD,MAAMtG,EAAQJ,EAAUK,WAAW,GACnC,MAAMc,EAAYf,EAAMgB,eACxB,MAAMsH,EAAUtI,EAAMuI,aAGtB,MAAMC,EAAgB,IAAIC,IAE1B,IAAI9D,EAAc5D,EAAUM,cAC5B,MAAOsD,GAAeA,IAAgB2D,EAAQjH,cAAe,CAC3D,IAAIwC,EAAUc,EAAYzD,WAAaC,KAAKC,UAAYuD,EAAYtD,cAAiBsD,EACrF,GAAId,GAAWA,EAAQxD,UAAUiB,SAAS,QAAS,CACjDkH,EAAc9H,IAAImD,E,CAEpBc,EAAeA,EAAY+D,eAAgCC,EAAAhE,EAAYtD,iBAAa,MAAAsH,SAAA,SAAAA,EAAED,Y,CAIxF,IAAIE,EAAaN,EAAQpH,WAAaC,KAAKC,UAAYkH,EAAQjH,cAAiBiH,EAChF,MAAOM,IAAeA,EAAWvI,UAAUiB,SAAS,SAAWsH,IAAe1M,KAAKG,OAAQ,CACzFuM,EAAaA,EAAWvH,a,CAE1B,GAAIuH,GAAcA,EAAWvI,UAAUiB,SAAS,QAAS,CACvDkH,EAAc9H,IAAIkI,E,CAIpB,MAAMC,EAAgB,IAAIL,GAAeM,OAAOC,GAC9C7E,IAAQ,KAAO,MAAQ6E,EAAKxH,QAAQyC,gBAAkBE,IAGxD,MAAM8E,EAAgC,IAAIR,GAAe5M,KAAKyD,IAC5D,MAAM4J,EAAalE,SAASC,cAAc6D,EAAgB,IAAM3E,GAChE+E,EAAW5I,UAAUK,IAAI,QACzBuI,EAAWrH,UAAYvC,EAAGuC,UAC1BvC,EAAGiG,YAAY2D,GACf,OAAOA,CAAU,IAGnB,GAAIZ,EAAmB,CACrBnM,KAAK2B,kBAAoBmL,EAAepN,KAAKiI,IAAO,CAAQA,a,CAI9D,MAAM0D,EAAWxC,SAASgB,cAC1B,IAAImD,EAAWF,EAAe,GAAGb,UAGjC,IAAKe,EAAU,CACbA,EAAWnE,SAASsC,eAAe,IACnC2B,EAAe,GAAG1B,YAAY4B,E,CAIhC,MAAOA,GAAYA,EAAShI,WAAaC,KAAKC,UAAW,CACvD8H,EAAWA,EAASf,WAAae,C,CAInC3B,EAASS,SAASkB,IAAUC,EAAAD,EAASvH,eAAa,MAAAwH,SAAA,SAAAA,EAAA1H,SAAU,GAC5D8F,EAASU,OAAOiB,IAAUE,EAAAF,EAASvH,eAAa,MAAAyH,SAAA,SAAAA,EAAA3H,SAAU,GAC1D7B,EAAUM,kBACVN,EAAUO,SAASoH,GAEnBrL,KAAKuG,qBAELvG,KAAKwI,kBAAkBlE,KAAK,CAAEqC,MAAO3G,KAAKG,OAAOuF,W,CAI3C,SAAAyH,CAAU7E,EAAiB8E,GACjC,MAAMlD,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7DsF,EAAO1E,iBACP0E,EAAOpE,iB,CAET,MAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,MAAMC,EAAQJ,EAAUK,WAAW,GACnC,IAAIgB,EAAejB,EAAMgB,eAGzB,MAAOC,GAAgBA,IAAiB/E,KAAKG,OAAQ,CACnD,GAAI4E,EAAaC,WAAaC,KAAKyD,cAAgB3D,EAAaZ,UAAUiB,SAAS,QAAS,CAC1F,K,CAEFL,EAAeA,EAAaI,a,CAI9B,GAAIJ,GAAgBA,IAAiB/E,KAAKG,OAAQ,CAEhD,MAAMkN,EAAoBtI,EAA6BoB,MAAMiC,UAC7D,GAAIiF,IAAqBD,EAAW,CAEjCrI,EAA6BoB,MAAMiC,UAAY,E,KAC3C,CAEJrD,EAA6BoB,MAAMiC,UAAYgF,C,EAKpD1J,EAAUM,kBACVN,EAAUO,SAASH,GAEnB9D,KAAKuG,qBAELvG,KAAKwI,kBAAkBlE,KAAK,CAAEqC,MAAO3G,KAAKG,OAAOuF,W,CAI3C,aAAA4H,CAAchF,EAAiBiF,G,MACrC,MAAMrD,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7DsF,EAAO1E,iBACP0E,EAAOpE,iB,CAET,MAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU0G,YAAa,OACjDpK,KAAKkM,kBAAkBqB,EAAM,MAC7B,MAAMC,GAAgBf,EAAAzM,KAAK2B,kBAAkB,MAAI,MAAA8K,SAAA,SAAAA,EAAA9E,QACjD,MAAM8F,EAAcD,EAAcrI,cAAcuI,uBAChD,MAAMC,EAAaH,EAAcrI,cAAcsE,mBAC/C,MAAMgB,EAAS+C,EAAcrI,cAC7B,GAAIsF,EAAOpF,QAAQyC,gBAAkB,KAAM,CACzC9H,KAAK2B,kBAAkB+F,SAASkG,IAC9B,GAAIH,EAAa,CACfA,EAAYI,sBAAsB,WAAYD,EAAKjG,Q,MAC9C,GAAIgG,EAAY,CACrBA,EAAWE,sBAAsB,cAAeD,EAAKjG,Q,KAChD,CACL3H,KAAKG,OAAO0N,sBAAsB,aAAcD,EAAKjG,Q,KAGzD,GAAIL,MAAMC,KAAKkD,EAAOxE,qBAAqB,OAAOV,QAAU,EAAG,CAC7DkF,EAAOrG,Q,GAML,UAAA0J,CAAWxF,EAAiBiF,G,MAClC,MAAMrD,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7DsF,EAAO1E,iBACP0E,EAAOpE,iB,CAET,MAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAC9C,IAAK7D,KAAKG,OAAOiF,SAAS1B,EAAU0G,YAAa,OACjDpK,KAAKkM,kBAAkB,KAAM,MAC7B,MAAMsB,EAAgBxN,KAAK2B,kBAAkB,GAAGgG,QAChD,MAAMoG,GAAetB,EAAAzM,KAAK2B,kBAAkB3B,KAAK2B,kBAAkB4D,OAAS,MAAE,MAAAkH,SAAA,SAAAA,EAAE9E,QAChF,MAAMgE,EAAU9C,SAASC,cAAcyE,GACvC,MAAM9C,EAAS+C,EAAcrI,cAE7B,IAAKnF,KAAKgO,WAAWR,EAAeO,GAAe,CACjDtD,EAAOM,aAAaY,EAAS6B,GAC7BxN,KAAK2B,kBAAkB+F,SAASkG,IAC9BjC,EAAQP,YAAYwC,EAAKjG,QAAQ,G,KAE9B,CACL,MAAMsG,EAAqBxD,EAAOxE,qBAAqB,MACvD,MAAMiI,EAAa5G,MAAMC,KAAK0G,GAAoBvO,KAAKiI,IAAO,CAAQA,cAEtE,GAAIuG,EAAW3I,QAAUvF,KAAK2B,kBAAkB4D,OAAQ,CACtD,GAAIgI,IAAS9C,EAAOpF,QAAQyC,cAAe,CACzC6D,EAAQjG,UAAY+E,EAAO/E,UAC3B+E,EAAO1B,WAAWC,aAAa2C,EAASlB,E,KACnC,CACLzK,KAAK2B,kBAAkB+F,SAASkG,IAC9B,MAAM/F,EAAU4C,EAAOtF,cAAcE,QAAQyC,gBAAkB,KAAO,KAAO,IAC7E,MAAMiF,EAAalE,SAASC,cAAcjB,GAC1CkF,EAAW5I,UAAUK,IAAI,QACzBuI,EAAWrH,UAAYkI,EAAKjG,QAAQjC,UACpC,GAAI+E,EAAOtF,cAAcE,QAAQyC,gBAAkB,KAAM,CACvD2C,EAAOtF,cAAc0I,sBAAsB,WAAYd,E,KAClD,CACLtC,EAAOiD,uBAAuBG,sBAAsB,WAAYd,E,CAElEtC,EAAOO,YAAY4C,EAAKjG,QAAQ,IAElC8C,EAAOrG,Q,MAEJ,CAELoJ,EAAcE,uBAAuBG,sBAAsB,YAAalC,GACxE3L,KAAK2B,kBAAkB+F,SAASkG,IAC9BjC,EAAQP,YAAYwC,EAAKjG,QAAQ,G,GAMjC,gBAAAwG,CAAiB7F,GACvB,MAAM4B,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7D5E,KAAKI,aAAagO,S,CAEpBpO,KAAKG,OAAOyJ,QACZ,MAAMlG,EAAYC,OAAOC,eACzB5D,KAAK+B,kBAAoB2B,EAAUK,WAAW,GAC9C,MAAM2F,EAAiB1J,KAAKuD,aAAaoG,WAAW3C,cAAc,2BAClE0C,EAAeE,O,CAGT,YAAAyE,CAAa/F,GACnBA,EAAG9C,iBACH,MAAM8I,EAAQhG,EAAGiB,OACjBvJ,KAAKgC,gBAAkBsM,EAAM3H,MAC7B,GAAI3G,KAAKgC,gBAAgBuD,OAAS,EAAG,CACnCvF,KAAKW,wBAA0B,K,KAC1B,CACLX,KAAKW,wBAA0B,I,EAI3B,iBAAA4N,CAAkBjG,GACxB,GAAIA,EAAG1D,KAAO,QAAS,CACrB5E,KAAKwO,WAAWlG,E,EAIZ,UAAAkG,CAAWlG,GACjBA,EAAG9C,iBACH,MAAM9B,EAAYC,OAAOC,eACzBF,EAAUM,kBACVN,EAAUO,SAASjE,KAAK+B,mBACxB/B,KAAKiK,cAAc3B,EAAI,IAAKtI,KAAKgC,iBACjC,GAAIhC,KAAKI,aAAc,CACrBJ,KAAKI,aAAaqO,U,EAId,UAAAT,CAAWU,EAAwBC,GACzC,MAAMC,EACJF,EAAUvJ,cAAcE,QAAQyC,gBAAkB,MAAQ4G,EAAUvJ,cAAcE,QAAQyC,gBAAkB,KAC9G,MAAM+G,EACJF,EAASxJ,cAAcE,QAAQyC,gBAAkB,MAAQ6G,EAASxJ,cAAcE,QAAQyC,gBAAkB,KAC5G,OAAO8G,GAAkBC,C,CAInB,WAAAC,CAAYnK,GAClBA,EAAMa,iBACNb,EAAMmB,kBAEN,MAAMiJ,EAAgBpK,EAAMoK,eAAkBpL,OAAeoL,cAC7D,MAAMC,EAAYD,EAAcE,QAAQ,cAExC,MAAMvL,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,MAAMC,EAAQJ,EAAUK,WAAW,GACnC,MAAMyC,EAAiB1C,EAAM2C,wBAC7B,MAAMtB,EACJqB,EAAexB,WAAaC,KAAKC,UAAYsB,EAAerB,cAAiBqB,EAC/E,GAAIrB,EAAchB,UAAUiB,SAAS,QAAS,CAC5CD,EAAcf,Q,CAGhBN,EAAMoL,iBAGN,MAAMC,EAAWtG,SAASoC,yBAC1B+D,EAAUI,MAAM,MAAM1H,SAASmF,IAC7B,GAAIA,EAAK7G,OAAQ,CACf,MAAMmD,EAAIN,SAASC,cAAc,KACjCK,EAAEhF,UAAUK,IAAI,QAChB2E,EAAE1D,YAAcoH,EAAK7G,OACrBmJ,EAAS/D,YAAYjC,E,KAKzBrF,EAAM0H,WAAW2D,GAGjBzL,EAAUM,kBACVN,EAAUO,SAASH,E,CAIb,eAAAuL,CAAgB/G,G,MACtB,MAAM4B,EAAS5B,EAAG4B,OAClB,GAAIA,aAAkBC,eAAiBD,EAAOtF,MAAQ,QAAS,CAC7DsF,EAAO1E,iBACP0E,EAAOpE,iB,CAET,MAAMpC,EAAYC,OAAOC,eACzB,IAAKF,GAAaA,EAAUG,aAAe,EAAG,OAE9C,MAAMC,EAAQJ,EAAUK,WAAW,GACnC,MAAMc,EAAYf,EAAMgB,eACxB,MAAMsH,EAAUtI,EAAMuI,aAGtB,MAAMC,EAAgB,IAAIC,IAE1B,IAAI9D,EAAc5D,EAAUM,cAC5B,MAAOsD,GAAeA,IAAgB2D,EAAQjH,cAAe,CAC3D,IAAIwC,EAAUc,EAAYzD,WAAaC,KAAKC,UAAYuD,EAAYtD,cAAiBsD,EACrF,GAAId,GAAWA,EAAQxD,UAAUiB,SAAS,QAAS,CACjDkH,EAAc9H,IAAImD,E,CAEpBc,EAAeA,EAAY+D,eAAgCC,EAAAhE,EAAYtD,iBAAa,MAAAsH,SAAA,SAAAA,EAAED,Y,CAIxF,IAAIE,EAAaN,EAAQpH,WAAaC,KAAKC,UAAYkH,EAAQjH,cAAiBiH,EAChF,MAAOM,IAAeA,EAAWvI,UAAUiB,SAAS,SAAWsH,IAAe1M,KAAKG,OAAQ,CACzFuM,EAAaA,EAAWvH,a,CAE1B,GAAIuH,GAAcA,EAAWvI,UAAUiB,SAAS,QAAS,CACvDkH,EAAc9H,IAAIkI,E,CAIpBJ,EAAc5E,SAASmF,IACrBA,EAAKnH,UAAYmH,EAAKpH,YACtBoH,EAAK1G,MAAMiC,UAAY,EAAE,IAG3BpI,KAAKkM,kBAAkB,IAAK,MAG5BxI,EAAUM,kBACVN,EAAUO,SAASH,E,CAGrB,MAAAwL,G,QACE,OACEC,EAACC,EAAI,CAAA5K,IAAA,2CACH6K,MAAO,CACL,CAAC,aAAc,KACf,CAAC,aAAazP,KAAKgD,eAAgB,MAErCmD,MAAO,CAAErD,OAAQ9C,KAAK8C,OAAQC,UAAW/C,KAAK+C,WAC9C2M,SAAS,IACTC,aAAc,IAAO3P,KAAKiC,gBAAkB,KAC5C2N,aAAc,IAAO5P,KAAKiC,gBAAkB,OAE5CsN,EAAK,OAAA3K,IAAA,2CAAA6K,MAAM,WACTF,EACa,OAAA3K,IAAA,uDAAA5E,KAAKiD,SAChB4M,IAAM1M,GAAOnD,KAAKoD,iBAAiBD,GACnC2M,gBAAgB,OAChBL,MAAM,2BACNC,SAAS,IACTxL,OAAQlE,KAAKkE,OACbK,QAASvE,KAAKuE,QACdwL,UAAW,IAAM/P,KAAKuG,qBACtByJ,QAAS,IAAMhQ,KAAKuG,qBACpB0J,UAAY3H,GAAOtI,KAAK0E,UAAU4D,GAClCD,QAAUC,GAAOtI,KAAKqI,QAAQC,GAC9B4H,QAASlQ,KAAK8O,YAAYqB,KAAKnQ,SAInCuP,EAAA,YAAA3K,IAAA,2CACE6K,MAAO,CACL,CAAC,kBAAmB,KACpB,CAAC,yBACCzP,KAAKmC,cACLnC,KAAKoC,cACLpC,KAAKqC,qBACLrC,KAAKsC,iBACLtC,KAAKuC,YACLvC,KAAKwC,YACLxC,KAAKyC,kBACLzC,KAAK0C,aACL1C,KAAK2C,aACL3C,KAAK4C,gBACL5C,KAAK6C,iBAGT0M,EAAK,OAAA3K,IAAA,2CAAA6K,MAAM,oBACTF,EAAA,YAAA3K,IAAA,2CAAUiL,IAAM1M,GAAOnD,KAAKkD,sBAAsBC,GAAKsM,MAAM,eAAc,YAAW,QACpFF,EAAK,OAAA3K,IAAA,2CAAAL,QAAU+D,GAAOtI,KAAKqJ,iBAAiBf,GAAKoH,SAAS,IAAID,MAAM,eACnEzP,KAAKmC,cACJoN,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,UAAWkO,SAAS,cAC7Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKK,iBAAmB,QAAU,OAC3CiQ,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,KAAI,YACrC,kBAAiB,aACf,GAAGhJ,EAAcU,KAAKkC,SAAU,aAIjDlC,KAAKoC,cACJmN,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,YAAakO,SAAS,cAC/Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKM,mBAAqB,QAAU,OAC7CgQ,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,KAAI,YACrC,oBAAmB,aACjB,GAAGhJ,EAAcU,KAAKkC,SAAU,eAIjDlC,KAAKqC,qBACJkN,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,YAAakO,SAAS,cAC/Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKO,mBAAqB,QAAU,OAC7C+P,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,UAAS,YAC1C,2BAA0B,aACxB,GAAGhJ,EAAcU,KAAKkC,SAAU,eAIjDlC,KAAKsC,iBACJiN,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,eAAgBkO,SAAS,cAClFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKQ,sBAAwB,QAAU,OAChD8P,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,KAAI,YACrC,uBAAsB,aACpB,GAAGhJ,EAAcU,KAAKkC,SAAU,kBAIjDlC,KAAKuC,YACJgN,EAAA,eAAA3K,IAAA,0DAA2B,GAAGtF,EAAcU,KAAKkC,SAAU,UAAWkO,SAAS,cAC5EpQ,KAAKU,iBACJ6O,EAAA,cACEc,QAAQ,QACRC,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,KAAI,YACrC,OACE,gBAAGhJ,EAAcU,KAAKkC,SAAU,YAG9CqN,EAAA,gBACEM,IAAM1M,GAAOnD,KAAKqD,uBAAuBF,GACzCsN,WAAW,QACXL,SAAS,eAETb,EAAK,OAAAmB,KAAK,sBACRnB,EAAA,cACEmB,KAAK,qBACLL,QAAQ,OACRC,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKmO,iBAAiB7F,GAAG,YACnC,OACE,gBAAGhJ,EAAcU,KAAKkC,SAAU,aAGhDqN,EAAA,YAAUoB,QAAQ,OAAOC,WAAW,SAASC,IAAI,OAAOH,KAAK,oBAC3DnB,EAAA,aACEM,IAAK7P,KAAKsD,gBACVwN,WAAaxI,GAAOtI,KAAKqO,aAAa/F,EAAG4B,QACzC/D,MAAO,CAAE4K,WAAY,SACrB7F,YAAY,sBACZ+E,UAAY3H,GAAOtI,KAAKuO,kBAAkBjG,GAC1CoH,WAAUjD,EAAAzM,KAAKI,gBAAY,MAAAqM,SAAA,SAAAA,EAAEuE,MAAO,IAAM,OAE5CzB,EACE,cAAA0B,SAAUjR,KAAKW,wBAAuB,YAC5B,QACV6P,WAAalI,GAAOtI,KAAKwO,WAAWlG,GACpCoH,WAAUzC,EAAAjN,KAAKI,gBAAY,MAAA6M,SAAA,SAAAA,EAAE+D,MAAO,IAAM,KAAI,aAClC,GAAG1R,EAAcU,KAAKkC,SAAU,eAOvDlC,KAAKwC,YACJ+M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,UAAWkO,SAAS,cAC7Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKS,iBAAmB,QAAU,OAC3C6P,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKiK,cAAc3B,EAAI,QAAO,YACxC,OAAM,aACJ,GAAGhJ,EAAcU,KAAKkC,SAAU,aAIjDlC,KAAKyC,kBACJ8M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,gBAAiBkO,SAAS,cACnFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKY,sBAAwB,QAAU,OAChD0P,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKmN,UAAU7E,EAAI,QAAO,YACpC,aAAY,aACV,GAAGhJ,EAAcU,KAAKkC,SAAU,mBAIjDlC,KAAKyC,kBACJ8M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,kBAAmBkO,SAAS,cACrFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKa,wBAA0B,QAAU,OAClDyP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKmN,UAAU7E,EAAI,UAAS,YACtC,eAAc,aACZ,GAAGhJ,EAAcU,KAAKkC,SAAU,qBAIjDlC,KAAKyC,kBACJ8M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,iBAAkBkO,SAAS,cACpFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKc,uBAAyB,QAAU,OACjDwP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKmN,UAAU7E,EAAI,SAAQ,YACrC,cAAa,aACX,GAAGhJ,EAAcU,KAAKkC,SAAU,oBAIjDlC,KAAK0C,aACJ6M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,oBAAqBkO,SAAS,cACvFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKe,0BAA4B,QAAU,OACpDuP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAK8N,WAAWxF,EAAI,MAAK,YACnC,iBAAgB,aACd,GAAGhJ,EAAcU,KAAKkC,SAAU,uBAIjDlC,KAAK0C,aACJ6M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,kBAAmBkO,SAAS,cACrFb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKgB,wBAA0B,QAAU,OAClDsP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAK8N,WAAWxF,EAAI,MAAK,YACnC,eAAc,aACZ,GAAGhJ,EAAcU,KAAKkC,SAAU,qBAIjDlC,KAAK2C,aACJ4M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,WAAYkO,SAAS,cAC9Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKiB,kBAAoB,QAAU,OAC5CqP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,cAAa,YAC9C,QAAO,aACL,GAAGhJ,EAAcU,KAAKkC,SAAU,cAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKkB,eAAiB,QAAU,OACzCoP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKmB,eAAiB,QAAU,OACzCmP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKoB,eAAiB,QAAU,OACzCkP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKqB,eAAiB,QAAU,OACzCiP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKsB,eAAiB,QAAU,OACzCgP,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK4C,gBACJ2M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,QAASkO,SAAS,cAC3Eb,EACE,cAAA3K,IAAA,2CAAAyL,QAASrQ,KAAKuB,eAAiB,QAAU,OACzC+O,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKsN,cAAchF,EAAI,MAAK,YACtC,MAAK,aACH,GAAGhJ,EAAcU,KAAKkC,SAAU,WAIjDlC,KAAK6C,gBACJ0M,EAA2B,eAAA3K,IAAA,6DAAGtF,EAAcU,KAAKkC,SAAU,sBAAuBkO,SAAS,cACzFb,EAAA,cAAA3K,IAAA,2CACEyL,QAAQ,OACRC,MAAM,UACNC,KAAK,QACLC,WAAalI,GAAOtI,KAAKqP,gBAAgB/G,GAAG,YAClC,WAAU,aACR,GAAGhJ,EAAcU,KAAKkC,SAAU,0BAKpDqN,EACE,cAAA3K,IAAA,2CAAAsM,GAAG,kBACHb,QAASrQ,KAAKwB,sBAAwB,QAAU,OAChDiO,MAAM,aACNa,MAAM,UACNC,KAAK,QACLC,WAAY,IAAMxQ,KAAKyD,kBAAiB,YAEtCzD,KAAKgD,aAAe,MAChBhD,KAAKwB,sBACH,WACA,aACFxB,KAAKwB,sBACH,aACA,e", "ignoreList": []}