{"version": 3, "names": ["sidebarCss", "Sidebar", "exports", "class_1", "hostRef", "_this", "this", "InnerSpacing", "isOpen", "type", "sidebarPosition", "margin", "width", "dtOutzone", "dtButtonClose", "background", "listiner", "event", "key", "onClickCloseButtom", "prototype", "toggle", "isOpenChanged", "newValue", "bdsToggle", "emit", "value", "document", "addEventListener", "removeEventListener", "componentWillLoad", "hasFooterSlot", "hostElement", "querySelector", "hasHeaderSlot", "render", "h", "class", "_a", "sidebar_dialog", "is_open", "concat", "outzone", "onClick", "_b", "sidebar", "style", "header", "content", "name", "closeButton", "icon", "size", "variant", "dataTest", "body", "element_scrolled", "footer"], "sources": ["src/components/sidebar/sidebar.scss?tag=bds-sidebar&encapsulation=shadow", "src/components/sidebar/sidebar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.sidebar_dialog {\n  width: 100%;\n  height: 100vh;\n  box-shadow: $shadow-2;\n  background-color: rgba(0, 0, 0, 0.7);\n  opacity: 0;\n  visibility: hidden;\n  transition: opacity 0.3s ease-in-out;\n  display: none;\n\n  &.type_over {\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: $zindex-modal-overlay;\n    .sidebar {\n      z-index: $zindex-modal;\n    }\n  }\n\n  &.type_fixed {\n    width: fit-content;\n    position: relative;\n    height: 100%;\n    box-shadow: none;\n  }\n\n  &.is_open {\n    display: flex;\n    opacity: 1;\n    visibility: visible;\n  }\n\n  & .outzone {\n    order: 2;\n    width: 100%;\n    height: 100vh;\n  }\n  .sidebar {\n    width: 360px;\n    transition: all 0.5s ease-in-out;\n    display: flex;\n    flex-direction: column;\n    background-color: $color-surface-2;\n    flex-shrink: 0;\n\n    &.position_left {\n      order: 1;\n    }\n\n    &.position_right {\n      order: 3;\n    }\n\n    &.background_surface-1 {\n      background-color: $color-surface-1;\n    }\n    &.background_surface-2 {\n      background-color: $color-surface-2;\n    }\n    &.background_surface-3 {\n      background-color: $color-surface-3;\n    }\n    &.background_surface-4 {\n      background-color: $color-surface-4;\n    }\n\n    &.type_fixed {\n      width: 288px;\n    }\n\n    & .header {\n      display: flex;\n      align-content: center;\n      justify-content: space-between;\n      padding: 24px;\n\n      & .content {\n        display: flex;\n        width: 100%;\n        align-items: center;\n        position: relative;\n        color: $color-content-default;\n\n        ::slotted(*) {\n          width: 100%;\n        }\n      }\n      & .closeButton {\n        border-radius: 8px;\n        contain: inherit;\n        -webkit-transition:\n          height 0.5s,\n          all 0.3s;\n        -moz-transition:\n          height 0.5s,\n          all 0.3s;\n        transition:\n          height 0.5s,\n          all 0.3s;\n        z-index: 1;\n        cursor: pointer;\n        color: $color-content-default;\n      }\n    }\n\n    & .body {\n      position: relative;\n      flex: 1 1 auto;\n      & .content {\n        position: absolute;\n        inset: 0;\n        z-index: 999999;\n        overflow-y: overlay;\n        overflow-x: clip;\n        @include custom-scroll;\n      }\n      & .margin {\n        padding: 8px 24px;\n      }\n    }\n\n    & .footer {\n      & .content {\n        padding: 24px;\n\n        ::slotted(*) {\n          height: 40px;\n          overflow: hidden;\n        }\n      }\n    }\n    &.is_open {\n      &.position_left {\n        right: calc(100% - 360px);\n      }\n      &.position_right {\n        left: calc(100% - 360px);\n      }\n    }\n  }\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Method, Watch, Element } from '@stencil/core';\n\nexport type sidebarPosition = 'left' | 'right';\nexport type sidebarType = 'over' | 'fixed';\nexport type sidebarBackground = 'surface-1' | 'surface-2' | 'surface-3' | 'surface-4';\n\n@Component({\n  tag: 'bds-sidebar',\n  styleUrl: 'sidebar.scss',\n  shadow: true,\n})\nexport class Sidebar {\n  private hasFooterSlot: boolean;\n  private hasHeaderSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() InnerSpacing?: number = 0;\n\n  /**;\n   * isOpen. Used to open sidebar.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = this.type === 'fixed' ? true : false;\n\n  /**\n   * sidebar position. Used to position the sidebar. Either on the left or on the right.\n   */\n  @Prop() sidebarPosition?: sidebarPosition = 'left';\n\n  /**\n   * sidebar type. Used to define how open.\n   */\n  @Prop() type?: sidebarType = 'over';\n\n  /**\n   * If true, a lateral margin will apear in the content.\n   */\n  @Prop() margin?: boolean = true;\n  /**\n   * Width, number to define sidebar width.\n   */\n  @Prop() width?: number = 360;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtOutzone is the data-test to button close.\n   */\n  @Prop() dtOutzone?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Width, number to define sidebar width.\n   */\n  @Prop() background?: sidebarBackground = 'surface-2';\n\n  /**\n   * Emitted when the isOpen has changed.\n   */\n  @Event() bdsToggle!: EventEmitter;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(newValue: boolean): void {\n    this.bdsToggle.emit({ value: newValue });\n    if (newValue === true) {\n      document.addEventListener('keyup', this.listiner, false);\n    } else {\n      document.removeEventListener('keyup', this.listiner, false);\n    }\n  }\n\n  componentWillLoad() {\n    this.hasFooterSlot = !!this.hostElement.querySelector('[slot=\"footer\"]');\n    this.hasHeaderSlot = !!this.hostElement.querySelector('[slot=\"header\"]');\n  }\n\n  private listiner = (event) => {\n    if (event.key == 'Escape' && this.type !== 'fixed') {\n      this.isOpen = false;\n    }\n  };\n\n  private onClickCloseButtom = () => {\n    this.isOpen = false;\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          sidebar_dialog: true,\n          is_open: this.isOpen,\n          [`type_${this.type}`]: true,\n        }}\n      >\n        {this.type === 'over' ? (\n          <div class={{ outzone: true }} onClick={() => this.onClickCloseButtom()} data-test={this.dtOutzone}></div>\n        ) : (\n          ''\n        )}\n        <div\n          class={{\n            sidebar: true,\n            is_open: this.isOpen,\n            [`type_${this.type}`]: true,\n            [`position_${this.sidebarPosition}`]: true,\n            [`background_${this.background}`]: true,\n          }}\n          style={{ width: `${this.width < 144 ? 144 : this.width}px` }}\n        >\n          {this.hasHeaderSlot && (\n            <div class={{ header: true }}>\n              <div class={{ content: true }}>\n                <slot name=\"header\" />\n              </div>\n              {this.type === 'fixed' ? (\n                ''\n              ) : (\n                <bds-button-icon\n                  class={{\n                    closeButton: true,\n                  }}\n                  icon=\"close\"\n                  size=\"short\"\n                  variant=\"secondary\"\n                  onClick={() => this.onClickCloseButtom()}\n                  dataTest={this.dtButtonClose}\n                ></bds-button-icon>\n              )}\n            </div>\n          )}\n\n          <div class={{ body: true }}>\n            <div class={{ content: true, element_scrolled: true, margin: this.margin }}>\n              <slot name=\"body\" />\n            </div>\n          </div>\n          {this.hasFooterSlot && (\n            <div class={{ footer: true }}>\n              <div class={{ content: true }}>\n                <slot name=\"footer\" />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": "wmDAAA,IAAMA,EAAa,wtG,ICWNC,EAAOC,EAAA,yBALpB,SAAAC,EAAAC,GAAA,IAAAC,EAAAC,K,6CAWWA,KAAYC,aAAY,EAKOD,KAAAE,OAAmBF,KAAKG,OAAS,QAAU,KAAO,MAKlFH,KAAeI,gBAAqB,OAKpCJ,KAAIG,KAAiB,OAKrBH,KAAMK,OAAa,KAInBL,KAAKM,MAAY,IAMjBN,KAASO,UAAY,KAMrBP,KAAaQ,cAAY,KAIzBR,KAAUS,WAAuB,YA2BjCT,KAAAU,SAAW,SAACC,GAClB,GAAIA,EAAMC,KAAO,UAAYb,EAAKI,OAAS,QAAS,CAClDJ,EAAKG,OAAS,K,CAElB,EAEQF,KAAkBa,mBAAG,WAC3Bd,EAAKG,OAAS,KAChB,CAgED,CA3FOL,EAAAiB,UAAAC,OAAN,W,qFACEf,KAAKE,QAAUF,KAAKE,O,iBAItBL,EAAAiB,UAAAE,cAAA,SAAcC,GACZjB,KAAKkB,UAAUC,KAAK,CAAEC,MAAOH,IAC7B,GAAIA,IAAa,KAAM,CACrBI,SAASC,iBAAiB,QAAStB,KAAKU,SAAU,M,KAC7C,CACLW,SAASE,oBAAoB,QAASvB,KAAKU,SAAU,M,GAIzDb,EAAAiB,UAAAU,kBAAA,WACExB,KAAKyB,gBAAkBzB,KAAK0B,YAAYC,cAAc,mBACtD3B,KAAK4B,gBAAkB5B,KAAK0B,YAAYC,cAAc,kB,EAaxD9B,EAAAiB,UAAAe,OAAA,W,QAAA,IAAA9B,EAAAC,KACE,OACE8B,EACE,OAAAlB,IAAA,2CAAAmB,OAAKC,EAAA,CACHC,eAAgB,KAChBC,QAASlC,KAAKE,QACd8B,EAAC,QAAAG,OAAQnC,KAAKG,OAAS,K,IAGxBH,KAAKG,OAAS,OACb2B,EAAK,OAAAC,MAAO,CAAEK,QAAS,MAAQC,QAAS,WAAM,OAAAtC,EAAKc,oBAAL,EAAsC,YAAAb,KAAKO,YAAiB,GAI5GuB,EAAA,OAAAlB,IAAA,2CACEmB,OAAKO,EAAA,CACHC,QAAS,KACTL,QAASlC,KAAKE,QACdoC,EAAC,QAAAH,OAAQnC,KAAKG,OAAS,KACvBmC,EAAC,YAAAH,OAAYnC,KAAKI,kBAAoB,KACtCkC,EAAC,cAAAH,OAAcnC,KAAKS,aAAe,K,GAErC+B,MAAO,CAAElC,MAAO,GAAA6B,OAAGnC,KAAKM,MAAQ,IAAM,IAAMN,KAAKM,MAAK,QAErDN,KAAK4B,eACJE,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEU,OAAQ,OACpBX,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEW,QAAS,OACrBZ,EAAA,QAAAlB,IAAA,2CAAM+B,KAAK,YAEZ3C,KAAKG,OAAS,QAAO,GAGpB2B,EAAA,mBACEC,MAAO,CACLa,YAAa,MAEfC,KAAK,QACLC,KAAK,QACLC,QAAQ,YACRV,QAAS,WAAM,OAAAtC,EAAKc,oBAAL,EACfmC,SAAUhD,KAAKQ,iBAMvBsB,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEkB,KAAM,OAClBnB,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEW,QAAS,KAAMQ,iBAAkB,KAAM7C,OAAQL,KAAKK,SAChEyB,EAAA,QAAAlB,IAAA,2CAAM+B,KAAK,WAGd3C,KAAKyB,eACJK,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEoB,OAAQ,OACpBrB,EAAA,OAAAlB,IAAA,2CAAKmB,MAAO,CAAEW,QAAS,OACrBZ,EAAM,QAAAlB,IAAA,2CAAA+B,KAAK,c,0PAzIP,I", "ignoreList": []}