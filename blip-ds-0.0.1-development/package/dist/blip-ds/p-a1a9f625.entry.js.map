{"version": 3, "names": ["listCss", "List", "constructor", "hostRef", "this", "itemListElement", "typeList", "chagedOptions", "event", "detail", "checked", "value", "setSelectedCheckbox", "setSelectedSwitch", "onClickActionsButtons", "bdsClickActionsButtons", "emit", "componentWillLoad", "data", "dataChanged", "componentWillRender", "updateData", "setitemListElement", "componentDidRender", "internalDataChanged", "valueChanged", "setSelectedRadio", "element", "shadowRoot", "querySelectorAll", "getElementsByTagName", "i", "length", "addEventListener", "internalData", "JSON", "parse", "itemList", "itens", "Array", "from", "radios", "filter", "item", "construct", "text", "_a", "secondaryText", "_b", "avatar<PERSON><PERSON>", "_c", "avatar<PERSON><PERSON><PERSON><PERSON>", "_d", "_e", "bdsListRadioChange", "checkboxs", "result", "map", "term", "bdsListCheckboxChange", "Switch", "bdsListSwitchChange", "render", "h", "Host", "key", "class", "list", "idx", "icon", "chips", "actionsButtons", "onBdsChecked", "ev", "onBdsClickActionButtom", "dataTest"], "sources": ["src/components/list/list.scss?tag=bds-list&encapsulation=shadow", "src/components/list/list.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { Data } from './list-interface';\n\nexport type TypeList = 'checkbox' | 'radio' | 'switch' | 'default';\n\n@Component({\n  tag: 'bds-list',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class List {\n  private itemListElement?: HTMLCollectionOf<HTMLBdsListItemElement> | NodeListOf<HTMLBdsListItemElement> = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() internalData: Data[];\n  /**\n   * Typelist. Used to .\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * The value of the selected radio\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string;\n\n  /**\n   * The Data of the list\n   * Should be passed this way:\n   * data='[{\"value\": \"01\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"true\",\"icon\": \"settings-builder\"}, {\"value\": \"02\",\"text\": \"Text\",\"secondaryText\": \"Secondary Text\",\"avatarName\": \"\",\"avatarThumbnail\": \"\",\"checked\"=\"false\",\"icon\": \"settings-builder\",}]'\n   * Data can also be passed as child by using bds-list-item component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true, reflect: true }) data?: string | Data[];\n\n  /**\n   * Emitted when the value checkboxes has changed because of a click event.\n   */\n  @Event() bdsListCheckboxChange!: EventEmitter;\n  /**\n   * Emitted when the value radios has changed because of a click event.\n   */\n  @Event() bdsListRadioChange!: EventEmitter;\n  /**\n   * Emitted when the value switches has changed because of a click event.\n   */\n  @Event() bdsListSwitchChange!: EventEmitter;\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionsButtons!: EventEmitter;\n\n  componentWillLoad() {\n    this.data && this.dataChanged();\n  }\n\n  componentWillRender() {\n    this.data && this.updateData();\n    if (!this.data) {\n      this.setitemListElement();\n    }\n  }\n  componentDidRender() {\n    if (this.data) {\n      this.internalDataChanged();\n    }\n  }\n\n  @Watch('data')\n  dataChanged() {\n    this.updateData();\n  }\n\n  @Watch('value')\n  valueChanged(value: string) {\n    this.setSelectedRadio(value);\n  }\n\n  @Watch('internalData')\n  internalDataChanged() {\n    this.itemListElement = this.element.shadowRoot.querySelectorAll('bds-list-item');\n  }\n\n  private setitemListElement() {\n    this.itemListElement = this.element.getElementsByTagName(\n      'bds-list-item',\n    ) as HTMLCollectionOf<HTMLBdsListItemElement>;\n\n    for (let i = 0; i < this.itemListElement.length; i++) {\n      this.itemListElement[i].typeList = this.typeList;\n      this.itemListElement[i].addEventListener('bdsChecked', (event: CustomEvent) => this.chagedOptions(event));\n    }\n  }\n\n  private updateData() {\n    if (this.data) {\n      if (typeof this.data === 'string') {\n        this.internalData = JSON.parse(this.data);\n      } else {\n        this.internalData = this.data;\n      }\n    }\n  }\n\n  private chagedOptions = (event: CustomEvent): void => {\n    const { detail } = event;\n    if (detail.typeList == 'radio') {\n      if (detail.checked == true) {\n        this.value = detail;\n      }\n    }\n    if (detail.typeList == 'checkbox') {\n      this.setSelectedCheckbox();\n    }\n    if (detail.typeList == 'switch') {\n      this.setSelectedSwitch();\n    }\n  };\n\n  private setSelectedRadio(itemList) {\n    const itens = Array.from(this.itemListElement);\n    const radios = itens.filter((item) => item.typeList == 'radio');\n    for (let i = 0; i < radios.length; i++) {\n      if (radios[i].value != itemList.value) {\n        radios[i].checked = false;\n      } else {\n        const construct = {\n          value: radios[i].value,\n          text: radios[i]?.text,\n          secondaryText: radios[i]?.secondaryText,\n          avatarName: radios[i]?.avatarName,\n          avatarThumbnail: radios[i]?.avatarThumbnail,\n          typeList: radios[i]?.typeList,\n        };\n        this.bdsListRadioChange.emit(construct);\n      }\n    }\n  }\n\n  private setSelectedCheckbox() {\n    const checkboxs = this.itemListElement;\n    const itens = Array.from(checkboxs).filter((item) => item.typeList == 'checkbox');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListCheckboxChange.emit(result);\n  }\n\n  private setSelectedSwitch() {\n    const Switch = this.itemListElement;\n    const itens = Array.from(Switch).filter((item) => item.typeList == 'switch');\n    const result = itens\n      .filter((item) => item.checked)\n      .map((term) => ({\n        value: term.value,\n        text: term?.text,\n        secondaryText: term?.secondaryText,\n        avatarName: term?.avatarName,\n        avatarThumbnail: term?.avatarThumbnail,\n        typeList: term?.typeList,\n      }));\n    this.bdsListSwitchChange.emit(result);\n  }\n\n  private onClickActionsButtons = (event: CustomEvent): void => {\n    const { detail } = event;\n    this.bdsClickActionsButtons.emit(detail);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            list: true,\n          }}\n        >\n          {this.internalData ? (\n            this.internalData.map((item, idx) => (\n              <bds-list-item\n                key={idx}\n                value={item.value}\n                text={item.text}\n                type-list={this.typeList ? this.typeList : item.typeList}\n                secondary-text={item.secondaryText}\n                avatar-name={item.avatarName}\n                avatar-thumbnail={item.avatarThumbnail}\n                checked={item.checked}\n                icon={item.icon}\n                chips={item.chips}\n                actionsButtons={item.actionsButtons}\n                onBdsChecked={(ev) => this.chagedOptions(ev)}\n                onBdsClickActionButtom={(ev) => this.onClickActionsButtons(ev)}\n                dataTest={item.dataTest}\n              ></bds-list-item>\n            ))\n          ) : (\n            <slot></slot>\n          )}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "gEAAA,MAAMA,EAAU,g1E,MCUHC,EAAI,MALjB,WAAAC,CAAAC,G,8OAMUC,KAAeC,gBAAmF,KAQlGD,KAAQE,SAAc,KAmFtBF,KAAAG,cAAiBC,IACvB,MAAMC,OAAEA,GAAWD,EACnB,GAAIC,EAAOH,UAAY,QAAS,CAC9B,GAAIG,EAAOC,SAAW,KAAM,CAC1BN,KAAKO,MAAQF,C,EAGjB,GAAIA,EAAOH,UAAY,WAAY,CACjCF,KAAKQ,qB,CAEP,GAAIH,EAAOH,UAAY,SAAU,CAC/BF,KAAKS,mB,GAwDDT,KAAAU,sBAAyBN,IAC/B,MAAMC,OAAEA,GAAWD,EACnBJ,KAAKW,uBAAuBC,KAAKP,EAAO,CAqC3C,CA9JC,iBAAAQ,GACEb,KAAKc,MAAQd,KAAKe,a,CAGpB,mBAAAC,GACEhB,KAAKc,MAAQd,KAAKiB,aAClB,IAAKjB,KAAKc,KAAM,CACdd,KAAKkB,oB,EAGT,kBAAAC,GACE,GAAInB,KAAKc,KAAM,CACbd,KAAKoB,qB,EAKT,WAAAL,GACEf,KAAKiB,Y,CAIP,YAAAI,CAAad,GACXP,KAAKsB,iBAAiBf,E,CAIxB,mBAAAa,GACEpB,KAAKC,gBAAkBD,KAAKuB,QAAQC,WAAWC,iBAAiB,gB,CAG1D,kBAAAP,GACNlB,KAAKC,gBAAkBD,KAAKuB,QAAQG,qBAClC,iBAGF,IAAK,IAAIC,EAAI,EAAGA,EAAI3B,KAAKC,gBAAgB2B,OAAQD,IAAK,CACpD3B,KAAKC,gBAAgB0B,GAAGzB,SAAWF,KAAKE,SACxCF,KAAKC,gBAAgB0B,GAAGE,iBAAiB,cAAezB,GAAuBJ,KAAKG,cAAcC,I,EAI9F,UAAAa,GACN,GAAIjB,KAAKc,KAAM,CACb,UAAWd,KAAKc,OAAS,SAAU,CACjCd,KAAK8B,aAAeC,KAAKC,MAAMhC,KAAKc,K,KAC/B,CACLd,KAAK8B,aAAe9B,KAAKc,I,GAoBvB,gBAAAQ,CAAiBW,G,cACvB,MAAMC,EAAQC,MAAMC,KAAKpC,KAAKC,iBAC9B,MAAMoC,EAASH,EAAMI,QAAQC,GAASA,EAAKrC,UAAY,UACvD,IAAK,IAAIyB,EAAI,EAAGA,EAAIU,EAAOT,OAAQD,IAAK,CACtC,GAAIU,EAAOV,GAAGpB,OAAS0B,EAAS1B,MAAO,CACrC8B,EAAOV,GAAGrB,QAAU,K,KACf,CACL,MAAMkC,EAAY,CAChBjC,MAAO8B,EAAOV,GAAGpB,MACjBkC,MAAMC,EAAAL,EAAOV,MAAE,MAAAe,SAAA,SAAAA,EAAED,KACjBE,eAAeC,EAAAP,EAAOV,MAAE,MAAAiB,SAAA,SAAAA,EAAED,cAC1BE,YAAYC,EAAAT,EAAOV,MAAE,MAAAmB,SAAA,SAAAA,EAAED,WACvBE,iBAAiBC,EAAAX,EAAOV,MAAE,MAAAqB,SAAA,SAAAA,EAAED,gBAC5B7C,UAAU+C,EAAAZ,EAAOV,MAAE,MAAAsB,SAAA,SAAAA,EAAE/C,UAEvBF,KAAKkD,mBAAmBtC,KAAK4B,E,GAK3B,mBAAAhC,GACN,MAAM2C,EAAYnD,KAAKC,gBACvB,MAAMiC,EAAQC,MAAMC,KAAKe,GAAWb,QAAQC,GAASA,EAAKrC,UAAY,aACtE,MAAMkD,EAASlB,EACZI,QAAQC,GAASA,EAAKjC,UACtB+C,KAAKC,IAAI,CACR/C,MAAO+C,EAAK/C,MACZkC,KAAMa,IAAI,MAAJA,SAAA,SAAAA,EAAMb,KACZE,cAAeW,IAAI,MAAJA,SAAA,SAAAA,EAAMX,cACrBE,WAAYS,IAAI,MAAJA,SAAA,SAAAA,EAAMT,WAClBE,gBAAiBO,IAAI,MAAJA,SAAA,SAAAA,EAAMP,gBACvB7C,SAAUoD,IAAI,MAAJA,SAAA,SAAAA,EAAMpD,aAEpBF,KAAKuD,sBAAsB3C,KAAKwC,E,CAG1B,iBAAA3C,GACN,MAAM+C,EAASxD,KAAKC,gBACpB,MAAMiC,EAAQC,MAAMC,KAAKoB,GAAQlB,QAAQC,GAASA,EAAKrC,UAAY,WACnE,MAAMkD,EAASlB,EACZI,QAAQC,GAASA,EAAKjC,UACtB+C,KAAKC,IAAI,CACR/C,MAAO+C,EAAK/C,MACZkC,KAAMa,IAAI,MAAJA,SAAA,SAAAA,EAAMb,KACZE,cAAeW,IAAI,MAAJA,SAAA,SAAAA,EAAMX,cACrBE,WAAYS,IAAI,MAAJA,SAAA,SAAAA,EAAMT,WAClBE,gBAAiBO,IAAI,MAAJA,SAAA,SAAAA,EAAMP,gBACvB7C,SAAUoD,IAAI,MAAJA,SAAA,SAAAA,EAAMpD,aAEpBF,KAAKyD,oBAAoB7C,KAAKwC,E,CAQhC,MAAAM,GACE,OACEC,EAACC,EAAI,CAAAC,IAAA,4CACHF,EAAA,OAAAE,IAAA,2CACEC,MAAO,CACLC,KAAM,OAGP/D,KAAK8B,aACJ9B,KAAK8B,aAAauB,KAAI,CAACd,EAAMyB,IAC3BL,EAAA,iBACEE,IAAKG,EACLzD,MAAOgC,EAAKhC,MACZkC,KAAMF,EAAKE,KACA,YAAAzC,KAAKE,SAAWF,KAAKE,SAAWqC,EAAKrC,SAAQ,iBACxCqC,EAAKI,cACR,cAAAJ,EAAKM,WACA,mBAAAN,EAAKQ,gBACvBzC,QAASiC,EAAKjC,QACd2D,KAAM1B,EAAK0B,KACXC,MAAO3B,EAAK2B,MACZC,eAAgB5B,EAAK4B,eACrBC,aAAeC,GAAOrE,KAAKG,cAAckE,GACzCC,uBAAyBD,GAAOrE,KAAKU,sBAAsB2D,GAC3DE,SAAUhC,EAAKgC,aAInBZ,EAAa,c", "ignoreList": []}