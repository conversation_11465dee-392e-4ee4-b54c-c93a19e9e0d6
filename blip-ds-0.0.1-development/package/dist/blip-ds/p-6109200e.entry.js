import{r as t,h as s,H as e}from"./p-C3J6Z5OX.js";const a={"blip-tokens":"^1.93.0"};var i={dependencies:a};const r=":host .illustration{display:-ms-flexbox;display:flex;height:100%;width:auto}:host(.bds-illustration) img{width:100%;height:100%}";const n=class{constructor(s){t(this,s);this.type="default";this.dataTest=null;this.setIllustrationContent=()=>{const t=i.dependencies["blip-tokens"].replace("^","");const s=`https://cdn.jsdelivr.net/npm/blip-tokens@${t}/build/json/illustrations/${this.type}/${this.name}.json`;fetch(s).then((t=>t.json().then((t=>{this.IllustrationContent=t[`asset-${this.type}-${this.name}-svg`]}))))}}componentWillLoad(){this.setIllustrationContent()}render(){return s(e,{key:"068f8c950e40c190bc704ef26afba5a5a5ca0043",role:"img",class:{"bds-illustration":true}},this.IllustrationContent?s("img",{draggable:false,src:`data:image/svg+xml;base64,${this.IllustrationContent}`,alt:this.alt,"data-test":this.dataTest}):s("div",{class:"default","data-test":this.dataTest}))}static get assetsDirs(){return["svg"]}};n.style=r;const o=".skeleton{min-width:8px;min-height:8px;background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16;overflow:hidden}.skeleton_shape--circle{border-radius:50%}.skeleton_shape--square{border-radius:8px}.animation{position:absolute;width:100%;height:100%;background:-webkit-gradient(linear, left top, right top, from(rgba(246, 246, 246, 0)), color-stop(50%, rgba(246, 246, 246, 0.56)), to(rgba(246, 246, 246, 0)));background:linear-gradient(90deg, rgba(246, 246, 246, 0) 0%, rgba(246, 246, 246, 0.56) 50%, rgba(246, 246, 246, 0) 100%);mix-blend-mode:overlay;-webkit-animation:2.5s ease-out infinite shine;animation:2.5s ease-out infinite shine}@-webkit-keyframes shine{0%{-webkit-transform:translateX(-100%);transform:translateX(-100%)}20%{-webkit-transform:translateX(100%);transform:translateX(100%)}100%{-webkit-transform:translateX(100%);transform:translateX(100%)}}@keyframes shine{0%{-webkit-transform:translateX(-100%);transform:translateX(-100%)}20%{-webkit-transform:translateX(100%);transform:translateX(100%)}100%{-webkit-transform:translateX(100%);transform:translateX(100%)}}";const l=class{constructor(s){t(this,s);this.shape="square";this.height="50px";this.width="100%";this.dataTest=null}render(){return s(e,{key:"096bd4a0d5488514a4e4175365b2cdc72b2f95be",style:{display:"flex",position:"relative",overflow:"hidden",width:this.width,height:this.height,borderRadius:this.shape==="circle"?"50%":"8px"}},s("bds-grid",{key:"****************************************",xxs:"12",class:{skeleton:true,[`skeleton_shape--${this.shape}`]:true}}),s("div",{key:"65afb39b74e9fd0a44e3b11c5b26d5726136eee2",style:{display:"flex",width:"100%",height:"100%",position:"absolute",borderRadius:this.shape==="circle"?"50%":"8px",overflow:"hidden"},"data-test":this.dataTest},s("div",{key:"6d1069c509fff8283cb7456a96cdd6d43fb83845",class:"animation"})))}};l.style=o;export{n as bds_illustration,l as bds_skeleton};
//# sourceMappingURL=p-6109200e.entry.js.map