{"version": 3, "names": ["menuListItemCss", "MenuListItem", "render", "color", "this", "h", "Host", "key", "role", "class", "name", "icon", "variant"], "sources": ["src/components/menu-list-item/menu-list-item.scss?tag=bds-menu-list-item&encapsulation=shadow", "src/components/menu-list-item/menu-list-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$menu-list-item-width: 76px;\n$menu-list-item-height: 56px;\n\n:host {\n  display: flex;\n  flex: 1;\n}\n\n.menu-list-item {\n  background-color: $color-neutral-light-snow;\n  color: currentColor;\n\n  cursor: pointer;\n  width: $menu-list-item-width;\n  height: $menu-list-item-height;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  @include no-select();\n\n  &:hover,\n  &:hover > &__text {\n    background-color: $color-neutral-light-whisper;\n    color: $color-primary-main;\n  }\n\n  &:active,\n  &:active > &__text {\n    background-color: $color-hover-light;\n    color: $color-primary-main;\n  }\n\n  &:focus,\n  &:focus > &__text {\n    background-color: $color-neutral-light-whisper;\n    color: $color-primary-main;\n  }\n\n  &__text {\n    color: $color-neutral-medium-cloud;\n  }\n}\n", "import { Component, Host, h, Prop } from '@stencil/core';\n\n@Component({\n  tag: 'bds-menu-list-item',\n  styleUrl: 'menu-list-item.scss',\n  shadow: true,\n})\nexport class MenuListItem {\n  @Prop() color: string;\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon!: string;\n\n  render(): HTMLElement {\n    const color = this.color || 'currentColor';\n\n    return (\n      <Host role=\"button\">\n        <div class=\"menu-list-item\">\n          <bds-icon color={color} name={this.icon}></bds-icon>\n          <bds-typo class=\"menu-list-item__text\" variant=\"fs-10\">\n            <slot />\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAkB,syB,MCOXC,EAAY,M,yBAQvB,MAAAC,GACE,MAAMC,EAAQC,KAAKD,OAAS,eAE5B,OACEE,EAACC,EAAK,CAAAC,IAAA,2CAAAC,KAAK,UACTH,EAAK,OAAAE,IAAA,2CAAAE,MAAM,kBACTJ,EAAU,YAAAE,IAAA,2CAAAJ,MAAOA,EAAOO,KAAMN,KAAKO,OACnCN,EAAA,YAAAE,IAAA,2CAAUE,MAAM,uBAAuBG,QAAQ,SAC7CP,EAAA,QAAAE,IAAA,+C", "ignoreList": []}