{"version": 3, "names": ["cardFooterCss", "<PERSON><PERSON><PERSON>er", "exports", "class_1", "hostRef", "this", "align", "prototype", "render", "h", "key", "xxs", "direction", "gap", "justifyContent"], "sources": ["src/components/card/card-footer/card-footer.scss?tag=bds-card-footer&encapsulation=shadow", "src/components/card/card-footer/card-footer.tsx"], "sourcesContent": [":host {\n    width: 100%;\n}", "import { Component, ComponentInterface, h, Prop } from '@stencil/core';\n\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n\n@Component({\n  tag: 'bds-card-footer',\n  styleUrl: 'card-footer.scss',\n  shadow: true,\n})\nexport class CardFooter implements ComponentInterface {\n  /**\n   * Prop for internal elements alignment. Will follow the same values of css.\n   */\n  @Prop() align?: justifyContent = 'flex-end';\n  render() {\n    return (\n      <bds-grid xxs=\"12\" direction=\"row\" gap=\"2\" justifyContent={this.align}>\n        <slot />\n      </bds-grid>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAgB,oB,ICSTC,EAAUC,EAAA,6BALvB,SAAAC,EAAAC,G,UASUC,KAAKC,MAAoB,UAQlC,CAPCH,EAAAI,UAAAC,OAAA,WACE,OACEC,EAAU,YAAAC,IAAA,2CAAAC,IAAI,KAAKC,UAAU,MAAMC,IAAI,IAAIC,eAAgBT,KAAKC,OAC9DG,EAAQ,QAAAC,IAAA,6C,WARO,I", "ignoreList": []}