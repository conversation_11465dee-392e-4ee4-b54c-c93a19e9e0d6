{"version": 3, "file": "bds-menu-separation.entry.esm.js", "sources": ["src/components/menu/menu-separation/menu-separation.scss?tag=bds-menu-separation&encapsulation=shadow", "src/components/menu/menu-separation/menu-separation.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuseparation {\n  display: flex;\n  align-items: center;\n  padding: 0 16px;\n\n  &__small {\n    margin: 8px 0;\n  }\n  &__default {\n    margin: 12px 0;\n  }\n  &__large {\n    margin: 16px 0;\n  }\n\n  & .dividor-item{\n    height: 1px;\n    width: 100%;\n    background-color: $color-neutral-medium-wave;\n  }\n\n  & .title-item{\n    margin-right: 8px;\n    margin-top: -4px;\n    color: $color-neutral-medium-elephant;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\n\nexport type divisorSize = 'small' | 'default' | 'large';\n\n@Component({\n  tag: 'bds-menu-separation',\n  styleUrl: 'menu-separation.scss',\n  shadow: true,\n})\nexport class BdsMenuSeparation {\n  /**\n   * Value. Used to insert a title to the divider.\n   */\n  @Prop() value?: string = null;\n  /**\n   * Size. Used to set the size of the divider.\n   */\n  @Prop() size?: string = null;\n  render() {\n    return (\n      <div\n        class={{\n          menuseparation: true,\n          [`menuseparation__${this.size}`]: true,\n        }}\n      >\n        {this.value && (\n          <bds-typo class=\"title-item\" variant=\"fs-10\" tag=\"span\">\n            {this.value}\n          </bds-typo>\n        )}\n        <div class=\"dividor-item\"></div>\n      </div>\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,iBAAiB,GAAG,mXAAmX;;MCShY,iBAAiB,GAAA,MAAA;AAL9B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,IAAI;AAC7B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAkB7B;IAjBC,MAAM,GAAA;QACJ,QACE,CACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;aACvC,EAAA,EAEA,IAAI,CAAC,KAAK,KACT,iEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACpD,IAAI,CAAC,KAAK,CACF,CACZ,EACD,CAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,cAAc,EAAO,CAAA,CAC5B;;;;;;;"}