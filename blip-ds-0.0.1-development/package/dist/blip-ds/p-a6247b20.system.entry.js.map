{"version": 3, "names": ["alertBodyCss", "AlertBody", "exports", "class_1", "prototype", "render", "h", "key", "class", "variant", "bold", "slot"], "sources": ["src/components/alert/alert-body/alert-body.scss?tag=bds-alert-body&encapsulation=shadow", "src/components/alert/alert-body/alert-body.tsx"], "sourcesContent": [".alert__body {  \n  position: relative;\n  width: 100%;\n  padding: 12px 16px;\n  margin-bottom: 8px;\n  box-sizing: border-box;\n\n}", "import { Component, ComponentInterface, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-alert-body',\n  styleUrl: 'alert-body.scss',\n  shadow: true,\n})\nexport class AlertBody implements ComponentInterface {\n  render() {\n    return (\n      <div class=\"alert__body\">\n        <bds-typo variant=\"fs-14\" bold=\"regular\" slot=\"body\">\n          <slot />\n        </bds-typo>\n      </div>\n    );\n  }\n}\n"], "mappings": "0IAAA,IAAMA,EAAe,qI,ICORC,EAASC,EAAA,4B,wBACpBC,EAAAC,UAAAC,OAAA,WACE,OACEC,EAAA,OAAAC,IAAA,2CAAKC,MAAM,eACTF,EAAU,YAAAC,IAAA,2CAAAE,QAAQ,QAAQC,KAAK,UAAUC,KAAK,QAC5CL,EAAQ,QAAAC,IAAA,8C,WALI,I", "ignoreList": []}