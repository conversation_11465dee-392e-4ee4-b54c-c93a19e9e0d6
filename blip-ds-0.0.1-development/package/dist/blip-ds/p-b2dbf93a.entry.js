import{r as e,c as t,h as o,H as a,a as i}from"./p-C3J6Z5OX.js";const r=':host{display:block;margin:-4px;padding:4px;width:100%;position:relative;overflow:hidden;-webkit-transition:height 0.5s;-moz-transition:height 0.5s;transition:height 0.5s}.nav_main{display:-ms-flexbox;display:flex;gap:8px;-ms-flex-align:center;align-items:center;padding:8px;position:relative;cursor:pointer;border-radius:8px;border:1px solid transparent;overflow:hidden}.nav_main--loading{cursor:wait}.nav_main--disable{opacity:0.5;cursor:not-allowed}.nav_main:before{content:"";position:absolute;inset:0}.nav_main:hover:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.nav_main:active:before{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}.nav_main:hover,.nav_main_active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_main_active:before{background-color:var(--color-content-default, rgb(40, 40, 40));border-color:var(--color-hover, rgba(0, 0, 0, 0.08));opacity:0.08}.nav_main--disable:before,.nav_main--disable:hover{border-color:transparent;background-color:transparent}.nav_main .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.nav_main .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.nav_main_text{position:relative;display:-ms-flexbox;display:flex;gap:2px;-ms-flex-direction:column;flex-direction:column}.nav_main_text .title-item{color:var(--color-content-default, rgb(40, 40, 40))}.nav_main_text .title-item--loading{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_text .subtitle-item{color:var(--color-content-default, rgb(40, 40, 40))}.nav_main_text .subtitle-item--loading{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_content{width:100%;-ms-flex-negative:99999;flex-shrink:99999}.nav_main_arrow{-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.nav_main_arrow--disable{color:var(--color-content-disable, rgb(89, 89, 89))}.nav_main_arrow_active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.accordion{display:grid;grid-template-rows:0fr;-webkit-transition:all ease 0.5s;-moz-transition:all ease 0.5s;transition:all ease 0.5s}.accordion_open{grid-template-rows:1fr;padding:8px 0}.accordion .container{overflow:hidden;position:relative;padding-left:23px}.accordion .container:before{content:"";position:absolute;width:2px;inset:0;left:23px;top:8px;bottom:8px;border-radius:8px;background-color:var(--color-hover, rgba(0, 0, 0, 0.08));opacity:0.8}.accordion .container--disable:before{background-color:transparent}.nav_tree_item{position:relative;display:-ms-flexbox;display:flex;gap:8px;-ms-flex-pack:justify;justify-content:space-between;-ms-flex-align:center;align-items:center;cursor:pointer;padding:8px;padding-left:22px}.nav_tree_item--loading{cursor:wait}.nav_tree_item--disable{opacity:0.5;cursor:not-allowed}.nav_tree_item--disable:before,.nav_tree_item--disable:hover{border-color:transparent;background-color:transparent}.nav_tree_item .icon-item{position:relative;color:var(--color-content-default, rgb(40, 40, 40))}.nav_tree_item .icon-item-active{color:var(--color-primary, rgb(30, 107, 241))}.nav_tree_item_content{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.nav_tree_item_slot{width:100%;-ms-flex-negative:99999;flex-shrink:99999}.nav_tree_item:before{content:"";position:absolute;width:2px;inset:0;top:8px;bottom:8px;border-radius:8px;background-color:transparent;-webkit-transition:background-color ease 0.8s;-moz-transition:background-color ease 0.8s;transition:background-color ease 0.8s}.nav_tree_item:hover:before{background-color:var(--color-pressed, rgba(0, 0, 0, 0.16));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item_active:before{background-color:var(--color-primary, rgb(30, 107, 241));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item_active:hover:before{background-color:var(--color-primary, rgb(30, 107, 241));-webkit-transition:background-color ease 0.3s;-moz-transition:background-color ease 0.3s;transition:background-color ease 0.3s}.nav_tree_item .icon-arrow{position:relative;-webkit-transition:all ease 0.3s;-moz-transition:all ease 0.3s;transition:all ease 0.3s;-webkit-transform:rotate(0deg);transform:rotate(0deg)}.nav_tree_item .icon-arrow-active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.nav_tree_item_button{padding:8px;margin-left:14px;border-radius:8px;border:1px solid transparent}.nav_tree_item_button:before{left:-15px}.nav_tree_item_button:after{content:"";position:absolute;inset:0;border-radius:8px;background-color:transparent}.nav_tree_item_button:hover,.nav_tree_item_button_active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_tree_item_button:hover:after,.nav_tree_item_button_active:after{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.08}.nav_tree_item_button:active{border-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}.nav_tree_item_button:active:after{background-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}.focus{position:relative}.focus:before{content:"";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.focus:focus-visible{outline:none}.focus:focus-visible:before{border-color:var(--color-focus, rgb(194, 38, 251))}';const n=class{constructor(o){e(this,o);this.bdsToogleChange=t(this,"bdsToogleChange");this.navTreeParent=null;this.navTreeChild=null;this.itensElement=null;this.collapse="single";this.icon=null;this.secondaryText=null;this.isOpen=false;this.loading=false;this.disable=false;this.dataTest=null;this.handler=()=>{if(!this.loading&&!this.disable){if(this.navTreeParent&&this.navTreeParent.collapse=="single"&&this.itensElement){for(let e=0;e<this.itensElement.length;e++){if(this.itensElement[e]!=this.element)this.itensElement[e].isOpen=false}}this.toggle()}}}async toggle(){this.isOpen=!this.isOpen}isOpenChanged(e){this.bdsToogleChange.emit({value:e,element:this.element})}componentWillLoad(){var e,t;this.navTreeParent=((e=this.element.parentElement)===null||e===void 0?void 0:e.tagName)=="BDS-NAV-TREE"&&this.element.parentElement||((t=this.element.parentElement)===null||t===void 0?void 0:t.tagName)=="BDS-NAV-TREE-ITEM"&&this.element.parentElement||null;this.navTreeChild=this.element.querySelector("bds-nav-tree-item")}componentWillRender(){if(this.navTreeParent){this.itensElement=this.navTreeParent.querySelectorAll("bds-nav-tree-item")}}handleKeyDown(e){if(e.key=="Enter"){this.handler()}}render(){return o(a,{key:"e1d3bcfd325b0fe24e59872f41fec9118f180e28"},o("div",{key:"17d859b63b84eb93ca0f30ede328c1ff699da856",tabindex:"0",onKeyDown:this.handleKeyDown.bind(this),class:"focus"},o("div",{key:"90c9d5c6b084667fa04ccbbf67ae1e7fc3e38a6e",class:{nav_tree_item:true,nav_tree_item_active:this.isOpen,nav_tree_item_button:!this.navTreeChild,nav_tree_item_button_active:!this.navTreeChild&&this.isOpen,[`nav_tree_item--loading`]:this.loading,[`nav_tree_item--disable`]:this.disable},onClick:()=>this.handler(),"data-test":this.dataTest,"aria-label":this.text+(this.secondaryText&&`: ${this.secondaryText}`)},this.loading?o("bds-loading-spinner",{size:"extra-small"}):this.icon?o("bds-icon",{class:{[`icon-item`]:true,[`icon-item-active`]:this.isOpen},size:"medium",name:this.icon,color:"inherit",theme:"outline"}):"",o("div",{key:"332afb1989b58cb2d45f59de1801cd7e781a5b1c",class:"nav_tree_item_content"},this.text&&o("bds-typo",{key:"3f4846a4d557ab09339946f9c929f6be171799a8",class:{["title-item"]:true,[`title-item--loading`]:this.loading},variant:"fs-14",tag:"span","line-height":"small",bold:this.isOpen?"bold":"semi-bold"},this.text),this.secondaryText&&o("bds-typo",{key:"fb23f3924bdaa30ecda86de5d50513417eb459cc",class:{["subtitle-item"]:true,[`subtitle-item--loading`]:this.loading},variant:"fs-12","line-height":"small",tag:"span",margin:false},this.secondaryText)),o("div",{key:"7fb98224ac8ecdc8dcc0207ec387d32f7b0d1a65",class:"nav_tree_item_slot"},o("slot",{key:"55751342b621931a9899e94bf613a5c3a5ca70f3",name:"header-content"})),this.navTreeChild&&o("bds-icon",{key:"dfc6d22d09f6659841f827f3d56ce3fac5564288",class:{[`nav_main_arrow`]:true,[`nav_main_arrow_active`]:this.isOpen,[`nav_main_arrow--loading`]:this.loading},name:"arrow-down"}))),this.navTreeChild&&o("div",{key:"0c24e36910cb1253b1bf7b161d1a40a6c07744a9",class:{accordion:true,accordion_open:this.isOpen}},o("div",{key:"2da280a88a3aad6d8aeb7509e91947e207dc1bf5",class:"container"},o("slot",{key:"9042f029e6f859a163c8a98c6732f7f726aa3fe5"}))))}get element(){return i(this)}static get watchers(){return{isOpen:["isOpenChanged"]}}};n.style=r;export{n as bds_nav_tree_item};
//# sourceMappingURL=p-b2dbf93a.entry.js.map