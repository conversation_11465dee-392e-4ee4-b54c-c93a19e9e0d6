{"version": 3, "names": ["menuActionCss", "BdsMenuAction", "constructor", "hostRef", "this", "openParentMenu", "openSubMenu", "positionSubMenu", "stateSubMenu", "delaySubMenu", "zIndex", "delay", "buttonText", "subMenu", "iconLeft", "subtitle", "description", "lipstick", "disabled", "onCloseSubMenu", "onChangeOpenParent", "event", "detail", "value", "componentWillLoad", "menuElement", "element", "parentElement", "addEventListener", "openParentMenuChanged", "active", "divMenu", "shadowRoot", "querySelectorAll", "offsetLeft", "offsetWidth", "window", "innerWidth", "openSubMenuChanged", "setTimeout", "clearTimeout", "stateSubMenuChanged", "state", "render", "actLeft", "actRight", "actLeftright", "openSubmenu", "closeSubmenu", "zIndexSubmenu", "h", "key", "class", "menuaction", "onMouseOver", "onMouseOut", "menuaction__button", "name", "theme", "size", "variant", "tag", "arrow", "menuaction__submenu", "menuaction__submenu__open", "style"], "sources": ["src/components/menu/menu-action/menu-action.scss?tag=bds-menu-action&encapsulation=shadow", "src/components/menu/menu-action/menu-action.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuaction {\n  position: relative;\n\n  &__button {\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    align-items: center;\n    background-color: $color-surface-1;\n    border: 0;\n    border-radius: 8px;\n    padding: 16px;\n    width: 100%;\n    text-align: left;\n    cursor: pointer;\n\n    &__activeicleft {\n      grid-template-columns: auto 1fr;\n    }\n\n    &__activeicright {\n      grid-template-columns: 1fr auto;\n    }\n\n    &__activeicleftright {\n      grid-template-columns: auto 1fr auto;\n    }\n\n    & .icon-item {\n      color: $color-content-default;\n    }\n    & .content-item {\n      width: 100%;\n      color: $color-content-default;\n      display: flex;\n      flex-direction: column;\n    }\n\n    & .arrow {\n      color: $color-content-default;\n    }\n\n    &__lipstick {\n      & .icon-item {\n        color: $color-extend-reds-lipstick;\n      }\n      & .content-item {\n        color: $color-extend-reds-lipstick;\n      }\n      & .arrow {\n        color: $color-extend-reds-lipstick;\n      }\n    }\n\n    &__disabled {\n      opacity: 0.5;\n      cursor: no-drop;\n    }\n\n    &:hover {\n      background-color: $color-surface-2;\n    }\n  }\n\n  &__submenu {\n    position: absolute;\n    pointer-events: none;\n    display: block;\n    padding: 2px;\n    background-color: $color-surface-1;\n    border-radius: 8px;\n    box-shadow: 0px 4px 16px rgba(7, 71, 166, 0.12);\n    min-width: 196px;\n    opacity: 0;\n    -webkit-transition: opacity 0.5s;\n    -moz-transition: opacity 0.5s;\n    transition: opacity 0.5s;\n\n    &__open {\n      pointer-events: auto;\n      opacity: 1;\n    }\n  }\n\n  &.position-right {\n    & .menuaction__button {\n      & .icon-item {\n        order: 0;\n      }\n      & .content-item {\n        order: 1;\n      }\n      & .arrow {\n        order: 2;\n      }\n    }\n    & .menuaction__submenu {\n      top: -2px;\n      left: 100%;\n    }\n  }\n\n  &.position-left {\n    & .menuaction__button {\n      & .icon-item {\n        order: 1;\n      }\n      & .content-item {\n        order: 2;\n      }\n      & .arrow {\n        order: 0;\n      }\n    }\n    & .menuaction__submenu {\n      top: -2px;\n      right: 100%;\n    }\n  }\n}\n", "import { Component, h, Element, State, Prop, Watch } from '@stencil/core';\n\nexport type closeSubMenuState = 'close' | 'pending' | 'open';\nexport type positionSubMenuState = 'right' | 'left';\n\n@Component({\n  tag: 'bds-menu-action',\n  styleUrl: 'menu-action.scss',\n  shadow: true,\n})\nexport class BdsMenuAction {\n  private menuElement?: HTMLBdsMenuElement;\n\n  @Element() private element: HTMLElement;\n\n  @State() openParentMenu?: boolean = false;\n  @State() openSubMenu?: boolean = false;\n  @State() positionSubMenu?: positionSubMenuState = 'right';\n  @State() stateSubMenu?: closeSubMenuState = 'close';\n  @State() delaySubMenu?: boolean = false;\n  @State() zIndex?: number = 0;\n  @State() delay = null;\n  /**\n   * ButtonText. Used to enter the display text for the item.\n   */\n  @Prop() buttonText?: string = '';\n  /**\n   * SubMenu. Used to declare that the button will have a submenu.\n   */\n  @Prop() subMenu?: boolean = false;\n  /**\n   * Iconleft. Used to insert the string icon and make the icon available to the left of the item.\n   */\n  @Prop() iconLeft?: string = null;\n  /**\n   * Subtitle. Used to insert a subtitle in the display item.\n   */\n  @Prop() subtitle?: string = null;\n  /**\n   * Description. Used to insert a subtitle in the display item.\n   */\n  @Prop() description?: string = null;\n  /**\n   * Lipstick. Used to declare that the item will be a negative/error action.\n   */\n  @Prop() lipstick?: boolean = false;\n  /**\n   * Disabled. Used to declare that the item will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  private onCloseSubMenu = (): void => {\n    this.stateSubMenu = 'close';\n  };\n\n  componentWillLoad() {\n    if (this.subMenu) {\n      this.menuElement = this.element.parentElement as HTMLBdsMenuElement;\n      this.menuElement.addEventListener('bdsOpenMenu', (event) => {\n        this.onChangeOpenParent(event);\n      });\n    }\n  }\n\n  @Watch('openParentMenu')\n  protected openParentMenuChanged(active: boolean): void {\n    if (active) {\n      const divMenu = this.menuElement.shadowRoot.querySelectorAll('div')[0];\n      this.positionSubMenu = divMenu.offsetLeft + divMenu.offsetWidth + 196 >= window.innerWidth ? 'left' : 'right';\n    }\n  }\n\n  @Watch('openSubMenu')\n  protected openSubMenuChanged(active: boolean): void {\n    if (active == false) {\n      this.stateSubMenu = 'pending';\n      this.delay = setTimeout(this.onCloseSubMenu, 1000);\n    }\n    if (active == true) {\n      clearTimeout(this.delay);\n      this.delay = null;\n      this.stateSubMenu = 'open';\n    }\n  }\n\n  @Watch('stateSubMenu')\n  protected stateSubMenuChanged(state: closeSubMenuState): void {\n    switch (state) {\n      case 'open':\n        this.delaySubMenu = true;\n        break;\n      case 'pending':\n        this.delaySubMenu = true;\n        break;\n      case 'close':\n        this.delaySubMenu = false;\n        break;\n    }\n  }\n\n  private onChangeOpenParent = (event) => {\n    this.openParentMenu = event.detail.value;\n  };\n\n  render() {\n    const actLeft = this.iconLeft && !this.subMenu;\n    const actRight = this.subMenu && !this.iconLeft;\n    const actLeftright = this.iconLeft && this.subMenu;\n\n    const openSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 1;\n        this.openSubMenu = true;\n      }\n    };\n\n    const closeSubmenu = () => {\n      if (this.subMenu == true) {\n        this.zIndex = 0;\n        this.openSubMenu = false;\n      }\n    };\n\n    const zIndexSubmenu = {\n      zIndex: `${this.zIndex}`,\n    };\n\n    return (\n      <div\n        class={{\n          menuaction: true,\n          [`position-${this.positionSubMenu}`]: true,\n        }}\n        onMouseOver={openSubmenu}\n        onMouseOut={closeSubmenu}\n      >\n        <button\n          class={{\n            menuaction__button: true,\n            [`menuaction__button__activeicleft`]: actLeft,\n            [`menuaction__button__activeicright`]: actRight,\n            [`menuaction__button__activeicleftright`]: actLeftright,\n            [`menuaction__button__lipstick`]: this.lipstick,\n            [`menuaction__button__disabled`]: this.disabled,\n          }}\n        >\n          {this.iconLeft && <bds-icon class=\"icon-item\" name={this.iconLeft} theme=\"outline\" size=\"small\"></bds-icon>}\n          <div class=\"content-item\">\n            {this.buttonText && (\n              <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\">\n                {this.buttonText}\n              </bds-typo>\n            )}\n            {this.subtitle && (\n              <bds-typo class=\"subtitle-item\" variant=\"fs-10\" tag=\"span\">\n                {this.subtitle}\n              </bds-typo>\n            )}\n            {this.description && (\n              <bds-typo class=\"description-item\" variant=\"fs-10\" tag=\"span\">\n                {this.description}\n              </bds-typo>\n            )}\n          </div>\n          {this.subMenu && (\n            <bds-icon\n              class={{ arrow: true }}\n              name={`arrow-${this.positionSubMenu}`}\n              theme=\"outline\"\n              size=\"small\"\n            ></bds-icon>\n          )}\n        </button>\n        {this.subMenu && (\n          <div\n            class={{\n              menuaction__submenu: true,\n              menuaction__submenu__open: this.delaySubMenu,\n            }}\n            style={zIndexSubmenu}\n          >\n            <slot />\n          </div>\n        )}\n      </div>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAgB,wrE,MCUTC,EAAa,MAL1B,WAAAC,CAAAC,G,UAUWC,KAAcC,eAAa,MAC3BD,KAAWE,YAAa,MACxBF,KAAeG,gBAA0B,QACzCH,KAAYI,aAAuB,QACnCJ,KAAYK,aAAa,MACzBL,KAAMM,OAAY,EAClBN,KAAKO,MAAG,KAITP,KAAUQ,WAAY,GAItBR,KAAOS,QAAa,MAIpBT,KAAQU,SAAY,KAIpBV,KAAQW,SAAY,KAIpBX,KAAWY,YAAY,KAIvBZ,KAAQa,SAAa,MAIrBb,KAAQc,SAAa,MAErBd,KAAce,eAAG,KACvBf,KAAKI,aAAe,OAAO,EAgDrBJ,KAAAgB,mBAAsBC,IAC5BjB,KAAKC,eAAiBgB,EAAMC,OAAOC,KAAK,CAsF3C,CApIC,iBAAAC,GACE,GAAIpB,KAAKS,QAAS,CAChBT,KAAKqB,YAAcrB,KAAKsB,QAAQC,cAChCvB,KAAKqB,YAAYG,iBAAiB,eAAgBP,IAChDjB,KAAKgB,mBAAmBC,EAAM,G,EAM1B,qBAAAQ,CAAsBC,GAC9B,GAAIA,EAAQ,CACV,MAAMC,EAAU3B,KAAKqB,YAAYO,WAAWC,iBAAiB,OAAO,GACpE7B,KAAKG,gBAAkBwB,EAAQG,WAAaH,EAAQI,YAAc,KAAOC,OAAOC,WAAa,OAAS,O,EAKhG,kBAAAC,CAAmBR,GAC3B,GAAIA,GAAU,MAAO,CACnB1B,KAAKI,aAAe,UACpBJ,KAAKO,MAAQ4B,WAAWnC,KAAKe,eAAgB,I,CAE/C,GAAIW,GAAU,KAAM,CAClBU,aAAapC,KAAKO,OAClBP,KAAKO,MAAQ,KACbP,KAAKI,aAAe,M,EAKd,mBAAAiC,CAAoBC,GAC5B,OAAQA,GACN,IAAK,OACHtC,KAAKK,aAAe,KACpB,MACF,IAAK,UACHL,KAAKK,aAAe,KACpB,MACF,IAAK,QACHL,KAAKK,aAAe,MACpB,M,CAQN,MAAAkC,GACE,MAAMC,EAAUxC,KAAKU,WAAaV,KAAKS,QACvC,MAAMgC,EAAWzC,KAAKS,UAAYT,KAAKU,SACvC,MAAMgC,EAAe1C,KAAKU,UAAYV,KAAKS,QAE3C,MAAMkC,EAAc,KAClB,GAAI3C,KAAKS,SAAW,KAAM,CACxBT,KAAKM,OAAS,EACdN,KAAKE,YAAc,I,GAIvB,MAAM0C,EAAe,KACnB,GAAI5C,KAAKS,SAAW,KAAM,CACxBT,KAAKM,OAAS,EACdN,KAAKE,YAAc,K,GAIvB,MAAM2C,EAAgB,CACpBvC,OAAQ,GAAGN,KAAKM,UAGlB,OACEwC,EACE,OAAAC,IAAA,2CAAAC,MAAO,CACLC,WAAY,KACZ,CAAC,YAAYjD,KAAKG,mBAAoB,MAExC+C,YAAaP,EACbQ,WAAYP,GAEZE,EAAA,UAAAC,IAAA,2CACEC,MAAO,CACLI,mBAAoB,KACpB,CAAC,oCAAqCZ,EACtC,CAAC,qCAAsCC,EACvC,CAAC,yCAA0CC,EAC3C,CAAC,gCAAiC1C,KAAKa,SACvC,CAAC,gCAAiCb,KAAKc,WAGxCd,KAAKU,UAAYoC,EAAA,YAAAC,IAAA,2CAAUC,MAAM,YAAYK,KAAMrD,KAAKU,SAAU4C,MAAM,UAAUC,KAAK,UACxFT,EAAK,OAAAC,IAAA,2CAAAC,MAAM,gBACRhD,KAAKQ,YACJsC,EAAA,YAAAC,IAAA,2CAAUC,MAAM,aAAaQ,QAAQ,QAAQC,IAAI,QAC9CzD,KAAKQ,YAGTR,KAAKW,UACJmC,EAAA,YAAAC,IAAA,2CAAUC,MAAM,gBAAgBQ,QAAQ,QAAQC,IAAI,QACjDzD,KAAKW,UAGTX,KAAKY,aACJkC,EAAA,YAAAC,IAAA,2CAAUC,MAAM,mBAAmBQ,QAAQ,QAAQC,IAAI,QACpDzD,KAAKY,cAIXZ,KAAKS,SACJqC,EACE,YAAAC,IAAA,2CAAAC,MAAO,CAAEU,MAAO,MAChBL,KAAM,SAASrD,KAAKG,kBACpBmD,MAAM,UACNC,KAAK,WAIVvD,KAAKS,SACJqC,EAAA,OAAAC,IAAA,2CACEC,MAAO,CACLW,oBAAqB,KACrBC,0BAA2B5D,KAAKK,cAElCwD,MAAOhB,GAEPC,EAAA,QAAAC,IAAA,8C", "ignoreList": []}