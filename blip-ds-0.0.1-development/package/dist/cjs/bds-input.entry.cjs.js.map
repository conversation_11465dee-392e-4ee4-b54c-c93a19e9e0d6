{"version": 3, "file": "bds-input.entry.cjs.js", "sources": ["src/components/input/input.scss?tag=bds-input&encapsulation=shadow", "src/components/input/input.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: 22px;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @if ($name == 'disabled') {\n    background: $color-surface-2;\n  }\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n:host {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n\n      &[type='date'] {\n        &::-webkit-calendar-picker-indicator {\n          opacity: 0;\n          pointer-events: none;\n        }\n      }\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    gap: 4px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      margin-top: 0px;\n    }\n\n    &--danger {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-negative;\n        }\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.input__container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n\n  &__label {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.input__container__wrapper {\n  display: flex;\n  flex-wrap: nowrap;\n}\n\n.input__container__text {\n  @include reset-input();\n  @include input-text();\n  width: 100%;\n  resize: none;\n  cursor: inherit;\n\n  &__chips {\n    width: auto;\n    min-width: 216px;\n    max-width: 216px;\n  }\n}\n", "/* eslint-disable no-console */\nimport { Component, h, Prop, State, Watch, Event, EventEmitter, Method, Host } from '@stencil/core';\nimport { InputType, InputAutocapitalize, InputAutoComplete, InputCounterLengthRules } from './input-interface';\nimport { emailValidation, numberValidation } from '../../utils/validations';\n\n@Component({\n  tag: 'bds-input',\n  styleUrl: 'input.scss',\n  shadow: true,\n})\nexport class Input {\n  private nativeInput?: HTMLInputElement;\n\n  @State() isPressed? = false;\n  @State() isPassword? = false;\n  @State() validationMesage? = '';\n  @State() validationDanger? = false;\n  /**\n   * Nome do input, usado para identificação no formulário.\n   */\n  @Prop() inputName? = '';\n\n  /**\n   * Define o tipo do input (por exemplo, `text`, `password`, etc).\n   */\n  @Prop({ reflect: true }) type?: InputType = 'text';\n\n  /**\n   * <PERSON><PERSON><PERSON><PERSON> que será exibido acima do input.\n   */\n  @Prop() label? = '';\n\n  /**\n   * Texto que será exibido como sugestão ou dica no input.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Define a capitalização automática do texto (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoCapitalize?: InputAutocapitalize = 'off';\n\n  /**\n   * Define o comportamento de autocompletar do navegador (valores possíveis: `on`, `off`).\n   */\n  @Prop() autoComplete?: InputAutoComplete = 'off';\n\n  /**\n   * Define o valor máximo permitido para o input.\n   */\n  @Prop() max?: string;\n\n  /**\n   * Define o número máximo de caracteres permitidos no input.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * Define o valor mínimo permitido para o input.\n   */\n  @Prop() min?: string;\n\n  /**\n   * Define o número mínimo de caracteres permitidos no input.\n   */\n  @Prop() minlength?: number;\n\n  /**\n   * Torna o input somente leitura.\n   */\n  @Prop() readonly = false;\n\n  /**\n   * Define se o input é obrigatório.\n   */\n  @Prop() required: boolean;\n\n  /**\n   * Define um padrão regex que o valor do input deve seguir.\n   */\n  @Prop() pattern?: string;\n\n  /**\n   * Mensagem de ajuda exibida abaixo do input.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Mensagem de erro exibida quando o valor do input é inválido.\n   */\n  @Prop({ mutable: true }) errorMessage?: string = '';\n\n  /**\n   * Mensagem exibida quando o valor do input é válido.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n\n  /**\n   * Nome do ícone a ser exibido dentro do input.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Define se o input está desabilitado.\n   */\n  @Prop({ reflect: true, mutable: true }) disabled?: boolean = false;\n\n  /**\n   * Define se o input está em estado de erro.\n   */\n  @Prop({ reflect: true, mutable: true }) danger?: boolean = false;\n\n  /**\n   * Define se o input está em estado de sucesso.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n\n  /**\n   * O valor atual do input.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Define se será exibido um contador de comprimento de caracteres.\n   */\n  @Prop() counterLength? = false;\n\n  /**\n   * Define a regra do contador de comprimento de caracteres (min, max, etc).\n   */\n  @Prop() counterLengthRule?: InputCounterLengthRules = null;\n\n  /**\n   * Define se o input será submetido ao pressionar Enter.\n   */\n  @Prop() isSubmit = false;\n\n  /**\n   * Define se o input é uma área de texto (textarea).\n   */\n  @Prop() isTextarea = false;\n\n  /**\n   * Define a quantidade de linhas da área de texto (se for `textarea`).\n   */\n  @Prop() rows?: number = 1;\n\n  /**\n   * Define a quantidade de colunas da área de texto (se for `textarea`).\n   */\n  @Prop() cols?: number = 0;\n\n  /**\n   * Mensagem de erro exibida quando o input não é preenchido e é obrigatório.\n   */\n  @Prop() requiredErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao comprimento mínimo.\n   */\n  @Prop() minlengthErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor mínimo permitido.\n   */\n  @Prop() minErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não atende ao valor máximo permitido.\n   */\n  @Prop() maxErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um email válido.\n   */\n  @Prop() emailErrorMessage: string;\n\n  /**\n   * Mensagem de erro exibida quando o valor do input não é um número válido.\n   */\n  @Prop() numberErrorMessage: string;\n\n  /**\n   * Define se o input será exibido como chips (um tipo de entrada com múltiplos valores).\n   */\n  @Prop() chips: boolean;\n\n  /**\n   * Data test é a prop para testar especificamente a ação do componente.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Prop() encode?: boolean = false;\n\n  /**\n   * Evento disparado quando o valor do input muda.\n   */\n  @Event({ bubbles: true, composed: true }) bdsChange!: EventEmitter;\n\n  /**\n   * Evento disparado quando o input recebe um input (digitação).\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Evento disparado quando o input perde o foco.\n   */\n  @Event() bdsOnBlur: EventEmitter;\n\n  /**\n   * Evento disparado quando o input ganha o foco.\n   */\n  @Event() bdsFocus: EventEmitter;\n\n  /**\n   * Evento disparado quando o formulário é submetido.\n   */\n  @Event() bdsSubmit: EventEmitter;\n\n  /**\n   * Evento disparado para validação de padrão regex.\n   */\n  @Event() bdsPatternValidation: EventEmitter;\n\n  /**\n   * Evento disparado quando a tecla \"Backspace\" é pressionada.\n   */\n  @Event() bdsKeyDownBackspace: EventEmitter;\n\n  /**\n   * Define o foco no campo de entrada.\n   */\n  @Method()\n  async setFocus(): Promise<void> {\n    this.onClickWrapper();\n  }\n\n  /**\n   * Remove o foco do campo de entrada.\n   */\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.onBlur();\n  }\n\n  /**\n   * Retorna o elemento de input do componente.\n   */\n  @Method()\n  async getInputElement(): Promise<HTMLInputElement> {\n    return this.nativeInput;\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.nativeInput.validity.valid;\n  }\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.value = '';\n  }\n\n  /**\n   * Codifica os caracteres especiais para exibição segura (evita injeção de código HTML).\n   */\n  private encodeValue(value?: string): string {\n    const lt = /</g,\n      gt = />/g,\n      ap = /'/g,\n      ic = /\"/g,\n      amp = /&/g,\n      slash = /\\//g;\nif(!this.encode) return value;\n    return (\n      value &&\n      value\n        .toString()\n        .replace(lt, '&lt;')\n        .replace(gt, '&gt;')\n        .replace(ap, '&#39;')\n        .replace(ic, '&#34;')\n        .replace(amp, '&amp;')\n        .replace(slash, '&#47;')\n    );\n  }\n\n  /**\n   * Avisa sobre a mudança do valor do campo de entrada.\n   */\n  @Watch('value')\n  protected valueChanged(newValue: string | null): void {\n    const changeValue = this.encode ? this.encodeValue(newValue || '') : newValue || '';\n    this.bdsChange.emit({ value: changeValue });\n  }\n\n  /**\n   * Tratamento de eventos de pressionamento de tecla (Enter, Backspace, etc).\n   */\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        this.bdsSubmit.emit({ event, value: this.value });\n\n        if (this.isSubmit) {\n          this.clearTextInput();\n          event.preventDefault();\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        this.bdsKeyDownBackspace.emit({ event, value: this.value });\n        break;\n    }\n  };\n\n  /**\n   * Função chamada ao digitar no campo de entrada.\n   */\n  private onInput = (ev: InputEvent): void => {\n    this.onBdsInputValidations();\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsInput.emit(ev);\n  };\n\n  /**\n   * Função chamada ao perder o foco do campo de entrada.\n   */\n  private onBlur = (): void => {\n    this.onBlurValidations();\n    this.isPressed = false;\n    this.bdsOnBlur.emit();\n  };\n\n  /**\n   * Função chamada ao ganhar o foco do campo de entrada.\n   */\n  private onFocus = (): void => {\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  /**\n   * Função chamada ao clicar no campo de entrada.\n   */\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  /**\n   * Limpa o valor do campo de entrada.\n   */\n  private clearTextInput = (ev?: Event) => {\n    if (!this.readonly && !this.disabled && ev) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n\n    this.value = '';\n\n    if (this.nativeInput) {\n      this.nativeInput.value = '';\n    }\n  };\n\n  /**\n   * Função que renderiza o ícone dentro do campo de entrada.\n   */\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon\n            class=\"input__icon--color\"\n            size={this.label ? 'medium' : 'small'}\n            name={this.icon}\n            color=\"inherit\"\n          ></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza a label do campo de entrada.\n   */\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  /**\n   * Função que renderiza as mensagens de erro ou sucesso abaixo do campo de entrada.\n   */\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Valida o campo de entrada ao perder o foco.\n   */\n  private onBlurValidations() {\n    this.required && this.requiredValidation();\n    this.pattern && this.patternValidation();\n    (this.minlength || this.maxlength) && this.lengthValidation();\n    (this.min || this.max) && this.minMaxValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Realiza as validações do campo enquanto o usuário digita.\n   */\n  private onBdsInputValidations() {\n    this.type === 'email' && this.emailValidation();\n    this.type === 'phonenumber' && this.numberValidation();\n    this.checkValidity();\n  }\n\n  /**\n   * Valida o padrão regex do campo.\n   */\n  private patternValidation() {\n    const regex = new RegExp(this.pattern);\n    this.bdsPatternValidation.emit(regex.test(this.nativeInput.value));\n  }\n\n  /**\n   * Valida se o campo é obrigatório.\n   */\n  private requiredValidation() {\n    if (this.nativeInput.validity.valueMissing) {\n      this.validationMesage = this.requiredErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida o comprimento do texto no campo de entrada.\n   */\n  private lengthValidation() {\n    if (this.nativeInput.validity.tooShort) {\n      this.validationMesage = this.minlengthErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.tooLong) {\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida os valores mínimos e máximos do campo de entrada.\n   */\n  private minMaxValidation() {\n    if (this.nativeInput.validity.rangeUnderflow) {\n      this.validationMesage = this.minErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.rangeOverflow) {\n      this.validationMesage = this.maxErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um email válido.\n   */\n  private emailValidation() {\n    if (emailValidation(this.nativeInput.value)) {\n      this.validationMesage = this.emailErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Valida se o campo contém um número válido.\n   */\n  private numberValidation() {\n    if (numberValidation(this.nativeInput.value)) {\n      this.validationMesage = this.numberErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  /**\n   * Verifica se o campo de entrada é válido.\n   */\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  /**\n   * Atualiza o valor do campo de entrada após as mudanças.\n   */\n  componentDidUpdate() {\n    if (this.nativeInput && this.value != this.nativeInput.value) {\n      this.nativeInput.value = this.value;\n    }\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const Element = this.isTextarea ? 'textarea' : 'input';\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div\n          class={{\n            input: true,\n            'input--state-primary': !this.danger && !this.validationDanger,\n            'input--state-danger': this.danger || this.validationDanger,\n            'input--state-success': this.success,\n            'input--state-disabled': this.disabled,\n            'input--label': !!this.label,\n            'input--pressed': isPressed,\n          }}\n          onClick={this.onClickWrapper}\n          onKeyDown={this.keyPressWrapper}\n          part=\"input-container\"\n        >\n          {this.renderIcon()}\n          <slot name=\"input-left\"></slot>\n          <div class=\"input__container\">\n            {this.renderLabel()}\n            <div class={{ input__container__wrapper: !this.chips, input__container__wrapper__chips: this.chips }}>\n              <slot name=\"inside-input-left\"></slot>\n              <Element\n                class={{ input__container__text: true, input__container__text__chips: this.chips }}\n                ref={(input) => (this.nativeInput = input)}\n                rows={this.rows}\n                cols={this.cols}\n                autocapitalize={this.autoCapitalize}\n                autocomplete={this.autoComplete}\n                disabled={this.disabled}\n                min={this.min}\n                max={this.max}\n                minLength={this.minlength}\n                maxLength={this.maxlength}\n                name={this.inputName}\n                onBlur={this.onBlur}\n                onFocus={this.onFocus}\n                onInput={this.onInput}\n                placeholder={this.placeholder}\n                readOnly={this.readonly}\n                type={this.type}\n                value={this.encodeValue(this.value)}\n                pattern={this.pattern}\n                required={this.required}\n                part=\"input\"\n                data-test={this.dataTest}\n              ></Element>\n            </div>\n          </div>\n          {this.counterLength && (\n            <bds-counter-text\n              length={this.value.length}\n              max={this.maxlength}\n              active={isPressed}\n              {...this.counterLengthRule}\n            />\n          )}\n          {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"small\" />}\n          <slot name=\"input-right\" />\n        </div>\n        {this.renderMessage()}\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "emailValidation", "numberValidation", "Host"], "mappings": ";;;;;AAAA,MAAM,QAAQ,GAAG,8/TAA8/T;;MCUlgU,KAAK,GAAA,MAAA;AALlB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;AAQW,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAClB,QAAA,IAAU,CAAA,UAAA,GAAI,KAAK;AACnB,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AACtB,QAAA,IAAgB,CAAA,gBAAA,GAAI,KAAK;AAClC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAI,EAAE;AAEvB;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAe,MAAM;AAElD;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;AAEnB;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAyB,KAAK;AAEpD;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAuB,KAAK;AAsBhD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAYxB;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AAEnC;;AAEG;AACsB,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AAEnD;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AAErD;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACqC,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAElE;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAEhE;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjE;;AAEG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;AAEnD;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAI,KAAK;AAE9B;;AAEG;AACK,QAAA,IAAiB,CAAA,iBAAA,GAA6B,IAAI;AAE1D;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAExB;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAG,KAAK;AAE1B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,CAAC;AAEzB;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,CAAC;AAqCzB;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAExB,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AA8GhC;;AAEG;AACK,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;AACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;AACf,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAEjD,oBAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,IAAI,CAAC,cAAc,EAAE;wBACrB,KAAK,CAAC,cAAc,EAAE;;oBAExB;AACF,gBAAA,KAAK,WAAW;AAChB,gBAAA,KAAK,QAAQ;AACX,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC3D;;AAEN,SAAC;AAED;;AAEG;AACK,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;YACzC,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;AAEhC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;AACxB,SAAC;AAED;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;YAC1B,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACvB,SAAC;AAED;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,SAAC;AAED;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAED;;AAEG;AACK,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAU,KAAI;AACtC,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE;gBAC1C,EAAE,CAAC,cAAc,EAAE;gBACnB,EAAE,CAAC,eAAe,EAAE;;AAGtB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AAEf,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;;AAE/B,SAAC;AA8PF;AAhZC;;AAEG;AAEH,IAAA,MAAM,QAAQ,GAAA;QACZ,IAAI,CAAC,cAAc,EAAE;;AAGvB;;AAEG;AAEH,IAAA,MAAM,WAAW,GAAA;QACf,IAAI,CAAC,MAAM,EAAE;;AAGf;;AAEG;AAEH,IAAA,MAAM,eAAe,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW;;AAGzB;;AAEG;AAEH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;;AAGxC;;AAEG;AAEH,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;AAGjB;;AAEG;AACK,IAAA,WAAW,CAAC,KAAc,EAAA;QAChC,MAAM,EAAE,GAAG,IAAI,EACb,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,EAAE,GAAG,IAAI,EACT,GAAG,GAAG,IAAI,EACV,KAAK,GAAG,KAAK;QACnB,IAAG,CAAC,IAAI,CAAC,MAAM;AAAE,YAAA,OAAO,KAAK;AACzB,QAAA,QACE,KAAK;YACL;AACG,iBAAA,QAAQ;AACR,iBAAA,OAAO,CAAC,EAAE,EAAE,MAAM;AAClB,iBAAA,OAAO,CAAC,EAAE,EAAE,MAAM;AAClB,iBAAA,OAAO,CAAC,EAAE,EAAE,OAAO;AACnB,iBAAA,OAAO,CAAC,EAAE,EAAE,OAAO;AACnB,iBAAA,OAAO,CAAC,GAAG,EAAE,OAAO;AACpB,iBAAA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC;;AAI9B;;AAEG;AAEO,IAAA,YAAY,CAAC,QAAuB,EAAA;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAE;QACnF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;;AA8E7C;;AAEG;IACK,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACPA,OAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,oBAAoB,EAC1B,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EACrC,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EAAA,CACL,CACR,CACP;;AAIL;;AAEG;IACK,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACRA,OAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;AAIL;;AAEG;IACK,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACEA,iBAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvCA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/BA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACNA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;AAGlB;;AAEG;IACK,iBAAiB,GAAA;AACvB,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC1C,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxC,QAAA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,EAAE;AAC7D,QAAA,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,gBAAgB,EAAE;QACjD,IAAI,CAAC,aAAa,EAAE;;AAGtB;;AAEG;IACK,qBAAqB,GAAA;QAC3B,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;QAC/C,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE;QACtD,IAAI,CAAC,aAAa,EAAE;;AAGtB;;AAEG;IACK,iBAAiB,GAAA;QACvB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACtC,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;AAGpE;;AAEG;IACK,kBAAkB,GAAA;QACxB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE;AAC1C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;AACjD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAIhC;;AAEG;IACK,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB;AAClD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;QAGF,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;;AAIJ;;AAEG;IACK,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,EAAE;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;QAGF,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE;AAC3C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;;AAIJ;;AAEG;IACK,eAAe,GAAA;QACrB,IAAIC,2BAAe,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;AAC9C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAIhC;;AAEG;IACK,gBAAgB,GAAA;QACtB,IAAIC,4BAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB;AAC/C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAIhC;;AAEG;IACK,aAAa,GAAA;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;AAIjC;;AAEG;IACH,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YAC5D,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;;;IAIvC,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;AAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,OAAO;AAEtD,QAAA,QACEF,OAAA,CAACG,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChDH,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;AAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClBA,OAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,YAAY,EAAQ,CAAA,EAC/BA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,gCAAgC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAA,EAClGA,OAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,mBAAmB,EAAQ,CAAA,EACtCA,OAAA,CAAC,OAAO,EACN,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,KAAK,EAAE,EAClF,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,YAAY,EAAE,IAAI,CAAC,YAAY,EAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,IAAI,EAAC,OAAO,EACD,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,CACf,CACP,CACF,EACL,IAAI,CAAC,aAAa,KACjBA,OAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,EAAA,0CAAA,EACE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EACzB,GAAG,EAAE,IAAI,CAAC,SAAS,EACnB,MAAM,EAAE,SAAS,EAAA,EACb,IAAI,CAAC,iBAAiB,CAAA,CAC1B,CACH,EACA,IAAI,CAAC,OAAO,IAAIA,uEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,OAAO,EAAG,CAAA,EAC5FA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,aAAa,EAAA,CAAG,CACvB,EACL,IAAI,CAAC,aAAa,EAAE,CAChB;;;;;;;;;;"}