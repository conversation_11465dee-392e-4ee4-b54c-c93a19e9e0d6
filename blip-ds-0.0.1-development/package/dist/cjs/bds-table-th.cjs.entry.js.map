{"file": "bds-table-th.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,kBAAkB,GAAG,wyBAAwyB;;MCQtzB,eAAe,GAAA,MAAA;AAL5B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOW,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;AAChB,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAChB,QAAA,IAAK,CAAA,KAAA,GAAG,EAAE;AACV,QAAA,IAAc,CAAA,cAAA,GAAmB,MAAM;AAkChD;IAhCC,iBAAiB,GAAA;QACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;AAClD,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;AACjG,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;;IAGvB,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,CAAC,qBAAqB,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;gBAC5C,UAAU,EAAE,IAAI,CAAC,OAAO;AACxB,gBAAA,CAAC,YAAY,IAAI,CAAC,cAAc,CAAE,CAAA,GAAE;AACrC,aAAA,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,WAAW,EAAE,OAAO,EAAC,OAAO,EAAA,EACnEA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACC,EACV,IAAI,CAAC,QAAQ,IACZA,sBACE,IAAI,EAAC,OAAO,EACZ,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,YAAY,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,UAAU,GAAG,EAAE,EAAA,CACxE,IACV;;AAGA,SAAA,CACD;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/table/table-header-cell/table-header-cell.scss?tag=bds-table-th&encapsulation=scoped", "src/components/table/table-header-cell/table-header-cell.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-cell;\n  padding: 0px 8px;\n}\n.th_cell {\n  display: flex;\n  align-items: center;\n  height: 64px;\n  gap: 8px;\n  font-family: $font-family;\n  box-sizing: border-box;\n\n  &--sortable-true:hover, &--sortable-false:hover  {\n    cursor: pointer;\n  }\n}\n\n.justify {\n  &--left {\n    justify-content: flex-start;\n  }\n  &--center {\n    justify-content: center;\n  }\n  &--right {\n    justify-content: flex-end;\n  }\n}\n\n.dense-th {\n  min-height: 48px;\n  height: auto;\n}\n\n:host:first-child {\n  padding-left: 16px;\n}\n\n:host:last-child {\n  padding-right: 16px;\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\nexport type JustifyContent = 'left' | 'center' | 'right';\n@Component({\n  tag: 'bds-table-th',\n  styleUrl: 'table-header-cell.scss',\n  scoped: true,\n})\nexport class TableHeaderCell {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @Prop() sortable = false;\n  @Prop() arrow = '';\n  @Prop() justifyContent: JustifyContent = 'left';\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n  render(): HTMLElement {\n    return (\n      <Host>\n        <div\n          class={{\n            th_cell: true,\n            [`th_cell--sortable-${this.sortable}`]: true,\n            'dense-th': this.isDense,\n            [`justify--${this.justifyContent}`]:true\n          }}\n        >\n          <bds-typo bold={this.sortable ? 'bold' : 'semi-bold'} variant=\"fs-14\">\n            <slot />\n          </bds-typo>\n          {this.sortable ? (\n            <bds-icon\n              size=\"small\"\n              name={this.arrow === 'asc' ? 'arrow-down' : this.arrow === 'dsc' ? 'arrow-up' : ''}\n            ></bds-icon>\n          ) : ''\n            // <div style={{ width: '20px' }}></div>\n          }\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}