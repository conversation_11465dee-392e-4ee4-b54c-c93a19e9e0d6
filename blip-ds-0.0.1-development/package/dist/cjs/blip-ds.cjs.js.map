{"version": 3, "file": "blip-ds.cjs.js", "sources": ["node_modules/@stencil/core/internal/client/patch-browser.js", "@lazy-browser-entrypoint?app-data=conditional"], "sourcesContent": ["/*\n Stencil Client Patch Browser v4.35.1 | MIT Licensed | https://stenciljs.com\n */\n\n// src/client/client-patch-browser.ts\nimport { BUILD, NAMESPACE } from \"@stencil/core/internal/app-data\";\nimport { consoleDevInfo, H, promiseResolve, win } from \"@stencil/core\";\nvar patchBrowser = () => {\n  if (BUILD.isDev && !BUILD.isTesting) {\n    consoleDevInfo(\"Running in development mode.\");\n  }\n  if (BUILD.cloneNodeFix) {\n    patchCloneNodeFix(H.prototype);\n  }\n  const scriptElm = BUILD.scriptDataOpts ? win.document && Array.from(win.document.querySelectorAll(\"script\")).find(\n    (s) => new RegExp(`/${NAMESPACE}(\\\\.esm)?\\\\.js($|\\\\?|#)`).test(s.src) || s.getAttribute(\"data-stencil-namespace\") === NAMESPACE\n  ) : null;\n  const importMeta = import.meta.url;\n  const opts = BUILD.scriptDataOpts ? (scriptElm || {})[\"data-opts\"] || {} : {};\n  if (importMeta !== \"\") {\n    opts.resourcesUrl = new URL(\".\", importMeta).href;\n  }\n  return promiseResolve(opts);\n};\nvar patchCloneNodeFix = (HTMLElementPrototype) => {\n  const nativeCloneNodeFn = HTMLElementPrototype.cloneNode;\n  HTMLElementPrototype.cloneNode = function(deep) {\n    if (this.nodeName === \"TEMPLATE\") {\n      return nativeCloneNodeFn.call(this, deep);\n    }\n    const clonedNode = nativeCloneNodeFn.call(this, false);\n    const srcChildNodes = this.childNodes;\n    if (deep) {\n      for (let i = 0; i < srcChildNodes.length; i++) {\n        if (srcChildNodes[i].nodeType !== 2) {\n          clonedNode.appendChild(srcChildNodes[i].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nexport {\n  patchBrowser\n};\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { patchBrowser } from '@stencil/core/internal/client/patch-browser';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\npatchBrowser().then(async (options) => {\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n});\n"], "names": ["win", "NAMESPACE", "promiseResolve", "globalScripts", "bootstrapLazy"], "mappings": ";;;;;AAAA;AACA;AACA;;AAKA,IAAI,YAAY,GAAG,MAAM;AAOzB,EAAE,MAAM,SAAS,GAA0BA,SAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,CAACA,SAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;AACnH,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,EAAEC,eAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,wBAAwB,CAAC,KAAKA;AAC1H,GAAG,CAAO;AACV,EAAE,MAAM,UAAU,GAAG,gQAAe;AACpC,EAAE,MAAM,IAAI,GAA0B,CAAC,SAAS,IAAI,EAAE,EAAE,WAAW,CAAC,IAAI,EAAE,CAAK;AAC/E,EAAE,IAAI,UAAU,KAAK,EAAE,EAAE;AACzB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI;AACrD;AACA,EAAE,OAAOC,oBAAc,CAAC,IAAI,CAAC;AAC7B,CAAC;;ACnBD,YAAY,EAAE,CAAC,IAAI,CAAC,OAAO,OAAO,KAAK;AACvC,EAAE,MAAMC,mBAAa,EAAE;AACvB,EAAE,OAAOC,mBAAa,CAAC,4BAA4B,EAAE,OAAO,CAAC;AAC7D,CAAC,CAAC;;;;", "x_google_ignoreList": [0]}