{"file": "bds-stepper.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,ukBAAukB;;MCM7kB,UAAU,GAAA,MAAA;;;;IAGrB,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AAC1C,YAAA,MAAM,CAAC,KAAK,GAAG,KAAK;YACpB,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,gBAAA,MAAM,CAAC,IAAI,GAAG,IAAI;;AAEtB,SAAC,CAAC;;IAGJ,gBAAgB,GAAA;QACd,IAAI,CAAC,UAAU,EAAE;;AAGnB;;;;;AAKG;IAEI,MAAM,aAAa,CAAC,KAAa,EAAA;QACtC,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI;;AAGxC;;;;;AAKG;IAEI,MAAM,gBAAgB,CAAC,KAAa,EAAA;QACzC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,IAAI;;AAG3C;;;;AAIG;AAEI,IAAA,MAAM,aAAa,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;;AAGrE;;;;AAIG;AAEI,IAAA,MAAM,gBAAgB,GAAA;AAC3B,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;AAIvB;;;;AAIG;AAEI,IAAA,MAAM,mBAAmB,GAAA;AAC9B,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AACpC,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;AAI1B,IAAA,IAAY,YAAY,GAAA;AACtB,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;;IAGjD,UAAU,GAAA;QAChB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1C,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAEjD,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,KAAI;YAClD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE;gBACvC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC;;AAEvD,SAAC,CAAC;;IAGJ,MAAM,GAAA;QACJ,QACEA,OAAC,CAAAC,UAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,oBAAoB,EAAA,EAC9BD,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/stepper/stepper.scss?tag=bds-stepper&encapsulation=shadow", "src/components/stepper/stepper.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  width: 100%;\n  border-radius: 8px;\n  box-sizing: border-box;\n  padding: 16px;\n  display: flex;\n  justify-content: space-between;\n\n  ::slotted(bds-step:last-child) {\n    flex: inherit;\n  }\n}\n\n::slotted(.stepper__container__divisor) {\n  flex: 1 1 auto;\n  align-self: center;\n  height: 1.5px;\n  background: $color-content-disable;\n  margin: 0px 8px;\n  min-width: 24px;\n}\n\n::slotted(.stepper__container__divisor--completed) {\n  border-top: 2px solid $color-primary;\n}\n", "import { Component, ComponentInterface, h, Element, Method, Host } from '@stencil/core';\n@Component({\n  tag: 'bds-stepper',\n  styleUrl: 'stepper.scss',\n  shadow: true,\n})\nexport class BdsStepper implements ComponentInterface {\n  @Element() el: HTMLBdsStepperElement;\n\n  connectedCallback() {\n    this.childOptions.forEach((option, index) => {\n      option.index = index;\n      if (index === this.childOptions.length - 1) {\n        option.last = true;\n      }\n    });\n  }\n\n  componentDidLoad() {\n    this.renderLine();\n  }\n\n  /**\n   * Set the active step\n   *\n   * @param index The index of the step to be set as active\n   * @returns void\n   */\n  @Method()\n  public async setActiveStep(index: number): Promise<void> {\n    this.resetActiveSteps();\n    this.childOptions[index].active = true;\n  }\n\n  /**\n   * Set the completed step\n   *\n   * @param index The index of the step to be set as completed\n   * @returns void\n   */\n  @Method()\n  public async setCompletedStep(index: number): Promise<void> {\n    this.childOptions[index].completed = true;\n  }\n\n  /**\n   * Returns the active step\n   *\n   * @returns HTMLBdsStepElement\n   */\n  @Method()\n  public async getActiveStep(): Promise<number> {\n    return this.childOptions.find((step) => step.active === true).index;\n  }\n\n  /**\n   * Reset all active steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetActiveSteps() {\n    for (const step of this.childOptions) {\n      step.active = false;\n    }\n  }\n\n  /**\n   * Reset all completed steps\n   *\n   * @returns void\n   */\n  @Method()\n  public async resetCompletedSteps() {\n    for (const step of this.childOptions) {\n      step.completed = false;\n    }\n  }\n\n  private get childOptions(): HTMLBdsStepElement[] {\n    return Array.from(this.el.querySelectorAll('bds-step'));\n  }\n\n  private renderLine() {\n    const line = document.createElement('div');\n    line.classList.add('stepper__container__divisor');\n\n    Array.from(this.childOptions).forEach((item, idx) => {\n      if (this.childOptions.length - 1 != idx) {\n        item.insertAdjacentHTML('afterend', line.outerHTML);\n      }\n    });\n  }\n\n  render() {\n    return (\n      <Host class=\"stepper__container\">\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "version": 3}