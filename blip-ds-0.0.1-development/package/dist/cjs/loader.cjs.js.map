{"version": 3, "file": "loader.cjs.js", "sources": ["@lazy-external-entrypoint?app-data=conditional"], "sourcesContent": ["export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\nexport const defineCustomElements = async (win, options) => {\n  if (typeof window === 'undefined') return undefined;\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n};\n"], "names": ["globalScripts", "bootstrapLazy"], "mappings": ";;;;AAGY,MAAC,oBAAoB,GAAG,OAAO,GAAG,EAAE,OAAO,KAAK;AAC5D,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,OAAO,SAAS;AACrD,EAAE,MAAMA,mBAAa,EAAE;AACvB,EAAE,OAAOC,mBAAa,CAAC,4BAA4B,EAAE,OAAO,CAAC;AAC7D;;;;;"}