'use strict';

var index = require('./index-D_zq0Z7d.js');

const gridCss = ":host(.color-brand){background-color:var(--color-brand, rgb(0, 150, 250))}:host(.color-primary){background-color:var(--color-primary, rgb(30, 107, 241))}:host(.color-secondary){background-color:var(--color-secondary, rgb(41, 41, 41))}:host(.color-surface-0){background-color:var(--color-surface-0, rgb(255, 255, 255))}:host(.color-surface-1){background-color:var(--color-surface-1, rgb(246, 246, 246))}:host(.color-surface-2){background-color:var(--color-surface-2, rgb(237, 237, 237))}:host(.color-surface-3){background-color:var(--color-surface-3, rgb(227, 227, 227))}:host(.color-surface-4){background-color:var(--color-surface-4, rgb(20, 20, 20))}:host(.color-surface-positive){background-color:var(--color-surface-positive, rgb(1, 114, 62))}:host(.color-surface-negative){background-color:var(--color-surface-negative, rgb(138, 0, 0))}:host(.color-surface-primary){background-color:var(--color-surface-primary, rgb(30, 107, 241))}:host(.color-content-default){background-color:var(--color-content-default, rgb(40, 40, 40))}:host(.color-content-disable){background-color:var(--color-content-disable, rgb(89, 89, 89))}:host(.color-content-ghost){background-color:var(--color-content-ghost, rgb(140, 140, 140))}:host(.color-content-bright){background-color:var(--color-content-bright, rgb(255, 255, 255))}:host(.color-content-din){background-color:var(--color-content-din, rgb(0, 0, 0))}:host(.color-border-1){background-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}:host(.color-border-2){background-color:var(--color-border-2, rgba(0, 0, 0, 0.16))}:host(.color-border-3){background-color:var(--color-border-3, rgba(0, 0, 0, 0.06))}:host(.color-info){background-color:var(--color-info, rgb(128, 227, 235))}:host(.color-system){background-color:var(--color-system, rgb(178, 223, 253))}:host(.color-focus){background-color:var(--color-focus, rgb(194, 38, 251))}:host(.color-success){background-color:var(--color-success, rgb(132, 235, 188))}:host(.color-warning){background-color:var(--color-warning, rgb(253, 233, 155))}:host(.color-error){background-color:var(--color-error, rgb(250, 190, 190))}:host(.color-delete){background-color:var(--color-delete, rgb(230, 15, 15))}:host(.color-shadow-0){background-color:var(--color-shadow-0, rgba(0, 0, 0, 0.04))}:host(.color-shadow-1){background-color:var(--color-shadow-1, rgba(0, 0, 0, 0.16))}:host(.color-hover){background-color:var(--color-hover, rgba(0, 0, 0, 0.08))}:host(.color-pressed){background-color:var(--color-pressed, rgba(0, 0, 0, 0.16))}:host(.color-positive){background-color:var(--color-positive, #10603b)}:host(.color-negative){background-color:var(--color-negative, #e60f0f)}:host{display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.container){width:100%}:host(.xxsoffset--auto){margin-left:auto !important}:host(.xxsoffset--1){margin-left:8.33% !important}:host(.xxsoffset--2){margin-left:16.66% !important}:host(.xxsoffset--3){margin-left:24.99% !important}:host(.xxsoffset--4){margin-left:33.32% !important}:host(.xxsoffset--5){margin-left:41.65% !important}:host(.xxsoffset--6){margin-left:50% !important}:host(.xxsoffset--7){margin-left:58.33% !important}:host(.xxsoffset--8){margin-left:66.66% !important}:host(.xxsoffset--9){margin-left:74.99% !important}:host(.xxsoffset--10){margin-left:83.32% !important}:host(.xxsoffset--11){margin-left:91.65% !important}:host(.xxsoffset--12){margin-left:100% !important}@media (max-width: 599px){:host(.container){width:100%}:host(.xxsoffset--auto){margin-left:auto !important}:host(.xxsoffset--1){margin-left:8.33% !important}:host(.xxsoffset--2){margin-left:16.66% !important}:host(.xxsoffset--3){margin-left:24.99% !important}:host(.xxsoffset--4){margin-left:33.32% !important}:host(.xxsoffset--5){margin-left:41.65% !important}:host(.xxsoffset--6){margin-left:50% !important}:host(.xxsoffset--7){margin-left:58.33% !important}:host(.xxsoffset--8){margin-left:66.66% !important}:host(.xxsoffset--9){margin-left:74.99% !important}:host(.xxsoffset--10){margin-left:83.32% !important}:host(.xxsoffset--11){margin-left:91.65% !important}:host(.xxsoffset--12){margin-left:100% !important}}@media (min-width: 600px){:host(.container){width:100%}:host(.xxsoffset--auto){margin-left:auto !important}:host(.xsoffset--1){margin-left:8.33% !important}:host(.xsoffset--2){margin-left:16.66% !important}:host(.xsoffset--3){margin-left:24.99% !important}:host(.xsoffset--4){margin-left:33.32% !important}:host(.xsoffset--5){margin-left:41.65% !important}:host(.xsoffset--6){margin-left:50% !important}:host(.xsoffset--7){margin-left:58.33% !important}:host(.xsoffset--8){margin-left:66.66% !important}:host(.xsoffset--9){margin-left:74.99% !important}:host(.xsoffset--10){margin-left:83.32% !important}:host(.xsoffset--11){margin-left:91.65% !important}:host(.xsoffset--12){margin-left:100% !important}}@media (min-width: 905px){:host(.container){max-width:848px;margin-left:auto !important;margin-right:auto !important}:host(.smoffset--auto){margin-left:auto !important}:host(.smoffset--1){margin-left:8.33% !important}:host(.smoffset--2){margin-left:16.66% !important}:host(.smoffset--3){margin-left:24.99% !important}:host(.smoffset--4){margin-left:33.32% !important}:host(.smoffset--5){margin-left:41.65% !important}:host(.smoffset--6){margin-left:50% !important}:host(.smoffset--7){margin-left:58.33% !important}:host(.smoffset--8){margin-left:66.66% !important}:host(.smoffset--9){margin-left:74.99% !important}:host(.smoffset--10){margin-left:83.32% !important}:host(.smoffset--11){margin-left:91.65% !important}:host(.smoffset--12){margin-left:100% !important}}@media (min-width: 993px){:host(.container){max-width:944px;margin-left:auto !important;margin-right:auto !important}:host(.mdoffset--auto){margin-left:auto !important}:host(.mdoffset--1){margin-left:8.33% !important}:host(.mdoffset--2){margin-left:16.66% !important}:host(.mdoffset--3){margin-left:24.99% !important}:host(.mdoffset--4){margin-left:33.32% !important}:host(.mdoffset--5){margin-left:41.65% !important}:host(.mdoffset--6){margin-left:50% !important}:host(.mdoffset--7){margin-left:58.33% !important}:host(.mdoffset--8){margin-left:66.66% !important}:host(.mdoffset--9){margin-left:74.99% !important}:host(.mdoffset--10){margin-left:83.32% !important}:host(.mdoffset--11){margin-left:91.65% !important}:host(.mdoffset--12){margin-left:100% !important}}@media (min-width: 1601px){:host(.container){max-width:1328px;margin-left:auto !important;margin-right:auto !important}:host(.lgoffset--auto){margin-left:auto !important}:host(.lgoffset--1){margin-left:8.33% !important}:host(.lgoffset--2){margin-left:16.66% !important}:host(.lgoffset--3){margin-left:24.99% !important}:host(.lgoffset--4){margin-left:33.32% !important}:host(.lgoffset--5){margin-left:41.65% !important}:host(.lgoffset--6){margin-left:50% !important}:host(.lgoffset--7){margin-left:58.33% !important}:host(.lgoffset--8){margin-left:66.66% !important}:host(.lgoffset--9){margin-left:74.99% !important}:host(.lgoffset--10){margin-left:83.32% !important}:host(.lgoffset--11){margin-left:91.65% !important}:host(.lgoffset--12){margin-left:100% !important}}@media (min-width: 1921px){:host(.container){max-width:1424px;margin-left:auto !important;margin-right:auto !important}:host(.xgoffset--auto){margin-left:auto !important}:host(.xgoffset--1){margin-left:8.33% !important}:host(.xgoffset--2){margin-left:16.66% !important}:host(.xgoffset--3){margin-left:24.99% !important}:host(.xgoffset--4){margin-left:33.32% !important}:host(.xgoffset--5){margin-left:41.65% !important}:host(.xgoffset--6){margin-left:50% !important}:host(.xgoffset--7){margin-left:58.33% !important}:host(.xgoffset--8){margin-left:66.66% !important}:host(.xgoffset--9){margin-left:74.99% !important}:host(.xgoffset--10){margin-left:83.32% !important}:host(.xgoffset--11){margin-left:91.65% !important}:host(.xgoffset--12){margin-left:100% !important}}@media (min-width: 600px){:host(.container-fluid){max-width:100%;margin-left:auto !important;margin-right:auto !important}}@media (min-width: 905px){:host(.container-fluid){max-width:848px;margin-left:auto !important;margin-right:auto !important}}@media (min-width: 993px){:host(.container-fluid){max-width:944px;margin-left:auto !important;margin-right:auto !important}}@media (min-width: 1280px){:host(.container-fluid){max-width:1232px;margin-left:auto !important;margin-right:auto !important}}@media (min-width: 1440px){:host(.container-fluid){max-width:1328px;margin-left:auto !important;margin-right:auto !important}}@media (min-width: 1920px){:host(.container-fluid){max-width:1424px;margin-left:auto !important;margin-right:auto !important}}:host(.flex_wrap--wrap){-ms-flex-wrap:wrap;flex-wrap:wrap}:host(.flex_wrap--wrap-reverse){-ms-flex-wrap:wrap-reverse;flex-wrap:wrap-reverse}:host(.direction--row){-ms-flex-direction:row;flex-direction:row}:host(.direction--column){-ms-flex-direction:column;flex-direction:column}:host(.direction--row-reverse){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.direction--column-reverse){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.justify_content--center){-ms-flex-pack:center;justify-content:center}:host(.justify_content--flex-start){-ms-flex-pack:start;justify-content:flex-start}:host(.justify_content--flex-end){-ms-flex-pack:end;justify-content:flex-end}:host(.justify_content--space-between){-ms-flex-pack:justify;justify-content:space-between}:host(.justify_content--space-around){-ms-flex-pack:distribute;justify-content:space-around}:host(.justify_content--space-evenly){-ms-flex-pack:space-evenly;justify-content:space-evenly}:host(.justify_content--stretch){-ms-flex-pack:stretch;justify-content:stretch}:host(.align_items--flex-start){-ms-flex-align:start;align-items:flex-start}:host(.align_items--flex-end){-ms-flex-align:end;align-items:flex-end}:host(.align_items--center){-ms-flex-align:center;align-items:center}:host(.align_items--stretch){-ms-flex-align:stretch;align-items:stretch}:host(.align_items--baseline){-ms-flex-align:baseline;align-items:baseline}:host(.gap--none){gap:0}:host(.gap--half){gap:4px}:host(.gap--1){gap:8px}:host(.gap--2){gap:16px}:host(.gap--3){gap:24px}:host(.gap--4){gap:32px}:host(.gap--5){gap:40px}:host(.gap--6){gap:48px}:host(.gap--7){gap:56px}:host(.gap--8){gap:64px}:host(.gap--9){gap:72px}:host(.gap--10){gap:80px}:host(.gap--11){gap:88px}:host(.gap--12){gap:96px}:host(.xxs--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.xxs--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.xxs--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}@media (min-width: 600px){:host(.xs--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.xs--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.xs--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.xs--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.xs--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.xs--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.xs--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.xs--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.xs--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.xs--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.xs--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.xs--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.xs--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}}@media (min-width: 905px){:host(.sm--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.sm--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.sm--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.sm--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.sm--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.sm--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.sm--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.sm--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.sm--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.sm--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.sm--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.sm--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.sm--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}}@media (min-width: 993px){:host(.md--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.md--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.md--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.md--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.md--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.md--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.md--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.md--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.md--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.md--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.md--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.md--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.md--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}}@media (min-width: 1601px){:host(.lg--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.lg--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.lg--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.lg--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.lg--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.lg--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.lg--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.lg--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.lg--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.lg--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.lg--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.lg--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.lg--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}}@media (min-width: 1921px){:host(.xg--auto){-ms-flex:1 0 auto;flex:1 0 auto;width:auto;padding-left:8px !important;padding-right:8px !important}:host(.xg--1){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%;padding-left:8px !important;padding-right:8px !important}:host(.xg--2){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%;padding-left:8px !important;padding-right:8px !important}:host(.xg--3){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%;padding-left:8px !important;padding-right:8px !important}:host(.xg--4){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%;padding-left:8px !important;padding-right:8px !important}:host(.xg--5){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%;padding-left:8px !important;padding-right:8px !important}:host(.xg--6){-ms-flex:0 0 auto;flex:0 0 auto;width:50%;padding-left:8px !important;padding-right:8px !important}:host(.xg--7){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%;padding-left:8px !important;padding-right:8px !important}:host(.xg--8){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%;padding-left:8px !important;padding-right:8px !important}:host(.xg--9){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%;padding-left:8px !important;padding-right:8px !important}:host(.xg--10){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%;padding-left:8px !important;padding-right:8px !important}:host(.xg--11){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%;padding-left:8px !important;padding-right:8px !important}:host(.xg--12){-ms-flex:0 0 auto;flex:0 0 auto;width:100%;padding-left:8px !important;padding-right:8px !important}}@media (min-width: 1280px){:host(.lg--auto .container-fluid){-ms-flex:1 0 auto;flex:1 0 auto;width:auto}:host(.lg--1.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%}:host(.lg--2.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%}:host(.lg--3.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%}:host(.lg--4.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%}:host(.lg--5.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%}:host(.lg--6.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:50%}:host(.lg--7.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%}:host(.lg--8.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%}:host(.lg--9.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%}:host(.lg--10.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%}:host(.lg--11.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%}:host(.lg--12.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:100%}}@media (min-width: 1440px){:host(.xg--auto .container-fluid){-ms-flex:1 0 auto;flex:1 0 auto;width:auto}:host(.xg--1.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%}:host(.xg--2.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%}:host(.xg--3.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%}:host(.xg--4.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%}:host(.xg--5.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%}:host(.xg--6.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:50%}:host(.xg--7.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%}:host(.xg--8.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%}:host(.xg--9.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%}:host(.xg--10.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%}:host(.xg--11.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%}:host(.xg--12.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:100%}}@media (min-width: 1920px){:host(.xxg--auto .container-fluid){-ms-flex:1 0 auto;flex:1 0 auto;width:auto}:host(.xxg--1.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:8.33%}:host(.xxg--2.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:16.66%}:host(.xxg--3.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:24.99%}:host(.xxg--4.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:33.32%}:host(.xxg--5.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:41.65%}:host(.xxg--6.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:50%}:host(.xxg--7.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:58.33%}:host(.xxg--8.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:66.66%}:host(.xxg--9.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:74.99%}:host(.xxg--10.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:83.32%}:host(.xxg--11.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:91.65%}:host(.xxg--12.container-fluid){-ms-flex:0 0 auto;flex:0 0 auto;width:100%}}:host(.padding--none){padding:0 !important}:host(.padding--l-none){padding-left:0 !important}:host(.padding--r-none){padding-right:0 !important}:host(.padding--t-none){padding-top:0 !important}:host(.padding--b-none){padding-bottom:0 !important}:host(.padding--x-none){padding-left:0 !important;padding-right:0 !important}:host(.padding--y-none){padding-top:0 !important;padding-bottom:0 !important}:host(.padding--half){padding:4px !important}:host(.padding--l-half){padding-left:4px !important}:host(.padding--r-half){padding-right:4px !important}:host(.padding--t-half){padding-top:4px !important}:host(.padding--b-half){padding-bottom:4px !important}:host(.padding--x-half){padding-left:4px !important;padding-right:4px !important}:host(.padding--y-half){padding-top:4px !important;padding-bottom:4px !important}:host(.padding--1){padding:8px !important}:host(.padding--l-1){padding-left:8px !important}:host(.padding--r-1){padding-right:8px !important}:host(.padding--t-1){padding-top:8px !important}:host(.padding--b-1){padding-bottom:8px !important}:host(.padding--x-1){padding-left:8px !important;padding-right:8px !important}:host(.padding--y-1){padding-top:8px !important;padding-bottom:8px !important}:host(.padding--2){padding:16px !important}:host(.padding--l-2){padding-left:16px !important}:host(.padding--r-2){padding-right:16px !important}:host(.padding--t-2){padding-top:16px !important}:host(.padding--b-2){padding-bottom:16px !important}:host(.padding--x-2){padding-left:16px !important;padding-right:16px !important}:host(.padding--y-2){padding-top:16px !important;padding-bottom:16px !important}:host(.padding--3){padding:24px !important}:host(.padding--l-3){padding-left:24px !important}:host(.padding--r-3){padding-right:24px !important}:host(.padding--t-3){padding-top:24px !important}:host(.padding--b-3){padding-bottom:24px !important}:host(.padding--x-3){padding-left:24px !important;padding-right:24px !important}:host(.padding--y-3){padding-top:24px !important;padding-bottom:24px !important}:host(.padding--4){padding:32px !important}:host(.padding--l-4){padding-left:32px !important}:host(.padding--r-4){padding-right:32px !important}:host(.padding--t-4){padding-top:32px !important}:host(.padding--b-4){padding-bottom:32px !important}:host(.padding--x-4){padding-left:32px !important;padding-right:32px !important}:host(.padding--y-4){padding-top:32px !important;padding-bottom:32px !important}:host(.padding--5){padding:40px !important}:host(.padding--l-5){padding-left:40px !important}:host(.padding--r-5){padding-right:40px !important}:host(.padding--t-5){padding-top:40px !important}:host(.padding--b-5){padding-bottom:40px !important}:host(.padding--x-5){padding-left:40px !important;padding-right:40px !important}:host(.padding--y-5){padding-top:40px !important;padding-bottom:40px !important}:host(.padding--6){padding:48px !important}:host(.padding--l-6){padding-left:48px !important}:host(.padding--r-6){padding-right:48px !important}:host(.padding--t-6){padding-top:48px !important}:host(.padding--b-6){padding-bottom:48px !important}:host(.padding--x-6){padding-left:48px !important;padding-right:48px !important}:host(.padding--y-6){padding-top:48px !important;padding-bottom:48px !important}:host(.padding--7){padding:56px !important}:host(.padding--l-7){padding-left:56px !important}:host(.padding--r-7){padding-right:56px !important}:host(.padding--t-7){padding-top:56px !important}:host(.padding--b-7){padding-bottom:56px !important}:host(.padding--x-7){padding-left:56px !important;padding-right:56px !important}:host(.padding--y-7){padding-top:56px !important;padding-bottom:56px !important}:host(.padding--8){padding:64px !important}:host(.padding--l-8){padding-left:64px !important}:host(.padding--r-8){padding-right:64px !important}:host(.padding--t-8){padding-top:64px !important}:host(.padding--b-8){padding-bottom:64px !important}:host(.padding--x-8){padding-left:64px !important;padding-right:64px !important}:host(.padding--y-8){padding-top:64px !important;padding-bottom:64px !important}:host(.padding--9){padding:72px !important}:host(.padding--l-9){padding-left:72px !important}:host(.padding--r-9){padding-right:72px !important}:host(.padding--t-9){padding-top:72px !important}:host(.padding--b-9){padding-bottom:72px !important}:host(.padding--x-9){padding-left:72px !important;padding-right:72px !important}:host(.padding--y-9){padding-top:72px !important;padding-bottom:72px !important}:host(.padding--10){padding:80px !important}:host(.padding--l-10){padding-left:80px !important}:host(.padding--r-10){padding-right:80px !important}:host(.padding--t-10){padding-top:80px !important}:host(.padding--b-10){padding-bottom:80px !important}:host(.padding--x-10){padding-left:80px !important;padding-right:80px !important}:host(.padding--y-10){padding-top:80px !important;padding-bottom:80px !important}:host(.padding--11){padding:88px !important}:host(.padding--l-11){padding-left:88px !important}:host(.padding--r-11){padding-right:88px !important}:host(.padding--t-11){padding-top:88px !important}:host(.padding--b-11){padding-bottom:88px !important}:host(.padding--x-11){padding-left:88px !important;padding-right:88px !important}:host(.padding--y-11){padding-top:88px !important;padding-bottom:88px !important}:host(.padding--12){padding:96px !important}:host(.padding--l-12){padding-left:96px !important}:host(.padding--r-12){padding-right:96px !important}:host(.padding--t-12){padding-top:96px !important}:host(.padding--b-12){padding-bottom:96px !important}:host(.padding--x-12){padding-left:96px !important;padding-right:96px !important}:host(.padding--y-12){padding-top:96px !important;padding-bottom:96px !important}:host(.margin--auto){margin:auto !important}:host(.margin--l-auto){margin-left:auto !important}:host(.margin--r-auto){margin-right:auto !important}:host(.margin--t-auto){margin-top:auto !important}:host(.margin--b-auto){margin-bottom:auto !important}:host(.margin--x-auto){margin-left:auto !important;margin-right:auto !important}:host(.margin--y-auto){margin-top:auto !important;margin-bottom:auto !important}:host(.margin--none){margin:0 !important}:host(.margin--l-none){margin-left:0 !important}:host(.margin--r-none){margin-right:0 !important}:host(.margin--t-none){margin-top:0 !important}:host(.margin--b-none){margin-bottom:0 !important}:host(.margin--x-none){margin-left:0 !important;margin-right:0 !important}:host(.margin--y-none){margin-top:0 !important;margin-bottom:0 !important}:host(.margin--half){margin:4px !important}:host(.margin--l-half){margin-left:4px !important}:host(.margin--r-half){margin-right:4px !important}:host(.margin--t-half){margin-top:4px !important}:host(.margin--b-half){margin-bottom:4px !important}:host(.margin--x-half){margin-left:4px !important;margin-right:4px !important}:host(.margin--y-half){margin-top:4px !important;margin-bottom:4px !important}:host(.margin--1){margin:8px !important}:host(.margin--l-1){margin-left:8px !important}:host(.margin--r-1){margin-right:8px !important}:host(.margin--t-1){margin-top:8px !important}:host(.margin--b-1){margin-bottom:8px !important}:host(.margin--x-1){margin-left:8px !important;margin-right:8px !important}:host(.margin--y-1){margin-top:8px !important;margin-bottom:8px !important}:host(.margin--2){margin:16px !important}:host(.margin--l-2){margin-left:16px !important}:host(.margin--r-2){margin-right:16px !important}:host(.margin--t-2){margin-top:16px !important}:host(.margin--b-2){margin-bottom:16px !important}:host(.margin--x-2){margin-left:16px !important;margin-right:16px !important}:host(.margin--y-2){margin-top:16px !important;margin-bottom:16px !important}:host(.margin--3){margin:24px !important}:host(.margin--l-3){margin-left:24px !important}:host(.margin--r-3){margin-right:24px !important}:host(.margin--t-3){margin-top:24px !important}:host(.margin--b-3){margin-bottom:24px !important}:host(.margin--x-3){margin-left:24px !important;margin-right:24px !important}:host(.margin--y-3){margin-top:24px !important;margin-bottom:24px !important}:host(.margin--4){margin:32px !important}:host(.margin--l-4){margin-left:32px !important}:host(.margin--r-4){margin-right:32px !important}:host(.margin--t-4){margin-top:32px !important}:host(.margin--b-4){margin-bottom:32px !important}:host(.margin--x-4){margin-left:32px !important;margin-right:32px !important}:host(.margin--y-4){margin-top:32px !important;margin-bottom:32px !important}:host(.margin--5){margin:40px !important}:host(.margin--l-5){margin-left:40px !important}:host(.margin--r-5){margin-right:40px !important}:host(.margin--t-5){margin-top:40px !important}:host(.margin--b-5){margin-bottom:40px !important}:host(.margin--x-5){margin-left:40px !important;margin-right:40px !important}:host(.margin--y-5){margin-top:40px !important;margin-bottom:40px !important}:host(.margin--6){margin:48px !important}:host(.margin--l-6){margin-left:48px !important}:host(.margin--r-6){margin-right:48px !important}:host(.margin--t-6){margin-top:48px !important}:host(.margin--b-6){margin-bottom:48px !important}:host(.margin--x-6){margin-left:48px !important;margin-right:48px !important}:host(.margin--y-6){margin-top:48px !important;margin-bottom:48px !important}:host(.margin--7){margin:56px !important}:host(.margin--l-7){margin-left:56px !important}:host(.margin--r-7){margin-right:56px !important}:host(.margin--t-7){margin-top:56px !important}:host(.margin--b-7){margin-bottom:56px !important}:host(.margin--x-7){margin-left:56px !important;margin-right:56px !important}:host(.margin--y-7){margin-top:56px !important;margin-bottom:56px !important}:host(.margin--8){margin:64px !important}:host(.margin--l-8){margin-left:64px !important}:host(.margin--r-8){margin-right:64px !important}:host(.margin--t-8){margin-top:64px !important}:host(.margin--b-8){margin-bottom:64px !important}:host(.margin--x-8){margin-left:64px !important;margin-right:64px !important}:host(.margin--y-8){margin-top:64px !important;margin-bottom:64px !important}:host(.margin--9){margin:72px !important}:host(.margin--l-9){margin-left:72px !important}:host(.margin--r-9){margin-right:72px !important}:host(.margin--t-9){margin-top:72px !important}:host(.margin--b-9){margin-bottom:72px !important}:host(.margin--x-9){margin-left:72px !important;margin-right:72px !important}:host(.margin--y-9){margin-top:72px !important;margin-bottom:72px !important}:host(.margin--10){margin:80px !important}:host(.margin--l-10){margin-left:80px !important}:host(.margin--r-10){margin-right:80px !important}:host(.margin--t-10){margin-top:80px !important}:host(.margin--b-10){margin-bottom:80px !important}:host(.margin--x-10){margin-left:80px !important;margin-right:80px !important}:host(.margin--y-10){margin-top:80px !important;margin-bottom:80px !important}:host(.margin--11){margin:88px !important}:host(.margin--l-11){margin-left:88px !important}:host(.margin--r-11){margin-right:88px !important}:host(.margin--t-11){margin-top:88px !important}:host(.margin--b-11){margin-bottom:88px !important}:host(.margin--x-11){margin-left:88px !important;margin-right:88px !important}:host(.margin--y-11){margin-top:88px !important;margin-bottom:88px !important}:host(.margin--12){margin:96px !important}:host(.margin--l-12){margin-left:96px !important}:host(.margin--r-12){margin-right:96px !important}:host(.margin--t-12){margin-top:96px !important}:host(.margin--b-12){margin-bottom:96px !important}:host(.margin--x-12){margin-left:96px !important;margin-right:96px !important}:host(.margin--y-12){margin-top:96px !important;margin-bottom:96px !important}";

const Grid = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
    }
    render() {
        return (index.h(index.Host, { key: 'decdd9f4d09b1f7ca667789c6daa457126edc8bc', class: {
                host: true,
                [`direction--${this.direction}`]: true,
                [`justify_content--${this.justifyContent}`]: true,
                [`${this.container === true ? 'container' : ''}`]: true,
                [`${this.containerFluid === true ? 'container-fluid' : ''}`]: true,
                [`flex_wrap--${this.flexWrap}`]: true,
                [`align_items--${this.alignItems}`]: true,
                [`xxs--${this.xxs}`]: true,
                [`xs--${this.xs}`]: true,
                [`sm--${this.sm}`]: true,
                [`md--${this.md}`]: true,
                [`lg--${this.lg}`]: true,
                [`xg--${this.xg}`]: true,
                [`gap--${this.gap}`]: true,
                [`xxsoffset--${this.xxsOffset}`]: true,
                [`xsoffset--${this.xsOffset}`]: true,
                [`smoffset--${this.smOffset}`]: true,
                [`mdoffset--${this.mdOffset}`]: true,
                [`lgoffset--${this.lgOffset}`]: true,
                [`xgoffset--${this.xgOffset}`]: true,
                [`padding--${this.padding}`]: true,
                [`margin--${this.margin}`]: true,
                [this.bgColor || '']: true,
            }, style: { height: this.height } }, index.h("slot", { key: '171193660df286f6bc53661bfedfdf09e1eab1e7' })));
    }
};
Grid.style = gridCss;

const paperCss = ".bds-hover:hover{mix-blend-mode:multiply}.focus::before{content:\"\";position:absolute;inset:-4px;border:2px solid transparent;border-radius:4px}.focus:focus-visible{outline:none}.focus:focus-visible::before{border-color:var(--color-focus, rgb(194, 38, 251))}.disabled{pointer-events:none}.bg-surface-1{background-color:var(--color-surface-1, rgb(246, 246, 246))}.bg-surface-2{background-color:var(--color-surface-2, rgb(237, 237, 237))}.bg-surface-3{background-color:var(--color-surface-3, rgb(227, 227, 227))}.bg-surface-4{background-color:var(--color-surface-4, rgb(20, 20, 20))}:host{display:block;border-radius:16px}:host(.border){border:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2));margin:-1px}:host(.bg-surface-0){background-color:var(--color-surface-0, rgb(255, 255, 255))}:host(.bg-surface-1){background-color:var(--color-surface-1, rgb(246, 246, 246))}:host(.bg-surface-2){background-color:var(--color-surface-2, rgb(237, 237, 237))}:host(.bg-surface-3){background-color:var(--color-surface-3, rgb(227, 227, 227))}:host(.bg-surface-4){background-color:var(--color-surface-4, rgb(20, 20, 20))}:host(.border-1){border-color:var(--color-border-1, rgba(0, 0, 0, 0.2))}:host(.border-2){border-color:var(--color-border-2, rgba(0, 0, 0, 0.16))}:host(.border-3){border-color:var(--color-border-3, rgba(0, 0, 0, 0.06))}:host(.border-primary){border-color:var(--color-primary, rgb(30, 107, 241))}:host(.border-secondary){border-color:var(--color-secondary, rgb(41, 41, 41))}:host(.border-positive){border-color:var(--color-positive, #10603b)}:host(.border-negative){border-color:var(--color-negative, #e60f0f)}:host(.border-warning){border-color:var(--color-warning, rgb(253, 233, 155))}:host(.border-error){border-color:var(--color-error, rgb(250, 190, 190))}:host(.border-success){border-color:var(--color-success, rgb(132, 235, 188))}:host(.border-delete){border-color:var(--color-delete, rgb(230, 15, 15))}:host(.paper__elevation--none){-webkit-box-shadow:none;box-shadow:none}:host(.paper__elevation--static){-webkit-box-shadow:0px 2px 8px -2px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 2px 8px -2px var(--color-shadow-1, rgba(0, 0, 0, 0.16))}:host(.paper__elevation--primary){-webkit-box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 6px 16px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16))}:host(.paper__elevation--secondary){-webkit-box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16));box-shadow:0px 8px 4px -4px var(--color-shadow-0, rgba(0, 0, 0, 0.04)), 0px 12px 12px -4px var(--color-shadow-1, rgba(0, 0, 0, 0.16))}.paper__display{display:contents}";

const Paper = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
        this.hasBorder = true;
        /**
         * Size. Entered as one of the size. Can be one of:
         * 'static', 'primary', 'secondary';
         */
        this.elevation = 'static';
        /**
         * Data test is the prop to specifically test the component action object.
         */
        this.dataTest = null;
        /**
         * Prop for set the border of the component.
         */
        this.border = false;
        /**
         * Prop for set the height of the component.
         */
        this.height = null;
        /**
         * Prop for set the width of the component.
         */
        this.width = null;
        /**
         * Prop for set the background color.
         */
        this.bgColor = 'surface-1';
        /**
         * Prop for set the border color.
         */
        this.borderColor = null;
    }
    componentWillLoad() {
        this.border === true ? (this.hasBorder = false) : (this.hasBorder = true);
    }
    render() {
        return (index.h(index.Host, { key: 'e4fd80f9d8336e6ebc926d346e12ed8c1b1d68ab', class: {
                [`paper__elevation--${this.elevation}`]: this.hasBorder,
                border: this.border,
                [`bg-${this.bgColor}`]: true,
                [`border-${this.borderColor}`]: true,
            }, style: { height: `${this.height}`, width: `${this.width}` } }, index.h("div", { key: '072168b3a626e925953c0a9031695576a7a3f18a', class: "paper__display", "data-test": this.dataTest }, index.h("slot", { key: 'e56012c1f1bffa2263992477c0f773e6b6797934' }))));
    }
};
Paper.style = paperCss;

const testComponentCss = "";

const TestComponent = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
    }
    render() {
        return (index.h("bds-grid", { key: '88efd6d547891be214f5fefe9348213cc39a3414', xxs: "12", padding: "x-2", "flex-wrap": "wrap" }, index.h("bds-grid", { key: '2fbb560353160afbe6f177ca27f2f940e67cfb45', xxs: "12", margin: "t-2" }, index.h("div", { key: '5e57a4d2586c9f69224a7e360dc963c2d3f47366', class: "titulo" }, index.h("bds-typo", { key: '015f54de57ee7180d2476dcabc6eded6a5969d09', variant: "fs-40", bold: "bold" }, "Titulo de teste fora de temas"))), index.h("bds-grid", { key: '22eaf66af5b786f73ca4fca8f8e4abc211f9ee94', xxs: "6", padding: "r-1" }, index.h("bds-theme-provider", { key: '62243b6d6d204c49160228a6492d4f11f806c63a', theme: "light" }, index.h("bds-paper", { key: '3bdc503e8021e587fa37b64729c702bddd1145cc', elevation: "none", border: true }, index.h("bds-grid", { key: 'aa64589fe02879b3ffe10915ff7d237f06e92cbb', padding: "2" })))), index.h("bds-grid", { key: 'b23b2a8eb164847c305f922c5ac5a0322bdb20bd', xxs: "6", padding: "l-1" }, index.h("bds-theme-provider", { key: '0d97d4e82b7f29492b8feb346912c4f56077b986', theme: "dark" }, index.h("bds-paper", { key: '8bc125ddfbc88305d7e2df36682f1e0d10ccaa85', elevation: "none", border: true }, index.h("bds-grid", { key: 'e9bab4f325fd3aec2dbf2a22aee4620c53142171', padding: "2" }))))));
    }
};
TestComponent.style = testComponentCss;

const themeProviderCss = ":host(.theme--light){width:100%;height:100%;--color-brand:rgb(0, 150, 250);--color-primary:rgb(30, 107, 241);--color-secondary:rgb(41, 41, 41);--color-surface-0:rgb(255, 255, 255);--color-surface-1:rgb(246, 246, 246);--color-surface-2:rgb(237, 237, 237);--color-surface-3:rgb(227, 227, 227);--color-surface-4:rgb(20, 20, 20);--color-surface-positive:rgb(1, 114, 62);--color-surface-negative:rgb(138, 0, 0);--color-surface-primary:rgb(30, 107, 241);--color-content-default:rgb(40, 40, 40);--color-content-disable:rgb(89, 89, 89);--color-content-ghost:rgb(140, 140, 140);--color-content-bright:rgb(255, 255, 255);--color-content-din:rgb(0, 0, 0);--color-border-1:rgba(0, 0, 0, 0.2);--color-border-2:rgba(0, 0, 0, 0.16);--color-border-3:rgba(0, 0, 0, 0.06);--color-positive:rgb(0, 122, 66);--color-negative:rgb(168, 11, 11);--color-info:rgb(128, 227, 235);--color-system:rgb(178, 223, 253);--color-focus:rgb(194, 38, 251);--color-success:rgb(132, 235, 188);--color-warning:rgb(253, 233, 155);--color-error:rgb(250, 190, 190);--color-delete:rgb(230, 15, 15);--color-extended-blue:rgb(25, 104, 240);--color-extended-blue-bright:rgb(178, 223, 253);--color-extended-ocean:rgb(0, 211, 228);--color-extended-ocean-bright:rgb(128, 227, 235);--color-extended-green:rgb(53, 222, 144);--color-extended-green-bright:rgb(132, 235, 188);--color-extended-yellow:rgb(251, 207, 35);--color-extended-yellow-bright:rgb(253, 233, 155);--color-extended-orange:rgb(240, 99, 5);--color-extended-orange-bright:rgb(252, 170, 115);--color-extended-red:rgb(230, 15, 15);--color-extended-red-bright:rgb(249, 159, 159);--color-extended-pink:rgb(251, 75, 193);--color-extended-pink-bright:rgb(253, 155, 220);--color-extended-gray:rgb(102, 102, 102);--color-extended-gray-bright:rgb(199, 199, 199);--color-hover:rgba(0, 0, 0, 0.08);--color-pressed:rgba(0, 0, 0, 0.16);--color-shadow-0:rgba(0, 0, 0, 0.04);--color-shadow-1:rgba(0, 0, 0, 0.16)}:host(.theme--dark){width:100%;height:100%;--color-brand:rgb(0, 150, 250);--color-primary:rgb(73, 139, 255);--color-secondary:rgb(255, 255, 255);--color-surface-0:rgb(66, 66, 66);--color-surface-1:rgb(57, 57, 57);--color-surface-2:rgb(31, 31, 31);--color-surface-3:rgb(20, 20, 20);--color-surface-4:rgb(10, 10, 10);--color-surface-positive:rgb(1, 86, 47);--color-surface-negative:rgb(87, 0, 0);--color-surface-primary:rgb(12, 80, 197);--color-content-default:rgb(255, 255, 255);--color-content-disable:rgb(148, 148, 148);--color-content-ghost:rgb(102, 102, 102);--color-content-bright:rgb(255, 255, 255);--color-content-din:rgb(0, 0, 0);--color-border-1:rgba(255, 255, 255, 0.2);--color-border-2:rgba(255, 255, 255, 0.16);--color-border-3:rgba(255, 255, 255, 0.06);--color-positive:rgb(107, 255, 188);--color-negative:rgb(255, 184, 184);--color-info:rgb(0, 79, 86);--color-system:rgb(0, 60, 100);--color-focus:rgb(194, 38, 251);--color-success:rgb(53, 94, 75);--color-warning:rgb(96, 89, 59);--color-error:rgb(123, 61, 61);--color-delete:rgb(182, 12, 12);--color-extended-blue:rgb(25, 104, 240);--color-extended-blue-bright:rgb(178, 223, 253);--color-extended-ocean:rgb(0, 211, 228);--color-extended-ocean-bright:rgb(128, 227, 235);--color-extended-green:rgb(53, 222, 144);--color-extended-green-bright:rgb(132, 235, 188);--color-extended-yellow:rgb(251, 207, 35);--color-extended-yellow-bright:rgb(253, 233, 155);--color-extended-orange:rgb(240, 99, 5);--color-extended-orange-bright:rgb(252, 170, 115);--color-extended-red:rgb(230, 15, 15);--color-extended-red-bright:rgb(249, 159, 159);--color-extended-pink:rgb(251, 75, 193);--color-extended-pink-bright:rgb(253, 155, 220);--color-extended-gray:rgb(102, 102, 102);--color-extended-gray-bright:rgb(199, 199, 199);--color-hover:rgba(255, 255, 255, 0.16);--color-pressed:rgba(255, 255, 255, 0.16);--color-shadow-0:var(--color-shadow-0, rgba(0, 0, 0, 0.04));--color-shadow-1:var(--color-shadow-1, rgba(0, 0, 0, 0.16))}:host(.theme--high-contrast){width:100%;height:100%;--color-brand:#0096fa;--color-primary:#1e6bf1;--color-secondary:#292929;--color-surface-1:#ffffff;--color-surface-2:#f5f5f5;--color-surface-3:#e0e0e0;--color-surface-4:#141414;--color-content-default:#292929;--color-content-disable:#666666;--color-content-ghost:#949494;--color-content-bright:#ffffff;--color-content-din:#000000;--color-border-1:#616161;--color-info:#80e3eb;--color-system:#99d5fd;--color-focus:#c226fb;--color-success:#84ebbc;--color-warning:#fde99b;--color-error:#f99f9f;--color-delete:#e60f0f;--color-extended-blue:#1968f0;--color-extended-ocean:#00d3e4;--color-extended-green:#35de90;--color-extended-yellow:#fbcf23;--color-extended-orange:#f06305;--color-extended-red:#e60f0f;--color-extended-pink:#fb4bc1;--color-extended-gray:#666666;--color-hover:rgba(0, 0, 0, 0.08);--color-pressed:rgba(0, 0, 0, 0.16);--color-shadow-1:rgba(0, 0, 0, 0.16);--color-positive:#10603b;--color-negative:#e60f0f}";

const ThemeProvider = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
        /**
         * Set what theme will be aplyed inside the component.
         * 'light', 'dark';
         */
        this.theme = 'light';
    }
    render() {
        return (index.h(index.Host, { key: '5ce2325607a18e007d5bb4c8c0ebfae16b51a6ec', class: { theme: true, [`theme--${this.theme}`]: true } }, index.h("slot", { key: '2272735544deff8edc52803943b2ce9dd8611f2e' })));
    }
};
ThemeProvider.style = themeProviderCss;

const typoCss = ":host{color:var(--color-content-default, rgb(40, 40, 40))}.typo{margin:0;font-family:\"Nunito Sans\", \"Carbona\", \"Tahoma\", \"Helvetica\", \"Arial\", sans-serif;font-style:normal;font-weight:normal;margin:0;-webkit-margin-before:0;margin-block-start:0;-webkit-margin-after:0;margin-block-end:0;-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:0;margin-inline-end:0;padding:0;border:0}.typo--italic{font-style:italic}.typo--no-wrap{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.typo--paragraph{margin-bottom:16px}.typo__variant--fs-10{font-size:0.625rem;line-height:150%}.typo__variant--fs-12{font-size:0.75rem;line-height:150%}.typo__variant--fs-14{font-size:0.875rem;line-height:150%}.typo__variant--fs-16{font-size:1rem;line-height:150%}.typo__variant--fs-20{font-size:1.25rem;line-height:100%}.typo__variant--fs-24{font-size:1.5rem;line-height:100%}.typo__variant--fs-32{font-size:2rem;line-height:100%}.typo__variant--fs-40{font-size:2.5rem;line-height:100%}.typo__margin--fs-20{margin-bottom:22px}.typo__margin--fs-24{margin-bottom:22px}.typo__margin--fs-32{margin-bottom:22px}.typo__margin--fs-40{margin-bottom:20px}.typo__line-height--none{line-height:0%}.typo__line-height--small{line-height:5%}.typo__line-height--simple{line-height:100%}.typo__line-height--plus{line-height:150%}.typo__line-height--double{line-height:200%}.typo__bold--regular{font-weight:400}.typo__bold--semi-bold{font-weight:600}.typo__bold--bold{font-weight:700}.typo__bold--extra-bold{font-weight:800}";

const Typo = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
        /**
         * Variant. Entered as one of the font size variant. Can be one of:
         * 'fs-10' ,'fs-12' ,'fs-14' ,'fs-16' ,'fs-20' ,'fs-24' ,'fs-32' ,'fs-40';
         */
        this.variant = 'fs-16';
        /**
         * Line Height. Entered as one of the line hieght. Can be one of:
         * 'none', 'small', 'simple', 'plus', 'double'
         */
        this.lineHeight = null;
        /**
         * Bold. Entered as one of the bold. Can be one of:
         * 'regular', 'semi-bold', 'bold', 'extra-bold';
         */
        this.bold = null;
        /**
         * Added font style italic
         */
        this.italic = false;
        /**
         * Added style no wrap
         */
        this.noWrap = false;
        /**
         * Tranform text in paragraph
         */
        this.paragraph = false;
        /**
         * If true, adds default margin values
         */
        this.margin = true;
        /**
         * Define element tag, must be used for acessibilty
         */
        this.tag = 'p';
        /**
         * Data test is the prop to specifically test the component action object.
         */
        this.dataTest = null;
    }
    render() {
        const Element = this.tag;
        return (index.h(Element, { key: 'fc26ef24e6f4b1946d971efc3003fb6d549c3e67', class: {
                typo: true,
                [`typo__variant--${this.variant}`]: true,
                [`typo__margin--${this.variant}`]: this.margin,
                'typo--no-wrap': this.noWrap,
                'typo--paragraph': this.paragraph,
                'typo--italic': this.italic,
                [`typo__line-height--${this.lineHeight}`]: !!this.lineHeight,
                [`typo__bold--${this.bold}`]: !!this.bold,
            }, part: "bds-typo__text", "data-test": this.dataTest }, index.h("slot", { key: '4ab6c5e8ab4c9213961fd45d02fc9e1cd002a070' })));
    }
};
Typo.style = typoCss;

exports.bds_grid = Grid;
exports.bds_paper = Paper;
exports.bds_test_component = TestComponent;
exports.bds_theme_provider = ThemeProvider;
exports.bds_typo = Typo;
//# sourceMappingURL=bds-grid.bds-paper.bds-test-component.bds-theme-provider.bds-typo.entry.cjs.js.map

//# sourceMappingURL=bds-grid_5.cjs.entry.js.map