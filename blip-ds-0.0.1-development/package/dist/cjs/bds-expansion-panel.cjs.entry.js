'use strict';

var index = require('./index-D_zq0Z7d.js');

const expansionPanelCss = "*{-webkit-transition:all 0.5s;-moz-transition:all 0.5s;transition:all 0.5s}:host{display:block}";

const ExpansionPanel = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
    }
    render() {
        return (index.h(index.Host, { key: 'cf757f0fd72f5d3a26b92a0bcb5aef0cf61e6165' }, index.h("slot", { key: '7f3ced814e28296cda6ea7d5189da3ebe2d848f4' })));
    }
};
ExpansionPanel.style = expansionPanelCss;

exports.bds_expansion_panel = ExpansionPanel;
//# sourceMappingURL=bds-expansion-panel.entry.cjs.js.map

//# sourceMappingURL=bds-expansion-panel.cjs.entry.js.map