{"version": 3, "file": "index-D_zq0Z7d.js", "sources": ["@stencil/core/internal/app-data", "@stencil/core/internal/app-globals", "node_modules/@stencil/core/internal/client/index.js?app-data=conditional"], "sourcesContent": ["export const NAMESPACE = 'blip-ds';\nexport const BUILD = /* blip-ds */ { allRenderFn: true, appendChildSlotFix: true, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: false, constructableCSS: true, cssAnnotations: true, devTools: false, element: false, event: true, experimentalScopedSlotChanges: false, experimentalSlotFixes: false, formAssociated: false, hasRenderFn: true, hostListener: true, hostListenerTarget: true, hostListenerTargetBody: true, hostListenerTargetDocument: false, hostListenerTargetParent: false, hostListenerTargetWindow: true, hotModuleReplacement: false, hydrateClientSide: false, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, hydratedSelectorName: \"hydrated\", initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: true, mode: false, modernPropertyDecls: false, observeAttribute: true, profile: false, prop: true, propBoolean: true, propMutable: true, propNumber: true, propString: true, reflect: true, scoped: true, scopedSlotTextContentFix: false, scriptDataOpts: true, shadowDelegatesFocus: false, shadowDom: true, slot: true, slotChildNodesFix: true, slotRelocation: true, state: true, style: true, svg: false, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: true, watchCallback: true };\nexport const Env = /* blip-ds */ {};\n", "export const globalScripts = () => {};\nexport const globalStyles = \"\";\n", "/*\n Stencil Client Platform v4.35.1 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/es2022-rewire-class-members.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\nvar PrimitiveType = /* @__PURE__ */ ((PrimitiveType2) => {\n  PrimitiveType2[\"Undefined\"] = \"undefined\";\n  PrimitiveType2[\"Null\"] = \"null\";\n  PrimitiveType2[\"String\"] = \"string\";\n  PrimitiveType2[\"Number\"] = \"number\";\n  PrimitiveType2[\"SpecialNumber\"] = \"number\";\n  PrimitiveType2[\"Boolean\"] = \"boolean\";\n  PrimitiveType2[\"BigInt\"] = \"bigint\";\n  return PrimitiveType2;\n})(PrimitiveType || {});\nvar NonPrimitiveType = /* @__PURE__ */ ((NonPrimitiveType2) => {\n  NonPrimitiveType2[\"Array\"] = \"array\";\n  NonPrimitiveType2[\"Date\"] = \"date\";\n  NonPrimitiveType2[\"Map\"] = \"map\";\n  NonPrimitiveType2[\"Object\"] = \"object\";\n  NonPrimitiveType2[\"RegularExpression\"] = \"regexp\";\n  NonPrimitiveType2[\"Set\"] = \"set\";\n  NonPrimitiveType2[\"Channel\"] = \"channel\";\n  NonPrimitiveType2[\"Symbol\"] = \"symbol\";\n  return NonPrimitiveType2;\n})(NonPrimitiveType || {});\nvar TYPE_CONSTANT = \"type\";\nvar VALUE_CONSTANT = \"value\";\nvar SERIALIZED_PREFIX = \"serialized:\";\n\n// src/utils/es2022-rewire-class-members.ts\nvar reWireGetterSetter = (instance, hostRef) => {\n  var _a;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n  members.map(([memberName, [memberFlags]]) => {\n    if ((BUILD2.state || BUILD2.prop) && (memberFlags & 31 /* Prop */ || memberFlags & 32 /* State */)) {\n      const ogValue = instance[memberName];\n      const ogDescriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(instance), memberName);\n      Object.defineProperty(instance, memberName, {\n        get() {\n          return ogDescriptor.get.call(this);\n        },\n        set(newValue) {\n          ogDescriptor.set.call(this, newValue);\n        },\n        configurable: true,\n        enumerable: true\n      });\n      instance[memberName] = hostRef.$instanceValues$.has(memberName) ? hostRef.$instanceValues$.get(memberName) : ogValue;\n    }\n  });\n};\n\n// src/client/client-host-ref.ts\nvar getHostRef = (ref) => {\n  if (ref.__stencil__getHostRef) {\n    return ref.__stencil__getHostRef();\n  }\n  return void 0;\n};\nvar registerInstance = (lazyInstance, hostRef) => {\n  lazyInstance.__stencil__getHostRef = () => hostRef;\n  hostRef.$lazyInstance$ = lazyInstance;\n  if (BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(lazyInstance, hostRef);\n  }\n};\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  if (BUILD3.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD3.method && BUILD3.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD3.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  const ref = hostRef;\n  hostElement.__stencil__getHostRef = () => ref;\n  if (!BUILD3.lazyLoad && BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(hostElement, hostRef);\n  }\n  return ref;\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD4.isTesting ? [\"STENCIL:\"] : [\n  \"%cstencil\",\n  \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"\n];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = (handler) => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD5.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(\n      `Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`\n    );\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD5.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${BUILD5.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`\n  ).then(\n    (importedModule) => {\n      if (!BUILD5.hotModuleReplacement) {\n        cmpModules.set(bundleId, importedModule);\n      }\n      return importedModule[exportName];\n    },\n    (e) => {\n      consoleError(e, hostRef.$hostElement$);\n    }\n  );\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\nvar setScopedSSR = (_opts) => {\n};\nvar needsScopedSSR = () => false;\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar COMMENT_NODE_ID = \"c\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar STENCIL_DOC_DATA = \"_stencilDocData\";\nvar DEFAULT_DOC_DATA = {\n  hostIds: 0,\n  rootLevelIds: 0,\n  staticComponents: /* @__PURE__ */ new Set()\n};\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\n  \"formAssociatedCallback\",\n  \"formResetCallback\",\n  \"formDisabledCallback\",\n  \"formStateRestoreCallback\"\n];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = (helpers) => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD6.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  var _a;\n  let supportsListenerOptions2 = false;\n  try {\n    (_a = win.document) == null ? void 0 : _a.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD6.constructableCSS ? /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD7.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD7.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD30, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = (path) => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD27 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null && v !== void 0;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = (text) => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/remote-value.ts\nvar RemoteValue = class _RemoteValue {\n  /**\n   * Deserializes a LocalValue serialized object back to its original JavaScript representation\n   *\n   * @param serialized The serialized LocalValue object\n   * @returns The original JavaScript value/object\n   */\n  static fromLocalValue(serialized) {\n    const type = serialized[TYPE_CONSTANT];\n    const value = VALUE_CONSTANT in serialized ? serialized[VALUE_CONSTANT] : void 0;\n    switch (type) {\n      case \"string\" /* String */:\n        return value;\n      case \"boolean\" /* Boolean */:\n        return value;\n      case \"bigint\" /* BigInt */:\n        return BigInt(value);\n      case \"undefined\" /* Undefined */:\n        return void 0;\n      case \"null\" /* Null */:\n        return null;\n      case \"number\" /* Number */:\n        if (value === \"NaN\") return NaN;\n        if (value === \"-0\") return -0;\n        if (value === \"Infinity\") return Infinity;\n        if (value === \"-Infinity\") return -Infinity;\n        return value;\n      case \"array\" /* Array */:\n        return value.map((item) => _RemoteValue.fromLocalValue(item));\n      case \"date\" /* Date */:\n        return new Date(value);\n      case \"map\" /* Map */:\n        const map2 = /* @__PURE__ */ new Map();\n        for (const [key, val] of value) {\n          const deserializedKey = typeof key === \"object\" && key !== null ? _RemoteValue.fromLocalValue(key) : key;\n          const deserializedValue = _RemoteValue.fromLocalValue(val);\n          map2.set(deserializedKey, deserializedValue);\n        }\n        return map2;\n      case \"object\" /* Object */:\n        const obj = {};\n        for (const [key, val] of value) {\n          obj[key] = _RemoteValue.fromLocalValue(val);\n        }\n        return obj;\n      case \"regexp\" /* RegularExpression */:\n        const { pattern, flags } = value;\n        return new RegExp(pattern, flags);\n      case \"set\" /* Set */:\n        const set = /* @__PURE__ */ new Set();\n        for (const item of value) {\n          set.add(_RemoteValue.fromLocalValue(item));\n        }\n        return set;\n      case \"symbol\" /* Symbol */:\n        return Symbol(value);\n      default:\n        throw new Error(`Unsupported type: ${type}`);\n    }\n  }\n  /**\n   * Utility method to deserialize multiple LocalValues at once\n   *\n   * @param serializedValues Array of serialized LocalValue objects\n   * @returns Array of deserialized JavaScript values\n   */\n  static fromLocalValueArray(serializedValues) {\n    return serializedValues.map((value) => _RemoteValue.fromLocalValue(value));\n  }\n  /**\n   * Verifies if the given object matches the structure of a serialized LocalValue\n   *\n   * @param obj Object to verify\n   * @returns boolean indicating if the object has LocalValue structure\n   */\n  static isLocalValueObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n      return false;\n    }\n    if (!obj.hasOwnProperty(TYPE_CONSTANT)) {\n      return false;\n    }\n    const type = obj[TYPE_CONSTANT];\n    const hasTypeProperty = Object.values({ ...PrimitiveType, ...NonPrimitiveType }).includes(type);\n    if (!hasTypeProperty) {\n      return false;\n    }\n    if (type !== \"null\" /* Null */ && type !== \"undefined\" /* Undefined */) {\n      return obj.hasOwnProperty(VALUE_CONSTANT);\n    }\n    return true;\n  }\n};\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/utils/serialize.ts\nfunction deserializeProperty(value) {\n  if (typeof value !== \"string\" || !value.startsWith(SERIALIZED_PREFIX)) {\n    return value;\n  }\n  return RemoteValue.fromLocalValue(JSON.parse(atob(value.slice(SERIALIZED_PREFIX.length))));\n}\n\n// src/utils/shadow-root.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nimport { globalStyles } from \"@stencil/core/internal/app-globals\";\nfunction createShadowRoot(cmpMeta) {\n  const shadowRoot = BUILD8.shadowDelegatesFocus ? this.attachShadow({\n    mode: \"open\",\n    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n  }) : this.attachShadow({ mode: \"open\" });\n  if (supportsConstructableStylesheets) {\n    const sheet = new CSSStyleSheet();\n    sheet.replaceSync(globalStyles);\n    shadowRoot.adoptedStyleSheets.push(sheet);\n  }\n}\n\n// src/utils/util.ts\nvar lowerPathParam = (fn) => (p) => fn(p.toLowerCase());\nvar isDtsFile = lowerPathParam((p) => p.endsWith(\".d.ts\") || p.endsWith(\".d.mts\") || p.endsWith(\".d.cts\"));\nvar isTsFile = lowerPathParam(\n  (p) => !isDtsFile(p) && (p.endsWith(\".ts\") || p.endsWith(\".mts\") || p.endsWith(\".cts\"))\n);\nvar isTsxFile = lowerPathParam(\n  (p) => p.endsWith(\".tsx\") || p.endsWith(\".mtsx\") || p.endsWith(\".ctsx\")\n);\nvar isJsxFile = lowerPathParam(\n  (p) => p.endsWith(\".jsx\") || p.endsWith(\".mjsx\") || p.endsWith(\".cjsx\")\n);\nvar isJsFile = lowerPathParam((p) => p.endsWith(\".js\") || p.endsWith(\".mjs\") || p.endsWith(\".cjs\"));\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/slot-polyfill-utils.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = internalCall(elm, \"childNodes\");\n  if (elm.tagName && elm.tagName.includes(\"-\") && elm[\"s-cr\"] && elm.tagName !== \"SLOT-FB\") {\n    getHostSlotNodes(childNodes, elm.tagName).forEach((slotNode) => {\n      if (slotNode.nodeType === 1 /* ElementNode */ && slotNode.tagName === \"SLOT-FB\") {\n        if (getSlotChildSiblings(slotNode, getSlotName(slotNode), false).length) {\n          slotNode.hidden = true;\n        } else {\n          slotNode.hidden = false;\n        }\n      }\n    });\n  }\n  let i2 = 0;\n  for (i2 = 0; i2 < childNodes.length; i2++) {\n    const childNode = childNodes[i2];\n    if (childNode.nodeType === 1 /* ElementNode */ && internalCall(childNode, \"childNodes\").length) {\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar getSlottedChildNodes = (childNodes) => {\n  const result = [];\n  for (let i2 = 0; i2 < childNodes.length; i2++) {\n    const slottedNode = childNodes[i2][\"s-nr\"] || void 0;\n    if (slottedNode && slottedNode.isConnected) {\n      result.push(slottedNode);\n    }\n  }\n  return result;\n};\nfunction getHostSlotNodes(childNodes, hostName, slotName) {\n  let i2 = 0;\n  let slottedNodes = [];\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && (!hostName || childNode[\"s-hn\"] === hostName) && (slotName === void 0 || getSlotName(childNode) === slotName)) {\n      slottedNodes.push(childNode);\n      if (typeof slotName !== \"undefined\") return slottedNodes;\n    }\n    slottedNodes = [...slottedNodes, ...getHostSlotNodes(childNode.childNodes, hostName, slotName)];\n  }\n  return slottedNodes;\n}\nvar getSlotChildSiblings = (slot, slotName, includeSlot = true) => {\n  const childNodes = [];\n  if (includeSlot && slot[\"s-sr\"] || !slot[\"s-sr\"]) childNodes.push(slot);\n  let node = slot;\n  while (node = node.nextSibling) {\n    if (getSlotName(node) === slotName && (includeSlot || !node[\"s-sr\"])) childNodes.push(node);\n  }\n  return childNodes;\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar addSlotRelocateNode = (newChild, slotNode, prepend, position) => {\n  if (newChild[\"s-ol\"] && newChild[\"s-ol\"].isConnected) {\n    return;\n  }\n  const slottedNodeLocation = document.createTextNode(\"\");\n  slottedNodeLocation[\"s-nr\"] = newChild;\n  if (!slotNode[\"s-cr\"] || !slotNode[\"s-cr\"].parentNode) return;\n  const parent = slotNode[\"s-cr\"].parentNode;\n  const appendMethod = prepend ? internalCall(parent, \"prepend\") : internalCall(parent, \"appendChild\");\n  if (BUILD9.hydrateClientSide && typeof position !== \"undefined\") {\n    slottedNodeLocation[\"s-oo\"] = position;\n    const childNodes = internalCall(parent, \"childNodes\");\n    const slotRelocateNodes = [slottedNodeLocation];\n    childNodes.forEach((n) => {\n      if (n[\"s-nr\"]) slotRelocateNodes.push(n);\n    });\n    slotRelocateNodes.sort((a, b) => {\n      if (!a[\"s-oo\"] || a[\"s-oo\"] < (b[\"s-oo\"] || 0)) return -1;\n      else if (!b[\"s-oo\"] || b[\"s-oo\"] < a[\"s-oo\"]) return 1;\n      return 0;\n    });\n    slotRelocateNodes.forEach((n) => appendMethod.call(parent, n));\n  } else {\n    appendMethod.call(parent, slottedNodeLocation);\n  }\n  newChild[\"s-ol\"] = slottedNodeLocation;\n  newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n};\nvar getSlotName = (node) => typeof node[\"s-sn\"] === \"string\" ? node[\"s-sn\"] : node.nodeType === 1 && node.getAttribute(\"slot\") || void 0;\nfunction patchSlotNode(node) {\n  if (node.assignedElements || node.assignedNodes || !node[\"s-sr\"]) return;\n  const assignedFactory = (elementsOnly) => (function(opts) {\n    const toReturn = [];\n    const slotName = this[\"s-sn\"];\n    if (opts == null ? void 0 : opts.flatten) {\n      console.error(`\n          Flattening is not supported for Stencil non-shadow slots.\n          You can use \\`.childNodes\\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `);\n    }\n    const parent = this[\"s-cr\"].parentElement;\n    const slottedNodes = parent.__childNodes ? parent.childNodes : getSlottedChildNodes(parent.childNodes);\n    slottedNodes.forEach((n) => {\n      if (slotName === getSlotName(n)) {\n        toReturn.push(n);\n      }\n    });\n    if (elementsOnly) {\n      return toReturn.filter((n) => n.nodeType === 1 /* ElementNode */);\n    }\n    return toReturn;\n  }).bind(node);\n  node.assignedElements = assignedFactory(true);\n  node.assignedNodes = assignedFactory(false);\n}\nfunction dispatchSlotChangeEvent(elm) {\n  elm.dispatchEvent(new CustomEvent(\"slotchange\", { bubbles: false, cancelable: false, composed: false }));\n}\nfunction findSlotFromSlottedNode(slottedNode, parentHost) {\n  var _a;\n  parentHost = parentHost || ((_a = slottedNode[\"s-ol\"]) == null ? void 0 : _a.parentElement);\n  if (!parentHost) return { slotNode: null, slotName: \"\" };\n  const slotName = slottedNode[\"s-sn\"] = getSlotName(slottedNode) || \"\";\n  const childNodes = internalCall(parentHost, \"childNodes\");\n  const slotNode = getHostSlotNodes(childNodes, parentHost.tagName, slotName)[0];\n  return { slotNode, slotName };\n}\n\n// src/runtime/dom-extras.ts\nvar patchPseudoShadowDom = (hostElementPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchInsertBefore(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD10.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD10.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      const childNodes = this.__childNodes || this.childNodes;\n      for (; i2 < childNodes.length; i2++) {\n        slotted = childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD10.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    if (slotNode) {\n      addSlotRelocateNode(newChild, slotNode);\n      const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const parent = internalCall(appendAfter, \"parentNode\");\n      const insertedNode = internalCall(parent, \"insertBefore\")(newChild, appendAfter.nextSibling);\n      dispatchSlotChangeEvent(slotNode);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const childNodes = this.__childNodes || this.childNodes;\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, toRemove[\"s-sn\"]);\n      if (slotNode && toRemove.isConnected) {\n        toRemove.remove();\n        updateFallbackSlotVisibility(this);\n        return;\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  HostElementPrototype.__prepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = (newChild[\"s-sn\"] = getSlotName(newChild)) || \"\";\n      const childNodes = internalCall(this, \"childNodes\");\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, slotName)[0];\n      if (slotNode) {\n        addSlotRelocateNode(newChild, slotNode, true);\n        const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        const parent = internalCall(appendAfter, \"parentNode\");\n        const toReturn = internalCall(parent, \"insertBefore\")(newChild, internalCall(appendAfter, \"nextSibling\"));\n        dispatchSlotChangeEvent(slotNode);\n        return toReturn;\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return HostElementPrototype.__prepend(newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.__append = HostElementPrototype.append;\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchInsertBefore = (HostElementPrototype) => {\n  const eleProto = HostElementPrototype;\n  if (eleProto.__insertBefore) return;\n  eleProto.__insertBefore = HostElementPrototype.insertBefore;\n  HostElementPrototype.insertBefore = function(newChild, currentChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    const slottedNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n    if (slotNode) {\n      let found = false;\n      slottedNodes.forEach((childNode) => {\n        if (childNode === currentChild || currentChild === null) {\n          found = true;\n          if (currentChild === null || slotName !== currentChild[\"s-sn\"]) {\n            this.appendChild(newChild);\n            return;\n          }\n          if (slotName === currentChild[\"s-sn\"]) {\n            addSlotRelocateNode(newChild, slotNode);\n            const parent = internalCall(currentChild, \"parentNode\");\n            internalCall(parent, \"insertBefore\")(newChild, currentChild);\n            dispatchSlotChangeEvent(slotNode);\n          }\n          return;\n        }\n      });\n      if (found) return newChild;\n    }\n    const parentNode = currentChild == null ? void 0 : currentChild.__parentNode;\n    if (parentNode && !this.isSameNode(parentNode)) {\n      return this.appendChild(newChild);\n    }\n    return this.__insertBefore(newChild, currentChild);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  patchHostOriginalAccessor(\"textContent\", hostElementPrototype);\n  Object.defineProperty(hostElementPrototype, \"textContent\", {\n    get: function() {\n      let text = \"\";\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => text += node.textContent || \"\");\n      return text;\n    },\n    set: function(value) {\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => {\n        if (node[\"s-ol\"]) node[\"s-ol\"].remove();\n        node.remove();\n      });\n      this.insertAdjacentHTML(\"beforeend\", value);\n    }\n  });\n};\nvar patchChildSlotNodes = (elm) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  patchHostOriginalAccessor(\"children\", elm);\n  Object.defineProperty(elm, \"children\", {\n    get() {\n      return this.childNodes.filter((n) => n.nodeType === 1);\n    }\n  });\n  Object.defineProperty(elm, \"childElementCount\", {\n    get() {\n      return this.children.length;\n    }\n  });\n  patchHostOriginalAccessor(\"firstChild\", elm);\n  Object.defineProperty(elm, \"firstChild\", {\n    get() {\n      return this.childNodes[0];\n    }\n  });\n  patchHostOriginalAccessor(\"lastChild\", elm);\n  Object.defineProperty(elm, \"lastChild\", {\n    get() {\n      return this.childNodes[this.childNodes.length - 1];\n    }\n  });\n  patchHostOriginalAccessor(\"childNodes\", elm);\n  Object.defineProperty(elm, \"childNodes\", {\n    get() {\n      const result = new FakeNodeList();\n      result.push(...getSlottedChildNodes(this.__childNodes));\n      return result;\n    }\n  });\n};\nvar patchSlottedNode = (node) => {\n  if (!node || node.__nextSibling !== void 0 || !globalThis.Node) return;\n  patchNextSibling(node);\n  patchPreviousSibling(node);\n  patchParentNode(node);\n  if (node.nodeType === Node.ELEMENT_NODE) {\n    patchNextElementSibling(node);\n    patchPreviousElementSibling(node);\n  }\n};\nvar patchNextSibling = (node) => {\n  if (!node || node.__nextSibling) return;\n  patchHostOriginalAccessor(\"nextSibling\", node);\n  Object.defineProperty(node, \"nextSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index + 1];\n      }\n      return this.__nextSibling;\n    }\n  });\n};\nvar patchNextElementSibling = (element) => {\n  if (!element || element.__nextElementSibling) return;\n  patchHostOriginalAccessor(\"nextElementSibling\", element);\n  Object.defineProperty(element, \"nextElementSibling\", {\n    get: function() {\n      var _a;\n      const parentEles = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentEles == null ? void 0 : parentEles.indexOf(this);\n      if (parentEles && index > -1) {\n        return parentEles[index + 1];\n      }\n      return this.__nextElementSibling;\n    }\n  });\n};\nvar patchPreviousSibling = (node) => {\n  if (!node || node.__previousSibling) return;\n  patchHostOriginalAccessor(\"previousSibling\", node);\n  Object.defineProperty(node, \"previousSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousSibling;\n    }\n  });\n};\nvar patchPreviousElementSibling = (element) => {\n  if (!element || element.__previousElementSibling) return;\n  patchHostOriginalAccessor(\"previousElementSibling\", element);\n  Object.defineProperty(element, \"previousElementSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousElementSibling;\n    }\n  });\n};\nvar patchParentNode = (node) => {\n  if (!node || node.__parentNode) return;\n  patchHostOriginalAccessor(\"parentNode\", node);\n  Object.defineProperty(node, \"parentNode\", {\n    get: function() {\n      var _a;\n      return ((_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode) || this.__parentNode;\n    },\n    set: function(value) {\n      this.__parentNode = value;\n    }\n  });\n};\nvar validElementPatches = [\"children\", \"nextElementSibling\", \"previousElementSibling\"];\nvar validNodesPatches = [\n  \"childNodes\",\n  \"firstChild\",\n  \"lastChild\",\n  \"nextSibling\",\n  \"previousSibling\",\n  \"textContent\",\n  \"parentNode\"\n];\nfunction patchHostOriginalAccessor(accessorName, node) {\n  let accessor;\n  if (validElementPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Element.prototype, accessorName);\n  } else if (validNodesPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Node.prototype, accessorName);\n  }\n  if (!accessor) {\n    accessor = Object.getOwnPropertyDescriptor(node, accessorName);\n  }\n  if (accessor) Object.defineProperty(node, \"__\" + accessorName, accessor);\n}\nfunction internalCall(node, method) {\n  if (\"__\" + method in node) {\n    const toReturn = node[\"__\" + method];\n    if (typeof toReturn !== \"function\") return toReturn;\n    return toReturn.bind(node);\n  } else {\n    if (typeof node[method] !== \"function\") return node[method];\n    return node[method].bind(node);\n  }\n}\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD11.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD11.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = (ref) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD11.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = (ref) => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD12.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD12.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD12.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD12.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD12.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD12.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD12.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD12.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = (inputElm) => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  var _a;\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const slottedNodes = [];\n  const shadowRootNodes = BUILD13.shadowDom && shadowRoot ? [] : null;\n  const vnode = newVNode(tagName, null);\n  vnode.$elm$ = hostElm;\n  const members = Object.entries(((_a = hostRef.$cmpMeta$) == null ? void 0 : _a.$members$) || {});\n  members.forEach(([memberName, [memberFlags, metaAttributeName]]) => {\n    var _a2, _b;\n    if (!(memberFlags & 31 /* Prop */)) {\n      return;\n    }\n    const attributeName = metaAttributeName || memberName;\n    const attrVal = hostElm.getAttribute(attributeName);\n    if (attrVal !== null) {\n      const attrPropVal = parsePropertyValue(\n        attrVal,\n        memberFlags,\n        BUILD13.formAssociated && !!(((_a2 = hostRef.$cmpMeta$) == null ? void 0 : _a2.$flags$) & 64 /* formAssociated */)\n      );\n      (_b = hostRef == null ? void 0 : hostRef.$instanceValues$) == null ? void 0 : _b.set(memberName, attrPropVal);\n    }\n  });\n  let scopeId2;\n  if (BUILD13.scoped) {\n    const cmpMeta = hostRef.$cmpMeta$;\n    if (cmpMeta && cmpMeta.$flags$ & 10 /* needsScopedEncapsulation */ && hostElm[\"s-sc\"]) {\n      scopeId2 = hostElm[\"s-sc\"];\n      hostElm.classList.add(scopeId2 + \"-h\");\n    } else if (hostElm[\"s-sc\"]) {\n      delete hostElm[\"s-sc\"];\n    }\n  }\n  if (win.document && (!plt.$orgLocNodes$ || !plt.$orgLocNodes$.size)) {\n    initializeDocumentHydrate(win.document.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  hostRef.$vnode$ = clientHydrate(\n    vnode,\n    childRenderNodes,\n    slotNodes,\n    shadowRootNodes,\n    hostElm,\n    hostElm,\n    hostId,\n    slottedNodes\n  );\n  let crIndex = 0;\n  const crLength = childRenderNodes.length;\n  let childRenderNode;\n  for (crIndex; crIndex < crLength; crIndex++) {\n    childRenderNode = childRenderNodes[crIndex];\n    const orgLocationId = childRenderNode.$hostId$ + \".\" + childRenderNode.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = childRenderNode.$elm$;\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName.toUpperCase();\n      if (childRenderNode.$tag$ === \"slot\") {\n        node[\"s-cr\"] = hostElm[\"s-cr\"];\n      }\n    }\n    if (childRenderNode.$tag$ === \"slot\") {\n      childRenderNode.$name$ = childRenderNode.$elm$[\"s-sn\"] || childRenderNode.$elm$[\"name\"] || null;\n      if (childRenderNode.$children$) {\n        childRenderNode.$flags$ |= 2 /* isSlotFallback */;\n        if (!childRenderNode.$elm$.childNodes.length) {\n          childRenderNode.$children$.forEach((c) => {\n            childRenderNode.$elm$.appendChild(c.$elm$);\n          });\n        }\n      } else {\n        childRenderNode.$flags$ |= 1 /* isSlotReference */;\n      }\n    }\n    if (orgLocationNode && orgLocationNode.isConnected) {\n      if (shadowRoot && orgLocationNode[\"s-en\"] === \"\") {\n        orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n      }\n      orgLocationNode.parentNode.removeChild(orgLocationNode);\n      if (!shadowRoot) {\n        node[\"s-oo\"] = parseInt(childRenderNode.$nodeId$);\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  }\n  const hosts = [];\n  const snLen = slottedNodes.length;\n  let snIndex = 0;\n  let slotGroup;\n  let snGroupIdx;\n  let snGroupLen;\n  let slottedItem;\n  for (snIndex; snIndex < snLen; snIndex++) {\n    slotGroup = slottedNodes[snIndex];\n    if (!slotGroup || !slotGroup.length) continue;\n    snGroupLen = slotGroup.length;\n    snGroupIdx = 0;\n    for (snGroupIdx; snGroupIdx < snGroupLen; snGroupIdx++) {\n      slottedItem = slotGroup[snGroupIdx];\n      if (!hosts[slottedItem.hostId]) {\n        hosts[slottedItem.hostId] = plt.$orgLocNodes$.get(slottedItem.hostId);\n      }\n      if (!hosts[slottedItem.hostId]) continue;\n      const hostEle = hosts[slottedItem.hostId];\n      if (!hostEle.shadowRoot || !shadowRoot) {\n        slottedItem.slot[\"s-cr\"] = hostEle[\"s-cr\"];\n        if (!slottedItem.slot[\"s-cr\"] && hostEle.shadowRoot) {\n          slottedItem.slot[\"s-cr\"] = hostEle;\n        } else {\n          slottedItem.slot[\"s-cr\"] = (hostEle.__childNodes || hostEle.childNodes)[0];\n        }\n        addSlotRelocateNode(slottedItem.node, slottedItem.slot, false, slottedItem.node[\"s-oo\"]);\n        if (BUILD13.experimentalSlotFixes) {\n          patchSlottedNode(slottedItem.node);\n        }\n      }\n      if (hostEle.shadowRoot && slottedItem.node.parentElement !== hostEle) {\n        hostEle.appendChild(slottedItem.node);\n      }\n    }\n  }\n  if (BUILD13.scoped && scopeId2 && slotNodes.length) {\n    slotNodes.forEach((slot) => {\n      slot.$elm$.parentElement.classList.add(scopeId2 + \"-s\");\n    });\n  }\n  if (BUILD13.shadowDom && shadowRoot && !shadowRoot.childNodes.length) {\n    let rnIdex = 0;\n    const rnLen = shadowRootNodes.length;\n    if (rnLen) {\n      for (rnIdex; rnIdex < rnLen; rnIdex++) {\n        shadowRoot.appendChild(shadowRootNodes[rnIdex]);\n      }\n      Array.from(hostElm.childNodes).forEach((node) => {\n        if (typeof node[\"s-en\"] !== \"string\" && typeof node[\"s-sn\"] !== \"string\") {\n          if (node.nodeType === 1 /* ElementNode */ && node.slot && node.hidden) {\n            node.removeAttribute(\"hidden\");\n          } else if (node.nodeType === 8 /* CommentNode */ || node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n            node.parentNode.removeChild(node);\n          }\n        }\n      });\n    }\n  }\n  plt.$orgLocNodes$.delete(hostElm[\"s-id\"]);\n  hostRef.$hostElement$ = hostElm;\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId, slottedNodes = []) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  const scopeId2 = hostElm[\"s-sc\"];\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = createSimpleVNode({\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          // If we don't add the initial classes to the VNode, the first `vdom-render.ts` patch\n          // won't try to reconcile them. Classes set on the node will be blown away.\n          $attrs$: { class: node.className || \"\" }\n        });\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        if (BUILD13.scoped && scopeId2) {\n          node[\"s-si\"] = scopeId2;\n          childVNode.$attrs$.class += \" \" + scopeId2;\n        }\n        const slotName = childVNode.$elm$.getAttribute(\"s-sn\");\n        if (typeof slotName === \"string\") {\n          if (childVNode.$tag$ === \"slot-fb\") {\n            addSlot(\n              slotName,\n              childIdSplt[2],\n              childVNode,\n              node,\n              parentVNode,\n              childRenderNodes,\n              slotNodes,\n              shadowRootNodes,\n              slottedNodes\n            );\n            if (BUILD13.scoped && scopeId2) {\n              node.classList.add(scopeId2);\n            }\n          }\n          childVNode.$elm$[\"s-sn\"] = slotName;\n          childVNode.$elm$.removeAttribute(\"s-sn\");\n        }\n        if (childVNode.$index$ !== void 0) {\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        }\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId,\n          slottedNodes\n        );\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = nonShadowNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        nonShadowNodes[i2],\n        hostId,\n        slottedNodes\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = createSimpleVNode({\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4] || \"0\",\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      });\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 3 /* TextNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (hostId === childVNode.$hostId$) {\n            if (!parentVNode.$children$) {\n              parentVNode.$children$ = [];\n            }\n            parentVNode.$children$[childVNode.$index$] = childVNode;\n          }\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childNodeType === COMMENT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 8 /* CommentNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 8 /* CommentNode */) {\n          childRenderNodes.push(childVNode);\n          node.remove();\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          const slotName = node[\"s-sn\"] = childIdSplt[5] || \"\";\n          addSlot(\n            slotName,\n            childIdSplt[2],\n            childVNode,\n            node,\n            parentVNode,\n            childRenderNodes,\n            slotNodes,\n            shadowRootNodes,\n            slottedNodes\n          );\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD13.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD13.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  } else {\n    if (node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n      node.remove();\n    }\n  }\n  return parentVNode;\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    const componentId = node[HYDRATE_ID] || node.getAttribute(HYDRATE_ID);\n    if (componentId) {\n      orgLocNodes.set(componentId, node);\n    }\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = 0; i2 < nonShadowNodes.length; i2++) {\n      initializeDocumentHydrate(nonShadowNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\nvar createSimpleVNode = (vnode) => {\n  const defaultVNode = {\n    $flags$: 0,\n    $hostId$: null,\n    $nodeId$: null,\n    $depth$: null,\n    $index$: \"0\",\n    $elm$: null,\n    $attrs$: null,\n    $children$: null,\n    $key$: null,\n    $name$: null,\n    $tag$: null,\n    $text$: null\n  };\n  return { ...defaultVNode, ...vnode };\n};\nfunction addSlot(slotName, slotId, childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes) {\n  node[\"s-sr\"] = true;\n  childVNode.$name$ = slotName || null;\n  childVNode.$tag$ = \"slot\";\n  const parentNodeId = (parentVNode == null ? void 0 : parentVNode.$elm$) ? parentVNode.$elm$[\"s-id\"] || parentVNode.$elm$.getAttribute(\"s-id\") : \"\";\n  if (BUILD13.shadowDom && shadowRootNodes && win.document) {\n    const slot = childVNode.$elm$ = win.document.createElement(childVNode.$tag$);\n    if (childVNode.$name$) {\n      childVNode.$elm$.setAttribute(\"name\", slotName);\n    }\n    if (parentNodeId && parentNodeId !== childVNode.$hostId$) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    } else {\n      node.parentNode.insertBefore(slot, node);\n    }\n    addSlottedNodes(slottedNodes, slotId, slotName, node, childVNode.$hostId$);\n    node.remove();\n    if (childVNode.$depth$ === \"0\") {\n      shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n    }\n  } else {\n    const slot = childVNode.$elm$;\n    const shouldMove = parentNodeId && parentNodeId !== childVNode.$hostId$ && parentVNode.$elm$.shadowRoot;\n    addSlottedNodes(slottedNodes, slotId, slotName, node, shouldMove ? parentNodeId : childVNode.$hostId$);\n    patchSlotNode(node);\n    if (shouldMove) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    }\n  }\n  childRenderNodes.push(childVNode);\n  slotNodes.push(childVNode);\n  if (!parentVNode.$children$) {\n    parentVNode.$children$ = [];\n  }\n  parentVNode.$children$[childVNode.$index$] = childVNode;\n}\nvar addSlottedNodes = (slottedNodes, slotNodeId, slotName, slotNode, hostId) => {\n  let slottedNode = slotNode.nextSibling;\n  slottedNodes[slotNodeId] = slottedNodes[slotNodeId] || [];\n  while (slottedNode && ((slottedNode[\"getAttribute\"] && slottedNode.getAttribute(\"slot\") || slottedNode[\"s-sn\"]) === slotName || slotName === \"\" && !slottedNode[\"s-sn\"] && (slottedNode.nodeType === 8 /* CommentNode */ && slottedNode.nodeValue.indexOf(\".\") !== 1 || slottedNode.nodeType === 3 /* TextNode */))) {\n    slottedNode[\"s-sn\"] = slotName;\n    slottedNodes[slotNodeId].push({ slot: slotNode, node: slottedNode, hostId });\n    slottedNode = slottedNode.nextSibling;\n  }\n};\nvar findCorrespondingNode = (node, type) => {\n  let sibling = node;\n  do {\n    sibling = sibling.nextSibling;\n  } while (sibling && (sibling.nodeType !== type || !sibling.nodeValue));\n  return sibling;\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = (selector) => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[\\s*part~=\\s*(\"[^\"]*\"|'[^']*')\\s*\\])/g, (_, keep) => {\n    const replaceBy = `__part-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  content = content.replace(/__part-(\\d+)__/g, (_, index) => placeholders[+index]);\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _safePartRe = /__part-(\\d+)__/g;\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = (selector) => {\n  const safeSelector2 = escapeRegExpSpecialCharacters(selector);\n  return new RegExp(\n    // First capture group: match any context before the selector that's not inside @supports selector()\n    // Using negative lookahead to avoid matching inside @supports selector(...) condition\n    `(^|[^@]|@(?!supports\\\\s+selector\\\\s*\\\\([^{]*?${safeSelector2}))(${safeSelector2}\\\\b)`,\n    \"g\"\n  );\n};\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = (input) => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = (input) => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = (input) => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = (cssText) => {\n  const supportsBlocks = [];\n  cssText = cssText.replace(/@supports\\s+selector\\s*\\(\\s*([^)]*)\\s*\\)/g, (_, selectorContent) => {\n    const placeholder = `__supports_${supportsBlocks.length}__`;\n    supportsBlocks.push(selectorContent);\n    return `@supports selector(${placeholder})`;\n  });\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  supportsBlocks.forEach((originalSelector, index) => {\n    cssText = cssText.replace(`__supports_${index}__`, originalSelector);\n  });\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i2 = 0; i2 < parts.length; i2++) {\n        const p = parts[i2].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i2 = m[4] - 1; i2 >= 0; i2--) {\n        const char = m[5][i2];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = (cssText) => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = (scopeSelector2) => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = (p) => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))(?=(?:[^()]*\\([^()]*\\))*[^()]*$)\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = !part.match(_safePartRe) && (shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1);\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map((shallowPart) => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector) => {\n  return processRules(cssText, (rule) => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId2) {\n    cssText = scopeSelectors(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map((ref) => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar expandPartSelectors = (cssText) => {\n  const partSelectorRe = /([^\\s,{][^,{]*?)::part\\(\\s*([^)]+?)\\s*\\)((?:[:.][^,{]*)*)/g;\n  return processRules(cssText, (rule) => {\n    if (rule.selector[0] === \"@\") {\n      return rule;\n    }\n    const selectors = rule.selector.split(\",\").map((sel) => {\n      const out = [sel.trim()];\n      let m;\n      while ((m = partSelectorRe.exec(sel)) !== null) {\n        const before = m[1].trimEnd();\n        const partNames = m[2].trim().split(/\\s+/);\n        const after = m[3] || \"\";\n        const partAttr = partNames.flatMap((p) => {\n          if (!rule.selector.includes(`[part~=\"${p}\"]`)) {\n            return [`[part~=\"${p}\"]`];\n          }\n          return [];\n        }).join(\"\");\n        const expanded = `${before} ${partAttr}${after}`;\n        if (!!partAttr && expanded !== sel.trim()) {\n          out.push(expanded);\n        }\n      }\n      return out.join(\", \");\n    });\n    rule.selector = selectors.join(\", \");\n    return rule;\n  });\n};\nvar scopeCss = (cssText, scopeId2, commentOriginalSelector) => {\n  const hostScopeId = scopeId2 + \"-h\";\n  const slotScopeId = scopeId2 + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const orgSelectors = [];\n  if (commentOriginalSelector) {\n    const processCommentedSelector = (rule) => {\n      const placeholder = `/*!@___${orgSelectors.length}___*/`;\n      const comment = `/*!@${rule.selector}*/`;\n      orgSelectors.push({ placeholder, comment });\n      rule.selector = placeholder + rule.selector;\n      return rule;\n    };\n    cssText = processRules(cssText, (rule) => {\n      if (rule.selector[0] !== \"@\") {\n        return processCommentedSelector(rule);\n      } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n        rule.content = processRules(rule.content, processCommentedSelector);\n        return rule;\n      }\n      return rule;\n    });\n  }\n  const scoped = scopeCssText(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  if (commentOriginalSelector) {\n    orgSelectors.forEach(({ placeholder, comment }) => {\n      cssText = cssText.replace(placeholder, comment);\n    });\n  }\n  scoped.slottedSelectors.forEach((slottedSelector) => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  cssText = expandPartSelectors(cssText);\n  return cssText;\n};\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType, isFormAssociated) => {\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && (propValue.startsWith(\"{\") && propValue.endsWith(\"}\") || propValue.startsWith(\"[\") && propValue.endsWith(\"]\"))) {\n    try {\n      propValue = JSON.parse(propValue);\n      return propValue;\n    } catch (e) {\n    }\n  }\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && propValue.startsWith(SERIALIZED_PREFIX)) {\n    propValue = deserializeProperty(propValue);\n    return propValue;\n  }\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD14.propBoolean && propType & 4 /* Boolean */) {\n      if (BUILD14.formAssociated && isFormAssociated && typeof propValue === \"string\") {\n        return propValue === \"\" || !!propValue;\n      } else {\n        return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n      }\n    }\n    if (BUILD14.propNumber && propType & 2 /* Number */) {\n      return typeof propValue === \"string\" ? parseFloat(propValue) : typeof propValue === \"number\" ? propValue : NaN;\n    }\n    if (BUILD14.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD21, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\nvar getElement = (ref) => BUILD15.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      if (BUILD16.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD17 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD17.attachStyles || !win.document) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : win.document;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD17.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = win.document.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD17.hydrateServerSide || BUILD17.hotModuleReplacement) && (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */ || cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */)) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(\n                styleElm,\n                (referenceNode2 == null ? void 0 : referenceNode2.parentNode) === styleContainerNode ? referenceNode2 : null\n              );\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD17.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    BUILD17.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if ((BUILD17.shadowDom || BUILD17.scoped) && BUILD17.cssAnnotations && flags & 10 /* needsScopedEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD17.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar convertScopedToShadow = (css) => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, \"$1{\");\nvar hydrateScopedToShadow = () => {\n  if (!win.document) {\n    return;\n  }\n  const styles2 = win.document.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let i2 = 0;\n  for (; i2 < styles2.length; i2++) {\n    registerStyle(styles2[i2].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles2[i2].innerHTML), true);\n  }\n};\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags, initialRender) => {\n  if (oldValue === newValue) {\n    return;\n  }\n  let isProp = isMemberInElement(elm, memberName);\n  let ln = memberName.toLowerCase();\n  if (BUILD18.vdomClass && memberName === \"class\") {\n    const classList = elm.classList;\n    const oldClasses = parseClassList(oldValue);\n    let newClasses = parseClassList(newValue);\n    if (BUILD18.hydrateClientSide && elm[\"s-si\"] && initialRender) {\n      newClasses.push(elm[\"s-si\"]);\n      oldClasses.forEach((c) => {\n        if (c.startsWith(elm[\"s-si\"])) newClasses.push(c);\n      });\n      newClasses = [...new Set(newClasses)];\n      classList.add(...newClasses);\n    } else {\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    }\n  } else if (BUILD18.vdomStyle && memberName === \"style\") {\n    if (BUILD18.updatable) {\n      for (const prop in oldValue) {\n        if (!newValue || newValue[prop] == null) {\n          if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.removeProperty(prop);\n          } else {\n            elm.style[prop] = \"\";\n          }\n        }\n      }\n    }\n    for (const prop in newValue) {\n      if (!oldValue || newValue[prop] !== oldValue[prop]) {\n        if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n          elm.style.setProperty(prop, newValue[prop]);\n        } else {\n          elm.style[prop] = newValue[prop];\n        }\n      }\n    }\n  } else if (BUILD18.vdomKey && memberName === \"key\") {\n  } else if (BUILD18.vdomRef && memberName === \"ref\") {\n    if (newValue) {\n      newValue(elm);\n    }\n  } else if (BUILD18.vdomListener && (BUILD18.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n    if (memberName[2] === \"-\") {\n      memberName = memberName.slice(3);\n    } else if (isMemberInElement(win, ln)) {\n      memberName = ln.slice(2);\n    } else {\n      memberName = ln[2] + memberName.slice(3);\n    }\n    if (oldValue || newValue) {\n      const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n      memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, capture);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, capture);\n      }\n    }\n  } else if (BUILD18.vdomPropOrAttr) {\n    const isComplex = isComplexType(newValue);\n    if ((isProp || isComplex && newValue !== null) && !isSvg) {\n      try {\n        if (!elm.tagName.includes(\"-\")) {\n          const n = newValue == null ? \"\" : newValue;\n          if (memberName === \"list\") {\n            isProp = false;\n          } else if (oldValue == null || elm[memberName] != n) {\n            if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n              elm[memberName] = n;\n            } else {\n              elm.setAttribute(memberName, n);\n            }\n          }\n        } else if (elm[memberName] !== newValue) {\n          elm[memberName] = newValue;\n        }\n      } catch (e) {\n      }\n    }\n    let xlink = false;\n    if (BUILD18.vdomXlink) {\n      if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n        memberName = ln;\n        xlink = true;\n      }\n    }\n    if (newValue == null || newValue === false) {\n      if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n        if (BUILD18.vdomXlink && xlink) {\n          elm.removeAttributeNS(XLINK_NS, memberName);\n        } else {\n          elm.removeAttribute(memberName);\n        }\n      }\n    } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex && elm.nodeType === 1 /* ElementNode */) {\n      newValue = newValue === true ? \"\" : newValue;\n      if (BUILD18.vdomXlink && xlink) {\n        elm.setAttributeNS(XLINK_NS, memberName, newValue);\n      } else {\n        elm.setAttribute(memberName, newValue);\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => {\n  if (typeof value === \"object\" && value && \"baseVal\" in value) {\n    value = value.baseVal;\n  }\n  if (!value || typeof value !== \"string\") {\n    return [];\n  }\n  return value.split(parseClassListRegex);\n};\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2, isInitialRender) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || {};\n  const newVnodeAttrs = newVnode.$attrs$ || {};\n  if (BUILD19.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(\n          elm,\n          memberName,\n          oldVnodeAttrs[memberName],\n          void 0,\n          isSvgMode2,\n          newVnode.$flags$,\n          isInitialRender\n        );\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(\n      elm,\n      memberName,\n      oldVnodeAttrs[memberName],\n      newVnodeAttrs[memberName],\n      isSvgMode2,\n      newVnode.$flags$,\n      isInitialRender\n    );\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD20.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (BUILD20.isDev && newVNode2.$elm$) {\n    consoleDevError(\n      `The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`\n    );\n  }\n  if (BUILD20.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(newVNode2.$text$);\n  } else if (BUILD20.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD20.isDebug || BUILD20.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : win.document.createTextNode(\"\");\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n  } else {\n    if (BUILD20.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    if (!win.document) {\n      throw new Error(\n        \"You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.\"\n      );\n    }\n    elm = newVNode2.$elm$ = BUILD20.svg ? win.document.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) : win.document.createElement(\n      !useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    );\n    if (BUILD20.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    if ((BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD20.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD20.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      patchSlotNode(elm);\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD20.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n      if (BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) {\n        addRemoveSlotScopedClass(contentRef, elm, newParentVNode.$elm$, oldParentVNode == null ? void 0 : oldParentVNode.$elm$);\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.__childNodes || host.childNodes).find(\n      (ref) => ref[\"s-cr\"]\n    );\n    const childNodeArray = Array.from(\n      parentElm.__childNodes || parentElm.childNodes\n    );\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.__childNodes || parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD20.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(referenceNode(childNode).parentNode, childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD20.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD20.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD20.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD20.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD20.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD20.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD20.slotRelocation) {\n          insertBefore(\n            referenceNode(oldStartVnode.$elm$).parentNode,\n            node,\n            referenceNode(oldStartVnode.$elm$)\n          );\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (BUILD20.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD20.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD20.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    if (isInitialRender && !leftVNode.$key$ && rightVNode.$key$) {\n      leftVNode.$key$ = rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => node && node[\"s-ol\"] || node;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD20.vdomText || text === null) {\n    if (BUILD20.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD20.vdomAttribute || BUILD20.reflect) {\n      if (BUILD20.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD20.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      }\n      updateElement(oldVNode, newVNode2, isSvgMode, isInitialRender);\n    }\n    if (BUILD20.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD20.updatable && BUILD20.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD20.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD20.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD20.vdomText && BUILD20.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD20.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  const children = elm.__childNodes || elm.childNodes;\n  for (const childNode of children) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.__childNodes || node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD20.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar nullifyVNodeRefs = (vNode) => {\n  if (BUILD20.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  if (BUILD20.scoped && typeof newNode[\"s-sn\"] === \"string\" && !!newNode[\"s-sr\"] && !!newNode[\"s-cr\"]) {\n    addRemoveSlotScopedClass(newNode[\"s-cr\"], newNode, parent, newNode.parentElement);\n  } else if (BUILD20.experimentalSlotFixes && typeof newNode[\"s-sn\"] === \"string\") {\n    if (parent.getRootNode().nodeType !== 11 /* DOCUMENT_FRAGMENT_NODE */) {\n      patchParentNode(newNode);\n    }\n    parent.insertBefore(newNode, reference);\n    const { slotNode } = findSlotFromSlottedNode(newNode);\n    if (slotNode) dispatchSlotChangeEvent(slotNode);\n    return newNode;\n  }\n  if (BUILD20.experimentalSlotFixes && parent.__insertBefore) {\n    return parent.__insertBefore(newNode, reference);\n  } else {\n    return parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  }\n};\nfunction addRemoveSlotScopedClass(reference, slotNode, newParent, oldParent) {\n  var _a, _b;\n  let scopeId2;\n  if (reference && typeof slotNode[\"s-sn\"] === \"string\" && !!slotNode[\"s-sr\"] && reference.parentNode && reference.parentNode[\"s-sc\"] && (scopeId2 = slotNode[\"s-si\"] || reference.parentNode[\"s-sc\"])) {\n    const scopeName = slotNode[\"s-sn\"];\n    const hostName = slotNode[\"s-hn\"];\n    (_a = newParent.classList) == null ? void 0 : _a.add(scopeId2 + \"-s\");\n    if (oldParent && ((_b = oldParent.classList) == null ? void 0 : _b.contains(scopeId2 + \"-s\"))) {\n      let child = (oldParent.__childNodes || oldParent.childNodes)[0];\n      let found = false;\n      while (child) {\n        if (child[\"s-sn\"] !== scopeName && child[\"s-hn\"] === hostName && !!child[\"s-sr\"]) {\n          found = true;\n          break;\n        }\n        child = child.nextSibling;\n      }\n      if (!found) oldParent.classList.remove(scopeId2 + \"-s\");\n    }\n  }\n}\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const isHostElement = isHost(renderFnResults);\n  const rootVnode = isHostElement ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD20.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD20.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD20.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD20.scoped || BUILD20.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) && !(cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */);\n  if (BUILD20.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD20.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"] && win.document) {\n          const orgLocationNode = BUILD20.isDebug || BUILD20.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : win.document.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD20.hydrateServerSide && (!BUILD20.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */)) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === (refNode.__parentNode || refNode.parentNode)) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          const parent = nodeToRelocate.__parentNode || nodeToRelocate.parentNode;\n          const nextSibling = nodeToRelocate.__nextSibling || nodeToRelocate.nextSibling;\n          if (!insertBeforeNode && parentNodeRef !== parent || nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD20.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */ && nodeToRelocate.tagName !== \"SLOT-FB\") {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](slotRefNode);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD20.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    const children = rootVnode.$elm$.__childNodes || rootVnode.$elm$.childNodes;\n    for (const childNode of children) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = (slotVNode) => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(\n    `<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`\n  );\n};\nvar originalLocationDebugNode = (nodeToRelocate) => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(\n    `org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`)\n  );\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD21.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    const index = ancestorComponent[\"s-p\"].push(\n      new Promise(\n        (r) => hostRef.$onRenderResolve$ = () => {\n          ancestorComponent[\"s-p\"].splice(index - 1, 1);\n          r();\n        }\n      )\n    );\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD21.taskQueue && BUILD21.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD21.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD21.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD21.lazyLoad && BUILD21.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event, elm));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    maybePromise = safeCall(instance, \"componentWillLoad\", void 0, elm);\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    maybePromise = safeCall(instance, \"componentWillUpdate\", void 0, elm);\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\", void 0, elm));\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD21.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD21.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD21.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD21.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD21.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD21.asyncLoading && rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD21.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD21.allRenderFn ? true : false;\n  const lazyLoad = BUILD21.lazyLoad ? true : false;\n  const taskQueue = BUILD21.taskQueue ? true : false;\n  const updatable = BUILD21.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD21.hasRenderFn || BUILD21.reflect) {\n      if (BUILD21.vdomRender || BUILD21.reflect) {\n        if (BUILD21.hydrateServerSide) {\n          return Promise.resolve(instance).then((value) => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD21.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  safeCall(instance, \"componentDidRender\", void 0, elm);\n  if (BUILD21.isDev) {\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD21.asyncLoading && BUILD21.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n    }\n    safeCall(instance, \"componentDidLoad\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD21.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidUpdate\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD21.method && BUILD21.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD21.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = (ref) => {\n  if (BUILD21.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = (who) => {\n  if (BUILD21.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n  if (BUILD21.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg, elm) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD21.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = (elm) => {\n  var _a, _b;\n  return BUILD21.hydratedClass ? elm.classList.add((_a = BUILD21.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD21.hydratedAttribute ? elm.setAttribute((_b = BUILD21.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = (elm) => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD22.lazyLoad && !hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`\n    );\n  }\n  const elm = BUILD22.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD22.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(\n    newVal,\n    cmpMeta.$members$[propName][0],\n    BUILD22.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n  );\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD22.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD22.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      }\n    }\n    if (!BUILD22.lazyLoad || instance) {\n      if (BUILD22.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD22.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD23.isTesting) {\n    if (prototype.__stencilAugmented) {\n      return;\n    }\n    prototype.__stencilAugmented = true;\n  }\n  if (BUILD23.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach((cbName) => {\n      const originalFormAssociatedCallback = prototype[cbName];\n      Object.defineProperty(prototype, cbName, {\n        value(...args) {\n          const hostRef = getHostRef(this);\n          const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : this;\n          if (!instance) {\n            hostRef.$onReadyPromise$.then((asyncInstance) => {\n              const cb = asyncInstance[cbName];\n              typeof cb === \"function\" && cb.call(asyncInstance, ...args);\n            });\n          } else {\n            const cb = BUILD23.lazyLoad ? instance[cbName] : originalFormAssociatedCallback;\n            typeof cb === \"function\" && cb.call(instance, ...args);\n          }\n        }\n      });\n    });\n  }\n  if (BUILD23.member && cmpMeta.$members$ || BUILD23.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD23.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD23.prop || BUILD23.state) && (memberFlags & 31 /* Prop */ || (!BUILD23.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        const { get: origGetter, set: origSetter } = Object.getOwnPropertyDescriptor(prototype, memberName) || {};\n        if (origGetter) cmpMeta.$members$[memberName][0] |= 2048 /* Getter */;\n        if (origSetter) cmpMeta.$members$[memberName][0] |= 4096 /* Setter */;\n        if (flags & 1 /* isElementConstructor */ || !origGetter) {\n          Object.defineProperty(prototype, memberName, {\n            get() {\n              if (BUILD23.lazyLoad) {\n                if ((cmpMeta.$members$[memberName][0] & 2048 /* Getter */) === 0) {\n                  return getValue(this, memberName);\n                }\n                const ref = getHostRef(this);\n                const instance = ref ? ref.$lazyInstance$ : prototype;\n                if (!instance) return;\n                return instance[memberName];\n              }\n              if (!BUILD23.lazyLoad) {\n                return origGetter ? origGetter.apply(this) : getValue(this, memberName);\n              }\n            },\n            configurable: true,\n            enumerable: true\n          });\n        }\n        Object.defineProperty(prototype, memberName, {\n          set(newValue) {\n            const ref = getHostRef(this);\n            if (BUILD23.isDev) {\n              if (\n                // we are proxying the instance (not element)\n                (flags & 1 /* isElementConstructor */) === 0 && // if the class has a setter, then the Element can update instance values, so ignore\n                (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0 && // the element is not constructing\n                (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 && // the member is a prop\n                (memberFlags & 31 /* Prop */) !== 0 && // the member is not mutable\n                (memberFlags & 1024 /* Mutable */) === 0\n              ) {\n                consoleDevWarn(\n                  `@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`\n                );\n              }\n            }\n            if (origSetter) {\n              const currentValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              if (typeof currentValue === \"undefined\" && ref.$instanceValues$.get(memberName)) {\n                newValue = ref.$instanceValues$.get(memberName);\n              } else if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                ref.$instanceValues$.set(memberName, currentValue);\n              }\n              origSetter.apply(this, [\n                parsePropertyValue(\n                  newValue,\n                  memberFlags,\n                  BUILD23.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n                )\n              ]);\n              newValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (!BUILD23.lazyLoad) {\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (BUILD23.lazyLoad) {\n              if ((flags & 1 /* isElementConstructor */) === 0 || (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0) {\n                setValue(this, memberName, newValue, cmpMeta);\n                if (flags & 1 /* isElementConstructor */ && !ref.$lazyInstance$) {\n                  ref.$onReadyPromise$.then(() => {\n                    if (cmpMeta.$members$[memberName][0] & 4096 /* Setter */ && ref.$lazyInstance$[memberName] !== ref.$instanceValues$.get(memberName)) {\n                      ref.$lazyInstance$[memberName] = newValue;\n                    }\n                  });\n                }\n                return;\n              }\n              const setterSetVal = () => {\n                const currentValue = ref.$lazyInstance$[memberName];\n                if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                  ref.$instanceValues$.set(memberName, currentValue);\n                }\n                ref.$lazyInstance$[memberName] = parsePropertyValue(\n                  newValue,\n                  memberFlags,\n                  BUILD23.formAssociated && !!(cmpMeta.$flags$ & 64 /* formAssociated */)\n                );\n                setValue(this, memberName, ref.$lazyInstance$[memberName], cmpMeta);\n              };\n              if (ref.$lazyInstance$) {\n                setterSetVal();\n              } else {\n                ref.$onReadyPromise$.then(() => setterSetVal());\n              }\n            }\n          }\n        });\n      } else if (BUILD23.lazyLoad && BUILD23.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD23.observeAttribute && (!BUILD23.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName) && BUILD23.lazyLoad) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD23.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          const propDesc = Object.getOwnPropertyDescriptor(prototype, propName);\n          newValue = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n          if (newValue !== this[propName] && (!propDesc.get || !!propDesc.set)) {\n            this[propName] = newValue;\n          }\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (BUILD23.reflect && m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if (BUILD24.lazyLoad && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(\n          `st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`,\n          `[Stencil] Load module for <${cmpMeta.$tagName$}>`\n        );\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD24.member && !Cstr.isProxied) {\n        if (BUILD24.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD24.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e, elm);\n      }\n      if (BUILD24.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD24.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$, elm);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD24.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD24.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD24.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (BUILD24.hydrateServerSide && BUILD24.shadowDom) {\n          if (cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */) {\n            style = scopeCss(style, scopeId2, true);\n          } else if (needsScopedSSR()) {\n            style = expandPartSelectors(style);\n          }\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD24.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance, elm) => {\n  if (BUILD24.lazyLoad) {\n    safeCall(instance, \"connectedCallback\", void 0, elm);\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD25.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD25.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD25.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD25.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          } else if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            const scopeId2 = getScopeId(cmpMeta, BUILD25.mode ? elm.getAttribute(\"s-mode\") : void 0);\n            elm[\"s-sc\"] = scopeId2;\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD25.slotRelocation && !hostId) {\n        if (BUILD25.hydrateServerSide || (BUILD25.slot || BUILD25.shadowDom) && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD25.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD25.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD25.prop && !BUILD25.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD25.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$, elm));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  if (!win.document) {\n    return;\n  }\n  const contentRefElm = elm[\"s-cr\"] = win.document.createComment(\n    BUILD25.isDebug ? `content-ref (host=${elm.localName})` : \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = (instance, elm) => {\n  if (BUILD26.lazyLoad) {\n    safeCall(instance, \"disconnectedCallback\", void 0, elm || instance);\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD26.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD26.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$, elm);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$, elm));\n    }\n  }\n  if (rootAppliedStyles.has(elm)) {\n    rootAppliedStyles.delete(elm);\n  }\n  if (elm.shadowRoot && rootAppliedStyles.has(elm.shadowRoot)) {\n    rootAppliedStyles.delete(elm.shadowRoot);\n  }\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD27.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD27.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD27.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD27.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD27.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD27.experimentalSlotFixes) {\n    if (BUILD27.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype);\n    }\n  } else {\n    if (BUILD27.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype);\n    }\n    if (BUILD27.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD27.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD27.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  if (BUILD27.hydrateClientSide && BUILD27.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __hasHostListenerAttached: false,\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      if (!this.__hasHostListenerAttached) {\n        const hostRef = getHostRef(this);\n        addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n        this.__hasHostListenerAttached = true;\n      }\n      connectedCallback(this);\n      if (originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          createShadowRoot.call(this, cmpMeta);\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(\n              `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`\n            );\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = (elm) => {\n  if (BUILD27.style && BUILD27.mode && !BUILD27.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD28 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD28.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  if (!win.document) {\n    console.warn(\"Stencil: No document found. Skipping bootstrapping lazy components.\");\n    return;\n  }\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = win.document.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ win.document.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", win.document.baseURI).href;\n  if (BUILD28.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD28.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  if (BUILD28.hydrateClientSide && BUILD28.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD28.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD28.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD28.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD28.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD28.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD28.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD28.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                createShadowRoot.call(self, cmpMeta);\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            } else if (!BUILD28.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n          plt.raf(() => {\n            var _a3;\n            const hostRef = getHostRef(this);\n            const i2 = deferredConnectedCallbacks.findIndex((host) => host === this);\n            if (i2 > -1) {\n              deferredConnectedCallbacks.splice(i2, 1);\n            }\n            if (((_a3 = hostRef == null ? void 0 : hostRef.$vnode$) == null ? void 0 : _a3.$elm$) instanceof Node && !hostRef.$vnode$.$elm$.isConnected) {\n              delete hostRef.$vnode$.$elm$;\n            }\n          });\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD28.experimentalSlotFixes) {\n        if (BUILD28.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype);\n        }\n      } else {\n        if (BUILD28.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype);\n        }\n        if (BUILD28.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD28.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD28.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD28.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD28.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function(hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD28.invisiblePrehydration && (BUILD28.hydratedClass || BUILD28.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    if (BUILD28.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD29 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD29.hostListener && listeners && win.document) {\n    if (BUILD29.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD29.hostListenerTarget ? getHostListenerTarget(win.document, elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    if (BUILD29.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n};\nvar getHostListenerTarget = (doc, elm, flags) => {\n  if (BUILD29.hostListenerTargetDocument && flags & 4 /* TargetDocument */) {\n    return doc;\n  }\n  if (BUILD29.hostListenerTargetWindow && flags & 8 /* TargetWindow */) {\n    return win;\n  }\n  if (BUILD29.hostListenerTargetBody && flags & 16 /* TargetBody */) {\n    return doc.body;\n  }\n  if (BUILD29.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) {\n    return elm.parentElement;\n  }\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = (opts) => Object.assign(plt, opts);\n\n// src/runtime/render.ts\nfunction render(vnode, container) {\n  const cmpMeta = {\n    $flags$: 0,\n    $tagName$: container.tagName\n  };\n  const ref = {\n    $flags$: 0,\n    $cmpMeta$: cmpMeta,\n    $hostElement$: container\n  };\n  renderVdom(ref, vnode);\n}\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc, staticComponents) => {\n  if (doc != null) {\n    const docData = STENCIL_DOC_DATA in doc ? doc[STENCIL_DOC_DATA] : { ...DEFAULT_DOC_DATA };\n    docData.staticComponents = new Set(staticComponents);\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc, doc.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach((orgLocationNode) => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n            if (typeof nodeRef[\"s-sn\"] === \"string\" && !nodeRef.getAttribute(\"slot\")) {\n              nodeRef.setAttribute(\"s-sn\", nodeRef[\"s-sn\"]);\n            }\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          } else if (nodeRef.nodeType === 8 /* CommentNode */) {\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${COMMENT_NODE_ID}.${childId}`;\n            nodeRef.parentNode.insertBefore(commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach((childNode) => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(\n          (node) => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]\n        );\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(\n            HYDRATE_CHILD_ID,\n            `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`\n          );\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n    if (typeof childElm[\"s-sn\"] === \"string\" && !childElm.getAttribute(\"slot\")) {\n      childElm.setAttribute(\"s-sn\", childElm[\"s-sn\"]);\n    }\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport {\n  BUILD30 as BUILD,\n  Build,\n  Env,\n  Fragment,\n  H,\n  H as HTMLElement,\n  HYDRATED_STYLE_ID,\n  Host,\n  NAMESPACE2 as NAMESPACE,\n  STENCIL_DEV_MODE,\n  addHostEventListeners,\n  bootstrapLazy,\n  cmpModules,\n  connectedCallback,\n  consoleDevError,\n  consoleDevInfo,\n  consoleDevWarn,\n  consoleError,\n  createEvent,\n  defineCustomElement,\n  disconnectedCallback,\n  forceModeUpdate,\n  forceUpdate,\n  getAssetPath,\n  getElement,\n  getHostRef,\n  getMode,\n  getRenderingRef,\n  getValue,\n  h,\n  insertVdomAnnotations,\n  isMemberInElement,\n  loadModule,\n  modeResolutionChain,\n  needsScopedSSR,\n  nextTick,\n  parsePropertyValue,\n  plt,\n  postUpdateComponent,\n  promiseResolve,\n  proxyComponent,\n  proxyCustomElement,\n  readTask,\n  registerHost,\n  registerInstance,\n  render,\n  renderVdom,\n  setAssetPath,\n  setErrorHandler,\n  setMode,\n  setNonce,\n  setPlatformHelpers,\n  setPlatformOptions,\n  setScopedSSR,\n  setValue,\n  styles,\n  supportsConstructableStylesheets,\n  supportsListenerOptions,\n  supportsShadow,\n  win,\n  writeTask\n};\n"], "names": ["BUILD20", "BUILD21", "BUILD23"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAY,MAAC,SAAS,GAAG;AAClB,MAAM,KAAK,iBAAiB,EAAynB,oBAAoB,EAAE,UAAU,EAA0G,QAAQ,EAAE,IAAI,EAAwZ,cAAc,EAAE,IAAI,EAAkF,SAAS,EAAE,IAAkO,CAAC;;ACDphD,MAAC,aAAa,GAAG,MAAM;AAC5B,MAAM,YAAY,GAAG,EAAE;;ACD9B;AACA;AACA;AACA,IAAI,SAAS,GAAG,MAAM,CAAC,cAAc;AACrC,IAAI,QAAQ,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK;AAChC,EAAE,KAAK,IAAI,IAAI,IAAI,GAAG;AACtB,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACjE,CAAC;;AAqED;AACA,IAAI,UAAU,GAAG,CAAC,GAAG,KAAK;AAC1B,EAAE,IAAI,GAAG,CAAC,qBAAqB,EAAE;AACjC,IAAI,OAAO,GAAG,CAAC,qBAAqB,EAAE;AACtC;AACA,EAAE,OAAO,MAAM;AACf,CAAC;AACE,IAAC,gBAAgB,GAAG,CAAC,YAAY,EAAE,OAAO,KAAK;AAClD,EAAE,YAAY,CAAC,qBAAqB,GAAG,MAAM,OAAO;AACpD,EAAE,OAAO,CAAC,cAAc,GAAG,YAAY;AAIvC;AACA,IAAI,YAAY,GAAG,CAAC,WAAW,EAAE,OAAO,KAAK;AAC7C,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,aAAa,EAAE,WAAW;AAC9B,IAAI,SAAS,EAAE,OAAO;AACtB,IAAI,gBAAgB,kBAAkB,IAAI,GAAG;AAC7C,GAAG;AAIH,EAAwC;AACxC,IAAI,OAAO,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACrF;AACA,EAA2B;AAC3B,IAAI,OAAO,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC/E,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;AAC3B,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;AAC5B;AACA,EAAE,MAAM,GAAG,GAAG,OAAO;AACrB,EAAE,WAAW,CAAC,qBAAqB,GAAG,MAAM,GAAG;AAI/C,EAAE,OAAO,GAAG;AACZ,CAAC;AACD,IAAI,iBAAiB,GAAG,CAAC,GAAG,EAAE,UAAU,KAAK,UAAU,IAAI,GAAG;AAQ9D,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,IAAgB,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;;AAUnE;AACA,IAAI,UAAU,mBAAmB,IAAI,GAAG,EAAE;AAE1C,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,KAAK;AACrD,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AACzD,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc;AACzC,EAKS,IAAI,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,MAAM,MAAM,GAAkC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAQ;AAChF,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC;AAC7B;AACA;AACA,EAAE,OAAO;AACT;AACA;AACA;AACA;AACA,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,SAAS,EAA2E,EAAE,CAAC;AACzG,EAAE,CAAC,CAAC,IAAI;AACR,IAAI,CAAC,cAAc,KAAK;AACxB,MAAwC;AACxC,QAAQ,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC;AAChD;AACA,MAAM,OAAO,cAAc,CAAC,UAAU,CAAC;AACvC,KAAK;AACL,IAAI,CAAC,CAAC,KAAK;AACX,MAAM,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;AAC5C;AACA,GAAG;AACH,CAAC;;AAED;AACA,IAAI,MAAM,mBAAmB,IAAI,GAAG,EAAE;AAkBtC,IAAI,YAAY,GAAG,kDAAkD;AAOrE,IAAI,WAAW,GAAG,wDAAwD;AAC1E,IAAI,QAAQ,GAAG,8BAA8B;AAU1C,IAAC,GAAG,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG;AAGnD,IAAI,GAAG,GAAG;AACV,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE,cAAc,EAAE,EAAE;AACpB,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE;AACnB,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,qBAAqB,CAAC,EAAE,CAAC;AACxC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AACxF,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,EAAE,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;AAC3F,EAAE,EAAE,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK,IAAI,WAAW,CAAC,SAAS,EAAE,IAAI;AAC1D,CAAC;AAKD,IAAI,uBAAuB,mBAAmB,CAAC,MAAM;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,wBAAwB,GAAG,KAAK;AACtC,EAAE,IAAI;AACN,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,gBAAgB;AAC9D,MAAM,GAAG;AACT,MAAM,IAAI;AACV,MAAM,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;AAC3C,QAAQ,GAAG,GAAG;AACd,UAAU,wBAAwB,GAAG,IAAI;AACzC;AACA,OAAO;AACP,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE;AACd;AACA,EAAE,OAAO,wBAAwB;AACjC,CAAC,GAAG;AACD,IAAC,cAAc,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAC7C,IAAI,gCAAgC,GAA6B,gBAAgB,CAAC,MAAM;AACxF,EAAE,IAAI;AACN,IAAI,IAAI,aAAa,EAAE;AACvB,IAAI,OAAO,OAAO,IAAI,aAAa,EAAE,CAAC,WAAW,KAAK,UAAU;AAChE,GAAG,CAAC,OAAO,CAAC,EAAE;AACd;AACA,EAAE,OAAO,KAAK;AACd,CAAC,GAAG,CAAQ;AAIZ,IAAI,YAAY,GAAG,KAAK;AACxB,IAAI,aAAa,GAAG,EAAE;AACtB,IAAI,cAAc,GAAG,EAAE;AAEvB,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK;AAC1C,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAChB,EAAE,IAAI,CAAC,YAAY,EAAE;AACrB,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,kBAAkB;AAClD,MAAM,QAAQ,CAAC,KAAK,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;AACpB;AACA;AACA,CAAC;AACD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK;AACzB,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC5C,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAClC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,YAAY,CAAC,CAAC,CAAC;AACrB;AACA;AACA,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;AAClB,CAAC;AAiBD,IAAI,KAAK,GAAG,MAAM;AAIlB,EAAE,OAAO,CAAC,aAAa,CAAC;AACxB,EAaS;AACT,IAAI,OAAO,CAAC,cAAc,CAAC;AAC3B,IAAI,IAAI,YAAY,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;AACpB;AACA;AACA,CAAC;AACD,IAAI,QAAQ,GAAG,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AAEhD,IAAI,SAAS,mBAAmB,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC;;AAe/D;AACA,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,MAAM;AAC5C,IAAI,aAAa,GAAG,CAAC,CAAC,KAAK;AAC3B,EAAE,CAAC,GAAG,OAAO,CAAC;AACd,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,UAAU;AAC3C,CAAC;;AAED;AACA,SAAS,wBAAwB,CAAC,GAAG,EAAE;AACvC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;AAChB,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,aAAa,CAAC,wBAAwB,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM;AACxK;;AAEA;AACA,IAAI,6BAA6B,GAAG,CAAC,IAAI,KAAK;AAC9C,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACpD,CAAC;;AAiGD;AACA,IAAI,cAAc,GAAG,EAAE;AACvB,QAAQ,CAAC,cAAc,EAAE;AACzB,EAAE,GAAG,EAAE,MAAM,GAAG;AAChB,EAAE,GAAG,EAAE,MAAM,GAAG;AAChB,EAAE,EAAE,EAAE,MAAM,EAAE;AACd,EAAE,MAAM,EAAE,MAAM,MAAM;AACtB,EAAE,SAAS,EAAE,MAAM;AACnB,CAAC,CAAC;AACF,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM;AACrB,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,KAAK,EAAE,KAAK;AACd,EAAE;AACF,CAAC,CAAC;AACF,IAAI,GAAG,GAAG,CAAC,KAAK,MAAM;AACtB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,KAAK,EAAE,IAAI;AACb,EAAE;AACF,CAAC,CAAC;AACF,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE,EAAE;AACzB,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;AAChC,IAAI,IAAI,GAAG,YAAY,OAAO,EAAE;AAChC,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,OAAO,EAAE,CAAC,GAAG,CAAC;AACpB;AACA;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK;AAC9B,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC;AACrB;AACA,EAAE,MAAM,uBAAuB;AAC/B;AACA,IAAI,MAAM,GAAG,CAAC,MAAM,KAAK;AACzB,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,OAAO,MAAM,CAAC,KAAK;AACvB,GAAG,MAAM;AACT,IAAI,MAAM,MAAM,CAAC,KAAK;AACtB;AACA,CAAC;AACD,IAAI,SAAS,GAAG,CAAC,MAAM,KAAK;AAC5B,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,OAAO,MAAM,CAAC,KAAK;AACvB,GAAG,MAAM;AACT,IAAI,MAAM,MAAM,CAAC,KAAK;AACtB;AACA,CAAC;AAaD,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,MAAM,UAAU,GAGX,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C,EAAE,IAAI,gCAAgC,EAAE;AACxC,IAAI,MAAM,KAAK,GAAG,IAAI,aAAa,EAAE;AACrC,IAAI,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC;AACnC,IAAI,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;AAC7C;AACA;AA2BA,IAAI,4BAA4B,GAAG,CAAC,GAAG,KAAK;AAC5C,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC;AACpD,EAAE,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,KAAK,SAAS,EAAE;AAC5F,IAAI,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACpE,MAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,CAAC,sBAAsB,QAAQ,CAAC,OAAO,KAAK,SAAS,EAAE;AACvF,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE;AACjF,UAAU,QAAQ,CAAC,MAAM,GAAG,IAAI;AAChC,SAAS,MAAM;AACf,UAAU,QAAQ,CAAC,MAAM,GAAG,KAAK;AACjC;AACA;AACA,KAAK,CAAC;AACN;AACA,EAAE,IAAI,EAAE,GAAG,CAAC;AACZ,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC7C,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC;AACpC,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,sBAAsB,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,MAAM,EAAE;AACpG,MAAM,4BAA4B,CAAC,SAAS,CAAC;AAC7C;AACA;AACA,CAAC;AACD,IAAI,oBAAoB,GAAG,CAAC,UAAU,KAAK;AAC3C,EAAE,MAAM,MAAM,GAAG,EAAE;AACnB,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACjD,IAAI,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,MAAM;AACxD,IAAI,IAAI,WAAW,IAAI,WAAW,CAAC,WAAW,EAAE;AAChD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AAC9B;AACA;AACA,EAAE,OAAO,MAAM;AACf,CAAC;AACD,SAAS,gBAAgB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC1D,EAAE,IAAI,EAAE,GAAG,CAAC;AACZ,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,SAAS;AACf,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACvC,IAAI,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC;AAC9B,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,QAAQ,CAAC,KAAK,QAAQ,KAAK,MAAM,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,QAAQ,CAAC,EAAE;AAC5I,MAAM,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;AAClC,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,OAAO,YAAY;AAC9D;AACA,IAAI,YAAY,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,gBAAgB,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACnG;AACA,EAAE,OAAO,YAAY;AACrB;AACA,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,GAAG,IAAI,KAAK;AACnE,EAAE,MAAM,UAAU,GAAG,EAAE;AACvB,EAAE,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzE,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE;AAClC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,QAAQ,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/F;AACA,EAAE,OAAO,UAAU;AACnB,CAAC;AACD,IAAI,mBAAmB,GAAG,CAAC,cAAc,EAAE,QAAQ,KAAK;AACxD,EAAE,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,oBAAoB;AACvD,IAAI,IAAI,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE,EAAE;AACzE,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;AAC1D,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;AAC3C,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,QAAQ,KAAK,EAAE;AACxB,CAAC;AACD,IAAI,mBAAmB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK;AACrE,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE;AACxD,IAAI;AACJ;AACA,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;AACzD,EAAE,mBAAmB,CAAC,MAAM,CAAC,GAAG,QAAQ;AACxC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE;AACzD,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU;AAC5C,EAAE,MAAM,YAAY,GAA+C,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC;AACtG,EAaS;AACT,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,mBAAmB,CAAC;AAClD;AACA,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,mBAAmB;AACxC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AACrC,CAAC;AACD,IAAI,WAAW,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,MAAM;AACxI,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACpE,EAAE,MAAM,eAAe,GAAG,CAAC,YAAY,KAAK,CAAC,SAAS,IAAI,EAAE;AAC5D,IAAI,MAAM,QAAQ,GAAG,EAAE;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;AACjC,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;AAC9C,MAAM,OAAO,CAAC,KAAK,CAAC;AACpB;AACA;AACA;AACA,QAAQ,CAAC,CAAC;AACV;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,aAAa;AAC7C,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC;AAC1G,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAChC,MAAM,IAAI,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE;AACvC,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,CAAC,mBAAmB;AACvE;AACA,IAAI,OAAO,QAAQ;AACnB,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC;AACf,EAAE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC;AAC/C,EAAE,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC;AAC7C;AACA,SAAS,uBAAuB,CAAC,GAAG,EAAE;AACtC,EAAE,GAAG,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1G;AACA,SAAS,uBAAuB,CAAC,WAAW,EAAE,UAAU,EAAE;AAC1D,EAAE,IAAI,EAAE;AACR,EAAE,UAAU,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,aAAa,CAAC;AAC7F,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;AAC1D,EAAE,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE;AACvE,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;AAC3D,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChF,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC/B;AA6DA,IAAI,oBAAoB,GAAG,CAAC,oBAAoB,KAAK;AACrD,EAAE,oBAAoB,CAAC,aAAa,GAAG,oBAAoB,CAAC,WAAW;AACvE,EAAE,oBAAoB,CAAC,WAAW,GAAG,SAAS,QAAQ,EAAE;AACxD,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC;AAC1E,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAC7C,MAAM,MAAM,cAAc,GAAG,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACrE,MAAM,MAAM,WAAW,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACnE,MAAM,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC;AAC5D,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC;AAClG,MAAM,uBAAuB,CAAC,QAAQ,CAAC;AACvC,MAAM,4BAA4B,CAAC,IAAI,CAAC;AACxC,MAAM,OAAO,YAAY;AACzB;AACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;AACvC,GAAG;AACH,CAAC;AAmJD,IAAI,mBAAmB,GAAG,CAAC,GAAG,KAAK;AACnC,EAAE,MAAM,YAAY,SAAS,KAAK,CAAC;AACnC,IAAI,IAAI,CAAC,CAAC,EAAE;AACZ,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB;AACA;AACA,EAAE,yBAAyB,CAAC,UAAU,EAAE,GAAG,CAAC;AAC5C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE;AACzC,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC;AAC5D;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,mBAAmB,EAAE;AAClD,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;AACjC;AACA,GAAG,CAAC;AACJ,EAAE,yBAAyB,CAAC,YAAY,EAAE,GAAG,CAAC;AAC9C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;AAC3C,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAC/B;AACA,GAAG,CAAC;AACJ,EAAE,yBAAyB,CAAC,WAAW,EAAE,GAAG,CAAC;AAC7C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;AAC1C,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACxD;AACA,GAAG,CAAC;AACJ,EAAE,yBAAyB,CAAC,YAAY,EAAE,GAAG,CAAC;AAC9C,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;AAC3C,IAAI,GAAG,GAAG;AACV,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;AACvC,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7D,MAAM,OAAO,MAAM;AACnB;AACA,GAAG,CAAC;AACJ,CAAC;AAoFD,IAAI,mBAAmB,GAAG,CAAC,UAAU,EAAE,oBAAoB,EAAE,wBAAwB,CAAC;AACtF,IAAI,iBAAiB,GAAG;AACxB,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE,aAAa;AACf,EAAE,iBAAiB;AACnB,EAAE,aAAa;AACf,EAAE;AACF,CAAC;AACD,SAAS,yBAAyB,CAAC,YAAY,EAAE,IAAI,EAAE;AACvD,EAAE,IAAI,QAAQ;AACd,EAAE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;AAClD,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/E,GAAG,MAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;AACvD,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC5E;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC;AAClE;AACA,EAAE,IAAI,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,GAAG,YAAY,EAAE,QAAQ,CAAC;AAC1E;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE;AACpC,EAAE,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,EAAE;AAC7B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;AACxC,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,OAAO,QAAQ;AACvD,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;AAC/D,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAClC;AACA;AAKA,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK;AAC3C,EAIS;AACT,IAAI,OAAO,MAAM;AACjB,MAAM;AACN,KAAK;AACL;AACA,CAAC;AACD,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,WAAW,KAAK;AACvC,EASS;AACT,IAAI,OAAO,MAAM;AACjB,MAAM;AACN,KAAK;AACL;AACA,CAAC;AA2DE,IAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,QAAQ,KAAK;AAC9C,EAAE,IAAI,KAAK,GAAG,IAAI;AAClB,EAAE,IAAI,GAAG,GAAG,IAAI;AAChB,EAAE,IAAI,QAAQ,GAAG,IAAI;AACrB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,MAAM,aAAa,GAAG,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK;AACtB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC1C,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;AACnB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,IAAI,CAAC,KAAK,CAAC;AACnB,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAC9D,QAAQ,IAAI,MAAM,GAAG,OAAO,QAAQ,KAAK,UAAU,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AAC9E,UAAU,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B;AAKA,QAAQ,IAAI,MAAM,IAAI,UAAU,EAAE;AAClC,UAAU,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK;AACjE,SAAS,MAAM;AACf,UAAU,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AACpE;AACA,QAAQ,UAAU,GAAG,MAAM;AAC3B;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,CAAC;AAChB,EAAE,IAAI,SAAS,EAAE;AAIjB,IAAI,IAAuB,SAAS,CAAC,GAAG,EAAE;AAC1C,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG;AACzB;AACA,IAAI,IAA8B,SAAS,CAAC,IAAI,EAAE;AAClD,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI;AAC/B;AACA,IAA2B;AAC3B,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK;AAC9D,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,SAAS,CAAC,KAAK,GAAG,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAClI;AACA;AACA;AAMA,EAAE,IAA8B,OAAO,QAAQ,KAAK,UAAU,EAAE;AAChE,IAAI,OAAO,QAAQ;AACnB,MAAM,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,SAAS;AACzC,MAAM,aAAa;AACnB,MAAM;AACN,KAAK;AACL;AACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC;AACxC,EAAE,KAAK,CAAC,OAAO,GAAG,SAAS;AAC3B,EAAE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,IAAI,KAAK,CAAC,UAAU,GAAG,aAAa;AACpC;AACA,EAAuB;AACvB,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG;AACrB;AACA,EAA8B;AAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ;AAC3B;AACA,EAAE,OAAO,KAAK;AACd;AACA,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;AAC9B,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,KAAK,EAAE,GAAG;AACd,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,UAAU,EAAE;AAChB,GAAG;AACH,EAA6B;AAC7B,IAAI,KAAK,CAAC,OAAO,GAAG,IAAI;AACxB;AACA,EAAuB;AACvB,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;AACtB;AACA,EAA8B;AAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI;AACvB;AACA,EAAE,OAAO,KAAK;AACd,CAAC;AACE,IAAC,IAAI,GAAG;AACX,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI;AAClD,IAAI,WAAW,GAAG;AAClB,EAAE,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;AACtE,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB;AACnF,CAAC;AACD,IAAI,eAAe,GAAG,CAAC,IAAI,MAAM;AACjC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO;AACtB,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU;AAC5B,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK;AAClB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM;AACpB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK;AAClB,EAAE,KAAK,EAAE,IAAI,CAAC;AACd,CAAC,CAAC;AACF,IAAI,gBAAgB,GAAG,CAAC,IAAI,KAAK;AACjC,EAAE,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;AACvC,IAAI,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;AACxC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AACnB,MAAM,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;AAC/B;AACA,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,MAAM,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;AACjC;AACA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;AAC/C,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;AAC7B,EAAE,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;AACnC,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;AACzB,EAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;AAC3B,EAAE,OAAO,KAAK;AACd,CAAC;AAweD,IAAI,oBAAoB,GAAG,CAAC,QAAQ,KAAK;AACzC,EAAE,MAAM,aAAa,GAAG,6BAA6B,CAAC,QAAQ,CAAC;AAC/D,EAAE,OAAO,IAAI,MAAM;AACnB;AACA;AACA,IAAI,CAAC,6CAA6C,EAAE,aAAa,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC;AAC1F,IAAI;AACJ,GAAG;AACH,CAAC;AACqB,oBAAoB,CAAC,WAAW;AACnC,oBAAoB,CAAC,OAAO;AACrB,oBAAoB,CAAC,eAAe;AAgW9D,IAAI,kBAAkB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,gBAAgB,KAAK;AAYpE,EAAE,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;AACtD,IAAI,IAA2B,QAAQ,GAAG,CAAC,gBAAgB;AAC3D,MAEa;AACb,QAAQ,OAAO,SAAS,KAAK,OAAO,GAAG,KAAK,GAAG,SAAS,KAAK,EAAE,IAAI,CAAC,CAAC,SAAS;AAC9E;AACA;AACA,IAAI,IAA0B,QAAQ,GAAG,CAAC,eAAe;AACzD,MAAM,OAAO,OAAO,SAAS,KAAK,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,GAAG;AACpH;AACA,IAAI,IAA0B,QAAQ,GAAG,CAAC,eAAe;AACzD,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC;AAC9B;AACA,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,OAAO,SAAS;AAClB,CAAC;AAUE,IAAC,UAAU,GAAG,CAAC,GAAG,KAAwB,UAAU,CAAC,GAAG,CAAC,CAAC,aAAa;;AAE1E;AACG,IAAC,WAAW,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,KAAK;AACxC,EAAE,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;AAC7B,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK;AAItB,MAAM,OAAO,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE;AAClC,QAAQ,OAAO,EAAE,IAA2B;AAC5C,QAAQ,QAAQ,EAAE,IAA4B;AAC9C,QAAQ,UAAU,EAAE,IAA+B;AACnD,QAAQ;AACR,OAAO,CAAC;AACR;AACA,GAAG;AACH;AACA,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,KAAK;AACrC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC/B,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;AACvB,EAAE,OAAO,EAAE;AACX,CAAC;AAID,IAAI,iBAAiB,mBAAmB,IAAI,OAAO,EAAE;AACrD,IAAI,aAAa,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,KAAK;AACpD,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;AAClC,EAAE,IAAI,gCAAgC,IAAI,OAAO,EAAE;AACnD,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,aAAa,EAAE;AACxC,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,KAAK,GAAG,OAAO;AACrB,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;AAChC;AACA,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,OAAO;AACnB;AACA,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC7B,CAAC;AACD,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,KAAK;AACtD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAa,CAAC;AAC5C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;AACpC,EAAE,IAA6B,CAAC,GAAG,CAAC,QAAQ,EAAE;AAC9C,IAAI,OAAO,QAAQ;AACnB;AACA,EAAE,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,KAAK,EAAE,0BAA0B,kBAAkB,GAAG,GAAG,CAAC,QAAQ;AACpH,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,IAAI,kBAAkB;AACxE,MAAM,IAAI,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACnE,MAAM,IAAI,QAAQ;AAClB,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,EAAE,aAAa,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC5F;AACA,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AACxC,QAEe;AACf,UAAU,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACxD,UAAU,QAAQ,CAAC,SAAS,GAAG,KAAK;AACpC,UAAU,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAChG,UAAU,IAAI,KAAK,IAAI,IAAI,EAAE;AAC7B,YAAY,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AACjD;AAIA,UAAU,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,EAAE;AACnE,YAAY,IAAI,kBAAkB,CAAC,QAAQ,KAAK,MAAM,EAAE;AACxD,cAAc,MAAM,eAAe,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC;AACjG,cAAc,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC;AACrK,cAAc,kBAAkB,CAAC,YAAY;AAC7C,gBAAgB,QAAQ;AACxB,gBAAgB,CAAC,cAAc,IAAI,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,UAAU,MAAM,kBAAkB,GAAG,cAAc,GAAG;AACxH,eAAe;AACf,aAAa,MAAM,IAAI,MAAM,IAAI,kBAAkB,EAAE;AACrD,cAAc,IAAI,gCAAgC,EAAE;AACpD,gBAAgB,MAAM,UAAU,GAAG,IAAI,aAAa,EAAE;AACtD,gBAAgB,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC;AAC7C,gBAAgB,kBAAkB,CAAC,kBAAkB,GAAG,CAAC,UAAU,EAAE,GAAG,kBAAkB,CAAC,kBAAkB,CAAC;AAC9G,eAAe,MAAM;AACrB,gBAAgB,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC;AACxF,gBAAgB,IAAI,sBAAsB,EAAE;AAC5C,kBAAkB,sBAAsB,CAAC,SAAS,GAAG,KAAK,GAAG,sBAAsB,CAAC,SAAS;AAC7F,iBAAiB,MAAM;AACvB,kBAAkB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC;AACtD;AACA;AACA,aAAa,MAAM;AACnB,cAAc,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC;AACjD;AACA;AACA,UAAU,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,+BAA+B;AAChE,YAAY,kBAAkB,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC;AAC3D;AACA;AACA,QAAQ,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,0BAA0B;AACzD,UAAU,QAAQ,CAAC,SAAS,IAAI,WAAW;AAC3C;AACA,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;AACrC;AACA;AACA,KAAK,MAAM,IAAgC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnG,MAAM,kBAAkB,CAAC,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,CAAC,kBAAkB,EAAE,KAAK,CAAC;AAC/F;AACA;AACA,EAAE,OAAO,QAAQ;AACjB,CAAC;AACD,IAAI,YAAY,GAAG,CAAC,OAAO,KAAK;AAChC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;AACnC,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO;AAC/B,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,SAAS,CAAC;AACvE,EAAE,MAAM,QAAQ,GAAG,QAAQ;AAC3B,IAA2C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE;AAC9F,IAAI,OAEF,CAAC;AACH,EAAE,IAAuE,KAAK,GAAG,EAAE,iCAAiC;AACpH,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ;AAC1B,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;AACtC;AACA,EAAE,eAAe,EAAE;AACnB,CAAC;AACD,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,KAAK,IAA0F,GAAG,CAAC,SAAS,CAAC;AAqB7I,IAAI,WAAW,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,KAAK;AACxF,EAAE,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC7B,IAAI;AACJ;AACA,EAAE,IAAI,MAAM,GAAG,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC;AACjD,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,WAAW,EAAE;AACnC,EAAE,IAAyB,UAAU,KAAK,OAAO,EAAE;AACnD,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS;AACnC,IAAI,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;AAC/C,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;AAC7C,IAOW;AACX,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E;AACA,GAAG,MAAM,IAAyB,UAAU,KAAK,OAAO,EAAE;AAC1D,IAA2B;AAC3B,MAAM,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AACnC,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;AACjD,UAAU,IAAkC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAChE,YAAY,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;AAC1C,WAAW,MAAM;AACjB,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;AAChC;AACA;AACA;AACA;AACA,IAAI,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AACjC,MAAM,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1D,QAAQ,IAAkC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC9D,UAAU,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrD,SAAS,MAAM;AACf,UAAU,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC1C;AACA;AACA;AACA,GAAG,MAAM,IAAuB,UAAU,KAAK,KAAK,EAAE,CACnD,MAAM,IAAuB,UAAU,KAAK,KAAK,EAAE;AACtD,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,QAAQ,CAAC,GAAG,CAAC;AACnB;AACA,GAAG,MAAM,IAA4B,CAAoB,CAAC,MAAM,CAAoC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzJ,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AAC/B,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,KAAK,MAAM,IAAI,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAC3C,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9B,KAAK,MAAM;AACX,MAAM,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9C;AACA,IAAI,IAAI,QAAQ,IAAI,QAAQ,EAAE;AAC9B,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,oBAAoB,CAAC;AAC/D,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;AAC9D,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AACnD;AACA,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AACnD;AACA;AACA,GAAG,MAAkC;AACrC,IAAI,MAAM,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC7C,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAM,EAAE;AAC9D,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxC,UAAU,MAAM,CAAC,GAAG,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,QAAQ;AACpD,UAAU,IAAI,UAAU,KAAK,MAAM,EAAE;AACrC,YAAY,MAAM,GAAG,KAAK;AAC1B,WAAW,MAAM,IAAI,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AAC/D,YAAY,IAAI,OAAO,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE;AACxE,cAAc,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;AACjC,aAAa,MAAM;AACnB,cAAc,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;AAC7C;AACA;AACA,SAAS,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;AACjD,UAAU,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ;AACpC;AACA,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB;AACA;AACA,IAAI,IAAI,KAAK,GAAG,KAAK;AACrB,IAA2B;AAC3B,MAAM,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;AACrD,QAAQ,UAAU,GAAG,EAAE;AACvB,QAAQ,KAAK,GAAG,IAAI;AACpB;AACA;AACA,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAChD,MAAM,IAAI,QAAQ,KAAK,KAAK,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;AACrE,QAAQ,IAAyB,KAAK,EAAE;AACxC,UAAU,GAAG,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC;AACrD,SAAS,MAAM;AACf,UAAU,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC;AACzC;AACA;AACA,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,iBAAiB,KAAK,KAAK,CAAC,SAAS,IAAI,GAAG,CAAC,QAAQ,KAAK,CAAC,oBAAoB;AACnH,MAAM,QAAQ,GAAG,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG,QAAQ;AAClD,MAAM,IAAyB,KAAK,EAAE;AACtC,QAAQ,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC1D,OAAO,MAAM;AACb,QAAQ,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC9C;AACA;AACA;AACA,CAAC;AACD,IAAI,mBAAmB,GAAG,IAAI;AAC9B,IAAI,cAAc,GAAG,CAAC,KAAK,KAAK;AAChC,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,EAAE;AAChE,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO;AACzB;AACA,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC3C,IAAI,OAAO,EAAE;AACb;AACA,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;AACzC,CAAC;AACD,IAAI,oBAAoB,GAAG,SAAS;AACpC,IAAI,mBAAmB,GAAG,IAAI,MAAM,CAAC,oBAAoB,GAAG,GAAG,CAAC;;AAEhE;AACA,IAAI,aAAa,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,KAAK;AACzE,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,KAAK,EAAE,2BAA2B,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK;AACjI,EAAE,MAAM,aAAa,GAAG,QAAQ,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE;AAC1D,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE;AAC9C,EAAyB;AACzB,IAAI,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE;AAC1E,MAAM,IAAI,EAAE,UAAU,IAAI,aAAa,CAAC,EAAE;AAC1C,QAAQ,WAAW;AACnB,UAAU,GAAG;AACb,UAAU,UAAU;AACpB,UAAU,aAAa,CAAC,UAAU,CAAC;AACnC,UAAU,MAAM;AAChB,UAAU,UAAU;AACpB,UAAU,QAAQ,CAAC,OAEX,CAAC;AACT;AACA;AACA;AACA,EAAE,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE;AACxE,IAAI,WAAW;AACf,MAAM,GAAG;AACT,MAAM,UAAU;AAChB,MAAM,aAAa,CAAC,UAAU,CAAC;AAC/B,MAAM,aAAa,CAAC,UAAU,CAAC;AAC/B,MAAM,UAAU;AAChB,MAAM,QAAQ,CAAC,OAEX,CAAC;AACL;AACA,CAAC;AACD,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;AAClC;AACA,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK;AACzD;AACA;AACA,IAAI;AACJ,GAAG;AACH;;AAEA;AACA,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,kBAAkB,GAAG,KAAK;AAC9B,IAAI,2BAA2B,GAAG,KAAK;AACvC,IAAI,iBAAiB,GAAG,KAAK;AAC7B,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,SAAS,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,UAAU,KAAK;AAChE,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,SAAS,GAAG,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC;AACzD,EAAE,IAAI,EAAE,GAAG,CAAC;AACZ,EAAE,IAAI,GAAG;AACT,EAAE,IAAI,SAAS;AACf,EAAE,IAAI,QAAQ;AACd,EAAE,IAA8B,CAAC,kBAAkB,EAAE;AACrD,IAAI,iBAAiB,GAAG,IAAI;AAC5B,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,MAAM,EAAE;AACpC,MAAM,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,UAAU;AAC/C;AACA;AACA,QAAQ,CAAC;AACT;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,OAAO;AACP;AACA;AAMA,EAAE,IAAwB,SAAS,CAAC,MAAM,KAAK,IAAI,EAAE;AACrD,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC;AACzE,GAAG,MAAM,IAA8B,SAAS,CAAC,OAAO,GAAG,CAAC,wBAAwB;AACpF,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAAsF,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;AAC9I,IAA+B;AAC/B,MAAM,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC;AAC/C;AACA,GAAG,MAAM;AAIT,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACvB,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ;AACR,OAAO;AACP;AACA,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,GAGjB,GAAG,CAAC,QAAQ,CAAC,aAAa;AAClC,MAAM,CAAC,kBAAkB,IAAIA,KAAO,CAAC,cAAc,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,wBAAwB,SAAS,GAAG,SAAS,CAAC;AAC1H,KAAK;AAIL,IAA+B;AAC/B,MAAM,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC;AAC/C;AACA,IAAI,IAAuF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;AACtI,MAAM,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;AAC9C;AACA,IAAI,IAAI,SAAS,CAAC,UAAU,EAAE;AAC9B,MAAM,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC3D,QAAQ,SAAS,GAAG,SAAS,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC;AAC5D,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC;AACpC;AACA;AACA;AAQA;AACA,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,WAAW;AAC3B,EAA8B;AAC9B,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,EAAE;AAChF,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;AACxB,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,UAAU;AAC9B,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE;AAC1C,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG;AACtE,MAAM,aAAa,CAAC,GAAG,CAAC;AACxB,MAAM,QAAQ,GAAG,cAAc,IAAI,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC;AACrG,MAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE;AAClF,QAEe;AACf,UAAU,yBAAyB,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC;AAChE;AACA;AACA,MAAyF;AACzF,QAAQ,wBAAwB,CAAC,UAAU,EAAE,GAAG,EAAE,cAAc,CAAC,KAAK,EAAE,cAAc,IAAI,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;AAC/H;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,CAAC;AAqBD,IAAI,yBAAyB,GAAG,CAAC,SAAS,EAAE,SAAS,KAAK;AAC1D,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC;AAClB,EAAE,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,UAAU,CAAC;AAStF,EAAE,KAAK,IAAI,EAAE,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;AAC7D,IAAI,MAAM,SAAS,GAAG,iBAAiB,CAAC,EAAE,CAAC;AAC3C,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,WAAW,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AAChE,MAAM,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;AAC5F,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;AAChC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAChC,MAAM,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAChC,MAAM,iBAAiB,GAAG,IAAI;AAC9B;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,yBAAyB,CAAC,SAAS,EAAE,SAAS,CAAC;AACrD;AACA;AACA,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;AACnB,CAAC;AACD,IAAI,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK;AAC9E,EAAE,IAAI,YAAY,GAA6B,SAAS,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,UAAU,IAAI,SAAS;AAC7G,EAAE,IAAI,SAAS;AACf,EAAE,IAAyB,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,OAAO,KAAK,WAAW,EAAE;AAC5F,IAAI,YAAY,GAAG,YAAY,CAAC,UAAU;AAC1C;AACA,EAAE,OAAO,QAAQ,IAAI,MAAM,EAAE,EAAE,QAAQ,EAAE;AACzC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE;AAC1B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC;AACxD,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,SAAS;AAC1C,QAAQ,YAAY,CAAC,YAAY,EAAE,SAAS,EAA2B,aAAa,CAAC,MAAM,CAAC,CAAS,CAAC;AACtG;AACA;AACA;AACA,CAAC;AACD,IAAI,YAAY,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK;AACjD,EAAE,KAAK,IAAI,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK;AAC7B,MAAM,gBAAgB,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,GAAG,EAAE;AACf,QAAoC;AACpC,UAAU,2BAA2B,GAAG,IAAI;AAC5C,UAAU,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE;AAC3B,YAAY,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;AAChC,WAAW,MAAM;AACjB,YAAY,yBAAyB,CAAC,GAAG,EAAE,IAAI,CAAC;AAChD;AACA;AACA,QAAQ,GAAG,CAAC,MAAM,EAAE;AACpB;AACA;AACA;AACA,CAAC;AACD,IAAI,cAAc,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,GAAG,KAAK,KAAK;AACtF,EAAE,IAAI,WAAW,GAAG,CAAC;AACrB,EAAE,IAAI,WAAW,GAAG,CAAC;AACrB,EAAE,IAAI,QAAQ,GAAG,CAAC;AAClB,EAAE,IAAI,EAAE,GAAG,CAAC;AACZ,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;AAClC,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;AACpC,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;AAClC,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9B,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;AACpC,EAAE,IAAI,IAAI;AACV,EAAE,IAAI,SAAS;AACf,EAAE,OAAO,WAAW,IAAI,SAAS,IAAI,WAAW,IAAI,SAAS,EAAE;AAC/D,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;AAC/B,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,KAAK,MAAM,IAAI,WAAW,IAAI,IAAI,EAAE;AACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,KAAK,MAAM,IAAI,aAAa,IAAI,IAAI,EAAE;AACtC,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,KAAK,MAAM,IAAI,WAAW,IAAI,IAAI,EAAE;AACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,KAAK,MAAM,IAAI,WAAW,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;AAC3E,MAAM,KAAK,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,KAAK,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE;AACvE,MAAM,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;AACtD,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,KAAK,MAAM,IAAI,WAAW,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,CAAC,EAAE;AACzE,MAAM,IAA8B,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,IAAI,WAAW,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE;AACtG,QAAQ,yBAAyB,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;AACxE;AACA,MAAM,KAAK,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,CAAC;AACxD,MAAM,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC;AACjF,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,KAAK,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;AACzE,MAAM,IAA8B,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,IAAI,WAAW,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE;AACtG,QAAQ,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;AACtE;AACA,MAAM,KAAK,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC;AACxD,MAAM,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;AACrE,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,EAAE;AACnB,MAA2B;AAC3B,QAAQ,KAAK,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,SAAS,EAAE,EAAE,EAAE,EAAE;AACtD,UAAU,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE;AAChG,YAAY,QAAQ,GAAG,EAAE;AACzB,YAAY;AACZ;AACA;AACA;AACA,MAAM,IAAuB,QAAQ,IAAI,CAAC,EAAE;AAC5C,QAAQ,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;AACnC,QAAQ,IAAI,SAAS,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE;AACrD,UAAU,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC;AAC5E,SAAS,MAAM;AACf,UAAU,KAAK,CAAC,SAAS,EAAE,aAAa,EAAE,eAAe,CAAC;AAC1D,UAAU,KAAK,CAAC,QAAQ,CAAC,GAAG,MAAM;AAClC,UAAU,IAAI,GAAG,SAAS,CAAC,KAAK;AAChC;AACA,QAAQ,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,IAAI,GAAG,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC;AAC7E,QAAQ,aAAa,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC;AAC5C;AACA,MAAM,IAAI,IAAI,EAAE;AAChB,QAAoC;AACpC,UAAU,YAAY;AACtB,YAAY,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,UAAU;AACzD,YAAY,IAAI;AAChB,YAAY,aAAa,CAAC,aAAa,CAAC,KAAK;AAC7C,WAAW;AACX;AAGA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG,SAAS,EAAE;AAC/B,IAAI,SAAS;AACb,MAAM,SAAS;AACf,MAAM,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK;AACtE,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,WAAW;AACjB,MAAM;AACN,KAAK;AACL,GAAG,MAAM,IAAyB,WAAW,GAAG,SAAS,EAAE;AAC3D,IAAI,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC;AAC/C;AACA,CAAC;AACD,IAAI,WAAW,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,eAAe,GAAG,KAAK,KAAK;AACtE,EAAE,IAAI,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;AAC5C,IAAI,IAA8B,SAAS,CAAC,KAAK,KAAK,MAAM,EAAE;AAC9D,MAAM,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;AACnD;AACA,IAAI,IAAuB,CAAC,eAAe,EAAE;AAC7C,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK;AACjD;AACA,IAAI,IAAI,eAAe,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE;AACjE,MAAM,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;AACxC;AACA,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,KAAK;AACd,CAAC;AACD,IAAI,aAAa,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI;AAC1D,IAAI,KAAK,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,GAAG,KAAK,KAAK;AAC9D,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;AAC9C,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU;AACzC,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,UAAU;AAE1C,EAAE,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM;AAC/B,EAAE,IAAI,aAAa;AACnB,EAAE,IAAyB,IAAI,KAAK,IAAI,EAAE;AAI1C,IAAkD;AAOlD,MAAM,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,SAA0B,CAAC;AACpE;AACA,IAAI,IAAyB,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;AAC3E,MAAM,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC;AAC/E,KAAK,MAAM,IAAI,WAAW,KAAK,IAAI,EAAE;AACrC,MAAM,IAA6C,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;AAC7E,QAAQ,GAAG,CAAC,WAAW,GAAG,EAAE;AAC5B;AACA,MAAM,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7E,KAAK,MAAM;AACX;AACA,MAAM,CAAC,eAAe,IAAIA,KAAO,CAAC,SAAS,IAAI,WAAW,KAAK;AAC/D,MAAM;AACN,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1D;AAIA,GAAG,MAAM,IAAkD,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE;AAC1F,IAAI,aAAa,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;AAC/C,GAAG,MAAM,IAAwB,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE;AAC3D,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI;AACnB;AACA,CAAC;AACD,IAAI,aAAa,GAAG,EAAE;AACtB,IAAI,4BAA4B,GAAG,CAAC,GAAG,KAAK;AAC5C,EAAE,IAAI,IAAI;AACV,EAAE,IAAI,gBAAgB;AACtB,EAAE,IAAI,CAAC;AACP,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU;AACrD,EAAE,KAAK,MAAM,SAAS,IAAI,QAAQ,EAAE;AACpC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;AAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU;AACnF,MAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;AACxC,MAAM,KAAK,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzD,QAAQ,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,IAAqF,CAAC,EAAE;AAC7K,UAAU,IAAI,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;AACnD,YAAY,IAAI,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC;AACzF,YAAY,2BAA2B,GAAG,IAAI;AAC9C,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,QAAQ;AACnD,YAAY,IAAI,gBAAgB,EAAE;AAClC,cAAc,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC3E,cAAc,gBAAgB,CAAC,aAAa,GAAG,SAAS;AACxD,aAAa,MAAM;AACnB,cAAc,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC9C,cAAc,aAAa,CAAC,IAAI,CAAC;AACjC,gBAAgB,aAAa,EAAE,SAAS;AACxC,gBAAgB,gBAAgB,EAAE;AAClC,eAAe,CAAC;AAChB;AACA,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;AAC9B,cAAc,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK;AAClD,gBAAgB,IAAI,mBAAmB,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;AACtF,kBAAkB,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC;AAC3F,kBAAkB,IAAI,gBAAgB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;AACvE,oBAAoB,YAAY,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa;AAC/E;AACA;AACA,eAAe,CAAC;AAChB;AACA,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,IAAI,CAAC,EAAE;AAC9E,YAAY,aAAa,CAAC,IAAI,CAAC;AAC/B,cAAc,gBAAgB,EAAE;AAChC,aAAa,CAAC;AACd;AACA;AACA;AACA;AACA,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,oBAAoB;AACpD,MAAM,4BAA4B,CAAC,SAAS,CAAC;AAC7C;AACA;AACA,CAAC;AACD,IAAI,gBAAgB,GAAG,CAAC,KAAK,KAAK;AAClC,EAAuB;AACvB,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;AACjE,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAC9D;AACA,CAAC;AACD,IAAI,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,KAAK;AACnD,EAAE,IAAsB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACvG,IAAI,wBAAwB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,aAAa,CAAC;AACrF;AASA,EAES;AACT,IAAI,OAAO,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC;AAC5E;AACA,CAAC;AACD,SAAS,wBAAwB,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;AAC7E,EAAE,IAAI,EAAE,EAAE,EAAE;AACZ,EAAE,IAAI,QAAQ;AACd,EAAE,IAAI,SAAS,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;AACxM,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;AACtC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;AACrC,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzE,IAAI,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAE;AACnG,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;AACrE,MAAM,IAAI,KAAK,GAAG,KAAK;AACvB,MAAM,OAAO,KAAK,EAAE;AACpB,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAC1F,UAAU,KAAK,GAAG,IAAI;AACtB,UAAU;AACV;AACA,QAAQ,KAAK,GAAG,KAAK,CAAC,WAAW;AACjC;AACA,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7D;AACA;AACA;AACA,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,aAAa,GAAG,KAAK,KAAK;AACtE,EAAK,IAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AACpB,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,aAAa;AACvC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;AACnC,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;AAC1D,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;AAC/C,EAAE,MAAM,SAAS,GAAG,aAAa,GAAG,eAAe,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC;AACpF,EAAE,WAAW,GAAG,OAAO,CAAC,OAAO;AAe/B,EAAE,IAAuB,OAAO,CAAC,gBAAgB,EAAE;AACnD,IAAI,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,EAAE;AAC/C,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG;AAChC,MAAM,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ;AAChF,KAAK;AACL;AACA,EAAE,IAAI,aAAa,IAAI,SAAS,CAAC,OAAO,EAAE;AAC1C,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;AACtD,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxF,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,GAAG,IAAI;AACxB,EAAE,SAAS,CAAC,OAAO,IAAI,CAAC;AACxB,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS;AAC7B,EAAE,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAuB,OAAO,CAAC,UAAU,IAAI,OAAO,CAAU;AAChG,EAA2C;AAC3C,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;AAC7B;AACA,EAAE,kBAAkB,GAAqB,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,GAAG,4BAA4B;AACpJ,EAA8B;AAC9B,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;AAChC,IAAI,2BAA2B,GAAG,KAAK;AACvC;AACA,EAAE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;AAC3C,EAA8B;AAC9B,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC;AACpB,IAAI,IAAI,iBAAiB,EAAE;AAC3B,MAAM,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;AACnD,MAAM,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;AAChD,QAAQ,MAAM,cAAc,GAAG,YAAY,CAAC,gBAAgB;AAC5D,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE;AACrD,UAAU,MAAM,eAAe,GAA8F,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;AAC5J,UAAU,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc;AAClD,UAAU,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,cAAc,CAAC;AAC3G;AACA;AACA,MAAM,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;AAChD,QAAQ,MAAM,cAAc,GAAG,YAAY,CAAC,gBAAgB;AAC5D,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,aAAa;AACtD,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU;AACtD,UAAU,IAAI,gBAAgB,GAAG,WAAW,CAAC,WAAW;AACxD,UAAuJ;AACvJ,YAAY,IAAI,eAAe,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,eAAe;AACrG,YAAY,OAAO,eAAe,EAAE;AACpC,cAAc,IAAI,OAAO,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI;AAC9E,cAAc,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,MAAM,CAAC,IAAI,aAAa,MAAM,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;AAC3I,gBAAgB,OAAO,GAAG,OAAO,CAAC,WAAW;AAC7C,gBAAgB,OAAO,OAAO,KAAK,cAAc,KAAK,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;AACnG,kBAAkB,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,WAAW;AAC1E;AACA,gBAAgB,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAClD,kBAAkB,gBAAgB,GAAG,OAAO;AAC5C,kBAAkB;AAClB;AACA;AACA,cAAc,eAAe,GAAG,eAAe,CAAC,eAAe;AAC/D;AACA;AACA,UAAU,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,UAAU;AACjF,UAAU,MAAM,WAAW,GAAG,cAAc,CAAC,aAAa,IAAI,cAAc,CAAC,WAAW;AACxF,UAAU,IAAI,CAAC,gBAAgB,IAAI,aAAa,KAAK,MAAM,IAAI,WAAW,KAAK,gBAAgB,EAAE;AACjG,YAAY,IAAI,cAAc,KAAK,gBAAgB,EAAE;AACrD,cAAc,IAAsC,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;AACvG,gBAAgB,cAAc,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ;AACnF;AACA,cAAc,YAAY,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB,CAAC;AAC3E,cAAc,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,sBAAsB,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE;AAC3G,gBAAgB,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;AAC1F;AACA;AACA;AACA,UAAU,cAAc,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC;AACzG,SAAS,MAAM;AACf,UAAU,IAAI,cAAc,CAAC,QAAQ,KAAK,CAAC,oBAAoB;AAC/D,YAAY,IAAI,aAAa,EAAE;AAC/B,cAAc,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;AACxF;AACA,YAAY,cAAc,CAAC,MAAM,GAAG,IAAI;AACxC;AACA;AACA;AACA;AACA,IAAI,IAAI,2BAA2B,EAAE;AACrC,MAAM,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;AACnD;AACA,IAAI,GAAG,CAAC,OAAO,IAAI,EAAE;AACrB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;AAC5B;AAYA,EAAE,UAAU,GAAG,MAAM;AACrB,CAAC;;AAcD;AACA,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,iBAAiB,KAAK;AACvD,EAAE,IAA4B,iBAAiB,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;AAC3G,IAAI,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI;AAC/C,MAAM,IAAI,OAAO;AACjB,QAAQ,CAAC,CAAC,KAAK,OAAO,CAAC,iBAAiB,GAAG,MAAM;AACjD,UAAU,iBAAiB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;AACvD,UAAU,CAAC,EAAE;AACb;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,IAAI,cAAc,GAAG,CAAC,OAAO,EAAE,aAAa,KAAK;AACjD,EAA8C;AAC9C,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;AACzB;AACA,EAAE,IAA4B,OAAO,CAAC,OAAO,GAAG,CAAC,6BAA6B;AAC9E,IAAI,OAAO,CAAC,OAAO,IAAI,GAAG;AAC1B,IAAI;AACJ;AACA,EAAE,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC;AACxD,EAAE,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC;AAC9D,EAAE,OAA2B,SAAS,CAAC,QAAQ,CAAC,CAAa;AAC7D,CAAC;AACD,IAAI,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa,KAAK;AAChD,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;AACnC,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;AAC/E,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;AAClE,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,IAAI,KAAK;AACnB,MAAM,CAAC,wBAAwB,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,uNAAuN;AAClR,KAAK;AACL;AACA,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,aAAa,EAAE;AACrB,IAAkD;AAClD,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;AAC5B,MAAM,IAAI,OAAO,CAAC,iBAAiB,EAAE;AACrC,QAAQ,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1G,QAAQ,OAAO,CAAC,iBAAiB,GAAG,MAAM;AAC1C;AACA;AAEA,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,CAAC;AACvE,GAAG,MAAM;AAET,IAAI,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,EAAE,MAAM,EAAE,GAAG,CAAC;AACzE;AAEA,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AACpG,EAAE,WAAW,EAAE;AACf,EAAE,OAAO,OAAO,CAAC,YAAY,EAAE,MAAM,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;AACvF,CAAC;AACD,IAAI,OAAO,GAAG,CAAC,YAAY,EAAE,EAAE,KAAK,UAAU,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK;AACrG,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;AACrB,EAAE,EAAE,EAAE;AACN,CAAC,CAAC,GAAG,EAAE,EAAE;AACT,IAAI,UAAU,GAAG,CAAC,YAAY,KAAK,YAAY,YAAY,OAAO,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,UAAU;AAClJ,IAAI,eAAe,GAAG,OAAO,OAAO,EAAE,QAAQ,EAAE,aAAa,KAAK;AAClE,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;AACnC,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;AACrE,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;AACxB,EAAE,IAAqB,aAAa,EAAE;AACtC,IAAI,YAAY,CAAC,OAAO,CAAC;AACzB;AACA,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;AAIrE,EAES;AACT,IAAI,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,aAAa,CAAC;AACrD;AAmBA,EAAE,IAA4B,EAAE,EAAE;AAClC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACxB,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM;AACxB;AACA,EAAE,SAAS,EAAE;AACb,EAAE,SAAS,EAAE;AACb,EAA4B;AAC5B,IAAI,MAAM,gBAAgB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAChE,IAAI,MAAM,UAAU,GAAG,MAAM,mBAAmB,CAAC,OAAO,CAAC;AACzD,IAAI,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;AACvC,MAAM,UAAU,EAAE;AAClB,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;AAC1B,MAAM,gBAAgB,CAAC,MAAM,GAAG,CAAC;AACjC;AACA;AAGA,CAAC;AAED,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,aAAa,KAAK;AAK5D,EAAE,IAAI;AAEN,IAAI,QAAQ,GAAiB,QAAQ,CAAC,MAAM,EAAE,CAAuC;AACrF,IAAgC;AAChC,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;AAC5B;AACA,IAA+B;AAC/B,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;AAC1B;AACA,IAAgD;AAChD,MAAiD;AACjD,QAEe;AACf,UAAU,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;AACtD;AACA;AAQA;AACA,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;AAC1C;AAEA,EAAE,OAAO,IAAI;AACb,CAAC;AAED,IAAI,mBAAmB,GAAG,CAAC,OAAO,KAAK;AACvC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS;AAC7C,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa;AACnC,EAAE,MAAM,aAAa,GAAG,UAAU,CAAC,YAAY,EAAE,OAAO,CAAC;AACzD,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;AAClE,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB;AAIvD,EAAE,QAAQ,CAAC,QAAQ,EAAE,oBAAoB,EAAE,MAAM,EAAE,GAAG,CAAC;AAKvD,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,0BAA0B,EAAE;AACxD,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;AACzB,IAAwD;AACxD,MAAM,eAAe,CAAC,GAAG,CAAC;AAC1B;AAIA,IAAI,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,CAAC;AAKvD,IAAI,aAAa,EAAE;AACnB,IAA8B;AAC9B,MAAM,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC;AACnC,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC9B,QAAQ,UAAU,CAAQ,CAAC;AAC3B;AACA;AACA,GAAG,MAAM;AAIT,IAAI,QAAQ,CAAC,QAAQ,EAAE,oBAAoB,EAAE,MAAM,EAAE,GAAG,CAAC;AAKzD,IAAI,aAAa,EAAE;AACnB;AACA,EAA0C;AAC1C,IAAI,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC;AACpC;AACA,EAA4B;AAC5B,IAAI,IAAI,OAAO,CAAC,iBAAiB,EAAE;AACnC,MAAM,OAAO,CAAC,iBAAiB,EAAE;AACjC,MAAM,OAAO,CAAC,iBAAiB,GAAG,MAAM;AACxC;AACA,IAAI,IAAI,OAAO,CAAC,OAAO,GAAG,GAAG,sBAAsB;AACnD,MAAM,QAAQ,CAAC,MAAM,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,CAAC,OAAO,IAAI,IAAyD;AAChF;AACA,CAAC;AAYD,IAAI,UAAU,GAAG,CAAC,GAAG,KAAK;AAI1B,EAAE,QAAQ,CAAC,MAAM,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;AAIjF,CAAC;AACD,IAAI,QAAQ,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,KAAK;AAC/C,EAAE,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AACpC,IAAI,IAAI;AACR,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AAClC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;AAC1B;AACA;AACA,EAAE,OAAO,MAAM;AACf,CAAC;AAYD,IAAI,eAAe,GAAG,CAAC,GAAG,KAAK;AAC/B,EAAK,IAAC,EAAE;AACR,EAAE,OAA+B,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,GAAGC,KAAO,CAAC,oBAAoB,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,CAAC,CAA2H;AAC5O,CAAC;;AAcD;AACA,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAChF,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,KAAK;AACnD,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;AACjC,EAAE,IAAwB,CAAC,OAAO,EAAE;AACpC,IAAI,MAAM,IAAI,KAAK;AACnB,MAAM,CAAC,gCAAgC,EAAE,OAAO,CAAC,SAAS,CAAC,yYAAyY;AACpc,KAAK;AACL;AACA,EAAE,MAAM,GAAG,GAAsB,OAAO,CAAC,aAAa,CAAM;AAC5D,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;AACvD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO;AAC/B,EAAE,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;AAClE,EAAE,MAAM,GAAG,kBAAkB;AAC7B,IAAI,MAAM;AACV,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAE/B,CAAC;AACH,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AACjE,EAAE,MAAM,cAAc,GAAG,MAAM,KAAK,MAAM,IAAI,CAAC,UAAU;AACzD,EAAE,IAAI,CAAsB,EAAE,KAAK,GAAG,CAAC,8BAA8B,IAAI,MAAM,KAAK,MAAM,KAAK,cAAc,EAAE;AAC/G,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;AAwBlD,IAAI,IAAyB,QAAQ,EAAE;AACvC,MAAM,IAA6B,OAAO,CAAC,UAAU,IAAI,KAAK,GAAG,GAAG,qBAAqB;AACzF,QAAQ,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzD,QAAQ,IAAI,YAAY,EAAE;AAC1B,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,eAAe,KAAK;AAChD,YAAY,IAAI;AAChB,cAAc,QAAQ,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjE,aAAa,CAAC,OAAO,CAAC,EAAE;AACxB,cAAc,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;AAClC;AACA,WAAW,CAAC;AACZ;AACA;AACA,MAAM,IAAyB,CAAC,KAAK,IAAI,CAAC,qBAAqB,EAAE,yBAAyB,MAAM,CAAC,oBAAoB;AACrH,QAAQ,IAAI,QAAQ,CAAC,qBAAqB,EAAE;AAC5C,UAAU,IAAI,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;AAClF,YAAY;AACZ;AACA;AACA,QAAQ,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC;AACtC;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,cAAc,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,KAAK;AAC/C,EAAE,IAAI,EAAE,EAAE,EAAE;AACZ,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AA2BlC,EAAE,IAAsB,OAAO,CAAC,SAAS,IAA6B,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC7G,IAAI,IAA6B,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACvE,MAAM,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;AACxC;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9E,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK;AACjD,MAAM,IAAuC,CAAC,WAAW,GAAG,EAAE,eAAe,CAAsB,KAAK,GAAG,CAAC,sBAAsB,WAAW,GAAG,EAAE,aAAa,EAAE;AACjK,QAAQ,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,IAAI,EAAE;AACjH,QAAQ,IAAI,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAChE,QAAQ,IAAI,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAChE,QAAQ,IAAI,KAAK,GAAG,CAAC,+BAA+B,CAAC,UAAU,EAAE;AACjE,UAAU,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;AACvD,YAAY,GAAG,GAAG;AAClB,cAAoC;AACpC,gBAAgB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,mBAAmB,CAAC,EAAE;AAClF,kBAAkB,OAAO,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC;AACnD;AACA,gBAAgB,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;AAC5C,gBAAgB,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,cAAc,GAAG,SAAS;AACrE,gBAAgB,IAAI,CAAC,QAAQ,EAAE;AAC/B,gBAAgB,OAAO,QAAQ,CAAC,UAAU,CAAC;AAC3C;AAIA,aAAa;AACb,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,UAAU,EAAE;AACxB,WAAW,CAAC;AACZ;AACA,QAAQ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;AACrD,UAAU,GAAG,CAAC,QAAQ,EAAE;AACxB,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;AAgBxC,YAAY,IAAI,UAAU,EAAE;AAC5B,cAAc,MAAM,YAAY,GAAG,WAAW,GAAG,EAAE,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC;AAClH,cAAc,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC/F,gBAAgB,QAAQ,GAAG,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC;AAC/D,eAAe,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,YAAY,EAAE;AAChF,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC;AAClE;AACA,cAAc,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE;AACrC,gBAAgB,kBAAkB;AAClC,kBAAkB,QAAQ;AAC1B,kBAAkB,WAEF;AAChB,eAAe,CAAC;AAChB,cAAc,QAAQ,GAAG,WAAW,GAAG,EAAE,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC;AACxG,cAAc,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC3D,cAAc;AACd;AAKA,YAAkC;AAClC,cAAc,IAAI,CAAC,KAAK,GAAG,CAAC,iCAAiC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,mBAAmB,CAAC,EAAE;AAChI,gBAAgB,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC7D,gBAAgB,IAAI,KAAK,GAAG,CAAC,+BAA+B,CAAC,GAAG,CAAC,cAAc,EAAE;AACjF,kBAAkB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM;AAClD,oBAAoB,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,iBAAiB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACzJ,sBAAsB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,QAAQ;AAC/D;AACA,mBAAmB,CAAC;AACpB;AACA,gBAAgB;AAChB;AACA,cAAc,MAAM,YAAY,GAAG,MAAM;AACzC,gBAAgB,MAAM,YAAY,GAAG,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC;AACnE,gBAAgB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,YAAY,EAAE;AAC3E,kBAAkB,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC;AACpE;AACA,gBAAgB,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,kBAAkB;AACnE,kBAAkB,QAAQ;AAC1B,kBAAkB,WAEF,CAAC;AACjB,gBAAgB,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;AACnF,eAAe;AACf,cAAc,IAAI,GAAG,CAAC,cAAc,EAAE;AACtC,gBAAgB,YAAY,EAAE;AAC9B,eAAe,MAAM;AACrB,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,CAAC;AAC/D;AACA;AACA;AACA,SAAS,CAAC;AACV,OAAO,MAAM,IAA0C,KAAK,GAAG,CAAC,+BAA+B,WAAW,GAAG,EAAE,eAAe;AAC9H,QAAQ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE;AACrD,UAAU,KAAK,CAAC,GAAG,IAAI,EAAE;AACzB,YAAY,IAAI,GAAG;AACnB,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;AACxC,YAAY,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,mBAAmB,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM;AAC5G,cAAc,IAAI,GAAG;AACrB,cAAc,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3F,aAAa,CAAC;AACd;AACA,SAAS,CAAC;AACV;AACA,KAAK,CAAC;AACN,IAAI,IAAgC,CAAsB,KAAK,GAAG,CAAC,4BAA4B,EAAE;AACjG,MAAM,MAAM,kBAAkB,mBAAmB,IAAI,GAAG,EAAE;AAC1D,MAAM,SAAS,CAAC,wBAAwB,GAAG,SAAS,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAClF,QAAQ,GAAG,CAAC,GAAG,CAAC,MAAM;AACtB,UAAU,IAAI,GAAG;AACjB,UAAU,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC3D,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAIC,KAAO,CAAC,QAAQ,EAAE;AACjE,YAAY,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrC,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC;AACjC,WAAW,MAAM,IAAI,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;AAC7F,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,EAAE;AACtC,YAAY;AACZ,WAAW,MAAM,IAAI,QAAQ,IAAI,IAAI,EAAE;AACvC,YAAY,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;AAC5C,YAAY,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO;AACrE,YAAY,IAAI,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC,8BAA8B,IAAI,MAAM,GAAG,GAAG,uBAAuB,QAAQ,KAAK,QAAQ,EAAE;AAElI,cAAc,MAAM,QAAQ,GAAsB,OAAO,CAAC,cAAc,CAAM;AAC9E,cAAc,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC;AACvF,cAAc,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,KAAK;AACvE,gBAAgB,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE;AACpD,kBAAkB,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACrF;AACA,eAAe,CAAC;AAChB;AACA,YAAY;AACZ;AACA,UAAU,MAAM,QAAQ,GAAG,MAAM,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC;AAC/E,UAAU,QAAQ,GAAG,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAG,QAAQ;AAChG,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAChF,YAAY,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACrC;AACA,SAAS,CAAC;AACV,OAAO;AACP,MAAM,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,IAAI;AAC1C,wBAAwB,IAAI,GAAG,CAAC;AAChC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AACrE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK;AAC7F,YAAY,IAAI,GAAG;AACnB,YAAY,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ;AAC7C,YAAY,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACtD,YAAY,IAAuB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,oBAAoB;AACjE,cAAc,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAChG;AACA,YAAY,OAAO,QAAQ;AAC3B,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA,EAAE,OAAO,IAAI;AACb,CAAC;;AAED;AACA,IAAI,mBAAmB,GAAG,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,KAAK;AACzE,EAAE,IAAI,IAAI;AACV,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,oCAAoC,CAAC,EAAE;AAClE,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE;AACzB,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc;AAC3C,IAAI,IAAwB,QAAQ,EAAE;AACtC,MAAM,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,OAAqB,CAAC;AACnE,MAAM,IAAI,UAAU,IAAI,MAAM,IAAI,UAAU,EAAE;AAC9C,QAAQ,MAAM,OAAO,GAAG,UAAU,CAG1B,CAAC;AACT,QAAQ,IAAI,GAAG,MAAM,UAAU;AAC/B,QAAQ,OAAO,EAAE;AACjB,OAAO,MAAM;AACb,QAAQ,IAAI,GAAG,UAAU;AACzB;AACA,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AACrG;AACA,MAAM,IAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;AAC7C,QAAmC;AACnC,UAAU,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;AAC5C;AACA,QAAQ,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,kBAAkB;AACzD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI;AAC7B;AACA,MAAM,MAAM,cAAc,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC;AAC5E,MAA0B;AAC1B,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC;AAC5B;AACA,MAAM,IAAI;AACV,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC;AACzB,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB,QAAQ,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC;AAC5B;AACA,MAA0B;AAC1B,QAAQ,OAAO,CAAC,OAAO,IAAI,EAAE;AAC7B;AACA,MAAiC;AACjC,QAAQ,OAAO,CAAC,OAAO,IAAI,GAAG;AAC9B;AACA,MAAM,cAAc,EAAE;AACtB,MAAM,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW;AAC5B,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS;AAClC,MAAM,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,oBAAoB;AAC9F;AACA,IAAI,IAAqB,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AAC7C,MAAM,IAAI,KAAK;AACf,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC1C,QAAQ,KAAK,GAAG,IAAI,CAAC,KAAK;AAC1B;AASA,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,OAA2B,CAAC;AAC9D,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AACjC,QAAQ,MAAM,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC;AAQjF,QAAQ,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC;AAC5F,QAAQ,iBAAiB,EAAE;AAC3B;AACA;AACA;AACA,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB;AACvD,EAAE,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC;AACtD,EAAE,IAA4B,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;AAC9E,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5C,GAAG,MAAM;AACT,IAAI,QAAQ,EAAE;AACd;AACA,CAAC;AACD,IAAI,qBAAqB,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK;AAC/C,EAAwB;AACxB,IAAI,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,CAAC;AACxD;AACA,CAAC;;AAED;AACA,IAAI,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACjC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC,EAAE;AACvD,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;AACnC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;AACrC,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,mBAAmB,EAAE,OAAO,CAAC,SAAS,CAAC;AAI3E,IAAI,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,oBAAoB,EAAE;AACnD,MAAM,OAAO,CAAC,OAAO,IAAI,CAAC;AAe1B,MAA6C;AAC7C,QAAQ,IAAwE;AAChF,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC,2BAA2B,CAAC,0BAA0B,EAAE;AACpF,UAAU,mBAAmB,CAAC,GAAG,CAAC;AAClC;AACA;AACA,MAAgC;AAChC,QAAQ,IAAI,iBAAiB,GAAG,GAAG;AACnC,QAAQ,OAAO,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,IAAI,iBAAiB,CAAC,IAAI,EAAE;AAC3F,UAAU,IAA6J,iBAAiB,CAAC,KAAK,CAAC,EAAE;AACjM,YAAY,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,GAAG,iBAAiB,CAAC;AACtF,YAAY;AACZ;AACA;AACA;AACA,MAAM,IAAkD,OAAO,CAAC,SAAS,EAAE;AAC3E,QAAQ,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK;AAC/E,UAAU,IAAI,WAAW,GAAG,EAAE,eAAe,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AAC7E,YAAY,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC;AACzC,YAAY,OAAO,GAAG,CAAC,UAAU,CAAC;AAClC,YAAY,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK;AACnC;AACA,SAAS,CAAC;AACV;AACA,MAEa;AACb,QAAQ,mBAAmB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC;AAClD;AACA,KAAK,MAAM;AACX,MAAM,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,WAAkB,CAAC;AACrE,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE;AAC7D,QAAQ,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;AAC1D,OAAO,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE;AACtE,QAAQ,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,qBAAqB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AAC/F;AACA;AACA,IAAI,YAAY,EAAE;AAClB;AACA,CAAC;AACD,IAAI,mBAAmB,GAAG,CAAC,GAAG,KAAK;AACnC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACrB,IAAI;AACJ;AACA,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa;AAChE,IAA8D;AAC9D,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;AAC9B,EAAE,YAAY,CAAC,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC,UAAU,CAAC;AAClD,CAAC;AAID,IAAI,kBAAkB,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK;AAC5C,EAAwB;AACxB,IAAI,QAAQ,CAAC,QAAQ,EAAE,sBAAsB,EAAE,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC;AACvE;AACA,CAAC;AACD,IAAI,oBAAoB,GAAG,OAAO,GAAG,KAAK;AAC1C,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC,EAAE;AACvD,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;AACnC,IAA8B;AAC9B,MAAM,IAAI,OAAO,CAAC,aAAa,EAAE;AACjC,QAAQ,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;AAC/D,QAAQ,OAAO,CAAC,aAAa,GAAG,MAAM;AACtC;AACA;AACA,IAEW,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE;AAClE,MAAM,kBAAkB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;AACrD,KAAK,MAAM,IAAI,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE;AACpE,MAAM,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,kBAAkB,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AAC1F;AACA;AACA,EAAE,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAClC,IAAI,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC;AACjC;AACA,EAAE,IAAI,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC/D,IAAI,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;AAC5C;AACA,CAAC;;AA2HD;AACG,IAAC,aAAa,GAAG,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,KAAK;AACnD,EAAE,IAAI,EAAE;AAKR,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACrB,IAAI,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC;AACvF,IAAI;AACJ;AACA,EAAE,MAAM,YAAY,GAAG,UAAU,CAAgB,CAAC;AAClD,EAAE,MAAM,OAAO,GAAG,EAAE;AACpB,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE;AACvC,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,cAAc;AAC5C,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI;AAChC,EAAE,MAAM,WAAW,mBAAmB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;AACzE,EAAE,MAAM,UAAU,mBAAmB,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACxE,EAAE,MAAM,0BAA0B,GAAG,EAAE;AACvC,EAAE,IAAI,eAAe;AACrB,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC;AAC7B,EAAE,GAAG,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI;AAYvF,EAAE,IAAI,iBAAiB,GAAG,KAAK;AAC/B,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,KAAK;AACvC,MAAM,IAAI,GAAG;AACb,MAAM,MAAM,OAAO,GAAG;AACtB,QAAQ,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;AAC/B,QAAQ,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;AACjC,QAAQ,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;AACjC,QAAQ,WAAW,EAAE,WAAW,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,0BAA0B;AACvD,QAAQ,iBAAiB,GAAG,IAAI;AAChC;AACA,MAA0B;AAC1B,QAAQ,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;AAC1C;AACA,MAAgC;AAChC,QAAQ,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;AAC5C;AACA,MAA2B;AAC3B,QAAQ,OAAO,CAAC,gBAAgB,GAAG,EAAE;AACrC;AACA,MAAiC;AACjC,QAAQ,OAAO,CAAC,UAAU,GAAG,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE;AACtE;AAIA,MAAM,MAAM,OAAO,GAAwG,OAAO,CAAC,SAAS;AAC5I,MAAM,MAAM,WAAW,GAAG,cAAc,WAAW,CAAC;AACpD;AACA,QAAQ,WAAW,CAAC,IAAI,EAAE;AAC1B,UAAU,KAAK,CAAC,IAAI,CAAC;AACrB,UAAU,IAAI,CAAC,2BAA2B,GAAG,KAAK;AAClD,UAAU,IAAI,GAAG,IAAI;AACrB,UAAU,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;AACrC,UAAU,IAAyB,OAAO,CAAC,OAAO,GAAG,CAAC,+BAA+B;AACrF,YAAgC;AAChC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpC,gBAAgB,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACpD,eAAe,MAAM;AACrB,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,MAAM,EAAE;AACrD,kBAAkB,MAAM,IAAI,KAAK;AACjC,oBAAoB,CAAC,0CAA0C,EAAE,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,6CAA6C;AACxK,mBAAmB;AACnB;AACA;AACA;AAGA;AACA;AACA,QAAQ,iBAAiB,GAAG;AAC5B,UAAU,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;AAC1C,UAAU,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;AACjD,YAAY,IAAI,CAAC,2BAA2B,GAAG,IAAI;AACnD,YAAY,qBAAqB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,WAAkB,CAAC;AAC5E;AACA,UAAU,IAAI,eAAe,EAAE;AAC/B,YAAY,YAAY,CAAC,eAAe,CAAC;AACzC,YAAY,eAAe,GAAG,IAAI;AAClC;AACA,UAAU,IAAI,eAAe,EAAE;AAC/B,YAAY,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;AACjD,WAAW,MAAM;AACjB,YAAY,GAAG,CAAC,GAAG,CAAC,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAClD;AACA;AACA,QAAQ,oBAAoB,GAAG;AAC/B,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACnD,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM;AACxB,YAAY,IAAI,GAAG;AACnB,YAAY,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;AAC5C,YAAY,MAAM,EAAE,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AACpF,YAAY,IAAI,EAAE,GAAG,EAAE,EAAE;AACzB,cAAc,0BAA0B,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;AACtD;AACA,YAAY,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,KAAK,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE;AACzJ,cAAc,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK;AAC1C;AACA,WAAW,CAAC;AACZ;AACA,QAAQ,gBAAgB,GAAG;AAC3B,UAAU,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,gBAAgB;AAClD;AACA,OAAO;AACP,MAIa;AACb,QAAuC;AACvC,UAAU,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC;AACpD;AAIA,QAAwC;AACxC,UAAU,oBAAoB,CAAC,WAAW,CAAC,SAAS,CAAC;AACrD;AAIA;AASA,MAAM,OAAO,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC;AAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACvE,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,QAAQ,eAAe,CAAC,MAAM;AAC9B,UAAU,OAAO;AACjB,UAAU,cAAc,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAChD,SAAS;AACT;AACA,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,IAAI,IAAI,iBAAiB,EAAE;AAC3B,MAAM,UAAU,CAAC,WAAW,IAAI,WAAW;AAC3C;AACA,IAA+F;AAC/F,MAAM,UAAU,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,YAAY;AAC7D;AACA,IAAI,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE;AACrC,MAAM,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;AAChD,MAAM,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC5F,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AACzB,QAAQ,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AAC/C;AACA,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,WAAW,GAAG,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;AAC5F;AACA;AACA,EAAE,eAAe,GAAG,KAAK;AACzB,EAAE,IAAI,0BAA0B,CAAC,MAAM,EAAE;AACzC,IAAI,0BAA0B,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACtE,GAAG,MAAM;AACT,IAEW;AACX,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACjE;AACA;AACA,EAAE,YAAY,EAAE;AAChB;AAOA,IAAI,qBAAqB,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,KAAK;AAChF,EAAE,IAA4B,SAAS,IAAI,GAAG,CAAC,QAAQ,EAAE;AAQzD,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK;AAC7C,MAAM,MAAM,MAAM,GAAgC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAM;AACvG,MAAM,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC;AACxD,MAAM,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC1C,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;AAC1C,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC5G,KAAK,CAAC;AACN;AACA,CAAC;AACD,IAAI,iBAAiB,GAAG,CAAC,OAAO,EAAE,UAAU,KAAK,CAAC,EAAE,KAAK;AACzD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAA0B;AAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,GAAG,GAAG,sBAAsB;AACrD,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;AAC3E,OAAO,MAAM;AACb,QAAQ,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAC5F;AACA;AAGA,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;AAC1C;AACA,CAAC;AACD,IAAI,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK;AAIjD,EAAE,IAAwC,KAAK,GAAG,CAAC,qBAAqB;AACxE,IAAI,OAAO,GAAG;AACd;AACA,EAAE,IAAsC,KAAK,GAAG,EAAE,mBAAmB;AACrE,IAAI,OAAO,GAAG,CAAC,IAAI;AACnB;AAIA,EAAE,OAAO,GAAG;AACZ,CAAC;AACD,IAAI,gBAAgB,GAAG,CAAC,KAAK,KAAK,uBAAuB,GAAG;AAC5D,EAAE,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC;AAC1C,EAAE,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,oBAAoB;AACzC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC;;AAEnC;AACG,IAAC,QAAQ,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,OAAO,GAAG;;;;;;;;;;;;;;", "x_google_ignoreList": [2]}