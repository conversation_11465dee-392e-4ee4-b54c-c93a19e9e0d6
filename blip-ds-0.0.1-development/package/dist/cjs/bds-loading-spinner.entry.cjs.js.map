{"version": 3, "file": "bds-loading-spinner.entry.cjs.js", "sources": ["src/assets/svg/load-extra-small.svg", "src/assets/svg/load-small.svg", "src/assets/svg/load-standard.svg", "src/components/loading-spinner/loading-spinner.scss?tag=bds-loading-spinner&encapsulation=shadow", "src/components/loading-spinner/loading-spinner.tsx"], "sourcesContent": ["<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path\nd=\"M1.6 8C0.716345 8 -0.0160869 7.27668 0.159469 6.41064C0.261393 5.90784 0.411689 5.41479 0.608964 4.93853C1.011 3.96793 1.60028 3.08601 2.34315 2.34315C3.08602 1.60028 3.96793 1.011 4.93853 0.608963C5.4148 0.411689 5.90784 0.261393 6.41064 0.159469C7.27668 -0.0160866 8 0.716345 8 1.6C8 2.48365 7.26447 3.17508 6.42946 3.46421C6.33981 3.49525 6.25099 3.52898 6.16312 3.56538C5.58076 3.8066 5.05161 4.16017 4.60589 4.60589C4.16017 5.05161 3.8066 5.58076 3.56538 6.16312C3.52898 6.25099 3.49525 6.33981 3.46421 6.42946C3.17508 7.26447 2.48366 8 1.6 8Z\"\nfill-rule=\"evenodd\"\nfill=\"currentColor\"\n/>\n</svg>", "<svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path\nd=\"M2 16C0.895431 16 -0.0128754 15.1011 0.124839 14.0052C0.302736 12.5895 0.669597 11.2009 1.21793 9.87706C2.022 7.93585 3.20055 6.17203 4.68629 4.68629C6.17203 3.20055 7.93586 2.022 9.87707 1.21793C11.2009 0.669596 12.5895 0.302734 14.0052 0.124838C15.1011 -0.0128746 16 0.89543 16 2C16 3.10457 15.0985 3.98304 14.0092 4.16628C13.1188 4.31608 12.246 4.56627 11.4078 4.91344C9.95189 5.5165 8.62902 6.40042 7.51472 7.51472C6.40042 8.62902 5.5165 9.95189 4.91345 11.4078C4.56627 12.246 4.31608 13.1188 4.16628 14.0092C3.98304 15.0985 3.10457 16 2 16Z\"\nfill-rule=\"evenodd\"\nfill=\"currentColor\"\n/>\n</svg>", "<svg width=\"64\" height=\"64\" viewBox=\"0 0 64 64\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path\nd=\"M4 32C1.79086 32 -0.0257507 30.2023 0.249677 28.0104C0.605472 25.1789 1.33919 22.4017 2.43586 19.7541C4.04401 15.8717 6.40111 12.3441 9.37259 9.37258C12.3441 6.40111 15.8717 4.044 19.7541 2.43585C22.4017 1.33919 25.1789 0.605469 28.0104 0.249676C30.2023 -0.0257492 32 1.79086 32 4C32 6.20914 30.197 7.96608 28.0185 8.33257C26.2376 8.63217 24.4919 9.13253 22.8156 9.82689C19.9038 11.033 17.258 12.8008 15.0294 15.0294C12.8008 17.258 11.033 19.9038 9.82689 22.8156C9.13253 24.4919 8.63217 26.2376 8.33257 28.0185C7.96608 30.197 6.20914 32 4 32Z\"\nfill-rule=\"evenodd\"\nfill=\"currentColor\"\n/>\n</svg>", "@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n}\n\n.spinner_container {\n  display: inline-flex;\n  position: relative;\n  box-sizing: border-box;\n  align-items: center;\n  justify-content: center;\n\n}\n\n.spinner_background {\n  border-radius: 50%;\n  border: 2px solid;\n  &_extra-small {\n    border-width: 2px;\n    width: 16px;\n    height: 16px;\n    box-sizing: border-box;\n  }\n  &_small {\n    border-width: 4px;\n    width: 32px;\n    height: 32px;\n    box-sizing: border-box;\n  }\n  &_standard {\n    border-width: 8px;\n    width: 64px;\n    height: 64px;\n    box-sizing: border-box;\n  }\n  &_main {\n    border-color: $color-content-default;\n    opacity: 0.16;\n  }\n  &_light {\n    border-color: $color-content-bright;\n    opacity: 0.16;\n  }\n  &_content {\n    border-color: $color-surface-0;\n    opacity: 0.16;\n  }\n  &_positive {\n    border-color: $color-positive;\n    opacity: 0.16;\n  }\n  &_negative {\n    border-color: $color-negative;\n    opacity: 0.16;\n  }\n}\n\n.spinner_loading {\n  animation: rotate 0.5s linear infinite;\n  position: absolute;\n\n  &_extra-small {\n    width: 16px;\n    height: 16px;\n  }\n  &_small {\n    width: 32px;\n    height: 32px;\n  }\n  &_standard {\n    width: 64px;\n    height: 64px;\n  }\n  &_main {\n    color: $color-primary;\n  }\n  &_light {\n    color: $color-content-bright;\n  }\n  &_content {\n    color: $color-surface-0;\n  }\n  &_positive {\n    color: $color-positive;\n  }\n  &_negative {\n    color: $color-negative;\n  }\n}\n@keyframes rotate {\n  to {\n    transform: rotate(360deg);\n  }\n}\n", "import { Component, h, Host, Prop, State } from '@stencil/core';\nimport loadExtraSmall from '../../assets/svg/load-extra-small.svg';\nimport loadSmall from '../../assets/svg/load-small.svg';\nimport loadStandard from '../../assets/svg/load-standard.svg';\n\nexport type LoadingSpinnerVariant = 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'delete';\nexport type loadingSize = 'extra-small' | 'small' | 'standard';\nexport type colorsVariants = 'main' | 'light' | 'content' | 'positive' | 'negative';\n\nexport type LoadingSpinnerColorMap = { [key in LoadingSpinnerVariant]: string };\n\n@Component({\n  tag: 'bds-loading-spinner',\n  styleUrl: 'loading-spinner.scss',\n  shadow: true,\n})\nexport class BdsLoadingSpinner {\n  @State() private svgContent?: string;\n  /**\n   * \tSets the color of the spinner, can be 'primary', 'secondary' or 'ghost'\n   */\n  @Prop() variant: LoadingSpinnerVariant = 'primary';\n  /**\n   * Size, Entered as one of the size. Can be one of:\n   * 'small', 'standard', 'large'.\n   */\n  @Prop() size?: loadingSize = 'standard';\n  /**\n   * Color, Entered as one of the color. Can be one of:\n   * 'default', 'white'.\n   */\n  @Prop() color?: colorsVariants = 'main';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setSvgContent();\n  }\n\n  /**Function to transform the svg in a div element. */\n  formatSvg = (svgContent: string) => {\n    const div = document.createElement('div');\n    div.innerHTML = svgContent;\n    const svgElm = div.firstElementChild;\n\n    svgElm.removeAttribute('width');\n    svgElm.removeAttribute('height');\n    return div.innerHTML;\n  };\n\n  setSvgContent = () => {\n    const innerHTML =\n      this.size == 'extra-small'\n        ? loadExtraSmall\n        : this.size == 'small'\n          ? loadSmall\n          : this.size == 'standard' && loadStandard;\n\n    const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));\n    this.svgContent = this.formatSvg(svg);\n  };\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            spinner_container: true,\n            [`spinner_background_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          <div\n            class={{\n              spinner_background: true,\n              [`spinner_background_${this.size}`]: true,\n              [`spinner_background_${this.color}`]: true,\n            }}\n          ></div>\n          <div\n            class={{\n              spinner_loading: true,\n              [`spinner_loading_${this.size}`]: true,\n              [`spinner_loading_${this.color}`]: true,\n            }}\n            innerHTML={this.svgContent}\n          ></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["loadExtraSmall", "loadSmall", "loadStandard", "h", "Host"], "mappings": ";;;;AAAA,MAAM,iBAAiB,GAAG,w8BAAw8B;;ACAl+B,MAAM,YAAY,GAAG,o8BAAo8B;;ACAz9B,MAAM,eAAe,GAAG,g8BAAg8B;;ACAx9B,MAAM,iBAAiB,GAAG,k3DAAk3D;;MCgB/3D,iBAAiB,GAAA,MAAA;AAL9B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOE;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAA0B,SAAS;AAClD;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAiB,UAAU;AACvC;;;AAGG;AACK,QAAA,IAAK,CAAA,KAAA,GAAoB,MAAM;AAEvC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;;AAOhC,QAAA,IAAA,CAAA,SAAS,GAAG,CAAC,UAAkB,KAAI;YACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACzC,YAAA,GAAG,CAAC,SAAS,GAAG,UAAU;AAC1B,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB;AAEpC,YAAA,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC;AAC/B,YAAA,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAChC,OAAO,GAAG,CAAC,SAAS;AACtB,SAAC;AAED,QAAA,IAAa,CAAA,aAAA,GAAG,MAAK;AACnB,YAAA,MAAM,SAAS,GACb,IAAI,CAAC,IAAI,IAAI;AACX,kBAAEA;AACF,kBAAE,IAAI,CAAC,IAAI,IAAI;AACb,sBAAEC;sBACA,IAAI,CAAC,IAAI,IAAI,UAAU,IAAIC,eAAY;AAE/C,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AACvC,SAAC;AA+BF;IAxDC,iBAAiB,GAAA;QACf,IAAI,CAAC,aAAa,EAAE;;IA0BtB,MAAM,GAAA;QACJ,QACEC,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,iBAAiB,EAAE,IAAI;AACvB,gBAAA,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AAC1C,aAAA,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAExBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,kBAAkB,EAAE,IAAI;AACxB,gBAAA,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACzC,gBAAA,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;AAC3C,aAAA,EACI,CAAA,EACPA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;AACrB,gBAAA,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACtC,gBAAA,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;aACxC,EACD,SAAS,EAAE,IAAI,CAAC,UAAU,EACrB,CAAA,CACH,CACD;;;;;;;"}