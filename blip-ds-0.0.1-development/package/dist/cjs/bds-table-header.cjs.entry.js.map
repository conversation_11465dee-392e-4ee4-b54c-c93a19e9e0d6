{"file": "bds-table-header.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,cAAc,GAAG,sHAAsH;;MCOhI,WAAW,GAAA,MAAA;;;;IACtB,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/table/table-header/table-header.scss?tag=bds-table-header&encapsulation=scoped", "src/components/table/table-header/table-header.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-header-group;\n  border-bottom: 1px solid $color-border-1;\n}", "import { Component, h, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-header',\n  styleUrl: 'table-header.scss',\n  scoped: true,\n})\nexport class TableHeader {\n  render(): HTMLElement {\n    return (\n      <Host>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "version": 3}