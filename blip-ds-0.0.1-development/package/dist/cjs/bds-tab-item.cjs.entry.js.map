{"file": "bds-tab-item.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,sKAAsK;;MCO5K,UAAU,GAAA,MAAA;AALvB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAME;;AAEG;AAC4C,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AAC5E;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,IAAI;AAC7B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC5B;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,MAAM;AACtC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,SAAS;AACtC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAa,KAAK;AAC/B;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAY,QAAQ;AACtC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAY,QAAQ;AACtC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AACjC;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAa,KAAK;AACxC;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,MAAM;AACvC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;AACnC;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAa,KAAK;AAC/B;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;AACnC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AACpC;;AAEG;AAC4C,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;AACrE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAwBjC;IArBC,MAAM,YAAY,CAAC,MAAM,EAAA;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,MAAM;;IAK7B,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;;IAG5E,MAAM,GAAA;QACJ,QACEA,OAAC,CAAAC,UAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,CAAS,OAAA,CAAA,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,EAAA,EACrED,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAA,WAAA,EAAa,IAAI,CAAC,QAAQ,EAAA,EACtDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAwB,sBAAA,CAAA,GAAG,IAAI,CAAC,IAAI,EAAE,EAAA,EAC3EA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,CACF,CACD;;;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/tabs/tab-item/tab-item.scss?tag=bds-tab-item&encapsulation=shadow", "src/components/tabs/tab-item/tab-item.tsx"], "sourcesContent": [":host {\n  display: none;\n}\n\n:host(.is-open) {\n  display: block;\n  height: 100%;\n}\n\n.tab_item {\n  height: 100%;\n  &_content {\n    display: none;\n    height: 100%;\n    &--open {\n      display: block;\n    }\n  }\n}\n", "import { Component, h, Host, Prop, Method, Watch, Event, EventEmitter } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab-item',\n  styleUrl: 'tab-item.scss',\n  shadow: true,\n})\nexport class BdsTabItem {\n  /**\n   * Use to set number of tabItem.\n   */\n  @Prop({ mutable: true, reflect: true }) public numberElement?: number = null;\n  /**\n   * The text to be shown at the Tab item.\n   */\n  @Prop() label?: string = null;\n  /**\n   * The icon to be shown at the Tab item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * The position of the icon at the Tab item ('left', 'right').\n   */\n  @Prop() iconPosition?: string = 'left';\n  /**\n   * The theme of the icon at the Tab item ('solid', 'outline', 'emoji', 'logos').\n   */\n  @Prop() iconTheme?: string = 'outline';\n  /**\n   * The shape of the badge to be shown at the Tab item ('circle', 'square', 'triangle', 'triangle-reverse', 'polygon').\n   */\n  @Prop() badge?: boolean = false;\n  /**\n   * The shape of the badge to be shown at the Tab item ('circle', 'square', 'triangle', 'triangle-reverse', 'polygon').\n   */\n  @Prop() badgeShape?: string = 'circle';\n  /**\n   * The color of the badge to be shown at the Tab item.\n   */\n  @Prop() badgeColor?: string = 'system';\n  /**\n   * The icon to be shown inside the badge at the Tab item ('system', 'danger', 'warning', 'success', 'neutral')\n   */\n  @Prop() badgeIcon?: string = null;\n  /**\n   * The animation of the badge to be shown at the Tab item.\n   */\n  @Prop() badgeAnimation?: boolean = false;\n  /**\n   * The animation of the badge to be shown at the Tab item.\n   */\n  @Prop() badgePosition?: string = 'left';\n  /**\n   * The number to be shown inside the badge at the Tab item.\n   */\n  @Prop() badgeNumber?: number = null;\n  /**\n   * Prop for disable the especific tab.\n   */\n  @Prop({ mutable: true, reflect: true }) disable?: boolean = false;\n  /**\n   * Prop to indicate an error state for the tab.\n   */\n  @Prop() error?: boolean = false;\n  /**\n   * Inline styles to be applied to the tab group header element.\n   */\n  @Prop() headerStyle?: string = null;\n  /**\n   * Inline styles to be applied to the tab group content element.\n   */\n  @Prop() contentStyle?: string = null;\n  /**\n   * Used to open/close the Tab item.\n   */\n  @Prop({ mutable: true, reflect: true }) public open?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n  @Event() tabDisabled: EventEmitter;\n\n  @Watch('disable')\n  disableChanged(): void {\n    this.tabDisabled.emit({ item: this.numberElement, disable: this.disable });\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ [`is-open`]: this.disable === true ? false : this.open }}>\n        <div class={{ tab_item: true }} data-test={this.dataTest}>\n          <div class={{ tab_item_content: true, [`tab_item_content--open`]: this.open }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}