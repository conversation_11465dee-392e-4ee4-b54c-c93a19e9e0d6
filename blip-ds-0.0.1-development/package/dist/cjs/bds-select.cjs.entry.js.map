{"file": "bds-select.entry.cjs.js", "mappings": ";;;;;AAAA,MAAM,SAAS,GAAG,snUAAsnU;;MCQ3nU,MAAM,GAAA,MAAA;AALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;AAaW,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;AAE7B,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAEf,QAAA,IAAI,CAAA,IAAA,GAAI,EAAE;AAEnB;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAC3C;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAkB/B;;AAEG;AACsB,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AACxC;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAsB1C;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;AAEnB;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AAEnC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AAClC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACqC,QAAA,IAAe,CAAA,eAAA,GAA+B,MAAM;AAE5F;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;;AAqHxB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAO,KAAU;AACzC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAI;AACxC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;AAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAClB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAE9B,SAAC;AAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAK,KAAY;;AAClC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;AACrE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;gBACxF,IAAI,cAAc,EAAE;AAClB,oBAAA,OAAO,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,KAAK;;;AAGrF,YAAA,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAE,SAAS,IAAG,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,SAAS,IAAI,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,MAAA,GAAA,MAAA,GAAH,GAAG,CAAE,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AACjE,SAAC;AAEO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAkB,KAAU;YAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;AACT,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;YAClB,IAAI,CAAC,MAAM,EAAE;AACf,SAAC;AAyKF;AAhVW,IAAA,aAAa,CAAC,MAAe,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;AACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;aAC9D;AACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;AAErE,QAAA,IAAI,MAAM;AACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;iBACzC;gBACL,IAAI,CAAC,oBAAoB,EAAE;;;IAKjC,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAE1C,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;QAG/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;AAItC,IAAA,YAAY,CAAC,EAAS,EAAA;AACpB,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,EAAE;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAoB,KAAK,OAAO,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE;AAC5D,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;IAIvB,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;QACrC,IAAI,CAAC,QAAQ,GAAGA,+BAAe,CAAC,IAAI,CAAC,EAAE,CAAC;;IAG1C,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE;QACpC,IAAI,CAAC,gBAAgB,EAAE;;IAGzB,gBAAgB,GAAA;QACd,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;aACzC;YACL,IAAI,CAAC,oBAAoB,EAAE;;;AAIvB,IAAA,mBAAmB,CAAC,KAAgC,EAAA;AAC1D,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;IAIlC,oBAAoB,GAAA;QAC1B,MAAM,aAAa,GAAGC,uCAAuB,CAAC;YAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;YACtB,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;AACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;YAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;IAK1C,cAAc,GAAA;QACZ,IAAI,CAAC,aAAa,EAAE;;IAGd,gBAAgB,GAAA;AACtB,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;YAC7C,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;QAEzD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;IAG9B,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;iBAC1C;AACL,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO;;;;AAKzC,IAAA,IAAY,YAAY,GAAA;QACtB,OAAO,IAAI,CAAC;AACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;AACrE,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;;AAG/D,IAAA,IAAY,mBAAmB,GAAA;QAC7B,OAAO,IAAI,CAAC;cACR,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ;cACrG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC;;AA2DzF,IAAA,eAAe,CAAC,KAAK,EAAA;;AAC3B,QAAA,QAAQ,KAAK,CAAC,GAAG;AACf,YAAA,KAAK,OAAO;gBACV,IAAI,CAAC,MAAM,EAAE;gBACb;AACF,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;AAEpB,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,WAA0C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;oBACxF;;AAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,iBAAgD,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;gBAC7E;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,mBAAmB,CAAC,eAA8C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;oBAC5F;;AAEF,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,EAAA,GAAA,IAAI,CAAC,EAAE,CAAC,gBAA+C,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,KAAK;gBAC5E;;;IAIE,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACPC,OAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAEDA,OAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACRA,OAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACEA,iBAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvCA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/BA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACNA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;IAGlB,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;QAElD,QACEA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,QAAQ,EAAA,EACjBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAiB,eAAA,EAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAC/EA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;aAC5B,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClBA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC7CA,OAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,KAAK,EAAE,IAAI,CAAC,IAAI,EAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,QAAQ,EACG,IAAA,EAAA,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CACnC,CACL,CACF,EACNA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvBA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAA,CAAY,CACjF,EACL,IAAI,CAAC,OAAO,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;gBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;AACrC,aAAA,EACD,IAAI,EAAC,aAAa,EAAA,EAEjB,IAAI,CAAC,eAAe,IACnB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,KACnC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,IAC7BA,OAAA,CAAA,mBAAA,EAAA,EACE,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,MAAM,CAAC,KAAK,EAAA,YAAA,EACP,MAAM,CAAC,SAAS,EAChB,YAAA,EAAA,MAAM,CAAC,SAAS,EAC5B,UAAU,EAAE,MAAM,CAAC,UAAU,EAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAA,EAEpB,MAAM,CAAC,IAAI,KACVA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,YAAY,EAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC,QAAQ,EAAC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAA,CAAa,CAClG,EACA,MAAM,CAAC,KAAK,CACK,KAEpBA,OAAmB,CAAA,mBAAA,EAAA,EAAA,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EACnG,EAAA,MAAM,CAAC,KAAK,CACK,CACrB,CACF,KAEDA,qBAAQ,CACT,CACG,CACF;;;;;;;;;;;;;", "names": ["getScrollParent", "positionAbsoluteElement", "h"], "sources": ["src/components/selects/select.scss?tag=bds-select&encapsulation=shadow", "src/components/selects/select/select.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Watch, Element, Listen } from '@stencil/core';\nimport { Option, SelectChangeEventDetail, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\n@Component({\n  tag: 'bds-select',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class Select {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() isOpen? = false;\n\n  @State() text? = '';\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalOptions: Option[];\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop() options?: string | Option[];\n\n  /**\n   * the value of the select.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  @Prop({ mutable: true }) value?: any | null;\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEventDetail>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    this.bdsChange.emit({ value: this.value });\n\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n\n    this.text = this.getText(this.value);\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true, capture: true })\n  handleWindow(ev: Event) {\n    const path = ev.composedPath();\n    if (!path.find((element: HTMLElement) => element == this.el)) {\n      this.isOpen = false;\n    }\n  }\n\n  componentWillLoad() {\n    this.options && this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  componentWillRender() {\n    this.options && this.updateOptions();\n    this.getValueSelected();\n  }\n\n  componentDidLoad() {\n    this.getValueSelected();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  @Watch('options')\n  optionsChanged() {\n    this.updateOptions();\n  }\n\n  private getValueSelected() {\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n      option.addEventListener('optionSelected', this.handler);\n    }\n    this.text = this.getText(this.value);\n  }\n\n  private updateOptions() {\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        this.internalOptions = JSON.parse(this.options);\n      } else {\n        this.internalOptions = this.options;\n      }\n    }\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option'))\n      : Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private get childOptionSelected(): HTMLBdsSelectOptionElement {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option')).find((option) => option.selected)\n      : Array.from(this.el.querySelectorAll('bds-select-option')).find((option) => option.selected);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private refNativeInput = (el: any): void => {\n    this.nativeInput = el;\n  };\n\n  private refDropdown = (el: HTMLElement) => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    this.isOpen = true;\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  };\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private getText = (value): string => {\n    const opt = this.childOptions.find((option) => option.value == value);\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.titleText ? internalOption.titleText : internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt?.titleText : (opt?.innerText ?? '');\n  };\n\n  private handler = (event: CustomEvent): void => {\n    const {\n      detail: { value },\n    } = event;\n    this.value = value;\n    this.toggle();\n  };\n\n  private keyPressWrapper(event) {\n    switch (event.key) {\n      case 'Enter':\n        this.toggle();\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.nextSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.firstElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n      case 'ArrowUp':\n        if (this.childOptionSelected) {\n          this.value = (this.childOptionSelected.previousSibling as HTMLBdsSelectOptionElement)?.value;\n          return;\n        }\n        this.value = (this.el.lastElementChild as HTMLBdsSelectOptionElement)?.value;\n        break;\n    }\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n\n    return (\n      <div class=\"select\">\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n            part=\"input-container\"\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                <input\n                  ref={this.refNativeInput}\n                  class={{ input__container__text: true }}\n                  onFocus={this.onFocus}\n                  onBlur={this.onBlur}\n                  value={this.text}\n                  disabled={this.disabled}\n                  placeholder={this.placeholder}\n                  readonly\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper.bind(this)}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n          role=\"application\"\n        >\n          {this.internalOptions ? (\n            this.internalOptions.map((option, idx) =>\n              option.icon || option.titleText ? (\n                <bds-select-option\n                  key={idx}\n                  value={option.value}\n                  title-text={option.titleText}\n                  slot-align={option.slotAlign}\n                  bulkOption={option.bulkOption}\n                  status={option.status}\n                >\n                  {option.icon && (\n                    <bds-icon slot=\"input-left\" name={option.icon} size=\"medium\" color={option.iconColor}></bds-icon>\n                  )}\n                  {option.label}\n                </bds-select-option>\n              ) : (\n                <bds-select-option key={idx} value={option.value} bulkOption={option.bulkOption} status={option.status}>\n                  {option.label}\n                </bds-select-option>\n              ),\n            )\n          ) : (\n            <slot />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "version": 3}