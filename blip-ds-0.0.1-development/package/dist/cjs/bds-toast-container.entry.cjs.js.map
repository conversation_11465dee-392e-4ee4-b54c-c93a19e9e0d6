{"version": 3, "file": "bds-toast-container.entry.cjs.js", "sources": ["src/components/toast-container/toast-container.scss?tag=bds-toast-container&encapsulation=scoped", "src/components/toast-container/toast-container.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$vertical-margin: 48px;\n$horizontal-margin: 48px;\n\n:host {\n  position: fixed;\n  display: flex;\n  flex-direction: column;\n  z-index: $zindex-toast;\n  width: 456px;\n  \n  &.bottom-right {\n    bottom: $vertical-margin;\n    right: $horizontal-margin\n  }\n  \n  &.bottom-left {\n    bottom: $vertical-margin;\n    left: $horizontal-margin;\n  }\n\n  &.top-right {\n    top: 24px;\n    right: 24px;\n  }\n  \n  &.top-left {\n    top: $vertical-margin;\n    left: $horizontal-margin;\n  }\n\n  @media (max-width: $sm-screen) {\n    right: 0px;\n    left: 0px;\n    width: 100%;\n\n    &.top-left,\n    &.top-right{\n      top: 20px;\n    }\n\n    &.bottom-left,\n    &.bottom-right{\n      bottom: 20px;\n    }\n  }\n}", "import { Component, h, Host } from '@stencil/core';\n\n@Component({\n  tag: 'bds-toast-container',\n  styleUrl: 'toast-container.scss',\n  scoped: true,\n})\nexport class BdsToastContainer {\n  render() {\n    return (\n      <Host>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,iBAAiB,GAAG,2nBAA2nB;;MCOxoB,iBAAiB,GAAA,MAAA;;;;IAC5B,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACR;;;;;;;"}