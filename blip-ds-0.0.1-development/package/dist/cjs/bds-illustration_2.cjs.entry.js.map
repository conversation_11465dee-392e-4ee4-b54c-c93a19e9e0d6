{"file": "bds-illustration.bds-skeleton.entry.cjs.js", "mappings": ";;;;;;;;;AAAA,MAAM,eAAe,GAAG,kIAAkI;;MCU7I,eAAe,GAAA,MAAA;AAN5B,IAAA,WAAA,CAAA,OAAA,EAAA;;AASE;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAqB,SAAS;AAW1C;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;;AAOhC,QAAA,IAAsB,CAAA,sBAAA,GAAG,MAAK;AAC5B,YAAA,MAAM,aAAa,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AAC9E,YAAA,MAAM,MAAM,GAAG,CAA4C,yCAAA,EAAA,aAAa,CAA6B,0BAAA,EAAA,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,OAAO;YAClI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAC1B,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAI;AAC5B,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAA,IAAA,CAAM,CAAC;aACvE,CAAC,CACH;AACH,SAAC;AAuBF;IApCC,iBAAiB,GAAA;QACf,IAAI,CAAC,sBAAsB,EAAE;;IAc/B,MAAM,GAAA;AACJ,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,IAAI,EAAC,KAAK,EACV,KAAK,EAAE;AACL,gBAAA,kBAAkB,EAAE,IAAI;AACzB,aAAA,EAEA,EAAA,IAAI,CAAC,mBAAmB,IACvBD,iBACE,SAAS,EAAE,KAAK,EAChB,GAAG,EAAE,CAAA,0BAAA,EAA6B,IAAI,CAAC,mBAAmB,CAAA,CAAE,EAC5D,GAAG,EAAE,IAAI,CAAC,GAAG,EAAA,WAAA,EACF,IAAI,CAAC,QAAQ,GACxB,KAEFA,OAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,SAAS,eAAY,IAAI,CAAC,QAAQ,EAAQ,CAAA,CACtD,CACI;;;;;;ACjEb,MAAM,WAAW,GAAG,8kCAA8kC;;MCSrlC,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAMU,QAAA,IAAK,CAAA,KAAA,GAAW,QAAQ;AACxB,QAAA,IAAM,CAAA,MAAA,GAAY,MAAM;AACxB,QAAA,IAAK,CAAA,KAAA,GAAY,MAAM;AAE/B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA+BjC;IA7BC,MAAM,GAAA;QACJ,QACEA,OAAA,CAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,QAAQ,EAAE,UAAU;AACpB,gBAAA,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;AACnB,gBAAA,YAAY,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;aACtD,EAAA,EAEDD,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAC,IAAI,EAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,EAAE,EAAa,CAAA,EAClGA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,QAAQ,EAAE,UAAU;AACpB,gBAAA,YAAY,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACrD,gBAAA,QAAQ,EAAE,QAAQ;aACnB,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAExBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,WAAW,GAAO,CACzB,CACD;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/illustration/illustration.scss?tag=bds-illustration&encapsulation=shadow", "src/components/illustration/illustration.tsx", "src/components/skeleton/skeleton.scss?tag=bds-skeleton&encapsulation=shadow", "src/components/skeleton/skeleton.tsx"], "sourcesContent": [":host {\n  .illustration {\n    display: flex;\n    height: 100%;\n    width: auto;\n  }\n  \n}\n\n:host(.bds-illustration) {\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n", "import { Component, h, Host, Prop, State } from '@stencil/core';\nimport { IllustrationType } from './illustration-interface';\nimport packageJson from '../../../package.json';\n\n@Component({\n  tag: 'bds-illustration',\n  assetsDirs: ['svg'],\n  styleUrl: 'illustration.scss',\n  shadow: true,\n})\nexport class BdsIllustration {\n  @State() private IllustrationContent?: string;\n\n  /**\n   * Specifies the type to use. Can be: 'default'.\n   */\n  @Prop() type: IllustrationType = 'default';\n  /**\n   * Specifies the name of illustration. Verify the names on illustration tokens.\n   */\n  @Prop() name: string;\n\n  /**\n   * Alternative text for the image.\n   */\n  @Prop() alt?: string;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  componentWillLoad() {\n    this.setIllustrationContent();\n  }\n\n  /**Function to map the svg and call the \"formatSvg\" function */\n  setIllustrationContent = () => {\n    const tokensVersion = packageJson.dependencies['blip-tokens'].replace('^', '');\n    const apiUrl = `https://cdn.jsdelivr.net/npm/blip-tokens@${tokensVersion}/build/json/illustrations/${this.type}/${this.name}.json`;\n    fetch(apiUrl).then((response) =>\n      response.json().then((data) => {\n        this.IllustrationContent = data[`asset-${this.type}-${this.name}-svg`];\n      }),\n    );\n  };\n\n  render(): HTMLElement {\n    return (\n      <Host\n        role=\"img\"\n        class={{\n          'bds-illustration': true,\n        }}\n      >\n        {this.IllustrationContent ? (\n          <img\n            draggable={false}\n            src={`data:image/svg+xml;base64,${this.IllustrationContent}`}\n            alt={this.alt}\n            data-test={this.dataTest}\n          />\n        ) : (\n          <div class=\"default\" data-test={this.dataTest}></div>\n        )}\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n.skeleton {\n  min-width: 8px;\n  min-height: 8px;\n  background-color: $color-content-default;\n  opacity: 0.16;\n  overflow: hidden;\n\n  &_shape {\n    &--circle {\n      border-radius: 50%;\n    }\n    &--square {\n      border-radius: 8px;\n    }\n  }\n}\n\n.animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    rgba(246, 246, 246, 0) 0%,\n    rgba(246, 246, 246, 0.56) 50%,\n    rgba(246, 246, 246, 0) 100%\n  );\n  mix-blend-mode: overlay;\n\n  animation: 2.5s ease-out infinite shine;\n}\n\n@keyframes shine {\n  0% {\n    transform: translateX(-100%);\n  }\n\n  20% {\n    transform: translateX(100%);\n  }\n\n  100% {\n    transform: translateX(100%);\n  }\n}\n", "import { Component, h, Host, Prop } from '@stencil/core';\n\nexport type Shape = 'circle' | 'square';\n\n@Component({\n  tag: 'bds-skeleton',\n  styleUrl: 'skeleton.scss',\n  shadow: true,\n})\nexport class Skeleton {\n  @Prop() shape?: Shape = 'square';\n  @Prop() height?: string = '50px';\n  @Prop() width?: string = '100%';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host\n        style={{\n          display: 'flex',\n          position: 'relative',\n          overflow: 'hidden',\n          width: this.width,\n          height: this.height,\n          borderRadius: this.shape === 'circle' ? '50%' : '8px',\n        }}\n      >\n        <bds-grid xxs=\"12\" class={{ skeleton: true, [`skeleton_shape--${this.shape}`]: true }}></bds-grid>\n        <div\n          style={{\n            display: 'flex',\n            width: '100%',\n            height: '100%',\n            position: 'absolute',\n            borderRadius: this.shape === 'circle' ? '50%' : '8px',\n            overflow: 'hidden',\n          }}\n          data-test={this.dataTest}\n        >\n          <div class=\"animation\"></div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}