{"file": "bds-modal.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,QAAQ,GAAG,+qDAA+qD;;MCQnrD,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAME;;AAEG;AAKI,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;AAE7B;;AAEG;AAKI,QAAA,IAAW,CAAA,WAAA,GAAa,IAAI;AAEnC;;AAEG;AAKI,QAAA,IAAI,CAAA,IAAA,GAAW,OAAO;AAE7B;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAa,IAAI;AAErC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,IAAI;AAEnC;;;AAGG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AAEjC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AA0B7B,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,KAAK,KAAI;AAC3B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE;gBACtE,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC;AAEO,QAAA,IAAgB,CAAA,gBAAA,GAAG,MAAW;AACpC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK;AACnB,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAK;AAC5B,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,IAAI,GAAG,KAAK;;AAErB,SAAC;AAiCF;AAlEC;;AAEG;AAEH,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI;;IAId,aAAa,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC1D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;;aAC/C;YACL,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC7D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;;;IAoBxD,MAAM,GAAA;QACJ,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;gBACnB,qBAAqB,EAAE,IAAI,CAAC,IAAI;AAChC,gBAAA,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACtC,aAAA,EAAA,EAEDA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,EAAE,EAAa,WAAA,EAAA,IAAI,CAAC,SAAS,EAAQ,CAAA,EACtGA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA,OAAA,EAAU,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI,EAAE,EAAA,EACvD,IAAI,CAAC,WAAW,KACfA,uEACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAC9B,QAAQ,EAAE,IAAI,CAAC,aAAa,GAC5B,CACH,EACA,IAAI,CAAC,IAAI,IAAI,OAAO,IAAIA,OAAa,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACrC,IAAI,CAAC,IAAI,KAAK,OAAO,KACpBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAA,CAAE,GAAG,IAAI,EAAE,EAAA,EACtDA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,CACP,CACG,CACF;;;;;;;;;;", "names": ["h"], "sources": ["src/components/modal/modal.scss?tag=bds-modal&encapsulation=shadow", "src/components/modal/modal.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.modal__dialog {\n  opacity: 0;\n  visibility: hidden;\n  width: 100%;\n  height: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: opacity 0.3s ease-in-out;\n  z-index: $zindex-modal-overlay;\n  display: none;\n\n  & .outzone {\n    position: absolute;\n    inset: 0;\n    background-color: $color-content-din;\n    opacity: 0.7;\n  }\n\n  &--dynamic {\n    overflow-y: auto;\n    padding-top: 40px;\n    padding-bottom: 40px;\n    height: -webkit-fill-available;\n  }\n\n  .modal {\n    position: relative;\n    margin: auto;\n    width: 592px;\n    height: 368px;\n    border-radius: 8px;\n    background: $color-surface-1;\n    box-shadow: $shadow-3;\n    padding: 32px;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n\n    &--dynamic {\n      height: auto;\n      width: auto;\n      max-width: 1000px;\n    }\n    .close-button {\n      position: relative;\n      color: $color-content-default;\n      align-self: flex-end;\n      margin-bottom: 16px;\n      cursor: pointer;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: -4px;\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n      &:focus-visible {\n        outline: none;\n\n        &::before {\n          border-color: $color-focus;\n        }\n      }\n    }\n\n    .slot {\n      &--dynamic {\n        flex: 1 1 auto;\n      }\n    }\n  }\n\n  &--open {\n    opacity: 1;\n    visibility: visible;\n    display: flex;\n  }\n}\n", "import { Component, ComponentInterface, h, Method, Event, EventEmitter, Prop, Watch } from '@stencil/core';\n\nexport type sizes = 'fixed' | 'dynamic';\n@Component({\n  tag: 'bds-modal',\n  styleUrl: 'modal.scss',\n  shadow: true,\n})\nexport class BdsModal implements ComponentInterface {\n  /**\n   * Used to open/close the modal\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public closeButton?: boolean = true;\n\n  /**\n   * Used to change the modal heights.\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public size?: sizes = 'fixed';\n\n  /**\n   * If true, the modal will close clicking outside the component.\n   */\n  @Prop() outzoneClose?: boolean = true;\n\n  /**\n   * If true, the modal will close keydown Enter.\n   */\n  @Prop() enterClose?: boolean = true;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtOutzone is the data-test to button close.\n   */\n  @Prop() dtOutzone?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n\n  /**\n   * Emitted when modal status has changed.\n   */\n  @Event() bdsModalChanged!: EventEmitter;\n\n  /**\n   * Can be used outside to open/close the modal\n   */\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Watch('open')\n  protected isOpenChanged(): void {\n    if (this.open) {\n      document.addEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'opened' });\n    } else {\n      document.removeEventListener('keydown', this.listener, false);\n      this.bdsModalChanged.emit({ modalStatus: 'closed' });\n    }\n  }\n\n  private listener = (event) => {\n    if (this.enterClose && (event.key == 'Enter' || event.key == 'Escape')) {\n      this.toggle();\n    }\n  };\n\n  private handleMouseClick = (): void => {\n    this.open = false;\n  };\n\n  private onClickOutzone = () => {\n    if (this.outzoneClose) {\n      this.open = false;\n    }\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          modal__dialog: true,\n          'modal__dialog--open': this.open,\n          [`modal__dialog--${this.size}`]: true,\n        }}\n      >\n        <div class={{ outzone: true }} onClick={() => this.onClickOutzone()} data-test={this.dtOutzone}></div>\n        <div class={{ modal: true, [`modal--${this.size}`]: true }}>\n          {this.closeButton && (\n            <bds-icon\n              size=\"medium\"\n              class=\"close-button\"\n              name=\"close\"\n              tabindex=\"0\"\n              onClick={this.handleMouseClick}\n              dataTest={this.dtButtonClose}\n            />\n          )}\n          {this.size == 'fixed' && <slot></slot>}\n          {this.size !== 'fixed' && (\n            <div class={{ slot: true, [`slot--${this.size}`]: true }}>\n              <slot></slot>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "version": 3}