{"version": 3, "file": "bds-counter-text.entry.cjs.js", "sources": ["src/components/counter-text/counter-text-interface.ts", "src/components/counter-text/counter-text.scss?tag=bds-counter-text", "src/components/counter-text/counter-text.tsx"], "sourcesContent": ["export enum CounterTextState {\n  Default = 'default',\n  Warning = 'warning',\n  Delete = 'delete',\n}\n\nexport type CounterTextRule = {\n  max: number;\n  min: number;\n};\n", "@use '../../globals/helpers' as *;\n\n.counter-text {\n  background: $color-surface-2;\n  color: $color-content-disable;\n  box-sizing: content-box;\n  width: fit-content;\n  border-radius: 11px;\n  padding: 0 8px;\n\n  @include no-select;\n\n  &--active {\n    background: $color-system;\n    color: $color-content-din;\n  }\n\n  &--warning {\n    background: $color-warning;\n    color: $color-content-din;\n  }\n\n  &--delete {\n    background: $color-delete;\n    color: $color-content-bright;\n  }\n}", "import { Component, h, Prop } from '@stencil/core';\nimport { CounterTextRule, CounterTextState } from './counter-text-interface';\n\n@Component({\n  tag: 'bds-counter-text',\n  styleUrl: 'counter-text.scss',\n})\nexport class CounterText {\n  @Prop({ mutable: true }) length!: number;\n  @Prop() max?: number;\n  @Prop({ mutable: true }) active? = false;\n\n  @Prop({ mutable: true }) warning?: CounterTextRule = { max: 20, min: 2 };\n  @Prop({ mutable: true }) delete?: CounterTextRule = { max: 1, min: 0 };\n\n  getState(): string {\n    const actualLength = this.getActualLength();\n\n    if (actualLength >= this.warning.min && actualLength <= this.warning.max) {\n      return CounterTextState.Warning;\n    }\n\n    if (actualLength <= this.delete.max) {\n      return CounterTextState.Delete;\n    }\n\n    return CounterTextState.Default;\n  }\n\n  getActualLength(): number {\n    return this.max - this.length;\n  }\n\n  render(): HTMLElement {\n    const state = this.getState();\n    const actualLength = this.getActualLength();\n\n    return (\n      <div\n        class={{\n          'counter-text': true,\n          'counter-text--active': this.active,\n          [`counter-text--${state}`]: true,\n        }}\n      >\n        <bds-typo variant=\"fs-10\">{actualLength}</bds-typo>\n      </div>\n    );\n  }\n}\n"], "names": ["h"], "mappings": ";;;;AAAA,IAAY,gBAIX;AAJD,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,GAI3B,EAAA,CAAA,CAAA;;ACJD,MAAM,cAAc,GAAG,gwBAAgwB;;MCO1wB,WAAW,GAAA,MAAA;AAJxB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAO2B,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAEf,QAAA,IAAO,CAAA,OAAA,GAAqB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE;AAC/C,QAAA,IAAM,CAAA,MAAA,GAAqB,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;AAoCvE;IAlCC,QAAQ,GAAA;AACN,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;AAE3C,QAAA,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACxE,OAAO,gBAAgB,CAAC,OAAO;;QAGjC,IAAI,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACnC,OAAO,gBAAgB,CAAC,MAAM;;QAGhC,OAAO,gBAAgB,CAAC,OAAO;;IAGjC,eAAe,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;;IAG/B,MAAM,GAAA;AACJ,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC7B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE;QAE3C,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;gBACpB,sBAAsB,EAAE,IAAI,CAAC,MAAM;AACnC,gBAAA,CAAC,CAAiB,cAAA,EAAA,KAAK,CAAE,CAAA,GAAG,IAAI;AACjC,aAAA,EAAA,EAEDA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,OAAO,EAAA,EAAE,YAAY,CAAY,CAC/C;;;;;;;"}