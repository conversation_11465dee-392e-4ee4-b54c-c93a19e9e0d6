{"version": 3, "file": "bds-radio.bds-switch.entry.cjs.js", "sources": ["src/components/radio/radio.scss?tag=bds-radio&encapsulation=shadow", "src/components/radio/radio.tsx", "src/components/switch/switch.scss?tag=bds-switch&encapsulation=shadow", "src/components/switch/switch.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$radio-spacing-text: 8px;\n$radio-circle-width: 24px;\n$radio-circle-height: 24px;\n$radio-border-radius: 16px;\n$radio-circle-pointer-width: 10px;\n$radio-circle-pointer-height: 10px;\n\n:host {\n  display: flex;\n}\n.radio {\n  display: flex;\n  cursor: pointer;\n  display: inline-flex;\n  align-items: center;\n  flex-wrap: nowrap;\n\n  [type='radio'] {\n    display: none;\n    &:focus {\n      outline: 0;\n    }\n  }\n\n  &__circle {\n    @include animation();\n    @include flex-align-middle();\n    width: $radio-circle-width;\n    height: $radio-circle-height;\n    flex-shrink: 0;\n    border: 2px solid $color-content-default;\n    padding: 4px;\n    border-radius: 100%;\n    box-sizing: border-box;\n    background: transparent;\n    position: relative;\n\n    &__pointer {\n      @include animation();\n      border-radius: 100%;\n      background: transparent;\n      height: $radio-circle-pointer-height;\n      width: $radio-circle-pointer-width;\n    }\n\n    .hover {\n      width: 0;\n      height: 0;\n      opacity: 0;\n    }\n\n    .focus:focus-visible {\n      display: flex;\n      position: absolute;\n      border: 2px solid $color-focus;\n      border-radius: 4px;\n      padding: 4px;\n      width: 100%;\n      height: 100%;\n      outline: none;\n    }\n  }\n\n  &:hover {\n    border-color: $color-content-disable;\n    .hover {\n      display: flex;\n      background-color: $color-hover;\n      position: absolute;\n      width: 36px;\n      height: 36px;\n      opacity: 1;\n      border-radius: 24px;\n      transition:\n        width 0.2s,\n        height 0.2s;\n    }\n  }\n\n  &__text {\n    @include no-select();\n    padding-left: $radio-spacing-text;\n    color: $color-content-default;\n  }\n\n  /** State Checked */\n  &__input[type='radio']:checked ~ &__circle {\n    background: transparent;\n    border-color: $color-content-default;\n\n    .radio__circle__pointer {\n      background-color: $color-primary;\n    }\n\n    &:hover {\n      border-color: $color-content-default;\n\n      .radio__circle__pointer {\n        background-color: $color-primary;\n      }\n    }\n  }\n\n  /** State Disabled */\n  &__input[type='radio']:disabled ~ &__circle {\n    border-color: $color-content-disable;\n    background-color: $color-surface-3;\n\n    .radio__circle__pointer {\n      background-color: transparent;\n    }\n  }\n  &__input[type='radio']:disabled:hover ~ &__circle {\n    border-color: $color-content-disable;\n    background-color: $color-surface-3;\n\n    .radio__circle__pointer {\n      background-color: transparent;\n    }\n  }\n  &__input[type='radio']:disabled:checked ~ &__circle {\n    border-color: $color-content-disable;\n    background-color: $color-surface-3;\n\n    .radio__circle__pointer {\n      background-color: $color-content-default;\n    }\n  }\n  &__input[type='radio']:disabled:checked:hover ~ &__circle {\n    border-color: $color-content-disable;\n    background-color: $color-surface-3;\n\n    .radio__circle__pointer {\n      background-color: $color-content-default;\n    }\n  }\n\n  &__input[type='radio']:disabled ~ &__text {\n    color: $color-content-disable;\n    cursor: not-allowed;\n  }\n  &__input[type='radio']:disabled ~ &__circle {\n    cursor: not-allowed;\n  }\n}\n", "import { Component, h, Prop, Event, EventEmitter, Watch, State, Method, Host } from '@stencil/core';\n\nlet radioButtonIds = 0;\n@Component({\n  tag: 'bds-radio',\n  styleUrl: 'radio.scss',\n  shadow: true,\n})\nexport class Radio {\n  private nativeInput?: HTMLInputElement;\n\n  @State() radioId?: string;\n\n  /**\n   * Refer. Field to add refer in radio buttom.\n   */\n  @Prop() refer?: string;\n\n  /**\n   * label in radio, with he the input size increases.\n   */\n  @Prop() label?: string;\n\n  /**\n   * The value of the input.\n   */\n  @Prop() value!: string;\n\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name?: string;\n\n  /**\n   * If `true`, the checkbox is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n\n  /**\n   * If `true`, the user cannot interact with the checkbox.\n   */\n  @Prop() disabled?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsClickChange!: EventEmitter;\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChange.emit({ checked: isChecked });\n  }\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  private onClick = (event: Event): void => {\n    this.checked = true;\n    this.bdsClickChange.emit({ checked: this.checked });\n    event.stopPropagation();\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  connectedCallback(): void {\n    this.radioId = this.refer || `bds-radio-${radioButtonIds++}`;\n  }\n\n  private handleClickKey(event) {\n    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {\n      this.onClick(event);\n      event.preventDefault();\n      this.bdsClickChange.emit({ checked: this.checked });\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host>\n        <label class=\"radio\" htmlFor={this.radioId}>\n          <input\n            class=\"radio__input\"\n            type=\"radio\"\n            ref={this.refNativeInput}\n            id={this.radioId}\n            onClick={this.onClick}\n            disabled={this.disabled}\n            checked={this.checked}\n            value={this.value}\n            name={this.name}\n            data-test={this.dataTest}\n          />\n          <div class=\"radio__circle\">\n            {!this.disabled ? <div class=\"focus\" tabindex=\"0\" onKeyDown={this.handleClickKey.bind(this)}></div> : ''}\n            {!this.disabled ? <div class=\"hover\"></div> : ''}\n            <div class=\"radio__circle__pointer\"></div>\n          </div>\n\n          {this.label && (\n            <bds-typo class=\"radio__text\" variant=\"fs-14\" bold={this.checked ? 'bold' : 'regular'} tag=\"span\">\n              {this.label}\n            </bds-typo>\n          )}\n\n          <slot />\n        </label>\n      </Host>\n    );\n  }\n}\n", "@use '../../globals/helpers' as *;\n\n$switch-size-width-tall: 56px;\n$switch-size-width-standard: 42px;\n$switch-size-width-short: 32px;\n\n$switch-size-height-tall: 32px;\n$switch-size-height-standard: 24px;\n$switch-size-height-short: 18px;\n\n$switch-size-slider-tall: 24px;\n$switch-size-slider-standard: 18px;\n$switch-size-slider-short: 13.5px;\n\n$switch-size-translateX-tall: 24px;\n$switch-size-translateX-standard: 18px;\n$switch-size-translateX-short: 13.75px;\n\n$slider-size-content-tall: 4px;\n$slider-size-content-standard: 3px;\n$slider-size-content-short: 2.25px;\n\n@mixin translateX_values($value) {\n  -webkit-transform: translateX($value);\n  -ms-transform: translateX($value);\n  transform: translateX($value);\n}\n\n@mixin switch_width_height($width, $height) {\n  width: $width;\n  height: $height;\n}\n\n@mixin slider_width_height($value, $margin) {\n  position: absolute;\n  content: ' ';\n  left: $margin;\n  bottom: $margin;\n  top: $margin;\n  background-color: $color-content-bright;\n  -webkit-transition: 0.4s;\n  transition: 0.4s;\n  border-radius: 50%;\n  width: $value;\n  height: $value;\n}\n\n.switch {\n  position: relative;\n  display: inline-block;\n\n  &--size-tall {\n    @include switch_width_height($switch-size-width-tall, $switch-size-height-tall);\n  }\n  &--size-standard {\n    @include switch_width_height($switch-size-width-standard, $switch-size-height-standard);\n  }\n  &--size-short {\n    @include switch_width_height($switch-size-width-short, $switch-size-height-short);\n  }\n\n  .focus:focus-visible {\n    display: flex;\n    position: absolute;\n    border: 2px solid $color-focus;\n    border-radius: 4px;\n    width: 100%;\n    height: 100%;\n    top: -4px;\n    left: -4px;\n    padding-right: 4px;\n    padding-bottom: 4px;\n    outline: none;\n  }\n}\n\n.switch input {\n  opacity: 100;\n  width: 0;\n  height: 0;\n}\n\n.slider {\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: $color-content-ghost;\n  -webkit-transition: 0.4s;\n  transition: 0.4s;\n  border-radius: 34px;\n\n  &--size-tall::before {\n    @include slider_width_height($switch-size-slider-tall, $slider-size-content-tall);\n  }\n\n  &--size-standard::before {\n    @include slider_width_height($switch-size-slider-standard, $slider-size-content-standard);\n  }\n\n  &--size-short::before {\n    @include slider_width_height($switch-size-slider-short, $slider-size-content-short);\n  }\n\n  &--deselected-disabled {\n    cursor: not-allowed;\n    background-color: $color-content-ghost;\n    opacity: 0.5;\n  }\n}\n\ninput:checked + .slider {\n  background-color: $color-surface-primary;\n\n  &--selected-disabled {\n    cursor: not-allowed;\n    opacity: 0.5;\n  }\n}\n\ninput:focus + .slider {\n  box-shadow: 0 0 1px $color-primary-main;\n\n  &--selected-disabled {\n    box-shadow: 0 0 1px $color-neutral-medium-silver;\n  }\n\n  &--deselected-disabled {\n    box-shadow: 0 0 1px $color-neutral-medium-wave;\n  }\n}\n\ninput:checked + .slider {\n  &--size-tall::before {\n    @include translateX_values($switch-size-translateX-tall);\n  }\n\n  &--size-standard::before {\n    @include translateX_values($switch-size-translateX-standard);\n  }\n\n  &--size-short::before {\n    @include translateX_values($switch-size-translateX-short);\n  }\n}\n", "import { Component, h, Prop, State, Method, Event, EventEmitter, Watch } from '@stencil/core';\nexport type SwitchSize = 'tall' | 'standard' | 'short';\n\nlet switchIds = 0;\n\n@Component({\n  tag: 'bds-switch',\n  styleUrl: 'switch.scss',\n  shadow: true,\n})\nexport class Switch {\n  private nativeInput?: HTMLInputElement;\n  /**\n   * Component identifier.\n   */\n  @State() switchId?: string;\n  /**\n   * The refer of the control.\n   */\n  @Prop() refer!: string;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: SwitchSize = 'standard';\n  /**\n   * The name of the control, which is submitted with the form data.\n   */\n  @Prop() name!: string;\n\n  /**\n   * If `true`, the switch is selected.\n   */\n  @Prop({ mutable: true, reflect: true }) checked = false;\n\n  /**\n   * If `true`, the user cannot interact with the switch.\n   */\n  @Prop() disabled = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  connectedCallback(): void {\n    this.switchId = this.refer || `bds-switch-${switchIds++}`;\n  }\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChange.emit({\n      checked: isChecked,\n    });\n  }\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter;\n\n  @Method()\n  getInputElement(): Promise<HTMLInputElement> {\n    return Promise.resolve(this.nativeInput);\n  }\n\n  @Method()\n  getValue(): Promise<boolean> {\n    return Promise.resolve(this.nativeInput.checked);\n  }\n\n  private onClick = (): void => {\n    this.checked = !this.checked;\n  };\n\n  private refNativeInput = (input: HTMLInputElement): void => {\n    this.nativeInput = input;\n  };\n\n  getSizeClass(): string {\n    return `switch switch--size-${this.size} `;\n  }\n\n  getSizeSliderClass(): string {\n    return `slider slider--size-${this.size} round `;\n  }\n\n  private getStyleState = (): string => {\n    if (this.checked && !this.disabled) {\n      return 'slider--selected';\n    }\n\n    if (!this.checked && !this.disabled) {\n      return 'slider--deselected';\n    }\n\n    if (this.checked && this.disabled) {\n      return 'slider--selected-disabled';\n    }\n\n    if (!this.checked && this.disabled) {\n      return 'slider--deselected-disabled';\n    }\n\n    return '';\n  };\n\n  private handleClick = (ev) => {\n    if (!this.disabled) {\n      if (ev.key === 'Enter') {\n        this.checked = !this.checked;\n      }\n    }\n  };\n\n  render(): HTMLElement {\n    const sizeClass = this.getSizeClass();\n    const sizeSliderClass = this.getSizeSliderClass();\n    const styleState = this.getStyleState();\n\n    return (\n      <label class={{ [sizeClass]: true }}>\n        <div tabindex=\"0\" onKeyDown={(ev) => this.handleClick(ev)} class=\"focus\"></div>\n        <input\n          type=\"checkbox\"\n          ref={this.refNativeInput}\n          id={this.switchId}\n          name={this.name}\n          onClick={this.onClick}\n          checked={this.checked}\n          disabled={this.disabled}\n          data-test={this.dataTest}\n        ></input>\n        <span class={{ [sizeSliderClass]: true, [styleState]: true }}></span>\n      </label>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,QAAQ,GAAG,+7HAA+7H;;ACEh9H,IAAI,cAAc,GAAG,CAAC;MAMT,KAAK,GAAA,MAAA;AALlB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AA8BE;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAElC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA2BxB,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAY,KAAU;AACvC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;AACnB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YACnD,KAAK,CAAC,eAAe,EAAE;AACzB,SAAC;AAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAuB,KAAU;AACzD,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AAC1B,SAAC;AA+CF;AArEW,IAAA,cAAc,CAAC,SAAkB,EAAA;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;IAI7C,eAAe,GAAA;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;;IAI1C,QAAQ,GAAA;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;IAalD,iBAAiB,GAAA;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,CAAa,UAAA,EAAA,cAAc,EAAE,CAAA,CAAE;;AAGtD,IAAA,cAAc,CAAC,KAAK,EAAA;AAC1B,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClE,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACnB,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;;;IAIvD,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAA,EACxCA,OACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,OAAO,EACZ,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,OAAO,EAChB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,EACJ,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,CAAA,EACFA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACvB,CAAC,IAAI,CAAC,QAAQ,GAAGA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAAQ,GAAG,EAAE,EACvG,CAAC,IAAI,CAAC,QAAQ,GAAGA,OAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,OAAO,EAAA,CAAO,GAAG,EAAE,EAChDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,wBAAwB,EAAA,CAAO,CACtC,EAEL,IAAI,CAAC,KAAK,KACTA,uEAAU,KAAK,EAAC,aAAa,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,SAAS,EAAE,GAAG,EAAC,MAAM,EAC9F,EAAA,IAAI,CAAC,KAAK,CACF,CACZ,EAEDA,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACF,CACH;;;;;;;;AC7Hb,MAAM,SAAS,GAAG,4zEAA4zE;;ACG90E,IAAI,SAAS,GAAG,CAAC;MAOJ,MAAM,GAAA,MAAA;AALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAgBE;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAgB,UAAU;AAMtC;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;AAEvD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAExB;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA4BxB,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;AAC9B,SAAC;AAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAuB,KAAU;AACzD,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AAC1B,SAAC;AAUO,QAAA,IAAa,CAAA,aAAA,GAAG,MAAa;YACnC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,gBAAA,OAAO,kBAAkB;;YAG3B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnC,gBAAA,OAAO,oBAAoB;;YAG7B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,gBAAA,OAAO,2BAA2B;;YAGpC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,gBAAA,OAAO,6BAA6B;;AAGtC,YAAA,OAAO,EAAE;AACX,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAE,KAAI;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE;AACtB,oBAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO;;;AAGlC,SAAC;AAwBF;IA5FC,iBAAiB,GAAA;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,IAAI,CAAc,WAAA,EAAA,SAAS,EAAE,CAAA,CAAE;;AAIjD,IAAA,cAAc,CAAC,SAAkB,EAAA;AACzC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAClB,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC;;IASJ,eAAe,GAAA;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;;IAI1C,QAAQ,GAAA;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;IAWlD,YAAY,GAAA;AACV,QAAA,OAAO,CAAuB,oBAAA,EAAA,IAAI,CAAC,IAAI,GAAG;;IAG5C,kBAAkB,GAAA;AAChB,QAAA,OAAO,CAAuB,oBAAA,EAAA,IAAI,CAAC,IAAI,SAAS;;IA+BlD,MAAM,GAAA;AACJ,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE;AACrC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE;AACjD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;AAEvC,QAAA,QACEA,OAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,EAAE,EAAA,EACjCA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,EAAC,OAAO,EAAO,CAAA,EAC/EA,OAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,IAAI,EAAC,UAAU,EACf,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,EAAE,EAAE,IAAI,CAAC,QAAQ,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,EACjB,CAAA,EACTA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,KAAK,EAAE,EAAE,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC,UAAU,GAAG,IAAI,EAAE,EAAS,CAAA,CAC/D;;;;;;;;;;;"}