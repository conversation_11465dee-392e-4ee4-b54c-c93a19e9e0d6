{"version": 3, "file": "bds-tab.entry.cjs.js", "sources": ["src/components/tabs/tab (depreciated)/tab/tab.scss?tag=bds-tab", "src/components/tabs/tab (depreciated)/tab/tab.tsx"], "sourcesContent": ["@use '../../../../globals/helpers' as *;\n\n.bds-tab {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  box-sizing: content-box;\n  min-width: fit-content;\n  max-width: 270px;\n  height: 46px;\n  max-height: 48px;\n  \n  cursor: pointer;\n  text-align: center;\n  color: $color-content-disable;\n  border-bottom: 2px solid transparent;\n\n  &:not(:last-child) {\n    margin-right: 32px;\n  }\n\n  &:hover {\n    color: $color-content-default;\n  }\n\n  &--selected {\n    animation-name: selectFade;\n    animation-duration: 0.75s;\n    animation-fill-mode: forwards;\n  }\n\n  &__text {\n    min-width: 90px;\n    max-width: 270px;\n  }\n\n  @keyframes selectFade {\n    from{\n       border-bottom: 2px solid transparent;\n       color: $color-content-default;\n    }\n    to{\n       border-bottom: 2px solid $color-brand;\n      color: $color-content-default;\n    }\n  }\n}\n\n@media (max-width: 599px) {\n  .bds-tab {\n    min-width: 110px;\n    text-overflow: ellipsis;\n  }\n}\n", "import { Component, ComponentInterface, EventEmitter, Event, h, Prop, Host, Listen, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-tab',\n  styleUrl: 'tab.scss',\n})\nexport class Tab implements ComponentInterface {\n  /**\n   * Specifies the Tab group. Used to link it to the TabPanel.\n   */\n  @Prop() group!: string;\n\n  /**\n   * The text to be shown at the Tab\n   */\n  @Prop() label!: string;\n\n  /**\n   * Prop to control externally if a tab will be active by default\n   */\n  @Prop() active = false;\n\n  /**\n   * State to control if a tab is current active\n   */\n  @State() isActive = false;\n\n  /**\n   * Event to emmit when the active tab should be updated\n   */\n  @Event() bdsTabChange: EventEmitter;\n\n  @Listen('bdsTabChange', { target: 'body' })\n  @Listen('bdsTabInit', { target: 'body' })\n  handleTabChange(event: CustomEvent) {\n    this.isActive = event.detail == this.group;\n  }\n\n  async onClick() {\n    this.bdsTabChange.emit(this.group);\n  }\n\n  render(): HTMLElement {\n    const bold = this.isActive ? 'bold' : 'regular';\n    return (\n      <Host\n        class={{\n          'bds-tab': true,\n          ['bds-tab--selected']: this.isActive,\n        }}\n        onClick={this.onClick.bind(this)}\n      >\n        <div class=\"bds-tab__text\">\n          <bds-typo variant=\"fs-16\" bold={bold}>\n            {this.label}\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,MAAM,GAAG,szCAAszC;;MCMxzC,GAAG,GAAA,MAAA;AAJhB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAeE;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAG,KAAK;AAEtB;;AAEG;AACM,QAAA,IAAQ,CAAA,QAAA,GAAG,KAAK;AAmC1B;AA1BC,IAAA,eAAe,CAAC,KAAkB,EAAA;QAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK;;AAG5C,IAAA,MAAM,OAAO,GAAA;QACX,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;IAGpC,MAAM,GAAA;AACJ,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS;QAC/C,QACEA,OAAA,CAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EACH,KAAK,EAAE;AACL,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ;aACrC,EACD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAEhCD,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACxBA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAE,IAAI,EACjC,EAAA,IAAI,CAAC,KAAK,CACF,CACP,CACD;;;;;;;"}