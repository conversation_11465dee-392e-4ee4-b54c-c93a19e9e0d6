{"version": 3, "file": "bds-select-chips.entry.cjs.js", "sources": ["src/components/selects/select.scss?tag=bds-select-chips&encapsulation=shadow", "src/components/selects/select-chips/select-chips.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 8px 12px;\n$input-padding-label: 7px 4px 7px 12px;\n$input-width: 100%;\n$input_expanded: 100%;\n$input_fixed: 140px;\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 200px;\n\n:host {\n  display: block;\n}\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n    .input__icon {\n      .bds-icon {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  gap: 8px;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-negative;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-surface-positive;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-hover;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    padding: 2px;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n      width: 100%;\n      line-height: 22px;\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-ghost;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n}\n\n.select {\n  position: relative;\n  outline: none;\n\n  &__icon {\n    color: $color-content-ghost;\n    display: flex;\n  }\n\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-0;\n    width: 100%;\n    max-height: 200px;\n    height: fit-content;\n    position: absolute;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-3;\n    overflow-y: auto;\n    z-index: 2;\n    pointer-events: none;\n    opacity: 0;\n\n    &--open {\n      opacity: 1;\n      pointer-events: auto;\n    }\n\n    &--position-top {\n      bottom: calc(100% + 4px);\n    }\n\n    &--position-bottom {\n      top: calc(100% + 4px);\n    }\n  }\n}\n\n.inside-input-left {\n  display: inline-flex;\n  gap: 8px;\n  flex-wrap: wrap;\n  max-height: 200px;\n  overflow-y: auto;\n  @include custom-scroll;\n}\n.input-chips__chip {\n  margin: 2px 4px 2px 0px;\n}\n\n.input-chips__chips {\n  flex: 1;\n}\n", "import { Component, Element, h, Prop, Method, Event, EventEmitter, Listen, Watch, State } from '@stencil/core';\nimport { Option, SelectChangeEvent, SelectOptionsPositionType } from '../select-interface';\nimport { getScrollParent, positionAbsoluteElement } from '../../../utils/position-element';\nimport { emailValidation, whitespaceValidation } from '../../../utils/validations';\nimport { InputChipsTypes } from '../../input-chips/input-chips-interface';\n\n@Component({\n  tag: 'bds-select-chips',\n  styleUrl: '../select.scss',\n  shadow: true,\n})\nexport class SelectChips {\n  private nativeInput?: HTMLInputElement;\n  private dropElement?: HTMLElement;\n  private iconDropElement?: HTMLBdsIconElement;\n  private positionHeightDrop?: SelectOptionsPositionType;\n\n  @State() internalOptions: Option[];\n\n  @Element() el!: HTMLElement;\n\n  @State() isOpen? = false;\n\n  @State() intoView?: HTMLElement = null;\n\n  @State() selectedOptions: { label: string; value: any }[] = [];\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger?: boolean = false;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  @State() internalChips: string[] = [];\n\n  @State() selectedOption: number;\n\n  /**\n   * The options of the select\n   * Should be passed this way:\n   * options='[{\"value\": \"Cat\", \"label\": \"Meow\"}, {\"value\": \"Dog\", \"label\": \"Woof\"}]'\n   * Options can also be passed as child by using bds-select-option component, but passing as a child you may have some compatibility problems with Angular.\n   */\n  @Prop({ mutable: true }) options?: string | Option[];\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * Used for add prefix on new option select.\n   */\n  @Prop({ reflect: true }) newPrefix?: string = '';\n\n  /**\n   * the value of the select.\n   */\n  @Prop({ mutable: true }) value?: string | null = '';\n\n  /**\n   * Add state danger on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) danger? = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Set maximum length value for the chip content\n   */\n\n  @Prop() maxlength?: number;\n\n  /**\n   * Indicated to pass an feedback to user.\n   */\n  @Prop({ mutable: true }) errorMessage? = '';\n\n  /**\n   * Disabled input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   *  label in input, with he the input size increases.\n   */\n  @Prop() label? = '';\n\n  /**\n   * used for add icon in input left. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n\n  /**\n   * Do not accept duplicate chip elements.\n   */\n  @Prop() duplicated?: boolean = false;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() canAddNew?: boolean = true;\n\n  /**\n   *  Specify if is possible to create a new tag that is not on the options.\n   */\n  @Prop() notFoundMessage?: string = 'No results found';\n\n  /**\n   * Defining the type is important so that it is possible to carry out validations. Can be one of:\n   * 'text' and 'email;\n   */\n  @Prop() type: InputChipsTypes = 'text';\n\n  /**\n   * The delimiter is used to add multiple chips in the same string.\n   */\n  @Prop() delimiters? = /,|;/;\n\n  /**\n   * If `true`, the user cannot modify the value.\n   */\n  @Prop() disableSubmit = false;\n  /**\n   * Indicated to pass a help the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Prop to insert the name of the input\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * A tip for the user who can enter no controls.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Set the placement of the options menu. Can be 'bottom' or 'top'.\n   */\n  @Prop({ mutable: true, reflect: true }) optionsPosition?: SelectOptionsPositionType = 'auto';\n  /**\n   * Prop for set the height of the component.\n   */\n  @Prop() height?: string;\n  /**\n   * Prop for set the max height of the component.\n   */\n  @Prop() maxHeight?: string;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<SelectChangeEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsChangeChips!: EventEmitter;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSelectChipsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the chip has added.\n   */\n  @Event() bdsSubmit!: EventEmitter;\n\n  @Watch('isOpen')\n  protected isOpenChanged(isOpen: boolean): void {\n    if (this.positionHeightDrop == 'bottom') {\n      this.iconDropElement.name = this.isOpen ? 'arrow-up' : 'arrow-down';\n    } else {\n      this.iconDropElement.name = this.isOpen ? 'arrow-down' : 'arrow-up';\n    }\n    if (isOpen)\n      if (this.optionsPosition != 'auto') {\n        this.setDefaultPlacement(this.optionsPosition);\n      } else {\n        this.validatePositionDrop();\n      }\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('options')\n  protected optionsChanged(): void {\n    if (typeof this.options === 'string') {\n      try {\n        this.internalOptions = JSON.parse(this.options);\n      } catch (e) {}\n    } else {\n      this.internalOptions = this.options;\n    }\n  }\n\n  /**\n   * Call change event before alter chips values.\n   */\n  @Watch('chips')\n  protected valueChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        try {\n          this.internalChips = JSON.parse(this.chips);\n        } catch {\n          this.internalChips = [];\n        }\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('internalChips')\n  protected internalValueChanged(): void {\n    this.handleChangeChipsValue();\n\n    if (this.internalChips.length > 0) {\n      this.selectedOptions = this.internalChips.map((item) => {\n        return {\n          label: item,\n          value: `${this.validValueChip(item, this.childOptions)}`,\n        };\n      });\n    }\n  }\n\n  private validValueChip(value, internalOptions: HTMLBdsSelectOptionElement[]): string {\n    const selectOption = internalOptions?.find((option) => option.textContent == value);\n    return `${selectOption ? selectOption.value : value}`;\n  }\n\n  /**\n   * Return the validity of the input chips.\n   */\n  @Method()\n  async isValid(): Promise<boolean> {\n    return this.validateChips();\n  }\n\n  /**\n   * Return the chips\n   */\n  @Method()\n  async getChips(): Promise<string[]> {\n    return this.internalChips;\n  }\n\n  /**\n   * Clear all chips\n   */\n  @Method()\n  async clear(): Promise<void> {\n    this.internalChips = [];\n    this.value = '';\n  }\n\n  @Method()\n  async add(value: string): Promise<void> {\n    this.handleDelimiters();\n    if (value) {\n      this.setChip(value);\n    } else {\n      this.setChip(this.value);\n    }\n    this.value = '';\n  }\n\n  @Method()\n  async setFocus(): Promise<void> {\n    this.nativeInput.focus();\n  }\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.nativeInput.blur();\n  }\n\n  componentWillLoad() {\n    this.valueChanged();\n    this.optionsChanged();\n    this.intoView = getScrollParent(this.el);\n  }\n\n  async componentDidLoad() {\n    await this.resetFilterOptions();\n    if (this.optionsPosition != 'auto') {\n      this.setDefaultPlacement(this.optionsPosition);\n    } else {\n      this.validatePositionDrop();\n    }\n  }\n\n  private setDefaultPlacement(value: SelectOptionsPositionType) {\n    if (value == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  private validatePositionDrop() {\n    const positionValue = positionAbsoluteElement({\n      actionElement: this.el,\n      changedElement: this.dropElement,\n      intoView: this.intoView,\n    });\n    this.positionHeightDrop = positionValue.y as SelectOptionsPositionType;\n    if (positionValue.y == 'bottom') {\n      this.dropElement.classList.add('select__options--position-bottom');\n      this.iconDropElement.name = 'arrow-down';\n    } else {\n      this.dropElement.classList.add('select__options--position-top');\n      this.iconDropElement.name = 'arrow-up';\n    }\n  }\n\n  async connectedCallback() {\n    for (const option of this.childOptions) {\n      option.addEventListener('optionSelected', this.handler);\n    }\n  }\n\n  private get childOptionsEnabled(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(\n          this.el.shadowRoot.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'),\n        )\n      : Array.from(this.el.querySelectorAll('bds-select-option:not([invisible]):not(#option-add):not(#no-option)'));\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return this.options\n      ? Array.from(this.el.shadowRoot.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'))\n      : Array.from(this.el.querySelectorAll('bds-select-option:not(#option-add):not(#no-option)'));\n  }\n\n  private handleChangeChipsValue = async () => {\n    await this.resetFilterOptions();\n  };\n\n  private async filterOptions(term: string) {\n    if (!term) {\n      await this.resetFilterOptions();\n      return;\n    }\n\n    for (const option of this.childOptions) {\n      const isExistsChip = this.existsChip(option.textContent, await this.getChips());\n      const optionTextLower = option.textContent.toLowerCase();\n      const termLower = term.toLowerCase();\n\n      if (isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n\n      if (term && optionTextLower.includes(termLower) && !isExistsChip) {\n        option.removeAttribute('invisible');\n      }\n\n      if (term && !optionTextLower.includes(termLower) && !isExistsChip) {\n        option.setAttribute('invisible', 'invisible');\n      }\n    }\n  }\n\n  private async resetFilterOptions() {\n    for (const option of this.childOptions) {\n      if (this.existsChip(option.textContent, await this.getChips())) {\n        option.setAttribute('invisible', 'invisible');\n      } else {\n        option.removeAttribute('invisible');\n      }\n    }\n  }\n\n  private refDropdown = (el: HTMLElement): void => {\n    this.dropElement = el;\n  };\n\n  private refIconDrop = (el: HTMLBdsIconElement) => {\n    this.iconDropElement = el;\n  };\n\n  private existsChip(optionChip: string, chips: string[]) {\n    return chips.some((chip) => optionChip === chip);\n  }\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handler = async (event: CustomEvent) => {\n    const {\n      detail: { value },\n    } = event;\n    this.selectedOption = value;\n    const text = this.getText(value);\n    await this.addChip(text);\n\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n    this.toggle();\n  };\n\n  private handlerNewOption = async (text: string) => {\n    await this.addChip(text);\n    this.toggle();\n  };\n\n  private enableCreateOption(): boolean {\n    return !!(this.childOptionsEnabled.length === 0 && this.nativeInput && this.nativeInput.value);\n  }\n\n  private async addChip(chip: string) {\n    await this.setChip(chip);\n    this.nativeInput.value = '';\n  }\n\n  private getText = (value: string) => {\n    const el: HTMLBdsSelectOptionElement = this.childOptions.find((option) => option.value === value);\n    return this.getTextFromOption(el);\n  };\n\n  private getTextFromOption = (opt: HTMLBdsSelectOptionElement): string => {\n    if (this.internalOptions) {\n      const internalOption = this.internalOptions.find((option) => option.value == opt?.value);\n      if (internalOption) {\n        return internalOption.label;\n      }\n    }\n    return opt?.titleText ? opt.titleText : (opt?.textContent?.trim() ?? '');\n  };\n\n  private setFocusWrapper = (): void => {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private removeFocusWrapper = (): void => {\n    this.nativeInput.blur();\n  };\n\n  private validateChips() {\n    if (this.type === 'email') {\n      return !this.internalChips.some((chip) => !this.validateChip(chip));\n    } else {\n      return true;\n    }\n  }\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private handleOnBlur(): void {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  }\n\n  private onInput = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    if (input) {\n      this.value = input.value || '';\n    }\n    this.bdsSelectChipsInput.emit(ev);\n    this.changedInputValue();\n  };\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    switch (event.key) {\n      case 'Enter':\n        if (this.canAddNew !== false) {\n          this.handleDelimiters();\n          this.setChip(this.value);\n          this.value = '';\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowDown':\n        if (!this.disabled) {\n          this.isOpen = true;\n        }\n        break;\n      case 'ArrowUp':\n        if (!this.disabled) {\n          this.isOpen = false;\n        }\n        break;\n      case 'Backspace':\n      case 'Delete':\n        if ((this.value === null || this.value.length <= 0) && this.internalChips.length) {\n          this.removeLastChip();\n          this.handleChangeChipsValue;\n          this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n          this.bdsChange.emit({ data: this.selectedOptions });\n        }\n        break;\n    }\n  };\n\n  private verifyAndSubstituteDelimiters(value: string) {\n    if (value.length === 1 && value[0].match(this.delimiters)) {\n      return '';\n    }\n\n    let newValue = value.replace(/;/g, ',').replace(/\\,+|;+/g, ',');\n\n    if (newValue[0].match(this.delimiters)) {\n      newValue = newValue.substring(1);\n    }\n\n    return newValue;\n  }\n\n  private handleDelimiters() {\n    const value = this.nativeInput.value;\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word.trimStart());\n    });\n\n    this.clearInputValues();\n  }\n\n  private async handleChange(event: CustomEvent<{ value: string }>) {\n    this.changedInputValue;\n    const {\n      detail: { value },\n    } = event;\n\n    this.value = value ? value.trim() : '';\n\n    if (value.length === 0) return;\n\n    const existTerm = value.match(this.delimiters);\n    if (!existTerm) return;\n\n    const newValue = this.verifyAndSubstituteDelimiters(value);\n    if (!newValue) {\n      this.clearInputValues();\n      return;\n    }\n\n    const words = newValue.split(this.delimiters);\n    words.forEach((word) => {\n      this.setChip(word);\n    });\n\n    this.clearInputValues();\n  }\n\n  private changedInputValue = async () => {\n    this.value = this.nativeInput.value;\n\n    if (this.nativeInput.value) {\n      await this.filterOptions(this.nativeInput.value);\n    } else {\n      await this.resetFilterOptions();\n    }\n\n    if (this.value && this.isOpen === false) {\n      this.isOpen = true;\n    }\n  };\n\n  private clearInputValues(value = '') {\n    this.nativeInput.value = value;\n    this.value = value;\n  }\n\n  private setChip(name: string) {\n    if (!this.duplicated) {\n      const exists = this.internalChips.some((chip) => chip.toLowerCase() === name.toLowerCase());\n      if (exists) return;\n    }\n\n    if (!whitespaceValidation(name)) {\n      return;\n    }\n\n    this.internalChips = [...this.internalChips, name];\n  }\n\n  private validateChip(name: string) {\n    const trimmedName = name.trim();\n    if (this.type === 'email' && emailValidation(trimmedName)) {\n      return false;\n    }\n    return true;\n  }\n\n  private removeLastChip() {\n    this.internalChips = this.internalChips.slice(0, this.internalChips.length - 1);\n  }\n\n  private removeChip(event: CustomEvent<{ id: string }>) {\n    const {\n      detail: { id },\n    } = event;\n\n    this.internalChips = this.internalChips.filter((_chip, index) => index.toString() !== id);\n    this.bdsChangeChips.emit({ data: this.internalChips, value: this.selectedOption });\n    this.bdsChange.emit({ data: this.selectedOptions });\n  }\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable\n            id={id}\n            key={id}\n            color=\"outline\"\n            close={!this.disabled}\n            onChipClickableClose={(event) => this.removeChip(event)}\n          >\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable\n              id={id}\n              key={id}\n              color=\"outline\"\n              close={!this.disabled}\n              onChipClickableClose={(event) => this.removeChip(event)}\n            >\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n\n  private generateKey(value: string) {\n    return value.toLowerCase().replace(/ /g, '-');\n  }\n\n  render() {\n    const isPressed = this.isPressed && !this.disabled;\n\n    let internalOptions: Option[] = [];\n    if (this.options) {\n      if (typeof this.options === 'string') {\n        try {\n          internalOptions = JSON.parse(this.options);\n        } catch (e) {}\n      } else {\n        internalOptions = this.options;\n      }\n    }\n\n    return (\n      <div class=\"select\" tabindex=\"0\" onFocus={this.setFocusWrapper} onBlur={this.removeFocusWrapper}>\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null} onClick={this.toggle}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n          >\n            {this.renderIcon()}\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                {this.internalChips.length > 0 && (\n                  <span style={{ height: this.height, maxHeight: this.maxHeight }} class=\"inside-input-left\">\n                    {this.renderChips()}\n                  </span>\n                )}\n                <input\n                  ref={(input) => (this.nativeInput = input)}\n                  class={{ input__container__text: true }}\n                  name={this.inputName}\n                  maxlength={this.maxlength}\n                  placeholder={this.placeholder}\n                  onInput={this.onInput}\n                  onFocus={this.onFocus}\n                  onBlur={() => this.handleOnBlur()}\n                  onChange={() => this.handleChange}\n                  value={this.value}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                  onKeyDown={this.keyPressWrapper}\n                ></input>\n              </div>\n            </div>\n            <div class=\"select__icon\">\n              <bds-icon ref={(el) => this.refIconDrop(el)} size=\"small\" color=\"inherit\"></bds-icon>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          ref={(el) => this.refDropdown(el)}\n          class={{\n            select__options: true,\n            'select__options--open': this.isOpen,\n          }}\n        >\n          {internalOptions.map((option) => (\n            <bds-select-option\n              key={this.generateKey(option.value)}\n              onOptionSelected={this.handler}\n              value={option.value}\n              status={option.status}\n            >\n              {option.label}\n            </bds-select-option>\n          ))}\n          <slot />\n          {this.canAddNew === true && this.enableCreateOption() && (\n            <bds-select-option\n              id=\"option-add\"\n              value=\"add\"\n              onClick={() => this.handlerNewOption(this.nativeInput.value)}\n            >\n              {this.newPrefix}\n              {this.nativeInput.value}\n            </bds-select-option>\n          )}\n          {!this.canAddNew && this.enableCreateOption() && (\n            <bds-select-option id=\"no-option\" value=\"add\">\n              {this.notFoundMessage}\n            </bds-select-option>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": ["getScrollParent", "positionAbsoluteElement", "whitespaceValidation", "emailValidation", "index", "h"], "mappings": ";;;;;;AAAA,MAAM,SAAS,GAAG,snUAAsnU;;MCW3nU,WAAW,GAAA,MAAA;AALxB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;;AAeW,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAEf,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;AAE7B,QAAA,IAAe,CAAA,eAAA,GAAoC,EAAE;AAC9D;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAa,KAAK;AAC3C;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAEtB,QAAA,IAAa,CAAA,aAAA,GAAa,EAAE;AAYrC;;;;AAIG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAsB,EAAE;AAEtD;;AAEG;AACsB,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAEhD;;AAEG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;AAEnD;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AACvD;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAOjE;;AAEG;AACsB,QAAA,IAAY,CAAA,YAAA,GAAI,EAAE;AAE3C;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAE1C;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,EAAE;AAEnB;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAE3C;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAa,KAAK;AAEpC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAa,IAAI;AAElC;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,kBAAkB;AAErD;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAoB,MAAM;AAEtC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAI,KAAK;AAE3B;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAG,KAAK;AAC7B;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AACnC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAE/B;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACqC,QAAA,IAAe,CAAA,eAAA,GAA+B,MAAM;AAS5F;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAwNxB,QAAA,IAAsB,CAAA,sBAAA,GAAG,YAAW;AAC1C,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;AACjC,SAAC;AAqCO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAe,KAAU;AAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,WAAW,GAAG,CAAC,EAAsB,KAAI;AAC/C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AAC3B,SAAC;AAMO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAE9B,SAAC;AAEO,QAAA,IAAA,CAAA,OAAO,GAAG,OAAO,KAAkB,KAAI;YAC7C,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;AACT,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAChC,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAExB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;AAClF,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE;AACf,SAAC;AAEO,QAAA,IAAA,CAAA,gBAAgB,GAAG,OAAO,IAAY,KAAI;AAChD,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,MAAM,EAAE;AACf,SAAC;AAWO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAa,KAAI;AAClC,YAAA,MAAM,EAAE,GAA+B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AACjG,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;AACnC,SAAC;AAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,CAAC,GAA+B,KAAY;;AACtE,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAI,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,KAAK,CAAA,CAAC;gBACxF,IAAI,cAAc,EAAE;oBAClB,OAAO,cAAc,CAAC,KAAK;;;YAG/B,OAAO,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAE,SAAS,IAAG,GAAG,CAAC,SAAS,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,MAAA,GAAA,MAAA,GAAA,GAAG,CAAE,WAAW,0CAAE,IAAI,EAAE,MAAI,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AAC1E,SAAC;AAEO,QAAA,IAAe,CAAA,eAAA,GAAG,MAAW;AACnC,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAW;AACtC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;AACzB,SAAC;AAUO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC;AAOO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,EAAc,KAAU;AACzC,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;;AAEhC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,EAAE;AAC1B,SAAC;AAEO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;AACvD,YAAA,QAAQ,KAAK,CAAC,GAAG;AACf,gBAAA,KAAK,OAAO;AACV,oBAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;wBAC5B,IAAI,CAAC,gBAAgB,EAAE;AACvB,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,wBAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACf,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;AAClF,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;AAErD,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;oBAEpB;AACF,gBAAA,KAAK,WAAW;AACd,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;oBAEpB;AACF,gBAAA,KAAK,SAAS;AACZ,oBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,wBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;oBAErB;AACF,gBAAA,KAAK,WAAW;AAChB,gBAAA,KAAK,QAAQ;oBACX,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;wBAChF,IAAI,CAAC,cAAc,EAAE;AAErB,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;AAClF,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;oBAErD;;AAEN,SAAC;AAkEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,YAAW;YACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;AAEnC,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;gBAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;iBAC3C;AACL,gBAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;;YAGjC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;AACvC,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;AAEtB,SAAC;AAoPF;AArqBW,IAAA,aAAa,CAAC,MAAe,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,QAAQ,EAAE;AACvC,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;;aAC9D;AACL,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,GAAG,UAAU;;AAErE,QAAA,IAAI,MAAM;AACR,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;iBACzC;gBACL,IAAI,CAAC,oBAAoB,EAAE;;;AAKjC,IAAA,YAAY,CAAC,EAAS,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,MAA0B,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;IAKb,cAAc,GAAA;AACtB,QAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;AACpC,YAAA,IAAI;gBACF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;AAC/C,YAAA,OAAO,CAAC,EAAE;;aACP;AACL,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO;;;AAIvC;;AAEG;IAEO,YAAY,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAClC,gBAAA,IAAI;oBACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;gBAC3C,OAAA,EAAA,EAAM;AACN,oBAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;iBAEpB;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;;;aAE5B;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;IAKjB,oBAAoB,GAAA;QAC5B,IAAI,CAAC,sBAAsB,EAAE;QAE7B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;gBACrD,OAAO;AACL,oBAAA,KAAK,EAAE,IAAI;AACX,oBAAA,KAAK,EAAE,CAAA,EAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAE,CAAA;iBACzD;AACH,aAAC,CAAC;;;IAIE,cAAc,CAAC,KAAK,EAAE,eAA6C,EAAA;AACzE,QAAA,MAAM,YAAY,GAAG,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,MAAA,GAAA,MAAA,GAAA,eAAe,CAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,IAAI,KAAK,CAAC;AACnF,QAAA,OAAO,CAAG,EAAA,YAAY,GAAG,YAAY,CAAC,KAAK,GAAG,KAAK,EAAE;;AAGvD;;AAEG;AAEH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;AAG7B;;AAEG;AAEH,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa;;AAG3B;;AAEG;AAEH,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;IAIjB,MAAM,GAAG,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;aACd;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE1B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;;AAIjB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAI1B,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;IAGzB,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAGA,+BAAe,CAAC,IAAI,CAAC,EAAE,CAAC;;AAG1C,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;AAC/B,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC;;aACzC;YACL,IAAI,CAAC,oBAAoB,EAAE;;;AAIvB,IAAA,mBAAmB,CAAC,KAAgC,EAAA;AAC1D,QAAA,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;IAIlC,oBAAoB,GAAA;QAC1B,MAAM,aAAa,GAAGC,uCAAuB,CAAC;YAC5C,aAAa,EAAE,IAAI,CAAC,EAAE;YACtB,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,CAA8B;AACtE,QAAA,IAAI,aAAa,CAAC,CAAC,IAAI,QAAQ,EAAE;YAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAClE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,YAAY;;aACnC;YACL,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AAC/D,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,UAAU;;;AAI1C,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC;;;AAI3D,IAAA,IAAY,mBAAmB,GAAA;QAC7B,OAAO,IAAI,CAAC;AACV,cAAE,KAAK,CAAC,IAAI,CACR,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,qEAAqE,CAAC;AAE9G,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,qEAAqE,CAAC,CAAC;;AAGjH,IAAA,IAAY,YAAY,GAAA;QACtB,OAAO,IAAI,CAAC;AACV,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,oDAAoD,CAAC;AACtG,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,oDAAoD,CAAC,CAAC;;IAOxF,MAAM,aAAa,CAAC,IAAY,EAAA;QACtC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,MAAM,IAAI,CAAC,kBAAkB,EAAE;YAC/B;;AAGF,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/E,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE;AACxD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE;YAEpC,IAAI,YAAY,EAAE;AAChB,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;AAG/C,YAAA,IAAI,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE;AAChE,gBAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;AAGrC,YAAA,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE;AACjE,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;;;AAK3C,IAAA,MAAM,kBAAkB,GAAA;AAC9B,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE;AAC9D,gBAAA,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;;iBACxC;AACL,gBAAA,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC;;;;IAajC,UAAU,CAAC,UAAkB,EAAE,KAAe,EAAA;AACpD,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC;;IA2B1C,kBAAkB,GAAA;QACxB,OAAO,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;IAGxF,MAAM,OAAO,CAAC,IAAY,EAAA;AAChC,QAAA,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;;IA4BrB,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;aAC9D;AACL,YAAA,OAAO,IAAI;;;IAgBP,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAgDhB,IAAA,6BAA6B,CAAC,KAAa,EAAA;AACjD,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzD,YAAA,OAAO,EAAE;;AAGX,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;AAE/D,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACtC,YAAA,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;AAGlC,QAAA,OAAO,QAAQ;;IAGT,gBAAgB,GAAA;AACtB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;AACpC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;AAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE;QAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS;YAAE;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE;YACvB;;QAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AAChC,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE;;IAGjB,MAAM,YAAY,CAAC,KAAqC,EAAA;QAE9D,MAAM,EACJ,MAAM,EAAE,EAAE,KAAK,EAAE,GAClB,GAAG,KAAK;AAET,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE;AAEtC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE;QAExB,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS;YAAE;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE;YACvB;;QAGF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7C,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACpB,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE;;IAiBjB,gBAAgB,CAAC,KAAK,GAAG,EAAE,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK;AAC9B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;AAGZ,IAAA,OAAO,CAAC,IAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3F,YAAA,IAAI,MAAM;gBAAE;;AAGd,QAAA,IAAI,CAACC,gCAAoB,CAAC,IAAI,CAAC,EAAE;YAC/B;;QAGF,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC;;AAG5C,IAAA,YAAY,CAAC,IAAY,EAAA;AAC/B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAIC,2BAAe,CAAC,WAAW,CAAC,EAAE;AACzD,YAAA,OAAO,KAAK;;AAEd,QAAA,OAAO,IAAI;;IAGL,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGzE,IAAA,UAAU,CAAC,KAAkC,EAAA;QACnD,MAAM,EACJ,MAAM,EAAE,EAAE,EAAE,EAAE,GACf,GAAG,KAAK;QAET,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;AACzF,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;AAClF,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;;IAG7C,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AAC9B,YAAA,OAAO,EAAE;;QAGX,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAEC,OAAK,KAAI;AAC5C,YAAA,MAAM,EAAE,GAAGA,OAAK,CAAC,QAAQ,EAAE;YAC3B,MAAM,KAAK,GAAG,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;AACxB,gBAAA,QACEC,OACE,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAEtD,IAAI,CACc;;iBAElB;AACL,gBAAA,QACEA,OAAa,CAAA,aAAA,EAAA,EAAA,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,cAAA,EAAe,IAAI,EAAA,EAC5DA,OACE,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,EACrB,oBAAoB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAEtD,CAAG,EAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA,IAAA,CAAM,CACX,CACT;;AAGpB,SAAC,CAAC;;IAGI,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACPA,OAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAEDA,OAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACRA,OAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACEA,iBAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvCA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/BA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACNA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;AAGV,IAAA,WAAW,CAAC,KAAa,EAAA;QAC/B,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;;IAG/C,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;QAElD,IAAI,eAAe,GAAa,EAAE;AAClC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;AACpC,gBAAA,IAAI;oBACF,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;AAC1C,gBAAA,OAAO,CAAC,EAAE;;iBACP;AACL,gBAAA,eAAe,GAAG,IAAI,CAAC,OAAO;;;AAIlC,QAAA,QACEA,kEAAK,KAAK,EAAC,QAAQ,EAAC,QAAQ,EAAC,GAAG,EAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,IAAI,CAAC,kBAAkB,EAAA,EAC7FA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAA,eAAA,EAAiB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAA,EACrGA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;AAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAA,EAE3B,IAAI,CAAC,UAAU,EAAE,EAClBA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,KAC5BA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAC,mBAAmB,IACvF,IAAI,CAAC,WAAW,EAAE,CACd,CACR,EACDA,OACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,EACjC,QAAQ,EAAE,MAAM,IAAI,CAAC,YAAY,EACjC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAA,WAAA,EACZ,IAAI,CAAC,QAAQ,EACxB,SAAS,EAAE,IAAI,CAAC,eAAe,EACxB,CAAA,CACL,CACF,EACNA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACvBA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAA,CAAY,CACjF,EACL,IAAI,CAAC,OAAO,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,GAAG,CAC5F,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EACjC,KAAK,EAAE;AACL,gBAAA,eAAe,EAAE,IAAI;gBACrB,uBAAuB,EAAE,IAAI,CAAC,MAAM;AACrC,aAAA,EAAA,EAEA,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,MAC1BA,OAAA,CAAA,mBAAA,EAAA,EACE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EACnC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAC9B,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,MAAM,EAAE,MAAM,CAAC,MAAM,EAEpB,EAAA,MAAM,CAAC,KAAK,CACK,CACrB,CAAC,EACFA,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,EACP,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE,KACnDA,OAAA,CAAA,mBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,EAAE,EAAC,YAAY,EACf,KAAK,EAAC,KAAK,EACX,OAAO,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAA,EAE3D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,WAAW,CAAC,KAAK,CACL,CACrB,EACA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAC3CA,OAAA,CAAA,mBAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAmB,EAAE,EAAC,WAAW,EAAC,KAAK,EAAC,KAAK,EAAA,EAC1C,IAAI,CAAC,eAAe,CACH,CACrB,CACG,CACF;;;;;;;;;;;;;;"}