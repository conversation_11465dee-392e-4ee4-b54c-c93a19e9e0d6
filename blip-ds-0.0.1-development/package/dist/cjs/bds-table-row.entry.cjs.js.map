{"version": 3, "file": "bds-table-row.entry.cjs.js", "sources": ["src/components/table/table-row/table-row.scss?tag=bds-table-row&encapsulation=scoped", "src/components/table/table-row/table-row.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n  display: table-row;\n  height: 64px;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  border-bottom: 1px solid $color-border-2;\n\n  .collapse-body {\n    padding: 16px;\n    max-height: 100px;\n    text-align: left;\n    opacity: 1;\n    transition: all ease 0.5s;\n  }\n}\n\n:host:last-child {\n  border-bottom: none;\n}\n\n:host(.clickable--true):hover {\n  background-color: $color-hover;\n  border-bottom: 1px solid $color-border-2;\n  cursor: pointer;\n}\n\n:host(.clickable--true) {\n  border-bottom: none;\n}\n\n:host(.selected--true) {\n  border-radius: 8px;\n  outline: 2px solid $color-primary;\n  outline-offset: -1px;\n  border-bottom: none;\n}\n\n:host(.dense-row) {\n  height: auto;\n}\n\n:host(.collapse-body) {\n  height: fit-content;\n}  \n.arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(180deg);\n  }\n  .active {\n      transform: rotate(0deg);\n    }\n\n:host(.collapse) {\n  height: 0;\n\n  .collapse-body {\n    padding: 0;\n    max-height: 0;\n    opacity: 0;\n    overflow: hidden;\n    transition: all ease-in-out 0.5s;\n  }\n\n  th {\n      padding: 0;\n    }\n}\n", "import { Component, h, Host, Prop, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-row',\n  styleUrl: 'table-row.scss',\n  scoped: true,\n})\nexport class TableRow {\n  @Element() private element: HTMLElement;\n  @State() isDense = false;\n  @State() collapse: boolean;\n  @State() isCollapsed = true;\n  @State() colspanNumber: number = null;\n  @State() bdsTable: HTMLBdsTableElement;\n  @State() collapseRow: HTMLBdsTableRowElement;\n  /**\n   * Prop to make hover animation.\n   */\n  @Prop({ mutable: true, reflect: true }) clickable?: boolean = false;\n  /**\n   * Prop to highlight the row selected.\n   */\n  @Prop() selected?: boolean = false;\n  @Prop() bodyCollapse?: string;\n  @Prop() dataTarget?: string;\n\n  toggleCollapse = (target) => {\n    if (this.collapse) {\n      const body = document.querySelector(`[body-collapse=\"${target}\"]`);\n      body.classList.toggle('collapse');\n      this.isCollapsed = !this.isCollapsed;\n    }\n  };\n\n  componentWillLoad() {\n    this.bdsTable = this.element.closest('bds-table');\n    this.collapseRow = document.querySelector(`[body-collapse=\"${this.dataTarget}\"]`);\n    this.colspanNumber = document.querySelector(`bds-table-row`).children.length;\n\n    if (this.bdsTable && (this.bdsTable.getAttribute('dense-table') === 'true' || this.bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n    if (this.bdsTable && (this.bdsTable.getAttribute('collapse') === 'true' || this.bdsTable.collapse === true)) {\n      this.collapse = true;\n      this.clickable = true;\n    }\n\n    if (this.collapseRow) {\n      this.collapseRow.classList.add('collapse');\n      this.collapseRow.classList.add('collapse-body');\n    }\n  }\n\n  componentWillUpdate() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('dense-table') === 'true' || bdsTable.denseTable === true)) {\n      this.isDense = true;\n    }\n  }\n\n  render(): HTMLElement {\n    if (this.bodyCollapse) {\n      return (\n        <th colSpan={this.colspanNumber}>\n          <div class=\"collapse-body\">\n            <slot></slot>\n          </div>\n        </th>\n      );\n    } else {\n      const isFirstRow = this.element.closest('bds-table-header') === this.element.parentElement;\n      return (\n        <Host\n          class={{\n            host: true,\n            [`clickable--${this.clickable}`]: !isFirstRow && this.clickable === true ? true : false,\n            [`selected--${this.selected}`]: true,\n            'dense-row': this.isDense,\n          }}\n          onClick={() => this.toggleCollapse(this.dataTarget)}\n        >\n          {this.collapse && (\n            <bds-table-cell type=\"custom\">\n              {!isFirstRow && <bds-icon class={{ arrow: true, active: this.isCollapsed }} name=\"arrow-down\"></bds-icon>}\n            </bds-table-cell>\n          )}\n          <slot />\n        </Host>\n      );\n    }\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,WAAW,GAAG,k8CAAk8C;;MCOz8C,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOW,QAAA,IAAO,CAAA,OAAA,GAAG,KAAK;AAEf,QAAA,IAAW,CAAA,WAAA,GAAG,IAAI;AAClB,QAAA,IAAa,CAAA,aAAA,GAAW,IAAI;AAGrC;;AAEG;AACqC,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AACnE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAIlC,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,MAAM,KAAI;AAC1B,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAmB,gBAAA,EAAA,MAAM,CAAI,EAAA,CAAA,CAAC;AAClE,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW;;AAExC,SAAC;AA2DF;IAzDC,iBAAiB,GAAA;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA,gBAAA,EAAmB,IAAI,CAAC,UAAU,CAAA,EAAA,CAAI,CAAC;AACjF,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA,aAAA,CAAe,CAAC,CAAC,QAAQ,CAAC,MAAM;QAE5E,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;AAChH,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;QAErB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,EAAE;AAC3G,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC;;;IAInD,mBAAmB,GAAA;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;AAClD,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE;AACjG,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;;IAIvB,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,QACEA,OAAI,CAAA,IAAA,EAAA,EAAA,OAAO,EAAE,IAAI,CAAC,aAAa,EAAA,EAC7BA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACxBA,OAAa,CAAA,MAAA,EAAA,IAAA,CAAA,CACT,CACH;;aAEF;AACL,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;AAC1F,YAAA,QACEA,OAAA,CAACC,UAAI,EAAA,EACH,KAAK,EAAE;AACL,oBAAA,IAAI,EAAE,IAAI;oBACV,CAAC,CAAA,WAAA,EAAc,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvF,oBAAA,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;oBACpC,WAAW,EAAE,IAAI,CAAC,OAAO;AAC1B,iBAAA,EACD,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAA,EAElD,IAAI,CAAC,QAAQ,KACZD,4BAAgB,IAAI,EAAC,QAAQ,EAAA,EAC1B,CAAC,UAAU,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,EAAC,YAAY,EAAA,CAAY,CAC1F,CAClB,EACDA,OAAQ,CAAA,MAAA,EAAA,IAAA,CAAA,CACH;;;;;;;;;"}