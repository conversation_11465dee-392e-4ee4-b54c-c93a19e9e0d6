'use strict';

var index = require('./index-D_zq0Z7d.js');

const loadExtraSmallSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGgKZD0iTTEuNiA4QzAuNzE2MzQ1IDggLTAuMDE2MDg2OSA3LjI3NjY4IDAuMTU5NDY5IDYuNDEwNjRDMC4yNjEzOTMgNS45MDc4NCAwLjQxMTY4OSA1LjQxNDc5IDAuNjA4OTY0IDQuOTM4NTNDMS4wMTEgMy45Njc5MyAxLjYwMDI4IDMuMDg2MDEgMi4zNDMxNSAyLjM0MzE1QzMuMDg2MDIgMS42MDAyOCAzLjk2NzkzIDEuMDExIDQuOTM4NTMgMC42MDg5NjNDNS40MTQ4IDAuNDExNjg5IDUuOTA3ODQgMC4yNjEzOTMgNi40MTA2NCAwLjE1OTQ2OUM3LjI3NjY4IC0wLjAxNjA4NjYgOCAwLjcxNjM0NSA4IDEuNkM4IDIuNDgzNjUgNy4yNjQ0NyAzLjE3NTA4IDYuNDI5NDYgMy40NjQyMUM2LjMzOTgxIDMuNDk1MjUgNi4yNTA5OSAzLjUyODk4IDYuMTYzMTIgMy41NjUzOEM1LjU4MDc2IDMuODA2NiA1LjA1MTYxIDQuMTYwMTcgNC42MDU4OSA0LjYwNTg5QzQuMTYwMTcgNS4wNTE2MSAzLjgwNjYgNS41ODA3NiAzLjU2NTM4IDYuMTYzMTJDMy41Mjg5OCA2LjI1MDk5IDMuNDk1MjUgNi4zMzk4MSAzLjQ2NDIxIDYuNDI5NDZDMy4xNzUwOCA3LjI2NDQ3IDIuNDgzNjYgOCAxLjYgOFoiCmZpbGwtcnVsZT0iZXZlbm9kZCIKZmlsbD0iY3VycmVudENvbG9yIgovPgo8L3N2Zz4=';

const loadSmallSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGgKZD0iTTIgMTZDMC44OTU0MzEgMTYgLTAuMDEyODc1NCAxNS4xMDExIDAuMTI0ODM5IDE0LjAwNTJDMC4zMDI3MzYgMTIuNTg5NSAwLjY2OTU5NyAxMS4yMDA5IDEuMjE3OTMgOS44NzcwNkMyLjAyMiA3LjkzNTg1IDMuMjAwNTUgNi4xNzIwMyA0LjY4NjI5IDQuNjg2MjlDNi4xNzIwMyAzLjIwMDU1IDcuOTM1ODYgMi4wMjIgOS44NzcwNyAxLjIxNzkzQzExLjIwMDkgMC42Njk1OTYgMTIuNTg5NSAwLjMwMjczNCAxNC4wMDUyIDAuMTI0ODM4QzE1LjEwMTEgLTAuMDEyODc0NiAxNiAwLjg5NTQzIDE2IDJDMTYgMy4xMDQ1NyAxNS4wOTg1IDMuOTgzMDQgMTQuMDA5MiA0LjE2NjI4QzEzLjExODggNC4zMTYwOCAxMi4yNDYgNC41NjYyNyAxMS40MDc4IDQuOTEzNDRDOS45NTE4OSA1LjUxNjUgOC42MjkwMiA2LjQwMDQyIDcuNTE0NzIgNy41MTQ3MkM2LjQwMDQyIDguNjI5MDIgNS41MTY1IDkuOTUxODkgNC45MTM0NSAxMS40MDc4QzQuNTY2MjcgMTIuMjQ2IDQuMzE2MDggMTMuMTE4OCA0LjE2NjI4IDE0LjAwOTJDMy45ODMwNCAxNS4wOTg1IDMuMTA0NTcgMTYgMiAxNloiCmZpbGwtcnVsZT0iZXZlbm9kZCIKZmlsbD0iY3VycmVudENvbG9yIgovPgo8L3N2Zz4=';

const loadStandardSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGgKZD0iTTQgMzJDMS43OTA4NiAzMiAtMC4wMjU3NTA3IDMwLjIwMjMgMC4yNDk2NzcgMjguMDEwNEMwLjYwNTQ3MiAyNS4xNzg5IDEuMzM5MTkgMjIuNDAxNyAyLjQzNTg2IDE5Ljc1NDFDNC4wNDQwMSAxNS44NzE3IDYuNDAxMTEgMTIuMzQ0MSA5LjM3MjU5IDkuMzcyNThDMTIuMzQ0MSA2LjQwMTExIDE1Ljg3MTcgNC4wNDQgMTkuNzU0MSAyLjQzNTg1QzIyLjQwMTcgMS4zMzkxOSAyNS4xNzg5IDAuNjA1NDY5IDI4LjAxMDQgMC4yNDk2NzZDMzAuMjAyMyAtMC4wMjU3NDkyIDMyIDEuNzkwODYgMzIgNEMzMiA2LjIwOTE0IDMwLjE5NyA3Ljk2NjA4IDI4LjAxODUgOC4zMzI1N0MyNi4yMzc2IDguNjMyMTcgMjQuNDkxOSA5LjEzMjUzIDIyLjgxNTYgOS44MjY4OUMxOS45MDM4IDExLjAzMyAxNy4yNTggMTIuODAwOCAxNS4wMjk0IDE1LjAyOTRDMTIuODAwOCAxNy4yNTggMTEuMDMzIDE5LjkwMzggOS44MjY4OSAyMi44MTU2QzkuMTMyNTMgMjQuNDkxOSA4LjYzMjE3IDI2LjIzNzYgOC4zMzI1NyAyOC4wMTg1QzcuOTY2MDggMzAuMTk3IDYuMjA5MTQgMzIgNCAzMloiCmZpbGwtcnVsZT0iZXZlbm9kZCIKZmlsbD0iY3VycmVudENvbG9yIgovPgo8L3N2Zz4=';

const loadingSpinnerCss = ":host{display:block}.spinner_container{display:-ms-inline-flexbox;display:inline-flex;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.spinner_background{border-radius:50%;border:2px solid}.spinner_background_extra-small{border-width:2px;width:16px;height:16px;-webkit-box-sizing:border-box;box-sizing:border-box}.spinner_background_small{border-width:4px;width:32px;height:32px;-webkit-box-sizing:border-box;box-sizing:border-box}.spinner_background_standard{border-width:8px;width:64px;height:64px;-webkit-box-sizing:border-box;box-sizing:border-box}.spinner_background_main{border-color:var(--color-content-default, rgb(40, 40, 40));opacity:0.16}.spinner_background_light{border-color:var(--color-content-bright, rgb(255, 255, 255));opacity:0.16}.spinner_background_content{border-color:var(--color-surface-0, rgb(255, 255, 255));opacity:0.16}.spinner_background_positive{border-color:var(--color-positive, #10603b);opacity:0.16}.spinner_background_negative{border-color:var(--color-negative, #e60f0f);opacity:0.16}.spinner_loading{-webkit-animation:rotate 0.5s linear infinite;animation:rotate 0.5s linear infinite;position:absolute}.spinner_loading_extra-small{width:16px;height:16px}.spinner_loading_small{width:32px;height:32px}.spinner_loading_standard{width:64px;height:64px}.spinner_loading_main{color:var(--color-primary, rgb(30, 107, 241))}.spinner_loading_light{color:var(--color-content-bright, rgb(255, 255, 255))}.spinner_loading_content{color:var(--color-surface-0, rgb(255, 255, 255))}.spinner_loading_positive{color:var(--color-positive, #10603b)}.spinner_loading_negative{color:var(--color-negative, #e60f0f)}@-webkit-keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}";

const BdsLoadingSpinner = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
        /**
         * 	Sets the color of the spinner, can be 'primary', 'secondary' or 'ghost'
         */
        this.variant = 'primary';
        /**
         * Size, Entered as one of the size. Can be one of:
         * 'small', 'standard', 'large'.
         */
        this.size = 'standard';
        /**
         * Color, Entered as one of the color. Can be one of:
         * 'default', 'white'.
         */
        this.color = 'main';
        /**
         * Data test is the prop to specifically test the component action object.
         */
        this.dataTest = null;
        /**Function to transform the svg in a div element. */
        this.formatSvg = (svgContent) => {
            const div = document.createElement('div');
            div.innerHTML = svgContent;
            const svgElm = div.firstElementChild;
            svgElm.removeAttribute('width');
            svgElm.removeAttribute('height');
            return div.innerHTML;
        };
        this.setSvgContent = () => {
            const innerHTML = this.size == 'extra-small'
                ? loadExtraSmallSvg
                : this.size == 'small'
                    ? loadSmallSvg
                    : this.size == 'standard' && loadStandardSvg;
            const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));
            this.svgContent = this.formatSvg(svg);
        };
    }
    componentWillLoad() {
        this.setSvgContent();
    }
    render() {
        return (index.h(index.Host, { key: '45a1a1848ff530eb26d783587a08e97dc395cfe2' }, index.h("div", { key: 'c82ecf86bed59b078270a36d38ca57a85d3053b8', class: {
                spinner_container: true,
                [`spinner_background_${this.size}`]: true,
            }, "data-test": this.dataTest }, index.h("div", { key: '98c3907f8e73206a8243565eeb06311ac7e2345c', class: {
                spinner_background: true,
                [`spinner_background_${this.size}`]: true,
                [`spinner_background_${this.color}`]: true,
            } }), index.h("div", { key: '8fe6324d3bafebf2f3d231f347bcad2a7940491a', class: {
                spinner_loading: true,
                [`spinner_loading_${this.size}`]: true,
                [`spinner_loading_${this.color}`]: true,
            }, innerHTML: this.svgContent }))));
    }
};
BdsLoadingSpinner.style = loadingSpinnerCss;

exports.bds_loading_spinner = BdsLoadingSpinner;
//# sourceMappingURL=bds-loading-spinner.entry.cjs.js.map

//# sourceMappingURL=bds-loading-spinner.cjs.entry.js.map