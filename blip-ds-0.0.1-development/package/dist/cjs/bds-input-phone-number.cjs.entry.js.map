{"file": "bds-input-phone-number.entry.cjs.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAM,mBAAmB,GAAG,q6SAAq6S;;MCcp7S,gBAAgB,GAAA,MAAA;AAL7B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;AAUW,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AAGf,QAAA,IAAgB,CAAA,gBAAA,GAAI,KAAK;AACzB,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AACtB,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAmB,EAAE;AAEpC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAI,EAAE;AAElB;;AAEG;;AAEsB,QAAA,IAAK,CAAA,KAAA,GAAmB,KAAK;AAEtD;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAI,KAAK;AACvD;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAI,KAAK;AAO1C;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AACnC;;AAEG;AACsB,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AACnD;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AAUrD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AAuBpC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAI,cAAc;AAC/B;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAC3C;;;AAGG;AACsB,QAAA,IAAQ,CAAA,QAAA,GAAe,OAAO;AAE/C,QAAA,IAAS,CAAA,SAAA,GAAQ,EAAE;AA8DnB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAoB,KAAU;AACtD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,SAAC;AAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,OAAO,EAAc,KAAI;AACnD,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE;gBAC7B,IAAI,CAAC,gBAAgB,EAAE;;AAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;AACxB,SAAC;AAqBO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAE9B,SAAC;AAQO,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,KAAkB,KAAU;AAC7C,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,MAAM;AAC9B,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI;AACjC,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO;AAC5B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC7B,KAAK,EAAE,IAAI,CAAC,IAAI;gBAChB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE,IAAI,CAAC,eAAe;AAC9B,aAAA,CAAC;YACF,IAAI,CAAC,MAAM,EAAE;AACf,SAAC;AAeO,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,KAAoB,KAAU;YACvD,MAAM,eAAe,GAAI,KAAK,CAAC,MAAkB,CAAC,SAAS,KAAK,YAAY;YAC5E,MAAM,cAAc,GAAI,KAAK,CAAC,MAAkB,CAAC,SAAS,KAAK,OAAO;AAEtE,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,eAAe,IAAI,cAAc,CAAC,EAAE;gBAChF,IAAI,CAAC,MAAM,EAAE;;AAEjB,SAAC;AAqJF;AAhTC,IAAA,MAAM,WAAW,GAAA;QACf,IAAI,CAAC,MAAM,EAAE;;IAIf,YAAY,GAAA;AACV,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;;AAKjD,IAAA,YAAY,CAAC,EAAS,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,MAA0B,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;;IAKvB,eAAe,GAAA;QACb,IAAI,CAAC,eAAe,EAAE;;IAGhB,eAAe,GAAA;AACrB,QAAA,QAAQ,IAAI,CAAC,QAAQ;AACnB,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,SAAS,GAAGA,cAAwB;gBACzC;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,SAAS,GAAGC,cAAwB;gBACzC;AACF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,SAAS,GAAGC,cAAwB;gBACzC;AACF,YAAA;AACE,gBAAA,IAAI,CAAC,SAAS,GAAGC,SAA2B;gBAC5C;;QAGJ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QAE9C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,OAAY,KAAK,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC;AAE3G,QAAA,IAAI,YAAY,KAAK,EAAE,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC;;aAC1C;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,UAAU,CAAC,CAAC,CAAC;;QAE9D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC;;IAG9C,mBAAmB,GAAA;QACjB,IAAI,CAAC,eAAe,EAAE;;AAGxB,IAAA,IAAY,YAAY,GAAA;AACtB,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;;IAmCxD,iBAAiB,GAAA;AACzB,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC7B,KAAK,EAAE,IAAI,CAAC,IAAI;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,eAAe;AAC9B,SAAA,CAAC;;IAGI,gBAAgB,GAAA;QACtB,IAAIC,4BAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB;AAC/C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;aACvB;AACL,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;AAUzB,IAAA,aAAa,CAAC,KAAK,EAAA;AACzB,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;YACxB,IAAI,CAAC,MAAM,EAAE;;;AAmBjB,IAAA,MAAM,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAA;AACrC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;AACtB,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC7B,KAAK,EAAE,IAAI,CAAC,IAAI;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,eAAe;AAC9B,SAAA,CAAC;;IAYI,aAAa,GAAA;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;IAIzB,UAAU,GAAA;AAChB,QAAA,QACE,IAAI,CAAC,IAAI,KACPC,OAAA,CAAA,KAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,oBAAoB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AACnC,aAAA,EAAA,EAEDA,OAAU,CAAA,UAAA,EAAA,EAAA,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,CACzF,CACP;;IAIG,WAAW,GAAA;AACjB,QAAA,QACE,IAAI,CAAC,KAAK,KACRA,OAAA,CAAA,OAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,uBAAuB,EAAE,IAAI;gBAC7B,kCAAkC,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;aACrE,EAAA,EAEDA,OAAA,CAAA,UAAA,EAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAClC,EAAA,IAAI,CAAC,KAAK,CACF,CACL,CACT;;IAIG,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;AAExB,QAAA,OAAO,OAAO,IACZA,OAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvCA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/BA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAY,CAC5E,EACNA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP,IACJ,IAAI;;IAGV,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;AAClD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,GAAG,YAAY;QACzD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAE9C,QAAA,QACEA,OAAA,CAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,eAAA,EAAgB,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAChDD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAiB,eAAA,EAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,IAAI,EAAA,EAC/EA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;gBACX,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,uBAAuB,EAAE,IAAI,CAAC,QAAQ;AACtC,gBAAA,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;AAC5B,gBAAA,gBAAgB,EAAE,SAAS;AAC5B,aAAA,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,iBAAiB,EAAA,EAErB,IAAI,CAAC,UAAU,EAAE,EAClBA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,MAAM,EACpB,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,WAAA,EAC7B,IAAI,CAAC,YAAY,EAC5B,KAAK,EAAC,aAAa,EACnB,QAAQ,EAAC,GAAG,EAAA,EAEZA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,IAAI,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,EAAC,SAAS,EAAY,CAAA,EAC7FA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,SAAS,GAAa,CACjD,EACNA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC1B,IAAI,CAAC,WAAW,EAAE,EACnBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAA,EAC7CA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,gCAAgC,EAAA,EACzCA,OAAkB,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,SAAA,EAAA,MAAM,EAAC,OAAO,EAAC,OAAO,IACrC,IAAI,CAAC,KAAK,CACF,CACP,EACNA,OAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,IAAI,EAAC,aAAa,EAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,OAAO,EAAC,4CAAsC,EAC9C,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,KAAK,EAAE,IAAI,CAAC,IAAI,EAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,eACZ,IAAI,CAAC,QAAQ,EAClB,SAAS,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,GAAG,IAAI,EAAA,CAC1C,CACL,CACF,EACL,IAAI,CAAC,OAAO,IAAIA,uEAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,EAAG,CAAA,EAChGA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,aAAa,EAAA,CAAG,CACvB,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,8BAA8B,EAAE,IAAI;gBACpC,oCAAoC,EAAE,IAAI,CAAC,MAAM;aAClD,EAEA,EAAA,IAAI,CAAC,MAAM;YACV,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,MAClBA,OAAA,CAAA,mBAAA,EAAA,EACE,GAAG,EAAE,IAAI,EACT,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAC9B,QAAQ,EAAE,IAAI,KAAK,IAAI,CAAC,eAAe,EACvC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,EACvF,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAA,EAEnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,OAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CACpC,CACrB,CAAC,CACA,CACD;;;;;;;;;;;;;", "names": ["countriesPtBR['default']", "countriesEnUS['default']", "countriesEsES['default']", "countriesDefault['default']", "numberValidation", "h", "Host"], "sources": ["src/components/input-phone-number/input-phone-number.scss?tag=bds-input-phone-number&encapsulation=shadow", "src/components/input-phone-number/input-phone-number.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n$select-options-box-shadow: 0px 2px 8px rgba(96, 123, 153, 0.3);\n$select-options-radius: 8px;\n$select-options-max-height: 250px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n  }\n\n  .input__container {\n    padding: 4px 8px 9px;\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      bds-typo {\n        color: $color-input-primary;\n      }\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n    flex-grow: 1;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n:host {\n  display: flex;\n  border-radius: 8px;\n  position: relative;\n  outline: none;\n  width: 100%;\n  min-width: 200px;\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  .bds-icon {\n    position: relative;\n    z-index: 1;\n  }\n\n  &--state {\n    &-primary {\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      @include input-theme(\n        'danger',\n        $color-negative,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      @include input-theme(\n        'success',\n        $color-positive,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n  }\n\n  & .icon-success {\n    color: $color-positive;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: 0;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-negative;\n      }\n      .input__message__text {\n        color: $color-negative;\n      }\n    }\n    &--success {\n      .input__message__icon {\n        .bds-icon {\n          color: $color-positive;\n        }\n      }\n      .input__message__text {\n        color: $color-content-default;\n      }\n    }\n  }\n  &__icon {\n    bds-icon {\n      position: relative;\n    }\n    bds-icon:first-child {\n      margin-right: 8px;\n    }\n    &::before {\n      content: '';\n      background: transparent;\n      height: calc(100% - 2px);\n      max-height: 54px;\n      width: 70px;\n      position: absolute;\n      left: 1px;\n      top: 1px;\n      border-radius: 8px 0px 0px 8px;\n    }\n    &::after {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n      pointer-events: none;\n    }\n    &:focus-visible {\n      outline: none;\n      &::after {\n        border-color: $color-focus;\n      }\n    }\n    position: relative;\n    height: 100%;\n    color: $color-content-disable;\n    display: flex;\n    align-items: center;\n    justify-content: space-evenly;\n    padding-right: 16px;\n    padding-left: 12px;\n    cursor: pointer;\n  }\n\n  &__country-code {\n    color: $color-content-disable;\n    padding-right: 5px;\n  }\n\n  &:hover,\n  &--pressed {\n    .input__icon {\n      &::before {\n        background: $color-surface-2;\n      }\n    }\n  }\n}\n\n.select-phone-number {\n  &__options {\n    @include custom-scroll;\n\n    background: $color-surface-2;\n    width: 100%;\n    max-height: 200px;\n    position: absolute;\n    top: 99%;\n    left: 0;\n    border-radius: $select-options-radius;\n    box-shadow: $shadow-2;\n    overflow-y: auto;\n    overflow-x: hidden;\n    z-index: 2;\n    margin-top: 4px;\n\n    transition:\n      transform 0.25s,\n      opacity 0.75s,\n      visibility 0.75s;\n    transform-origin: top left;\n    transform: scaleY(0);\n    opacity: 0;\n\n    &--open {\n      visibility: visible;\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Method, Watch, Element, Listen, Host } from '@stencil/core';\nimport { Option } from '../selects/select-interface';\nimport { numberValidation } from '../../utils/validations';\nimport * as countriesDefault from './countries.json';\nimport * as countriesPtBR from './countries-pt_BR.json';\nimport * as countriesEnUS from './countries-en_US.json';\nimport * as countriesEsES from './countries-es_ES.json';\n\nexport type languages = 'pt_BR' | 'es_ES' | 'en_US';\n@Component({\n  tag: 'bds-input-phone-number',\n  styleUrl: 'input-phone-number.scss',\n  shadow: true,\n})\nexport class InputPhoneNumber {\n  private nativeInput?: HTMLInputElement;\n\n  @Element() el!: HTMLBdsSelectElement;\n\n  @State() isOpen? = false;\n  @State() selectedCountry: string;\n  @State() isoCode: string;\n  @State() validationDanger? = false;\n  @State() validationMesage? = '';\n  @State() isPressed? = false;\n\n  /**\n   * Lista de opções do select.\n   */\n  @Prop() options?: Array<Option> = [];\n\n  /**\n   * Valor do input de telefone.\n   */\n  @Prop() text? = '';\n\n  /**\n   * Valor do select.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  @Prop({ mutable: true }) value?: string | null = '+55';\n\n  /**\n   * Habilita o estado \"danger\" no input.\n   */\n  @Prop({ mutable: true, reflect: true }) danger? = false;\n  /**\n   * Habilita o estado \"success\" no input.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Desabilita o input.\n   */\n  @Prop({ reflect: true }) disabled? = false;\n\n  /**\n   * Se `true`, o valor do input será obrigatório.\n   */\n  @Prop() required: boolean;\n\n  /**\n   * Mensagem de ajuda para o usuário.\n   */\n  @Prop() helperMessage?: string = '';\n  /**\n   * Mensagem de erro a ser exibida.\n   */\n  @Prop({ mutable: true }) errorMessage?: string = '';\n  /**\n   * Mensagem de sucesso a ser exibida.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Mensagem de erro para campo obrigatório.\n   */\n  @Prop() requiredErrorMessage: string;\n  /**\n   * Mensagem de erro para validação numérica.\n   */\n  @Prop() numberErrorMessage: string;\n\n  /**\n   * Data-test para identificar o componente.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Data-test para o botão de seleção de bandeira.\n   */\n  @Prop() dtSelectFlag?: string = null;\n\n  /**\n   * Evento disparado quando o valor é alterado.\n   */\n  @Event({ bubbles: true, composed: true }) bdsPhoneNumberChange!: EventEmitter;\n  /**\n   * Evento disparado quando o input sofre alteração.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n  /**\n   * Evento disparado quando a seleção é cancelada.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n  /**\n   * Evento disparado quando o select ganha foco.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n  /**\n   * Evento disparado quando o select perde o foco.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  /**\n   * Label do input.\n   */\n  @Prop() label? = 'Phone number';\n  /**\n   * Ícone à esquerda do input.\n   */\n  @Prop({ reflect: true }) icon?: string = '';\n  /**\n   * Valores possíveis: \"pt_BR\", \"en_US\", \"es_ES\".\n   * Se nenhum for informado, utiliza o arquivo padrão (countries.json).\n   */\n  @Prop({ mutable: true }) language?: languages = 'pt_BR';\n\n  private countries: any = {};\n\n  @Method()\n  async removeFocus(): Promise<void> {\n    this.onBlur();\n  }\n\n  @Watch('value')\n  valueChanged(): void {\n    for (const option of this.childOptions) {\n      option.selected = this.value === option.value;\n    }\n  }\n\n  @Listen('mousedown', { target: 'window', passive: true })\n  handleWindow(ev: Event) {\n    if (!this.el.contains(ev.target as HTMLInputElement)) {\n      this.isOpen = false;\n    }\n  }\n\n  @Watch('language')\n  languageChanged() {\n    this.updateCountries();\n  }\n\n  private updateCountries() {\n    switch (this.language) {\n      case 'pt_BR':\n        this.countries = countriesPtBR['default'];\n        break;\n      case 'en_US':\n        this.countries = countriesEnUS['default'];\n        break;\n      case 'es_ES':\n        this.countries = countriesEsES['default'];\n        break;\n      default:\n        this.countries = countriesDefault['default'];\n        break;\n    }\n\n    const flagsNames = Object.keys(this.countries);\n  \n    const countryIndex = Object.values(this.countries).findIndex((country: any) => country.code === this.value);\n  \n    if (countryIndex !== -1) {\n      this.selectedCountry = flagsNames[countryIndex];\n    } else {\n      this.selectedCountry = this.selectedCountry || flagsNames[0];\n    }\n    this.isoCode = this.isoCode || flagsNames[0];\n  }\n\n  componentWillRender() {\n    this.updateCountries();\n  }\n\n  private get childOptions(): HTMLBdsSelectOptionElement[] {\n    return Array.from(this.el.querySelectorAll('bds-select-option'));\n  }\n\n  private refNativeInput = (el: HTMLInputElement): void => {\n    this.nativeInput = el;\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onFocus = (): void => {\n    this.bdsFocus.emit();\n    this.isPressed = true;\n  };\n\n  private onBlur = (): void => {\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    this.checkValidity();\n    if (input) {\n      this.text = input.value || '';\n      this.numberValidation();\n    }\n    this.bdsInput.emit(ev);\n  };\n\n  @Watch('text')\n  protected handleInputChange(): void {\n    this.bdsPhoneNumberChange.emit({\n      value: this.text,\n      code: this.value,\n      isoCode: this.isoCode,\n      country: this.selectedCountry,\n    });\n  }\n\n  private numberValidation() {\n    if (numberValidation(this.nativeInput.value)) {\n      this.validationMesage = this.numberErrorMessage;\n      this.validationDanger = true;\n    } else {\n      this.validationDanger = false;\n    }\n  }\n\n  private toggle = (): void => {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter') {\n      this.toggle();\n    }\n  }\n\n  private handler = (event: CustomEvent): void => {\n    const { value } = event.detail;\n    this.value = value.code;\n    this.selectedCountry = value.flag;\n    this.isoCode = value.isoCode;\n    this.bdsPhoneNumberChange.emit({\n      value: this.text,\n      code: this.value,\n      isoCode: this.isoCode,\n      country: this.selectedCountry,\n    });\n    this.toggle();\n  };\n\n  @Method()\n  async changeCountry(code, isoCode, flag) {\n    this.value = code;\n    this.selectedCountry = flag;\n    this.isoCode = isoCode;\n    this.bdsPhoneNumberChange.emit({\n      value: this.text,\n      code: this.value,\n      isoCode: this.isoCode,\n      country: this.selectedCountry,\n    });\n  }\n\n  private keyPressWrapper = (event: KeyboardEvent): void => {\n    const isSelectElement = (event.target as Element).localName === 'bds-select';\n    const isInputElement = (event.target as Element).localName === 'input';\n\n    if (event.key === 'Enter' && !this.isOpen && (isSelectElement || isInputElement)) {\n      this.toggle();\n    }\n  };\n\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  private renderIcon(): HTMLElement {\n    return (\n      this.icon && (\n        <div\n          class={{\n            input__icon: true,\n            'input__icon--large': !!this.label,\n          }}\n        >\n          <bds-icon size={this.label ? 'medium' : 'small'} name={this.icon} color=\"inherit\"></bds-icon>\n        </div>\n      )\n    );\n  }\n\n  private renderLabel(): HTMLElement {\n    return (\n      this.label && (\n        <label\n          class={{\n            input__container__label: true,\n            'input__container__label--pressed': this.isPressed && !this.disabled,\n          }}\n        >\n          <bds-typo variant=\"fs-12\" bold=\"bold\">\n            {this.label}\n          </bds-typo>\n        </label>\n      )\n    );\n  }\n\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    return message ? (\n      <div class={styles} part=\"input__message\">\n        <div class=\"input__message__icon\">\n          <bds-icon size=\"x-small\" name={icon} theme=\"outline\" color=\"inherit\"></bds-icon>\n        </div>\n        <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n          {message}\n        </bds-typo>\n      </div>\n    ) : null;\n  }\n\n  render(): HTMLElement {\n    const isPressed = this.isPressed && !this.disabled;\n    const iconArrow = this.isOpen ? 'arrow-up' : 'arrow-down';\n    const flagsNames = Object.keys(this.countries);\n\n    return (\n      <Host aria-disabled={this.disabled ? 'true' : null}>\n        <div class={{ element_input: true }} aria-disabled={this.disabled ? 'true' : null}>\n          <div\n            class={{\n              input: true,\n              'input--state-primary': !this.danger && !this.validationDanger,\n              'input--state-danger': this.danger || this.validationDanger,\n              'input--state-success': this.success,\n              'input--state-disabled': this.disabled,\n              'input--label': !!this.label,\n              'input--pressed': isPressed,\n            }}\n            onClick={this.onClickWrapper}\n            onKeyDown={this.keyPressWrapper}\n            part=\"input-container\"\n          >\n            {this.renderIcon()}\n            <div\n              onClick={this.toggle}\n              onKeyDown={this.handleKeyDown.bind(this)}\n              data-test={this.dtSelectFlag}\n              class=\"input__icon\"\n              tabindex=\"0\"\n            >\n              <bds-icon size=\"medium\" theme=\"solid\" name={this.selectedCountry} color=\"primary\"></bds-icon>\n              <bds-icon size=\"x-small\" name={iconArrow}></bds-icon>\n            </div>\n            <div class=\"input__container\">\n              {this.renderLabel()}\n              <div class={{ input__container__wrapper: true }}>\n                <div class=\"input__container__country-code\">\n                  <bds-typo no-wrap=\"true\" variant=\"fs-14\">\n                    {this.value}\n                  </bds-typo>\n                </div>\n                <input\n                  class={{ input__container__text: true }}\n                  type=\"phonenumber\"\n                  required={this.required}\n                  pattern=\"/^(\\(?\\+?[0-9]*\\)?)?[0-9_\\- \\(\\)]*$/\"\n                  ref={this.refNativeInput}\n                  onInput={this.changedInputValue}\n                  onFocus={this.onFocus}\n                  onBlur={this.onBlur}\n                  value={this.text}\n                  disabled={this.disabled}\n                  data-test={this.dataTest}\n                  {...{ maxlength: this.value === '+55' ? 25 : null }}\n                ></input>\n              </div>\n            </div>\n            {this.success && <bds-icon class=\"icon-success\" name=\"check\" theme=\"outline\" size=\"xxx-small\" />}\n            <slot name=\"input-right\" />\n          </div>\n          {this.renderMessage()}\n        </div>\n        <div\n          class={{\n            'select-phone-number__options': true,\n            'select-phone-number__options--open': this.isOpen,\n          }}\n        >\n          {this.isOpen &&\n            flagsNames.map((flag) => (\n              <bds-select-option\n                key={flag}\n                onOptionSelected={this.handler}\n                selected={flag === this.selectedCountry}\n                value={{ code: this.countries[flag].code, isoCode: this.countries[flag].isoCode, flag }}\n                status={this.countries[flag].isoCode}\n              >\n                {this.countries[flag].name} {this.countries[flag].code}\n              </bds-select-option>\n            ))}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}