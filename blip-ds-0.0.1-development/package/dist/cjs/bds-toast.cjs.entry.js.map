{"file": "bds-toast.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,QAAQ,GAAG,+lHAA+lH;;MCcnmH,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAOE;;AAEG;AACsB,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC7C;;;AAGG;AACK,QAAA,IAAU,CAAA,UAAA,GAAe,QAAQ;AACzC;;;AAGG;AACK,QAAA,IAAO,CAAA,OAAA,GAAgB,QAAQ;AAavC;;;AAGG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAG,CAAC;AACpB;;;;;AAKG;AACK,QAAA,IAAY,CAAA,YAAA,GAAqB,OAAO;AAChD;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAG,KAAK;AACpB;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAG,KAAK;AACpB;;;AAGG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAiB,aAAa;AAE9C;;;AAGG;AACK,QAAA,IAAc,CAAA,cAAA,GAAY,IAAI;AACtC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AAKrC;;AAEG;AACK,QAAA,IAAmB,CAAA,mBAAA,GAAG,MAAK;AACjC,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO;gBAAE,IAAI,CAAC,KAAK,EAAE;iBAC1C;gBACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE;;AAEhB,SAAC;QAiFO,IAAA,CAAA,WAAW,GAAmB;AACpC,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,KAAK,EAAE,OAAO;AACd,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,YAAY,EAAE,cAAc;SAC7B;AAyDF;AAhJS,IAAA,gBAAgB,CAAC,KAAK,EAAA;AAC5B,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;YAC9C,KAAK,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO;gBAAE,IAAI,CAAC,KAAK,EAAE;iBAC1C;gBACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE;;;;AAKlB;;AAEG;AAEH,IAAA,MAAM,MAAM,CAAC,EACX,UAAU,EACV,YAAY,EACZ,UAAU,EACV,IAAI,EACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,QAAQ,GACQ,EAAA;QAChB,IAAI,cAAc,GAAG,QAAQ,CAAC,aAAa,CACzC,CAAA,oBAAA,EAAuB,OAAO,KAAK,cAAc,GAAG,WAAW,GAAG,aAAa,CAAA,CAAE,CAClF;QAED,IAAI,cAAc,EAAE;AAClB,YAAA,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;AACnC,YAAA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,GAAG,WAAW,GAAG,aAAa,CAAC;;aACjF;AACL,YAAA,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC;AAC9D,YAAA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,GAAG,WAAW,GAAG,aAAa,CAAC;AACtF,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;AACzC,YAAA,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;QAErC,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,QAAQ;QAC3C,IAAI,CAAC,EAAE,CAAC,YAAY,GAAG,YAAY,IAAI,OAAO;AAC9C,QAAA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU;AAC/B,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,SAAS;AAC7B,QAAA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU;QAC/B,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,IAAI,QAAQ;QACrC,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,GAAG,IAAI,IAAI,CAAC;AACvC,QAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,OAAO,KAAK,cAAc,GAAG,WAAW,GAAG,aAAa;AAE3E,QAAA,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,aAAJ,IAAI,KAAA,MAAA,GAAJ,IAAI,GAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AAErD,QAAA,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI;QAEnB,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,EAAE;YACxB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI;gBACnB,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;iBACjB,EAAE,GAAG,CAAC;AACT,aAAC,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;;;AAIxB;;AAEG;AAEH,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE;AACtB,YAAA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;AAChE,YAAA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;;aACxD;AACL,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;AACrD,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC;;QAGpD,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;SACjB,EAAE,GAAG,CAAC;;IAaT,MAAM,GAAA;QACJ,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,CAAC,UAAU,IAAI,CAAC,OAAO,CAAE,CAAA,GAAG,IAAI;AAChC,gBAAA,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAE,CAAA,GAAG,IAAI;gBAC3C,CAAC,CAAA,WAAA,EAAc,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,IAAI;gBAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,aAAA,EAAA,EAEA,IAAI,CAAC,OAAO,KAAK,cAAc,KAC9BA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,eAAe,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,EAAC,IAAI,EAAC,OAAO,EAAA,CAAG,CAC/E,EACA,IAAI,CAAC,IAAI,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,aAAa,EAAC,KAAK,EAAC,SAAS,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAI,CAAA,EAC7FA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,gBAAgB,EAAA,EACxB,IAAI,CAAC,UAAU,KACdA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EAClC,IAAI,CAAC,UAAU,CACP,CACZ,EACA,IAAI,CAAC,SAAS,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAa,CAC/E,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;AACnB,gBAAA,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAE,CAAA,GAAG,IAAI;aAC5C,EAEA,EAAA,IAAI,CAAC,UAAU,KAAK,QAAQ,IAC3BA,OACE,CAAA,YAAA,EAAA,EAAA,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3C,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE,EACzC,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,UAAU,EACf,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAA,EAE5B,IAAI,CAAC,UAAU,CACL,KAEbA,OAAA,CAAA,iBAAA,EAAA,EACE,OAAO,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE,EACzC,IAAI,EAAC,OAAO,EACZ,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3C,QAAQ,EAAC,GAAG,EACZ,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,OAAO,EACZ,QAAQ,EAAE,IAAI,CAAC,aAAa,GAC5B,CACH,CACG,CACF;;;;;;;;", "names": ["h"], "sources": ["src/components/toast/toast.scss?tag=bds-toast&encapsulation=shadow", "src/components/toast/toast.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n@mixin inherit-color() {\n  color: inherit;\n  background-color: inherit;\n}\n:host {\n  .show,\n  .hide {\n    display: flex;\n  }\n  .show {\n    opacity: 1;\n\n    &--top-right,\n    &--bottom-right {\n      animation: toastAnimationFadeInFromRight 1s;\n    }\n\n    &--top-left,\n    &--bottom-left {\n      animation: toastAnimationFadeInFromLeft 1s;\n    }\n  }\n  .hide {\n    transition: all 1s;\n    animation: toastAnimationFadeOut 0.5s;\n  }\n}\n\n.toast {\n  display: none;\n  position: relative;\n  box-sizing: border-box;\n  border-radius: 8px;\n  box-shadow: $shadow-3;\n  color: $color-content-default;\n  opacity: 0;\n  margin-top: 16px;\n  overflow: hidden;\n  gap: 16px;\n\n  &--action--icon {\n    min-width: 440px;\n    max-width: 440px;\n    padding: 8px 16px;\n\n    bds-icon-button {\n      height: 32px;\n    }\n\n    @media (max-width: $sm-screen) {\n      min-width: 220px;\n      width: 95%;\n      margin: 16px auto 0px auto;\n    }\n  }\n\n  &--action--button {\n    min-width: 440px;\n    max-width: 456px;\n    padding: 8px 16px;\n\n    @media (max-width: $sm-screen) {\n      min-width: 220px;\n      width: 95%;\n      margin: 16px auto 0px auto;\n    }\n  }\n\n  &--system {\n    background: $color-system;\n  }\n\n  &--error {\n    background: $color-error;\n  }\n\n  &--success {\n    background: $color-success;\n  }\n\n  &--warning {\n    background: $color-warning;\n  }\n\n  &--undo {\n    background-color: $color-system;\n  }\n\n  &--redo {\n    background-color: $color-system;\n  }\n  &--notification {\n    background-color: $color-surface-1;\n  }\n\n  &__icon {\n    position: relative;\n    display: flex;\n    align-items: center;\n    padding: 8px 0;\n  }\n\n  &__ballon {\n    display: flex;\n    position: absolute;\n    top: -8px;\n    left: -12px;\n    color: $color-system;\n    width: 72px;\n  }\n\n  &__content {\n    position: relative;\n    height: 100%;\n    width: 100%;\n    align-items: flex-start;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    padding: 8px 0;\n  }\n\n  &__action {\n    display: flex;\n    align-items: flex-start;\n\n    bds-button-icon,\n    bds-button {\n      position: relative;\n      &::before {\n        content: '';\n        position: absolute;\n        inset: -4px;\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n\n        &::before {\n          border-color: $color-focus;\n        }\n      }\n    }\n\n    &__button {\n      white-space: nowrap;\n    }\n  }\n}\n\n@keyframes toastAnimationFadeInFromRight {\n  0% {\n    opacity: 0;\n    right: -200px;\n  }\n  50% {\n    opacity: 0.9;\n    right: 1px;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes toastAnimationFadeInFromLeft {\n  0% {\n    opacity: 0;\n    left: -200px;\n  }\n  50% {\n    opacity: 0.9;\n    left: 1px;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes toastAnimationFadeOut {\n  0% {\n    opacity: 1;\n  }\n  30% {\n    max-height: 60px;\n  }\n  80% {\n    opacity: 0;\n    max-height: 30px;\n  }\n  100% {\n    max-height: 0px;\n  }\n}\n", "import { Component, ComponentInterface, h, Prop, Method, Element, Event, EventEmitter } from '@stencil/core';\nimport {\n  ActionType,\n  VariantType,\n  ButtonActionType,\n  CreateToastType,\n  IconVariantMap,\n  PositionType,\n} from './toast-interface';\n@Component({\n  tag: 'bds-toast',\n  styleUrl: 'toast.scss',\n  shadow: true,\n})\nexport class BdsToast implements ComponentInterface {\n  @Element() el: HTMLBdsToastElement;\n  /**\n   * used for add the icon. Uses the bds-icon component.\n   */\n  @Prop({ reflect: true }) icon?: string = null;\n  /**\n   * ActionType. Defines if the button should have a button or an icon. Can be one of:\n   * 'icon', 'button';\n   */\n  @Prop() actionType: ActionType = 'button';\n  /**\n   * Variant. Defines the color of the toast. Can be one of:\n   * 'system', 'error', 'success', 'warning', 'undo', 'redo';\n   */\n  @Prop() variant: VariantType = 'system';\n  /**\n   * The title of the component:\n   */\n  @Prop() toastTitle: string;\n  /**\n   * The text content of the component:\n   */\n  @Prop() toastText: string;\n  /**\n   * If the action type is button, this will be the text of the button:\n   */\n  @Prop() buttonText: string;\n  /**\n   * Time to close the toast in seconds\n   * 0 = never close automatically (default value)\n   */\n  @Prop() duration = 0;\n  /**\n   * Define an action to the button toast. Can be one of:\n   * 'close', 'custom';\n   * if the action type is set to close, the button will close automatically.\n   * if the action type is set to custom, a function need to be passed when the toastButtonClick is emitted.\n   */\n  @Prop() buttonAction: ButtonActionType = 'close';\n  /**\n   * Controls the open event of the component:\n   */\n  @Prop() show = false;\n  /**\n   * Controls the hide event of the component:\n   */\n  @Prop() hide = false;\n  /**\n   * The toast position on the screen. Can be one of:\n   * 'top-right', 'top-left', 'bottom-right', 'bottom-left' (default value);\n   */\n  @Prop() position: PositionType = 'bottom-left';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonAction is the data-test to button action.\n   */\n  @Prop() dtButtonAction?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Event used to execute some action when the action button on the toast is clicked\n   */\n  @Event() toastButtonClick!: EventEmitter;\n  /**\n   * Sends an event to be used when creating an action when clicking the toast button\n   */\n  private _buttonClickHandler = () => {\n    if (this.buttonAction === 'close') this.close();\n    else {\n      this.toastButtonClick.emit(this.el);\n      this.close();\n    }\n  };\n\n  private _keyPressHandler(event) {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault();\n      if (this.buttonAction === 'close') this.close();\n      else {\n        this.toastButtonClick.emit(this.el);\n        this.close();\n      }\n    }\n  }\n\n  /**\n   * Can be used outside to open the toast\n   */\n  @Method()\n  async create({\n    actionType,\n    buttonAction,\n    buttonText,\n    icon,\n    toastText,\n    toastTitle,\n    variant,\n    duration,\n  }: CreateToastType) {\n    let toastContainer = document.querySelector(\n      `bds-toast-container.${variant === 'notification' ? 'top-right' : 'bottom-left'}`,\n    );\n\n    if (toastContainer) {\n      toastContainer.appendChild(this.el);\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n    } else {\n      toastContainer = document.createElement('bds-toast-container');\n      toastContainer.classList.add(variant === 'notification' ? 'top-right' : 'bottom-left');\n      document.body.appendChild(toastContainer);\n      toastContainer.appendChild(this.el);\n    }\n    this.el.actionType = actionType || 'button';\n    this.el.buttonAction = buttonAction || 'close';\n    this.el.buttonText = buttonText;\n    this.el.toastText = toastText;\n    this.el.toastTitle = toastTitle;\n    this.el.variant = variant || 'system';\n    this.el.duration = duration * 1000 || 0;\n    this.el.position = variant === 'notification' ? 'top-right' : 'bottom-left';\n\n    this.el.icon = icon ?? this.mapIconName[this.variant];\n\n    this.el.show = true;\n\n    if (this.el.duration > 0) {\n      setTimeout(() => {\n        this.el.hide = true;\n        setTimeout(() => {\n          this.el.remove();\n        }, 400);\n      }, this.el.duration);\n    }\n  }\n\n  /**\n   * Can be used outside the component to close the toast\n   */\n  @Method()\n  async close() {\n    if (this.el.shadowRoot) {\n      this.el.shadowRoot.querySelector('div').classList.remove('show');\n      this.el.shadowRoot.querySelector('div').classList.add('hide');\n    } else {\n      this.el.querySelector('div').classList.remove('show');\n      this.el.querySelector('div').classList.add('hide');\n    }\n\n    setTimeout(() => {\n      this.el.remove();\n    }, 400);\n  }\n\n  private mapIconName: IconVariantMap = {\n    system: 'bell',\n    error: 'error',\n    success: 'like',\n    warning: 'attention',\n    undo: 'undo',\n    redo: 'redo',\n    notification: 'notification',\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          toast: true,\n          [`toast--${this.variant}`]: true,\n          [`toast--action--${this.actionType}`]: true,\n          [`show show--${this.position}`]: this.show,\n          hide: this.hide,\n        }}\n      >\n        {this.variant === 'notification' && (\n          <bds-icon class=\"toast__ballon\" theme=\"solid\" name=\"blip-chat\" size=\"brand\" />\n        )}\n        {this.icon && <bds-icon class=\"toast__icon\" theme=\"outline\" size=\"medium\" name={this.icon} />}\n        <div class=\"toast__content\">\n          {this.toastTitle && (\n            <bds-typo variant=\"fs-14\" bold=\"bold\">\n              {this.toastTitle}\n            </bds-typo>\n          )}\n          {this.toastText && <bds-typo variant=\"fs-14\" innerHTML={this.toastText}></bds-typo>}\n        </div>\n        <div\n          class={{\n            toast__action: true,\n            [`toast__action__${this.actionType}`]: true,\n          }}\n        >\n          {this.actionType === 'button' ? (\n            <bds-button\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              onClick={() => this._buttonClickHandler()}\n              variant=\"secondary\"\n              size=\"standard\"\n              dataTest={this.dtButtonAction}\n            >\n              {this.buttonText}\n            </bds-button>\n          ) : (\n            <bds-button-icon\n              onClick={() => this._buttonClickHandler()}\n              size=\"short\"\n              onKeyDown={this._keyPressHandler.bind(this)}\n              tabindex=\"0\"\n              variant=\"secondary\"\n              icon=\"close\"\n              dataTest={this.dtButtonClose}\n            />\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "version": 3}