{"version": 3, "file": "bds-modal-close-button.entry.cjs.js", "sources": ["src/components/modal/modal-close-button/modal-close-button.scss?tag=bds-modal-close-button&encapsulation=shadow", "src/components/modal/modal-close-button/modal-close-button.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.modal__close__button-icon {\n  opacity: 0;\n  visibility: hidden;\n  color: $color-content-default;\n  display: flex;\n  justify-content: flex-end;\n  padding-bottom: 16px;\n\n  &--active {\n    opacity: 1;\n    visibility: visible;\n  }\n}\n", "import { Component, ComponentInterface, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-modal-close-button',\n  styleUrl: 'modal-close-button.scss',\n  shadow: true,\n})\nexport class BdsModalCloseButton implements ComponentInterface {\n  /**\n   * Used to hide or show the close button\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public active?: boolean = true;\n\n  render() {\n    return (\n      <div\n        class={{\n          'modal__close__button-icon': true,\n          'modal__close__button-icon--active': this.active,\n        }}\n      >\n        <bds-icon size=\"medium\" name=\"close\"></bds-icon>\n      </div>\n    );\n  }\n}\n"], "names": ["h"], "mappings": ";;;;AAAA,MAAM,mBAAmB,GAAG,6QAA6Q;;MCO5R,mBAAmB,GAAA,MAAA;AALhC,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AAKI,QAAA,IAAM,CAAA,MAAA,GAAa,IAAI;AAc/B;IAZC,MAAM,GAAA;QACJ,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,2BAA2B,EAAE,IAAI;gBACjC,mCAAmC,EAAE,IAAI,CAAC,MAAM;aACjD,EAAA,EAEDA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAA,CAAY,CAC5C;;;;;;;"}