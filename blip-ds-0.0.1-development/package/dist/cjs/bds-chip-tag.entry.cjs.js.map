{"version": 3, "file": "bds-chip-tag.entry.cjs.js", "sources": ["src/components/chip-tag/chip-tag.scss?tag=bds-chip-tag&encapsulation=shadow", "src/components/chip-tag/chip-tag.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  max-width: 100%;\n\n  .chip_tag {\n    display: flex;\n    align-items: center;\n    min-width: 32px;\n    width: fit-content;\n    height: 24px;\n    border-radius: 12px;\n    padding: 0px 4px;\n    box-sizing: border-box;\n\n    &--container-text {\n      &--full {\n        width: 100%;\n      }\n      &--half {\n        width: calc(100% - 16px);\n      }\n    }\n\n    &--icon {\n      display: flex;\n      align-items: center;\n      width: 16px;\n      height: 16px;\n    }\n    &--text {\n      display: flex;\n      align-items: center;\n      margin: 0 8px;\n      font-family: $font-family;\n    }\n    &--default {\n      background-color: $color-system;\n      color: $color-content-din;\n    }\n    &--info {\n      background-color: $color-info;\n      color: $color-content-din;\n    }\n    &--success {\n      background-color: $color-success;\n      color: $color-content-din;\n    }\n    &--warning {\n      background-color: $color-warning;\n      color: $color-content-din;\n    }\n    &--danger {\n      background-color: $color-error;\n      color: $color-content-din;\n    }\n    &--outline {\n      border: 1px solid $color-border-1;\n      color: $color-content-default;\n    }\n    &--disabled {\n      background-color: $color-surface-3;\n      color: $color-content-default;\n    }\n  }\n}\n", "import { Component, Host, h, Prop } from '@stencil/core';\n\nexport type ColorChipTag = 'default' | 'info' | 'success' | 'warning' | 'danger' | 'outline' | 'disabled';\n\n@Component({\n  tag: 'bds-chip-tag',\n  styleUrl: 'chip-tag.scss',\n  shadow: true,\n})\nexport class ChipTag {\n  /**\n   * used for add icon in left container. Uses the bds-icon component.\n   */\n  @Prop() icon?: string;\n  /**\n   * used for change the color. Uses one of them.\n   */\n  @Prop() color?: ColorChipTag = 'default';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host>\n        <div\n          class={{\n            chip_tag: true,\n            [`chip_tag--${this.color}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.icon && (\n            <div class=\"chip_tag--icon\">\n              <bds-icon size=\"x-small\" name={this.icon}></bds-icon>\n            </div>\n          )}\n          <div class={this.icon ? `chip_tag--container-text--half` : `chip_tag--container-text--full`}>\n            <bds-typo no-wrap=\"true\" class=\"chip_tag--text\" variant=\"fs-12\" bold=\"bold\">\n              <slot></slot>\n            </bds-typo>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,6nDAA6nD;;MCSnoD,OAAO,GAAA,MAAA;AALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAUE;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAkB,SAAS;AACxC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA0BjC;IAxBC,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,CAAC,aAAa,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI;aAClC,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAEvB,IAAI,CAAC,IAAI,KACRA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,gBAAgB,EAAA,EACzBA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAa,CAAA,CACjD,CACP,EACDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAA,8BAAA,CAAgC,GAAG,CAAA,8BAAA,CAAgC,EAAA,EACzFA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,SAAA,EAAkB,MAAM,EAAC,KAAK,EAAC,gBAAgB,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAA,EACzEA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACJ,CACP,CACF,CACD;;;;;;;"}