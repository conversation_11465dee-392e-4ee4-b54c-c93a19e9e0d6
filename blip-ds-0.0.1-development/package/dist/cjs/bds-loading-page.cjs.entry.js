'use strict';

var index = require('./index-D_zq0Z7d.js');

const messageBallonSvg = 'data:image/svg+xml;base64,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';

const loadingPageCss = ":host{display:block;position:fixed;top:0;left:0;height:100vh;width:100vw;z-index:999}.loading-container{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;background-color:var(--color-surface-4, rgb(20, 20, 20));opacity:0.65;mix-blend-mode:multiply}.page_loading{-webkit-animation:growUp 0.8s ease-in-out infinite;animation:growUp 0.8s ease-in-out infinite;width:116px;height:128px;color:var(--color-surface-1, rgb(246, 246, 246))}@-webkit-keyframes growUp{from{opacity:0}10%{opacity:1}60%{opacity:1}to{-webkit-transform:scale(1.6);transform:scale(1.6);opacity:0}}@keyframes growUp{from{opacity:0}10%{opacity:1}60%{opacity:1}to{-webkit-transform:scale(1.6);transform:scale(1.6);opacity:0}}";

const BdsLoading = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
        /**
         * Data test is the prop to specifically test the component action object.
         */
        this.dataTest = null;
        /**Function to transform the svg in a div element. */
        this.formatSvg = (svgContent) => {
            const div = document.createElement('div');
            div.innerHTML = svgContent;
            const svgElm = div.firstElementChild;
            svgElm.removeAttribute('width');
            svgElm.removeAttribute('height');
            return div.innerHTML;
        };
        this.setSvgContent = () => {
            const innerHTML = messageBallonSvg;
            const svg = atob(innerHTML.replace('data:image/svg+xml;base64,', ''));
            this.svgContent = this.formatSvg(svg);
        };
    }
    componentWillLoad() {
        this.setSvgContent();
    }
    render() {
        return (index.h(index.Host, { key: '01c10373b36658a99c810fe6dcf7b9a40698f636' }, index.h("div", { key: '8fbf9a177462daaecec6f92f318d20a1a9906c3d', class: "loading-container", "data-test": this.dataTest }, index.h("div", { key: '48a7facd88861f573b09f408b1623408cd48300d', class: { page_loading: true }, innerHTML: this.svgContent }))));
    }
};
BdsLoading.style = loadingPageCss;

exports.bds_loading_page = BdsLoading;
//# sourceMappingURL=bds-loading-page.entry.cjs.js.map

//# sourceMappingURL=bds-loading-page.cjs.entry.js.map