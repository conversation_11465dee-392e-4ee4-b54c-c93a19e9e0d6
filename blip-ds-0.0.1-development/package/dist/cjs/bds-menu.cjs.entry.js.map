{"file": "bds-menu.entry.cjs.js", "mappings": ";;;;;AAAA,MAAM,OAAO,GAAG,geAAge;;MCUne,OAAO,GAAA,MAAA;AALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAQW,QAAA,IAAU,CAAA,UAAA,GAAiB,IAAI;AAC/B,QAAA,IAAQ,CAAA,QAAA,GAAiB,IAAI;AAC7B,QAAA,IAAe,CAAA,eAAA,GAAY,CAAC;AAC5B,QAAA,IAAgB,CAAA,gBAAA,GAAY,CAAC;AACtC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC5B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAkB,OAAO;AACzC;;AAEG;AAKI,QAAA,IAAI,CAAA,IAAA,GAAa,KAAK;AA+BrB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAe,KAAU;AACjD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,kBAAkB,GAAG,CAAC,KAAK,KAAI;AACrC,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK;YACjB,KAAK,CAAC,eAAe,EAAE;AACzB,SAAC;AAyBF;IAxDC,iBAAiB,GAAA;QACf,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAGA,+BAAe,CAAC,IAAI,CAAC,UAAU,CAAC;;AAIlD,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI;;IAId,QAAQ,GAAA;AAChB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACzC,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,MAAM,aAAa,GAAGC,+BAAe,CAAC;gBACpC,aAAa,EAAE,IAAI,CAAC,UAAU;gBAC9B,cAAc,EAAE,IAAI,CAAC,WAAW;gBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACxB,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,GAAG;AACxC,YAAA,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,IAAI;;;IAa9C,MAAM,GAAA;AACJ,QAAA,MAAM,YAAY,GAAG;AACnB,YAAA,GAAG,EAAE,CAAA,EAAG,IAAI,CAAC,eAAe,CAAI,EAAA,CAAA;AAChC,YAAA,IAAI,EAAE,CAAA,EAAG,IAAI,CAAC,gBAAgB,CAAI,EAAA,CAAA;SACnC;QAED,QACEC,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAE,IAAI,CAAC,cAAc,EACxB,KAAK,EAAE;AACL,gBAAA,IAAI,EAAE,IAAI;AACV,gBAAA,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAE,CAAA,GAAG,IAAI;AAChC,gBAAA,CAAC,CAAY,UAAA,CAAA,GAAG,IAAI,CAAC,IAAI;aAC1B,EACD,KAAK,EAAE,YAAY,EAAA,EAEnBA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,EACL,IAAI,CAAC,IAAI,IAAIA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAA,CAAQ,CAC5F;;;;;;;;;;", "names": ["getScrollParent", "positionElement", "h", "Host"], "sources": ["src/components/menu/menu.scss?tag=bds-menu&encapsulation=shadow", "src/components/menu/menu.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.menu {\n  position: fixed;\n  pointer-events: none;\n  top: 0;\n  left: 0;\n  padding: 2px;\n  background-color: $color-surface-1;\n  border-radius: 8px;\n  box-shadow: 0px 8px 12px rgba(0, 0, 0, 0.08);\n  width: 240px;\n  opacity: 0;\n  -webkit-transition: opacity 0.5s;\n  -moz-transition: opacity 0.5s;\n  transition: opacity 0.5s;\n  z-index: $zindex-modal;\n\n  &__open {\n    pointer-events: auto;\n    opacity: 1;\n  }\n}\n\n.outzone {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  z-index: $zindex-modal-overlay;\n}\n", "import { Component, Host, ComponentInterface, h, State, Method, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { getScrollParent, positionElement } from '../../utils/position-element';\n\nexport type menuPosition = 'bottom' | 'right';\n\n@Component({\n  tag: 'bds-menu',\n  styleUrl: 'menu.scss',\n  shadow: true,\n})\nexport class BdsMenu implements ComponentInterface {\n  private menuElement?: HTMLElement;\n\n  @State() refElement?: HTMLElement = null;\n  @State() intoView?: HTMLElement = null;\n  @State() menupositionTop?: number = 0;\n  @State() menupositionLeft?: number = 0;\n  /**\n   * Menu. Used to link the minus with the action button.\n   */\n  @Prop() menu?: string = null;\n  /**\n   * Position. Used to position the Menu. Either on the left or on the bottom.\n   */\n  @Prop() position?: menuPosition = 'right';\n  /**\n   * Open. Used to open/close the menu.\n   */\n  @Prop({\n    mutable: true,\n    reflect: true,\n  })\n  public open?: boolean = false;\n\n  /**\n   * bdsToggle. Event to return selected date value.\n   */\n  @Event() bdsToggle?: EventEmitter;\n\n  componentWillLoad() {\n    this.refElement = document.getElementById(this.menu);\n    this.intoView = getScrollParent(this.refElement);\n  }\n\n  @Method()\n  async toggle() {\n    this.open = !this.open;\n  }\n\n  @Watch('open')\n  protected openMenu() {\n    this.bdsToggle.emit({ value: this.open });\n    if (this.open) {\n      const positionValue = positionElement({\n        actionElement: this.refElement,\n        changedElement: this.menuElement,\n        intoView: this.intoView,\n      });\n      this.menupositionTop = positionValue.top;\n      this.menupositionLeft = positionValue.left;\n    }\n  }\n\n  private refMenuElement = (el: HTMLElement): void => {\n    this.menuElement = el;\n  };\n\n  private onClickCloseButtom = (event) => {\n    this.open = false;\n    event.stopPropagation();\n  };\n\n  render() {\n    const menuPosition = {\n      top: `${this.menupositionTop}px`,\n      left: `${this.menupositionLeft}px`,\n    };\n\n    return (\n      <Host>\n        <div\n          ref={this.refMenuElement}\n          class={{\n            menu: true,\n            [`menu__${this.position}`]: true,\n            [`menu__open`]: this.open,\n          }}\n          style={menuPosition}\n        >\n          <slot></slot>\n        </div>\n        {this.open && <div class={{ outzone: true }} onClick={(ev) => this.onClickCloseButtom(ev)}></div>}\n      </Host>\n    );\n  }\n}\n"], "version": 3}