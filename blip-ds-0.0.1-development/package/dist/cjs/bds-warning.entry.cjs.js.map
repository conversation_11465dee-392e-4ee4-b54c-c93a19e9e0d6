{"version": 3, "file": "bds-warning.entry.cjs.js", "sources": ["src/components/warning/warning.scss?tag=bds-warning&encapsulation=shadow", "src/components/warning/warning.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n  align-items: center;\n}\n\n.warning__body {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n  background-color: $color-neutral-light-breeze;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.warning__icon {\n  color: $color-extend-browns-cheetos;\n}\n\n.warning__message {\n  color: $color-neutral-dark-rooftop;\n  margin-left: 8px;\n}\n", "import { Component, ComponentInterface, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'bds-warning',\n  styleUrl: 'warning.scss',\n  shadow: true,\n})\nexport class Warning implements ComponentInterface {\n  render() {\n    return (\n      <Host>\n        <div class=\"warning__body\">\n          <bds-icon class=\"warning__icon\" theme=\"solid\" size=\"small\" name=\"warning\"></bds-icon>\n          <bds-typo variant=\"fs-14\" tag=\"span\" class=\"warning__message\">\n            <slot />\n          </bds-typo>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,0VAA0V;;MCOhW,OAAO,GAAA,MAAA;;;;IAClB,MAAM,GAAA;AACJ,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACxBA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,eAAe,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAY,CAAA,EACrFA,OAAU,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAC,KAAK,EAAC,kBAAkB,EAAA,EAC3DA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAQ,CACC,CACP,CACD;;;;;;;"}