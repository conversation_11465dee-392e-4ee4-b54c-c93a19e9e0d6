{"version": 3, "file": "bds-nav-tree.entry.cjs.js", "sources": ["src/components/nav-tree/nav-tree.scss?tag=bds-nav-tree&encapsulation=shadow", "src/components/nav-tree/nav-tree.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  margin: -4px;\n  padding: 4px;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: height 0.5s;\n  -moz-transition: height 0.5s;\n  transition: height 0.5s;\n}\n\n.nav_main {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  padding: 8px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 8px;\n  border: 1px solid transparent;\n  overflow: hidden;\n\n  &--loading {\n    cursor: wait;\n  }\n\n  &--disable {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n\n  @include hover-and-pressed();\n\n  &:hover,\n  &_active {\n    border-color: $color-pressed;\n  }\n\n  &_active {\n    &:before {\n      background-color: $color-content-default;\n      border-color: $color-hover;\n      opacity: 0.08;\n    }\n  }\n\n  &--disable {\n    &:before,\n    &:hover {\n      border-color: transparent;\n      background-color: transparent;\n    }\n  }\n\n  & .icon-item {\n    position: relative;\n    color: $color-content-default;\n\n    &-active {\n      color: $color-primary;\n    }\n  }\n\n  &_text {\n    position: relative;\n    display: flex;\n    gap: 2px;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n\n    & .subtitle-item {\n      color: $color-content-default;\n      &--loading {\n        color: $color-content-disable;\n      }\n    }\n  }\n\n  &_content {\n    width: 100%;\n    flex-shrink: 99999;\n  }\n\n  &_arrow {\n    -webkit-transition: all ease 0.3s;\n    -moz-transition: all ease 0.3s;\n    transition: all ease 0.3s;\n    transform: rotate(0deg);\n\n    &--disable {\n      color: $color-content-disable;\n    }\n\n    &_active {\n      transform: rotate(180deg);\n    }\n  }\n}\n.accordion {\n  display: grid;\n  grid-template-rows: 0fr;\n  -webkit-transition: all ease 0.5s;\n  -moz-transition: all ease 0.5s;\n  transition: all ease 0.5s;\n\n  &_open {\n    grid-template-rows: 1fr;\n    padding: 8px 0;\n  }\n\n  & .container {\n    overflow: hidden;\n    position: relative;\n    padding-left: 23px;\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      left: 23px;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: $color-hover;\n      opacity: 0.8;\n    }\n\n    &--disable {\n      &:before {\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.nav_tree {\n  &_item {\n    position: relative;\n    display: flex;\n    gap: 8px;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    padding: 8px;\n    padding-left: 22px;\n\n    &--loading {\n      cursor: wait;\n    }\n\n    &--disable {\n      opacity: 0.5;\n      cursor: not-allowed;\n\n      &:before,\n      &:hover {\n        border-color: transparent;\n        background-color: transparent;\n      }\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    &_content {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n    }\n\n    &_slot {\n      width: 100%;\n      flex-shrink: 99999;\n    }\n\n    &:before {\n      content: '';\n      position: absolute;\n      width: 2px;\n      inset: 0;\n      top: 8px;\n      bottom: 8px;\n      border-radius: 8px;\n      background-color: transparent;\n      -webkit-transition: background-color ease 0.8s;\n      -moz-transition: background-color ease 0.8s;\n      transition: background-color ease 0.8s;\n    }\n\n    &:hover {\n      &:before {\n        background-color: $color-pressed;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n    }\n\n    &_active {\n      &:before {\n        background-color: $color-primary;\n        -webkit-transition: background-color ease 0.3s;\n        -moz-transition: background-color ease 0.3s;\n        transition: background-color ease 0.3s;\n      }\n      &:hover {\n        &:before {\n          background-color: $color-primary;\n          -webkit-transition: background-color ease 0.3s;\n          -moz-transition: background-color ease 0.3s;\n          transition: background-color ease 0.3s;\n        }\n      }\n    }\n\n    & .icon-arrow {\n      position: relative;\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n\n    &_button {\n      padding: 8px;\n      margin-left: 14px;\n      border-radius: 8px;\n      border: 1px solid transparent;\n\n      &:before {\n        left: -15px;\n      }\n\n      &:after {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        background-color: transparent;\n      }\n\n      &:hover,\n      &_active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.08;\n        }\n      }\n      &:active {\n        border-color: $color-pressed;\n        &:after {\n          background-color: $color-content-default;\n          opacity: 0.16;\n        }\n      }\n    }\n  }\n}\n\n.focus {\n  position: relative;\n\n  &:before {\n    content: '';\n    position: absolute;\n    inset: -4px;\n    border: 2px solid transparent;\n    border-radius: 4px;\n  }\n\n  &:focus-visible {\n    outline: none;\n\n    &:before {\n      border-color: $color-focus;\n    }\n  }\n}\n", "import { Component, Host, Element, State, Prop, Method, Event, EventEmitter, Watch, h } from '@stencil/core';\n\nexport type collapses = 'single' | 'multiple';\n\n@Component({\n  tag: 'bds-nav-tree',\n  styleUrl: 'nav-tree.scss',\n  shadow: true,\n})\nexport class NavTree {\n  private itemsGroup?: HTMLBdsNavTreeGroupElement = null;\n\n  @Element() private element: HTMLElement;\n\n  @State() isOpenAftAnimation?: boolean = false;\n  @State() navTreeChild? = null;\n  @State() numberElement?: number = null;\n  /**\n   * Focus Selected. Used to add title in header accordion.\n   */\n  @Prop() collapse?: collapses = 'single';\n  /**\n   * A prop for make the nav open.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = false;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text!: string;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Loading state. Indicates if the component is in a loading state.\n   */\n  @Prop() loading?: boolean = false;\n\n  /**\n   * Disable state. Indicates if the component is disabled.\n   */\n  @Prop() disable?: boolean = false;\n  /**\n   * When de open or close of component change, the event are dispache.\n   */\n  @Event() bdsToogleChange: EventEmitter;\n\n  @Method()\n  async toggle() {\n    if (!this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  @Method()\n  async reciveNumber(number) {\n    this.numberElement = number;\n  }\n\n  @Method()\n  async open() {\n    this.isOpen = true;\n  }\n\n  @Method()\n  async close() {\n    this.isOpen = false;\n  }\n  @Watch('isOpen')\n  protected isOpenChanged(value): void {\n    this.bdsToogleChange.emit({ value: value, element: this.element });\n    if (value) {\n      if (this.itemsGroup.collapse == 'single') {\n        this.itemsGroup?.closeAll(this.numberElement);\n      }\n    }\n  }\n\n  componentWillLoad() {\n    this.itemsGroup =\n      this.element.parentElement.tagName == 'BDS-NAV-TREE-GROUP' &&\n      (this.element.parentElement as HTMLBdsNavTreeGroupElement);\n    this.navTreeChild = this.element.querySelector('bds-nav-tree-item') === null ? false : true;\n  }\n\n  private handler = (): void => {\n    if (!this.loading && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  };\n\n  private handleKeyDown(event) {\n    if (event.key == 'Enter' && !this.disable) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <div tabindex=\"0\" onKeyDown={this.handleKeyDown.bind(this)} class=\"focus\">\n          <div\n            class={{\n              [`nav_main--disable`]: this.disable,\n            }}\n          >\n            <div\n              onClick={this.handler}\n              class={{\n                nav_main: true,\n                nav_main_active: this.isOpen,\n                [`nav_main--loading`]: this.loading,\n                [`nav_main--disable`]: this.disable,\n              }}\n              data-test={this.dataTest}\n              aria-label={this.text + (this.secondaryText && `: ${this.secondaryText}`)}\n            >\n              {this.loading ? (\n                <bds-loading-spinner size=\"extra-small\"></bds-loading-spinner>\n              ) : this.icon ? (\n                <bds-icon\n                  class={{\n                    [`icon-item`]: true,\n                    [`icon-item-active`]: this.isOpen,\n                  }}\n                  size=\"medium\"\n                  name={this.icon}\n                  color=\"inherit\"\n                  theme=\"outline\"\n                ></bds-icon>\n              ) : (\n                ''\n              )}\n              <div class=\"nav_main_text\">\n                {this.text && (\n                  <bds-typo\n                    class={{ ['title-item']: true, [`title-item--loading`]: this.loading }}\n                    variant=\"fs-14\"\n                    tag=\"span\"\n                    line-height=\"small\"\n                    bold={this.isOpen ? 'bold' : 'semi-bold'}\n                  >\n                    {this.text}\n                  </bds-typo>\n                )}\n                {this.secondaryText && (\n                  <bds-typo\n                    class={{ ['subtitle-item']: true, [`subtitle-item--loading`]: this.loading }}\n                    variant=\"fs-12\"\n                    line-height=\"small\"\n                    tag=\"span\"\n                    margin={false}\n                  >\n                    {this.secondaryText}\n                  </bds-typo>\n                )}\n              </div>\n              <div class=\"nav_main_content\">\n                <slot name=\"header-content\"></slot>\n              </div>\n              {this.navTreeChild && (\n                <bds-icon\n                  name=\"arrow-down\"\n                  class={{\n                    [`nav_main_arrow`]: true,\n                    [`nav_main_arrow_active`]: this.isOpen,\n                    [`nav_main_arrow--loading`]: this.loading,\n                  }}\n                ></bds-icon>\n              )}\n            </div>\n          </div>\n        </div>\n        <div\n          class={{\n            accordion: true,\n            accordion_open: this.isOpen && this.navTreeChild,\n          }}\n        >\n          <div class={{ ['container']: true, [`container--disable`]: this.disable }}>\n            <slot></slot>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,04KAA04K;;MCSh5K,OAAO,GAAA,MAAA;AALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAMU,QAAA,IAAU,CAAA,UAAA,GAAgC,IAAI;AAI7C,QAAA,IAAkB,CAAA,kBAAA,GAAa,KAAK;AACpC,QAAA,IAAY,CAAA,YAAA,GAAI,IAAI;AACpB,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AACtC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAe,QAAQ;AACvC;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAChE;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAK5B;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AACrC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjC;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AA4CzB,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAClC,gBAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAE9B,SAAC;AAiGF;AA1IC,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;;IAK9B,MAAM,YAAY,CAAC,MAAM,EAAA;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,MAAM;;AAI7B,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;AAIpB,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAGX,IAAA,aAAa,CAAC,KAAK,EAAA;;AAC3B,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QAClE,IAAI,KAAK,EAAE;YACT,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,QAAQ,EAAE;AACxC,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;;;;IAKnD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,UAAU;AACb,YAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,oBAAoB;AACzD,gBAAA,IAAI,CAAC,OAAO,CAAC,aAA4C;QAC5D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;;AASrF,IAAA,aAAa,CAAC,KAAK,EAAA;QACzB,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;;IAI9B,MAAM,GAAA;QACJ,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,QAAQ,EAAC,GAAG,EAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAC,OAAO,EAAA,EACvEA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,CAAC,CAAmB,iBAAA,CAAA,GAAG,IAAI,CAAC,OAAO;AACpC,aAAA,EAAA,EAEDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,KAAK,EAAE;AACL,gBAAA,QAAQ,EAAE,IAAI;gBACd,eAAe,EAAE,IAAI,CAAC,MAAM;AAC5B,gBAAA,CAAC,CAAmB,iBAAA,CAAA,GAAG,IAAI,CAAC,OAAO;AACnC,gBAAA,CAAC,CAAmB,iBAAA,CAAA,GAAG,IAAI,CAAC,OAAO;aACpC,EAAA,WAAA,EACU,IAAI,CAAC,QAAQ,gBACZ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,IAAI,CAAK,EAAA,EAAA,IAAI,CAAC,aAAa,CAAE,CAAA,CAAC,EAAA,EAExE,IAAI,CAAC,OAAO,IACXA,OAAqB,CAAA,qBAAA,EAAA,EAAA,IAAI,EAAC,aAAa,EAAuB,CAAA,IAC5D,IAAI,CAAC,IAAI,IACXA,OACE,CAAA,UAAA,EAAA,EAAA,KAAK,EAAE;gBACL,CAAC,CAAA,SAAA,CAAW,GAAG,IAAI;AACnB,gBAAA,CAAC,CAAkB,gBAAA,CAAA,GAAG,IAAI,CAAC,MAAM;aAClC,EACD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EACf,KAAK,EAAC,SAAS,EACL,CAAA,KAEZ,EAAE,CACH,EACDA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,eAAe,EAAA,EACvB,IAAI,CAAC,IAAI,KACRA,uEACE,KAAK,EAAE,EAAE,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC,CAAqB,mBAAA,CAAA,GAAG,IAAI,CAAC,OAAO,EAAE,EACtE,OAAO,EAAC,OAAO,EACf,GAAG,EAAC,MAAM,EACE,aAAA,EAAA,OAAO,EACnB,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,WAAW,IAEvC,IAAI,CAAC,IAAI,CACD,CACZ,EACA,IAAI,CAAC,aAAa,KACjBA,OACE,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC,CAAwB,sBAAA,CAAA,GAAG,IAAI,CAAC,OAAO,EAAE,EAC5E,OAAO,EAAC,OAAO,iBACH,OAAO,EACnB,GAAG,EAAC,MAAM,EACV,MAAM,EAAE,KAAK,EAEZ,EAAA,IAAI,CAAC,aAAa,CACV,CACZ,CACG,EACNA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC3BA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,gBAAgB,EAAA,CAAQ,CAC/B,EACL,IAAI,CAAC,YAAY,KAChBA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,IAAI,EAAC,YAAY,EACjB,KAAK,EAAE;gBACL,CAAC,CAAA,cAAA,CAAgB,GAAG,IAAI;AACxB,gBAAA,CAAC,CAAuB,qBAAA,CAAA,GAAG,IAAI,CAAC,MAAM;AACtC,gBAAA,CAAC,CAAyB,uBAAA,CAAA,GAAG,IAAI,CAAC,OAAO;AAC1C,aAAA,EACS,CAAA,CACb,CACG,CACF,CACF,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,cAAc,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY;AACjD,aAAA,EAAA,EAEDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,CAAA,kBAAA,CAAoB,GAAG,IAAI,CAAC,OAAO,EAAE,EAAA,EACvEA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,CACF,CACD;;;;;;;;;;;"}