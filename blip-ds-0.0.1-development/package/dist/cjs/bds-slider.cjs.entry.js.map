{"file": "bds-slider.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,SAAS,GAAG,2nFAA2nF;;MCQhoF,MAAM,GAAA,MAAA;AALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AAYW,QAAA,IAAA,CAAA,UAAU,GAAY,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,MAAA,GAAA,EAAA,CAAA,QAAQ,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,MAAA,GAAA,EAAA,IAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC;AAiB/F;;AAEG;AACK,QAAA,IAAA,CAAA,KAAK,GAAY,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AAEhD;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AAEjC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAa,KAAK;AAC/B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAe,MAAM;AAOjC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AA6CxB,QAAA,IAAA,CAAA,aAAa,GAAG,CAAC,EAAoB,KAAU;AACrD,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACtB,SAAC;AAEO,QAAA,IAAA,CAAA,aAAa,GAAG,CAAC,EAAyB,KAAU;AAC1D,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACtB,SAAC;AAEO,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,EAAe,KAAU;AACjD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACvB,SAAC;AAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,OAAgC,KAAY;YAClE,MAAM,KAAK,GAAG,OAAO;AACrB,YAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;YAC/C,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;YAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC,YAAA,MAAM,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC;AACpD,YAAA,OAAO,UAAU;AACnB,SAAC;AAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,EAAc,KAAU;AAC9C,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;AAClD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAG,EAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG;AAC7D,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK;AAC1E,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AAChC,SAAC;AAEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,MAAW;AACrC,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAoB,kBAAA,CAAA,CAAC;AACtD,SAAC;AAEO,QAAA,IAAiB,CAAA,iBAAA,GAAG,MAAW;AACrC,YAAA,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAoB,kBAAA,CAAA,CAAC;AACzD,SAAC;AAEO,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,KAAa,KAAgB;AACnD,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;;iBACvB;gBACL,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;;AAEvE,SAAC;AAkDF;IArIC,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;gBACxC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;AACnD,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe;;iBAChC;AACL,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW;AACvC,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe;;;aAElC;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAChC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EACjC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CACpC;;;IAIrB,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;;IAEzE,kBAAkB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAG,EAAA,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1D,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG;;aACrB;YACL,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAG,EAAA,IAAI,CAAC,GAAG,CAAA,CAAE,GAAG,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAG,EAAA,IAAI,CAAC,GAAG,CAAA,CAAE,GAAG,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAG,EAAA,IAAI,CAAC,IAAI,CAAA,CAAE,GAAG,EAAE;;;IAI1D,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;AACvE,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACpE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;;IAkD9E,YAAY,CAAC,KAAa,EAAE,GAAY,EAAA;AAC9C,QAAA,MAAM,YAAY,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK;QAC5C,MAAM,UAAU,GAAG,EAAE;AACrB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEpB,QAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;;IAGvF,MAAM,GAAA;AACJ,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OACE,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,IAAI,CAAC,aAAa,EACvB,IAAI,EAAC,OAAO,EACZ,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AAClB,aAAA,EACD,KAAK,EAAE,IAAI,CAAC,KAAe,EAC3B,OAAO,EAAE,IAAI,CAAC,YAAY,EAC1B,YAAY,EAAE,IAAI,CAAC,iBAAiB,EACpC,YAAY,EAAE,IAAI,CAAC,iBAAiB,EACzB,WAAA,EAAA,IAAI,CAAC,QAAQ,EACxB,CAAA,EACFA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,UAAU,EAAA,EACnBA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,CAAc,YAAA,CAAA,GAAG,IAAI,EAAE,CAAC,CAAA,kBAAA,CAAoB,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,EACpF,GAAG,EAAE,IAAI,CAAC,cAAc,EAAA,EAExBA,OACE,CAAA,aAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,GAAG,EAAE,IAAI,CAAC,aAAa,EACvB,KAAK,EAAE,EAAE,CAAC,CAAsB,oBAAA,CAAA,GAAG,IAAI,EAAE,EACzC,QAAQ,EAAC,YAAY,EAAA,cAAA,EACP,IAAI,CAAC,UAAU,EAAA,EAE7BA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,CAAA,kBAAA,CAAoB,GAAG,IAAI,EAAE,EAAQ,CAAA,CACxC,CACV,EACL,IAAI,CAAC,OAAO;YACX,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAEE,OAAK,MAC7BF,iBAAK,GAAG,EAAEE,OAAK,EAAE,KAAK,EAAE,CAAM,IAAA,CAAA,EAC3B,EAAA,IAAI,CAAC,KAAK,IAAIF,sBAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAE,EAAA,CAAG,EAAA,IAAI,CAAC,IAAI,CAAE,CAAA,CAAY,CACnF,CACP,CAAC,CACA,CACD;;;;;;;", "names": ["h", "Host", "index"], "sources": ["src/components/slider/slider.scss?tag=bds-slider&encapsulation=shadow", "src/components/slider/slider.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  position: relative;\n  display: flex;\n  width: 100%;\n  height: 32px;\n}\n\n.track-bg {\n  position: absolute;\n  display: flex;\n  justify-content: space-between;\n  inset: 0 8px;\n  pointer-events: none;\n\n  .progress-bar {\n    position: absolute;\n    height: 4px;\n    border-radius: 1rem;\n    z-index: 2;\n    &-liner {\n      background-color: $color-primary;\n    }\n    &-tooltip {\n      position: absolute;\n      top: -6px;\n      right: -.5rem;\n    }\n    &-thumb {\n      position: relative;\n      width: 1rem;\n      height: 1rem;\n      border-radius: 1rem;\n      background-color: $color-primary;\n      z-index: 0;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        background-color: $color-hover;\n        border-radius: 1rem;\n        transition: all .3s ease-in-out;\n      }\n    }\n\n    &-hover {\n      .progress-bar{\n        &-thumb {\n          &::before {\n            transform: scale(2);\n          }\n        }\n      }\n    }\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    height: 4px;\n    background-color: $color-content-default;\n    opacity: .16;\n    border-radius: 1rem;\n  }\n\n  & .step {\n    position: relative;\n    width: 2px;\n    height: 8px;\n    display: flex;\n    justify-content: center;\n    background-color: $color-content-disable;\n    border-bottom-left-radius: 1rem;\n    border-bottom-right-radius: 1rem;\n    & .label-step {\n      margin-top: 1rem;\n    }\n  }\n}\n\n.element-min {\n  position: relative;\n  height: 4px;\n  background-color: $color-primary;\n  border-top-left-radius: 1rem;\n  border-bottom-left-radius: 1rem;\n}\n.element-max {\n  position: relative;\n  height: 4px;\n  border-top-right-radius: 1rem;\n  border-bottom-right-radius: 1rem;\n}\n\n.input_slide {\n  -webkit-appearance: none;\n  appearance: none;\n  margin: 0;\n  background: transparent;\n  cursor: pointer;\n  width: 100%;\n  height: 4px;\n  position: relative;\n  border-radius: 1rem;\n  background: transparent;\n  color: -internal-light-dark(transparent, transparent);\n\n  &.has_min {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n    margin-left: 0;\n  }\n  \n  &.has_max {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  &:hover{ \n    .input_slide::-webkit-slider-thumb,\n    .input_slide::-moz-range-thumb {\n      -webkit-appearance: none;\n    }\n  }\n\n}\n\n/* Thumb: webkit */\n.input_slide::-webkit-slider-thumb,\n.input_slide::-moz-range-thumb {\n  -webkit-appearance: none;\n  position: relative;\n  height: 16px;\n  width: 16px;\n  border-radius: 50%;\n  border: none;\n}\n\n\n\n.group_slide {\n  position: relative;\n  width: 100%;\n\n  & .input_slide {\n    width: inherit;\n    position: absolute;\n  }\n  \n  & .input_slide_start {\n    left: 0;\n  }\n  & .input_slide_end {\n    right: 0;\n  }\n  & .input_slide::-webkit-slider-thumb,\n  .input_slide::-moz-range-thumb  {\n    -webkit-appearance: none;\n  }\n}\n", "import { Component, Host, h, State, Prop, EventEmitter, Event } from '@stencil/core';\nimport { typeRange, StepOption } from './slider-interface';\n\n@Component({\n  tag: 'bds-slider',\n  styleUrl: 'slider.scss',\n  shadow: true,\n})\nexport class Slider {\n  private inputSlide?: HTMLInputElement;\n  private bdsTooltip?: HTMLBdsTooltipElement;\n  private progressBar?: HTMLElement;\n\n  @State() stepArray?: StepOption[];\n  @State() internalOptions?: StepOption[];\n  @State() inputValue?: string = this.value?.toString() ?? (this.min ? this.min.toString() : '0');\n\n  /**\n   * Step, property to insert steps into the input range.\n   */\n  @Prop() step?: number;\n\n  /**\n   * Min, property to set the minimum value of the range.\n   */\n  @Prop() min?: number;\n\n  /**\n   * Max, property to set the maximum value of the range.\n   */\n  @Prop() max?: number;\n\n  /**\n   * Value, prop to define value of input.\n   */\n  @Prop() value?: number = this.min ? this.min : 0;\n\n  /**\n   * Markers, Prop to enable markers.\n   */\n  @Prop() markers?: boolean = false;\n\n  /**\n   * Label, Prop to enable Label.\n   */\n  @Prop() label?: boolean = false;\n  /**\n   * Type, prop to select type of slider.\n   */\n  @Prop() type?: typeRange = 'fill';\n\n  /**\n   * Data Markers, prop to select ype of markers.\n   */\n  @Prop() dataMarkers?: string | StepOption[];\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  /**\n   * bdsChange. Event to return selected date value.\n   */\n  @Event() bdsChange?: EventEmitter;\n\n  componentWillLoad() {\n    if (this.dataMarkers) {\n      if (typeof this.dataMarkers === 'string') {\n        this.internalOptions = JSON.parse(this.dataMarkers);\n        this.stepArray = this.internalOptions;\n      } else {\n        this.internalOptions = this.dataMarkers;\n        this.stepArray = this.internalOptions;\n      }\n    } else {\n      this.stepArray = this.arrayToSteps(\n        (this.max - this.min) / this.step,\n        Number.isInteger((this.max - this.min) / this.step),\n      ) as StepOption[];\n    }\n  }\n\n  componentDidLoad() {\n    this.progressBar.style.width = `${this.valuePercent(this.inputSlide)}%`;\n  }\n  componentDidRender() {\n    if (this.internalOptions) {\n      this.inputSlide.min = '0';\n      this.inputSlide.max = `${this.internalOptions.length - 1}`;\n      this.inputSlide.step = '1';\n    } else {\n      this.inputSlide.min = this.min ? `${this.min}` : '';\n      this.inputSlide.max = this.max ? `${this.max}` : '';\n      this.inputSlide.step = this.step ? `${this.step}` : '';\n    }\n  }\n\n  componentDidUpdate() {\n    this.progressBar.style.width = `${this.valuePercent(this.inputSlide)}%`;\n    const valueName = this.emiterChange(parseInt(this.inputSlide.value));\n    this.inputValue = this.stepArray.length > 0 ? valueName.name : this.inputSlide.value;\n  }\n\n  private refInputSlide = (el: HTMLInputElement): void => {\n    this.inputSlide = el;\n  };\n\n  private refBdsTooltip = (el: HTMLBdsTooltipElement): void => {\n    this.bdsTooltip = el;\n  };\n\n  private refProgressBar = (el: HTMLElement): void => {\n    this.progressBar = el;\n  };\n\n  private valuePercent = (element: HTMLInputElement | null): number => {\n    const input = element;\n    const min = input.min ? parseInt(input.min) : 0;\n    const max = parseInt(input.max);\n    const val = parseInt(input.value);\n    const percentage = ((val - min) * 100) / (max - min);\n    return percentage;\n  };\n\n  private onInputSlide = (ev: InputEvent): void => {\n    const input = ev.target as HTMLInputElement | null;\n    this.progressBar.style.width = `${this.valuePercent(input)}%`;\n    const valueName = this.emiterChange(parseInt(input.value));\n    this.inputValue = this.stepArray.length > 0 ? valueName.name : input.value;\n    this.bdsChange.emit(valueName);\n  };\n\n  private onInputMouseEnter = (): void => {\n    this.bdsTooltip.visible();\n    this.progressBar.classList.add(`progress-bar-hover`);\n  };\n\n  private onInputMouseLeave = (): void => {\n    this.bdsTooltip.invisible();\n    this.progressBar.classList.remove(`progress-bar-hover`);\n  };\n\n  private emiterChange = (value: number): StepOption => {\n    if (this.internalOptions) {\n      return this.stepArray[value];\n    } else {\n      return this.stepArray.find((item) => parseInt(item.name) === value);\n    }\n  };\n\n  private arrayToSteps(value: number, int: boolean): unknown {\n    const numberToCalc = int ? value + 1 : value;\n    const valueSteps = [];\n    for (let i = 0; i < numberToCalc; i++) {\n      valueSteps.push(i);\n    }\n    return valueSteps.map((term) => ({ value: term, name: term * this.step + this.min }));\n  }\n\n  render() {\n    return (\n      <Host>\n        <input\n          ref={this.refInputSlide}\n          type=\"range\"\n          class={{\n            input_slide: true,\n          }}\n          value={this.value as number}\n          onInput={this.onInputSlide}\n          onMouseEnter={this.onInputMouseEnter}\n          onMouseLeave={this.onInputMouseLeave}\n          data-test={this.dataTest}\n        />\n        <div class=\"track-bg\">\n          <div\n            class={{ [`progress-bar`]: true, [`progress-bar-liner`]: this.type !== 'no-linear' }}\n            ref={this.refProgressBar}\n          >\n            <bds-tooltip\n              ref={this.refBdsTooltip}\n              class={{ [`progress-bar-tooltip`]: true }}\n              position=\"top-center\"\n              tooltip-text={this.inputValue}\n            >\n              <div class={{ [`progress-bar-thumb`]: true }}></div>\n            </bds-tooltip>\n          </div>\n          {this.markers &&\n            this.stepArray.map((item, index) => (\n              <div key={index} class={`step`}>\n                {this.label && <bds-typo class=\"label-step\" variant=\"fs-10\">{`${item.name}`}</bds-typo>}\n              </div>\n            ))}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}