'use strict';

var index = require('./index-D_zq0Z7d.js');

const tableHeaderCss = ".sc-bds-table-header-h{display:table-header-group;border-bottom:1px solid var(--color-border-1, rgba(0, 0, 0, 0.2))}";

const TableHeader = class {
    constructor(hostRef) {
        index.registerInstance(this, hostRef);
    }
    render() {
        return (index.h(index.Host, { key: '0d9f9d4edd7a5bcc9841762b88d90db8e97bd1ea' }, index.h("slot", { key: '1bc9ca332c14b831b7a4ab96249436dad9acc6a6' })));
    }
};
TableHeader.style = tableHeaderCss;

exports.bds_table_header = TableHeader;
//# sourceMappingURL=bds-table-header.entry.cjs.js.map

//# sourceMappingURL=bds-table-header.cjs.entry.js.map