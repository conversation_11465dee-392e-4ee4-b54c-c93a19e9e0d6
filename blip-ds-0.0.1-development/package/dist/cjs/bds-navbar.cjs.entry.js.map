{"file": "bds-navbar.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,SAAS,GAAG,2wDAA2wD;;MCUhxD,MAAM,GAAA,MAAA;AALnB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAQE;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAiB,UAAU;AAE9C;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAsB,WAAW;AAExD;;AAEG;AACK,QAAA,IAAc,CAAA,cAAA,GAAoB,eAAe;AAEzD;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAmBjC;IAjBC,MAAM,GAAA;AACJ,QAAA,QACEA,OAAC,CAAAC,UAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAA,CAAE,GAAG,IAAI,EAAE,EAAA,EAC5CD,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,MAAM,EAAE,IAAI;AACZ,gBAAA,CAAC,4BAA4B,IAAI,CAAC,cAAc,CAAE,CAAA,GAAG,IAAI;AACzD,gBAAA,CAAC,wBAAwB,IAAI,CAAC,WAAW,CAAE,CAAA,GAAG,IAAI;AAClD,gBAAA,CAAC,6BAA6B,IAAI,CAAC,eAAe,CAAE,CAAA,GAAG,IAAI;aAC5D,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAExBA,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACJ,CACD;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/navbar/navbar.scss?tag=bds-navbar&encapsulation=shadow", "src/components/navbar/navbar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n}\n\n:host(.horizontal) {\n  width: 100%;\n  height: fit-content;\n}\n\n:host(.vertical) {\n  width: fit-content;\n  height: 100%;\n}\n\n.navbar {\n  width: fit-content;\n  display: flex;\n  gap: 8px;\n  box-sizing: border-box;\n\n  ::slotted(*) {\n    display: flex;\n    gap: 8px;\n    align-items: center;\n  }\n\n  &__justify-content {\n    &__flex-start {\n      justify-content: flex-start;\n    }\n    &__center {\n      justify-content: center;\n    }\n    &__flex-end {\n      justify-content: flex-end;\n    }\n    &__space-between {\n      justify-content: space-between;\n    }\n    &__space-around {\n      justify-content: space-around;\n    }\n    &__space-evenly {\n      justify-content: space-evenly;\n    }\n  }\n\n  &__orientation {\n    &__horizontal {\n      flex-direction: row;\n      width: 100%;\n      padding: 8px 16px;\n      ::slotted(*) {\n        flex-direction: row;\n      }\n    }\n    &__vertical {\n      flex-direction: column;\n      height: 100%;\n      padding: 16px 8px;\n      ::slotted(*) {\n        flex-direction: column;\n      }\n    }\n  }\n  \n  &__background-color {\n    &__surface-1 {\n      background-color: $color-surface-1;\n    }\n    &__surface-2 {\n      background-color: $color-surface-2;\n    }\n    &__surface-3 {\n      background-color: $color-surface-3;\n    }\n    &__surface-4 {\n      background-color: $color-surface-4;\n    }\n  }\n}\n", "import { Component, h, Host, Prop, Element } from '@stencil/core';\n\nexport type orientation = 'horizontal' | 'vertical';\nexport type navbarBackground = 'surface-1' | 'surface-2' | 'surface-3';\nexport type justifyContent = 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';\n@Component({\n  tag: 'bds-navbar',\n  styleUrl: 'navbar.scss',\n  shadow: true,\n})\nexport class Navbar {\n  @Element() hostElement: HTMLElement;\n\n  /**\n   * Navbar orientation. Used to orientation the navbar. Either on the left or on the right.\n   */\n  @Prop() orientation?: orientation = 'vertical';\n\n  /**\n   * Width, number to define navbar width.\n   */\n  @Prop() backgroundColor?: navbarBackground = 'surface-1';\n\n  /**\n   * Justify Content. Used to align itens in navbar.\n   */\n  @Prop() justifyContent?: justifyContent = 'space-between';\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    return (\n      <Host class={{ [`${this.orientation}`]: true }}>\n        <div\n          class={{\n            navbar: true,\n            [`navbar__justify-content__${this.justifyContent}`]: true,\n            [`navbar__orientation__${this.orientation}`]: true,\n            [`navbar__background-color__${this.backgroundColor}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          <slot />\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}