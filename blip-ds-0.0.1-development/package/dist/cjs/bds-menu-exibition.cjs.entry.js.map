{"file": "bds-menu-exibition.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,gBAAgB,GAAG,gnBAAgnB;;MCS5nB,gBAAgB,GAAA,MAAA;AAL7B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAY,IAAI;AAClC;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AACvC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAgB,UAAU;AAC5C;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,IAAI;AAC7B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,IAAI;AACnC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAa,KAAK;AAuCnC;IArCC,MAAM,GAAA;QACJ,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe;QACzD,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,aAAa,EAAE,IAAI;AACnB,gBAAA,CAAC,CAAyB,uBAAA,CAAA,GAAG,IAAI,CAAC,QAAQ;AAC3C,aAAA,EAAA,EAEA,SAAS,KACRA,OAAA,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAC,aAAa,EACnB,IAAI,EAAE,IAAI,CAAC,UAAU,EACrB,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,EAAA,CACT,CACf,EACDA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,cAAc,EAAA,EACtB,IAAI,CAAC,KAAK,KACTA,uEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACpD,IAAI,CAAC,KAAK,CACF,CACZ,EACA,IAAI,CAAC,QAAQ,KACZA,uEAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,IACvD,IAAI,CAAC,QAAQ,CACL,CACZ,EACA,IAAI,CAAC,WAAW,KACfA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,kBAAkB,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAA,EAC1D,IAAI,CAAC,WAAW,CACR,CACZ,CACG,CACF;;;;;;;", "names": ["h"], "sources": ["src/components/menu/menu-exibition/menu-exibition.scss?tag=bds-menu-exibition&encapsulation=shadow", "src/components/menu/menu-exibition/menu-exibition.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n.menuexibition {\n  display: flex;\n  align-items: center;\n  padding: 16px;\n\n  &__disabled {\n    opacity: 0.5;\n    cursor: no-drop;\n  }\n\n  & .avatar-item {\n    display: block;\n    margin-right: 8px;\n  }\n\n  & .content-item {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n\n    & .title-item {\n      color: $color-content-default;\n    }\n\n    & .subtitle-item {\n      color: $color-content-disable;\n    }\n\n    & .description-item {\n      color: $color-content-default;\n    }\n  }\n}\n", "import { Component, h, Prop } from '@stencil/core';\n\nexport type avatarSize = 'extra-small' | 'small' | 'standard';\n\n@Component({\n  tag: 'bds-menu-exibition',\n  styleUrl: 'menu-exibition.scss',\n  shadow: true,\n})\nexport class BdsMenuExibition {\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * AvatarSize. Used to set avatar size.\n   */\n  @Prop() avatarSize?: avatarSize = 'standard';\n  /**\n   * Value. Used to insert a title in the display item.\n   */\n  @Prop() value?: string = null;\n  /**\n   * Subtitle. Used to insert a subtitle in the display item.\n   */\n  @Prop() subtitle?: string = null;\n  /**\n   * Description. Used to insert a subtitle in the display item.\n   */\n  @Prop() description?: string = null;\n  /**\n   * Disabled. Used to declare that the item will be disabled.\n   */\n  @Prop() disabled?: boolean = false;\n\n  render() {\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <div\n        class={{\n          menuexibition: true,\n          [`menuexibition__disabled`]: this.disabled,\n        }}\n      >\n        {hasAvatar && (\n          <bds-avatar\n            class=\"avatar-item\"\n            name={this.avatarName}\n            thumbnail={this.avatarThumbnail}\n            size={this.avatarSize}\n          ></bds-avatar>\n        )}\n        <div class=\"content-item\">\n          {this.value && (\n            <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\">\n              {this.value}\n            </bds-typo>\n          )}\n          {this.subtitle && (\n            <bds-typo class=\"subtitle-item\" variant=\"fs-10\" tag=\"span\">\n              {this.subtitle}\n            </bds-typo>\n          )}\n          {this.description && (\n            <bds-typo class=\"description-item\" variant=\"fs-10\" tag=\"span\">\n              {this.description}\n            </bds-typo>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "version": 3}