{"version": 3, "file": "bds-list-item.entry.cjs.js", "sources": ["src/components/list/list.scss?tag=bds-list-item&encapsulation=shadow", "src/components/list/list-item.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n}\n\n:host(.list_item_content) {\n    display: flex;\n    width: fit-content;\n}\n\n.list {\n  &_item {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    &_tall {\n      padding: 16px;\n    }\n    &_standard {\n      padding: 8px 16px;\n    }\n    &_short {\n      padding: 8px;\n    }\n\n    & .input_list {\n      position: relative;\n    }\n\n    & .avatar-item {\n      position: relative;\n      display: block;\n    }\n\n    & .icon-item {\n      position: relative;\n      color: $color-content-default;\n\n      &-active {\n        color: $color-primary;\n      }\n    }\n\n    & .grow-up {\n      position: relative;\n      flex-grow: 2;\n    }\n\n    & .content-slot {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    & .content-item {\n      position: relative;\n      display: flex;\n      gap: 2px;\n      flex-direction: column;\n\n      & .title-item {\n        color: $color-content-default;\n      }\n\n      & .subtitle-item {\n        color: $color-content-default;\n      }\n    }\n\n    .content-area {\n      position: relative;\n      flex-grow: 2;\n      .internal-chips,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        gap: 8px;\n      }\n    }\n\n    .action-area {\n      position: relative;\n      .internal-actions-buttons,\n      ::slotted(*) {\n        display: flex;\n        flex-direction: row;\n        color: $color-content-default;\n      }\n    }\n\n    & .icon-arrow {\n      -webkit-transition: all ease 0.3s;\n      -moz-transition: all ease 0.3s;\n      transition: all ease 0.3s;\n      transform: rotate(0deg);\n\n      &-active {\n        transform: rotate(180deg);\n      }\n    }\n  }\n}\n\n.border_radius {\n  border-radius: 8px;\n  &:before, &:after, .active {\n    border-radius: 8px;\n  }\n}\n.active {\n  position: absolute;\n  background-color: $color-content-default;\n  opacity: 0.08;\n  inset: 0;\n}\n\n.clickable {\n  position: relative;\n  cursor: pointer;\n  gap: 8px;\n  &:before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  @include hover-and-pressed();\n}\n", "import { Element, Component, Host, h, State, Prop, Event, EventEmitter, Watch } from '@stencil/core';\nimport { TypeList } from './list';\nexport type ItemSize = 'tall' | 'standard' | 'short';\n@Component({\n  tag: 'bds-list-item',\n  styleUrl: 'list.scss',\n  shadow: true,\n})\nexport class ListItem {\n  private hasActionAreaSlot: boolean;\n  private hasContentAreaSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() internalChips: string[] = [];\n\n  @State() internalActionsButtons: string[] = [];\n\n  @Prop({ mutable: true, reflect: true }) checked?: boolean = false;\n  /**\n   * Typelis. Used toselect type of item list.\n   */\n  @Prop() typeList?: TypeList = null;\n  /**\n   * AvatarName. Used to enter the avatar name.\n   */\n  @Prop() avatarName?: string = null;\n  /**\n   * AvatarThumbnail. Used to insert the avatar photo.\n   */\n  @Prop() avatarThumbnail?: string = null;\n  /**\n   * Icon. Used to add icon in list item.\n   */\n  @Prop() icon?: string = null;\n  /**\n   * Value. Used to insert a value in list item.\n   */\n  @Prop() value: string = null;\n  /**\n   * Text. Used to insert a text in the display item.\n   */\n  @Prop() text?: string = null;\n  /**\n   * SecondaryText. Used to insert a secondaryText in the display item.\n   */\n  @Prop() secondaryText?: string = null;\n\n  /**\n   * The chips on the component\n   * Should be passed this way:\n   * chips='[\"chip1\", \"chip2\"]'\n   */\n  @Prop({ mutable: true }) chips: string | string[] = [];\n\n  /**\n   * The actions buttons on the component\n   * Should be passed this way:\n   * actions-buttons='[\"copy\", \"settings-general\", \"more-options-horizontal\"]'\n   */\n  @Prop({ mutable: true }) actionsButtons: string | string[] = [];\n\n  /**\n   * Clickable. Used to define if the item is clickable or not.\n   */\n  @Prop() clickable?: boolean = false;\n\n  /**\n   * Active. Used to define when the item is highlighted.\n   */\n  @Prop() active?: boolean = false;\n  /**\n   * Enable rounded border on item\n   */\n  @Prop() borderRadius?: boolean = false;\n\n  /**\n   * Size. Entered as one of the size. Can be one of:\n   * 'tall', 'standard', 'short';\n   */\n  @Prop() size?: ItemSize = 'standard';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Emitted when the value has changed because of a click event.\n   */\n  @Event() bdsChecked!: EventEmitter;\n\n  /**\n   * Emitted when click in someone actions buttom insert in data.\n   */\n  @Event() bdsClickActionButtom!: EventEmitter;\n\n  componentWillLoad() {\n    this.hasActionAreaSlot = !!this.hostElement.querySelector('[slot=\"action-area\"]');\n    this.hasContentAreaSlot = !!this.hostElement.querySelector('[slot=\"content-area\"]');\n    this.chipsChanged();\n    this.actionsButtonsChanged();\n  }\n\n  @Watch('checked')\n  protected checkedChanged(isChecked: boolean): void {\n    this.bdsChecked.emit({\n      value: this.value,\n      text: this.text,\n      secondaryText: this.secondaryText,\n      typeList: this.typeList,\n      checked: isChecked,\n    });\n  }\n\n  @Watch('chips')\n  protected chipsChanged(): void {\n    if (this.chips) {\n      if (typeof this.chips === 'string') {\n        this.internalChips = JSON.parse(this.chips);\n      } else {\n        this.internalChips = this.chips;\n      }\n    } else {\n      this.internalChips = [];\n    }\n  }\n\n  @Watch('actionsButtons')\n  protected actionsButtonsChanged(): void {\n    if (this.actionsButtons) {\n      if (typeof this.actionsButtons === 'string') {\n        this.internalActionsButtons = JSON.parse(this.actionsButtons);\n      } else {\n        this.internalActionsButtons = this.actionsButtons;\n      }\n    } else {\n      this.internalActionsButtons = [];\n    }\n  }\n\n  private handler = (): void => {\n    this.typeList == 'radio' ? (this.checked = true) : (this.checked = !this.checked);\n  };\n\n  private clickActionButtons = (data, event): void => {\n    const elementButton = event.composedPath()[0];\n    this.bdsClickActionButtom.emit({\n      value: this.value,\n      icon: data,\n      elementButton: elementButton,\n    });\n  };\n\n  private renderChips() {\n    if (!this.internalChips.length) {\n      return [];\n    }\n\n    return this.internalChips.map((chip, index) => {\n      const id = index.toString();\n      const limit = 30;\n      if (chip.length <= limit) {\n        return (\n          <bds-chip-clickable id={id} key={id} color=\"default\">\n            {chip}\n          </bds-chip-clickable>\n        );\n      } else {\n        return (\n          <bds-tooltip key={id} position=\"top-center\" tooltip-text={chip}>\n            <bds-chip-clickable id={id} key={id} color=\"default\">\n              {`${chip.slice(0, limit)} ...`}\n            </bds-chip-clickable>\n          </bds-tooltip>\n        );\n      }\n    });\n  }\n\n  private renderActionsButtons() {\n    if (!this.internalActionsButtons.length) {\n      return [];\n    }\n\n    return this.internalActionsButtons.map((button, index) => {\n      const id = index.toString();\n      return (\n        <bds-button-icon\n          key={id}\n          variant=\"secondary\"\n          icon={button}\n          size=\"short\"\n          onClick={(ev) => this.clickActionButtons(button, ev)}\n        ></bds-button-icon>\n      );\n    });\n  }\n\n  render() {\n    const hasInput =\n      this.clickable == true || this.typeList == 'checkbox' || this.typeList == 'radio' || this.typeList == 'switch';\n    const hasLeftInput = this.typeList == 'checkbox' || this.typeList == 'radio';\n    const hasAvatar = this.avatarName || this.avatarThumbnail;\n    return (\n      <Host>\n        <div\n          onClick={this.handler}\n          tabindex=\"0\"\n          class={{\n            list_item: true,\n            clickable: hasInput,\n            border_radius: this.borderRadius,\n            [`list_item_${this.size}`]: true,\n          }}\n          data-test={this.dataTest}\n        >\n          {this.active && <div class=\"active\"></div>}\n          {hasLeftInput && (\n            <div class={{ input_list: true }}>\n              {this.typeList == 'radio' && <bds-radio value={this.value} checked={this.checked}></bds-radio>}\n              {this.typeList == 'checkbox' && (\n                <bds-checkbox refer=\"\" label=\"\" name=\"cb1\" disabled={false} checked={this.checked}></bds-checkbox>\n              )}\n            </div>\n          )}\n          {hasAvatar ? (\n            <bds-avatar\n              class=\"avatar-item\"\n              name={this.avatarName}\n              thumbnail={this.avatarThumbnail}\n              size=\"extra-small\"\n            ></bds-avatar>\n          ) : (\n            this.icon && (\n              <bds-icon\n                class={{\n                  [`icon-item`]: true,\n                  [`icon-item-active`]: this.active,\n                }}\n                size=\"medium\"\n                name={this.icon}\n                color=\"inherit\"\n                theme={this.active ? 'solid' : 'outline'}\n              ></bds-icon>\n            )\n          )}\n          <div class={{ [`content-slot`]: true }}>\n            <slot></slot>\n          </div>\n          {(this.text || this.secondaryText) && (\n            <div\n              class={{\n                [`content-item`]: true,\n                [`grow-up`]: !this.hasActionAreaSlot && !this.hasContentAreaSlot && this.internalChips.length < 0,\n              }}\n            >\n              {this.text && (\n                <bds-typo class=\"title-item\" variant=\"fs-16\" tag=\"span\" bold={this.active ? 'bold' : 'regular'}>\n                  {this.text}\n                </bds-typo>\n              )}\n              {this.secondaryText && (\n                <bds-typo class=\"subtitle-item\" variant=\"fs-12\" line-height=\"small\" tag=\"span\">\n                  {this.secondaryText}\n                </bds-typo>\n              )}\n            </div>\n          )}\n          <div class={{ [`content-area`]: true, [`grow-up`]: true }}>\n            {this.internalChips.length > 0 && <div class=\"internal-chips\">{this.renderChips()}</div>}\n            <slot name=\"content-area\"></slot>\n          </div>\n          {(!this.typeList || this.typeList == 'default') && (\n            <div class={{ [`action-area`]: true }}>\n              {this.internalActionsButtons.length > 0 && (\n                <div class=\"internal-actions-buttons\">{this.renderActionsButtons()}</div>\n              )}\n              <slot name=\"action-area\"></slot>\n            </div>\n          )}\n          {this.typeList == 'switch' && <bds-switch refer=\"\" name=\"\" checked={this.checked}></bds-switch>}\n        </div>\n      </Host>\n    );\n  }\n}\n"], "names": ["index", "h", "Host"], "mappings": ";;;;AAAA,MAAM,OAAO,GAAG,i1EAAi1E;;MCQp1E,QAAQ,GAAA,MAAA;AALrB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;AAWW,QAAA,IAAa,CAAA,aAAA,GAAa,EAAE;AAE5B,QAAA,IAAsB,CAAA,sBAAA,GAAa,EAAE;AAEN,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAc,IAAI;AAClC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAY,IAAI;AAClC;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AACvC;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC5B;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAW,IAAI;AAC5B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,IAAI;AAC5B;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AAErC;;;;AAIG;AACsB,QAAA,IAAK,CAAA,KAAA,GAAsB,EAAE;AAEtD;;;;AAIG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAsB,EAAE;AAE/D;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AAEnC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAChC;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAa,KAAK;AAEtC;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAc,UAAU;AACpC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAuDxB,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;YAC3B,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AACnF,SAAC;QAEO,IAAA,CAAA,kBAAkB,GAAG,CAAC,IAAI,EAAE,KAAK,KAAU;YACjD,MAAM,aAAa,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC7C,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,gBAAA,IAAI,EAAE,IAAI;AACV,gBAAA,aAAa,EAAE,aAAa;AAC7B,aAAA,CAAC;AACJ,SAAC;AAsIF;IA7LC,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,sBAAsB,CAAC;AACjF,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC;QACnF,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,qBAAqB,EAAE;;AAIpB,IAAA,cAAc,CAAC,SAAkB,EAAA;AACzC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC;;IAIM,YAAY,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;iBACtC;AACL,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK;;;aAE5B;AACL,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;IAKjB,qBAAqB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAC3C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;;iBACxD;AACL,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc;;;aAE9C;AACL,YAAA,IAAI,CAAC,sBAAsB,GAAG,EAAE;;;IAiB5B,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AAC9B,YAAA,OAAO,EAAE;;QAGX,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAEA,OAAK,KAAI;AAC5C,YAAA,MAAM,EAAE,GAAGA,OAAK,CAAC,QAAQ,EAAE;YAC3B,MAAM,KAAK,GAAG,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;gBACxB,QACEC,OAAoB,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAC,SAAS,IACjD,IAAI,CACc;;iBAElB;gBACL,QACEA,OAAa,CAAA,aAAA,EAAA,EAAA,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAC,YAAY,EAAA,cAAA,EAAe,IAAI,EAAA,EAC5DA,OAAoB,CAAA,oBAAA,EAAA,EAAA,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAC,SAAS,EAAA,EACjD,CAAA,EAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAM,IAAA,CAAA,CACX,CACT;;AAGpB,SAAC,CAAC;;IAGI,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;AACvC,YAAA,OAAO,EAAE;;QAGX,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAED,OAAK,KAAI;AACvD,YAAA,MAAM,EAAE,GAAGA,OAAK,CAAC,QAAQ,EAAE;AAC3B,YAAA,QACEC,OAAA,CAAA,iBAAA,EAAA,EACE,GAAG,EAAE,EAAE,EACP,OAAO,EAAC,WAAW,EACnB,IAAI,EAAE,MAAM,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAA,CACnC;AAEvB,SAAC,CAAC;;IAGJ,MAAM,GAAA;QACJ,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ;AAChH,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO;QAC5E,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe;AACzD,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAC,GAAG,EACZ,KAAK,EAAE;AACL,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,SAAS,EAAE,QAAQ;gBACnB,aAAa,EAAE,IAAI,CAAC,YAAY;AAChC,gBAAA,CAAC,aAAa,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AACjC,aAAA,EACU,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EAEvB,IAAI,CAAC,MAAM,IAAIA,kEAAK,KAAK,EAAC,QAAQ,EAAO,CAAA,EACzC,YAAY,KACXA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAA,EAC7B,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAIA,wEAAW,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAc,CAAA,EAC7F,IAAI,CAAC,QAAQ,IAAI,UAAU,KAC1BA,OAAc,CAAA,cAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,IAAI,EAAC,KAAK,EAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAiB,CAAA,CACnG,CACG,CACP,EACA,SAAS,IACRA,OAAA,CAAA,YAAA,EAAA,EACE,KAAK,EAAC,aAAa,EACnB,IAAI,EAAE,IAAI,CAAC,UAAU,EACrB,SAAS,EAAE,IAAI,CAAC,eAAe,EAC/B,IAAI,EAAC,aAAa,EAAA,CACN,KAEd,IAAI,CAAC,IAAI,KACPA,OAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAE;gBACL,CAAC,CAAA,SAAA,CAAW,GAAG,IAAI;AACnB,gBAAA,CAAC,CAAkB,gBAAA,CAAA,GAAG,IAAI,CAAC,MAAM;AAClC,aAAA,EACD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,SAAS,EAAA,CAC9B,CACb,CACF,EACDA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,CAAC,CAAc,YAAA,CAAA,GAAG,IAAI,EAAE,EAAA,EACpCA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAa,CACT,EACL,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,MAC/BA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;gBACL,CAAC,CAAA,YAAA,CAAc,GAAG,IAAI;AACtB,gBAAA,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;AAClG,aAAA,EAAA,EAEA,IAAI,CAAC,IAAI,KACRA,uEAAU,KAAK,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,EAAC,GAAG,EAAC,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,SAAS,EAC3F,EAAA,IAAI,CAAC,IAAI,CACD,CACZ,EACA,IAAI,CAAC,aAAa,KACjBA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,OAAO,iBAAa,OAAO,EAAC,GAAG,EAAC,MAAM,EAAA,EAC3E,IAAI,CAAC,aAAa,CACV,CACZ,CACG,CACP,EACDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,CAAC,CAAc,YAAA,CAAA,GAAG,IAAI,EAAE,CAAC,CAAS,OAAA,CAAA,GAAG,IAAI,EAAE,EAAA,EACtD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAIA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,gBAAgB,EAAE,EAAA,IAAI,CAAC,WAAW,EAAE,CAAO,EACxFA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,cAAc,EAAA,CAAQ,CAC7B,EACL,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,MAC5CA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,CAAC,CAAa,WAAA,CAAA,GAAG,IAAI,EAAE,EAAA,EAClC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,KACrCA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAC,0BAA0B,EAAE,EAAA,IAAI,CAAC,oBAAoB,EAAE,CAAO,CAC1E,EACDA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,aAAa,EAAA,CAAQ,CAC5B,CACP,EACA,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAIA,OAAY,CAAA,YAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAe,CAAA,CAC3F,CACD;;;;;;;;;;;;;"}