{"file": "bds-data-table.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,YAAY,GAAG,ogDAAogD;;MCU5gD,SAAS,GAAA,MAAA;AALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;AAOW,QAAA,IAAQ,CAAA,QAAA,GAAS,EAAE;AAC5B;;AAEG;AACM,QAAA,IAAU,CAAA,UAAA,GAAU,EAAE;AAC/B;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAiBhC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAChC;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAa,KAAK;AAK/B;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AA2JlC;IAtJC,iBAAiB,GAAA;QACf,IAAI,CAAC,mBAAmB,EAAE;;IAGpB,mBAAmB,GAAA;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;AAG3C,IAAA,WAAW,CAAC,KAAK,EAAA;QACf,IAAI,KAAK,EAAE;AACT,YAAA,OAAOA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,OAAO,EAAA,CAAY;;aACpD;AACL,YAAA,OAAO,IAAI;;;IAKf,MAAM,UAAU,CAAC,KAAa,EAAA;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;AAG1C,IAAA,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;;AAGxE,IAAA,WAAW,CAAC,GAAG,EAAA;AACb,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,GAAG,IAAI;AAEtD,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;AAChC,gBAAA,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;AACjC,aAAC,CAAC;;aACG;YACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAA;AAChC,gBAAA,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACjC,aAAC,CAAC;;;IAIN,MAAM,GAAA;AACJ,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,OAAO,EAAA,EAClBA,OAAO,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,OAAO,EAAA,EAClBA,OAAI,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,QAAQ,EACf,EAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAEE,OAAK,MAC/BF,OAAA,CAAA,IAAA,EAAA,EAAI,KAAK,EAAC,cAAc,EAAC,GAAG,EAAEE,OAAK,EAAA,EAChC,IAAI,CAAC,OAAO,IACXF,OAAA,CAAA,UAAA,EAAA,EACE,KAAK,EAAC,aAAa,EACnB,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAC3C,OAAO,EAAC,OAAO,EACf,IAAI,EAAE,IAAI,CAAC,YAAY,KAAK,CAAG,EAAA,IAAI,CAAC,KAAK,CAAA,CAAE,GAAG,MAAM,GAAG,WAAW,EAAA,EAEjE,IAAI,CAAC,OAAO,CACJ,KAEXA,OAAU,CAAA,UAAA,EAAA,EAAA,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,EACvC,EAAA,IAAI,CAAC,OAAO,CACJ,CACZ,EACA,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,CAAA,EAAG,IAAI,CAAC,KAAK,CAAE,CAAA,IAC5FA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,aAAa,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,OAAO,EAAA,CAAY,IACpE,IAAI,CAAC,aAAa,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,CAAG,EAAA,IAAI,CAAC,KAAK,EAAE,IAChGA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,YAAY,EAAC,IAAI,EAAC,OAAO,EAAY,CAAA,KAEpD,EAAE,CACH,CACE,CACN,CAAC,CACC,CACC,EACRA,OAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAEE,OAAK,MAC9BF,gBAAI,KAAK,EAAC,UAAU,EAAC,GAAG,EAAEE,OAAK,EAAA,EAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,KAAI;AACvC,YAAA,QACEF,gBAAI,KAAK,EAAC,WAAW,EAAC,GAAG,EAAE,GAAG,EAAA,EAC3B,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,IACvCA,OACE,CAAA,iBAAA,EAAA,EAAA,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAEE,OAAK,EAAE,UAAU,CAAC,UAAU,CAAC,EACnE,OAAO,EAAC,WAAW,EACnB,IAAI,EAAE,IAAI,CAAC,CAAA,EAAG,UAAU,CAAC,UAAU,EAAE,CAAC,EACtC,IAAI,EAAC,OAAO,EAAA,CACK,KAEnB,EAAE,CACH,EACA,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,YAAY,IACzCF,OACE,CAAA,iBAAA,EAAA,EAAA,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAEE,OAAK,EAAE,UAAU,CAAC,YAAY,CAAC,EACrE,OAAO,EAAC,WAAW,EACnB,IAAI,EAAE,IAAI,CAAC,CAAG,EAAA,UAAU,CAAC,YAAY,CAAA,CAAE,CAAC,EACxC,IAAI,EAAC,OAAO,EAAA,CACK,KAEnB,EAAE,CACH,EACA,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,YAAY,IACzCF,OACE,CAAA,iBAAA,EAAA,EAAA,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAEE,OAAK,EAAE,UAAU,CAAC,YAAY,CAAC,EACrE,OAAO,EAAC,WAAW,EACnB,IAAI,EAAE,IAAI,CAAC,CAAG,EAAA,UAAU,CAAC,YAAY,EAAE,CAAC,EACxC,IAAI,EAAC,OAAO,EAAA,CACK,KAEnB,EAAE,CACH,EACA,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,IAC7BF,OAAc,CAAA,cAAA,EAAA,EAAA,KAAK,EAAE,IAAI,CAAC,CAAG,EAAA,UAAU,CAAC,KAAK,CAAA,CAAE,CAAC,GAAG,IAAI,CAAC,CAAG,EAAA,UAAU,CAAC,KAAK,CAAA,CAAE,CAAC,GAAG,SAAS,EACvF,EAAA,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAE,CAAA,CAAC,CACf,KAEf,EAAE,CACH,EACA,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,GAAG,IAC5BA,OACE,CAAA,YAAA,EAAA,EAAA,IAAI,EAAC,aAAa,EAClB,SAAS,EAAE,IAAI,CAAC,CAAG,EAAA,UAAU,CAAC,GAAG,CAAA,CAAE,CAAC,EACpC,IAAI,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAA,CAAE,CAAC,GACrB,KAEd,EAAE,CACH,EACA,UAAU,CAAC,KAAK,IACf,EAAE,KAEFA,OACE,CAAA,UAAA,EAAA,EAAA,OAAO,EAAC,OAAO,EACf,IAAI,EAAE,IAAI,CAAC,YAAY,KAAK,CAAG,EAAA,UAAU,CAAC,KAAK,CAAA,CAAE,GAAG,MAAM,GAAG,SAAS,EAAA,EAErE,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAE,CAAA,CAAC,CACnB,CACZ,CACE;SAER,CAAC,CACC,CACN,CAAC,CACI,CACF,CACH;;;;;;;;", "names": ["h", "Host", "index"], "sources": ["src/components/table/data-table.scss?tag=bds-data-table&encapsulation=shadow", "src/components/table/data-table.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n  width: 100%;\n\n  .table {\n    display: grid;\n    font-family: $font-family;\n    color: $color-content-default;\n    width: 100%;\n    border: 1px solid $color-border-3;\n    border-radius: 8px;\n    overflow-x: auto;\n    background-color: $color-surface-1;\n\n    .thead {\n      border-bottom: 1px solid $color-border-1;\n      padding: 0 16px;\n      .header {\n        display: flex;\n        flex-direction: row;\n        justify-content: space-between;\n        text-align: left;\n        align-items: center;\n        height: 64px;\n        gap: 16px;\n        .header-title {\n          height: 64px;\n          width: 100%;\n          display: flex;\n          flex-direction: row;\n          align-items: center;\n          justify-content: flex-start;\n          gap: 8px;\n          \n          .title-click {\n            cursor: pointer;\n          }\n        }\n      }\n    }\n\n    .body-row {\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      height: 64px;\n      padding: 0 16px;\n      gap: 16px;\n      border-bottom: 1px solid $color-border-2;\n\n      .body-item {\n        height: 48px;\n        width: 100%;\n        gap: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n      }\n    }\n\n    .body-row:last-child {\n      border-bottom: none;\n    }\n  }\n}\n", "import { Component, Host, h, Prop, State, Element, Event, EventEmitter, Method } from '@stencil/core';\n\ntype Data = {\n  [key: string]: any;\n};\n@Component({\n  tag: 'bds-data-table',\n  styleUrl: 'data-table.scss',\n  shadow: true,\n})\nexport class DataTable {\n  @Element() el!: HTMLElement;\n  @State() newTable: Data = [];\n  /**\n   * For keep the Object of header;\n   */\n  @State() headerData?: Data = [];\n  /**\n   * For keep the Object of table content.\n   */\n  @State() tableData?: Data[] = [];\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() sortAscending?: boolean;\n  /**\n   * For keep the state of the prop sort.\n   */\n  @State() headerActive: string;\n  /**\n   * Prop to recive the content of the table.\n   */\n  @Prop() options?: string;\n  /**\n   * Prop to recive the header and configuration of table.\n   */\n  @Prop() column?: string;\n  /**\n   * Prop to activate the possibility of use avatar in any column.\n   */\n  @Prop() avatar?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() chips?: boolean = false;\n  /**\n   * Prop to activate the possibility of use chip in any column.\n   */\n  @Prop() actionArea?: boolean;\n  /**\n   * Prop to activate the sorting.\n   */\n  @Prop() sorting?: boolean = false;\n  @Event() bdsTableClick: EventEmitter;\n  @Event() bdsTableDelete: EventEmitter;\n  @Event() bdsTableChange: EventEmitter;\n\n  componentWillLoad() {\n    this.getDataFromProprety();\n  }\n\n  private getDataFromProprety() {\n    this.headerData = JSON.parse(this.column);\n    this.tableData = JSON.parse(this.options);\n  }\n\n  renderArrow(value) {\n    if (value) {\n      return <bds-icon name=\"arrow-up\" size=\"small\"></bds-icon>;\n    } else {\n      return null;\n    }\n  }\n\n  @Method()\n  async deleteItem(index: number) {\n    const itemDelete = this.tableData.filter((item, i) => i === index && item);\n    this.bdsTableDelete.emit(itemDelete[0]);\n    this.tableData.splice(index, 1);\n    this.tableData = [...this.tableData];\n    this.bdsTableChange.emit(this.tableData);\n  }\n\n  clickButton(item, index, btn) {\n    this.bdsTableClick.emit({ item: item, index: index, nameButton: btn });\n  }\n\n  orderColumn(idx) {\n    this.headerActive = idx;\n    this.sortAscending = this.sortAscending ? false : true;\n\n    if (this.sortAscending === false) {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? 1 : -1;\n      });\n    } else {\n      this.tableData.sort(function (a, b) {\n        return a[idx] > b[idx] ? -1 : 1;\n      });\n    }\n  }\n\n  render() {\n    return (\n      <Host>\n        <table class=\"table\">\n          <thead class=\"thead\">\n            <tr class=\"header\">\n              {this.headerData.map((item, index) => (\n                <th class=\"header-title\" key={index}>\n                  {this.sorting ? (\n                    <bds-typo\n                      class=\"title-click\"\n                      onClick={() => this.orderColumn(item.value)}\n                      variant=\"fs-14\"\n                      bold={this.headerActive === `${item.value}` ? 'bold' : 'semi-bold'}\n                    >\n                      {item.heading}\n                    </bds-typo>\n                  ) : (\n                    <bds-typo variant=\"fs-14\" bold=\"semi-bold\">\n                      {item.heading}\n                    </bds-typo>\n                  )}\n                  {this.sortAscending === true && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon class=\"header-icon\" name=\"arrow-up\" size=\"small\"></bds-icon>\n                  ) : this.sortAscending === false && this.sorting === true && this.headerActive === `${item.value}` ? (\n                    <bds-icon name=\"arrow-down\" size=\"small\"></bds-icon>\n                  ) : (\n                    ''\n                  )}\n                </th>\n              ))}\n            </tr>\n          </thead>\n          <tbody>\n            {this.tableData.map((item, index) => (\n              <tr class=\"body-row\" key={index}>\n                {this.headerData.map((columnItem, idx) => {\n                  return (\n                    <td class=\"body-item\" key={idx}>\n                      {this.actionArea && columnItem.editAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.editAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.editAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.deleteAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.deleteAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.deleteAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.actionArea && columnItem.customAction ? (\n                        <bds-button-icon\n                          onClick={() => this.clickButton(item, index, columnItem.customAction)}\n                          variant=\"secondary\"\n                          icon={item[`${columnItem.customAction}`]}\n                          size=\"short\"\n                        ></bds-button-icon>\n                      ) : (\n                        ''\n                      )}\n                      {this.chips && columnItem.chips ? (\n                        <bds-chip-tag color={item[`${columnItem.chips}`] ? item[`${columnItem.chips}`] : 'default'}>\n                          {item[`${columnItem.value}`]}\n                        </bds-chip-tag>\n                      ) : (\n                        ''\n                      )}\n                      {this.avatar && columnItem.img ? (\n                        <bds-avatar\n                          size=\"extra-small\"\n                          thumbnail={item[`${columnItem.img}`]}\n                          name={item[`${columnItem.value}`]}\n                        ></bds-avatar>\n                      ) : (\n                        ''\n                      )}\n                      {columnItem.chips ? (\n                        ''\n                      ) : (\n                        <bds-typo\n                          variant=\"fs-14\"\n                          bold={this.headerActive === `${columnItem.value}` ? 'bold' : 'regular'}\n                        >\n                          {item[`${columnItem.value}`]}\n                        </bds-typo>\n                      )}\n                    </td>\n                  );\n                })}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </Host>\n    );\n  }\n}\n"], "version": 3}