{"version": 3, "file": "position-element-Due63Z64.js", "sources": ["src/utils/position-element.ts"], "sourcesContent": ["export interface Position {\n  top: number;\n  left: number;\n}\n\nexport type reference = 'top' | 'bottom' | 'left' | 'right';\n\nexport interface BreakPostion {\n  x: reference | string;\n  y: reference | string;\n}\n\nexport const getScrollParent = (node: HTMLElement) => {\n  if (node === null) {\n    return null;\n  }\n\n  if (node.classList.contains('element_scrolled') || node?.tagName === 'BODY') {\n    return node;\n  } else {\n    return getScrollParent(node.offsetParent as HTMLElement);\n  }\n};\n\nexport function getParentsUntil(element: HTMLElement, stopSelector: string): HTMLElement[] {\n  const parents: HTMLElement[] = [element];\n\n  while (element && !element.matches(stopSelector)) {\n    element = element.parentElement;\n    parents.push(element);\n  }\n\n  return parents;\n}\n\nexport function positionElement({\n  actionElement,\n  changedElement,\n  intoView,\n}: {\n  actionElement: HTMLElement;\n  changedElement: HTMLElement;\n  intoView: HTMLElement;\n}): Position {\n  const body = intoView ? intoView : document.body;\n  const parentElement: HTMLElement = body.offsetParent as HTMLElement;\n  const contentScrolled = !!body.classList.contains('element_scrolled');\n\n  const positionTop = contentScrolled\n    ? actionElement.offsetTop - body.scrollTop + parentElement.offsetTop\n    : actionElement.offsetTop - window.scrollY;\n\n  const positionLeft = contentScrolled ? actionElement.offsetLeft + parentElement.offsetLeft : actionElement.offsetLeft;\n\n  const changedpositionTop =\n    changedElement?.offsetHeight > window.innerHeight - positionTop\n      ? positionTop - changedElement?.offsetHeight - 16\n      : positionTop + actionElement?.offsetHeight + 16;\n  const changedpositionLeft =\n    changedElement?.offsetWidth > window.innerWidth - positionLeft\n      ? positionLeft + actionElement?.offsetWidth - changedElement?.offsetWidth\n      : positionLeft;\n\n  const limitedHeightScreen = window.innerHeight - changedElement?.offsetHeight;\n  const limitedWidthScreen = window.innerWidth - changedElement?.offsetWidth;\n\n  const result = {\n    top:\n      changedpositionTop < 8\n        ? 8\n        : changedpositionTop > limitedHeightScreen\n          ? limitedHeightScreen - 8\n          : changedpositionTop,\n    left:\n      changedpositionLeft < 0 ? 0 : changedpositionLeft > limitedWidthScreen ? limitedWidthScreen : changedpositionLeft,\n  };\n\n  return result;\n}\n\nexport function positionAbsoluteElement({\n  actionElement,\n  changedElement,\n  intoView,\n}: {\n  actionElement: HTMLElement;\n  changedElement: HTMLElement;\n  intoView: HTMLElement;\n}): BreakPostion {\n  const body = intoView ? intoView : document.body;\n  const numberHeignt = body.offsetHeight < changedElement.offsetHeight ? window.screen.height : body.offsetHeight;\n  const numberWidth = body.offsetWidth < changedElement.offsetWidth ? window.screen.width : body.offsetWidth;\n  const heightTop = numberHeignt - actionElement.offsetTop;\n  const widthLeft = numberWidth - actionElement.offsetLeft;\n\n  const result = {\n    y: heightTop < changedElement.offsetHeight + actionElement.offsetHeight ? 'top' : 'bottom',\n    x: widthLeft < changedElement.offsetWidth ? 'right' : 'left',\n  };\n\n  return result;\n}\n\nexport const getItems = (itenslenght: number) => {\n  const items = [];\n  let item = 1;\n\n  while (item <= itenslenght) {\n    const newItem = {\n      id: item,\n      label: `Frame - ${item}`,\n    };\n    items.push(newItem);\n    item++;\n  }\n  return items;\n};\n\nexport const getHighestItem = (items) => {\n  var maxoffsetHeight = Math.max.apply(\n    null,\n    items.map((a) => a.offsetHeight),\n  );\n  var output = items.filter((a) => a.offsetHeight == maxoffsetHeight).map((a) => a.offsetHeight);\n  return output;\n};\n\nexport const gapChanged = (gap: string) => {\n  let spaceGap;\n  switch (gap) {\n    case 'none':\n      spaceGap = 0;\n      break;\n    case 'half':\n      spaceGap = 4;\n      break;\n    case '1':\n      spaceGap = 8;\n      break;\n    case '2':\n      spaceGap = 16;\n      break;\n    case '3':\n      spaceGap = 24;\n      break;\n    case '4':\n      spaceGap = 32;\n      break;\n    case '5':\n      spaceGap = 40;\n      break;\n    case '6':\n      spaceGap = 48;\n      break;\n    case '7':\n      spaceGap = 56;\n      break;\n    case '8':\n      spaceGap = 64;\n      break;\n    case '9':\n      spaceGap = 72;\n      break;\n    case '10':\n      spaceGap = 80;\n      break;\n    case '11':\n      spaceGap = 88;\n      break;\n    case '12':\n      spaceGap = 96;\n      break;\n    default:\n      spaceGap = 0;\n  }\n  return spaceGap;\n};\n"], "names": [], "mappings": ";;AAYa,MAAA,eAAe,GAAG,CAAC,IAAiB,KAAI;AACnD,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,QAAA,OAAO,IAAI;;IAGb,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAA,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,OAAO,MAAK,MAAM,EAAE;AAC3E,QAAA,OAAO,IAAI;;SACN;AACL,QAAA,OAAO,eAAe,CAAC,IAAI,CAAC,YAA2B,CAAC;;AAE5D;AAEgB,SAAA,eAAe,CAAC,OAAoB,EAAE,YAAoB,EAAA;AACxE,IAAA,MAAM,OAAO,GAAkB,CAAC,OAAO,CAAC;IAExC,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAChD,QAAA,OAAO,GAAG,OAAO,CAAC,aAAa;AAC/B,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;AAGvB,IAAA,OAAO,OAAO;AAChB;AAEM,SAAU,eAAe,CAAC,EAC9B,aAAa,EACb,cAAc,EACd,QAAQ,GAKT,EAAA;AACC,IAAA,MAAM,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI;AAChD,IAAA,MAAM,aAAa,GAAgB,IAAI,CAAC,YAA2B;AACnE,IAAA,MAAM,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC;IAErE,MAAM,WAAW,GAAG;UAChB,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;UACzD,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO;AAE5C,IAAA,MAAM,YAAY,GAAG,eAAe,GAAG,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU;AAErH,IAAA,MAAM,kBAAkB,GACtB,CAAA,cAAc,aAAd,cAAc,KAAA,MAAA,GAAA,MAAA,GAAd,cAAc,CAAE,YAAY,IAAG,MAAM,CAAC,WAAW,GAAG;AAClD,UAAE,WAAW,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,YAAY,CAAA,GAAG;AAC/C,UAAE,WAAW,IAAG,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,MAAA,GAAA,MAAA,GAAA,aAAa,CAAE,YAAY,CAAA,GAAG,EAAE;AACpD,IAAA,MAAM,mBAAmB,GACvB,CAAA,cAAc,aAAd,cAAc,KAAA,MAAA,GAAA,MAAA,GAAd,cAAc,CAAE,WAAW,IAAG,MAAM,CAAC,UAAU,GAAG;AAChD,UAAE,YAAY,IAAG,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,WAAW,CAAA,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,uBAAd,cAAc,CAAE,WAAW;UACvE,YAAY;AAElB,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,YAAY,CAAA;AAC7E,IAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,IAAG,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,MAAA,GAAA,MAAA,GAAA,cAAc,CAAE,WAAW,CAAA;AAE1E,IAAA,MAAM,MAAM,GAAG;QACb,GAAG,EACD,kBAAkB,GAAG;AACnB,cAAE;cACA,kBAAkB,GAAG;kBACnB,mBAAmB,GAAG;AACxB,kBAAE,kBAAkB;QAC1B,IAAI,EACF,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,mBAAmB;KACpH;AAED,IAAA,OAAO,MAAM;AACf;AAEM,SAAU,uBAAuB,CAAC,EACtC,aAAa,EACb,cAAc,EACd,QAAQ,GAKT,EAAA;AACC,IAAA,MAAM,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI;IAChD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;IAC/G,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;AAC1G,IAAA,MAAM,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC,SAAS;AACxD,IAAA,MAAM,SAAS,GAAG,WAAW,GAAG,aAAa,CAAC,UAAU;AAExD,IAAA,MAAM,MAAM,GAAG;AACb,QAAA,CAAC,EAAE,SAAS,GAAG,cAAc,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,GAAG,KAAK,GAAG,QAAQ;AAC1F,QAAA,CAAC,EAAE,SAAS,GAAG,cAAc,CAAC,WAAW,GAAG,OAAO,GAAG,MAAM;KAC7D;AAED,IAAA,OAAO,MAAM;AACf;AAEa,MAAA,QAAQ,GAAG,CAAC,WAAmB,KAAI;IAC9C,MAAM,KAAK,GAAG,EAAE;IAChB,IAAI,IAAI,GAAG,CAAC;AAEZ,IAAA,OAAO,IAAI,IAAI,WAAW,EAAE;AAC1B,QAAA,MAAM,OAAO,GAAG;AACd,YAAA,EAAE,EAAE,IAAI;YACR,KAAK,EAAE,CAAW,QAAA,EAAA,IAAI,CAAE,CAAA;SACzB;AACD,QAAA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;AACnB,QAAA,IAAI,EAAE;;AAER,IAAA,OAAO,KAAK;AACd;AAEa,MAAA,cAAc,GAAG,CAAC,KAAK,KAAI;IACtC,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAClC,IAAI,EACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CACjC;AACD,IAAA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,IAAI,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC;AAC9F,IAAA,OAAO,MAAM;AACf;AAEa,MAAA,UAAU,GAAG,CAAC,GAAW,KAAI;AACxC,IAAA,IAAI,QAAQ;IACZ,QAAQ,GAAG;AACT,QAAA,KAAK,MAAM;YACT,QAAQ,GAAG,CAAC;YACZ;AACF,QAAA,KAAK,MAAM;YACT,QAAQ,GAAG,CAAC;YACZ;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,CAAC;YACZ;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,GAAG;YACN,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,IAAI;YACP,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,IAAI;YACP,QAAQ,GAAG,EAAE;YACb;AACF,QAAA,KAAK,IAAI;YACP,QAAQ,GAAG,EAAE;YACb;AACF,QAAA;YACE,QAAQ,GAAG,CAAC;;AAEhB,IAAA,OAAO,QAAQ;AACjB;;;;;;;;;;"}