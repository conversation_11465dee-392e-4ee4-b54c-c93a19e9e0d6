{"version": 3, "file": "bds-sidebar.entry.cjs.js", "sources": ["src/components/sidebar/sidebar.scss?tag=bds-sidebar&encapsulation=shadow", "src/components/sidebar/sidebar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n.sidebar_dialog {\n  width: 100%;\n  height: 100vh;\n  box-shadow: $shadow-2;\n  background-color: rgba(0, 0, 0, 0.7);\n  opacity: 0;\n  visibility: hidden;\n  transition: opacity 0.3s ease-in-out;\n  display: none;\n\n  &.type_over {\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: $zindex-modal-overlay;\n    .sidebar {\n      z-index: $zindex-modal;\n    }\n  }\n\n  &.type_fixed {\n    width: fit-content;\n    position: relative;\n    height: 100%;\n    box-shadow: none;\n  }\n\n  &.is_open {\n    display: flex;\n    opacity: 1;\n    visibility: visible;\n  }\n\n  & .outzone {\n    order: 2;\n    width: 100%;\n    height: 100vh;\n  }\n  .sidebar {\n    width: 360px;\n    transition: all 0.5s ease-in-out;\n    display: flex;\n    flex-direction: column;\n    background-color: $color-surface-2;\n    flex-shrink: 0;\n\n    &.position_left {\n      order: 1;\n    }\n\n    &.position_right {\n      order: 3;\n    }\n\n    &.background_surface-1 {\n      background-color: $color-surface-1;\n    }\n    &.background_surface-2 {\n      background-color: $color-surface-2;\n    }\n    &.background_surface-3 {\n      background-color: $color-surface-3;\n    }\n    &.background_surface-4 {\n      background-color: $color-surface-4;\n    }\n\n    &.type_fixed {\n      width: 288px;\n    }\n\n    & .header {\n      display: flex;\n      align-content: center;\n      justify-content: space-between;\n      padding: 24px;\n\n      & .content {\n        display: flex;\n        width: 100%;\n        align-items: center;\n        position: relative;\n        color: $color-content-default;\n\n        ::slotted(*) {\n          width: 100%;\n        }\n      }\n      & .closeButton {\n        border-radius: 8px;\n        contain: inherit;\n        -webkit-transition:\n          height 0.5s,\n          all 0.3s;\n        -moz-transition:\n          height 0.5s,\n          all 0.3s;\n        transition:\n          height 0.5s,\n          all 0.3s;\n        z-index: 1;\n        cursor: pointer;\n        color: $color-content-default;\n      }\n    }\n\n    & .body {\n      position: relative;\n      flex: 1 1 auto;\n      & .content {\n        position: absolute;\n        inset: 0;\n        z-index: 999999;\n        overflow-y: overlay;\n        overflow-x: clip;\n        @include custom-scroll;\n      }\n      & .margin {\n        padding: 8px 24px;\n      }\n    }\n\n    & .footer {\n      & .content {\n        padding: 24px;\n\n        ::slotted(*) {\n          height: 40px;\n          overflow: hidden;\n        }\n      }\n    }\n    &.is_open {\n      &.position_left {\n        right: calc(100% - 360px);\n      }\n      &.position_right {\n        left: calc(100% - 360px);\n      }\n    }\n  }\n}\n", "import { Component, h, State, Prop, EventEmitter, Event, Method, Watch, Element } from '@stencil/core';\n\nexport type sidebarPosition = 'left' | 'right';\nexport type sidebarType = 'over' | 'fixed';\nexport type sidebarBackground = 'surface-1' | 'surface-2' | 'surface-3' | 'surface-4';\n\n@Component({\n  tag: 'bds-sidebar',\n  styleUrl: 'sidebar.scss',\n  shadow: true,\n})\nexport class Sidebar {\n  private hasFooterSlot: boolean;\n  private hasHeaderSlot: boolean;\n\n  @Element() hostElement: HTMLElement;\n\n  @State() InnerSpacing?: number = 0;\n\n  /**;\n   * isOpen. Used to open sidebar.\n   */\n  @Prop({ mutable: true, reflect: true }) isOpen?: boolean = this.type === 'fixed' ? true : false;\n\n  /**\n   * sidebar position. Used to position the sidebar. Either on the left or on the right.\n   */\n  @Prop() sidebarPosition?: sidebarPosition = 'left';\n\n  /**\n   * sidebar type. Used to define how open.\n   */\n  @Prop() type?: sidebarType = 'over';\n\n  /**\n   * If true, a lateral margin will apear in the content.\n   */\n  @Prop() margin?: boolean = true;\n  /**\n   * Width, number to define sidebar width.\n   */\n  @Prop() width?: number = 360;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtOutzone is the data-test to button close.\n   */\n  @Prop() dtOutzone?: string = null;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Width, number to define sidebar width.\n   */\n  @Prop() background?: sidebarBackground = 'surface-2';\n\n  /**\n   * Emitted when the isOpen has changed.\n   */\n  @Event() bdsToggle!: EventEmitter;\n\n  @Method()\n  async toggle() {\n    this.isOpen = !this.isOpen;\n  }\n\n  @Watch('isOpen')\n  isOpenChanged(newValue: boolean): void {\n    this.bdsToggle.emit({ value: newValue });\n    if (newValue === true) {\n      document.addEventListener('keyup', this.listiner, false);\n    } else {\n      document.removeEventListener('keyup', this.listiner, false);\n    }\n  }\n\n  componentWillLoad() {\n    this.hasFooterSlot = !!this.hostElement.querySelector('[slot=\"footer\"]');\n    this.hasHeaderSlot = !!this.hostElement.querySelector('[slot=\"header\"]');\n  }\n\n  private listiner = (event) => {\n    if (event.key == 'Escape' && this.type !== 'fixed') {\n      this.isOpen = false;\n    }\n  };\n\n  private onClickCloseButtom = () => {\n    this.isOpen = false;\n  };\n\n  render() {\n    return (\n      <div\n        class={{\n          sidebar_dialog: true,\n          is_open: this.isOpen,\n          [`type_${this.type}`]: true,\n        }}\n      >\n        {this.type === 'over' ? (\n          <div class={{ outzone: true }} onClick={() => this.onClickCloseButtom()} data-test={this.dtOutzone}></div>\n        ) : (\n          ''\n        )}\n        <div\n          class={{\n            sidebar: true,\n            is_open: this.isOpen,\n            [`type_${this.type}`]: true,\n            [`position_${this.sidebarPosition}`]: true,\n            [`background_${this.background}`]: true,\n          }}\n          style={{ width: `${this.width < 144 ? 144 : this.width}px` }}\n        >\n          {this.hasHeaderSlot && (\n            <div class={{ header: true }}>\n              <div class={{ content: true }}>\n                <slot name=\"header\" />\n              </div>\n              {this.type === 'fixed' ? (\n                ''\n              ) : (\n                <bds-button-icon\n                  class={{\n                    closeButton: true,\n                  }}\n                  icon=\"close\"\n                  size=\"short\"\n                  variant=\"secondary\"\n                  onClick={() => this.onClickCloseButtom()}\n                  dataTest={this.dtButtonClose}\n                ></bds-button-icon>\n              )}\n            </div>\n          )}\n\n          <div class={{ body: true }}>\n            <div class={{ content: true, element_scrolled: true, margin: this.margin }}>\n              <slot name=\"body\" />\n            </div>\n          </div>\n          {this.hasFooterSlot && (\n            <div class={{ footer: true }}>\n              <div class={{ content: true }}>\n                <slot name=\"footer\" />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n}\n"], "names": ["h"], "mappings": ";;;;AAAA,MAAM,UAAU,GAAG,utGAAutG;;MCW7tG,OAAO,GAAA,MAAA;AALpB,IAAA,WAAA,CAAA,OAAA,EAAA;;;AAWW,QAAA,IAAY,CAAA,YAAA,GAAY,CAAC;AAElC;;AAEG;AACqC,QAAA,IAAA,CAAA,MAAM,GAAa,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,KAAK;AAE/F;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAqB,MAAM;AAElD;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAiB,MAAM;AAEnC;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,IAAI;AAC/B;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAY,GAAG;AAE5B;;;AAGG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,IAAI;AAEjC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AACrC;;AAEG;AACK,QAAA,IAAU,CAAA,UAAA,GAAuB,WAAW;AA2B5C,QAAA,IAAA,CAAA,QAAQ,GAAG,CAAC,KAAK,KAAI;AAC3B,YAAA,IAAI,KAAK,CAAC,GAAG,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AAClD,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEvB,SAAC;AAEO,QAAA,IAAkB,CAAA,kBAAA,GAAG,MAAK;AAChC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACrB,SAAC;AAgEF;AA3FC,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM;;AAI5B,IAAA,aAAa,CAAC,QAAiB,EAAA;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AACxC,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;;aACnD;YACL,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;;;IAI/D,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,CAAC;AACxE,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,CAAC;;IAa1E,MAAM,GAAA;QACJ,QACEA,OACE,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE;AACL,gBAAA,cAAc,EAAE,IAAI;gBACpB,OAAO,EAAE,IAAI,CAAC,MAAM;AACpB,gBAAA,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;aAC5B,EAAA,EAEA,IAAI,CAAC,IAAI,KAAK,MAAM,IACnBA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAAa,WAAA,EAAA,IAAI,CAAC,SAAS,GAAQ,KAE1G,EAAE,CACH,EACDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,MAAM;AACpB,gBAAA,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI;AAC3B,gBAAA,CAAC,YAAY,IAAI,CAAC,eAAe,CAAE,CAAA,GAAG,IAAI;AAC1C,gBAAA,CAAC,cAAc,IAAI,CAAC,UAAU,CAAE,CAAA,GAAG,IAAI;aACxC,EACD,KAAK,EAAE,EAAE,KAAK,EAAE,CAAG,EAAA,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,EAAE,EAAA,EAE3D,IAAI,CAAC,aAAa,KACjBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAA,EAC1BA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAA,EAC3BA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,QAAQ,EAAA,CAAG,CAClB,EACL,IAAI,CAAC,IAAI,KAAK,OAAO,IACpB,EAAE,KAEFA,OAAA,CAAA,iBAAA,EAAA,EACE,KAAK,EAAE;AACL,gBAAA,WAAW,EAAE,IAAI;AAClB,aAAA,EACD,IAAI,EAAC,OAAO,EACZ,IAAI,EAAC,OAAO,EACZ,OAAO,EAAC,WAAW,EACnB,OAAO,EAAE,MAAM,IAAI,CAAC,kBAAkB,EAAE,EACxC,QAAQ,EAAE,IAAI,CAAC,aAAa,EACX,CAAA,CACpB,CACG,CACP,EAEDA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAA,EACxBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAA,EACxEA,OAAA,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAM,IAAI,EAAC,MAAM,EAAA,CAAG,CAChB,CACF,EACL,IAAI,CAAC,aAAa,KACjBA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAA,EAC1BA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAA,EAC3BA,OAAM,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,IAAI,EAAC,QAAQ,EAAG,CAAA,CAClB,CACF,CACP,CACG,CACF;;;;;;;;;;;"}