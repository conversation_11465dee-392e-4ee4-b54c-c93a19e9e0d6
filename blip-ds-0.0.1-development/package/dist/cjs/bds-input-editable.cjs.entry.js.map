{"file": "bds-input-editable.entry.cjs.js", "mappings": ";;;;AAAA,MAAM,gBAAgB,GAAG,ymWAAymW;;MCcrnW,aAAa,GAAA,MAAA;AAL1B,IAAA,WAAA,CAAA,OAAA,EAAA;;;;;;;;AAaE;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK;AAE1B;;AAEG;AACM,QAAA,IAAO,CAAA,OAAA,GAAG,IAAI;AAEvB;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAI,KAAK;AAE3B;;AAEG;AACM,QAAA,IAAS,CAAA,SAAA,GAAa,KAAK;AAEpC;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,EAAE;AAE/B;;AAEG;AACM,QAAA,IAAgB,CAAA,gBAAA,GAAI,KAAK;AAElC;;;AAGG;AACK,QAAA,IAAI,CAAA,IAAA,GAAuB,UAAU;AAE7C;;AAEG;AACK,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAEhC;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAChC;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,EAAE;AAE/B;;AAEG;AACqC,QAAA,IAAK,CAAA,KAAA,GAAmB,EAAE;AAOlE;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAY,CAAC;AAY9B;;AAEG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,EAAE;AAClC;;AAEG;AACsB,QAAA,IAAc,CAAA,cAAA,GAAY,EAAE;AACrD;;AAEG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,EAAE;AAEnC;;AAEG;AACK,QAAA,IAAW,CAAA,WAAA,GAAY,EAAE;AAEjC;;AAEG;AACqC,QAAA,IAAM,CAAA,MAAA,GAAa,KAAK;AAChE;;AAEG;AACqC,QAAA,IAAO,CAAA,OAAA,GAAa,KAAK;AACjE;;;AAGG;AACK,QAAA,IAAY,CAAA,YAAA,GAAY,IAAI;AACpC;;;AAGG;AACK,QAAA,IAAa,CAAA,aAAA,GAAY,IAAI;AACrC;;;AAGG;AACK,QAAA,IAAe,CAAA,eAAA,GAAY,IAAI;AA+B/B,QAAA,IAAa,CAAA,aAAA,GAAG,MAAW;YACjC,IAAI,CAAC,aAAa,EAAE;AACtB,SAAC;AAEO,QAAA,IAAa,CAAA,aAAA,GAAG,MAAW;AACjC,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS;AAClC,SAAC;AAMO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;AAClC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;AACvC,YAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC5E,gBAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC5E,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;gBACrB,IAAI,CAAC,aAAa,EAAE;;AAExB,SAAC;AAEO,QAAA,IAAA,CAAA,iBAAiB,GAAG,OAAO,EAAc,KAAI;AACnD,YAAA,MAAM,KAAK,GAAG,EAAE,CAAC,MAAiC;YAClD,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC/C,oBAAA,IAAI,CAAC,OAAO,GAAG,KAAK;;qBACf;AACL,oBAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;;AAGvB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjF,SAAC;AAEO,QAAA,IAAO,CAAA,OAAA,GAAG,MAAW;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACtB,SAAC;AAEO,QAAA,IAAM,CAAA,MAAA,GAAG,MAAW;YAC1B,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACxB,SAAC;AAEO,QAAA,IAAc,CAAA,cAAA,GAAG,MAAW;YAClC,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;;AAE5B,SAAC;AAyDO,QAAA,IAAS,CAAA,SAAA,GAAG,MAAa;AAC/B,YAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,gBAAA,OAAO,UAAU;;iBACZ;AACL,gBAAA,OAAO,OAAO;;AAElB,SAAC;AAoHF;IAhOC,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK;;IA8CpB,iBAAiB,GAAA;QACvB,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,EAAE;QAC7D,IAAI,CAAC,aAAa,EAAE;;IAGd,kBAAkB,GAAA;QACxB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE;AAC1C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;AACjD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;IAIxB,gBAAgB,GAAA;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB;AAClD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;QAGF,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;YAC5B;;;IAII,aAAa,GAAA;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;;;AAIzB,IAAA,mBAAmB,CAAC,KAAK,EAAA;AAC/B,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;YACxB,IAAI,CAAC,aAAa,EAAE;;;AAIhB,IAAA,iBAAiB,CAAC,KAAK,EAAA;AAC7B,QAAA,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,EAAE;YACxB,IAAI,CAAC,cAAc,EAAE;;;IAIzB,gBAAgB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,EAAE;AACxB,YAAA,OAAO,OAAO;;AACT,aAAA,IAAI,IAAI,CAAC,IAAI,IAAI,UAAU,EAAE;AAClC,YAAA,OAAO,OAAO;;AACT,aAAA,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE;AAC9B,YAAA,OAAO,OAAO;;aACT;AACL,YAAA,OAAO,OAAO;;;IAUV,aAAa,GAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,MAAM;AACxE,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;AAEvG,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,GAAG,IAAI,CAAC,gBAAgB;QAEtE,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAClB,cAAE;cACA,IAAI,CAAC;AACL,kBAAE;kBACA,gBAAgB;QAExB,IAAI,OAAO,EAAE;AACX,YAAA,QACEA,iBAAK,KAAK,EAAE,MAAM,EAAE,IAAI,EAAC,gBAAgB,EAAA,EACvCA,OAAK,CAAA,KAAA,EAAA,EAAA,KAAK,EAAC,sBAAsB,EAAA,EAC/BA,OAAA,CAAA,UAAA,EAAA,EAAU,IAAI,EAAC,SAAS,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,GAAY,CAC1E,EACNA,OAAA,CAAA,UAAA,EAAA,EAAU,KAAK,EAAC,sBAAsB,EAAC,OAAO,EAAC,OAAO,EAAA,EACnD,OAAO,CACC,CACP;;AAIV,QAAA,OAAO,SAAS;;IAElB,MAAM,GAAA;AACJ,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE;AACvC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE;QACpC,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,iBAAiB,EAAA,EAC1BA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,yBAAyB,EAAE,IAAI,CAAC,SAAS,EAAE,EACrF,OAAO,EAAE,IAAI,CAAC,aAAa,EAAA,WAAA,EAChB,IAAI,CAAC,YAAY,EAC5B,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,EAE9CA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,GAAG,EAAC,MAAM,EACV,IAAI,EAAC,+BAA+B,EACpC,KAAK,EAAC,+BAA+B,EACrC,OAAO,EAAE,OAAO,IAEf,IAAI,CAAC,KAAK,CACF,EACXA,OAAA,CAAA,UAAA,EAAA,EAAU,GAAG,EAAC,WAAW,EAAC,KAAK,EAAC,+BAA+B,EAAC,IAAI,EAAC,MAAM,EAAA,CAAY,CACnF,EACNA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,yBAAyB,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,EAAA,EACzFA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,EAAA,EACzEA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE;AACL,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,IAAI;gBACZ,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;AAC9D,gBAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB;gBAC3D,sBAAsB,EAAE,IAAI,CAAC,OAAO;gBACpC,gBAAgB,EAAE,IAAI,CAAC,SAAS;aACjC,EACD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAA,EAE5BA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,kBAAkB,EAAA,EAC3BA,OAAA,CAAA,OAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EACE,KAAK,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,EACvC,GAAG,EAAE,CAAC,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,EAC1C,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,IAAI,EAAE,IAAI,CAAC,SAAS,EACpB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAC/B,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,QAAQ,EAAE,IAAI,EACd,IAAI,EAAC,OAAO,EAAA,WAAA,EACD,IAAI,CAAC,QAAQ,GACjB,CACL,EACL,IAAI,CAAC,OAAO,IAAIA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,KAAK,EAAC,cAAc,EAAC,IAAI,EAAC,WAAW,EAAC,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,GAAG,CAC9F,EACL,IAAI,CAAC,aAAa,EAAE,CACjB,EACNA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAC,+BAA+B,EAAA,EACxCA,OAAA,CAAA,UAAA,EAAA,EACE,GAAG,EAAC,YAAY,EAChB,KAAK,EAAC,sCAAsC,EAC5C,KAAK,EAAC,OAAO,EACb,IAAI,EAAC,OAAO,EACZ,OAAO,EAAE,IAAI,CAAC,aAAa,EAC3B,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,QAAQ,EAAE,IAAI,CAAC,aAAa,EAClB,CAAA,EACZA,OAAA,CAAA,UAAA,EAAA,EACE,GAAG,EAAC,gBAAgB,EACpB,KAAK,EAAE;AACL,gBAAA,0CAA0C,EAAE,IAAI;AAChD,gBAAA,iDAAiD,EAAE,CAAC,IAAI,CAAC,OAAO;AACjE,aAAA,EACD,KAAK,EAAC,OAAO,EACb,IAAI,EAAC,WAAW,EAChB,OAAO,EAAE,IAAI,CAAC,cAAc,EAC5B,QAAQ,EAAC,GAAG,EACZ,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5C,QAAQ,EAAE,IAAI,CAAC,eAAe,EACpB,CAAA,CACR,CACF,CACF,CACD;;;;;;;;", "names": ["h", "Host"], "sources": ["src/components/input-editable/input-editable.scss?tag=bds-input-editable&encapsulation=shadow", "src/components/input-editable/input-editable.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n$input-padding: 8px 4px 9px 12px;\n$input-padding-label: 7px 4px 8px 12px;\n$input-width: 100%;\n\n$input_expanded: 100%;\n$input_fixed: 140px;\n\n@mixin input-text {\n  font-family: $font-family;\n  font-size: $fs-14;\n  line-height: $line-hight-plus;\n}\n\n@mixin input-border($color, $boxShadow: false) {\n  border: 1px solid $color;\n  box-sizing: border-box;\n  border-radius: 8px;\n\n  @if ($boxShadow) {\n    -webkit-box-shadow: 0 0 0 2px $boxShadow;\n    box-shadow: 0 0 0 2px $boxShadow;\n  }\n}\n\n@mixin input-theme($name, $primary, $secondary, $label, $text, $border, $hover, $focus) {\n  $color-input-primary: $primary;\n  $color-input-secondary: $secondary;\n  $color-input-label: $label;\n  $color-input-text: $text;\n  $color-input-border: $border;\n  $color-input-hover: $hover;\n  $color-input-focus: $focus;\n\n  @if ($name == 'disabled') {\n    background: $color-surface-2;\n  }\n\n  @include input-border($color-input-border);\n\n  // States\n  &:hover {\n    @include input-border($color-input-primary);\n\n    @if ($name == 'disabled') {\n      @include input-border($color-input-border);\n    }\n  }\n\n  &.input--pressed {\n    @include input-border($color-input-primary, $color-input-focus);\n  }\n\n  .input__container__label {\n    color: $color-input-label;\n\n    &--pressed {\n      color: $color-input-primary;\n    }\n  }\n\n  .input__container__text {\n    caret-color: $color-input-primary;\n    color: $color-input-text;\n  }\n}\n\n@mixin part_input_font_size($value) {\n  font-size: $value;\n  line-height: 0px;\n}\n\n@mixin input_max_width() {\n  &.expanded {\n    max-width: $input_expanded;\n  }\n\n  &.fixed {\n    max-width: $input_fixed;\n  }\n}\n\n.element_input {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  input,\n  textarea {\n    box-shadow: inherit;\n    &::placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n\n    &::-webkit-input-placeholder {\n      color: $color-content-ghost;\n      opacity: 1;\n    }\n  }\n}\n\n.input {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: $input-padding;\n\n  flex: 1;\n  width: 100%;\n  max-width: 100%;\n  max-height: 100%;\n\n  background: $color-surface-1;\n\n  &--state {\n    &-primary {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-primary;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'primary',\n        $color-primary,\n        $color-surface-1,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-primary,\n        $color-info\n      );\n    }\n\n    &-danger {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-delete;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'danger',\n        $color-delete,\n        $color-error,\n        $color-delete,\n        $color-content-default,\n        $color-delete,\n        $color-delete,\n        $color-error\n      );\n    }\n\n    &-success {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-success;\n          z-index: 0;\n          border-radius: 8px;\n        }\n      }\n      @include input-theme(\n        'success',\n        $color-content-default,\n        $color-success,\n        $color-content-default,\n        $color-content-default,\n        $color-border-1,\n        $color-content-default,\n        $color-success\n      );\n    }\n\n    &-disabled {\n      .input__icon {\n        position: relative;\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background-color: $color-primary;\n          z-index: 0;\n          opacity: 50%;\n          border-radius: 8px;\n        }\n      }\n      opacity: 50%;\n      pointer-events: none;\n      cursor: not-allowed;\n      background: $color-surface-2;\n    }\n  }\n\n  & .icon-success {\n    color: $color-success;\n    margin-left: 4px;\n  }\n\n  &--label {\n    padding: $input-padding-label;\n  }\n\n  &__icon {\n    cursor: inherit;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 8px;\n    margin-right: 8px;\n    padding: 2.5px;\n\n    &--large {\n      padding: 4px;\n    }\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    width: 100%;\n\n    &__wrapper {\n      display: flex;\n      flex-wrap: wrap;\n    }\n\n    &__wrapper__chips {\n      @include custom-scroll();\n      display: inline;\n      max-height: 100px;\n      overflow: auto;\n    }\n\n    &__label {\n      display: flex;\n      align-items: center;\n    }\n\n    &__text {\n      @include reset-input();\n      @include input-text();\n      @include custom-scroll();\n\n      resize: none;\n      cursor: inherit;\n    }\n  }\n\n  &__message {\n    display: flex;\n    align-items: baseline;\n    height: 20px;\n    margin: 3.7px 2.5px;\n    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    color: $color-content-disable;\n    word-break: break-word;\n    height: auto;\n    min-height: 20px;\n    bds-typo {\n      margin-top: 0px;\n      align-self: self-start;\n    }\n\n    &__icon {\n      display: flex;\n      padding-right: 4px;\n      margin-top: 0px;\n      padding-top: 2px;\n    }\n\n    &--danger {\n      .bds-icon {\n        color: $color-delete;\n      }\n      .input__message__text {\n        color: $color-delete;\n      }\n    }\n  }\n}\n\n.input__editable {\n  display: block;\n\n  &--static {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    position: relative;\n\n    &::before {\n      content: '';\n      position: absolute;\n      inset: -4px;\n      border: 2px solid transparent;\n      border-radius: 4px;\n    }\n\n    &:focus-visible {\n      outline: none;\n\n      &::before {\n        border-color: $color-focus;\n      }\n    }\n    &:hover {\n      .input__editable--static__typo {\n        border: 1px solid $color-primary;\n      }\n\n      .input__editable--static__icon {\n        color: $color-primary;\n      }\n    }\n\n    &__typo {\n      border: 1px solid transparent;\n      margin: 0;\n      padding: 8px;\n      border-radius: 8px;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      overflow: hidden;\n      max-width: 80%;\n      color: $color-content-default;\n    }\n\n    &__icon {\n      margin-left: 8px;\n      color: $color-content-ghost;\n    }\n  }\n\n  &--active {\n    display: flex;\n    align-items: flex-start;\n\n    .element_input {\n      min-width: 120px;\n      margin-right: 4px;\n\n      @include input_max_width();\n\n      &.short input {\n        @include part_input_font_size($fs-16);\n      }\n      &.standard input {\n        @include part_input_font_size($fs-24);\n      }\n      &.tall input {\n        @include part_input_font_size($fs-40);\n      }\n\n      &::part(input-container) {\n        padding: 4px 4px 5px 12px;\n      }\n\n      &::part(input__message) {\n        min-width: 180px;\n      }\n    }\n\n    bds-icon {\n      cursor: pointer;\n      position: relative;\n\n      &::before {\n        content: '';\n        position: absolute;\n        inset: -4px;\n        border: 2px solid transparent;\n        border-radius: 4px;\n      }\n\n      &:focus-visible {\n        outline: none;\n\n        &::before {\n          border-color: $color-focus;\n        }\n      }\n    }\n    &__icon {\n      display: flex;\n      align-items: center;\n      margin: auto 0;\n\n      &--error {\n        color: $color-delete;\n\n        &:hover {\n          color: $color-delete;\n        }\n      }\n\n      &--checkball {\n        color: $color-primary;\n\n        &:hover {\n          color: $color-primary;\n        }\n\n        &--error {\n          color: $color-content-ghost;\n\n          &:hover {\n            color: $color-content-ghost;\n          }\n        }\n      }\n    }\n  }\n\n  &--hidden {\n    display: none;\n  }\n}\n", "import { Component, Prop, State, Event, EventEmitter, Element, h, Host } from '@stencil/core';\nimport { FontSize } from '../typo/typo';\n\nexport type SizeInputEditable = 'short' | 'standard' | 'tall';\nexport interface InputEditableEventDetail {\n  value: string;\n  oldValue: string;\n}\n\n@Component({\n  tag: 'bds-input-editable',\n  styleUrl: 'input-editable.scss',\n  shadow: true,\n})\nexport class InputEditable {\n  private nativeInput?: HTMLInputElement;\n\n  @Element() el!: HTMLBdsInputEditableElement;\n  /**\n   * Value to keep the old value of the input.\n   */\n  @State() oldValue: string;\n  /**\n   * Conditions the element to say whether it is pressed or not, to add styles.\n   */\n  @State() isEditing = false;\n\n  /**\n   * Used to block the confirm icon.\n   */\n  @State() isValid = true;\n\n  /**\n   * Used to validate it is pressed.\n   */\n  @State() isPressed? = false;\n\n  /**\n   * Used to validate it is focused.\n   */\n  @State() isFocused?: boolean = false;\n\n  /**\n   * Used to set the error message setted by the internal validators\n   */\n  @State() validationMesage? = '';\n\n  /**\n   * Used to set the danger behavior by the internal validators\n   */\n  @State() validationDanger? = false;\n\n  /**\n   * Set the component size. Can be one of:\n   * 'short' | 'standard' | 'tall';\n   */\n  @Prop() size?: SizeInputEditable = 'standard';\n\n  /**\n   * Defines whether the component will be expandable\n   */\n  @Prop() expand?: boolean = false;\n\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n  /**\n   * Input Name\n   */\n  @Prop() inputName?: string = '';\n\n  /**\n   * The value of the input.\n   */\n  @Prop({ mutable: true, reflect: true }) value?: string | null = '';\n\n  /**\n   * Error message when input is required\n   */\n  @Prop() requiredErrorMessage: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the minimum number of characters that the user can enter.\n   */\n  @Prop() minlength?: number = 0;\n\n  /**\n   * Error message when the value is lower than the minlength\n   */\n  @Prop() minlengthErrorMessage: string;\n\n  /**\n   * If the value of the type attribute is `text`, `email`, `search`, `password`, `tel`, or `url`, this attribute specifies the maximum number of characters that the user can enter.\n   */\n  @Prop() maxlength?: number;\n\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop() errorMessage?: string = '';\n  /**\n   * Indicated to pass an feeback to user.\n   */\n  @Prop({ mutable: true }) successMessage?: string = '';\n  /**\n   * Indicated to pass a help to the user in complex filling.\n   */\n  @Prop() helperMessage?: string = '';\n\n  /**\n   * Placeholder for native input element.\n   */\n  @Prop() placeholder?: string = '';\n\n  /**\n   * Add state danger on input, use for use feedback. If true avoid save confirmation.\n   */\n  @Prop({ mutable: true, reflect: true }) danger?: boolean = false;\n  /**\n   * Add state success on input, use for use feedback.\n   */\n  @Prop({ reflect: true, mutable: true }) success?: boolean = false;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonEdit is the data-test to button edit.\n   */\n  @Prop() dtButtonEdit?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonClose is the data-test to button close.\n   */\n  @Prop() dtButtonClose?: string = null;\n  /**\n   * Data test is the prop to specifically test the component action object.\n   * dtButtonConfirm is the data-test to button confirm.\n   */\n  @Prop() dtButtonConfirm?: string = null;\n  /**\n   * Emitted when input text confirm.\n   */\n  @Event() bdsInputEditableSave: EventEmitter<InputEditableEventDetail>;\n\n  /**\n   * Emitted when the value has changed.\n   */\n  @Event() bdsChange!: EventEmitter<InputEditableEventDetail>;\n\n  /**\n   * Emitted when the input has changed.\n   */\n  @Event() bdsInput!: EventEmitter<InputEvent>;\n\n  /**\n   * Emitted when the selection is cancelled.\n   */\n  @Event() bdsCancel!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsFocus!: EventEmitter<void>;\n\n  /**\n   * Emitted when the select loses focus.\n   */\n  @Event() bdsBlur!: EventEmitter<void>;\n\n  private handleEditing = (): void => {\n    this.toggleEditing();\n  };\n\n  private toggleEditing = (): void => {\n    this.isEditing = !this.isEditing;\n  };\n\n  componentWillLoad() {\n    this.oldValue = this.value;\n  }\n\n  private handleSaveText = (): void => {\n    const newValue = this.nativeInput.value;\n    if (newValue.length > 0 && newValue.length >= this.minlength && !this.danger) {\n      this.bdsInputEditableSave.emit({ value: newValue, oldValue: this.oldValue });\n      this.oldValue = newValue;\n      this.value = newValue;\n      this.toggleEditing();\n    }\n  };\n\n  private changedInputValue = async (ev: InputEvent) => {\n    const input = ev.target as HTMLInputElement | null;\n    this.checkValidity();\n    if (input) {\n      if (input.value.length < Number(this.minlength)) {\n        this.isValid = false;\n      } else {\n        this.isValid = true;\n      }\n    }\n    this.bdsInput.emit(ev);\n    this.bdsChange.emit({ value: this.nativeInput.value, oldValue: this.oldValue });\n  };\n\n  private onFocus = (): void => {\n    this.isFocused = true;\n    this.isPressed = true;\n    this.bdsFocus.emit();\n  };\n\n  private onBlur = (): void => {\n    this.onBlurValidations();\n    this.bdsBlur.emit();\n    this.isPressed = false;\n  };\n\n  private onClickWrapper = (): void => {\n    this.onFocus();\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  };\n\n  private onBlurValidations() {\n    this.requiredValidation();\n    (this.minlength || this.maxlength) && this.lengthValidation();\n    this.checkValidity();\n  }\n\n  private requiredValidation() {\n    if (this.nativeInput.validity.valueMissing) {\n      this.validationMesage = this.requiredErrorMessage;\n      this.validationDanger = true;\n    }\n  }\n\n  private lengthValidation() {\n    if (this.nativeInput.validity.tooShort) {\n      this.validationMesage = this.minlengthErrorMessage;\n      this.validationDanger = true;\n      return;\n    }\n\n    if (this.nativeInput.validity.tooLong) {\n      this.validationDanger = true;\n      return;\n    }\n  }\n\n  private checkValidity() {\n    if (this.nativeInput.validity.valid) {\n      this.validationDanger = false;\n    }\n  }\n\n  private handleKeyDownToggle(event) {\n    if (event.key == 'Enter') {\n      this.toggleEditing();\n    }\n  }\n\n  private handleKeyDownSave(event) {\n    if (event.key == 'Enter') {\n      this.handleSaveText();\n    }\n  }\n\n  getFontSizeClass(): FontSize {\n    if (this.size == 'short') {\n      return 'fs-16';\n    } else if (this.size == 'standard') {\n      return 'fs-24';\n    } else if (this.size == 'tall') {\n      return 'fs-40';\n    } else {\n      return 'fs-24';\n    }\n  }\n  private getExpand = (): string => {\n    if (this.expand) {\n      return 'expanded';\n    } else {\n      return 'fixed';\n    }\n  };\n  private renderMessage(): HTMLElement {\n    const icon = this.danger ? 'error' : this.success ? 'checkball' : 'info';\n    let message = this.danger ? this.errorMessage : this.success ? this.successMessage : this.helperMessage;\n\n    if (!message && this.validationDanger) message = this.validationMesage;\n\n    const styles =\n      this.danger || this.validationDanger\n        ? 'input__message input__message--danger'\n        : this.success\n          ? 'input__message input__message--success'\n          : 'input__message';\n\n    if (message) {\n      return (\n        <div class={styles} part=\"input__message\">\n          <div class=\"input__message__icon\">\n            <bds-icon size=\"x-small\" name={icon} theme=\"solid\" color=\"inherit\"></bds-icon>\n          </div>\n          <bds-typo class=\"input__message__text\" variant=\"fs-12\">\n            {message}\n          </bds-typo>\n        </div>\n      );\n    }\n\n    return undefined;\n  }\n  render() {\n    const variant = this.getFontSizeClass();\n    const inputExpand = this.getExpand();\n    return (\n      <Host>\n        <div class=\"input__editable\">\n          <div\n            class={{ 'input__editable--static': true, 'input__editable--hidden': this.isEditing }}\n            onClick={this.handleEditing}\n            data-test={this.dtButtonEdit}\n            tabindex=\"0\"\n            onKeyDown={this.handleKeyDownToggle.bind(this)}\n          >\n            <bds-typo\n              tag=\"span\"\n              part=\"input__editable--static__typo\"\n              class=\"input__editable--static__typo\"\n              variant={variant}\n            >\n              {this.value}\n            </bds-typo>\n            <bds-icon key=\"edit-icon\" class=\"input__editable--static__icon\" name=\"edit\"></bds-icon>\n          </div>\n          <div class={{ 'input__editable--active': true, 'input__editable--hidden': !this.isEditing }}>\n            <div class={{ element_input: true, [inputExpand]: true, [this.size]: true }}>\n              <div\n                class={{\n                  input: true,\n                  select: true,\n                  'input--state-primary': !this.danger && !this.validationDanger,\n                  'input--state-danger': this.danger || this.validationDanger,\n                  'input--state-success': this.success,\n                  'input--pressed': this.isPressed,\n                }}\n                onClick={this.onClickWrapper}\n              >\n                <div class=\"input__container\">\n                  <input\n                    class={{ input__container__text: true }}\n                    ref={(input) => (this.nativeInput = input)}\n                    minLength={this.minlength}\n                    maxLength={this.maxlength}\n                    name={this.inputName}\n                    onBlur={this.onBlur}\n                    onFocus={this.onFocus}\n                    onInput={this.changedInputValue}\n                    placeholder={this.placeholder}\n                    value={this.value}\n                    required={true}\n                    part=\"input\"\n                    data-test={this.dataTest}\n                  ></input>\n                </div>\n                {this.success && <bds-icon class=\"icon-success\" name=\"checkball\" theme=\"solid\" size=\"xxx-small\" />}\n              </div>\n              {this.renderMessage()}\n            </div>\n            <div class=\"input__editable--active__icon\">\n              <bds-icon\n                key=\"error-icon\"\n                class=\"input__editable--active__icon--error\"\n                theme=\"solid\"\n                name=\"error\"\n                onClick={this.handleEditing}\n                tabindex=\"0\"\n                onKeyDown={this.handleKeyDownToggle.bind(this)}\n                dataTest={this.dtButtonClose}\n              ></bds-icon>\n              <bds-icon\n                key=\"checkball-icon\"\n                class={{\n                  'input__editable--active__icon--checkball': true,\n                  'input__editable--active__icon--checkball--error': !this.isValid,\n                }}\n                theme=\"solid\"\n                name=\"checkball\"\n                onClick={this.handleSaveText}\n                tabindex=\"0\"\n                onKeyDown={this.handleKeyDownSave.bind(this)}\n                dataTest={this.dtButtonConfirm}\n              ></bds-icon>\n            </div>\n          </div>\n        </div>\n      </Host>\n    );\n  }\n}\n"], "version": 3}