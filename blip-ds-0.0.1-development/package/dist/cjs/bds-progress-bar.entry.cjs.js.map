{"version": 3, "file": "bds-progress-bar.entry.cjs.js", "sources": ["src/components/progress-bar/progress-bar.scss?tag=bds-progress-bar&encapsulation=shadow", "src/components/progress-bar/progress-bar.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: block;\n}\n\n.progress_bar {\n  box-sizing: border-box;\n  position: relative;\n  border-radius: 32px;\n  border: 1px solid $color-content-disable;\n  margin-bottom: 4px;\n\n  &.size_small {\n    height: 8px;\n    .bar_behind {\n      & .progress {\n        border-radius: 1px;\n      }\n    }\n  }\n\n  &.size_default {\n    height: 16px;\n    .bar_behind {\n      & .progress {\n        border-radius: 2px;\n      }\n    }\n  }\n\n  .bar_behind {\n    position: absolute;\n    inset: 0.5px 1px 1px 0.5px;\n    border-radius: 16px;\n    overflow: hidden;\n\n    & .progress {\n      position: absolute;\n      height: 100%;\n      @include animation();\n      overflow: hidden;\n\n      &.color {\n        &_default {\n          background-color: $color-extended-blue;\n        }\n        &_positive {\n          background-color: $color-extended-green;\n        }\n        &_information {\n          background-color: $color-extended-yellow;\n        }\n        &_warning {\n          background-color: $color-extended-red;\n        }\n      }\n\n      & .loading {\n        position: absolute;\n        left: -16px;\n        width: calc(100% + 16px);\n        height: 100%;\n        background: rgb(255, 255, 255);\n        background: linear-gradient(\n          90deg,\n          rgba(255, 255, 255, 0) 0%,\n          rgba(255, 255, 255, 0) 75%,\n          rgba(0, 0, 0, 0.26) 75%\n        );\n        background-size: 4px;\n        transform: skewX(-15deg);\n        animation-name: load;\n        animation-timing-function: linear;\n        animation-duration: 0.5s;\n        animation-iteration-count: infinite;\n      }\n    }\n  }\n}\n\n.typo_progress {\n  color: $color-content-default;\n}\n\n@keyframes load {\n  from {\n    left: -16px;\n  }\n  to {\n    left: 0;\n  }\n}\n", "import { Component, Host, Prop, h } from '@stencil/core';\n\nexport type progressBarSize = 'small' | 'default';\n\nexport type progressBarColor = 'default' | 'positive' | 'information' | 'warning';\n\n@Component({\n  tag: 'bds-progress-bar',\n  styleUrl: 'progress-bar.scss',\n  shadow: true,\n})\nexport class BdsProgressBar {\n  /**\n   * Percent, property to enter the progress bar status percentage value.\n   */\n  @Prop() percent?: number = 0;\n  /**\n   * Size, property to define size of component.\n   */\n  @Prop() size?: progressBarSize = 'default';\n  /**\n   * Text, property to define status of component.\n   */\n  @Prop() color?: progressBarColor = 'default';\n  /**\n   * Text, property to enable the bar info text.\n   */\n  @Prop() text?: string = '';\n  /**\n   * Data test is the prop to specifically test the component action object.\n   */\n  @Prop() dataTest?: string = null;\n\n  render() {\n    const styles = { width: `${this.percent ? (this.percent > 100 ? 100 : this.percent) : 0}%` };\n    return (\n      <Host>\n        <div class={{ progress_bar: true, [`size_${this.size}`]: true }} data-test={this.dataTest}>\n          <div class={{ bar_behind: true }}>\n            <div class={{ progress: true, [`color_${this.color}`]: true }} style={styles}></div>\n          </div>\n        </div>\n        {this.text && (\n          <div class={{ typo_progress: true }}>\n            <bds-typo variant=\"fs-14\">{this.text}</bds-typo>\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,cAAc,GAAG,6sEAA6sE;;MCWvtE,cAAc,GAAA,MAAA;AAL3B,IAAA,WAAA,CAAA,OAAA,EAAA;;AAME;;AAEG;AACK,QAAA,IAAO,CAAA,OAAA,GAAY,CAAC;AAC5B;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAqB,SAAS;AAC1C;;AAEG;AACK,QAAA,IAAK,CAAA,KAAA,GAAsB,SAAS;AAC5C;;AAEG;AACK,QAAA,IAAI,CAAA,IAAA,GAAY,EAAE;AAC1B;;AAEG;AACK,QAAA,IAAQ,CAAA,QAAA,GAAY,IAAI;AAmBjC;IAjBC,MAAM,GAAA;AACJ,QAAA,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAG,EAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA,CAAA,CAAG,EAAE;AAC5F,QAAA,QACEA,QAACC,UAAI,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,EACHD,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAA,KAAA,EAAQ,IAAI,CAAC,IAAI,CAAE,CAAA,GAAG,IAAI,EAAE,EAAa,WAAA,EAAA,IAAI,CAAC,QAAQ,EAAA,EACvFA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAA,EAC9BA,OAAK,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAS,MAAA,EAAA,IAAI,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAQ,CAAA,CAChF,CACF,EACL,IAAI,CAAC,IAAI,KACRA,OAAA,CAAA,KAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAK,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAA,EACjCA,OAAA,CAAA,UAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAU,OAAO,EAAC,OAAO,EAAA,EAAE,IAAI,CAAC,IAAI,CAAY,CAC5C,CACP,CACI;;;;;;;"}