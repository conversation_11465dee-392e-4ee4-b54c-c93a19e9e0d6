{"version": 3, "file": "bds-navbar-content.entry.cjs.js", "sources": ["src/components/navbar/navbar.scss?tag=bds-navbar-content&encapsulation=shadow", "src/components/navbar/navbar-content.tsx"], "sourcesContent": ["@use '../../globals/helpers' as *;\n\n:host {\n  display: flex;\n}\n\n:host(.horizontal) {\n  width: 100%;\n  height: fit-content;\n}\n\n:host(.vertical) {\n  width: fit-content;\n  height: 100%;\n}\n\n.navbar {\n  width: fit-content;\n  display: flex;\n  gap: 8px;\n  box-sizing: border-box;\n\n  ::slotted(*) {\n    display: flex;\n    gap: 8px;\n    align-items: center;\n  }\n\n  &__justify-content {\n    &__flex-start {\n      justify-content: flex-start;\n    }\n    &__center {\n      justify-content: center;\n    }\n    &__flex-end {\n      justify-content: flex-end;\n    }\n    &__space-between {\n      justify-content: space-between;\n    }\n    &__space-around {\n      justify-content: space-around;\n    }\n    &__space-evenly {\n      justify-content: space-evenly;\n    }\n  }\n\n  &__orientation {\n    &__horizontal {\n      flex-direction: row;\n      width: 100%;\n      padding: 8px 16px;\n      ::slotted(*) {\n        flex-direction: row;\n      }\n    }\n    &__vertical {\n      flex-direction: column;\n      height: 100%;\n      padding: 16px 8px;\n      ::slotted(*) {\n        flex-direction: column;\n      }\n    }\n  }\n  \n  &__background-color {\n    &__surface-1 {\n      background-color: $color-surface-1;\n    }\n    &__surface-2 {\n      background-color: $color-surface-2;\n    }\n    &__surface-3 {\n      background-color: $color-surface-3;\n    }\n    &__surface-4 {\n      background-color: $color-surface-4;\n    }\n  }\n}\n", "import { Component, h, Host, Element } from '@stencil/core';\n\n@Component({\n  tag: 'bds-navbar-content',\n  styleUrl: 'navbar.scss',\n  shadow: true,\n})\nexport class NavbarContent {\n  @Element() hostElement: HTMLElement;\n\n  render() {\n    return (\n      <Host class={{ NavbarContent: true }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,SAAS,GAAG,2wDAA2wD;;MCOhxD,aAAa,GAAA,MAAA;;;;IAGxB,MAAM,GAAA;AACJ,QAAA,QACEA,OAAC,CAAAC,UAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAA,EAClCD,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;"}