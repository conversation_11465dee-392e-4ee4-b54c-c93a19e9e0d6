{"version": 3, "file": "bds-table-body.entry.cjs.js", "sources": ["src/components/table/table-body/table-body.scss?tag=bds-table-body&encapsulation=scoped", "src/components/table/table-body/table-body.tsx"], "sourcesContent": ["@use '../../../globals/helpers' as *;\n\n:host {\n    display: table-row-group;\n    height: 64px;\n  }\n\n  :host(.multiple) {\n    border-bottom: 1px solid $color-border-2;\n  }\n  \n  :host:last-child {\n    border-bottom: none;\n  }", "import { Component, h, Host, Element, State } from '@stencil/core';\n\n@Component({\n  tag: 'bds-table-body',\n  styleUrl: 'table-body.scss',\n  scoped: true,\n})\nexport class TableBody {\n  @Element() private element: HTMLElement;\n  @State() multipleRows = false;\n\n  componentWillLoad() {\n    const bdsTable = this.element.closest('bds-table');\n    if (bdsTable && (bdsTable.getAttribute('collapse') === 'true' || bdsTable.collapse === true)) {\n      this.multipleRows = true;\n    }\n  }\n\n  render(): HTMLElement {\n    return (\n      <Host class={{ host: true, multiple: this.multipleRows }}>\n        <slot />\n      </Host>\n    );\n  }\n}\n"], "names": ["h", "Host"], "mappings": ";;;;AAAA,MAAM,YAAY,GAAG,+MAA+M;;MCOvN,SAAS,GAAA,MAAA;AALtB,IAAA,WAAA,CAAA,OAAA,EAAA;;AAOW,QAAA,IAAY,CAAA,YAAA,GAAG,KAAK;AAgB9B;IAdC,iBAAiB,GAAA;QACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;AAClD,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,EAAE;AAC5F,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;IAI5B,MAAM,GAAA;AACJ,QAAA,QACEA,OAAC,CAAAC,UAAI,EAAC,EAAA,GAAA,EAAA,0CAAA,EAAA,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,EAAA,EACtDD,OAAQ,CAAA,MAAA,EAAA,EAAA,GAAA,EAAA,0CAAA,EAAA,CAAA,CACH;;;;;;;;"}